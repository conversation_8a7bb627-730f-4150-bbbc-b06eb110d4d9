import React, { useState } from 'react';
import styled from 'styled-components';
import { useAuth } from '../../hooks/useAuth';

const HeaderContainer = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
`;

const LeftSection = styled.div`
  display: flex;
  align-items: center;
  gap: 30px;
`;

const Logo = styled.div`
  font-family: 'Brush Script MT', cursive;
  font-size: 28px;
  font-weight: bold;
  color: white;
  text-decoration: none;
  cursor: pointer;
  
  &:hover {
    opacity: 0.9;
  }
`;

const Navigation = styled.nav`
  display: flex;
  gap: 25px;
`;

const NavItem = styled.a`
  color: white;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  cursor: pointer;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: 15px;
`;

const IconButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const UserSection = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

const UserAvatar = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  color: white;
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
`;

const SignInText = styled.span`
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
`;

const JoinUsText = styled.span`
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
`;

const Header: React.FC = () => {
  const { user, isAuthenticated, signOut } = useAuth();
  const [showSignInModal, setShowSignInModal] = useState(false);

  const handleSignInClick = () => {
    if (isAuthenticated) {
      signOut();
    } else {
      setShowSignInModal(true);
    }
  };

  return (
    <HeaderContainer>
      <LeftSection>
        <Logo>Flipbook</Logo>
        <Navigation>
          <NavItem href="#file">FILE</NavItem>
          <NavItem href="#edit">EDIT</NavItem>
          <NavItem href="#share">SHARE!</NavItem>
          <NavItem href="#help">HELP</NavItem>
        </Navigation>
      </LeftSection>

      <RightSection>
        <IconButton title="Tools">
          🔧
        </IconButton>

        <UserSection>
          <UserAvatar>
            {isAuthenticated && user ?
              (user.firstName?.charAt(0) || 'U') + (user.lastName?.charAt(0) || 'S') :
              'FR'
            }
          </UserAvatar>
          <UserInfo>
            <SignInText onClick={handleSignInClick} style={{ cursor: 'pointer' }}>
              {isAuthenticated ? 'Sign Out' : 'Sign In'}
            </SignInText>
            {!isAuthenticated && <JoinUsText>Join Us</JoinUsText>}
            {isAuthenticated && user && (
              <JoinUsText>{user.firstName} {user.lastName}</JoinUsText>
            )}
          </UserInfo>
        </UserSection>
      </RightSection>
    </HeaderContainer>
  );
};

export default Header;
