{"ast": null, "code": "// Custom hook for managing flipbook data and operations\n// Matches the functionality from .NET FlipbookController\nimport{useState,useEffect,useCallback}from'react';import{apiService}from'../services/api.service';import{mockApiService}from'../services/mockApi.service';// Environment-based API service selection\nconst getApiService=()=>{return process.env.REACT_APP_USE_MOCK_API==='false'?apiService:mockApiService;};export const useFlipbooks=()=>{// State management\nconst[userFlipbooks,setUserFlipbooks]=useState([]);const[inspirationFlipbooks,setInspirationFlipbooks]=useState([]);const[userFlipbooksLoading,setUserFlipbooksLoading]=useState(false);const[inspirationFlipbooksLoading,setInspirationFlipbooksLoading]=useState(false);const[error,setError]=useState(null);const api=getApiService();// Computed loading state\nconst loading=userFlipbooksLoading||inspirationFlipbooksLoading;// Fetch user's flipbooks - matches GetPortfoliosDetails_Pagemanager call\nconst refreshUserFlipbooks=useCallback(async()=>{try{setUserFlipbooksLoading(true);setError(null);const response=await api.getMyFlipbooks();if(response.success&&response.data){setUserFlipbooks(response.data);}else{setError(response.error||'Failed to fetch user flipbooks');}}catch(err){setError(err instanceof Error?err.message:'Failed to fetch user flipbooks');}finally{setUserFlipbooksLoading(false);}},[api]);// Fetch inspiration flipbooks - matches FillInspireFBDetails method\nconst refreshInspirationFlipbooks=useCallback(async()=>{try{setInspirationFlipbooksLoading(true);setError(null);const response=await api.getInspirationFlipbooks();if(response.success&&response.data){setInspirationFlipbooks(response.data);}else{setError(response.error||'Failed to fetch inspiration flipbooks');}}catch(err){setError(err instanceof Error?err.message:'Failed to fetch inspiration flipbooks');}finally{setInspirationFlipbooksLoading(false);}},[api]);// Create new flipbook - matches AddPortfolioTitle method\nconst createFlipbook=useCallback(async request=>{try{setError(null);const response=await api.createNewFlipbook(request);if(response.success&&response.data){// Refresh user flipbooks after creation\nawait refreshUserFlipbooks();return response.data;}else{setError(response.error||'Failed to create flipbook');return null;}}catch(err){setError(err instanceof Error?err.message:'Failed to create flipbook');return null;}},[api,refreshUserFlipbooks]);// Copy inspiration flipbook - matches DuplicateWholeFlipbook method\nconst copyInspirationFlipbook=useCallback(async request=>{try{setError(null);const response=await api.copyInspirationFlipbook(request);if(response.success&&response.data){// Refresh user flipbooks after copying\nawait refreshUserFlipbooks();return response.data;}else{setError(response.error||'Failed to copy inspiration flipbook');return null;}}catch(err){setError(err instanceof Error?err.message:'Failed to copy inspiration flipbook');return null;}},[api,refreshUserFlipbooks]);// Update flipbook - matches UpdateFlipbookTitle method\nconst updateFlipbook=useCallback(async request=>{try{setError(null);const response=await api.updateFlipbook(request);if(response.success){// Refresh user flipbooks after update\nawait refreshUserFlipbooks();return true;}else{setError(response.error||'Failed to update flipbook');return false;}}catch(err){setError(err instanceof Error?err.message:'Failed to update flipbook');return false;}},[api,refreshUserFlipbooks]);// Delete flipbook - matches DeleteFlipbook method\nconst deleteFlipbook=useCallback(async portfolioId=>{try{setError(null);const response=await api.deleteFlipbook(portfolioId);if(response.success){// Refresh user flipbooks after deletion\nawait refreshUserFlipbooks();return true;}else{setError(response.error||'Failed to delete flipbook');return false;}}catch(err){setError(err instanceof Error?err.message:'Failed to delete flipbook');return false;}},[api,refreshUserFlipbooks]);// Set flipbook preferred status - matches SetflipbookIspreferred method\nconst setFlipbookPreferred=useCallback(async(portfolioId,isPreferred)=>{try{setError(null);const response=await api.setFlipbookPreferred(portfolioId,isPreferred);if(response.success){// Refresh user flipbooks after preference change\nawait refreshUserFlipbooks();return true;}else{setError(response.error||'Failed to set flipbook preference');return false;}}catch(err){setError(err instanceof Error?err.message:'Failed to set flipbook preference');return false;}},[api,refreshUserFlipbooks]);// Initial data loading on mount\nuseEffect(()=>{refreshUserFlipbooks();refreshInspirationFlipbooks();},[refreshUserFlipbooks,refreshInspirationFlipbooks]);return{// Data\nuserFlipbooks,inspirationFlipbooks,// Loading states\nloading,userFlipbooksLoading,inspirationFlipbooksLoading,// Error state\nerror,// Operations\nrefreshUserFlipbooks,refreshInspirationFlipbooks,createFlipbook,copyInspirationFlipbook,updateFlipbook,deleteFlipbook,setFlipbookPreferred};};", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "apiService", "mockApiService", "getApiService", "process", "env", "REACT_APP_USE_MOCK_API", "useFlipbooks", "userFlipbooks", "setUserFlipbooks", "inspirationFlipbooks", "setInspirationFlipbooks", "userFlipbooksLoading", "setUserFlipbooksLoading", "inspirationFlipbooksLoading", "setInspirationFlipbooksLoading", "error", "setError", "api", "loading", "refreshUserFlipbooks", "response", "getMyFlipbooks", "success", "data", "err", "Error", "message", "refreshInspirationFlipbooks", "getInspirationFlipbooks", "createFlipbook", "request", "createNewFlipbook", "copyInspirationFlipbook", "updateFlipbook", "deleteFlipbook", "portfolioId", "setFlipbookPreferred", "isPreferred"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/hooks/useFlipbooks.ts"], "sourcesContent": ["// Custom hook for managing flipbook data and operations\n// Matches the functionality from .NET FlipbookController\n\nimport { useState, useEffect, useCallback } from 'react';\nimport {\n  Portfolio,\n  Inspiration,\n  CreateFlipbookRequest,\n  CopyInspirationRequest,\n  UpdateFlipbookRequest,\n  ApiResponse\n} from '../types/flipbook.types';\nimport { apiService } from '../services/api.service';\nimport { mockApiService } from '../services/mockApi.service';\n\n// Environment-based API service selection\nconst getApiService = () => {\n  return process.env.REACT_APP_USE_MOCK_API === 'false' ? apiService : mockApiService;\n};\n\ninterface UseFlipbooksReturn {\n  // Data\n  userFlipbooks: Portfolio[];\n  inspirationFlipbooks: Inspiration[];\n\n  // Loading states\n  loading: boolean;\n  userFlipbooksLoading: boolean;\n  inspirationFlipbooksLoading: boolean;\n\n  // Error states\n  error: string | null;\n\n  // Operations\n  refreshUserFlipbooks: () => Promise<void>;\n  refreshInspirationFlipbooks: () => Promise<void>;\n  createFlipbook: (request: CreateFlipbookRequest) => Promise<number | null>;\n  copyInspirationFlipbook: (request: CopyInspirationRequest) => Promise<number | null>;\n  updateFlipbook: (request: UpdateFlipbookRequest) => Promise<boolean>;\n  deleteFlipbook: (portfolioId: number) => Promise<boolean>;\n  setFlipbookPreferred: (portfolioId: number, isPreferred: boolean) => Promise<boolean>;\n}\n\nexport const useFlipbooks = (): UseFlipbooksReturn => {\n  // State management\n  const [userFlipbooks, setUserFlipbooks] = useState<Portfolio[]>([]);\n  const [inspirationFlipbooks, setInspirationFlipbooks] = useState<Inspiration[]>([]);\n  const [userFlipbooksLoading, setUserFlipbooksLoading] = useState(false);\n  const [inspirationFlipbooksLoading, setInspirationFlipbooksLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const api = getApiService();\n\n  // Computed loading state\n  const loading = userFlipbooksLoading || inspirationFlipbooksLoading;\n\n  // Fetch user's flipbooks - matches GetPortfoliosDetails_Pagemanager call\n  const refreshUserFlipbooks = useCallback(async () => {\n    try {\n      setUserFlipbooksLoading(true);\n      setError(null);\n\n      const response: ApiResponse<Portfolio[]> = await api.getMyFlipbooks();\n\n      if (response.success && response.data) {\n        setUserFlipbooks(response.data);\n      } else {\n        setError(response.error || 'Failed to fetch user flipbooks');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to fetch user flipbooks');\n    } finally {\n      setUserFlipbooksLoading(false);\n    }\n  }, [api]);\n\n  // Fetch inspiration flipbooks - matches FillInspireFBDetails method\n  const refreshInspirationFlipbooks = useCallback(async () => {\n    try {\n      setInspirationFlipbooksLoading(true);\n      setError(null);\n\n      const response: ApiResponse<Inspiration[]> = await api.getInspirationFlipbooks();\n\n      if (response.success && response.data) {\n        setInspirationFlipbooks(response.data);\n      } else {\n        setError(response.error || 'Failed to fetch inspiration flipbooks');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to fetch inspiration flipbooks');\n    } finally {\n      setInspirationFlipbooksLoading(false);\n    }\n  }, [api]);\n\n  // Create new flipbook - matches AddPortfolioTitle method\n  const createFlipbook = useCallback(async (request: CreateFlipbookRequest): Promise<number | null> => {\n    try {\n      setError(null);\n\n      const response: ApiResponse<number> = await api.createNewFlipbook(request);\n\n      if (response.success && response.data) {\n        // Refresh user flipbooks after creation\n        await refreshUserFlipbooks();\n        return response.data;\n      } else {\n        setError(response.error || 'Failed to create flipbook');\n        return null;\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to create flipbook');\n      return null;\n    }\n  }, [api, refreshUserFlipbooks]);\n\n  // Copy inspiration flipbook - matches DuplicateWholeFlipbook method\n  const copyInspirationFlipbook = useCallback(async (request: CopyInspirationRequest): Promise<number | null> => {\n    try {\n      setError(null);\n\n      const response: ApiResponse<number> = await api.copyInspirationFlipbook(request);\n\n      if (response.success && response.data) {\n        // Refresh user flipbooks after copying\n        await refreshUserFlipbooks();\n        return response.data;\n      } else {\n        setError(response.error || 'Failed to copy inspiration flipbook');\n        return null;\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to copy inspiration flipbook');\n      return null;\n    }\n  }, [api, refreshUserFlipbooks]);\n\n  // Update flipbook - matches UpdateFlipbookTitle method\n  const updateFlipbook = useCallback(async (request: UpdateFlipbookRequest): Promise<boolean> => {\n    try {\n      setError(null);\n\n      const response: ApiResponse<boolean> = await api.updateFlipbook(request);\n\n      if (response.success) {\n        // Refresh user flipbooks after update\n        await refreshUserFlipbooks();\n        return true;\n      } else {\n        setError(response.error || 'Failed to update flipbook');\n        return false;\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to update flipbook');\n      return false;\n    }\n  }, [api, refreshUserFlipbooks]);\n\n  // Delete flipbook - matches DeleteFlipbook method\n  const deleteFlipbook = useCallback(async (portfolioId: number): Promise<boolean> => {\n    try {\n      setError(null);\n\n      const response: ApiResponse<boolean> = await api.deleteFlipbook(portfolioId);\n\n      if (response.success) {\n        // Refresh user flipbooks after deletion\n        await refreshUserFlipbooks();\n        return true;\n      } else {\n        setError(response.error || 'Failed to delete flipbook');\n        return false;\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to delete flipbook');\n      return false;\n    }\n  }, [api, refreshUserFlipbooks]);\n\n  // Set flipbook preferred status - matches SetflipbookIspreferred method\n  const setFlipbookPreferred = useCallback(async (portfolioId: number, isPreferred: boolean): Promise<boolean> => {\n    try {\n      setError(null);\n\n      const response: ApiResponse<boolean> = await api.setFlipbookPreferred(portfolioId, isPreferred);\n\n      if (response.success) {\n        // Refresh user flipbooks after preference change\n        await refreshUserFlipbooks();\n        return true;\n      } else {\n        setError(response.error || 'Failed to set flipbook preference');\n        return false;\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to set flipbook preference');\n      return false;\n    }\n  }, [api, refreshUserFlipbooks]);\n\n  // Initial data loading on mount\n  useEffect(() => {\n    refreshUserFlipbooks();\n    refreshInspirationFlipbooks();\n  }, [refreshUserFlipbooks, refreshInspirationFlipbooks]);\n\n  return {\n    // Data\n    userFlipbooks,\n    inspirationFlipbooks,\n\n    // Loading states\n    loading,\n    userFlipbooksLoading,\n    inspirationFlipbooksLoading,\n\n    // Error state\n    error,\n\n    // Operations\n    refreshUserFlipbooks,\n    refreshInspirationFlipbooks,\n    createFlipbook,\n    copyInspirationFlipbook,\n    updateFlipbook,\n    deleteFlipbook,\n    setFlipbookPreferred,\n  };\n};\n"], "mappings": "AAAA;AACA;AAEA,OAASA,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CASxD,OAASC,UAAU,KAAQ,yBAAyB,CACpD,OAASC,cAAc,KAAQ,6BAA6B,CAE5D;AACA,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1B,MAAO,CAAAC,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAAK,OAAO,CAAGL,UAAU,CAAGC,cAAc,CACrF,CAAC,CAyBD,MAAO,MAAM,CAAAK,YAAY,CAAGA,CAAA,GAA0B,CACpD;AACA,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGX,QAAQ,CAAc,EAAE,CAAC,CACnE,KAAM,CAACY,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGb,QAAQ,CAAgB,EAAE,CAAC,CACnF,KAAM,CAACc,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CACvE,KAAM,CAACgB,2BAA2B,CAAEC,8BAA8B,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CACrF,KAAM,CAACkB,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,QAAQ,CAAgB,IAAI,CAAC,CAEvD,KAAM,CAAAoB,GAAG,CAAGf,aAAa,CAAC,CAAC,CAE3B;AACA,KAAM,CAAAgB,OAAO,CAAGP,oBAAoB,EAAIE,2BAA2B,CAEnE;AACA,KAAM,CAAAM,oBAAoB,CAAGpB,WAAW,CAAC,SAAY,CACnD,GAAI,CACFa,uBAAuB,CAAC,IAAI,CAAC,CAC7BI,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAI,QAAkC,CAAG,KAAM,CAAAH,GAAG,CAACI,cAAc,CAAC,CAAC,CAErE,GAAID,QAAQ,CAACE,OAAO,EAAIF,QAAQ,CAACG,IAAI,CAAE,CACrCf,gBAAgB,CAACY,QAAQ,CAACG,IAAI,CAAC,CACjC,CAAC,IAAM,CACLP,QAAQ,CAACI,QAAQ,CAACL,KAAK,EAAI,gCAAgC,CAAC,CAC9D,CACF,CAAE,MAAOS,GAAG,CAAE,CACZR,QAAQ,CAACQ,GAAG,WAAY,CAAAC,KAAK,CAAGD,GAAG,CAACE,OAAO,CAAG,gCAAgC,CAAC,CACjF,CAAC,OAAS,CACRd,uBAAuB,CAAC,KAAK,CAAC,CAChC,CACF,CAAC,CAAE,CAACK,GAAG,CAAC,CAAC,CAET;AACA,KAAM,CAAAU,2BAA2B,CAAG5B,WAAW,CAAC,SAAY,CAC1D,GAAI,CACFe,8BAA8B,CAAC,IAAI,CAAC,CACpCE,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAI,QAAoC,CAAG,KAAM,CAAAH,GAAG,CAACW,uBAAuB,CAAC,CAAC,CAEhF,GAAIR,QAAQ,CAACE,OAAO,EAAIF,QAAQ,CAACG,IAAI,CAAE,CACrCb,uBAAuB,CAACU,QAAQ,CAACG,IAAI,CAAC,CACxC,CAAC,IAAM,CACLP,QAAQ,CAACI,QAAQ,CAACL,KAAK,EAAI,uCAAuC,CAAC,CACrE,CACF,CAAE,MAAOS,GAAG,CAAE,CACZR,QAAQ,CAACQ,GAAG,WAAY,CAAAC,KAAK,CAAGD,GAAG,CAACE,OAAO,CAAG,uCAAuC,CAAC,CACxF,CAAC,OAAS,CACRZ,8BAA8B,CAAC,KAAK,CAAC,CACvC,CACF,CAAC,CAAE,CAACG,GAAG,CAAC,CAAC,CAET;AACA,KAAM,CAAAY,cAAc,CAAG9B,WAAW,CAAC,KAAO,CAAA+B,OAA8B,EAA6B,CACnG,GAAI,CACFd,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAI,QAA6B,CAAG,KAAM,CAAAH,GAAG,CAACc,iBAAiB,CAACD,OAAO,CAAC,CAE1E,GAAIV,QAAQ,CAACE,OAAO,EAAIF,QAAQ,CAACG,IAAI,CAAE,CACrC;AACA,KAAM,CAAAJ,oBAAoB,CAAC,CAAC,CAC5B,MAAO,CAAAC,QAAQ,CAACG,IAAI,CACtB,CAAC,IAAM,CACLP,QAAQ,CAACI,QAAQ,CAACL,KAAK,EAAI,2BAA2B,CAAC,CACvD,MAAO,KAAI,CACb,CACF,CAAE,MAAOS,GAAG,CAAE,CACZR,QAAQ,CAACQ,GAAG,WAAY,CAAAC,KAAK,CAAGD,GAAG,CAACE,OAAO,CAAG,2BAA2B,CAAC,CAC1E,MAAO,KAAI,CACb,CACF,CAAC,CAAE,CAACT,GAAG,CAAEE,oBAAoB,CAAC,CAAC,CAE/B;AACA,KAAM,CAAAa,uBAAuB,CAAGjC,WAAW,CAAC,KAAO,CAAA+B,OAA+B,EAA6B,CAC7G,GAAI,CACFd,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAI,QAA6B,CAAG,KAAM,CAAAH,GAAG,CAACe,uBAAuB,CAACF,OAAO,CAAC,CAEhF,GAAIV,QAAQ,CAACE,OAAO,EAAIF,QAAQ,CAACG,IAAI,CAAE,CACrC;AACA,KAAM,CAAAJ,oBAAoB,CAAC,CAAC,CAC5B,MAAO,CAAAC,QAAQ,CAACG,IAAI,CACtB,CAAC,IAAM,CACLP,QAAQ,CAACI,QAAQ,CAACL,KAAK,EAAI,qCAAqC,CAAC,CACjE,MAAO,KAAI,CACb,CACF,CAAE,MAAOS,GAAG,CAAE,CACZR,QAAQ,CAACQ,GAAG,WAAY,CAAAC,KAAK,CAAGD,GAAG,CAACE,OAAO,CAAG,qCAAqC,CAAC,CACpF,MAAO,KAAI,CACb,CACF,CAAC,CAAE,CAACT,GAAG,CAAEE,oBAAoB,CAAC,CAAC,CAE/B;AACA,KAAM,CAAAc,cAAc,CAAGlC,WAAW,CAAC,KAAO,CAAA+B,OAA8B,EAAuB,CAC7F,GAAI,CACFd,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAI,QAA8B,CAAG,KAAM,CAAAH,GAAG,CAACgB,cAAc,CAACH,OAAO,CAAC,CAExE,GAAIV,QAAQ,CAACE,OAAO,CAAE,CACpB;AACA,KAAM,CAAAH,oBAAoB,CAAC,CAAC,CAC5B,MAAO,KAAI,CACb,CAAC,IAAM,CACLH,QAAQ,CAACI,QAAQ,CAACL,KAAK,EAAI,2BAA2B,CAAC,CACvD,MAAO,MAAK,CACd,CACF,CAAE,MAAOS,GAAG,CAAE,CACZR,QAAQ,CAACQ,GAAG,WAAY,CAAAC,KAAK,CAAGD,GAAG,CAACE,OAAO,CAAG,2BAA2B,CAAC,CAC1E,MAAO,MAAK,CACd,CACF,CAAC,CAAE,CAACT,GAAG,CAAEE,oBAAoB,CAAC,CAAC,CAE/B;AACA,KAAM,CAAAe,cAAc,CAAGnC,WAAW,CAAC,KAAO,CAAAoC,WAAmB,EAAuB,CAClF,GAAI,CACFnB,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAI,QAA8B,CAAG,KAAM,CAAAH,GAAG,CAACiB,cAAc,CAACC,WAAW,CAAC,CAE5E,GAAIf,QAAQ,CAACE,OAAO,CAAE,CACpB;AACA,KAAM,CAAAH,oBAAoB,CAAC,CAAC,CAC5B,MAAO,KAAI,CACb,CAAC,IAAM,CACLH,QAAQ,CAACI,QAAQ,CAACL,KAAK,EAAI,2BAA2B,CAAC,CACvD,MAAO,MAAK,CACd,CACF,CAAE,MAAOS,GAAG,CAAE,CACZR,QAAQ,CAACQ,GAAG,WAAY,CAAAC,KAAK,CAAGD,GAAG,CAACE,OAAO,CAAG,2BAA2B,CAAC,CAC1E,MAAO,MAAK,CACd,CACF,CAAC,CAAE,CAACT,GAAG,CAAEE,oBAAoB,CAAC,CAAC,CAE/B;AACA,KAAM,CAAAiB,oBAAoB,CAAGrC,WAAW,CAAC,MAAOoC,WAAmB,CAAEE,WAAoB,GAAuB,CAC9G,GAAI,CACFrB,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAI,QAA8B,CAAG,KAAM,CAAAH,GAAG,CAACmB,oBAAoB,CAACD,WAAW,CAAEE,WAAW,CAAC,CAE/F,GAAIjB,QAAQ,CAACE,OAAO,CAAE,CACpB;AACA,KAAM,CAAAH,oBAAoB,CAAC,CAAC,CAC5B,MAAO,KAAI,CACb,CAAC,IAAM,CACLH,QAAQ,CAACI,QAAQ,CAACL,KAAK,EAAI,mCAAmC,CAAC,CAC/D,MAAO,MAAK,CACd,CACF,CAAE,MAAOS,GAAG,CAAE,CACZR,QAAQ,CAACQ,GAAG,WAAY,CAAAC,KAAK,CAAGD,GAAG,CAACE,OAAO,CAAG,mCAAmC,CAAC,CAClF,MAAO,MAAK,CACd,CACF,CAAC,CAAE,CAACT,GAAG,CAAEE,oBAAoB,CAAC,CAAC,CAE/B;AACArB,SAAS,CAAC,IAAM,CACdqB,oBAAoB,CAAC,CAAC,CACtBQ,2BAA2B,CAAC,CAAC,CAC/B,CAAC,CAAE,CAACR,oBAAoB,CAAEQ,2BAA2B,CAAC,CAAC,CAEvD,MAAO,CACL;AACApB,aAAa,CACbE,oBAAoB,CAEpB;AACAS,OAAO,CACPP,oBAAoB,CACpBE,2BAA2B,CAE3B;AACAE,KAAK,CAEL;AACAI,oBAAoB,CACpBQ,2BAA2B,CAC3BE,cAAc,CACdG,uBAAuB,CACvBC,cAAc,CACdC,cAAc,CACdE,oBACF,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}