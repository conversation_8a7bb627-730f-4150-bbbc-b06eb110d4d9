@font-face { font-family: 'GillSans-Light'; src: url('/fonts/Flipbook/GillSans-Light.eot'); src: url('/fonts/Flipbook/GillSans-Light.woff2') format('woff2'), url('/fonts/Flipbook/GillSans-Light.woff') format('woff'), url('/fonts/Flipbook/GillSans-Light.ttf') format('truetype'), url('/fonts/Flipbook/GillSans-Light.svg#GillSans-Light') format('svg'), url('/fonts/Flipbook/GillSans-Light.eot?#iefix') format('/fonts/Flipbook/embedded-opentype'); font-weight: normal; font-style: normal; }
@font-face { font-family: 'GillSansB'; src: url('/fonts/GillSans-Bold.eot?#iefix') format('embedded-opentype'), url('/fonts/GillSans-Bold.otf') format('opentype'), url('/fonts/GillSans-Bold.woff') format('woff'), url('/fonts/GillSans-Bold.ttf') format('truetype'), url('/fonts/GillSans-Bold.svg#GillSans-Bold') format('svg'); font-weight: normal; font-style: normal; }
@font-face { font-family: 'GillSans UltraBold'; src: url('/fonts/GillSans-UltraBold.eot?#iefix') format('embedded-opentype'), url('/fonts/GillSans-UltraBold.ttf') format('opentype'), url('/fonts/GillSans-UltraBold.woff') format('woff'), url('/fonts/GillSans-UltraBold.ttf') format('truetype'), url('/fonts/GillSans-UltraBold.svg#GillSans-UltraBold') format('svg'); font-weight: normal; font-style: normal; }
@font-face { font-family: 'GillSans'; src: url('/fonts/gillsans.eot'); src: local(''), url('/fonts/gillsans.woff') format('woff'), url('/fonts/gillsans.ttf') format('truetype'), url('/fonts/gillsans.svg') format('svg'); font-weight: normal; font-style: normal; }
@font-face { font-family: 'OptimaNovaLT-Black'; src: url('/fonts/Flipbook/OptimaNovaLT-Black.eot'); src: url('/fonts/Flipbook/OptimaNovaLT-Black.woff2') format('woff2'), url('/fonts/Flipbook/OptimaNovaLT-Black.woff') format('woff'), url('/fonts/Flipbook/OptimaNovaLT-Black.ttf') format('truetype'), url('/fonts/Flipbook/OptimaNovaLT-Black.svg#OptimaNovaLT-Black') format('svg'), url('/fonts/Flipbook/OptimaNovaLT-Black.eot?#iefix') format('embedded-opentype'); font-weight: normal; font-style: normal; }
@font-face { font-family: 'Gill Sans MT'; src: url('/fonts/GillSansMT.eot'); src: url('/fonts/GillSansMT.eot?#iefix') format('embedded-opentype'), url('/fonts/GillSansMT.woff') format('woff'), url('/fonts/GillSansMT.ttf') format('truetype'); font-weight: normal; font-style: normal; }
@font-face { font-family: 'gill_sanssemibold'; src: url('/fonts/Flipbook/GillSans-SemiBold.eot') format('embedded-opentype'); src: url('/fonts/Flipbook/gillsans-semibold-webfont.woff2') format('woff2'), url('/fonts/Flipbook/gillsans-semibold-webfont.woff') format('woff'), url('/fonts/Flipbook/GillSans-SemiBold.ttf') format('truetype'), url('GillSans-SemiBold.svg#GillSans-SemiBold') format('svg'); font-weight: normal; font-style: normal; }
@font-face { font-family: 'Sylfaen'; src: url('/fonts/Flipbook/Sylfaen.eot'); src: url('/fonts/Flipbook/Sylfaen.woff') format('woff'), url('/fonts/Flipbook/Sylfaen.ttf') format('truetype'), url('/fonts/Flipbook/Sylfaen.svg#Sylfaen') format('svg'); font-weight: normal; font-style: normal; }
@font-face { font-family: 'PoorRichard-Regular'; src: url('/fonts/Flipbook/PoorRichard-Regular.eot?#iefix') format('embedded-opentype'), url('/fonts/Flipbook/PoorRichard-Regular.woff') format('woff'), url('/fonts/Flipbook/PoorRichard-Regular.ttf') format('truetype'), url('/fonts/Flipbook/PoorRichard-Regular.svg#PoorRichard-Regular') format('svg'); font-weight: normal; font-style: normal; }
@font-face { font-family: 'GillSansCE-Roman'; src: url('/fonts/Flipbook/GillSansCE-Roman.eot'); src: url('/fonts/Flipbook/GillSansCE-Roman.woff2') format('woff2'), url('/fonts/Flipbook/GillSansCE-Roman.woff') format('woff'), url('/fonts/Flipbook/GillSansCE-Roman.ttf') format('truetype'), url('/fonts/Flipbook/GillSansCE-Roman.svg#GillSansCE-Roman') format('svg'), url('/fonts/Flipbook/GillSansCE-Roman.eot?#iefix') format('embedded-opentype'); font-weight: normal; font-style: normal; }
@font-face { font-family: 'Zapfino'; src: url('/fonts/Zapfino.eot');src: url('/fonts/Zapfino.woff') format('woff'), url('/fonts/Zapfino.ttf') format('truetype'), url('/fonts/Zapfino.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face { font-family: 'palisadebold'; src: url('/fonts/palisade_bold-webfont.woff2') format('woff2'), url('/fonts/palisade_bold-webfont.woff') format('woff'); font-weight: normal; font-style: normal;}
/*VR 2018-10-17 Start*/
@font-face {font-family: 'Aileron-Regular';src: url('/fonts/editorfonts/Aileron-Regular.eot');src: url('/fonts/editorfonts/Aileron-Regular.woff') format('woff'), url('/fonts/editorfonts/Aileron-Regular.ttf') format('truetype'), url('/fonts/editorfonts/Aileron-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Allura-Regular';src: url('/fonts/editorfonts/Allura-Regular.eot');src: url('/fonts/editorfonts/Allura-Regular.woff') format('woff'), url('/fonts/editorfonts/Allura-Regular.ttf') format('truetype'), url('/fonts/editorfonts/Allura-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Arvo-Regular';src: url('/fonts/editorfonts/Arvo-Regular.eot');src: url('/fonts/editorfonts/Arvo-Regular.woff') format('woff'), url('/fonts/editorfonts/Arvo-Regular.ttf') format('truetype'), url('/fonts/editorfonts/Arvo-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'CinzelDecorative-Regular';src: url('/fonts/editorfonts/CinzelDecorative-Regular.eot');src: url('/fonts/editorfonts/CinzelDecorative-Regular.woff') format('woff'), url('/fonts/editorfonts/CinzelDecorative-Regular.ttf') format('truetype'), url('/fonts/editorfonts/CinzelDecorative-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Cinzel-Regular';src: url('/fonts/editorfonts/Cinzel-Regular.eot');src: url('/fonts/editorfonts/Cinzel-Regular.woff') format('woff'), url('/fonts/editorfonts/Cinzel-Regular.ttf') format('truetype'), url('/fonts/editorfonts/Cinzel-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'CodePro_Demo';src: url('/fonts/editorfonts/Code_Pro_Demo.eot');src: url('/fonts/editorfonts/Code_Pro_Demo.woff') format('woff'), url('/fonts/editorfonts/Code_Pro_Demo.ttf') format('truetype'), url('/fonts/editorfonts/Code_Pro_Demo.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'GlacialIndifference-Regular';src: url('/fonts/editorfonts/GlacialIndifference-Regular.eot');src: url('/fonts/editorfonts/GlacialIndifference-Regular.woff') format('woff'), url('/fonts/editorfonts/GlacialIndifference-Regular.ttf') format('truetype'), url('/fonts/editorfonts/GlacialIndifference-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Glegoo-Regular';src: url('/fonts/editorfonts/Glegoo-Regular.eot');src: url('/fonts/editorfonts/Glegoo-Regular.woff') format('woff'), url('/fonts/editorfonts/Glegoo-Regular.ttf') format('truetype'), url('/fonts/editorfonts/Glegoo-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'JosefinSans-Regular';src: url('/fonts/editorfonts/JosefinSans-Regular.eot');src: url('/fonts/editorfonts/JosefinSans-Regular.woff') format('woff'), url('/fonts/editorfonts/JosefinSans-Regular.ttf') format('truetype'), url('/fonts/editorfonts/JosefinSans-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'JosefinSlab-Regular';src: url('/fonts/editorfonts/JosefinSlab-Regular.eot');src: url('/fonts/editorfonts/JosefinSlab-Regular.woff') format('woff'), url('/fonts/editorfonts/JosefinSlab-Regular.ttf') format('truetype'), url('/fonts/editorfonts/JosefinSlab-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'JuliusSansOne-Regular';src: url('/fonts/editorfonts/JuliusSansOne-Regular.eot');src: url('/fonts/editorfonts/JuliusSansOne-Regular.woff') format('woff'), url('/fonts/editorfonts/JuliusSansOne-Regular.ttf') format('truetype'), url('/fonts/editorfonts/JuliusSansOne-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Kollektif';src: url('/fonts/editorfonts/Kollektif.eot');src: url('/fonts/editorfonts/Kollektif.woff') format('woff'), url('/fonts/editorfonts/Kollektif.ttf') format('truetype'), url('/fonts/editorfonts/Kollektif.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'LibreBaskerville-Regular';src: url('/fonts/editorfonts/LibreBaskerville-Regular.eot');src: url('/fonts/editorfonts/LibreBaskerville-Regular.woff') format('woff'), url('/fonts/editorfonts/LibreBaskerville-Regular.ttf') format('truetype'), url('/fonts/editorfonts/LibreBaskerville-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Lustria-Regular';src: url('/fonts/editorfonts/Lustria-Regular.eot');src: url('/fonts/editorfonts/Lustria-Regular.woff') format('woff'), url('/fonts/editorfonts/Lustria-Regular.ttf') format('truetype'), url('/fonts/editorfonts/Lustria-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'MontserratAlternates-Regular';src: url('/fonts/editorfonts/MontserratAlternates-Regular.eot');src: url('/fonts/editorfonts/MontserratAlternates-Regular.woff') format('woff'), url('/fonts/editorfonts/MontserratAlternates-Regular.ttf') format('truetype'), url('/fonts/editorfonts/MontserratAlternates-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Montserrat-Regular';src: url('/fonts/editorfonts/Montserrat-Regular.eot');src: url('/fonts/editorfonts/Montserrat-Regular.woff') format('woff'), url('/fonts/editorfonts/Montserrat-Regular.ttf') format('truetype'), url('/fonts/editorfonts/Montserrat-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'NixieOne-Regular';src: url('/fonts/editorfonts/NixieOne-Regular.eot');src: url('/fonts/editorfonts/NixieOne-Regular.woff') format('woff'), url('/fonts/editorfonts/NixieOne-Regular.ttf') format('truetype'), url('/fonts/editorfonts/NixieOne-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Pacifico';src: url('/fonts/editorfonts/Pacifico.eot');src: url('/fonts/editorfonts/Pacifico.woff') format('woff'), url('/fonts/editorfonts/Pacifico.ttf') format('truetype'), url('/fonts/editorfonts/Pacifico.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Parisienne-regular';src: url('/fonts/editorfonts/parisienne-regular.eot');src: url('/fonts/editorfonts/parisienne-regular.woff') format('woff'), url('/fonts/editorfonts/parisienne-regular.ttf') format('truetype'), url('/fonts/editorfonts/parisienne-regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'PinyonScript-Regular';src: url('/fonts/editorfonts/PinyonScript-Regular.eot');src: url('/fonts/editorfonts/PinyonScript-Regular.woff') format('woff'), url('/fonts/editorfonts/PinyonScript-Regular.ttf') format('truetype'), url('/fonts/editorfonts/PinyonScript-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'PlayfairDisplay-Regular';src: url('/fonts/editorfonts/PlayfairDisplay-Regular.eot');src: url('/fonts/editorfonts/PlayfairDisplay-Regular.woff') format('woff'), url('/fonts/editorfonts/PlayfairDisplay-Regular.ttf') format('truetype'), url('/fonts/editorfonts/PlayfairDisplay-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'PlayfairDisplaySC-Regular';src: url('/fonts/editorfonts/PlayfairDisplaySC-Regular.eot');src: url('/fonts/editorfonts/PlayfairDisplaySC-Regular.woff') format('woff'), url('/fonts/editorfonts/PlayfairDisplaySC-Regular.ttf') format('truetype'), url('/fonts/editorfonts/PlayfairDisplaySC-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Quattrocento-Regular';src: url('/fonts/editorfonts/Quattrocento-Regular.eot');src: url('/fonts/editorfonts/Quattrocento-Regular.woff') format('woff'), url('/fonts/editorfonts/Quattrocento-Regular.ttf') format('truetype'), url('/fonts/editorfonts/Quattrocento-Regular.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'AvenirLTStd-Black'; src: url('/fonts/Flipbook/AvenirLTStd-Black.otf') format('opentype');}
@font-face {font-family: 'AvenirLTStd-Book';src: url('/fonts/Flipbook/AvenirLTStd-Book.otf') format('opentype');}
@font-face {font-family: 'avenirltstd-heavy';src: url('/fonts/Avenir85Heavy.otf') format('opentype');}
@font-face {font-family: 'Avenir-Book';src: url('/fonts/Flipbook/Avenir-Book.eot');src: url('/fonts/Flipbook/Avenir-Book.woff') format('woff'), url('/fonts/Flipbook/Avenir-Book.ttf') format('truetype'), url('/fonts/Flipbook/Avenir-Book.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: "PalatinoLTStd-Light";src: url("/fonts/editorfonts/Palatino LT Std Light.eot");src: url("/fonts/editorfonts/Palatino LT Std Light.eot?#iefix") format("embedded-opentype"), url("/fonts/editorfonts/Palatino LT Std Light.woff2") format("woff2"), url("/fonts/editorfonts/Palatino LT Std Light.woff") format("woff"), url("/fonts/editorfonts/Palatino LT Std Light.ttf") format("truetype"), url("/fonts/editorfonts/Palatino LT Std Light.svg#Palatino LT Std Light") format("svg");}
@font-face {font-family: 'Palatino';src: url('/fonts/editorfonts/palatino.eot');src: url('/fonts/editorfonts/palatino.woff') format('woff'), url('/fonts/editorfonts/palatino.ttf') format('truetype'), url('/fonts/editorfonts/palatino.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'OptimaBold';src:url('/fonts/OptimaBold.eot');src:url('/fonts/OptimaBold.eot') format('embedded-opentype'), url('/fonts/OptimaBold.woff2') format('woff2'), url('/fonts/OptimaBold.woff') format('woff'), url('/fonts/OptimaBold.ttf') format('truetype'), url('/fonts/OptimaBold.svg#OptimaBold') format('svg');}
@font-face {font-family: 'GillSans-SemiBold';src: url('/fonts/Flipbook/GillSans-SemiBold.eot') format('embedded-opentype');src: url('/fonts/Flipbook/gillsans-semibold-webfont.woff2') format('woff2'), url('/fonts/Flipbook/gillsans-semibold-webfont.woff') format('woff'), url('/fonts/Flipbook/GillSans-SemiBold.ttf') format('truetype'), url('GillSans-SemiBold.svg#GillSans-SemiBold') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Avenir-Light';src: url('/fonts/Avenir-Light.eot?#iefix') format('embedded-opentype'), url('/fonts/Avenir-Light.woff') format('woff'), url('/fonts/Avenir-Light.ttf') format('truetype'), url('/fonts/Avenir-Light.svg#Avenir-Light') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Avenir-Heavy';src: url('/fonts/Avenir-Heavy.eot?#iefix') format('embedded-opentype'), url('/fonts/Avenir-Heavy.woff') format('woff'), url('/fonts/Avenir-Heavy.ttf') format('truetype'), url('/fonts/Avenir-Heavy.svg#Avenir-Heavy') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Rollerscript-Smooth'; src: url('/fonts/Avenir-Heavy.eot?#iefix') format('embedded-opentype'), url('/fonts/Avenir-Heavy.woff') format('woff'), url('/fonts/Avenir-Heavy.ttf') format('truetype'), url('/fonts/Avenir-Heavy.svg#Avenir-Heavy') format('svg');font-weight: normal;font-style: normal;}/*HN ST-1777 add font*/
@font-face {font-family: 'Copperplate';src: url('/fonts/Copperplate.woff') format('woff'), url('/fonts/Copperplate.ttf') format('truetype'), url('/fonts/Copperplate.svg#Copperplate') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Neutra-Text-Book';src: url('/fonts/Neutra Text Book.eot');src: url('/fonts/Neutra Text Book.woff') format('woff'), url('/fonts/Neutra Text Book.ttf') format('truetype'), url('/fonts/Neutra Text Book.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'NeutraText-Light';src: url('/fonts/NeutraText-Light.eot');src: url('/fonts/NeutraText-Light.woff') format('woff'), url('/fonts//NeutraText-Light.ttf') format('truetype'), url('/fonts/NeutraText-Light.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Helvetica-Bold';src: url('/fonts/Helvetica-Bold.eot');src: url('/fonts/Helvetica-Bold.woff') format('woff'), url('/fonts/Helvetica-Bold.ttf') format('truetype'), url('/fonts/Helvetica-Bold.svg') format('svg');font-weight: normal;font-style: normal;}
@font-face {font-family: 'GaramondNormal';src: url('/fonts/GaramondNormal.eot');src: url('/fonts/GaramondNormal.eot') format('embedded-opentype'),url('../../fonts/GaramondNormal.woff2') format('woff2'),url('/fonts/GaramondNormal.woff') format('woff'),url('../../fonts/GaramondNormal.ttf') format('truetype'),url('/fonts/GaramondNormal.svg#GaramondNormal') format('svg');}
body { font-family: 'Gill Sans MT'; position: relative; height: 100%; }
body.set_position { position: relative; }
html { height: auto; }
a, a:focus, a:hover { outline: none !important; }
/****642 styles***/
.create_flip { margin: 20px; outline: 0; }
.create_flip:focus, .create_flip:active { outline: 0; }
/*ST-1281 */
.back-dropmodal {height: 100%; }
.model-info-icon { display:none; }
.model-info-icon .icndelete, .model-info-icon .icnnsuccess  { display:none;}
.model-info-icon.delete-icon , .delete-icon .icndelete { display:block !important; }
.model-info-icon.nsuccess-icon , .nsuccess-icon .icnnsuccess { display:block !important; }
.tick-left.show-tick .dropdown-menu li.selected a span.check-mark {left:7px;right:initial;}
.tick-left.show-tick .dropdown-menu li a { padding-left:34px;}
.new_flip .modal-body .new_flip_name_span{margin-bottom: 12px;}
.new_flip .modal-body .new_flip_name_span span { display: inline-block; min-width: 18px;}
.new_flip .new_flip_name .form-control::-webkit-input-placeholder {color: #000;}
.new_flip .new_flip_name .form-control::-moz-placeholder {color: #000;}
.new_flip .new_flip_name .form-control:-ms-input-placeholder {color: #000;}
.new_flip .new_flip_name .form-control:-moz-placeholder {color: #000;}
.modal_back .modal-backdrop { background-color: #FFF; }
.add_page_flip { text-align: center; }
/* PK - ST-1313 , Align Modal Popup To Center */
body .modal {padding: 0;border-radius: 0;background-color: transparent;max-width: 100%;width: 100% !important;-webkit-transform: translate(-50%, -50%) !important;-moz-transform: translate(-50%, -50%) !important;-o-transform: translate(-50%, -50%) !important;    -ms-transform: translate(-50%, -50%) !important;transform: translate(-50%, -50%) !important;margin: 0 auto;max-height: calc(100% - 50px) !important;position: fixed;top: 50% !important;left: 50% !important;}
body .modal.color_picker_modal { top: 0 !important; -webkit-transform: translate(0) !important; -moz-transform: translate(0) !important; -o-transform: translate(0) !important; -ms-transform: translate(0) !important; transform: translate(0) !important; margin: 0 auto;left: auto !important; max-height: 100% !important; }
body .modal.add_page_flip { overflow-x: hidden; overflow-y: scroll; }
/*877*/
.insert_page { font-family: 'gill_sanssemibold'; border: 1px solid #57307a; color: #57307a; border-radius: 30px; text-transform: uppercase; padding: 6px 30px; text-decoration: none; display: inline-block; font-size: 13px; margin: 25px 0 0; transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s; }
.insert_page:hover, .insert_page:focus { background-color: #57307a; color: #FFF; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; text-decoration: none; }
.close_popup { position: absolute; top: 12px; right: 12px; }
.close_popup img { width: 20px; }
/****end 642 styles***/

/****638 styles***/
.flip_wrapper { width: 600px; height: 579px; margin: 0 auto 40px; border: 1px solid #000; position: relative; }
.flip_wrapper .new_flip_end { background: url(/images/endpapers.jpg) top center no-repeat; background-size: 100% 100%; height: 577px; width: 100%; padding: 25px 30px; }
.flip_wrapper .new_flip_end .inside_flip { text-align: center; width: 100%; position: relative; background-color: #fff; height: 100%; }
.flip_wrapper .new_flip_end .flip_nicolaus { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); -webkit-transform: translate(-50%, -50%); -ms-transform: translate(-50%, -50%); -moz-transform: translate(-50%, -50%); -o-transform: translate(-50%, -50%); border: 1px solid #000; padding: 8px; width: 250px; }
.flip_wrapper .new_flip_end .flip_nicolaus h1 { font-size: 24px; color: rgba(35, 31, 32, 0.7); margin: 0; font-weight: normal; }
.flip_wrapper .new_flip_end .flip_nicolaus .fred_number { padding: 0 15px 0 0; }
.flip_wrapper .new_flip_end .flip_nicolaus span { font-size: 13px; color: #231F20; display: inline-block; width: 49%; }
.flip_wrapper .new_flip_end .flip_nicolaus a { font-size: 10px; text-decoration: none; color: #231F20; display: inline-block; margin: 6px 0 0; font-weight: normal; }
.flip_wrapper .flip_bottom { position: absolute; left: 0; right: 0; bottom: 25px; }
.flip_wrapper .flip_bottom img { width: 180px; }
.trade_logo_copy { margin: 0 0 20px; }
.edit_text_click { width: 600px; margin: 20px auto 8px; }
.edit_text_click p { margin: 0; font-size: 20px; color: #000; font-family: 'GillSans-Light'; }
.flip_overlay { position: absolute; left: 0; right: 0; top: 0; bottom: 0; background-color: rgba(255, 255, 255, 0.6); }
/****end 638 styles***/

/****646 styles***/
.flip_fred_img { border: 15px solid #000; position: relative; height: 100%; }
.flip_fred_img img { width: 100%; height: 100%; }
.flip_fred_img .flip_fred_text { position: absolute; top: 42%; background-color: rgba(255, 255, 255, 0.6); padding: 2px 0 2px 20px; right: 0; width: 200px; }
.flip_fred_img .flip_fred_text h2 { color: #000; margin: 0; padding: 0; font-size: 40px; font-weight: normal; font-family: 'Gill Sans MT'; }
.flip_fred_img .flip_fred_text h2 hr { height: 1px; width: 100%; background-color: #000; margin: 3px 0; display: block; }
.flip_fred_img .flip_fred_text h2 b { font-family: 'OptimaNovaLT-Black'; font-size: 39px; letter-spacing: 3px; }
/****end 646 styles***/
.spnAlertMessagestext {color: #4a4a4a !important;}/* HN ST-1781 add class*/
.spantext1 {font-size: 25px; font-family: GillSans;font-weight: 10}
/* HN ST-1781 add class*/
/****657 STyles****/
/* ST 887 PK 01/04/2018 */
.main_heading h1 {color: #000000;font-size: 30px;font-family: 'gill_sanssemibold';text-transform: uppercase;margin: 15px 0 10px;}
.main_heading h2 { color: #000; margin: 10px 0 25px; position: relative; padding-left: 80px; font-size: 16px; }
.main_heading h2 img { width: 60px; position: absolute; left: 10px; top: -25px; }
.image_gallery ul { margin: 0; padding: 0; }
.image_gallery ul li { margin: 0 15px; outline: 0; position: relative; }
.image_gallery .gallery_btn, .img_pop_gallery_wrapper .img_pop_gallery .gallery_btn { position: absolute; right: 0px; top: 0; z-index: 1; }
.divMyUploadLibary.image_gallery .gallery_btn {top: 1px;right: 2px}/*RP ST-1674*/ /*ST-1404 , 1554 PK,01/21/2018*/
.image_gallery .gallery_btn span, .img_pop_gallery_wrapper .img_pop_gallery .gallery_btn span {  width: 16px !important;height: 16px !important; display: inline-block;cursor: pointer;  margin-right: 0px !important; text-align: center;  } /*ST-682 16x16*//*RP ST-1627*/
.image_gallery .gallery_btn span:last-child, .img_pop_gallery_wrapper .img_pop_gallery .gallery_btn span:last-child { margin: 0; }
.image_gallery .gallery_btn span img { width: 12px !important;height:12px !important; margin:0 auto; /*ST-886 PK */ display: block; }
.img_pop_gallery_wrapper .img_pop_gallery .gallery_btn {right:2px;}/*ST-1891*/
/* Glisse: Overlay */
#glisse-overlay { background: rgba(0, 0, 0.3); }
#glisse-close { height: 40px; width: 40px; text-align: center; position: fixed; top: 10.2%; right: 10.1%; background: url("/images/close_icon.png") no-repeat; background-size: cover; z-index: 10000; cursor: pointer; }
.glisse-next a, .glisse-prev a { width: 40px; height: 40px; display: block; position: fixed; bottom: 10px; line-height: 40px; }
.glisse-next a { left: 50%; margin-left: 20px; text-decoration: none; text-align: center; }
.glisse-next a:hover, .glisse-next a:focus, .glisse-prev a:hover, .glisse-prev a:focus { text-decoration: none; }
.glisse-next a:before { content: "\f105"; font-family: "FontAwesome"; font-size: 40px; color: #000; display: block; }
.glisse-prev a { left: 50%; margin-left: -40px; text-align: center; text-decoration: none; }
.glisse-prev a:before { content: "\f104"; font-family: "FontAwesome"; font-size: 40px; color: #000; display: block; }
#glisse-overlay, #glisse-close, #glisse-controls, #glisse-spinner { opacity: 0; }
#glisse-overlay { position: fixed; top: 0; bottom: 0; left: 0; right: 0; background-color: rgba(255,255,255,.75); }
#glisse-overlay.loading #glisse-spinner { opacity: 1; }
#glisse-overlay-content { position: fixed; top: 10.1%; right: 10%; bottom: 10%; left: 10%; background-repeat: no-repeat; background-position: 50% 50%; opacity: 0; -moz-background-size: cover; -webkit-background-size: cover; -o-background-size: cover; background-size: cover; -webkit-backface-visibility: hidden; backface-visibility: hidden; border: 1px solid #979797; z-index: 999; }
#glisse-overlay-content img { display: none; }
#glisse-overlay-content.shake { -webkit-animation: shake 600ms 1; -moz-animation: shake 600ms 1; -ms-animation: shake 600ms 1; -o-animation: shake 600ms 1; animation: shake 600ms 1; }
#glisse-spinner { position: fixed; top: 50%; left: 50%; margin: -40px 0 0 -40px; height: 80px; width: 80px; background: rgba(0,0,0,.6) url("/images/loader.gif") center center no-repeat; border-radius: 6px; }
.glisse-next a, .glisse-prev a { -webkit-transition: all 100ms ease; -moz-transition: all 100ms ease; -o-transition: all 100ms ease; -ms-transition: all 100ms ease; transition: all 100ms ease; -webkit-user-select: none; -moz-user-select: none; user-select: none; }
#glisse-controls .ended a { opacity: .3; cursor: default; } /* disabled effect */
/* Glisse: Keyframes */
@-webkit-keyframes shake { 0%, 100% {
-webkit-transform: translateX(0);
}
 10%, 30%, 50%, 70%, 90% {
-webkit-transform: translateX(-10px);
}
 20%, 40%, 60%, 80% {
-webkit-transform: translateX(10px);
}
}
 @-moz-keyframes shake { 0%, 100% {
-moz-transform: translateX(0);
}
 10%, 30%, 50%, 70%, 90% {
-moz-transform: translateX(-10px);
}
 20%, 40%, 60%, 80% {
-moz-transform: translateX(10px);
}
}
 @-ms-keyframes shake { 0%, 100% {
-ms-transform: translateX(0);
}
 10%, 30%, 50%, 70%, 90% {
-ms-transform: translateX(-10px);
}
 20%, 40%, 60%, 80% {
-ms-transform: translateX(10px);
}
}
 @-o-keyframes shake { 0%, 100% {
-o-transform: translateX(0);
}
 10%, 30%, 50%, 70%, 90% {
-o-transform: translateX(-10px);
}
 20%, 40%, 60%, 80% {
-o-transform: translateX(10px);
}
}
 @keyframes shake { 0%, 100% {
transform: translateX(0);
}
 10%, 30%, 50%, 70%, 90% {
transform: translateX(-10px);
}
 20%, 40%, 60%, 80% {
transform: translateX(10px);
}
}
.image_gallery img { outline: 0; }
.image_lib_wrapper { padding: 0 0 0 15px; }
.image_gallery .responsive .slick-arrow { height: 140px; width: 63px; background-size: 60% 80% !important; background-color: transparent !important; border: 0; position: absolute; top: 12%; outline: 0; }
.image_gallery .responsive .slick-arrow.slick-prev { background: url("/images/left-angel.png") top center no-repeat; left: -55px; }
.image_gallery .responsive .slick-arrow.slick-next { background: url("/images/right-angel.png") top center no-repeat; right: -55px; }
.img_library h3 { color: #000; margin: 30px 0 20px; font-size: 16px; padding-left: 15px; }
.img_pop_gallery_wrapper { height: 490px; overflow-y: auto; margin: 0 -10px 15px 0; }
.img_pop_gallery_wrapper::-webkit-scrollbar {width: 20px;height: 10px;background-color: transparent;}
.img_pop_gallery_wrapper::-webkit-scrollbar-thumb {border-radius: 10px;background-color: #d8d8d8;}
.img_pop_gallery_wrapper .img_pop_gallery { width: 196px; margin: 0 1% 15px 1%; float: left; position: relative; border: 1px solid transparent; transition: all ease-out 0.3s; -webkit-transition: all ease-out 0.3s; -moz-transition: all ease-out 0.3s; }/*ST-1692 ST-1693*/
.img_pop_gallery_wrapper .img_pop_gallery:hover {border: solid 1px #4990e2; transition: all ease-in 0.3s; -webkit-transition: all ease-in 0.3s; -moz-transition: all ease-in 0.3s; background-color: rgba(255, 255, 255, 0.6) !important;}
.img_pop_gallery_wrapper .img_pop_gallery .hover_effect { background-color: rgba(255, 255, 255, 0.4); position: absolute; top: 0; right: 0; bottom: 0; left: 0; opacity: 0; transition: all ease-out 0.3s; -webkit-transition: all ease-out 0.3s; -moz-transition: all ease-out 0.3s; }
.img_pop_gallery_wrapper .img_pop_gallery:hover .hover_effect { opacity: 1; transition: all ease-in 0.3s; -webkit-transition: all ease-in 0.3s; -moz-transition: all ease-in 0.3s; }
.img_pop_gallery_wrapper .img_pop_gallery img { width: 100%; }
#EndPaperGV .img_pop_gallery_wrapper .img_pop_gallery img { width: 100%; height:190px; }
.gallery_btn_wrapper { text-align: right; margin: 0 15px; padding: 15px 0; }

/*ST-886 PK 01/04/2018*/
.gallery_btn_wrapper .cancel_img { border: 1px solid #57307a; color: #57307a; border-radius: 20px; text-transform: uppercase; padding: 6px 40px; text-decoration: none; display: inline-block; margin: 0 0 5px; margin: 0 5px 15px 0; transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s; -ms-transition: all ease-out 0.5s;font-size:13px !important;font-family: 'gill_sanssemibold' !important; }
.gallery_btn_wrapper .cancel_img:hover, .image_lib_wrapper .select_img:hover {color:#777 }/*RP ST-1881*/
.gallery_btn_wrapper .select_img { border: 1px solid #57307a; color: #57307a; border-radius: 20px; text-transform: uppercase; padding: 6px 40px 6px 53px; text-decoration: none; display: inline-block;  margin: 0 0 15px 5px; transition: all ease-in-out 0.5s; -webkit-transition: all ease-in-out 0.5s; -moz-transition: all ease-in-out 0.5s; -ms-transition: all ease-in-out 0.5s; position: relative;font-size:13px !important;font-family: 'gill_sanssemibold' !important; }
.gallery_btn_wrapper a.select_img img { position: absolute; left: 10px; top:3px; transition: all ease-in-out 0.5s; -webkit-transition: all ease-in-out 0.5s; -moz-transition: all ease-in-out 0.5s; -ms-transition: all ease-in-out 0.5s; }
.gallery_btn_wrapper .select_img:hover { background-color: #57307a; color: #FFF; transition: all ease-in-out 0.5s; -webkit-transition: all ease-in-out 0.5s; -moz-transition: all ease-in-out 0.5s; -ms-transition: all ease-in-out 0.5s; }
.gallery_btn_wrapper .select_img .btn_on { opacity: 0; transition: all ease-in-out 0.5s; -webkit-transition: all ease-in-out 0.5s; -moz-transition: all ease-in-out 0.5s; -ms-transition: all ease-in-out 0.5s; }
.gallery_btn_wrapper .select_img:hover .btn_off { opacity: 0; transition: all ease-in-out 0.5s; -webkit-transition: all ease-in-out 0.5s; -moz-transition: all ease-in-out 0.5s; -ms-transition: all ease-in-out 0.5s; }
.gallery_btn_wrapper .select_img:hover .btn_on { opacity: 1; transition: all ease-in-out 0.5s; -webkit-transition: all ease-in-out 0.5s; -moz-transition: all ease-in-out 0.5s; -ms-transition: all ease-in-out 0.5s; }
#proRangeSlider { margin: 0 15px; padding: 15px 0; }
#proRangeSlider #minus, #proRangeSlider #plus, #proRangeSlider #rangeSlider { display: inline-block; vertical-align: middle; cursor: pointer; margin-right: 10px; }
#proRangeSlider #plus { margin-right: 0; }
.msie #proRangeSlider { margin: -30px 15px 0; }
.msie #minus, .msie #plus { margin: -14px 0 0; }
input[type=range] { -webkit-appearance: none; margin: 10px 0; width: 300px; }
input[type=range]:focus { outline: none; }
input[type=range]::-webkit-slider-runnable-track {width: 100%;height: 2px;cursor: pointer;animate: 0.2s;box-shadow: 0px 0px 0px #000, 0px 0px 0px #0d0d0d;background: #a9a9a9;border-radius: 25px;border: 0px solid #000101;}
input[type=range]::-webkit-slider-thumb {box-shadow: 0px 0px 0px #000, 0px 0px 0px #0d0d0d;border: 0px solid #000;height: 20px;width: 20px;border-radius: 50%;background: #000;cursor: pointer;-webkit-appearance: none;margin-top: -10px;}
input[type=range]:focus::-webkit-slider-runnable-track {background: #a9a9a9;}
input[type=range]::-moz-range-track {width: 100%;height: 2px;cursor: pointer;animate: 0.2s;box-shadow: 0px 0px 0px #000, 0px 0px 0px #0d0d0d;background: #a9a9a9;border-radius: 25px;border: 0px solid #000101;}
input[type=range]::-moz-range-thumb {box-shadow: 0px 0px 0px #000, 0px 0px 0px #0d0d0d;border: 0px solid #000;height: 20px;width: 20px;border-radius: 50%;background: #000;cursor: pointer;}
input[type=range]::-ms-track {width: 100%;height: 2px;cursor: pointer;animate: 0.2s;background: transparent;border-color: transparent;border-width: 20px 0;color: transparent;}
input[type=range]::-ms-fill-lower {background: #a9a9a9;border: 0px solid #000101;box-shadow: 0px 0px 0px #000, 0px 0px 0px #0d0d0d;}
input[type=range]::-ms-fill-upper {background: #a9a9a9;border: 0px solid #000101;box-shadow: 0px 0px 0px #000, 0px 0px 0px #0d0d0d;}
input[type=range]::-ms-thumb {box-shadow: 0px 0px 0px #000, 0px 0px 0px #0d0d0d;border: 0px solid #000;height: 20px;width: 20px;border-radius: 50%;background: #000;cursor: pointer;}
/****End 657 STyles****/

/****672 styles****/
.main_wrapper { width: 100%; margin: 0 auto;top: 0; }
.noP { padding: 0; }
/*PK-1322 - 9/10/2018 - Update SideBar Background Color*/
.sidebar {/*ST-901 6%*/ float: left; background-color: #000000; height: 91vh; position: relative; padding-top: 25px; z-index:2 }/*ST-1676*/
#frame_body_outer {    height: 82vh !important;}
.sidebar ul { margin: 0px; padding: 0; text-align: center; }
.sidebar ul li { margin: 0; padding: 5px 0 15px ; display: block; position: relative; }
.sidebar ul li.active:after, .sidebar ul li.active:before { left: 100%; top: 30px; border: solid transparent; content: " "; height: 15px; width: 40px; position: absolute; pointer-events: none; } /*ST-1438 PK 11/21/2018 , Notch Position*/
/*PK-1322 - 9/10/2018 - Update SideBar Background Color For Active Class*/
.sidebar ul li.active:after { border-color: rgba(74, 74, 74, 0); border-left-color: #000000; border-width: 15px; margin-top: -15px; }
.sidebar ul li.active:before { border-color: rgba(74, 74, 74, 0); border-left-color:#000000; border-width: 15px; margin-top: -15px; z-index: 10; }
.sidebar ul li img { width: 26px; }
.sidebar ul li p { margin: 1px 0 0; color: #ffffff; font-size: 15px; font-family: 'GillSans-Light'; line-height: 20px; }
.msie .sidebar ul li p { margin: 2px 0 0; }
.sidebar ul.preview_save { margin: 0;  position: absolute; padding: 0px; z-index: 100; width: 100%; } /*bottom: 3%; ST-1487 , PK 12/06/2018*/ 
.sidebar ul.preview_save li:last-child {    padding-bottom: 5px;}
.selecting_endpaper {/* ST-901 width: 12.5% !important;*/ float: left; height: 91vh; background-color: #d8d8d8; text-align: center; padding: 0 0 10px; position: relative; min-height: 920px;}
.imgEpFilterIcon {height:28px; width:28px;position: absolute;right: 6px;top: 68px;cursor:pointer;}/*ST-1780*/
/*ST-882,885 PK 01/04/2018 */
.selecting_endpaper h3 { font-size: 15px; color: #ffffff; margin: 10px 0;  text-transform: uppercase; padding: 0 35px 0 15px;font-family: 'gill_sanssemibold'; font-style: normal;font-stretch: normal;line-height: normal;letter-spacing: normal; }
.selecting_endpaper h4 { text-transform: uppercase; font-size: 16px; margin: 0 0 5px; color: #000000; text-align: left; padding: 0 35px 0 24%; font-family: 'GillSans-Light'; font-style: normal;font-stretch: normal;line-height: normal;letter-spacing: normal;}
/*.endpaper_listing { padding: 0 0 0 18%; overflow-y: scroll; height: 180px; margin: 0 10px 10px 0; }*/
.endpaper_listing { padding: 0 0 0 18%;  }
.pick_solid_color h4 {font-family: GillSans;font-size: 15px;color: #000000;padding: 0px 15px}
.endpaper_listing ul { margin: 0; padding: 0; }
.endpaper_listing ul li {display: block;margin: 0 10px 10px 0;position: relative}
.endpaper_listing ul li:last-child { margin: 0 10px 0 0; }
.endpaper_listing ul li img { width: 100px; height: 100px;}/*ST-1340*/
.flip_lib_div { border-top: 1px solid #979797; margin: 0 20px 5px 21%; }
/*.flip_lib_listing { padding: 0 0 0 18%; overflow-y: auto; height: 28%; margin: 0 10px 15px 0; }*/
.flip_lib_listing { padding: 0 0 0 18%;}
.flip_lib_listing ul { margin: 0; padding: 0; }
.flip_lib_listing ul li { display: block; margin: 0 10px 10px 0; }
.flip_lib_listing ul li:last-child { margin: 0 10px 0 0; }
.flip_lib_listing ul li img { width: 100px; height: 100px; }/*ST-1340*/
.uploads_btn {   width: 100%;}
.uploads_btn a { display: block; width: 85%; padding: 4px 0; text-align: center; text-decoration: none; border: 1px solid #57307a; color: #6b448d; border-radius: 20px; margin: 0 auto 6px; position: relative; background-color: #FFF; font-family: 'gill_sanssemibold'; text-transform: uppercase; font-size: 11px; }
.uploads_btn a span { position: absolute; left: 10px; color: #417505; }
.flip_view { text-align: center; position: absolute; bottom: 20px;/*ST-901 2%*/ width: 100%; }
.selecting_endpaper .flip_view {bottom: 18px;}
.selecting_endpaper .endpaperGvFilter {top: 680px;}/*ST-1687*//*RP ST_1847 660px*/

.flip_view ul { margin: 0; padding: 0; line-height: 16px; }/*RP ST-1833*/
.flip_view ul li { display: inline-block; margin: 0 5px 0 0; /*border: 2px solid #000; RP ST-1833*/width:56px; /*width: 34%; ST-901*/ position: relative; }
.flip_view ul li:last-child { margin: 0; /*border: 1px solid #000; RP ST-1833*/ padding: 0; vertical-align: top; }
.flip_view ul li .view_sidebar { width: 35%; border-right: 1px solid #979797; padding: 5px 0 5px 0; float: left; background-color: #d8d8d8; }
.flip_view ul li .view_sidebar ul li { display: block; margin: 0 auto 5px; width: 10px; height: 10px; background-color: #417505; border: 0; }
.flip_view ul li .view_sidebar ul li:last-child { background-color: #57307a; margin: 0 auto 0; padding: 0; }
.flip_view ul li .flip_page_view { width: 65%; float: left; padding: 10px 2px; background-color: #ebebeb; }
.flip_view ul li .flip_page_view ul li { float: left; width: 50%; border: 1px solid #979797; height: 15px;  background-color: #FFF; margin: 0; }
.flip_view ul li .flip_page_view ul li:last-child { border-left: 0; padding: 0; }
.flip_view ul li .click_page_view { position: absolute; top: 0; right: 0; left: 0; bottom: 0; }
.flip_view ul li:last-child ul { padding: 5px 6px; }
.flip_view ul li.active ul{padding: 3px 6px;}
.flip_view ul li:last-child ul li { height: 11px; width: 11px; background-color: #d0021b; border: 0; float: left; margin: 0 3px 5px 0 }
.flip_view ul li:last-child ul li:nth-child(2) { background-color: #f5a623; }
.flip_view ul li:last-child ul li:nth-child(3) { background-color: #f8e71c; margin: 0 0 5px; }
.flip_view ul li:last-child ul li:nth-child(4) { background-color: #7ed321; margin: 0 3px 0 0; }
.flip_view ul li:last-child ul li:nth-child(5) { background-color: #bd10e0; margin: 0 3px 0 0; }
.flip_view ul li:last-child ul li:last-child { background-color: #2c67f6; border: 0; padding: 0; margin: 0; }
.page_view_text, .grid_filter_text { width: 34%; display: inline-block; text-align: center; }
.endpaperGvFilter .grid_filter_text { width: 32%; margin-top:4px; }
.page_view_text p, .grid_filter_text p { text-transform: uppercase; color: #000; margin: 0; font-size: 12px; font-family: 'gill_sanssemibold'; line-height: 15px; }
/****end 672 styles****/


.frederich_img .left_sec {text-align:left !important}
.content_wrap_in  {text-align:left !important}
.content_wrap .cke_editable  {text-align:left /*!important*/}

/****674 styles****/
.middle_endpaper { width: 100%; float: left; padding: 0 30px 0px 30px; }/*ST-1692 ST-1693*/
.endpaper_img_lib h3 { margin: 30px 0 5px; }
.endpaper_img_lib .img_pop_gallery_wrapper { margin: 0 -10px 0 0; height: 420px; }
ul.responsive1 { margin: 0 0 20px; }
.image_gallery .responsive1 .slick-arrow { height: 80px; width: 46px; background-size: 50% 80% !important; background-color: transparent !important; border: 0; position: absolute; top: 35%; outline: 0; }
.image_gallery .responsive1 .slick-arrow.slick-prev { background: url("/images/left-angel.png") top center no-repeat; left: -35px; }
.image_gallery .responsive1 .slick-arrow.slick-next { background: url("/images/right-angel.png") top center no-repeat; right: -45px; }
.image_gallery h2,.img_library h3 { padding-left: 15px; margin: 10px 0; color: #000; position: relative; font-size: 20px; line-height:23px; font-family:'GillSans-Light' }
.middle_endpaper .img_pop_gallery_wrapper .img_pop_gallery { width: 18% !important; height: 30% !important; overflow: hidden; } /*ST-1692 ST-1693*/
.middle_endpaper .gallery_btn_wrapper { text-align: center; }
.gallery_btn_wrapper a.select_img img { width: auto; }
.end_paper_lib h3 { text-align: center; font-size: 16px; color: #fff; border-bottom:solid 1px #fff; width:100px; margin:10px auto 70px; padding:0px }
.end_paper_lib .gray_line { height: 1px; background-color: #979797; width: 75%; margin: 0 0 0 15px; }
.end_paper_lib .by_color, .end_paper_lib .by_style, .end_paper_lib .pick_solid_color { width: 82%; margin: 12px auto 13px; position: relative; }
.end_paper_lib .by_color{  margin-bottom:29px}
.by_color .divEpColorName {position: absolute;text-align: right;width: 100%;top: 0px;right: 14px}/*ST-1701*/
.by_color .divEpColorName .spanStandColName { font-family:avenirltstd-heavy; font-size: 15px;text-transform: capitalize;}/*ST-1701*/
.end_paper_lib .by_color h4, .end_paper_lib .by_style h4, .end_paper_lib .pick_solid_color h4 { margin: 0 0 12px; color: #000; font-size: 14px; text-transform: uppercase; padding: 0; }
.end_paper_lib .by_style h4 { margin: 0px 0 5px; }
.end_paper_lib .by_color .color_wrapper, .end_paper_lib .pick_solid_color .color_wrapper { width: 100%; margin: 0 0 5px; text-align: left; line-height: 12px; }
.end_paper_lib .color_wrapper span { width: 20px; height: 20px; display: inline-block; vertical-align: top; }
.end_paper_lib .color_wrapper span img { vertical-align: top; }
.end_paper_lib .color_wrapper span.black { background-color: #000000; }
.end_paper_lib .color_wrapper span.gray { background-color: #646464; }
.end_paper_lib .color_wrapper span.white { background-color: #FFF; border: 1px solid #979797; }
.end_paper_lib .color_wrapper span.yellow { background-color: #feeb75; } /*--*/
.end_paper_lib .color_wrapper span.orange { background-color: #f98227; }
.end_paper_lib .color_wrapper span.mahroon { background-color: #c01f2a; }
.end_paper_lib .color_wrapper span.purple { background-color: #410166; }
/*.end_paper_lib .color_wrapper span.blue {  }*/
.end_paper_lib .color_wrapper span.green { background-color: #417505; }
.end_paper_lib .color_wrapper span.pink { background-color: #fc5da9; }
.end_paper_lib .color_wrapper span.brown { background-color: #573725; }
.end_paper_lib .color_wrapper span.purple-light { background-color: #c09fe2; } /*ST-1687*/
.end_paper_lib .color_wrapper span.blue-light { background-color: #3652c0; } /*ST-1687*/
.end_paper_lib .color_wrapper span.light-gray{ background-color: #c2c0c0; }/*ST-1340*/
.end_paper_lib .color_wrapper span{ border-radius:2px}/*ST-1547,1586 ,2019 01 22*/
    /*ST-1909*/
.end_paper_lib .color_wrapper span.spanCpDelete{height:16px; width:16px; display:none; top:-1px;left:15px;}
.end_paper_lib .color_wrapper .endPaper_Color:hover span.spanCpDelete {display: block;}
.end_paper_lib .color_wrapper .ieColorDelete .cursorDelete span {height: auto;width: auto;position: absolute;left: 4px;bottom: 0px;}
.end_paper_lib .collapse1 {position: absolute;top: -7px;right: 0;display: none}
.end_paper_lib .plus_collapse { display: none; }
.end_paper_lib .by_style ul { margin: 0; padding: 0 0 0 6px; }
.end_paper_lib .by_style ul li { display: block; }
.end_paper_lib .by_style ul li input { vertical-align: middle; margin: 0 5px 0 0; }
.end_paper_lib .by_style ul li label { line-height: 20px; font-size: 12px; font-weight: 400; padding-left: 22px; color: #000; cursor: pointer; margin-left: 0; font-family: 'Gill Sans MT'; }
.end_paper_lib .by_style ul li .chkbtn { margin: 0; vertical-align: middle; }
.end_paper_lib .by_style ul li { position: relative; margin: 0; text-align: left; }
.end_paper_lib .by_style ul li .chkbtn input { display: none; position: relative; z-index: -9999; margin: 0; }
.end_paper_lib .by_style ul li .chkbtn span { width: 25px; height: 25px; background: url("/images/check.png") no-repeat; top: -2px; position: absolute; left: 0; }
.end_paper_lib .by_style ul li .chkbtn input:checked + span { background: url("/images/check_mark.png") no-repeat; }
.end_paper_lib .by_style ul li .chkbtn p { margin: 0; }
.end_paper_lib .white_line { width: 100%; height: 1px; background-color: #FFF; margin: 0 0 9px; }
/****End 674 styles****/

/****660 styles 877****/
.add_page { margin: 20px; outline: 0; }
.add_page:focus, .add_page:active { outline: 0; }
.add_page_flip .modal-content { border: solid 2px #410166; border-radius: 0; box-shadow: none; background: #fafafa }/*BV, revert Back ST-1801 */
.add_page_flip .modal-content .modal-body { padding: 15px 70px; }
.radio_btn_wrap { padding: 4px 10px; color: #4a4a4a !important; font-size: 15px; font-family: 'GillSans'; font-weight:normal;}
.add_page_flip h1 {text-align: center;color: #000;font-size: 25px;font-weight:600;margin: 15px 0 10px;text-transform: uppercase;font-family: 'GillSans-SemiBold';}
.add_page_flip h2 { color: #63378a; margin: 0 0 10px; font-family: 'GillSans'; font-weight:300; font-size: 25px; text-transform: uppercase; text-align: left; }
.double_spreads_wrapper #ulDoubleSpread li.insertpagebtn{width: 97px !important;margin-left: 138px!important;margin-right: 7px;}/*RP ST-1657*/
.add_page_flip .single_spread_wrapper{text-align:start!important;}/*RP ST-1657*/
.add_page_flip .single_spread_wrapper .insertpagebtn{margin-right: 0px!important;}/*RP ST-1657*/
.divLayoutSelectionLabel{font-family: 'GillSans';font-size: 18px;height: 21px;}/*RP ST-1657*/
.double_spreads_wrapper ul { margin: 0;padding: 0;margin-left: 14px !important}/*RP ST-1657 */
.double_spreads_wrapper ul li { display: inline-block; list-style-type: none; margin: 0 79px 0 0; width: 28%; vertical-align: top; }
.double_spreads_wrapper ul li img { width: 100%; }
.double_spreads_wrapper ul li:nth-child(3n) { margin: 0; }
.radio_btn_wrap input[type='radio'] { -webkit-appearance: none; width: 20px; height: 20px; border-radius: 50%; outline: none; border: 1px solid #a4a4a4; margin-right: 10px; vertical-align: middle; margin-bottom: 5px; cursor: pointer; background-color: #FFF; }
.radio_btn_wrap input[type='radio']:before { content: ''; display: block; width: 14px; height: 14px; margin: 2px auto; border-radius: 50%; }
.radio_btn_wrap input[type='radio']:checked:before { background: #472850; }
.double_spreads_wrapper .radio { text-align: center; }
.double_spreads_wrapper .radio label { display: block; padding: 0; }
.double_spreads_wrapper .radio label input[type=radio] { margin: -2px 0 0 10px; }
.single_spread { margin: 15px 0 0; }
.single_spread h2 span { text-transform: lowercase; }
.single_spread_wrapper ul { margin: 15px 0 0 14px !important;padding: 0;}/*RP ST-1657 */
.single_spread_wrapper ul li { display: inline-block; list-style-type: none; margin: 0 25px 10px 0; width: 14.4%; position: relative; text-align: center; vertical-align: top; }
.single_spread_wrapper ul li:first-child { width: 26%; margin: 0 63px 10px 0; }
.add_page_flip .single_spread_wrapper ul li:first-child{display: inline-block; list-style-type: none; margin: 0 -1px 10px 0; width: 14.4%; position: relative; text-align: center; vertical-align: top;}
.single_spread_wrapper ul li:nth-child(5), .single_spread_wrapper ul li:nth-child(11) { margin: 0 0 10px 0; }
.add_page_flip .single_spread_wrapper ul li:nth-child(2){margin-right: 50px;}
.single_spread_wrapper ul li img { width: 100%; }
.single_spread_wrapper ul li h3 { position: absolute; left: 0; right: 0; top: 5px; color: #363836; font-size: 15px; margin: 0; font-family: 'PoorRichard-Regular'; }
.single_spread_wrapper ul li p { color: #000; font-size: 15px; margin: 10px 0 0; font-family: 'GillSans';}
.single_spread_wrapper ul li .checkbox, .single_spread_wrapper ul li .radio { margin: 5px 0; }
.modal-dialog.modal-lg { width: 1140px; }
.single_spread_wrapper ul li .checkbox, .single_spread_wrapper ul li .radio{margin: 9px 0 !important; padding-left: 85% !important;}
/****End 660 styles****/


/****676 styles****/
.pick_your_binding { background-color: #d8d8d8; /*padding: 0px 15px 10px*/ padding: 0px 2px 0px 22px; width:183px; /*width: 12.5%; ST-901*/ float: left; } /*PK ST-1556 , ST-1456, RP */
.pick_your_binding h2 { font-size: 13px; color: #FFF; margin: 25px 0 10px; text-align: center; text-transform: uppercase; font-family: 'gill_sanssemibold'; }
.pick_your_binding ul { padding: 0; margin: 0 0 -20px 0; line-height: 12px; }
.pick_your_binding ul li { margin: 0 0 15px; display: block; text-align: center; position: relative; }
.pick_your_binding ul li img { width: 100%; }
.pick_your_binding ul li span { display: block; color: #000000;padding-top:4px; font-size: 15px; font-family: 'GillSans-Light'; margin: 4px 0 0; line-height: 18px; } /*ST-885 PK 01/04/2018*/
/*ST-1456 PK 11/20/2018 , Change overflow-y to Auto*/
/*ST-1280,VR,2018 10 29, ST-1556, BV, 2019 02 13 */ 
#ulSideBar { overflow-y: auto; /*ST-787-Fix*/ 
max-height: calc(100% - 225px); margin: 0 0 15px; 
overflow-x: hidden;
height: auto !important; 
max-height:776px
}
ul#ulSideBar li { margin: 0 22px 5px 4px }/*ST-1556,BV 2019 02 20 , ST-1456, RP*/
ul#ulSideBar li:last-child { margin: 0 5px 0 0; }
/*ST-1615- BV, 2019 02 15, getting issue in image DnD*/
/*#ulSideBar li .clsActive img{ border:0px;outline: solid 1px rgb(87, 48, 122);}*/
/*ST-1558*/
.binding_views { text-align: center; margin: 312px 0 0; }
.binding_views ul li { position: relative; width: 40%; display: inline-block; margin: 0 5px 0 0; vertical-align: top; }
.binding_views ul li:last-child { margin: 0; }
.binding_views ul li a { /*position: absolute; top: 20px; right: 0; bottom: 0; left: 0;*/ text-decoration:none !important }/*ST-1676*/
.binding_views ul li .single_view_wrapper { border: 2px solid #000; margin-left:25px;} /*PK ST-1556*/
.binding_views ul li .single_view_wrapper .single_view_left { width: 40%; height: 31px; border-right: 1px solid #979797; background-color: #d8d8d8; float: left; }
.binding_views ul li .single_view_wrapper .single_view_right { width: 60%; height: 31px; background-color: #ebebeb; float: left; padding: 7px 2px; }
.binding_views ul li .single_view_wrapper .single_view_right ul li { width: 50%; float: left; border: 1px solid #979797; margin: 0; height: 15px; }
.binding_views ul li .single_view_wrapper .single_view_right ul li:last-child { border-left: 0; }
.binding_views ul li p { font-size: 10px; color: #000; margin: 5px 0 0; font-family: GillSans-SemiBold; text-transform: uppercase; } /*PK ST-1556*/
.binding_views ul li .grid_view_wrapper { border: 1px solid #000; padding: 5px; margin-left:11px} /*PK ST-1556*/
.binding_views ul li .grid_view_wrapper ul li { display: inline-block; width: 42%; margin: 0 5px 5px 0; float: left; }
.binding_views ul li .grid_view_wrapper ul li:nth-child(3) { margin: 0 5px 0 0; }
.binding_views ul li .grid_view_wrapper ul li:nth-child(2) { margin: 0 0 5px 0; }
.binding_views ul li .grid_view_wrapper ul li:last-child { margin: 0; }
.binding_views ul li .grid_view_wrapper ul li span { float: left; width: 50%; border: 1px solid #979797; height: 9px; margin: 0; background-color: #FFF; }
.binding_views ul li .grid_view_wrapper ul li span:last-child { border-left: 0; }
/****end 676 Styles****/

/****679 Styles****/
.main_heading h3 { font-size: 20px; font-family: 'GillSans-Light'; color: #000; margin: 15px 0 0; padding: 0 0 10px; border-bottom: 1px solid #979797; text-align: center; display: inline-block; width: 110px; text-transform: uppercase; }
.main_heading h2.library { padding-left: 0; font-family: 'GillSans-Light'; font-size: 20px; margin: 10px 0 15px; }
.upload_lib ul { margin: 0; padding: 0; }
.upload_lib_filter .gallery_btn_wrapper .cancel_img, .upload_lib_filter .gallery_btn_wrapper .select_img { padding: 3px 15px; }
.upload_lib_filter .gallery_btn_wrapper .done_img { padding: 3px 30px; transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s; }
.upload_lib_filter .gallery_btn_wrapper .select_img:hover, .upload_lib_filter .gallery_btn_wrapper .done_img:hover { background-color: #57307a; color: #FFF; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; }
.upload_lib_filter .gallery_btn_wrapper, .upload_lib_filter #proRangeSlider { padding: 0 0 15px; }
.upload_lib_filter #proRangeSlider { margin: 0; }
.msie .upload_lib_filter #proRangeSlider { margin: -30px 0 0; }
/****End 679 Styles****/

/****670 styles****/
.flip_first_heading { text-align: center;width:1238px } /* ST-1535 PK 12/14/2018 , Updated to Have Pages in Center of FB,BV,ST-1556 */
/*MB, 867*//*ST-883 - PK - 01/05/2018 Font Size from 25 to 20; MB, 868, SB 25 , BV,ST-1556*/
.flip_first_heading h1 { padding-bottom: 10px !important; margin: 19px 0 3px; padding: 0 0 10px; border-bottom: 1px solid #979797; display: inline-block; text-transform: uppercase; font-size: 25px;  font-family: 'GillSans-Light'; color: #000;font-weight: 300; margin-left:15px;}/*ST-1556.1*/
.flip_first_heading h2 { font-size: 30px; color: #000; margin: 0; font-family: 'gill_sanssemibold'; font-weight: 600; }

.gap-right-10 {    margin-right:10px !important;}
/*ST-1556.1,ST-1660.1*/
#pageHeadr {padding-bottom: 10px !important; margin-bottom:12px;}
#frame_body_outer {overflow-y: auto;overflow-x: auto;}
.underline-heading {margin-top: 5px;margin-bottom: 5px;border: 0;border-top: 1px solid #979797;width: 70px;}
.First_flip_wrapper { width: 1200px; height: 490px; position: relative; margin: 25px auto 35px; border: 1px solid #000; -webkit-margin-before: 30px; }
.First_flip_wrapper .flip_custom_wrapper { width: 100%; height: 100%; position: relative; }
.flip_custom_left, .flip_custom_right { width: 50%; float: left; height: 93%; background-color: #fff; border-width: 0px 1px 0px 0px; padding: 40px 50px 25px 40px; }
.flip_custom_left .tree_img_wrapper { width: 100%; height: 400px; }
.flip_custom_left .tree_img_wrapper img { width: 100%; }
.flip_custom_left hr { margin: 30px 0 10px; border-top: 1px solid #4c4c4c; }
.flip_custom_left h2 { margin: 0; font-size: 20px; color: #9B9B9B; font-family: 'gill_sanssemibold'; text-align: center; }
.flip_custom_right { padding: 25px; }
.flip_custom_right .page_title_wrapper { width: 100%; border: 2px solid #422164; padding: 0px 20px 0px 20px; position: relative; z-index: 100; text-align: center; height: 440px; }
.flip_custom_right .page_title_wrapper .tree_opacity_img { position: absolute; right: 15px; top: 0; background: url("/images/tree_pic_sm.png") top center no-repeat; width: 40%; height: 190px; background-size: 100%; z-index: 1; opacity: 0.19; }
.flip_custom_right .page_title_wrapper h3 { margin: 0 75px; padding: 0 0 5px; border-bottom: 1px solid #000; font-size: 25px; color: #ABABAB; width: 355px; font-family: 'GillSansB'; }
.flip_custom_right .page_title_wrapper h4 { margin: 0 0 35px; font-size: 20px; color: #ABABAB; text-align: center; font-family: 'Sylfaen' }
.flip_custom_right .page_img_left { width: 30%; float: left; margin: 0 20px 25px 0; height: 152px; }
.flip_custom_right .page_img_left img { width: 100%; height: 100%; }
.flip_custom_right .page_text_right { width: 65%; float: left; border: dashed 1px #000; border-image: url(/images/LdVc.png) 1 round; border-image-source: url("/images/border_img.png"); padding: 5px 10px; }
.flip_custom_right .page_text_right p { font-size: 12px; color: #5C5C5C; font-size: 12px; font-family: 'PoorRichard-Regular'; margin: 0 0 4px; }
.flip_custom_right .page_text_right p.noM { margin: 0; }
.flip_custom_right .tree_images ul { margin: 0; padding: 0; }
.flip_custom_right .tree_images ul li { width: 30%; margin: 0 19px 0 0; display: inline-block; list-style-type: none; height: 100px; }
.flip_custom_right .tree_images ul li:last-child { margin: 0; }
.flip_custom_right .tree_images ul li img { width: 100%; height: 100% }
.First_flip_wrapper .flip_custom_wrapper .stitches_black { position: absolute; top: 0; left: 49.6%; bottom: 0; height: 100%; }
.First_flip_wrapper .flip_custom_wrapper .stitch { background: url(/images/stitches_gray.png) top center repeat-y; height: 100%; width: 10px; margin: 0 auto 10px; }
/****End 670 styles****/

/****640 Styles****/
.creat_flip_heading h1 { font-size: 28px; color: #57307a; margin: 57px 0 20px; font-family: 'GillSans-Light'; text-transform: uppercase; font-weight:300; } /*PK ST-1386 11/15/2018 , Update Top Margin*/
.flip_sample_wrapper ul { margin: 0; padding: 0; }
.flip_sample_wrapper ul li { margin: 0 30px 0 0; list-style-type: none; display: inline-block; vertical-align: top; text-align: center; position: relative; }
.flip_sample_wrapper ul li.auto_save { margin: 0 0 0 30px; position: relative; }
.flip_sample_wrapper ul li.auto_save:before { content: ""; width: 1px; height: 209px; background-color: #979797; position: absolute; left: -30px; }
.flip_sample_wrapper ul li .flip_sample { width: 200px; height: 200px; position: relative; transition: all ease-out 0.3s; -webkit-transition: all ease-out 0.3s; -moz-transition: all ease-out 0.3s; }
.flip_sample_wrapper ul li .flip_sample .hover_effect { background-color: rgba(255, 255, 255, 0.4); position: absolute; top: 0; right: 0; bottom: 0; left: 0; opacity: 0; transition: all ease-out 0.3s; -webkit-transition: all ease-out 0.3s; -moz-transition: all ease-out 0.3s; }
.flip_sample_wrapper ul li .flip_sample img { width: 100%;height:100%; }
.flip_sample_wrapper ul li .flip_sample:hover .hover_effect { opacity: 1; transition: all ease-in 0.3s; -webkit-transition: all ease-in 0.3s; -moz-transition: all ease-in 0.3s; }
.flip_sample_wrapper ul li .flip_sample:hover { border: 2px solid #4990e2; transition: all ease-in 0.3s; -webkit-transition: all ease-in 0.3s; -moz-transition: all ease-in 0.3s; }
/*ST-1899*/
.flip_sample_wrapper ul li p.flip_subname {  display: block; margin: 8px 0 0; text-align:left; font-family:'Helvetica-Bold',Sans-Serif; font-size:13px;color: #401660;  line-height: 16px;letter-spacing: normal;}
.flip_sample_wrapper ul li p.flip_subname a img {width: 13px; margin-left: 6px; margin-top: -4px; }
.flip_sample_wrapper ul li span { font-size: 12px; display: block;  margin: 0; text-align:left;font-family:'Helvetica-Bold',Sans-Serif; font-size:10px;color: #9b9b9b;  line-height: 12px;letter-spacing: normal;}
.flip_sample_wrapper ul li .flip_sample .close_icon { position: absolute; right: 0; top: -2px; }
.flip_sample_wrapper ul li .flip_sample:hover .close_icon img, .flip_sample_wrapper ul li .flip_sample .close_icon img { border: 0; }
/****End 640 Styles****/

/****684 Syles****/
.create_new_wrapper { width: 100%; background-color: #FFF; }
.create_new_wrapper h1 { font-size: 30px; /*MB, 889*/ color: #000; margin: 20px 0 0; text-align: center; text-transform: uppercase; font-family: 'gill_sanssemibold'; }
.share_wrapper h2 { color: #4a4a4a; font-size: 25px;/*MB, 889*/ margin: 30px 0 20px; text-transform: uppercase; }
.control { display: inline-block; position: relative; padding-left: 50px; margin-bottom: 15px; cursor: pointer; font-size: 15px; color: #363836; font-family: 'GillSans-Light'; text-transform: uppercase; }
.control input { position: absolute; z-index: -1; opacity: 0; }
.control__indicator { position: absolute; top: 0px; left: 5px; height: 15px; width: 15px; background: #FFF; border: 1px solid #949494; }/*ST-1748*/
.control input:disabled ~ .control__indicator { background: #e6e6e6; opacity: 0.6; pointer-events: none; }
.control__indicator:after { content: ''; position: absolute; display: none; }
.control input:checked ~ .control__indicator:after { display: block; }
.control--checkbox .control__indicator:after {content: '\2713';font-size: 29px;position: absolute;top: -4px;left: -1px;color: #410166;    transform: rotate(9deg);}/* ST-1748 .control--checkbox .control__indicator:after { left: 5px; top: 0px; width: 5px; height: 10px; border: solid #000; border-width: 0 2px 2px 0; transform: rotate(45deg); }*/
.publish_flip { float: right; display: inline-block; border: 1px solid #57307a; color: #6b448d; background-color: #FFF; font-size: 14px; font-family: 'gill_sanssemibold'; margin: 0 70px 0 0; border-radius: 20px; padding: 5px 20px; text-decoration: none; transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s; text-transform: uppercase; }
.publish_flip:hover { color: #FFF; background-color: #6B448D; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; text-decoration: none; }
.check_wrapper { width: 100%; display: block; }
.share_wrapper p { color: #363836; font-size: 15px; display: inline-block; font-family: 'GillSansCE-Roman'; margin: 5px 0; }
a.download_flip { margin: 0 0px 0 0; }/*ST-1748*/
.share_wrapper ul { margin: 0; padding: 0; }
.share_wrapper ul li { margin: 0 0 15px; display: block; }
.share_wrapper ul li:last-child { margin: 0; }
.input_wrapper { width: 320px; display: inline-block; }
.share_wrapper .input_wrapper input { border: 1px solid #979797; }
.share_wrapper .input_wrapper input:focus { border-color: #979797; box-shadow: none; }
.share_wrapper .input_wrapper span { color: #AAA; font-size: 14px; font-family: 'GillSans-Light'; }
.share_wrapper .input_wrapper input::-webkit-input-placeholder {color: #9A9A9A;}
.share_wrapper .input_wrapper input::-moz-placeholder {color: #9A9A9A;}
.share_wrapper .input_wrapper input:-ms-input-placeholder {color: #9A9A9A;}
.share_wrapper .input_wrapper input:-moz-placeholder {color: #9A9A9A;}
a.send_flip { padding: 5px 45px; margin: 0 0px 0 0; }/*ST-1748*/
a.order_flip { border: 1px solid #57307a; color: #6b448d; background-color: #FFF; font-size: 13px; font-family: 'gill_sanssemibold'; margin: 0 10px 0 0; border-radius: 20px; padding: 5px 20px; text-decoration: none; transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s; text-transform: uppercase; display: inline-block; }/*ST-1748*/
a.order_flip:hover { color: #FFF; background-color: #6B448D; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; text-decoration: none; }
.book_block { position: absolute; left: 0; top: 10px; margin-left: -14px; }/* right: 0; width: 400px; margin: 0 auto; RP ST-1748*/
.book_block img { width: 100%; }
/*ST-1339 PK 09/11/2018 Update CSS For Share Flipbook Page*/
.share_wrapper h2.hardcopies { margin: 80px auto 20px auto;width:50% }
.share_wrapper a.order_flip {margin: 0 auto;}
h2.share_social { margin: 0 0 20px; }
.social_icons ul { margin: 0; padding: 0; }
.social_icons ul li { margin: 0 35px 0 0; display: inline; list-style-type: none; }
.social_icons ul li .control { padding: 0; }
.social_icons ul li .control .control__indicator { position: relative; top: 0; left: 0; margin: 10px auto 0; }
.header_icons ul { margin: 0; padding: 0; }
.header_icons ul li a { margin: 0; padding: 0; color: black; }
.header_icons ul li a img { margin-top: 0; padding: 0; width: 4%; }
.header_icons ul li { margin: 0 35px 0 0; display: inline-block; list-style-type: none; }
.header_icons ul li .control { padding: 0; }
.header_icons ul li .control .control__indicator { position: relative; top: 0; left: 0; margin: 10px auto 0; }
/*ST-1339 PK 09/11/2018 Update CSS For Share Flipbook Page*/
a.share_btn { border: 1px solid #57307a; color: #6b448d; background-color: #FFF; font-size: 13px; font-family: 'gill_sanssemibold'; border-radius: 20px; padding: 5px 0; text-decoration: none; transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s; text-transform: uppercase; display: block; width: 125px; margin-left: 16%;margin-bottom:1%; text-align: center; }
a.share_btn:hover { color: #FFF; background-color: #6B448D; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; text-decoration: none; }
/****End 684 Syles****/

/****653 styles****/
.image_toolbar { background-color: #f7f7f7; width: 100%; padding: 10px 20px ;min-width: 1300px; /*ST-901*/}
.image_toolbar ul.image_tools { margin: 0 20px; padding: 0 7px 0 0px; display: inline-block; border-right: 1px solid #949798; line-height: 25px; }
.image_toolbar ul.image_tools li { display: inline-block; margin: 0 15px 0 0; }
.image_toolbar ul.image_tools li a { color: #131412; font-size: 14px; text-decoration: none; display: inline-block; vertical-align: middle; }
.image_toolbar h1 { margin: 0; color: #363836; font-size: 20px; font-family: 'GillSans-Light'; display: inline-block; line-height: 30px;}
/*Update For ST- 848 PK*/
.image_toolbar ul.goto_img, .image_toolbar ul.blank_img { padding: 0; margin: 0 15px 0 0px; display: inline-block; border-right: 1px solid #949798; }
.image_toolbar ul.blank_img { border: 0; }
.image_toolbar ul.blank_img img {background: #fff !important;} /*ST-1502 , 12/10/2018 PK */
.image_toolbar ul.goto_img li, .image_toolbar ul.blank_img li { display: block; margin: 0 15px 0 0; }
.image_toolbar ul.goto_img li a, .image_toolbar ul.blank_img li a { color: #363836; font-size: 20px; text-decoration: none; display: inline-block; vertical-align: middle; font-family: 'GillSans-Light'; margin: 0 0 0 1px; }
.bull_heading { width: 95%; margin: 0 auto 30px; }
.bull_heading h2 span img { width: 10px; position: static; }
.image_gallery .responsive2 .slick-arrow { height: 75px; width: 30px; background-size: 60% 80% !important; background-color: transparent !important; border: 0; position: absolute; top: -5px; outline: 0; }
.bull_wrapper ul li { margin: 0 5px; width: 162px; }
.bull_wrapper .gallery_btn { right: -1px; top: 0px; } /* ST-1532 PK 12/18/2018*/
.bull_wrapper .gallery_btn .cursorDelete {margin-left:-1px;}
.bull_wrapper .responsive2 li img {max-width: 100% !important;}/*RP ST-1484 height: 84px !important;*/
.bull_wrapper .responsive2 li .gallery_btn img { border: 0; height:15px !important; width: 15px !important;}/*RP ST-1627*/
/*ST-682 16x16 , 1554 PK,01/21/2018*/
.image_gallery .responsive2 .slick-arrow.slick-prev { background: url("/images/left-angel.png") top center no-repeat; left: -45px; }
.image_gallery .responsive2 .slick-arrow.slick-next { background: url("/images/right-angel.png") top center no-repeat; right: -45px; }
/****End 653 styles****/

/****681 styles****/
.drag_drop_img.list-grid { height: auto; width: 100%; }
.drag_drop_img h2 { padding: 0 0 10px; border-bottom: 1px solid #979797; }
/* ST-888 PK 04/01/2017 */
.pick_your_binding.frame_editor h3 { color: #000; font-size: 15px; font-family: 'GillSans-Light'; margin: 0 0 20px; text-align: center; border-top: 0px solid #979797; padding-bottom: 8px; padding-top:10px;}/*ST-1766*/
.drag_drop_img .gallery_btn { position: absolute; right: 1px; top: 1px;margin:-1px; }
.rest_library .gallery_btn { position: absolute; right: 4px; top: 1px;margin:-1px; } /* PK 1532 12/18/2018*/
.drag_drop_img .gallery_btn span { background-color: rgba(255, 255, 255, 0.7); width: 20px; display: inline-block; cursor: pointer; margin: 0 -7px 0 0; text-align: center; }
.rest_library .gallery_btn span {padding-top: 0px;background-color: rgba(255, 255, 255, 0.7);width: 15px;display: inline-block;cursor: pointer;margin: 0 -3px 0 0 !important;text-align: center;}
.drag_drop_img .gallery_btn span:last-child { margin: 0; }
.drag_drop_img .gallery_btn span img { width: 15px; margin: auto; display: block; }
.frame_editor .rest_library { padding: 0 5px 0 10px; margin: 0 5px 20px 0; height: 74%; overflow-y: scroll; }/*ST-1427 ,74%*/
.frame_editor .rest_library ul { border-right: 1px solid #979797; }
.frame_editor .rest_library ul li { display: block; margin: 0 10px 14px 0; }
.frame_editor .rest_library ul li:last-child { margin: 0 10px 0 0; }
.drag_drop_img .binding_views { margin: 0; }
.drag_drop_img .binding_views .view_imges { padding: 0px; background-color: #FFF; border: 1px solid #57307a; color: #6b448d; border-radius: 20px; display: inline-block; text-decoration: none; text-transform: uppercase; font-size: 13px; font-family: 'gill_sanssemibold'; margin: 0 0 20px; transition: all ease-in-out 0.5s; -webkit-transition: all ease-in-out 0.5s; -moz-transition: all ease-in-out 0.5s;width: 170px;height: 30px;line-height: 30px; }/*RP ST-1881*/
.drag_drop_img .binding_views .view_imges:hover { color: #777; }/*RP ST-1881*/
.drag_drop_img .binding_views ul { text-align: center; }
.drag_drop_img .binding_views ul li { display: inline-block; margin: 0 5px 0 0; width: 37%; }
.drag_drop_img .binding_views ul li:last-child { margin: 0; vertical-align: top; }
.drag_drop_img .binding_views ul li .lib_view_wrapper { border: 1px solid #000; width: 90%; position: relative; background-color: #FFF; padding: 2px 2px 3px 4px; }
.drag_drop_img .binding_views ul li .lib_view_wrapper span { float: left; margin: 0 3px 4px 0; line-height: 14px; }
.drag_drop_img .binding_views ul li .lib_view_wrapper span:nth-child(3) { margin: 0 0 4px 0; }
.drag_drop_img .binding_views ul li .lib_view_wrapper span:nth-child(4), .drag_drop_img .binding_views ul li .lib_view_wrapper span:nth-child(5) { margin: 0 3px 0 0; }
.drag_drop_img .binding_views ul li .lib_view_wrapper span:last-child { margin: 0; }
.drag_drop_img .binding_views ul li .drag_drop_wrapper { border: 2px solid #000; }
.drag_drop_wrapper .drag_sidebar { width: 35%; border-right: 1px solid #979797; padding: 5px 0 6px 0; float: left; background-color: #d8d8d8; }
.drag_drop_wrapper .drag_sidebar ul { margin: 0; padding: 0; }
.drag_drop_wrapper .drag_sidebar ul li { display: block; margin: 0 auto 5px; width: 7px; height: 7px; background-color: #8bb7ea; border: 0; }
.drag_drop_wrapper .drag_sidebar ul li:last-child { margin: 0 auto 0; padding: 0; }
.drag_drop_wrapper .drag_middlebar { width: 65%; float: left; padding: 15px 7px; background-color: #FFF; }
.drag_drop_wrapper .drag_middlebar ul { margin: 0; padding: 0; }
.drag_drop_wrapper .drag_middlebar ul li { float: left; border: 1px solid #979797; height: 12px; width: 14px; background-color: #FFF; margin: 0; }
.drag_drop_img.list-grid .binding_views ul li .lib_view_wrapper { padding: 2px 2px 3px 4px; }
.drag_drop_img.list-grid .binding_views ul li .lib_view_wrapper span { margin: 0 1px 1px 0; }
.drag_drop_img.list-grid .binding_views ul li .lib_view_wrapper span:nth-child(4), .drag_drop_img.list-grid .binding_views ul li .lib_view_wrapper span:nth-child(5) { margin: 0 1px 1px 0; }
.drag_drop_img.list-grid .binding_views ul li p { width: 90%; margin: 5px 0 0; line-height: 12px; font-size: 10px; letter-spacing: -0.6px; }
.drag_drop_img.list-grid .drag_drop_wrapper .drag_middlebar { padding: 13px 3px; }
.drag_drop_img.list-grid .drag_drop_wrapper .drag_sidebar { padding: 6px 0 5px; }
.drag_drop_img.list-grid .drag_drop_wrapper .drag_sidebar ul li, .drag_drop_img.list-grid .drag_drop_wrapper .drag_sidebar ul li:last-child { margin: 0 auto 2px; }
.drag_drop_img.list-grid .drag_drop_img .binding_views ul li { width: inherit; }
.drag_drop_img.list-grid .binding_views ul li .lib_view_wrapper, .drag_drop_img.list-grid .binding_views ul li .drag_drop_wrapper { border: 1px solid #000; }
.drag_drop_img.list-grid .binding_views ul li.active .lib_view_wrapper, .drag_drop_img.list-grid .binding_views ul li.active .drag_drop_wrapper { border: 2px solid #000; }
/****End 681 styles****/

/****686 Styles****/
.link_flip { margin: 20px; outline: 0; }
.link_flip:focus, .link_flip:active { outline: 0; }
.link_my_flip .modal-dialog { width: 800px; }
.link_my_flip .modal-content { border: 2px solid #410166; border-radius: 0; text-align: center; box-shadow: none; }
/* ST - 889 PK 01/04/2017 */
.cke-PLI68 {width: 438px;padding-right:22px;padding-left:22px;line-height: 21px;}
.cke-PLI68 p{font-family: MillerText-Roman;font-size: 19px;line-height: 21px;}
.cke-PLI68 .cke_editable {min-height: 66px;height: 66px;overflow:hidden;line-height: 21px;}
/*ST-1281 AC*/
.dropdown_option { width: 276px; margin: 0 auto 20px; }
.dropdown_option .select_option { border: 1px solid #979797; text-align: left; position: relative; z-index: 1; }
.dropdown_option .select_option a { display: block; padding: 3px 30px 3px 10px; line-height: 24px; overflow: hidden; border: 0; margin: 0 auto; color: #000; text-decoration: none; }
.dropdown_option .select_option a span.hida, span.multiSel span { cursor: pointer; display: inline-block; padding: 0 3px 2px 0; vertical-align: middle; font-size: 20px; font-family: 'GillSans-Light'; }
.dropdown_option .select_option a img { position: absolute; top: 8px; right: 8px; }
.dropdown_option .multi_wrapper { position: relative; }
.dropdown_option .multi_wrapper ul { background-color: #FFF; border: 0; color: #000; display: none; left: 0px; padding: 5px 0 2px 10px; position: absolute; top: 2px; width: 230px; list-style: none; text-align: left; margin: -3px 0 0; border: 1px solid #979797; height: 137px; overflow-y: auto; z-index: 10; }
.dropdown_option span.value { display: none; }
.dropdown_option .multi_wrapper ul li a { padding: 5px; display: block; }
.dropdown_option .multi_wrapper ul li input { vertical-align: middle; margin: 0 5px 0 0; }
.dropdown_option .multi_wrapper ul li a:hover { background-color: #fff; }
.dropdown_option .multi_wrapper ul li label { line-height: 20px; font-size: 18px; font-weight: 400; padding-left: 25px; color: #808080; cursor: pointer; margin-left: 0; }
.dropdown_option .multi_wrapper ul li .chkbtn { margin: 0; vertical-align: middle; }
.dropdown_option .multi_wrapper ul li { position: relative; margin: 0 0 10px; }
.dropdown_option .multi_wrapper ul li .chkbtn input { display: none; position: relative; z-index: -9999; margin: 0; }
.dropdown_option .multi_wrapper ul li .chkbtn span { width: 25px; height: 25px; background: url("/images/check.png") no-repeat; top: -2px; position: absolute; left: 0; }
.dropdown_option .multi_wrapper ul li .chkbtn input:checked + span { background: url("/images/check_mark.png") no-repeat; }
.dropdown_option .multi_wrapper ul li .chkbtn p { margin: 0; }
.publish_flip_link { border: 1px solid #57307a; color: #6b448d; border-radius: 20px; text-transform: uppercase; padding: 5px 60px; text-decoration: none; display: inline-block; font-size: 14px; margin: 0 0 5px; transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s; text-decoration: none; }
.publish_flip_link:hover { background-color: #57307a; color: #FFF; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; text-decoration: none; }
.success_img { position: absolute; bottom: 15px; right: 15px; }
/****End 686 Styles****/

/****699 Styles****/
/*ST-1676*/
.frame_editor { /*padding: 0px 15px 10px 28px;*/ height: 91vh; position: relative; }
.zindex1{z-index:1}
.zIndex-non {z-index:0;}
.border_toolbar { background: #f2f2f2; padding: 4px 30px; height: 49.9px; line-height: 38px; min-width:1010px;/*ST-901*/ }
/* ST-1308  PK 09/13/2018 , Remove Width 590px */
.border_toolbar ul { margin: 0; padding: 0; display: inline-block;}
.border_toolbar ul li:first-child { padding: 0 15px 0 0; }
.border_toolbar ul li { display: inline-block; margin: 0; border-right: 1px solid #979797; padding: 0 15px 0 9px; vertical-align: middle; }/*ST-1835 - padding: 0 8px 0 6px;*/
.border_toolbar ul li a { font-size: 17px; color: #363836; font-family: 'GillSans-Light'; text-decoration: none; }
.border_toolbar ul li a img { width: 32px; vertical-align: top; margin-top: 3px; margin-right: 5px;margin-left: 0px; }
.border_toolbar ul li:first-child h2 { font-size: 20px; color: #3a3b39; font-family: 'GillSans-Light'; margin-left:10px; display: inline-block; }
.singlePg.border_toolbar ul li:first-child h2 {margin-left:25px;}
.border_toolbar ul li h2 { font-size: 17px; color: #363836; font-family: 'GillSans-Light'; margin: 0; display: inline-block; }
.border_toolbar .colorPicker { height: 30px; width: 60px; background-size: 26px 30px;margin-right: 10px;}
/* ST-1308  PK 09/13/2018 , Added Margin Left */
/*ST-1835*/
.border_thickness { margin-left:10px; }
.singlePg.border_toolbar li {padding: 0 20px 0 20px;}
.singlePg.border_toolbar #rbtnBorderOnly {margin-left:20px;}

.bordertool_radio {display: inline-block;}/*ST-1835*/
li.bordertool_radio {border-right:0px !important;}
#rangeBorderSlider.ui-widget.ui-widget-content { border: 0 !important; height: 2px; background: #9b9b9b !important; position: relative; margin: 0 10px 0 0; display: inline-block; width: 120px; vertical-align: middle; }
.value_print { width: 120px; display: inline-block; vertical-align: middle; margin: 0 0 0; color: #363836; font-size: 17px; font-family: 'GillSans-Light'; }
#rangeBorderSlider .ui-slider-handle { background: #000; border-radius: 50%; top: -7px; outline: none; width: 15px; height: 15px; box-shadow: 1px 2px 2px 0 rgba(0, 0, 0, 0.5);}
#rangeBorderSlider .ui-slider-handle:focus { border-color: #000; }
#rangeBorderSlider .ui-slider-handle.ui-state-active { border: 0; }
/* ST-888 PK 04/01/2017 */
.frame_editor h2 { margin: 25px 17px 10px -6px;   font-family: 'gill_sanssemibold';font-size: 15px;font-weight: 600;font-style: normal;font-stretch: normal;line-height: normal;letter-spacing: normal;text-align: center;color: #ffffff;} /*ST-1456, RP*/

.frame_editor ul li { margin: 0 0 10px; position: relative; }
.frame_editor ul li a.cross_close {position: absolute;  top: 20px; right: 5px;}/*ST-1485*/
.frame_editor ul li a.list_icon { position: absolute; bottom: 20px; right: 10px; }/*ST-1485*/
.frame_editor ul li a.list_icon i { color: #4e4e4e!important; }/*RP ST-1627*/
.frame_editor .binding_views { margin: 0;margin-left: -9px; position: absolute; left: 0; right: 0; width: 100%; z-index: 11; }/*ST-1556*/
.frame_editor .binding_views ul li { margin: 4px; /* ST-883 , PK , 01/05/2017 ; To Make Sure Zeplin */ }
.drag_drop_img .binding_views ul li {margin: 0;}
.frame_editor ul li img { width: 100%; }
.frame_editor ul li.adjust_width img { width: auto; }
.frame_editor ul li p.up_heading { font-size: 17px; text-align: center; margin: 0; font-family: 'GillSansCE-Roman'; color: #FFF; line-height: 18px; }
.frame_editor ul li span { margin: 0; }
.frame_body { width: 81.5%; float: left; padding: -3px 20px;}
.frame_body .arrow_wrapper { width: 1280px; position: relative;}/*ST-1556,BV*/
.frame_body .arrow_wrapper .arrows { position: absolute; top: 45%; width: 50px; height: 60px; z-index: 10; }
/*VR,ST-1767*/
.frame_body .arrow_wrapper .arrow_left { background: url("/images/left-angel.png") center center no-repeat; left: 5px;  background-size: contain; }
.frame_body .arrow_wrapper .arrow_right { background: url("/images/right-angel.png") center center no-repeat;right: -9px !important;background-size: contain;}
.frame_body .flip_custom_left { padding: 0px 0px 0px 25px; text-align: center; }
.frame_body .flip_custom_left .tree_img_wrapper img { margin: 25px 0; }
.frame_body .flip_custom_left h2 { font-size: 18px; border-bottom: 1px solid #979797; width: 300px; display: inline-block; margin: 0 0 0; }
.frame_body .flip_custom_left span { display: block; color: #9B9B9B; }
.frame_body .flip_custom_right .page_title_wrapper h3 { font-size: 24px; }
.frame_body .flip_custom_right .page_title_wrapper { border: 3px solid #000; }
.frame_body .flip_custom_right .page_title_wrapper .tree_opacity_img { background: url("/images/shingle_new.png") top center no-repeat; opacity: 0.1; }
.frame_editor .frame_btn_copy { bottom: 0; }
.frame_editor .frame_btn span { position: absolute; left: 10px; top: 3px; }
.full_frame { position: absolute; bottom: -32px; right: 0; }
.arrow_wrapper .trade_logo_copy { margin: 0 55px 0; }
/****End 699 Styles****/


 
/*ST-1545*/
/*.divSelectedImgBorder,*/

/*ST-1369 PK 11/14/2018 */
.borderbottomnone { border-bottom: none !important; }
.maxheight60 { min-height: 60px; }
.maxheight100 { min-height: 100px; }
.height30 { min-height: 30px !important; }
.height50 { min-height: 50px !important; }
.height60 { min-height: 60px !important; }

.height100 { min-height: 97px !important; }
.height210 { min-height: 210px !important; }
.flpbook_achievemnet h3 { margin: 0 0px 3px !important; padding: 0 4px 3px !important; position: relative !important; width: 100% !important; text-align: left !important; font-weight: normal !important; font-family: inherit !important; }/*ST-1124*/
.bordebottomblack { border-bottom: 1px solid #000 !important; position:relative }
.flpbook_achievemnet h3 p { font-weight: normal !important; margin: 0px !important; font-family: inherit !important; }

/****709 Styles****/
.dash_board { width: 94%; float: left; padding: 15px; height: 500px; background-color: #FFF; }
/* ST-1321 , PK , 09/19/2018 , Set AlignMent for 1st and Last Child in Grid */
.grid_page_manage .mB:nth-child(1) p, .grid_page_manage .mB:last-child p{ text-align:center !important;padding-left: 92px;}/*RP ST-1848*/
.grid_page_manage .img_wrapper { width: 100%; position: relative; overflow: hidden; min-height:134px;} /*ST-1438 PK 11/21/2018 Added Min-Height*/
.grid_page_manage img { width: 100%; }
.grid_page_manage .mB { margin-bottom: 40px; }
.grid_page_manage p { margin: 5px 0 0; text-align: right; font-size: 15px; color: #000;font-family: 'GillSans-Light' !important; font-weight: 300; }
.grid_page_manage p.text-center { text-align: center; }
.grid_page_manage .grid_overlay { position: absolute; bottom: 0; top: 0; right: 0; left: 0; width: 100%; height: 100%; text-align: center; background-color: rgba(255, 255, 255, 0); transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -ms-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; transform: translateX(-120px); -webkit-transform: translateX(-120px); -ms-transform: translateX(-120px); -moz-transform: translateX(-120px); }
.grid_page_manage .img_wrapper:hover .grid_overlay { background-color: rgba(255, 255, 255, 0.8); top: 0; bottom: 0; transition: all ease-out 0.2s; -webkit-transition: all ease-out 0.2s; -ms-transition: all ease-out 0.2s; -moz-transition: all ease-out 0.2s; transform: translateX(0); -webkit-transform: translateX(0); -ms-transform: translateX(0); -moz-transform: translateX(0); }
.grid_page_manage .img_wrapper a.edit_overlay_text { color: #410260; text-transform: uppercase; text-decoration: none; position: absolute; right: 0; top: 0; bottom: 0; height: 140px; width: 100%; text-align: center; padding: 60px 0; opacity: 0; transition: all ease-in-out 0.5s; -webkit-transition: all ease-in-out 0.5s; -ms-transition: all ease-in-out 0.5s; -moz-transition: all ease-in-out 0.5s; transform: translateX(-120px); -webkit-transform: translateX(-120px); -ms-transform: translateX(-120px); -moz-transform: translateX(-120px); font-family: 'GillSans-Light'; font-size: 20px; font-weight: 300; }
.grid_page_manage .img_wrapper:hover a.edit_overlay_text { left: 0; right: 0; opacity: 1; transition: all ease-in-out 0.5s; -webkit-transition: all ease-in-out 0.5s; -ms-transition: all ease-in-out 0.5s; -moz-transition: all ease-in-out 0.5s; transform: translateX(0px); -webkit-transform: translateX(0px); -ms-transform: translateX(0px); -moz-transform: translateX(0px); border: solid 1px #4990e2; }
.flip_first_heading p { text-align: left; padding: 0 0 0 15px; color: #000; font-size: 20px; margin: 0 0 15px; font-family: 'GillSans-Light'; }
.grid_page_manage .flip_first_heading h1 { margin: 5px 0 5px; }
.grid_page_manage .flip_first_heading h2 { margin: 0 0 20px; }
/*PK ST-1556*/
.grid_view_new { margin: 0; position: absolute; bottom: 3.5%; left: 75px; z-index: 1000; } /*ST-1438 PK 11/21/2018*/
/*ST-1556,BV*/
.binding_views.grid_view_new ul li .single_view_wrapper { border: 1px solid #000; height:40px;margin-bottom: -2px; }
.binding_views.grid_view_new ul li .grid_view_wrapper { border: 3px solid #000; padding: 3px; margin-left: -16px; }/*ST-1438 PK 11/21/2018*/
.grid_view_new ul { margin: 0; padding: 0; line-height: 12px; }
.trade_logo {position: absolute;left: 0;right: 0;bottom: 18px;text-align: center;z-index: 10;} /*SMJ ST-947, ST-1675,BV,2019 03 12*//*RP ST-1730*/
/*ST-1319 PK , 09/19/2018 , Update BetaTesting CSS */
.beta_testing { right: 0;bottom: 3.5%;position:absolute !important;}
.EndPaperControl-betaIcon.beta_testing {display:none; } /*When endpaper control are shown ST-1388,BV*/
.manage_img img { width: auto; }
.grid_cross { width: 20px; height: 20px; background-color: #FFF; text-align: center; position: absolute; right: 1px; top: 1px; padding: 5px; }
.grid_cross img { width: auto; display: block; }
/****End 709 Styles****/

.page_manager_f13 { width: 83.5%; }
.flip_sample.FBselection .hover_effect { opacity : 1 !important; }
.flip_sample.FBselection { border: 1px solid #4a90e2 !important; }
.ui-dialog .ui-dialog-titlebar-close { background: none !important; border: none !important }
.frame_body .arrow_wrapper { width: 100%; }
.binding-image { }
.binding-image .container { width: 100%; }
.binding-image .image-category { float: left; margin-right: 30px; margin-bottom: 20px; }
/* ST -887 PK 04/01/2018 */
.binding-image .image-category h4 { font-size: 20px;font-family: gillsans-light;color: #000000; margin: -22px 0px 0px 0px; }
.binding-image .font20Light{font-size: 20px;font-family: GillSans-Light;color: #000000;}
.binding-image .upload_lib ul { float: left; }
.binding-image .upload_lib ul li { width: inherit; margin: 0 9px; margin-top: 40px; }
.binding-image .upload_lib.image_gallery ul li img { width: 147px; }
.binding-image .upload_lib.image_gallery ul li .gallery_btn img { width: 100%; }
.text-center { text-align: center !important; }
.addbtn { margin-top: 310px; }
.trade_logo { z-index: -6; }
.border_toolbar { margin-left: 16.5%; }
/*#topImageToolBar{position: relative; left: 15%;}*/

/*ST-752*/
.divbullpenall{ margin-top: 1.5%;    margin-bottom: 62px; }/*RP ST-1868*/
.divbullpenparonama{ margin-top: 4.5%;    margin-bottom: 62px; }/*RP ST-1868*/
.frame_editor .frame_btn { display: block; width: 100%; padding: 4px 0; text-align: center; text-decoration: none; border: 1px solid #57307a; color: #6b448d; border-radius: 20px; margin: 0 auto 7px; position: sticky; background-color: #FFF; font-family: 'gill_sanssemibold'; text-transform: uppercase; font-size: 11px; }/*ST-1556 , 2019 02 19*/
.upload_lib ul li { float: left; width: 30%; margin: 0 57px 15px 0; display: block; }
/*.upload_lib ul li:nth-child(3n+3){margin: 0 0 15px 0;}*/
.div-SideBar-btns { position: absolute; bottom: 10%; width: 80%; }
.bull_heading h2 img { width: 35px; top: -7px; left: 3px;}
.bull_heading h2 { margin: 0 0 10px; font-size: 20px; padding-left: 50px; font-family: 'GillSans-Light'; display: inline-block; color:#000; }
.bull_wrapper { padding: 15px 10px 15px; border: 1px solid #979797; margin: 0 0 0; width: 100%; }
/*.main_heading.bull_heading .container-fluid{padding-left: 0px;}*/
.new_flip_main_wrapper { padding-left: 2%; float: left; width: 96%; }
.new_flip_main_wrapper, #dvDBPublishShare { width: 94%; padding-left: 5%; float: left }
/*ST-666 START*/

.modal-PagePopUp { display: none; /* Hidden by default */ position: fixed; /* Stay in place */ z-index: 999; /* Sit on top */ left: 0; top: 0; width: 100%; /* Full width */ height: 100%; /* Full height */ overflow: auto; /* Enable scroll if needed */ background-color: rgb(0,0,0); /* Fallback color */ background-color: rgba(255,255,255,0.8); /* Black w/ opacity */ }

/*ST-838*/
.modal-content-SCPopUp { background-color: #fefefe; margin-left: 10%; margin-top: 5%; padding: 1px 0 0 10px; border: 1px solid #888; width: 70%; height: 480px; }
/*ST-666, displaying popup in center*/
.modal-content-PagePopUp {background-color: #fefefe;padding: 1px 0 0 10px;border: 1px solid #888;position: absolute;width: 1380px;height: 725px;z-index: 15;/*top: 50%;left: 50%;*/margin: -320px 0 0 -690px;}/*ST-1310*/
/* The Close Button */
.close-SCPagePopUp, .close-PagePopUp { color: #aaa; float: right; font-size: 28px; font-weight: bold; padding-right: 10px; padding-top: 8px; }
.close-SCPagePopUp:hover, .close-SCPagePopUp:focus, .close-PagePopUp:hover, .close-PagePopUp:focus { color: black; text-decoration: none; cursor: pointer; }
/*ST-666 END*/

/*ST - 869 873 SUNK*/
#topImageToolBar .image_toolbar { height:49.9px; padding-top:7px !important; padding-bottom:7px !important;}
.image_toolbar ul.goto_img li#aGoToImageLibrary img{width: 34px;height: 34px;margin-right: 10px;}
/*ST-1281 AC*/
.modal.new_flip .modal-dialog {width: 464px;}/*ST-1870*/
.image_toolbar ul.blank_img li img{ margin-right:10px;cursor:pointer;}
.tab_content.Tab3DisplayImgOnTop{}
.cke_top, .cke_chrome{border: none !important;}                 
/*ST-901,1/14/2018 */
.cke_top{height: inherit !important; background-color: #f2f2f2 !important; padding-top:11px !important; line-height:32px !important;padding-bottom:11px !important;min-width:880px;}
.cke_combo_open{width: 30px !important;height: 30px !important; margin:0 !important;float: right !important;}
/*ST-1354*/
.cke_combo_arrow, .cke_button_arrow{width: 24.2px !important;height: 24.3px !important;background-image: url(/images/color_dropdown.jpg) !important;margin:0 !important;
background-repeat: no-repeat !important;background-position: right center !important;background-size: 26px 25px !important;color: transparent !important;border: 1px solid #58585b !important;outline: none !important;cursor: pointer !important;border-top-right-radius: 7px !important;border-bottom-right-radius: 7px !important;position: relative !important;top: -1px !important;right: -5px !important;z-index: 9;}
.cke_combo{ margin: 0 10px 0 0 !important} /*ST-1089*/
/*ST-1766*/
a.cke_combo_button, .cke_toolbox span:nth-child(2) a.cke_button, .cke_toolbox span:nth-child(6) a.cke_button{border: solid 0.7px #4d4d4c !important; width:125px !important; height:24.3px !important; background:#FFF !important;border-radius: 7px;-moz-border-radius: 7px;-webkit-border-radius: 7px;font-family: 'GillSansCE-Roman' !important;margin:0 !important;padding:0 !important;box-sizing:inherit !important;}
.cke_combo_on a.cke_combo_button, .cke_combo_off a.cke_combo_button:hover, .cke_combo_off a.cke_combo_button:focus, .cke_combo_off a.cke_combo_button:active{margin:0 !important;padding:0 !important;}
.cke_combo_text{font-family: 'GillSansCE-Roman' !important;line-height: 22px !important;color:#000 !important;width: auto !important;min-width: 70% !important;font-size: 13px !important;letter-spacing: -0.2px;color:#000 !important;}
.cke_combo__fontsize a.cke_combo_button {    width: 65px !important; /*ST-1089*/}
.cke_combo__fontsize .cke_combo_text { width: 42px !important;  min-width: 42px !important;}
.cke_toolgroup a.cke_button:last-child:after, .cke_toolgroup a.cke_button.cke_button_disabled:hover:last-child:after {content: "";position: inherit !important;height: inherit !important;width: 0;border-right:0 !important;margin-top: 0px !important;top: 0;right: -3px;}
.ShareMessageLabel{padding-top: 3rem;}/*RP ST-1674*/
.ShareMessage{ background: #ffffff;border: 0.9px solid #979797!important;}/*RP ST-1674*/
.ShareMessage textarea{resize: none;height: 59.9px!important;}/*RP ST-1674*/
.cke_toolbox span:nth-child(2){margin-top: -1px;}/*RP ST-1621*/
.cke_toolbox span:nth-child(2) .cke_button__textcolor_icon {background: inherit !important;}
.cke_toolbox span:nth-child(2) a.cke_button, .cke_toolbox span:nth-child(6) a.cke_button {width:50px !important;border-radius: 0px;-moz-border-radius: 0px;-webkit-border-radius: 0px;margin-right: 10px !important;}
.cke_toolbox span:nth-child(2) .cke_button_arrow {right: -12px !important;height:24px !important}
/*ST-1089*/
.cke_toolbox span:nth-child(6) a.cke_button{border-radius: 14px 20px 20px 14px;-moz-border-radius: 14px 20px 20px 14px;-webkit-border-radius: 14px 20px 20px 14px;width:60px !important;}
/*ST-1822,BV*/
.cke_toolbox span:nth-child(6) .cke_toolgroup a.cke_button:last-child:after, .cke_toolbox span:nth-child(6) .cke_toolgroup a.cke_button.cke_button_disabled:hover:last-child:after{content: "" !important;position: absolute !important;margin-top: 4px !important;top: 4px !important;right: 7px !important;background:url(/images/arrow-bottum.png) left top no-repeat;background-size: 14px 9px;width:14px;height:9px;}
/*ST-1354.1*/
.cke_toolbox span:nth-child(5).cke_toolbar {border-left: 1px solid #979797;padding-left: 0px; /*ST-1089*/margin-left:6px;}
.cke_toolbox span:nth-child(5).cke_combo{margin: 0 10px 0 0 !important;}
/*ST-1822,BV*/
.cke_toolgroup{    margin: 1px 2px 0px 0 !important;}
/*ST - 869 873 END SUNK */

/*ST-894 PK 12/15/2017*/
.flashdarkbutton{color: #FFF !important; background-color: #6B448D; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; text-decoration: none;}
/*875*/
.ui-dialog.ui-corner-all.ui-widget.ui-widget-content.ui-front.no-close.ui-draggable.ui-resizable{background-color: #fafafa; border: solid 2px #410166 ;}
.ui-widget.ui-widget-content{}
.ui-dialog-content .main_heading h1{ font-size:30px;}
.ui-dialog-content .main_heading h2{ font-size:20px; font-family: 'GillSans-Light';}
.spImageOnPage{font-size: 13px !important;font-weight: 600; font-family: 'gill_sanssemibold'; color: #4a4a4a; /*ST-886 PK 01/04/2018*/}
.bb-custom-firstpage{background-color: #FFF; } /*Remove opacity:0.6; in back cover ST-758*/
.bb-custom-firstpage .fred_nicolaus{border-color: rgba(0, 0, 0, 0.4) !important;}
.tab_content .range{ background:#9b9b9b; }

 @media screen and (min-width: 1401px) {
    .end_paper_lib .uploadbtnmargin{margin-top: 125px  !important;}
    .permanentchild a.frame_btn{ font-size:12px !important;}/*ST-1377, 2018-09-28   END VR*/
    .new_flip_main_wrapper { width: 94%; }
    .drag_drop_img .gallery_btn span { margin: 0 -2px 0 0; }
    .end_paper_lib h3 { padding-top: 8%;/*padding-bottom:11%*/ }
    .end_paper_lib .by_color, .end_paper_lib .by_style, .end_paper_lib .pick_solid_color { margin: 15% auto 15%; }
     .end_paper_lib .color_wrapper span { width: 25px; height: 25px; }
     /* Added By VR 2018-09-24 END*/
    .end_paper_lib .color_wrapper span img { height: 100%; width: 100% }
    .tool_cottage .cootage_on_georgica .click_upload, .tool_cottage .project_f11 ul li { width: 180px; height: 177px; }
    .tool_cottage .cootage_on_georgica .click_edit { width: 228px; }
    .tool_cottage .project_f11 .click_edit_inner { padding: 5px 5px 5px 10px; }
    .page_manager_f13 .inner_wrapper { padding: 20px 20px 30px 20px; }
    .frame_body { width: 81.5% }
}

 /*== 16 oct css start here  ==*/

.modal-content {    background: #fafafa !important;}
#dvOpenFlipBook .modal-body img {    max-height: 90px;}
div#dvFlipBookDeleteClear .modal-content h1, div#dvSaveMyFlipBook .modal-content h1, div#dvAutoSaveSettings .modal-content h1, div#dvOpenFlipBook .modal-content h1, div#dvDeleteMultipleFlipBook .modal-content h1, div#dvFlipBookDeleteClearfb .modal-content h1 {font-size: 25px;padding: 0;font-family: 'GillSans';color: #434343;margin: 12px 0;}
.popupMainH1Text {font-size: 18.8px !important;/*RP ST-1674*/padding: 0 !important;font-family: 'avenirltstd-heavy' !important;/*RP ST-1674*/color: black !important;/*RP ST-1674*/margin: 12px 0;line-height: initial;font-weight: 900;/*RP ST-1674*/height: 25px;/*RP ST-1674*/}
div#dvNewFlipBookName .modal-content h1, div#dvOpenFlipBook .modal-content h1, div#dvDeleteMultipleFlipBook .modal-content h1 {font-size: 18px;font-family: 'GillSans';color: #4a4a4a;margin: 12px 0;line-height: initial;}
.globalPoupuButtonStyle{font : gill_sanssemibold !important;font-size: 14px !important;color: #410166 !important;}
select#ddlFlipbooks, select#ddlFlipbooksDelete {font-size: 16px !important;background: #fff !important;font-family: 'gill_sanssemibold' !important;}

@media (max-height: 768px) and (min-width: 1366px) and (max-width: 1366px) {
    .modal-content-PagePopUp {width: 1200px;margin: -320px 0 0 -600px;}
    #frame_body_outer {height: 80vh !important;}
    .frame_body .arrow_wrapper { width: 950px; }
    .bull_heading { width: 99%; margin: 0 0 30px 3%; }
    .page_manager_f13 { width: 79%; }
    .frame_editor .frame_btn { font-size: 10px; letter-spacing: -1px; }
    .frame_editor .frame_btn span { left: 6px; }
    .paddingleft7 { padding-left: 13%; }
    .frame_editor h2 { font-size: 12px; }
    .end_paper_lib{ margin-bottom:10px}
    .end_paper_lib .by_style ul li { line-height: 18px }
    .end_paper_lib .color_wrapper span { width: 25px; height: 25px; margin-bottom:3px }
    .end_paper_lib .color_wrapper span img { width: 25px; height: 25px; }
    .uploads_btn, .uploads_btn a { margin-bottom: 13px; }
    .end_paper_lib .pick_solid_color { margin: 4px auto 5px; }
    .end_paper_lib .by_color h4 { margin: 0px 0px 4px; }
    .end_paper_lib .by_color, .end_paper_lib .by_style, .end_paper_lib .pick_solid_color { margin: 7px auto 29px; }
}
@media (max-height: 800px) and (min-width: 1280px) and (max-width: 1280px) {
    #frame_body_outer {height: 80vh !important;}
    .frame_body .arrow_wrapper { width: 950px; }
    .bull_heading { width: 99%; margin: 0 0 30px 2.7%; }
    .page_manager_f13 { width: 79%; }
    .frame_editor .frame_btn { font-size: 10px; letter-spacing: -1px; }
    .frame_editor .frame_btn span { left: 6px; }
    .paddingleft7 { padding-left: 13%; }
    .frame_editor h2 { font-size: 12px; }
    .end_paper_lib .by_style ul li { line-height: 18px }
    .end_paper_lib .color_wrapper span { width: 15px; height: 15px; }
    .end_paper_lib .color_wrapper span img { width: 15px; height: 15px; }
    .uploads_btn, .uploads_btn a { margin-bottom: 6px; }
    .end_paper_lib .pick_solid_color { margin: 4px auto 5px; }
    .end_paper_lib .by_color, .end_paper_lib .by_style, .end_paper_lib .pick_solid_color { margin: 7px auto 8px; }
    .flip_view ul li:last-child ul { padding: 5px; }
    .flip_view ul li .flip_page_view ul li { height: 12px; width: 12px; }
    .uploads_btn { bottom: 15%; }
}
@media (max-height: 768px) and (min-width: 1280px) and (max-width: 1280px) {
     #frame_body_outer {        height: 80vh !important;    }
    .frame_body .arrow_wrapper { width: 950px; }
    .bull_heading { width: 106%; margin: 0 0 30px 2.7%; }
    .page_manager_f13 { width: 79%; }
    .frame_editor .frame_btn { font-size: 10px; letter-spacing: -1px; }
    .frame_editor .frame_btn span { left: 6px; }
    .paddingleft7 { padding-left: 13%; }
    .frame_editor h2 { font-size: 12px; }
    .end_paper_lib .by_style ul li { line-height: 18px }
    .end_paper_lib .color_wrapper span { width: 15px; height: 15px; }
    .end_paper_lib .color_wrapper span img { width: 15px; height: 15px; }
    .uploads_btn, .uploads_btn a { margin-bottom: 6px; }
    .end_paper_lib .pick_solid_color { margin: 4px auto 5px; }
    .end_paper_lib .by_color, .end_paper_lib .by_style, .end_paper_lib .pick_solid_color { margin: 7px auto 8px; }
    .flip_view ul li:last-child ul { padding: 5px; }
    .flip_view ul li .flip_page_view ul li { height: 12px; width: 12px; }
    #divBullPenDrag { margin-top: 10.5%; }
    .uploads_btn { bottom: 15%; }
}
 @media (min-width:992px) and (max-width:1199px) {
    .sidebar { /*width: 6% !important; ST-901*/ float: left; height: 89vh; min-height: 89vh; }
    #frame_body_outer {        height: 80vh !important;    }
    .flip_view ul li:last-child ul { padding: 5px; }
    .flip_view ul li .flip_page_view ul li { width: 10px; }
    .frame_body { width: 80%; }
    .new_flip_main_wrapper { width: 94% ; }
    #divBullPenDrag { margin-top: 12.5%; }
    .drag_drop_img .binding_views .view_imges { padding: 5px 10px; font-size: 9px; }
    .frame_editor .rest_library { height: 40%; }
    .drag_drop_wrapper .drag_middlebar ul li { width: 10px; }
    .drag_drop_img.list-grid .binding_views ul li .lib_view_wrapper span { width: 9.5px; }
    .uploads_btn a { margin-bottom: 8px; }
    .end_paper_lib .color_wrapper span, .end_paper_lib .color_wrapper span img { width: 12px; height: 12px; }
    .end_paper_lib .by_color h4, .end_paper_lib .by_style h4, .end_paper_lib .pick_solid_color h4 { font-size: 11.5px; margin-bottom: 5px; }
    .end_paper_lib .by_style ul li label { font-size: 11px; line-height: 10px; }
    .end_paper_lib .by_color, .end_paper_lib .by_style, .end_paper_lib .pick_solid_color { margin: 7px auto 8px; width: 90%; }
    .middle_endpaper { padding: 0 30px; }
}


    /*MB, 2/24/18, Reversed Changeset 1065*/
@media (max-width:991px) {
    .sidebar { /*ST-901 6%*/ float: left;  }
    /*ST-901*/
    #frame_body_outer {height: 674px !important;}
    .selecting_endpaper { height: 704px; min-height: 704px; }/*was 100%* before 901*/
    .flip_view ul li:last-child ul { padding: 5px; }
    .flip_view ul li .flip_page_view ul li { width: 12px; }
}

@media only screen and (max-width: 1199px) {
    .upload_lib ul li { width: 29%; margin: 0 60px 40px 0; }
    .img_library { overflow-x: hidden; }
    .img_pop_gallery_wrapper .img_pop_gallery { width: 158px; }
    .publish_flip, a.download_flip, a.send_flip { margin: 0; }
    h2.share_social { margin: 20px 0; }
    .book_block { top: 50px; }
}
@media only screen and (max-width: 992px) {
    .main_heading h2.library { font-size: 18px; }
    .upload_lib ul li { margin: 0 46px 40px 0; }
    .upload_lib_filter .gallery_btn_wrapper, .upload_lib_filter #proRangeSlider { text-align: center; }
    /****653 styles****/
    .bull_heading { width: 90%; }
    /****End 653 styles****/
    .img_pop_gallery_wrapper .img_pop_gallery { width: 205px; }
    .gallery_btn_wrapper, #proRangeSlider { text-align: center; }
    .image_gallery ul { padding: 0 15px 0 0; }
    .image_lib_wrapper { padding: 0 15px; }
    .img_pop_gallery_wrapper { margin: 0 -5px 15px 0; }
    .link_my_flip .modal-dialog { width: 700px; }
    .add_page_flip .modal-content .modal-body { padding: 15px 30px; }
    .double_spreads_wrapper ul li { margin: 0 39px 0 0; width: 28%; }
    .single_spread_wrapper ul li:first-child { width: 28%; margin: 0 31px 10px 0; }
    .single_spread_wrapper ul li { margin: 0 19px 10px 0; }
    /*653*/
    .image_toolbar { padding: 10px 0 10px 10px; }
    .image_toolbar h1, .image_toolbar ul.goto_img li a, .image_toolbar ul.blank_img li a { font-size: 16px; }
    .image_toolbar ul.image_tools, .image_toolbar ul.goto_img, .image_toolbar ul.blank_img { margin: 0 0 0 15px; line-height: 20px; }
    .image_toolbar ul.image_tools li, .image_toolbar ul.goto_img li, .image_toolbar ul.blank_img li { margin: 0 15px 0 0; }
    .image_toolbar ul.image_tools li a { font-size: 12px; }
    .image_toolbar ul.blank_img li { margin: 0; }
    .bull_heading h2 img { width: 40px; top: -9px; }
    .bull_heading h2 { padding-left: 60px; }
    .bull_wrapper { padding: 15px 15px 15px; margin: 15px 0 0; }
    .bull_heading h2 { font-size: 18px; }
    .image_gallery ul.responsive2 { padding: 0; }
    /*653*/
}

@media only screen and (max-width: 768px) {
    .upload_lib ul li {width: 47%;margin: 0 25px 20px 0;}
    .upload_lib ul li:nth-child(3n+3) {margin: 0 25px 20px 0;}
    .upload_lib ul li:nth-child(2n+2) {margin: 0 0 20px 0;}
    .upload_lib_filter #proRangeSlider {text-align: center;}
    /****653 styles****/
    .bull_heading h2 img {top: 0;}
    /****End 653 styles****/
    .image_lib_wrapper {padding: 0 14px;}
    .img_pop_gallery_wrapper .img_pop_gallery {width: 180px;}
    .book_block {position: static;}
    .publish_flip.download_flip {position: relative;z-index: 10;}
    .input_wrapper {width: 290px;}
    .share_wrapper h2.hardcopies {margin: 20px 0;text-align: left;}
    a.order_flip {float: right;margin: 0;}
    h2.share_social {margin: 75px 0 20px;}
    .social_icons ul li {margin: 0 20px 0 0;}
    a.share_btn {float: right;}
    .link_my_flip .modal-dialog {width: 100%;margin: 0;padding: 20px;}
    .add_page_flip h2 {font-size: 18px;}
    .double_spreads_wrapper ul li {width: 45%;margin: 0 30px 0 0;}
    .double_spreads_wrapper ul li:nth-child(2n) {margin: 0;}
    .double_spreads_wrapper ul li:nth-child(3n) {margin: 0 30px 0 0;}
    .double_spreads_wrapper ul li:last-child {margin: 0;}
    .double_spreads_wrapper .radio label input[type=radio] {margin: 2px 0 0 4px;}
    .single_spread_wrapper ul li:first-child {width: 45%;margin: 0 22px 10px 0;}
    .single_spread_wrapper ul li {width: 22%;margin: 0 10px 10px 0;}
    .single_spread_wrapper ul li:nth-child(3) {margin: 0 0 10px 0;}
    .single_spread_wrapper ul li:nth-child(5) {margin: 0 10px 10px 0;}
    .single_spread_wrapper ul li:nth-child(7) {margin: 0 0 10px 0;}
    /*653*/
    .image_toolbar h1 {display: block;font-size: 18px;margin: 0 0 15px 0;}
    .image_toolbar ul.image_tools {margin: 0 0 15px 0;}
    .image_toolbar ul.goto_img {margin: 0;}
    .image_toolbar ul.goto_img li a, .image_toolbar ul.blank_img li a {font-size: 18px;}
    /*653*/
}
 @media only screen and (max-width: 480px) {
    .upload_lib ul li { width: 100%; margin: 0 0 20px; }
    .upload_lib ul li:nth-child(3n+3), .upload_lib ul li:nth-child(2n+2) { margin: 0 0 20px 0; }
    input[type=range] { width: 200px; }
    /****653 styles****/
    .bull_wrapper ul li/****End 653 styles****/
    .img_pop_gallery_wrapper .img_pop_gallery { width: 205px; }
    .create_new_wrapper h1 { font-size: 20px; }
    .share_wrapper h2 { font-size: 16px; }
    .control { padding-left: 40px; font-size: 14px; }
    .publish_flip.download_flip { margin: 0 0 10px; }
    .book_block { width: auto; }
    .input_wrapper { width: 100%; margin: 0 0 10px; }
    .share_wrapper h2.hardcopies { margin: 45px 0 20px; }
    .social_icons ul li { margin: 0 25px 0 0; }
    .link_my_flip .modal-content h1 { font-size: 20px; }
    .publish_flip_link { margin: 0 0 60px; }
    .image_gallery.choose_image ul { padding: 0; }
    .image_gallery.choose_image ul li { margin: 0 20px 0 0; }
    .image_lib_wrapper.choosean_image { padding: 0 15px; }
    .image_lib_wrapper.choosean_image .img_pop_gallery_wrapper { margin: 0 -10px 15px 0; padding: 0 10px 0 0; }
    .image_lib_wrapper.choosean_image .img_pop_gallery_wrapper .img_pop_gallery { width: 100%; margin: 0 15px 15px 0; }
    .main_heading h2 img { left: 0; top: -15px; }
    .main_heading h2 { margin: 30px 0 20px; }
    .add_page_flip .modal-content .modal-body { padding: 15px; }
    .double_spreads_wrapper ul li { width: 100%; margin: 0; }
    .single_spread_wrapper ul li:first-child { width: 100%; margin: 0 0 10px 0; }
    .single_spread_wrapper ul li { width: 45%; margin: 0 19px 10px 0; }
    .single_spread_wrapper ul li:nth-child(2n+1) { margin: 0 0 10px 0; }
    .main_heading .bull_wrapper h2 { margin: 0 0 17px; }
    .main_heading .bull_wrapper h2 img { top: 0; }

    .upload_lib ul li { float: left; width: 30%; margin: 0 57px 15px 0; display: block; }
    .bull_heading h2 img { width: 35px; top: -7px; }
    .bull_heading h2 { margin: 0 0 10px; font-size: 20px; padding-left: 50px; font-family: 'GillSans-Light'; font-weight: 600; display: inline-block; }
    .bull_wrapper { padding: 12px 15px 15px; border: 1px solid #000000; margin: 0 0 0; width: 105%; }
}
 @media only screen and (max-width: 845px) {
    .sidebar {float: left; /* width: 60px !important ST-901 height: 100%; min-height: 100%;*/ }
    #frame_body_outer {    height: 815px !important;}
    .endpaper_listing { height: 140px; margin-bottom: 10px; }
    .flip_view ul li:last-child ul { padding: 4px; }
    .flip_view ul li .flip_page_view ul li { height: 12px; width: 12px; }
    .flip_view ul li .view_sidebar ul li { margin: 0 auto 2px; }
    .flip_view ul li .view_sidebar { padding: 2px 0; }
    .uploads_btn a { margin: 0 auto 5px; }
    .selecting_endpaper h4 { font-size: 12px; }
}

/*}*//**** ST-770 *****/
.addLRSection_flip { float: left; display: inline-block; border: 1px solid #57307a; color: #57307a; background-color: #FFF; font-size: 13px; font-family: 'GillSans-SemiBold';height: 25px; width: 173px; font-weight: 600; text-align: center; margin: 0 0px 0 130px; border-radius: 20px; padding: 0px;line-height: 25px; text-decoration: none; transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s; text-transform: uppercase;  } /*margin-top: 56px; PK ST-1556*/
.addLRSection_flip span { font-size: 23px;vertical-align: middle; position:absolute; margin-left:-8px}
.addLRSection_flip:hover { color: #FFF; background-color: #6B448D; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; text-decoration: none; }
.addLRSection_flip:hover.opacity60 {border: 1px solid #57307a;color: #57307a;background-color: #FFF;}/*ST-1688,BV 2019 01 03*/
/*ST-682#C*/
.highlight-onDrag { border: 3px solid #01e8ff !important; z-index: 99; }/* ST-1575, BV 2019 01 02,2019 02 11*/
/*ST-786*//*ST-1089*/
.cke_combo_text { width: 85px !important; min-width: 65px !important; }

.cke_combo__lineheight a.cke_combo_button{width: 113px !important;} /*ST-1354.1*/
.cke_combo__lineheight a.cke_combo_button .cke_combo_text{width: 60px !important;min-width: 60px !important;text-align:center;}
.cke_combo__fontsize a.cke_combo_button .cke_combo_text{width: 25px !important;min-width: 25px !important; text-align:center}/*ST-1354*/
.cke_combo__letterspacing a.cke_combo_button{ width:103px !important;}
.cke_combo__letterspacing a.cke_combo_button .cke_combo_text{width: 66px !important; min-width: 66px !important;}
.cke_combo__tokens a.cke_combo_button{width:85px !important;}
.cke_combo__tokens a.cke_combo_button .cke_combo_text{width: 35px !important; min-width: 35px  !important;}/*ST-1354.1,VR*/
a.cke_button{ padding-left:0 !important; padding-right:0 !important;}
.cke_combo_text{ padding-left:7px !important;}
.cke_button__textcolor .cke_button__textcolor_icon{ margin-left:4px; margin-top:4px;} /*ST-1108*/
a.cke_button_off{ border:1px solid #f8f8f8 !important;}
.cke_toolbar a.cke_button__texttoolbar:focus, .cke_toolbar a.cke_button__texttoolbar:active, .cke_toolbar a.cke_button__texttoolbar:hover {padding:4px 0px !important; }

/*From Page Mange*/
.top_heading .heading_in .cke_editable p {font-size: 18px !important;font-family: 'ACaslonPro-Regular';}
.bottom_text_wrap .bottom_text .cke_editable p, .bottom_content .cke_editable p {font-family: 'ACaslonPro-Regular';color: #a1a1a1;}
.fred_text {overflow: visible !important}
#aCustomURL {width: 161.5px;height: 32.5px;opacity: 0.45;border-radius: 38px;border: solid 1px #57307a;background-color: #ffffff;text-align: center;text-transform: uppercase;padding: 8px 40px;display: inline-block;font-size: 13px;margin: 0 7px 5px;font-family: 'gill_sanssemibold';font-weight: 600;font-style: normal;font-stretch: normal;line-height: normal;letter-spacing: normal;color: #410260;}
#aCustomURL.disableButton:hover {background-color: #57307a; color: #FFF !important; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; font-family: 'gill_sanssemibold' !important; font-size: 14px;}
.commonFontStyle {font-family: GillSans;font-style: normal;font-stretch: normal;line-height: normal;letter-spacing: normal;text-align: center;}
/*ST-1398*/
#txtCustomURL {font-size: 20px;font-weight: 300;color: #a1a1a1;border: none;border-bottom: 1px solid #c01f2a;background: transparent;text-align: left;margin-left: -5px;max-width: 120px;padding: 0px;height:23px;}
.commonFontStyleLight {font-family: GillSans-Light;font-style: normal;font-stretch: normal;line-height: normal;letter-spacing: normal;text-align: center;}
.commonFontStyleBold {font-family: GillSans-SemiBold;font-style: normal;font-stretch: normal;line-height: normal;letter-spacing: normal;text-align: center;}
#aCustomURL:hover {color: #6B448D !important;}



/*ST-777*/
.Img-AreaBgDefault {border: solid 1px #000000; background-color: #FFFFFF; }/*ST-1404 , ST-1891,BV,#A2A2A2 to Black as suggested*/
.BullPenUiLiImg { margin-left: auto; margin-right: auto; display: block; max-height: 100px; max-width: 166px; }
.BullPenUiLiImg, .RestOfImgUiLiImg {z-index: 9999;}
.TooImageH1 {padding-left: 30px;}
.ImgBullPenImgLI { height: 100px; width: 166px; }

/*ST-882 PK 01/04/2018*/
#dvAlertBox {z-index: 9999; }
.eraserForAllDiv img, .eraserForAllDivimg, .Hamburger {display: none;} /*ST-1577,BV 2019 01 016*/
.eraserForAllDivimg {cursor:pointer;}
/*ST-1125*/
.ShowFBLibaryFullOnBar { height: 72% !important; }
/*ST-1124*/
.cke_editable p span:first-child {line-height: 1;}/*RP ST-1751*/ /*ST-1843 , 1.1 to  1*/
/*ST-901*/
.selecting_endpaper {width:166px;}
.cke_dialog_tabs a:nth-child(2) {display:none;} /*ST-1110*/
.sidebar {width: 80px;}
/*ST-1310,BV*/
#divPopUp-ContentParent{width: 100%;overflow: auto;}
.zoomPopUp05  {-webkit-transform: scale(0.500);-moz-transform: scale(0.500);-ms-transform: scale(0.500);transform: scale(0.500);transform-origin:  center;}
.zoomPopUp100 {-webkit-transform: scale(1.000);-moz-transform: scale(1.000);-ms-transform: scale(1.000);transform: scale(1.000);transform-origin:  center;}
.zoomPopUp125 {-webkit-transform: scale(1.250);-moz-transform: scale(1.250);-ms-transform: scale(1.250);transform: scale(1.250);transform-origin: top left;}
.zoomPopUp150 {-webkit-transform: scale(1.500);-moz-transform: scale(1.500);-ms-transform: scale(1.500);transform: scale(1.500);transform-origin: top left;}
.zoomPopUp200 {-webkit-transform: scale(2.000);-moz-transform: scale(2.000);-ms-transform: scale(2.000);transform: scale(2.000);transform-origin: top left;}
.zoomPopUp250 {-webkit-transform: scale(2.500);-moz-transform: scale(2.500);-ms-transform: scale(2.500);transform: scale(2.500);transform-origin: top left;}
.zoomPopUp500 {-webkit-transform: scale(5.000);-moz-transform: scale(5.000);-ms-transform: scale(5.000);transform: scale(5.000);transform-origin: top left;}
.divddlZoomPopUp {text-align:right;}
.pZoomArrows {margin-top:2%;}
.pZoomArrows.pZoomArrowsImg {/*margin-top: -2.5%;*/background: white;}
.add_page_flip h2 {font-family: 'GillSans-Light';}
.add_page_flip .beta_testing {right: 2%;bottom: 10px;top:1060px !important;}
.add_page_flip .beta_testing img { height:80px}
.add_page_flip .beta_testing img {    height: 80px}
#singletCustomonLoadImage {display: none !important}

/*ST-1340, 2018-09-19 START VR*/
.uploadbtnmargin {margin-top: 145px;margin-bottom: 70px}
.selecting_endpaper.endpapergridbar {min-height: 1100px;} /*ST-1687*/
.end_paper_lib .by_style ul li { margin-bottom:5px !important}

/*ST-1340, 2018-09-19 END VR*/

/*ST-1340, 2018-09-20 START VR*/
.image_lib_wrapper { position:relative}
.img_pop_gallery_wrapper:after {background: #979797;width: 1px;/*height: 100%; RP ST-1796*/position: absolute;content: '';right: -15px;z-index: 99999}
/*ST-1340, 2018-09-20 END VR*/
/*ST-1321, 2018-09-20 START VR*/
.dash_boardmain {    position: relative}
/*ST-1438 PK, 10/17/2018 START */
.dash_board {height: 850px !important;margin: 0 -10px 15px 0;width: 96.5%;}/*ST-1558,VR*/
/*ST-1314 PK , 09/19/2018 , Added Class For Blank Print Warning CSS*/
.blankPrintWarning {width: 456px;height: 18px;color: #000000;margin: auto;display: block;font-family: GillSans;font-size: 15px;font-weight: bold;}
.paddingtop2pct {padding-top:2% !important;}
.paddingtop5pct {padding-top: 4% !important;}
.margintopmin2pct {margin-top: -2%;}
.blankPrintWarning {padding-top:0px;}

/*ST-1352, 2018-09-21 F-11 zeplin START VR*/
.select_img:hover {background-color: #540B8F !important;}
.select_img .btn_hover, .select_img .btn_active {display: none}
.select_img.active .btn_initial, .select_img.active .btn_active {display: none}
.select_img.active .btn_hover {display: block}
.select_img:hover .btn_active {display: block}
.select_img.active:hover {background-color: #540B8F;color: #FFF;transition: all ease-in-out 0.5s;-webkit-transition: all ease-in-out 0.5s;-moz-transition: all ease-in-out 0.5s;-ms-transition: all ease-in-out 0.5s;}
.select_img.active:hover .btn_initial, .select_img.active:hover .btn_hover {display: none}
.select_img.active:hover .btn_active {display: block}
.color_wrapper { width:80%; margin:0 auto}
.color_wrapper a {float: left;margin: 2px;}
/*ST-1352, 2018-09-21 F-11 zeplin END VR*/

/*ST-1343, 2018-09-21 F-12 zeplin START VR*/
.selecting_endpaper .flip_view > ul > li .click_page_view {    /*background: rgba(255, 255, 255, 0.6); RP ST-1833*/}
.selecting_endpaper .flip_view > ul > li.active .click_page_view, .selecting_endpaper .flip_view > ul > li.active1 .click_page_view, .selecting_endpaper .flip_view > ul > li:hover .click_page_view {background: rgba(255, 255, 255, 0);}
/*ST-1343, 2018-09-21 F-12 zeplin END VR*/
.divTopToolBars {height:45px;}/*ST-1278*/

/*Added, 2018-09-24 Endpaper paddingbottom START VR*/
#divEndPaperBar .selecting_endpaper { padding-bottom:140px}

.color_wrapper{ height:90px; width: 82%; }
/*Added, 2018-09-24 Endpaper PICK SOLID COLOR SCROLL END VR*/

/*Update, 2018-09-25 Endpaper Sidebar Heading , whiteline and gridview image list padding START VR*/
.selecting_endpaper h3 {padding: 0 15px 0 15px; font-size:17px}
.flip_lib_div {background: #fff;height: 1px;width: 95px;margin: -10px auto 14px; border:0px}
.hrEpDiv.flip_lib_div {width: 80px;} /*ST-1807,ST-1766*/
.img_pop_gallery_wrapper .img_pop_gallery { margin-bottom:21px}/*ST-1687*/
.selecting_endpaper.endpapergridbar h3{padding: 0 0px 0 0px; text-align: center;}
.selecting_endpaper.endpapergridbar .color_wrapper{ height:auto; overflow:hidden}
.end_paper_lib .by_color h4,.end_paper_lib .by_style h4{  font-family: GillSans;  font-size: 15px;color: #000000;}
.end_paper_lib .by_style ul li label{font-family:'GillSans-Light'; font-size:13px; color:#000000}
.end_paper_lib .by_color{ margin-top:16px;}
.end_paper_lib .by_style{ margin-top:35px;}
.end_paper_lib .uploadbtnmargin{ margin-top:112px; margin-bottom:95px}
.end_paper_lib .collapse1{ top:0px}
.end_paper_lib .by_color h4{ margin-bottom:7px;}/*ST-1687,BV*/
/*Update, 2018-09-25 Endpaper Sidebar Heading , whiteline and gridview image list paddin START VR*/
.disable_btn{opacity:0.5; cursor:not-allowed; pointer-events:none;}
.userEndPaperImg {height: 170px;width: 100%;}
.sysEndPaperImg {height: 100%;}

/*ST-1797*/
.divUploadEPToolTip {font-size: 10px;font-family: GillSans-Light;text-align: center;font-weight: 600;color: #000000;position: absolute;top: 46px;}
.divUploadEPToolTip span {position :absolute;width: 100px;}
.divUploadEPToolTip .EpSpan1 {top: 8px;left: 0;}
.divUploadEPToolTip .EpSpan2 {left: 1px;top: 36px;}
.ulliEpUploadText .divUploadEPToolTip {display:none;}
.ulliEpUploadText:hover .divUploadEPToolTip {display:block;}

/*Update, 2018-09-26 Endpaper Sidebar Heading, Scroll Bar  START VR*/
.selecting_endpaper.pageview{min-height:1300px}  /*ST-1584, 2019 01 23 min-height:1070px*/
.selecting_endpaper .noendpaperbtn{height:30px; position:relative; margin-top:30px; margin-bottom:24px; margin-top:0px;}
.selecting_endpaper .noendpaperbtn .uploads_btn .fa{ color:#000; font-size:15px;}

/*************** SCROLLBAR BASE CSS ***************/
.scroll-wrapper {overflow: hidden !important;padding: 0 !important;position: relative;}
.scroll-wrapper > .scroll-content {border: none !important;box-sizing: content-box !important;height: auto;left: 0;margin: 0;max-height: none;max-width: none !important;overflow: scroll !important;padding: 0;position: relative !important;top: 0;width: auto !important;}
.scroll-wrapper > .scroll-content::-webkit-scrollbar {height: 0;width: 0;}
.scroll-element {display: none;}
.scroll-element, .scroll-element div {box-sizing: content-box;}
.scroll-element.scroll-x.scroll-scrollx_visible,.scroll-element.scroll-y.scroll-scrolly_visible {display: block;}
.scroll-element .scroll-bar,.scroll-element .scroll-arrow {cursor: default;}
.scroll-textarea {border: 1px solid #cccccc;border-top-color: #999999;}
.scroll-textarea > .scroll-content {overflow: hidden !important;}
.scroll-textarea > .scroll-content > textarea {border: none !important;box-sizing: border-box;height: 100% !important;margin: 0;max-height: none !important;max-width: none !important;overflow: scroll !important;outline: none;padding: 2px;position: relative !important;top: 0;width: 100% !important;}
.scroll-textarea > .scroll-content > textarea::-webkit-scrollbar {height: 0;width: 0;}
/*************** SCROLLBAR RAIL ***************/
.scrollbar-rail > .scroll-element,.scrollbar-rail > .scroll-element div {border: none;margin: 0;overflow: hidden;padding: 0;position: absolute;z-index: 10;}
.scrollbar-rail > .scroll-element {background-color: transparent;}
.scrollbar-rail > .scroll-element div {display: block;height: 100%;left: 0;top: 0;width: 100%;}
.scrollbar-rail > .scroll-element .scroll-element_size {background-color: #BBBBBB;background-color: rgba(0, 0, 0, 0.15);}
.scrollbar-rail > .scroll-element .scroll-element_outer:hover .scroll-element_size {background-color: #666;background-color: rgba(0, 0, 0, 0.5);}
.scrollbar-rail > .scroll-element.scroll-x {bottom: 0;height: 12px;left: 0;min-width: 100%;padding: 3px 0 2px;width: 100%;}
.scrollbar-rail > .scroll-element.scroll-y {height: 100%;min-height: 100%;padding: 0 2px 0 3px;right: 0;top: 0;width: 25px;}
.scrollbar-rail > .scroll-element .scroll-bar {background-color: #fff;-webkit-border-radius: 10px;-moz-border-radius: 10px;border-radius: 10px;box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);}
.scrollbar-rail > .scroll-element .scroll-element_outer:hover .scroll-bar {box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);}
/* scrollbar height/width & offset from container borders */
.scrollbar-rail > .scroll-content.scroll-scrolly_visible {left: -17px;margin-left: 17px;}
.scrollbar-rail > .scroll-content.scroll-scrollx_visible {margin-top: 17px;top: -17px;}
.scrollbar-rail > .scroll-element.scroll-x .scroll-bar {height: 10px;min-width: 10px;top: 1px;}
.scrollbar-rail > .scroll-element.scroll-y .scroll-bar {left: 1px;min-height: 30px;width: 20px; height:30px !important}
.scrollbar-rail > .scroll-element.scroll-x .scroll-element_outer {height: 15px;left: 5px;}
.scrollbar-rail > .scroll-element.scroll-x .scroll-element_size {height: 2px;left: -10px;top: 5px;}
.scrollbar-rail > .scroll-element.scroll-y .scroll-element_outer {top: 5px;width: 24px;}
.scrollbar-rail > .scroll-element.scroll-y .scroll-element_size {left: 10px;top: -10px;width: 2px;}
/* update scrollbar offset if both scrolls are visible */
.scrollbar-rail > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size {left: -25px;}
.scrollbar-rail > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size {top: -25px;}
.scrollbar-rail > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_track {left: -25px;}
.scrollbar-rail > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_track {top: -25px;}

.selectyouendpaper{ height:230px !important; margin-bottom:24px;}

.endpaper_listing{ height:100%}
.uploadendpaper{height:214px !important; margin-top:10px; margin-bottom:20px}
.picksolidcolor{height:90px !important;}
.picksolidcolor .color_wrapper{ height:100%}
.selecting_endpaper.endpapergridbar .color_wrapper{ margin-bottom:0px}
.flip_view ul li{ width:63px; height:46px}/*RP ST-1833*/
.flip_view ul .page-view-active-images{background-image:url(/images/FBIcons/page-view-active.png);width:64px;}/*RP ST-1833*/
.flip_view ul .page-view-deactive-images{background-image:url(/images/FBIcons/page-view-deactive.png);width:61px;}/*RP ST-1833*/
.flip_view ul .page-view-deactive-images:hover{background-image:url(/images/FBIcons/page-v-iew-hover.png);width:61px;}/*RP ST-1833*/
.flip_view ul .filter-view-active-images{background-image:url(/images/FBIcons/filter-active.png)}/*RP ST-1833*/
.flip_view ul .filter-view-deactive-images{background-image:url(/images/FBIcons/filter-inactive.png);width:61px;height:44px}/*RP ST-1833*/
.flip_view ul .filter-view-deactive-images:hover{background-image:url(/images/FBIcons/filter-hover.png);width:61px;height:44px}/*RP ST-1833*/
.flip_view ul li .view_sidebar {padding: 4px 0 5px 0;}
.flip_view ul li .flip_page_view{padding: 9px 2px;}
.flip_view ul li ul{ background:#fff}
.flip_view ul li .view_sidebar ul{background:#D8D8D8}
.flip_view ul li.active{ background:#fff;border: 3px solid #000;}
.flip_view ul li.active ul{padding: 3px 5px;}

/*Update, 2018-09-26 Endpaper Sidebar Heading, Scroll Bar  END VR*/
.endpaper_listing ul li{ text-align:left; padding-left:5px}
/*ST-1378, 2018-09-27   START VR*/
.bb-custom-firstpage .fred_nicolaus {position: absolute;top: 45%;border: 1px solid #000000;padding: 6px;min-width: 250px;max-width: 400px;overflow: hidden;width: auto;left: 50%;-webkit-transform: translateX(-50%);transform: translateX(-50%)}
/* PK , 02/01/2019 , Pick Wrong Color , Instead of Hover Picked Color For Selected Text , ST-1613 */
.bb-custom-firstpage .fred_nicolaus:hover {border: solid 3px #c6deff !important;}
.bb-custom-firstpage .fred_nicolaus {border: 3px solid transparent;} /*ST-2025*/
.bb-custom-firstpage.left_layout_border #div2 p {width: 136px;height: 28px;font-family: 'GillSans-Light';font-size: 25px;font-weight: 300;font-style: normal;font-stretch: normal;line-height: normal;letter-spacing: normal;text-align: center;color: #888888;}
/*ST-1378, 2018-09-27   END VR*/

#divEndPaperGridViewMain{ margin-top:-20px}
#divEndPaperGridViewMain .main_heading h1 {font-size: 25px}
#divEndPaperGridViewMain h3{ margin-top:0px}
.end_paper_lib .by_style{ margin-bottom:10px}

/*ST-1377, 2018-09-28   START VR*/
.sidebar ul #liFlipbooks img { width: 60px; margin:0 auto }
.sidebar ul #liPages img { width: 53px; margin:0 auto }
.sidebar ul #liImages img { width: 40px; margin:0 auto }
.sidebar ul #liEndPaper img { width: 40px; margin:0 auto }
.sidebar ul #liBindings img { width: 42px; margin:0 auto }
.sidebar ul #liPublish img { width: 35px; margin:0 auto }
.sidebar ul #liPreview img { width: 40px; margin:0 auto }
.sidebar ul #liSaveFB img { width: 40px; margin:0 auto }
.binding_views ul li .single_view_wrapper{border: solid 3px #000000; width:55px; /*height:40px*/} /*PK ST-1556*/
.binding_views ul li .single_view_wrapper .single_view_left{height: 34px; width:30%}
.binding_views ul li .single_view_wrapper .single_view_right{height: 34px; width:70%;padding: 10px 5px;}
.binding_views ul li .single_view_wrapper .single_view_right ul li{ background:#fff}
.binding_views ul li .grid_view_wrapper{width:55px; height:40px;padding: 8px 5px;} /*PK ST-1556*/
.binding_views ul li p{width:65px;height:12px;} /*PK ST-1556*/
.permanentchild .ancactive{ background:#fff!important; color:#57307a!important}
.permanentchild a.frame_btn{ position:relative;color:#57307a!important;border: solid 1px #57307a;font-family: 'GillSans-SemiBold';font-size: 13px; line-height:25px; padding:0px;border-radius: 38px; height:25px}
.permanentchild a.frame_btn img{ position:absolute; top:0px; width:13px} /*PK ST-1556*/
.div-SideBar-btns{ width:95%; margin-left:-4px;margin-right:-7px;margin-top:26px;margin-bottom:20px;} /*PK ST-1556*/
.frame_editor ul li a.list_icon i{ width:11px; height:7px; font-size: 11px;}/*RP ST-1627*/
.modal.renameflip .modal-dialog{width: 288px; height:208px;}
.modal.renameflip h1{ text-align:center; padding-left:0px;font-family: 'GillSans';font-size: 18px;color: #4a4a4a; margin-bottom:10px; margin-top:0px}
.modal.renameflip h1 img{ display:block; position:relative; margin:0 auto 10px; left:auto; top:auto; width:61px}
.modal.renameflip .new_flip_name { width:245px; margin-top:0px}
.modal.renameflip .new_flip_name .form-control{width:245px;font-family: 'GillSans';font-size: 18px;color: #000000;background:#fafafa;}
.modal.renameflip .submit_flip{ width:93px; height:30px; line-height:30px; font-family:'GillSans-SemiBold'; color:#410260;border:solid 1px #410260; padding:0px; text-align:center;   font-size: 14px; border-radius: 20px; background:#fff}
.modal.renameflip .submit_flip:hover{ color:#fff; background:#6B448D}
.modal.renameflip .renameflip_close{ position:absolute; top:12px; right:15px}
.modal.renameflip .modal-dialog .modal-body{background:#fafafa; position:relative}
@media screen and (min-width: 1601px) {
    .permanentchild a.frame_btn {font-size: 11px }
}
.color_picker_modal button.close{color: #aaa;float: right;font-size: 26px !important;cursor:pointer; opacity:1;font-family: 'GillSans-Light';}
/*ST-1377, 2018-09-28   END VR*/
.end_paper_lib .color_wrapper span img { width: 30px; height: 30px; }
.selecting_endpaper.pageview{width:183px}
.selecting_endpaper.pageview .uploadendpaper .endpaper_listing{ padding-left:21.5%}
.selecting_endpaper.pageview .uploads_btn a{font-size: 13px; width:160px; padding:0px;height: 30px;line-height:30px;}
.selecting_endpaper.endpapergridbar{width:190px}
.frame_body{width: 79.1%;}/*ST-1693*/
.end_paper_lib .by_style ul li label{ font-size:15px}
.responsive1.slick-initialized .slick-slide{width: 196px !important;}/*ST-1320 , Fix*/
ul.responsive1{width: 900px;}
.image_gallery ul.responsive1 li{margin: 0 14px;}/*ST-1693*/
.end_paper_lib .color_wrapper span{ width:30px; height:30px}
.end_paper_lib .by_color{  margin-bottom:25px !important}
.end_paper_lib .by_style{margin-top: 20px;}
.img_library.endpaper_img_lib .container-fluid{ width:100%; margin-left:0px} /*ST-1692*/
.end_paper_lib .uploadbtnmargin{ margin-top:112px; margin-bottom:95px}
.divUploadText {font-size: 12px;font-family:GillSans-Light; text-align: center;font-weight: 300;color: #000000; position:absolute;top:46px;left:26px;}
.ui-button .ui-icon{ width:20px; height:20px}
.ui-dialog .ui-dialog-titlebar-close{ right:20px !important;}
.ui-button.ui-dialog-titlebar-close .ui-icon {background-image: url(/images/btn-close-20.png) !important;}
.color_picker_modal button.close,.closeIEPopUpMain {background-image: url(/images/close_black_icon.png) !important; width:15px; height:15px; background-repeat:no-repeat; color:transparent!important;}
.color_picker_modal button.close span{ display:none}
@media screen and (max-width: 1281px) {.permanentchild a.frame_btn{ font-size:12px }.permanentchild a.frame_btn img{width:12px}}
#PageZoomPopUp .close-PagePopUp {position: absolute;right: 52px;top: 20px;}
#PageZoomPopUp .modal-content-PagePopUp{width: 1240px;height: 925px;left: 53%;}
#divPopUp-ContentParent {height:650px;margin-top: 70px;}
/*ST-1408, 2018-10-02   START VR*/
.scrollbar-addpage > .scroll-element,.scrollbar-addpage > .scroll-element div {border: none;margin: 0;overflow: hidden;padding: 0;position: absolute;z-index: 10;}
.scrollbar-addpage > .scroll-element {background-color: transparent;}
.scrollbar-addpage > .scroll-element div {display: block;height: 100%;left: 0;top: 0;width: 100%;}
.scrollbar-addpage > .scroll-element .scroll-element_size {background-color: #BBBBBB;background-color: rgba(0, 0, 0, 0.15);}
.scrollbar-addpage > .scroll-element .scroll-element_outer:hover .scroll-element_size {background-color: #666;background-color: rgba(0, 0, 0, 0.5);}
.scrollbar-addpage > .scroll-element.scroll-x {bottom: 0;height: 12px;left: 0;min-width: 100%;padding: 3px 0 2px;width: 100%;}
.scrollbar-addpage > .scroll-element.scroll-y {height: 100%;min-height: 100%;padding: 0 2px 0 3px;right: 0;top: 0;width: 27px;}
.scrollbar-addpage > .scroll-element .scroll-bar {background-color: #550b90;-webkit-border-radius: 27px;-moz-border-radius: 27px;border-radius: 27px;box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);}
.scrollbar-addpage > .scroll-element .scroll-element_outer:hover .scroll-bar {box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);}
.scrollbar-addpage > .scroll-content.scroll-scrolly_visible {left: -17px;margin-left: 17px;}
.scrollbar-addpage > .scroll-content.scroll-scrollx_visible {margin-top: 17px;top: -17px;}
.scrollbar-addpage > .scroll-element.scroll-x .scroll-bar {height: 10px;min-width: 10px;top: 1px;}
.scrollbar-addpage > .scroll-element.scroll-y .scroll-bar {left: 1px;min-height: 27px;width: 27px; height:27px !important}
.scrollbar-addpage > .scroll-element.scroll-x .scroll-element_outer {height: 15px;left: 5px;}
.scrollbar-addpage > .scroll-element.scroll-x .scroll-element_size {height: 2px;left: -10px;top: 5px;}
.scrollbar-addpage > .scroll-element.scroll-y .scroll-element_outer {top: 5px;width: 32px;}
.scrollbar-addpage > .scroll-element.scroll-y .scroll-element_size {left: 14px;top: -10px;width: 2px;}
.scrollbar-addpage > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size {left: -27px;}
.scrollbar-addpage > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size {top: -27px;}
.scrollbar-addpage > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_track {left: -27px;}
.scrollbar-addpage > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_track {top: -27px;}

.add_page_flip .modal-dialog.modal-lg{ width:1277px}
.add_page_flip .double_spreads_wrapper ul li {width: 262px;margin-right: 29px;}/*ST-1657 */
.add_page_flip .double_spreads_wrapper ul li img{ max-width:100%}
.double_spreads_wrapper ul li.insertpagebtn{padding-top: 22px;}
.double_spreads_wrapper ul li.insertpagebtn img{ width:95px}
.single_spread_wrapper ul li.insertpagebtn img{ width:95px}
.addapagediv{height:820px ;}
.add_page_flip .modal-content .modal-body{ padding-left:20px; padding-right:20px}
.addapagediv .addpageinner{ width:97%; margin:0 auto}
.add_page_flip .single_spread_wrapper ul li {width: 132px;margin-right: 35px !important;}/*ST-1657 */
.add_page_flip .single_spread_wrapper ul li:first-child{ margin-right:0px !important; width:132px;}
.add_page_flip .single_spread_wrapper ul li:nth-child(2){ margin-left:-2px}
.modal-backdrop { opacity: .7;}.add_page_flip .single_spread_wrapper{ position:relative;}.add_page_flip .single_spread_wrapper .dragtext{ position:absolute; font-family:'GillSans-SemiBold'; color:#63388a; font-size:15px; line-height:18px; text-transform:uppercase;z-index: 99;left: 90px;top: 50px; background:#fff}/*RP ST-1657*/
                                                                                         .add_page_flip .single_spread_wrapper ul li img{ width:auto;max-width:100%;}
.singletok{ background:url('/images/singlet-ok.png') no-repeat; width:95px; height:27px; border:0px; margin:5px auto}
.singletok:hover{background:url('/images/singlet-ok-hover.png') no-repeat;}
/*ST-1408, 2018-10-02   END VR*/

/*ST-1412 - 680 2018-10-04 START VR, ST-1677*/ 

.drag_drop_img.list-grid .binding_views ul li .lib_view_wrapper{ background:url(/images/FBIcons/library_deactive.png) no-repeat;  width:67px; height:47px; border:0px; padding:0}/*RP ST-1833*/
.drag_drop_img.list-grid .binding_views ul li .lib_view_wrapper:hover{ background:url(/images/FBIcons/library_hover.png) no-repeat; }/*RP ST-1833*/
.drag_drop_img.list-grid .binding_views ul li .lib_view_wrapper span{ display:none}
.drag_drop_img.list-grid .binding_views ul li.active .lib_view_wrapper{background:url(/images/FBIcons/library_active.png) no-repeat;width:67px; height:47px; border:0px; padding:0}/*RP ST-1833*/
.drag_drop_img.list-grid .binding_views ul li .drag_drop_wrapper{ background:url(/images/FBIcons/line_deactive.png) no-repeat;  width:67px; height:47px; border:0px; padding:0; margin:auto;margin-left: 2px;}/*RP ST-1833*/
.drag_drop_img.list-grid .binding_views ul li .drag_drop_wrapper:hover{ background:url(/images/FBIcons/line_HOVER.png) no-repeat;}/*RP ST-1833*/

.drag_drop_img.list-grid .binding_views ul li .drag_drop_wrapper div{ display:none}
.drag_drop_img.list-grid .binding_views ul li.active .drag_drop_wrapper{background:url(/images/FBIcons/line_Active.png) no-repeat;width:67px; height:47px; border:0px; padding:0}/*RP ST-1833*/
.drag_drop_img .binding_views ul li:last-child{width:80px; text-align: center;}
.drag_drop_img.list-grid .binding_views ul li p{ font-family:'GillSans-SemiBold'; font-size:12px; width:100%;margin-left: -8px;}/*ST-1556,BV*/
.drag_drop_img .binding_views ul li:first-child{ margin-left:30px}
.pick_your_binding.frame_editor{ min-height:1070px}
 /*ST-1412 - 680 2018-10-04 END VR*/

 /*ST-1412 - 682 2018-10-05 START VR*/ 
#divImageSelecterPopUp .noimgbullpen{ position:absolute; top:0px;left:40%;   font-family: 'GillSans-Light'; font-size:18px;color: #000000; border:solid 1px #130d0d; line-height:26px; padding-left:8px; padding-right:8px}
.div-ChossAnImg-NoImage{ height:auto!important}
.choosean_image .img_pop_gallery_wrapper #divImageUpload { width: 208px !important; border: 2px solid #9b9b9b; height:149px!important;  }/*RP ST-1796*/
.choosean_image .img_pop_gallery_wrapper #divImageUpload .nohover{ display:block}
.choosean_image .img_pop_gallery_wrapper #divImageUpload .hover{ display:none}
.choosean_image .img_pop_gallery_wrapper #divImageUpload .divUploadText{display:none; bottom:3px; top:auto; left:32px}
.choosean_image .img_pop_gallery_wrapper #divImageUpload:hover{border: 2px solid #4a90e2;}
.choosean_image .img_pop_gallery_wrapper #divImageUpload:hover .nohover{ display:none}
.choosean_image .img_pop_gallery_wrapper #divImageUpload:hover .hover{ display:block}
.choosean_image .img_pop_gallery_wrapper #divImageUpload:hover .divUploadText{display:block}

.ImageGalleryScroll .sb-content .clearfix .Img-MULMainImgdiv #divImgUploadFromImage{ width: 100% !important; border: 2px solid #9b9b9b; height:179px!important;  }
.ImageGalleryScroll .sb-content .clearfix .Img-MULMainImgdiv #divImgUploadFromImage .nohover{ display:block; width: 100%; height: 174px;}
.ImageGalleryScroll .sb-content .clearfix .Img-MULMainImgdiv #divImgUploadFromImage .hover{ display:none; width: 100%; height: 174px;}
.ImageGalleryScroll .sb-content .clearfix .Img-MULMainImgdiv #divImgUploadFromImage .divUploadText{display:none; top: 158px;float: left;left: 50%;transform: translate(-50%, -50%);width: 100%;}
.ImageGalleryScroll .sb-content .clearfix .Img-MULMainImgdiv #divImgUploadFromImage:hover{border: 2px solid #4a90e2;}
.ImageGalleryScroll .sb-content .clearfix .Img-MULMainImgdiv #divImgUploadFromImage:hover .nohover{ display:none}
.ImageGalleryScroll .sb-content .clearfix .Img-MULMainImgdiv #divImgUploadFromImage:hover .hover{ display:block}
.ImageGalleryScroll .sb-content .clearfix .Img-MULMainImgdiv #divImgUploadFromImage:hover .divUploadText{display:block}
 
.choosean_image .img_pop_gallery_wrapper .img_pop_gallery { width:207px; position:relative;text-align:center;overflow:hidden;}/*RP ST-1796*/
.choosean_image .img_pop_gallery_wrapper .img_pop_gallery img{ width:auto!important; height:157px !important;margin:0 auto;max-width:203px;}/*RP ST-1796*/
.choosean_image .img_pop_gallery_wrapper #divImageUpload img{ width:204px!important; height:156px !important}
.choosean_image .img_pop_gallery_wrapper .img_pop_gallery .gallery_btn span { width:17px !important; height:17px !important}/*ST-1891*/
.choosean_image .img_pop_gallery_wrapper .img_pop_gallery .gallery_btn span img { width:17px !important; height:17px !important; margin:1px !important;}/*ST-1891*/
#divImageSelecterPopUp .img_library h3{ line-height:20px; margin-bottom:26px; margin-top:0px}
#divImageSelecterPopUp .responsive{ margin-bottom:41px}
#divImageSelecterPopUp .responsive .gallery_btn span { width:16px !important; height:16px !important}
#divImageSelecterPopUp .responsive .gallery_btn span img { width:16px !important; height:16px !important}
#divImageSelecterPopUp.ui-dialog-content .container{ width:97% !important}
.ImageGalleryScroll .gallery_btn span { width: 17px !important; height: 17px !important; }/*, 1554 PK,01/21/2018*//*RP ST-1627*/
.ImageGalleryScroll .gallery_btn span img {  width: 17px !important; height: 17px !important;}/*RP ST-1627*/
.leftSideWhitebox, .rightSideWhitebox { height: 17px !important; width: 17px !important; text-align: center; opacity: 1; position: absolute; right: 2px;top: 0px;}/*RP ST-1627*/
.leftSideWhitebox img {margin-left: -8px !important;}/*RP ST-1627*/
.leftSideWhitebox img, .rightSideWhitebox img { width: 17px !important;height: 17px !important}/*RP ST-1627*/
.EndPaperDeleteBtn.gallery_btn span {  width: 16px !important;height: 16px !important; display: inline-block; cursor: pointer; margin-right: -6px; text-align: center;  top: 0; position: absolute; right: 0;}/*RP ST-1627*/
.EndPaperDeleteBtn.gallery_btn p {  width: 20px !important;height: 20px !important; display: inline-block; cursor: pointer; margin-right: 35px; text-align: center; margin-top: 40px; position: absolute; right: 0;}/*RP ST-1789*/
.EndPaperDeleteBtn.gallery_btn p span {  margin-right: 32px;}/*RP ST-1789*/
.EndPaperDeleteBtn.gallery_btn span img { width: 20px !important; height: 20px !important;}/*RP ST-1627*/
#ImgPopMainBullPen .slick-initialized .slick-slide {width: 208px !important;height: 160px !important;border: 1px solid black;}
#ImgPopMainBullPen .slick-initialized .slick-slide img{height: 157px;margin: 0 auto ;width: auto ;max-height: 157px ;max-width: 202px;}/*RP ST-1796*//*width: auto !important;, ST-1891 removed !important; s*/
#divImageSelecterPopUp .image_gallery .responsive .slick-arrow.slick-prev{    left: -35px !important;}
#divImageSelecterPopUp .image_gallery .responsive .slick-arrow.slick-next{    right: -35px !important;}
#divImageSelecterPopUp .image_gallery .responsive .slick-arrow{ height:125px !important; width:43px !important}
/*ST-1412 - 682 2018-10-05 END VR*/
.borderdendpaperImgtoolbar {height: 32px; width:32px;}
.borderdendpapertoolbar{    height: 32px;  width: 32px; float: left;    margin-right: 10px;}
.ui-droppable-active .cross_close img {border:none;}
#btnViewImageInUse {padding-top: 6px;padding-bottom: 7px;}

/*ST-1396 AMIT*/
.publishWaringPopup .modal-dialog { width: 800px; }
.We-are-missing-your { max-width: 375px; font-family: GillSans; font-size: 20px; font-weight: normal; font-style: normal; font-stretch: normal; line-height: normal; letter-spacing: normal; text-align: center; margin: 0 auto; color: #410166;padding-top:12px;}/*RP ST-1869*/
.Do-you-want-to-enter { max-width: 309px; font-family: GillSans; font-size: 20px; font-weight: normal; font-style: normal; font-stretch: normal; line-height: normal;letter-spacing: normal; text-align: center; color: #410166; margin:23px auto;}
.number_popup { max-width: 509px; border: solid 2px #410166; background-color: #fafafa;}
.popup_btns_yes { width: 161.5px; height: 32.5px; border-radius: 38px; border: solid 1px #57307a; background-color: #ffffff; color: #410166; transition:ease-in-out .5s; -webkit-transition:ease-in-out .5s; moz-transition:ease-in-out .5s; font-family: gill_sanssemibold !important; font-size: 14px !important; padding: 8px; display: inline-block;}
.popup_btns_yes:hover { background: #57307a; color:#fff; text-decoration:none;cursor:pointer;}
.number_popup .modal-content { border: none !important; border-radius: 0px !important; background-color: #fafafa !important}


.colorPickerPane {z-index: 999;}
/*ST-1428*/
.spnImageFlipbookName {color: #410166;}
/*ST-1404,ST-1681*/
#ulSideBar .highlight-onDrag .cross_close img {border:none !important;}
#ulSideBar .ui-droppable-active img {outline:2px solid #01e8ff !important;} /*ST-1679#4*/
#ulSideBar .ui-droppable-active {background-color:transparent;} 
#ulSideBar .ui-droppable-active {border:#D8D8D8;} 
#ulSideBar .ui-droppable-active .cross_close img {border:none !important;outline:0px solid #01e8ff !important;} 
#ulSideBar .highlight-onDrag { border: none !important; } /*Hide border*/
#ulSideBar .highlight-onDrag .clsActive img{ outline: 3px solid  #01e8ff !important; z-index: 99; }
#btnImgInUseDone {margin-right:50px;}

/*ST-1396 AC*/
.btns_row a:first-child{
	margin-right:32px;
}

/*ST-1433 2018-10-17 VR START*/
@media screen and (max-width: 1361px) {
    .new_flip_main_wrapper { width: 93%; padding-left:3%;}
}
@media screen and (max-width: 1281px) {
    .new_flip_main_wrapper { width: 92% ; padding-left:5%;}
}
@media screen and (max-width: 1201px) {
    .new_flip_main_wrapper{ width:90%}
}
@media screen and (max-width: 991px) {
    .new_flip_main_wrapper{ width:80%}
}
/*ST-1433 2018-10-17 VR END*/


/* ST-1415 NY*/
.modal_custom {width: 462px !important;height: 626px;border: solid 2px #410166;background-color: #fff !important;}
.modal_visible {    overflow: visible !important;    top: 31% !important;}
.logo {  width: 84px;height: 84px;transform: rotate(13deg);}
.logo_img {width: 100%;text-align: center;}
.logo_img img {display: inline-block;}
.feedback_title {width: 100%;font-size: 20px;font-weight: 900;font-style: normal;font-stretch: normal;line-height: normal;letter-spacing: normal;text-align: center;color: #000000;font-family: 'AvenirLTStd-Book' !important;}
.modal_visible .closenew {margin: -2px -2px 0px 0px !important;}
.modal_custom label {font-size: 20px;font-weight: 600;font-style: normal;font-stretch: normal;line-height: normal;letter-spacing: normal;color: #000000;font-family: 'AvenirLTStd-Book' !important;}
.modal_custom .form-custom {border-bottom: solid 1px #000 !important;box-shadow: none !important;border: none;border-radius: 0px;height: 24px;padding: 0px 2px !important;font-size: 20px;}
.modal_custom .form-group {margin-bottom: 40px !important; /*ST-1869 VR 07/June/2019*/}
.modal_custom .modal-content {box-shadow: none ;border: none;}
.modal_custom .form-group textarea {width: 100%;height: 141px;border: solid 1px #979797;background-color: #ffffff;resize: none !important;/*margin-top: 12px;*/ /*RP ST-1726*/ /*ST-1869 VR 07/June/2019*/font-family: 'AvenirLTStd-Book' !important;}
.quip_btn {float: left;width: 100%;text-align: center;}
.quip_btn a {width: 138px;height: 27px;border-radius: 20px;border: solid 1px #57307a;background-color: #ffffff;cursor: pointer;padding: 5px 40px;color: #410166;text-transform: uppercase;display: inline-block;text-decoration: none;line-height: 15px;font-family: 'AvenirLTStd-Book' !important;font-weight:900;margin-top:15px; /*BV,ST-1685,2019 04 09*/}
.quip_btn a:hover {text-decoration: none;}
.modal_custom .form-custom {color: #000000; font-family: GillSans;font-size: 20px;}
.overlay_new {background: #FFFFFF;opacity:0.2 !important;/*RP ST-1869*/width: 100%;position: fixed;height: 100%;bottom: 0;z-index: 1001;display: none;}
/*ST-1281 AC*/
div.modal-dialog .close.closenew {height: 15px; /*RP ST-1674*/width: 15px; /*RP ST-1674*//*opacity: 0.5 !important;*/ /*RP ST-1674*/}
/*ST-1281 AC change for dropdown font/size*/
div.modal-dialog .dropdown_option .multiselect-native-select .multiselect.dropdown-toggle.btn {font-family: gill_sanssemibold;font-size: 16px;color: #000000;}
div.modal-dialog .dropdown_option .multiselect-native-select .multiselect-container .dropdown-menu {width: 100%;}
.sendemail_transition .field.new_flip_name .textfield {height: 26px;/*RP ST-1674*/padding: 6px 0px;margin: 0;}
.sendemail_transition .field.new_flip_name {width: 100%;margin-bottom: 25px;}
input#txtURL {height: 36px;}
/*Fix for send mail popup backoverlay*/
.modal-backdrop.in {display: none;}

.sendemail_transition textarea#txtFeedbackComments {resize: none;height: 120px;line-height: initial;background-color:#ffffff;}

div#dvSaveFeedback .modal-body {border-width: 0px 1px 2px 1px;border-style: solid;border-color: #410166;}   
/*style changes 10/31/2018*/
div#dvSaveFeedback .modal-body .form-custom {background: none;}
div#dvSaveFeedback .modal-body {background: #fafafa;}
div#dvSaveFeedback .modal-body {border: 2px solid #410166;}
div#dvSaveFeedback .modal-dialog.modal_custom {border: none;}
div#dvSaveFeedback .modal-dialog .pagereference { /*RP ST-1674*/ /*ST-1869 VR 07/June/2019*/font-family: "GillSans";font-size: 10px;font-weight: 600;position: absolute;bottom: 10px;color: lightgray;width: 150%;top: 115px;display: block;margin: 0 auto;left: -10px;right: 0px;}
/*ST-1433 2018-10-17 VR END*/

/*ST-1400 2018-10-22 VR START*/
#dvDeleteMultipleFlipBook .btn-group{ position:relative;border-color:#000000}
#dvDeleteMultipleFlipBook .btn-group .caret {border: 0px !important;background: url(/images/delete-flipbook-arrow.png) no-repeat; position:absolute; right:10px; width:9px; height:22px; }
#dvDeleteMultipleFlipBook .btn-group .dropdown-menu{ top:-20px; width:100%; min-width: 100%; border: 1px solid rgba(0,0,0,1);}
#dvDeleteMultipleFlipBook .multiselect-container input + span:before {content: '';background-color: transparent;background-repeat: no-repeat;background-size: 15px;display: block;width: 15px;height: 15px;border: solid 1px #979797}
#dvDeleteMultipleFlipBook .multiselect-container span {position: absolute;left: 10px;top: 10px;}
#dvDeleteMultipleFlipBook .multiselect-container input {display: none;}
#dvDeleteMultipleFlipBook .checkbox input:checked + span:before {background-image: url("/images/delete-flipbook-cross.png");border: solid 1px transparent;height: 21px;}
#dvDeleteMultipleFlipBook .dropdown-menu > .active > a{ background:#fff; color:#000000}
/*ST-1400 2018-10-22 VR END*/

/*ST-1318 PK , 10/22/2018 Starts*/
.Your-URL-username-of {width: 215px;height: 32px;font-family: 'GillSans-SemiBold';font-size: 12px;font-weight: normal;font-style: normal;font-stretch: normal;line-height: normal;letter-spacing: normal;text-align: center;color: #000000;position: absolute;top: 62%;left: 50%;transform: translate(-50%, -50%);-webkit-transform: translate(-50%, -50%);-ms-transform: translate(-50%, -50%);-moz-transform: translate(-50%, -50%);-o-transform: translate(-50%, -50%);padding: 6px;}
.Your-URL-username-of .text-style-2 {font-family: GillSans;color: #4a90e2;}
.UserNameRow {width: 136px;height: 28px;font-family: 'GillSans-Light';font-size: 25px;font-weight: 300;font-style: normal;font-stretch: normal;line-height: normal;letter-spacing: normal;text-align: center;color: #888888;}
.PhoneNumberRow {width: 90px;height: 14px;font-family: GillSans;font-size: 12px;font-weight: normal;font-style: normal;font-stretch: normal;line-height: 20px;letter-spacing: normal;color: #888888;}
.PhoneNumberRow p{width: 90px;height: 14px;font-family: GillSans;font-size: 12px;font-weight: normal;font-style: normal;font-stretch: normal;line-height: 20px;letter-spacing: normal;color: #888888;}
.EmailRow  {width: 142px;height: 14px;font-family: GillSans;font-size: 12px;font-weight: normal;font-style: normal;font-stretch: normal;line-height: 13px !important;letter-spacing: normal;color: #888888;padding-top:1px;}/*RP ST-1814*/
.EmailRow p {width: 142px;height: 14px;font-family: GillSans;font-size: 12px;font-weight: normal;font-style: normal;font-stretch: normal;line-height: 14px !important;letter-spacing: normal;color: #888888;}
.divcustomURLRow {margin: 0 ;cursor: pointer;height: 14px;padding-top: 0px; font-family: GillSans;font-size: 12px;font-weight: normal;font-style: normal;font-stretch: normal;line-height: normal;letter-spacing: normal;color: #888888;text-align: center;}
.customURLRow p {padding-top: 4px;margin: 0 auto;}
.ApplyHoverUnderLine:hover {border-bottom: solid 1px #4a90e2;padding-bottom:25px}
.ApplyHoverUnderLine2:hover {border-bottom: solid 1px #4a90e2;padding-bottom:15px}
/*ST-1318 PK, 10/22/2018 Ends */

.div-SideBar-btns {position: static;width: 150px}
.sticky {position: fixed;bottom: 100px;transition: top 1s linear;}
/*ST-1474*/

.divBindingSingleCovMsg .modal-content {width:365px;}

/*ST-1407, ST-1490,BV,2019 01 04 make it border.*/
.flpbook_achievemnet:hover {border: 3px solid #d5b9ed !important;}

.cke_editable.ApplyHoverEffect:where(:hover, :focus) {border: 3px solid #d5b9ed  !important;}
.cke_editable.ApplyHoverEffect {border: 3px solid transparent;} /*ST-2025*/

.cke_editable{padding:0px 2px 2px 2px}/*ST-2050*/
#inline94  .ApplyHoverEffect:hover {border: 3px solid #d5b9ed !important;border-left:none!important}
.flpbook_achievemnet:hover.fullBorderForRW {/*outline: 0px solid #c6deff;*/ }

/*ST-1405 AC*/
/*ST-2041*/
#dvDBPublishShare .book_block { text-align: center; perspective: 900px; perspective-origin: 50% 50%; left:83px;top:-100px;/*RP ST-1834*/}/*ST-2041*/
@media (min-width: 1450px) and (max-width: 1600px) { #dvDBPublishShare .book_block{left:84px;}}/*RP ST-1834*/
@media (min-width: 1152px) and (max-width: 1440px) {#dvDBPublishShare .book_block{left:64px;}}/*RP ST-1834*/
#dvDBPublishShare .share_wrapper h2.hardcopies{margin-right:111px}/*RP ST-1834 */
#dvDBPublishShare .share_wrapper a.order_flip{    margin-left: 154px;}/*RP ST-1834*/
#dvDBPublishShare .sharefbBetaicon{left:157px}/*RP ST-1834*/
/*#dvDBPublishShare .book_block img {transform: rotateX(45deg) rotateY(0deg) rotateZ(331deg) translateX(0px) translateY(0px) translateZ(0px);max-width: 320px;}*/
#dvDBPublishShare .book_block img {transform: rotateX(40deg) rotateY(0deg) rotateZ(334deg) translateX(0px) translateY(0px) translateZ(0px);max-width: 410px;box-shadow: -5px 5px 10px grey;}

/* ST-1441 , PK , 10/28/2018 , Related to 'TMI We are out of Space' */
.tmi_trianglebox {position: absolute;z-index: 9999;top: 206px;left: 20px; background:url(/images/tmi-bg.png) no-repeat; width:149px; height:148px}
.tmi_trianglebox .inner-triangle {position: relative;}
.tmi_trianglebox .inner-triangle .red_text {width: 61px;font-family: 'GillSans';font-size: 13px;text-align: center;color: #000000;font-weight: normal;font-style: normal;font-stretch: normal;line-height: 13px;letter-spacing: normal; margin:72px auto 0px}
.tmi_trianglebox .red_ex{ left: 61px;top: -35px;position: absolute;}
.tmi_trianglebox .red_close{position:absolute; left:70px; bottom:-25px;}

/* NY, ST-1423*/
.save_popup .modal-dialog {width: 272px !important;}
.save_popup .modal-body {padding: 28px 30px 30px 30px !important;}
.save_popup .modal-content {}
.wrapper_save_popup {width: 240px;display: inline-block;position: relative;left: 91px;}
.check {width: 0;overflow: hidden;display: inline-block;opacity: 0;/*MB, #1500, 1/21/19*/animation: show 0s ease-out 0s alternate;-webkit-animation: show 1s ease-out 0s alternate;animation-fill-mode: forwards;/*font-size: 90px;font-style: italic;font-weight:bold;*/position: absolute;color: green;text-align: left;top: -4px;left: 0;height: 61px;}
.divSuccAutoCloseMsg .p-t-22 {padding-top: 35px !important;margin: 35px 0 0 !important;}
@keyframes show {
    0% {width: 0;opacity: 0;}
    100% {width: 60px;opacity: 1;}
}

@-webkit-keyframes show {
    0% {width: 0;opacity: 0;}
    100% {width: 60px;opacity: 1;}
}

/*ST-1481 , ST-1354 , ST-1354.1,VR*/
.cke_combo__lineheight .cke_combo_arrow, .cke_combo__letterspacing .cke_combo_arrow {width: 13px !important;height: 9px !important;background-image: url(/images/editor-down.png) !important;background-size: 13px 9px !important;border: 0px !important;top: 8px !important;right: -10px !important;}
.cke_combo__lineheight a.cke_combo_button, .cke_combo__letterspacing a.cke_combo_button{ border-radius:9px !important}
.cke_combo__lineheight a.cke_combo_button:before {content:'';background: url(/images/editor-leading.png) no-repeat;width:9px;height:16px;position:absolute;top:7px;left:7px}
.cke_combo__lineheight a.cke_combo_button .cke_combo_text {position: absolute;padding-right: 0px !important;padding-left: 0px !important;width: 74px !important;left: 18px;line-height: 25px !important;text-align: center; color:#343534 !important}/*ST-1354.1*/
.cke_combo__lineheight a.cke_combo_button .cke_combo_open{ width:20px !important}
.cke_combo__lineheight .cke_combo_arrow{right: 0px !important;}
.cke_combo__letterspacing a.cke_combo_button:before {content: '';background: url(/images/editor-kerning.png) no-repeat;width: 16px;height: 7px;position: absolute;top: 11px;left: 6px}
.cke_combo__letterspacing a.cke_combo_button {width: 132px !important}
.cke_combo__letterspacing a.cke_combo_button .cke_combo_text{padding-left: 26px !important;min-width: 86px !important; text-align:center; color:#343534 !important; line-height:25px !important}/*ST-1354,BV,Words in DDL's not centered.*/
.cke_combo__letterspacing a.cke_combo_button .cke_combo_open{ width:20px !important}
.cke_combo__letterspacing .cke_combo_arrow{right: 0px !important;}
.cke_button__link .cke_button_label.cke_button__link_label {display: block !important;font-family: 'GillSans' !important;font-size: 13px !important;padding-left: 6px !important;margin-top: 0px !important;}
.cke_button__link .cke_button_icon {background-image: unset !important;background: #fff url(/images/editor-link.png) no-repeat !important;right: 4px !important;position: absolute !important; top:4px !important}/*ST-1354, Add space in Link*/
.cke_button__link.cke_button:last-child:after {display: none !important}
.cke_button__bold, .cke_button__italic, .cke_button__underline, .cke_button__justifyleft, .cke_button__justifycenter, .cke_button__justifyright { width:20px}
.cke_button__bold .cke_button__bold_icon {background: url(/images/editor-bold.png) no-repeat center !important;width: 15px !important;height: 14px;margin-left: 3px; margin-right: 3px; margin-top:6px}/*ST-1556*//*ST-1742 set width height icon*/
.cke_button__italic .cke_button__italic_icon {background: url(/images/editor-italic.png) no-repeat center !important;width: 15px !important;height: 14px;/*ST-1742 set italic icon*/margin-left: 3px; margin-right: 3px; margin-top:6px}/*ST-1556.1*/
.cke_button__underline .cke_button__underline_icon {background: url(/images/editor-underline.png) no-repeat center !important;width: 14px !important;height: 18px;margin-left: 3px; margin-right: 3px; margin-top:6px}/*ST-1556*/
.cke_button__justifyleft .cke_button__justifyleft_icon {background: url(/images/editor-left.png) no-repeat center !important;width: 18px !important;height: 19px;margin-left: 3px;margin-top: 3px;}/*ST-1556*/
.cke_button__justifycenter .cke_button__justifycenter_icon {background: url(/images/editor-center.png) no-repeat center !important;width: 18px !important;height: 19px;margin-left: 3px;margin-top: 3px;}/*ST-1556*/
.cke_button__justifyright .cke_button__justifyright_icon {background: url(/images/editor-right.png) no-repeat center !important;width: 18px !important;height: 19px;margin-left: 3px;margin-top: 3px;}/*ST-1556*/
.cke_combo__tokens a.cke_combo_button {background: url(/images/editor-opacity.png) no-repeat !important;width: 69px !important;height: 29px !important;border: 0px !important;}
.cke_combo__tokens .cke_combo_arrow {width: 13px !important;height: 9px !important;background-image: url(/images/editor-down.png) !important;background-size: 13px 9px !important;border: 0px !important;top: 12px !important;right: 5px !important;}
.cke_combo__tokens a.cke_combo_button .cke_combo_open {width: 15px !important;margin-right: 6px !important;}
.cke_combo__tokens .cke_combo_text {display: none !important}
.cke_combopanel{ margin-top:3px !important}
.cke_panel_grouptitle{ text-align:center}
.cke_button__textcolor{ width:48px !important; border-radius:0px 10px 10px 0px !important}
.cke_button__textcolor .cke_button__textcolor_icon{ margin:0px; position:absolute;width: 26px; height:24px; border: 1px solid #4d4d4c;margin-top: -1px;} /*ST-1621 Adjust Height to Match Other DDLs, PK ST-1621 */
.cke_toolbox span:nth-child(2) .cke_button_arrow {right: -26px !important;}

.cke_combopanel__letterspacing{ height:300px !important; width:106px !important; margin-left:12px !important}
.cke_combopanel__tokens{ width:69px !important}
.cke_combopanel__lineheight{ width:100px !important}

.cke_combopanel__fontsize{ width:250px !important}
.cke_combo__fontsize a.cke_combo_button .cke_combo_text{min-width: 35px !important;position: absolute;width: 46px !important;padding-left: 0px !important;height: 24px;line-height: 24px !important; color:#1a1919!important;font-family: GillSans !important;font-size: 13px !important;}/*ST-1354.1*/
.cke_combo__fontsize a.cke_combo_button{ width: 70px !important;}

.cke_combo__font a.cke_combo_button{ width:160px !important}
.cke_combo__font a.cke_combo_button .cke_combo_text {min-width: 132px !important;position: absolute;padding-left: 0px !important;width: 132px !important;line-height: 24px !important; color:#1a1919 !important}/*ST-1354.1*/
a.cke_button__link{width: 60px !important;border-radius: 10px !important;border: solid 0.7px #4d4d4c !important;background:#fff !important; height:16px!important;cursor:pointer !important; }/*ST-1822,BV- margin-left: 10px !important;*/
a.cke_button__link:hover{height:24px !important; background:#fff !important;padding: 0px !important;}/*ST-1354.1,VR*/
.cke_button__link .cke_button_label.cke_button__link_label{line-height: 25px !important; color:#343534 !important;cursor:pointer;}
.cke_combo__font .cke_combo_text {text-align:center;font-family: GillSans !important; font-size: 13px !important; } /*BV,2018 11 17 ST-1354, as per finding,BV2019 04 03 ,Words in DDL's not centered.*/
/*ST-1822*/
.cke_editable.fullBorderForRW.ApplyHoverEffect:hover,
.fullBorderForRW ,
.fullBorderForRW:hover ,
.fullBorderForRW:focus {border: 3px solid #d5b9ed !important;}
.PLI-Border-208.divBorderActive #inline15.fullBorderForRW{border: 3px solid transparent !important;}/*2020-02-26,VR*/
.PLI-Border-208.divBorderActive #inline15.ApplyHoverEffect:hover { border: 3px solid #c6deff !important;}/*2020-02-26,VR*/

.frederich_img1 p span{ margin-top:0px}
.frame_body .flip_first_heading .row{ left:0px !important; padding-left: 60px !important;}/*ST-1556,VR*/
.frame_body .flip_first_heading .floatleft{float: none; display: inline-block;}
.frame_body .flip_first_heading input[type=text]{ text-align:center;border-bottom:solid 3px #d5b9ed !important;}
.frederich_img1 .flpbook_achievemnet {width:98% !important}
/* ST-1369 VR 11/15/2018 Ends */
/* ST-1369 VR 11/15/2018 Ends */

/*ST-1397 AC 11/19/2018 start*/
.joinflipbookpopup {width: 428px;min-height: 745px;background: #fafafa;border: solid 2px #410166;margin: 0 auto;position: relative}
.joinflipbookpopup .fliplogo {margin: 30px auto 55px;text-align: center}
.joinflipbookpopup .closebtn {position: absolute;right: 13px;top: 13px}
.joinflipbookpopup .welcomenote {font-family: 'GillSans-Light';font-size: 44px;line-height: 50px;color: #410260;text-align: center}
.joinflipbookpopup .textdetail {font-family: 'GillSans-Light';font-size: 20px;line-height: 24px;color: #000;text-align: center;width: 340px;margin: 0 auto 60px}
.joinflipbookpopup .jfp-form {width: 302px;margin: 0 auto}
.joinflipbookpopup .jfp-textbox {position: relative;margin-bottom: 44px}
.joinflipbookpopup .jfp-astrick {background: url(/images/joinflipbook-astrik.png) no-repeat;position: absolute;width: 15px;height: 15px;top: 7px;left: 0px}
.joinflipbookpopup .jfp-input {outline: none;width: 280px;margin-left: 20px;border: 0px;line-height: 25px;border-bottom: solid 1px #410166;font-family: 'GillSans-Light';font-size: 18px;color: #7e7e7e;background: transparent}
.joinflipbookpopup .jfp-inpu:focus {border-color: inherit;-webkit-box-shadow: none;box-shadow: none;}
.joinflipbookpopup .jfp-passicon {background: url(/images/joinflipbook-password.png) no-repeat;position: absolute;width: 23px;height: 18px;top: 5px;right: 0px}
.joinflipbookpopup .jfp-textbox.lastbox {margin-bottom: 0px}
.joinflipbookpopup .textrecomended {font-family: 'gill_sanssemibold';font-size: 12px;line-height: 20px;color: #550b90;text-align: center;width: 340px;margin: 5px auto 18px}
.joinflipbookpopup .letsgobtn {margin: 0 auto;display: block;height: 32px;line-height: 32px;width: 161px;text-align: center;background: #fff;border: 1px solid #57307a;color: #410166 !important;border-radius: 20px;text-transform: uppercase;text-decoration: none;font-size: 13px;transition: all ease-out 0.5s;-webkit-transition: all ease-out 0.5s;-moz-transition: all ease-out 0.5s;font-family: 'gill_sanssemibold' !important}
.joinflipbookpopup .letsgobtn:hover {background-color: #57307a;color: #FFF !important;transition: all ease-in 0.5s;-webkit-transition: all ease-in 0.5s;-moz-transition: all ease-in 0.5s;}
div#errorMessageForGuestRegister {color: #c01f2a !important;display: none;}

div#guestRegistrationPopup .modal-body {    min-height: 745px !important;}
/*ST-1397 AC 11/19/2018 end*/

/*ST-1418 PK 11/21/2018 CSS For Cursor On Hover Starts */
.cursorDelete {cursor: url("/images/Cursor Trash Can.png"),auto !important;}
.cursorEdit { cursor: url("/images/Curson Edit Pencil.png"),auto !important; }
.cursorDrag { cursor: url("/images/Cursor Cross Arrow.png"),auto !important; }
.ui-drop-hover .cursorDrag { cursor: copy !important; } /*ST-1419*/
.cursorNone {cursor:unset;}
/*.cursorAdd {cursor: url("/images/frame_plus.png"),auto !important;}*/
.cursorAdd {cursor: cell !important;}
.cursorDupe {cursor: url("/images/frame_copy.png"),auto !important;}
.curosrZoomIn{cursor: zoom-in;}
.cursorHelp {cursor:help;}
.padding2 {padding:2px !important}
/*ST-1418 PK 11/21/2018 CSS For Cursor On Hover Ends */
/*ST-1369,VR ,BV*/
.frederich_img .right_sec {padding-left: 0px;padding-top: 0px;margin-top: 7px !important;position: absolute;right: 30px;width: 168px; height: 168px;float: right;margin: 0px;text-align: center;}
.ip_left{ margin-top:10px !important}
.int_res .divCkEdiMain p{ color:#c0c0c0;font-family:'Palatino'}
.int_res h3 p span{font-family: 'Avenir-Book' !important;}
.divTopHeadingLeft {width: 322px;}


/*ST-1512- 1438*/
.grid_page_manage .dash_board {width: 97.5%;}
.sidebar ul li.active:after, .sidebar ul li.active:before{border:0px}
.sidebar ul li.active:before{ background:url(/images/FlipBook/notch-icon.png) no-repeat; width:15px; height:40px; border:0px}
.sidebar ul #liBindings.active:before{ top:21px}
.sidebar ul #liFlipbooks.active:before{ top:31px}
.sidebar ul #liPages.active:before{ top:20px}
.sidebar ul #liImages.active:before{ top:20px}
.sidebar ul #liEndPaper.active:before{ top:20px}
.sidebar ul #liPublish.active:before{ top:23px}
.sidebar ul #liPreview.active:before{ top:20px}
.pick_your_binding ul.ulBindings{margin-left:3px; width:139px; margin-bottom:20px; padding-top:16px}/*ST-1766*/
/*ST-1487 PK 11/22/2018 Starts*/
.btnDoneEndPaper {font-family: 'AvenirLTStd-Book';display: inline-block;width: 86px;height: 28px;text-align: center;padding: 0;line-height: 27px;border: 1px solid #410166;border-radius: 14px; text-transform: uppercase;font-size: 15px;color: #410166;cursor: pointer;    margin-right: 15px;}
.btnDoneEndPaper:hover {color: #FFF;background-color: #6B448D;transition: all ease-in 0.5s;-webkit-transition: all ease-in 0.5s;-moz-transition: all ease-in 0.5s;text-decoration: none;}
.widthunset {width:100% !important;float:unset !important;}
/*ST-1487 PK 11/22/2018 Ends*/
.spanDummyText {color:red;}
/*ST-1369*/
.IsCkeDummyText p, .IsCkeDummyText p span, .IsCkeDummyText span{color :#afafaf; /*line-height:15px;display: inline; ST-1571,BV 2019 01 03 not show p tag spacing. */}
/*1751  remove color for text*/
.IsCkeUserText p{color :#000000; }
 
.bb-custom-side.frederich_img.frederich_img_Left, .bb-custom-side.frederich_img.frederich_img1{height:528px !important}
.bb-custom-side.frederich_img.frederich_img_Left .content_wrap, .bb-custom-side.frederich_img.frederich_img1 .content_wrap {height: 100%; width: 522px; overflow:hidden}
.bb-custom-side.frederich_img.frederich_img1 .content_wrap{padding-left:1.3rem;}/*RP ST-1731*/
.bb-custom-side.frederich_img1 .footer_url{ top:70px !important}
.bb-custom-side.frederich_img.frederich_img_Left .content_wrap .in_inner {width: 492px}
.bb-custom-side.frederich_img.frederich_img1 .content_wrap .in_inner {width: 503px}
.bb-custom-side.frederich_img.frederich_img_Left .top_heading_wrap, .bb-custom-side.frederich_img1 .top_heading_wrap {position: relative}
.bb-custom-side.frederich_img.frederich_img_Left .top_heading_wrap .idivPencil{ position:absolute; right:0px; z-index:9}
.bb-custom-side.frederich_img.frederich_img1 .top_heading_wrap .idivPencil{ position:absolute; right:0px; z-index:9}


#div74 .cke_editable {max-height:60px;}
#div82 .cke_editable{min-height:85px;}
#div93 .cke_editable {min-height:190px;}
#div92 .cke_editable{min-height:20px;}
#div192 .cke_editable {min-height:20px;}
.fullBorderForRW:hover .eraserForAllDivimg{display:block;}
/*ST-1427*/
.margin-bottom-44 {margin-bottom :44px;}
.cldivBullPenDrag {display:block !important;}
.clAntiBullPen .antiBullPen {display:none !important;}
/*ST-1388*/
.liUserEndPaper .gallery_btn span img,
.endpaper_listing .gallery_btn span img {width: 16px !important;height: 16px !important;}/*RP ST-1627*/
 .list_icon.hamburger img{height: 7px;width: 11px;}/*RP ST-1627*/
/* ST-1388 VR 01/12/2018 Start */
.uploadendpaper .endpaper_listing ul li{ width:100px; position:relative}
.endpaper_listing .gallery_btn img { width: 20px; height: 20px;position: absolute;top: 0px;right: 1px; /*right:-5px; background:rgba(255, 255, 255, 0.65)*/}/*RP ST-1627*/
.endpaper_listing .gallery_btn p img { width: 20px; height: 20px;position: absolute;top: 0px;right: 1px; }/*RP ST-1789*//*ST-1687*/
.selecting_endpaper .uploads_btn a span i {color: #417505;}
.selecting_endpaper .uploads_btn a span{ position:static; left:inherit; display:inline-block; padding-right:5px; font-size:13px; color:#57307a; font-family:GillSans-SemiBold;}
.selecting_endpaper .uploads_btn #aBtnUploadEndPaperGV{width:160px; height:30px;padding:7px 0;}
/*ST-1807*/
.noendpaperbtn .uploads_btn a{ padding-left:15px!important}
.noendpaperbtn .uploads_btn a span{ position:absolute; left:19px; display:inline-block; padding-right:5px} 
.scrollbar-rail > .scroll-element.scroll-y .scroll-bar {left: 1px;min-height: 18px;width: 18px; height:18px !important;}/*ST-1588*/

/* ST-1388 VR 01/12/2018 End */


/*ST-1455 PK 11/30/2018 */
.SubLayouts .last_slide_Left .last_slide_div1{width: 235px !important; height: 223px !important; position: absolute; left: 25px; top: 88px; background-color: #f3f3f4; text-align: center;}
.SubLayouts .last_slide_Left .last_slide_div2{width: 235px !important; height: 231px !important; position: absolute; left: 25px; bottom: 20px; }
.SubLayouts .last_slide_Left .last_slide_div3{width: 280px !important; height: 320px !important; position: absolute; right: 25px; top: 24px; }
.SubLayouts .last_slide_Left .last_slide_div4{width: 280px !important; height: 200px !important; position: absolute; right: 25px; bottom: 20px; }
/*ST-1453 PK 12/05/2018 */
.SubLayouts .sublayout12 .top_heading .heading_in p, .SubLayouts .sublayout14 .top_heading .heading_in p {width: 472px;height: auto;font-family: Avenir-Book !important;font-size: 21pt !important;font-weight: normal;font-style: normal;font-stretch: normal;line-height: 35px !important;letter-spacing: normal;color: #000000;}
.SubLayouts .sublayout12 .top_heading .heading_in, .SubLayouts .sublayout14 .top_heading .heading_in {border-bottom: solid 2px #000000;   }
.SubLayouts .cottage .georgica_pond {padding:36px 47px 43px 47px !important;}
.sublayout14 .ultwoimg li {width: 232px !important;}
.sublayout14 .ultwoimg li:first-child {margin-right:16px;}
.transparent3pxborder {border:solid 3px transparent;}
/*ST-1388*/

/*ST-1780*/
.EndPaperControl{margin-top:5px}
.EndPaperControl.EndPaperSingleView {margin-top:0px}/*ST-1692*/
.EndPaperControl .clr{ clear:both}
.EndPaperControl .noendp{ width:384px; float:left;font-family: 'GillSans'; font-size:15px; color:#000000; line-height:18px; position:relative;margin-top: 0px;}
.EndPaperControl .intropagezoomdiv{ width:384px; margin:0 auto; position:relative; float:left}
.EndPaperControl .intropagezoomdiv .intropagezoominner{ width:362px;margin:10px auto 0px; position:relative}
.EndPaperControl .intropagezoomdiv .intropagezoominner .intropagezoom{ width:362px !important}
.EndPaperControl .intropagezoomdiv .intropagezoomSM{ width:10px; height:10px; border:solid 1px #979797; position:absolute; left:-15px; top:-4px}
.EndPaperControl .intropagezoomdiv .intropagezoomLG{ width:19px; height:19px; border:solid 1px #979797; position:absolute; right:-25px; top:-9px}
.EndPaperControl .intropageview { float:right; width:164px}
.EndPaperControl .intropageviewinner{width:150px;height:135px; border:solid 0.5px #000000}
.EndPaperControl .intropageviewinner .itp-title{font-family: 'gill_sanssemibold'; font-size:20px; color:#000000; text-align:center; margin:5px auto 5px;}
.EndPaperControl .intropageviewinner .itp-list{ padding:5px 10px;font-family: 'GillSans'; font-size:20px; color:#000000;}
.EndPaperControl .itp-list input[type='radio'] { -webkit-appearance: none; width: 20px; height: 20px; border-radius: 50%; outline: none; border: 1px solid #a4a4a4; margin-right: 0px; vertical-align: middle; margin-bottom: 5px; cursor: pointer; background-color: #FFF; }
.EndPaperControl .itp-list input[type='radio']:before { content: ''; display: block; width: 14px; height: 14px; margin: 2px auto; border-radius: 50%; }
.EndPaperControl .itp-list input[type='radio']:checked:before { background: #472850; }
.EndPaperControl .noendp .checkdiv{ position:absolute; left:0px; }
.EndPaperControl .noendp .chkNoEndPaper {}
.EndPaperControl .noendp [type="checkbox"]:not(:checked),.EndPaperControl .noendp [type="checkbox"]:checked {position: absolute;left: -9999px;}
.EndPaperControl .noendp [type="checkbox"]:not(:checked) + label,.EndPaperControl .noendp [type="checkbox"]:checked + label { position: relative;overflow: hidden;padding-left: 25px;cursor: pointer;display: inline-block;height: 30px;line-height: 30px;-webkit-user-select: none; -moz-user-select: none; -khtml-user-select: none;-ms-user-select: none;}/*ST-1754*/
.EndPaperControl .noendp [type="checkbox"] + label:before,[type="checkbox"] + label:after {content: '';position: absolute;left: 0;z-index: 1;-webkit-transition: .2s;transition: .2s;}
.EndPaperControl .noendp [type="checkbox"]:not(:checked) + label:before {top: 6px;width: 19px; height: 19px;border: 1px solid #979797;}
.EndPaperControl .noendp [type="checkbox"]:not(:checked) + label:after {top: 6px;width: 19px; height: 19px;border: 1px solid #979797;z-index: 0;}
.EndPaperControl .noendp [type="checkbox"]:checked + label:before {top: -2px;left: 3px; background:url(/images/FBIcons/blue-check.png) no-repeat; width:25px; height:25px;z-index: 99;}/*ST-1754*/
.EndPaperControl .noendp [type="checkbox"]:checked + label:after {top: 6px;width: 19px; height: 19px;border: 1px solid #979797;z-index: 0;}/*ST-1754*/
.EndPaperControl .noendp [type="checkbox"]:disabled:not(:checked) + label:before,[type="checkbox"]:disabled:checked + label:before {top: 0;box-shadow: none;background-color: #444;width: 18px; height: 18px;border: 3px solid #444;-webkit-transform: rotateZ(0deg);transform: rotateZ(0deg);}
.EndPaperControl .noendp [type="checkbox"]:disabled + label {}
.EndPaperControl .noendp [type="checkbox"]:disabled:not(:checked) + label:hover:before {border-color: #979797;}
.EndPaperControl{ margin-left:65px; width:1153px !important}
#intropagezoom.ui-widget.ui-widget-content { border: 0 !important; height: 2px; background: #9b9b9b !important; position: relative; margin: -17px 10px 0 0; display: inline-block;  vertical-align: middle; }
#intropagezoom .ui-slider-handle { background: #000; border-radius: 50%; top: -7px; outline: none; width: 15px; height: 15px; box-shadow: 1px 2px 2px 0 rgba(0, 0, 0, 0.5);}
#intropagezoom .ui-slider-handle:focus { border-color: #000; }
#intropagezoom .ui-slider-handle.ui-state-active { border: 0; }
.EndPaperControl .intropageviewinner .fcv-title{font-family: 'GillSans-SemiBold'; font-size:17px; color:#000000; text-align:center; margin:9px auto 0px;}/*ST-1780,BV*/
.EndPaperControl .intropageviewinner .fcv-list{ padding: 0px 0px 0px 15px;font-family: 'GillSans-SemiBold'; font-size:17px; color:#000000;} /*ST-1780,BV*/
.divfcv-list {width: 132px;height: 2px;opacity: 0.5;margin: 5px auto 13px;background: #3c3c3c;}
.bordertool_radio input[type='radio'],
.EndPaperControl .fcv-list input[type='radio'] { -webkit-appearance: none; width: 18px; height: 18px; border-radius: 50%; outline: none; border: 1px solid #a4a4a4; margin-right: 0px; vertical-align: middle; margin-bottom: 5px; cursor: pointer; background-color: #FFF; position: relative; }/*ST-1780*/
.bordertool_radio input[type='radio'] {width: 20px; height: 20px; }/*ST-1835*/
.bordertool_radio input[type='radio']:before,
.EndPaperControl .fcv-list input[type='radio']:before { content: ''; width: 10px; height: 10px; margin: 0px auto; border-radius: 50%; position:absolute; left:3px; top:3px }/*ST-1780*/
.bordertool_radio input[type='radio']:before {width: 14px;height: 14px;left: 2px;top: 2px;}/*ST-1835*/
.bordertool_radio input[type='radio']:checked:before,
.EndPaperControl .fcv-list input[type='radio']:checked:before { background: #472850; }
.bordertool_radio span {font-family :GillSans-Light; font-size:18px; color:#000000; cursor:pointer;}/*ST-1835*/

.EndPaperControl.EndPaperSingleView{ position:relative}
.EndPaperControl.EndPaperSingleView .intropageview{position: absolute;top: -208px;right: 100px;}/*ST-1898*/
.EndPaperControl.EndPaperSingleView .noendp {width:365px}
.bb-custom-wrapper.NoEpBorderFC {outline:none;}/*ST-1793*/
.epPageNavigation {cursor:pointer;}
/*ST-1526*/
.divLayout1 .fred_img img {border:none;} /* Update Opacity to 0.51 from 1 , PK 12/18/2018 , ST-1502 */ /*ST-1597, BV, 2019 01 15, Removed opacity:0.51;*/
.divLayout1 .bb-custom-wrapper .bb-bookblock {width: 576px;height: 576px;}
.divLayout1 .bg_end_paper {height: 576px;}
.divLayout1 .fred_text {min-height:inherit}

/*ST-1565,BV,2019 01 30*/
.divImageTimeSpan .TimeSpan {text-align: center;margin-top: 3px;font-family: GillSansCE-Roman;font-size:12px;}/*ST-1582,BV 2019 02 12*/
.choosean_image .img_pop_gallery_wrapper .img_pop_gallery .spImageOnPage{ right:1px;}
.divImageOnPage {height:16px;}

/*ST-1492,Selecting full areas*/
ul.blank_img,li#aGoToImageLibrary {cursor:pointer;}
/*ST-1532 PK, ST-1554 PK 01/29/2019 , Update CSS To Match With Image Library  */
.close_icon_PlaceHolder {position: absolute;right: 0px;top: 0px; width: 17px;height: 17px; /*background-color: rgba(255, 255, 255, 0.6);*/ display: inline-block; /*opacity:0.6*/}/*RP ST-1627*//*background-color: rgba(255, 255, 255, 0.6);*/
.close_icon_PlaceHolder img { /*width: 15px !important;height: 15px !important;*/ width: 17px !important;height: 17px !important; cursor: pointer; /*margin-right: 0px;margin-left: 1px;margin-bottom: 1px;*/ text-align: center;}/*RP ST-1627*/
.close_icon_PlaceHolder a {top:0px !important}/*, 1554 PK,01/21/2018*/
/* ST-1535 PK 12/14/2018 */
.margintopmin10{margin-top:-10px}
.marginleftmin4{margin-left:-4px}
.borderbottom3px {border-bottom: solid 3px #c6deff;}
.flip_sample_wrapper li:not(:first-child) .flip_sample { outline:1px solid #000000 } /* ST-1539 PK 12/14/2018 */
/* ST-1438 PK 12/17/2018*/
.grid_page_manage {overflow-x:hidden !important}
.grid_page_manage .flip_first_heading{width:1154px}
.Yellow-Pencil {width: 22px;height: 22px;}

/*ST-1538 PK 12/20/2018 Starts*/
#rangeBorderSlider .ui-slider-handle{ background:url('/images/round-btn.png?V=3') no-repeat; width:20px; height:20px; box-shadow: 0px 0px 0px 0 rgba(0,0,0,.5); border:0px !important; top:-10px}/*ST-1631,VR*/
#rangeBorderSlider .ui-slider-handle:hover, #rangeBorderSlider .ui-slider-handle:focus { -webkit-transform: scale(1); -moz-transform: scale(1); -o-transform: scale(1); -ms-transform: scale(1); transform: scale(1); border:0px !important }
#intropagezoom .ui-slider-handle{ background:url('/images/round-btn.png?V=3') no-repeat; width:20px; height:20px; box-shadow: 0px 0px 0px 0 rgba(0,0,0,.5); border:0px !important; top:-10px}/*ST-1631,VR*/
#intropagezoom .ui-slider-handle:hover, #intropagezoom .ui-slider-handle:focus { -webkit-transform: scale(1); -moz-transform: scale(1); -o-transform: scale(1); -ms-transform: scale(1); transform: scale(1); border:0px !important }

.EndPaperControl .intropagezoomdiv .intropagezoomSM{ width:10px; height:14px; background: url(/images/endpaper-zoomin.png) no-repeat; border:solid 0px #979797; position:absolute; left:-15px; top:-7px}
.EndPaperControl .intropagezoomdiv .intropagezoomLG{ width:19px; height:30px; background: url(/images/endpaper-zoomout.png) no-repeat; border:solid 0px #979797; position:absolute; right:-25px; top:-14px}
/*ST-1538 PK 12/20/2018 Ends*/
/*ST-1586,VR 2019 01 23*/
.picksolidcolor.picksolid{height:160px !important;}

/*ST-1556 PK 02/06/2019 Starts*/
.hrSidebar {margin-top:26px;margin-bottom:26px; border-top: 2px solid #fff;width: 183px;margin-left: -22px;}/*ST-1556.1,BV,2019 04 09, ST-1766 ,ST-2032 m-top:26*/
.permanentchild a.frame_btn.cursorAdd{padding-left: 10px; margin-bottom:7px}/*ST-1510.1,VR*/
.permanentchild a.frame_btn.cursorAdd img{left: 6px;top: 2px;}
.permanentchild a.frame_btn.cursorDupe{padding-left: 5px;; width:152px; height:25px;margin-left: -1px;}/*ST-1556, BV 2019 02 19*/
.permanentchild a.frame_btn.cursorDupe img{left: 1px;top: 2px;}
/*ST-1556 PK 02/06/2019 Starts*/


/*ST-1615,BV, 2019 02 12*/
.ImageGalleryScroll .scroll-content .clearfix .Img-MULMainImgdiv #divImgUploadFromImage{ width: 100% !important; border: 2px solid #9b9b9b; height:179px!important;  }
.ImageGalleryScroll .scroll-content .clearfix .Img-MULMainImgdiv #divImgUploadFromImage .nohover{ display:block; width: 100%; height: 174px;}
.ImageGalleryScroll .scroll-content .clearfix .Img-MULMainImgdiv #divImgUploadFromImage .hover{ display:none; width: 100%; height: 174px;}
.ImageGalleryScroll .scroll-content .clearfix .Img-MULMainImgdiv #divImgUploadFromImage .divUploadText{top: 158px;float: left;left: 50%;transform: translate(-50%, -50%);width: 100%;}
.ImageGalleryScroll .scroll-content .clearfix .Img-MULMainImgdiv #divImgUploadFromImage:hover{border: 2px solid #4a90e2;}
.ImageGalleryScroll .scroll-content .clearfix .Img-MULMainImgdiv #divImgUploadFromImage:hover .nohover{ display:none}
.ImageGalleryScroll .scroll-content .clearfix .Img-MULMainImgdiv #divImgUploadFromImage:hover .hover{ display:block}
.ImageGalleryScroll .scroll-content .clearfix .Img-MULMainImgdiv #divImgUploadFromImage:hover .divUploadText{display:block}

/*ST-1556,BV,ST-1354.1*/
.binding_views.grid_view_new ul li:last-child p{ margin-left:-21px !important}
.binding_views.grid_view_new ul li .single_view_left,.binding_views.grid_view_new ul li .single_view_right{ height:38px;}
.binding_views.grid_view_new ul{margin-left: -11px;}
.cke_combo__tokens.ckeOpacityClass{padding-left: 5px !important;}
.cke_combo__tokens.ckeOpacityClass a.cke_combo_button{width: 61px!important;}
.binding_views.grid_view_new ul li:last-child p{ margin-left:-10px !important}
.binding_views.grid_view_new ul li .single_view_left,.binding_views.grid_view_new ul li .single_view_right{ height:36px;}
.drag_drop_img .binding_views{margin-left: -4px;}
.drag_drop_img.pick_your_binding.list-grid .binding_views ul li:first-child{margin-left: 30px;}
.drag_drop_img.list-grid .binding_views ul li.active{    margin-left: -5px;}
.cke_combo__lineheight:after, .cke_combo__letterspacing:after{ display:none}
.cke_toolbox span:nth-child(2) a.cke_button.cke_button__textcolor {border-left: 0px !important; }
.cke_button.cke_button__textcolor .cke_button__textcolor_icon{ border-left: 1px solid #4d4d4c;}

#ulSideBar .bordercover.clsActive img{ border:0px !important;outline: 1px solid rgb(87, 48, 122);}
#ulSideBar .bordercover img{ height:70px}
/*ST-1479,2019 02 21. */
#top .cke_button__unlink {display:none;}
.cke_dialog .cke_dialog_body{ background:#FAFAFA; outline:solid 3px #410166}
.cke_dialog .cke_dialog_contents_body{ background:#FAFAFA; position:relative}
.cke_dialog .cke_dialog_tabs{ display:none}
.cke_dialog .cke_dialog_contents{ border:0px; margin-top:0px}
.cke_dialog .cke_dialog_title{ background:transparent; border:0px;padding-bottom: 0px;}
.cke_dialog .cke_dialog_body .linkheading{ text-align:center;}
.cke_dialog .cke_dialog_body .linkheading .linkicon{ background:url("/images/hyperlink-icon.png?v=2") no-repeat; height:80px; width:76px; margin: 10px auto 0px;}/*RP ST-1869*/ /*ST-1869 VR 07/June/2019*/
.cke_dialog .cke_dialog_body .linkheading .linktitle{font-family: 'GillSans'; font-size:25px; text-align:center; color:#4a4a4a; margin: 10px auto 0px; }
.cke_dialog .cke_dialog_body label{font-family: 'GillSans'; font-size:18px; color:#4a4a4a; margin-bottom: 3px; }/*RP ST-1869*/
.cke_dialog .cke_dialog_footer{ background:#FAFAFA; border:0px; margin-top:10px; position:relative;padding: 10px 6px 10px 6px;}/*RP ST-1869*/
.cke_dialog .cke_dialog_contents_body{ height:auto !important;padding-left:15px; padding-right:15px;}
.cke_dialog .cke_dialog_footer .cke_dialog_ui_button_cancel{position: absolute;left: -335px; border:0px; background:transparent; padding:0px;font-family: 'GillSans'; font-size:13px; color:#5a5a5a;line-height: 13px;bottom: 5px;}/*RP ST-1869*/
.cke_dialog .cke_dialog_footer .cke_dialog_ui_button_cancel .linkremoveicon{ background:url("/images/Trashcan.png") no-repeat; height:15px; width:13px;position: absolute; margin-left: -5px; padding:0px; cursor:pointer}
.cke_dialog .cke_dialog_footer .cke_dialog_ui_button_ok {font-weight:900;font-family:"Avenir-Heavy"!important;width: 119px;height: 28px;border-radius: 30px;background-color: #ffffff; border:solid 1px #410166; color:#410166; line-height:28px; text-align:center; font-size:15px;font-family: 'Avenir-Book'; padding:0px;}/*RP ST-1761*/
.cke_dialog .cke_dialog_footer .cke_resizer{margin-top: 32px;}
.cke_dialog_contents_body .cke_dialog_page_contents:first-child table tr:nth-child(2) .cke_dialog_ui_input_select{ width:155px !important}

.cke_dialog .cke_dialog_body .cke_dialog_close_button{background:url("/images/close_black_icon.png?v=2") no-repeat; height:15px; width:15px;opacity: 1; top: 15px;right: 15px;}
.cke_dialog input.cke_dialog_ui_input_text, .cke_dialog select.cke_dialog_ui_input_select, .cke_dialog textarea.cke_dialog_ui_input_textarea {border: solid 1px #979797}
.cke_dialog input.cke_dialog_ui_input_text,
.cke_dialog textarea.cke_dialog_ui_input_textarea {color:#4f4f4f; font-size:16px;font-family:'Gill Sans'}
.cke_dialog .cke_dialog_ui_vbox_child{ padding:0px; padding-bottom: 5px !important;}
.cke_dialog select.cke_dialog_ui_input_select { background-color:#fff !important; background: url(/images/hyperlink-select-arrow.png) no-repeat right;-webkit-appearance: none;line-height: 14px;padding-right: 26px;}/*RP ST-1869 */
.cke_dialog_background_cover{background-color: #fff !important; opacity:0.8 !important}
.cke_dialog_ui_hbox_first{ width:16% !important}

span.linkremovetext {cursor:pointer;}
/* ST-1616,VR, 2019 02 21*/
#BackCover_businessCardPopup{background-color: rgba(255, 255, 255, 0)!important;overflow: inherit !important;}

/*.sidebar, .frame_editor{ z-index:1}*/
#divgridView{ position:relative}
#divgridView .binding_views.grid_view_new.grid_view_new>ul{margin-left: -5px;}
#divgridView .binding_views.grid_view_new > ul li{width:70px}
#divgridView .binding_views.grid_view_new ul li .single_view_wrapper{margin-left: 3px; height:39px}
#divgridView .binding_views.grid_view_new ul li .grid_view_wrapper{margin-left: -8px;}
#divgridView .binding_views.grid_view_new ul li:last-child p{ margin-left: -12px !important;}
#divgridView .binding_views.grid_view_new ul li:first-child p{ margin-left: 0px !important;}
#divgridView .binding_views.grid_view_new ul{margin-left: 0px;}
#divgridView .binding_views.grid_view_new ul li .single_view_wrapper .single_view_right ul li{ width:50%}
#divgridView .binding_views ul li .grid_view_wrapper ul li{width: 42% !important;}
#divgridView .binding_views{ left:0px !important;margin-left: 110px;}
/*ST-1676*/
#dvBindingTab .blinding_page_active{ background: url(/images/FBIcons/Binding_PageView_1.png) no-repeat; width: 55px;height: 40px;background-size: 55px 40px; margin-left:24px}/*RP ST-1840*/
#dvBindingTab .blinding_grid{ background: url(/images/FBIcons/Babybook.png) no-repeat; width:56px; height:40px; margin-left:8px;opacity:0.5}/*RP ST-1855*/
#dvBindingTab .blinding_grid:hover{opacity:1}/*RP ST-1855*/
#divgridViewBinding .blinding_page{ background: url(/images/FBIcons/Binding_PageView_2.png) no-repeat; width:64px; height:47px; margin-left:24px}/*RP ST-1840*/
#divgridViewBinding .blinding_page:hover{ background: url(/images/FBIcons/Binding_PageView_3.png) no-repeat; }/*RP ST-1840 add*/
#divgridViewBinding .blinding_grid_active{ background: url(/images/FBIcons/Babybook3.png) no-repeat; width:58px; height:42px; margin-left:11px;background-size: 56px 42px;}/*RP ST-1855*/

#businessbodybg{background-color: rgba(255, 255, 255, 0.6)!important;width: 100%;height: 100%;position: fixed;z-index: 999;}
/*ST-1677*/
.h1FbInspiration {font-family: GillSans-SemiBold;font-size: 25px;color: #000000; margin-top:73px; margin-bottom:35px;}
.divFbSubHeading{font-family: GillSans-Light;font-size:20px;color: #000000; margin-bottom:19px;margin-left:4px;}
.marginTop31{margin-top:31px;}
.ImageGalleryScroll.scrollbar-rail > .scroll-element.scroll-x{ display:none}

/*
    Second Slostate

    -- Delet 1870

*/

h2.SideNavBarType2 {font-size:12px;}
/*ST-1692, Under Development*/
.endpapergridbar .flip_view ul li.active ul{padding: 4px 3px;}
.endpapergridbar .flip_view ul li:last-child ul li { height: 11px; width: 11px; background-color: #d0021b; border: 0; float: left; margin: 0 1px 5px 2px }
.endpapergridbar .flip_view ul li:last-child ul li:nth-child(2) { background-color: #f5a623; margin: 0 2px 5px 2px}
.endpapergridbar .flip_view ul li:last-child ul li:nth-child(3) { background-color: #f8e71c; margin: 0 2px 5px 2px }
.endpapergridbar .flip_view ul li:last-child ul li:nth-child(4) { background-color: #7ed321; margin: 0 1px 0px 2px }
.endpapergridbar .flip_view ul li:last-child ul li:nth-child(5) { background-color: #bd10e0; margin: 0 2px 0px 2px }
.endpapergridbar .flip_view ul li:last-child ul li:last-child { background-color: #2c67f6; border: 0; padding: 0; margin: 0 2px 0px 2px }
.scroll-wrapper.picksolidcolor{height:unset !important; max-height:90px}
.image_gallery ul.responsive1{width: 100%;}/*ST-1693*/
/*ST-1687*/
/*ST-1754*/
.Ep-Fil-selected {background-image: url(/images/FBIcons/star-white.png) !important;background-position: center !important ;background-repeat: no-repeat !important ; z-index:2;}
.Ep-Fil-selected-Gray {background-image: url(/images/FBIcons/star-gray.png) !important;background-position: center!important;background-repeat: no-repeat!important; z-index:2;}
.endPaper_Color.IeColor-selected {background-image: url(/images/ImageEditor/icon-star.png) ;background-position: center ;background-repeat: no-repeat ; z-index:2;}/*ST-2051*/
.endPaper_Color.IeColor-selected {border: none;}/*ST-2051*/
.currentUsedEndPaper {border:2px solid #410166;}/*ST-1687 darkgray*/
.pickpalletecolor{ max-height:90px;min-height:90px;}/*ST-1916, Added Min height*/


.end_paper_lib .color_wrapper span.rainbow.Ep-Fil-selected{ background:none; z-index:unset}
.end_paper_lib .color_wrapper span.rainbow{ background:url(/images/FBIcons/rainbow-icon.png) no-repeat !important; width:28px; height:28px;position: relative; }/*ST-1754*/
.end_paper_lib .color_wrapper span.rainbow.Ep-Fil-selected:before {content:''; position:absolute;background-image: url(/images/FBIcons/star-white.png) !important;background-position: center !important ;background-repeat: no-repeat !important ; z-index:2;width: 16px;height: 16px;left: 6px;top: 6px;}
/*ST-1754*/
.end_paper_lib .color_wrapper span.spanColorImg{ width:28px; height:28px; position:relative}
.end_paper_lib .color_wrapper span.spanColorImg.Ep-Fil-selected{background: none;z-index: unset;}
.end_paper_lib .color_wrapper span.spanColorImg.Ep-Fil-selected:before {content:''; position:absolute;background-image: url(/images/FBIcons/star-white.png) !important;background-position: center !important ;background-repeat: no-repeat !important ; z-index:2;width: 16px;height: 16px;left: 6px;top: 6px;}

/*ST-2044*/
.ImgEndPaperGVItem .sysEndPaperImg,
.EndPaper-Side-List {
    object-fit: cover;
    object-position: Left top;
}
.rest_library .RestOfImgUiLiImg 
{
    object-fit: cover;
}
.lblprofimage .imageover{
    height: 68px;
    width: 68px;
    object-fit: cover;
}
@media screen and (min-width: 1349px) {
    .middle_endpaper .img_pop_gallery_wrapper .img_pop_gallery{width: 175px !important;height: 175px !important;overflow: hidden;}
    .endpaper_img_lib .img_pop_gallery_wrapper{margin: 0 -10px 0 0;height: 580px;}
    .responsive1.slick-initialized .slick-slide{width: 170px !important;}
}

@media screen and (min-width: 1500px) {
    .middle_endpaper .img_pop_gallery_wrapper .img_pop_gallery{width: 196px !important;height: 196px !important;overflow: hidden;}
    .endpaper_img_lib .img_pop_gallery_wrapper{margin: 0 -10px 0 0;height: 880px;}
}

.endpapergridbar h3{ border-bottom:0px;position:relative}
.endpapergridbar h3::after{ content: '';position: absolute;width: 80px;height: 1px;background: #fff;bottom: -3px;left: 11px;right: 0px;}
.ImgEndPaperGVItem.selected {border: 2px solid #0146ff;}
.ImgEndPaperGVItem.selected:hover {border: solid 2px #4990e2;}
.grid_page_manage{ position:relative; padding:30px 0px 60px;}
.grid_page_manage .gridpagebetatesting{ width:106px; position:absolute; bottom:10px; right:0px; z-index:99999}
.selecting_endpaper .selectyouendpaper{ margin-bottom:0px}
.selecting_endpaper .white_line{ background:#ffffff; height:2px; margin:3px 0px 15px}
.selecting_endpaper .white_line.noTopMargin { margin-top:0px !important;}
.selecting_endpaper .white_line.noBottomMargin { margin-bottom:0px !important;}
.end_paper_lib .by_style{ margin-bottom:20px}
.end_paper_lib .uploadbtnmargin{ margin-top:39px !important; margin-bottom:40px}
.end_paper_lib h3{ margin-bottom:40px}
.end_paper_lib .by_color{margin-bottom: 10px !important;}
.selecting_endpaper.endpapergridbar{width:200px}
.end_paper_lib.endpapergridbar .color_wrapper span{ width:28px; height:28px}

/*ST-1675*/
#divBarSinglGrid .single_view_wrapper{background: url(/images/FBIcons/Babybook2.png) no-repeat; width:63px; height:44px; border:0px;}/*RP ST-1795*/
#divBarSinglGrid .grid_view_wrapper{background: url(/images/FBIcons/Babybook.png) no-repeat; width:63px; height:44px; border:0px;opacity:0.5} /*RP ST-1795*/
#divBarSinglGrid .grid_view_wrapper:hover {opacity:1}/*RP ST-1795*/
#divBarSinglGrid .single_view_wrapper div, #divBarSinglGrid .grid_view_wrapper ul{ display:none}
#divgridView .grid_view_new .single_view_wrapper{background: url(/images/FBIcons/Babybook1.png) no-repeat; width:63px !important; height:44px !important; border:0px !important;margin-left: 6px !important;opacity:0.5}/*RP ST-1795*/
#divgridView .grid_view_new .single_view_wrapper:hover {opacity:1}
#divgridView .grid_view_new .grid_view_wrapper{background: url(/images/FBIcons/Babybook3.png) no-repeat; width:63px !important; height:44px !important; border:0px !important;    margin-left: -5px !important;}/*RP ST-1795*/
#divgridView .grid_view_new .single_view_wrapper div, #divgridView .grid_view_new .grid_view_wrapper ul{ display:none}
/*ST-1693*/
.by_color .color_wrapper a{ margin:1px}
.by_color .color_wrapper span{ margin-bottom:0px !important}
/*RP ST-1674*/
#dvSendmailPopup .grayBorder:after { /*RP ST-1674*/background-color: transparent !important;}
/*RP ST-1627*/
.Layout11ImageDiv .close_icon_PlaceHolder {right: -134px;top: -174px;position:relative!important;}
.Layout11ImageDiv .close_icon_PlaceHolder img{height:auto!important;}
.img-PLI216{position:relative;}
.cke-PLI45 .divEraserForAll{margin:0!important;}
.cke-PLI45 .divEraserForAll .imgEraserForAll{border:0px!important;}
/*RP ST-1728*/
.linetext {    height: 25px;}
/*RP ST-1730*/
.sidebarLogo{position: absolute; /*bottom: 18px;*/ bottom: 20px;left: 8px;}/*RP ST-1627*//*RP ST-1730 change bottom: 208px; TO bottom: 20px;*/
.switchLable{pointer-events: all;}
/*ST-1527, VR*/

/*ST-1558, VR*/
ul#ulSideBar.scroll-scrolly_visible  li{ margin: 0 8px 5px 18px;}
.scrollpage > .scroll-element,.scrollpage > .scroll-element div {border: none;margin: 0;overflow: hidden;padding: 0;position: absolute;z-index: 10;}
.scrollpage > .scroll-element {background-color: transparent;}
.scrollpage > .scroll-element div {display: block;height: 100%;left: 0;top: 0;width: 100%;}
.scrollpage > .scroll-element .scroll-element_size {background-color: #BBBBBB;background-color: rgba(0, 0, 0, 0.15);}
.scrollpage > .scroll-element .scroll-element_outer:hover .scroll-element_size {background-color: #666;background-color: rgba(0, 0, 0, 0.5);}
.scrollpage > .scroll-element.scroll-x {bottom: 0;height: 12px;left: 0;min-width: 100%;padding: 3px 0 2px;width: 100%; display:none}
.scrollpage > .scroll-element.scroll-y {height: 100%;min-height: 100%;padding: 0 2px 0 3px;right: -8px;top: 0;width: 25px;}
.scrollpage > .scroll-element .scroll-bar {background-color: #fff;-webkit-border-radius: 18px;-moz-border-radius: 18px;border-radius: 18px;box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);}
.scrollpage > .scroll-element .scroll-element_outer:hover .scroll-bar {box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);}
.scrollpage > .scroll-content.scroll-scrolly_visible {left: -17px;margin-left: 17px;}
.scrollpage > .scroll-content.scroll-scrollx_visible {margin-top: 17px;top: -17px;}
.scrollpage > .scroll-element.scroll-x .scroll-bar {height: 10px;min-width: 10px;top: 1px;}
.scrollpage > .scroll-element.scroll-y .scroll-bar {left: 1px;min-height: 18px;width: 18px; height:18px !important}
.scrollpage > .scroll-element.scroll-x .scroll-element_outer {height: 15px;left: 5px;}
.scrollpage > .scroll-element.scroll-x .scroll-element_size {height: 2px;left: -10px;top: 5px;}
.scrollpage > .scroll-element.scroll-y .scroll-element_outer {top: 5px;width: 24px;}
.scrollpage > .scroll-element.scroll-y .scroll-element_size {left: 10px;top: -10px;width: 2px;}
.scrollpage > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size {left: -27px;}
.scrollpage > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size {top: -27px;}
.scrollpage > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_track {left: -27px;}
.scrollpage > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_track {top: -27px;}
/*ST-1558,VR,2019 04 29*/
.dash_board > .scroll-element,.dash_board > .scroll-element div {border: none;margin: 0;overflow: hidden;padding: 0;position: absolute;z-index: 10;}
.dash_board > .scroll-element {background-color: transparent;}
.dash_board > .scroll-element div {display: block;height: 100%;left: 0;top: 0;width: 100%;}
.dash_board > .scroll-element .scroll-element_size {background-color: #BBBBBB;background-color: rgba(0, 0, 0, 0.15);}
.dash_board > .scroll-element .scroll-element_outer:hover .scroll-element_size {background-color: #666;background-color: rgba(0, 0, 0, 0.5);}
.dash_board > .scroll-element.scroll-x {bottom: 0;height: 12px;left: 0;min-width: 100%;padding: 3px 0 2px;width: 100%; display:none}
.dash_board > .scroll-element.scroll-y {height: 100%;min-height: 100%;padding: 0 3px 0 3px;right: -8px;top: 0;width: 25px;}/*ST-1558*/
.dash_board > .scroll-element .scroll-bar {background-color: #ccc;-webkit-border-radius: 18px;-moz-border-radius: 18px;border-radius: 18px;box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);}
.dash_board > .scroll-element .scroll-element_outer:hover .scroll-bar {box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);}
.dash_board > .scroll-content.scroll-scrolly_visible {left: -17px;margin-left: 17px;}
.dash_board > .scroll-content.scroll-scrollx_visible {margin-top: 17px;top: -17px;}
.dash_board > .scroll-element.scroll-x .scroll-bar {height: 10px;min-width: 10px;top: 1px;}
.dash_board > .scroll-element.scroll-y .scroll-bar {left: 1px;min-height: 18px;width: 18px; height:18px !important}
.dash_board > .scroll-element.scroll-x .scroll-element_outer {height: 15px;left: 5px;}
.dash_board > .scroll-element.scroll-x .scroll-element_size {height: 2px;left: -10px;top: 5px;}
.dash_board > .scroll-element.scroll-y .scroll-element_outer {top: 5px;width: 24px;}/*ST-1558*/
.dash_board > .scroll-element.scroll-y .scroll-element_size {left: 10px;top: -10px;width: 2px;}
.dash_board > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size {left: -27px;}
.dash_board > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size {top: -27px;}
.dash_board > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_track {left: -27px;}
.dash_board > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_track {top: -27px;}

.frame_editor .rest_library ul { border-right: 0px solid #979797; }

/*RP ST-1730*/
#liTopDuck{padding-top:1rem;}

/*Old PopUP Code
    Locasiton 
    for VR
    ST-1869 
    ----- Delet after ST-1870
*/
    /*ST-1558*/
.ImageGalleryScroll.scrollbar-rail > .scroll-element .scroll-bar, .currImageGalleryScroll.scrollbar-rail > .scroll-element .scroll-bar {background: #ccc} 
.img_pop_gallery_wrapper > .scroll-element,.img_pop_gallery_wrapper > .scroll-element div {border: none;margin: 0;overflow: hidden;padding: 0;position: absolute;z-index: 10;}
.img_pop_gallery_wrapper > .scroll-element {background-color: transparent;}
.img_pop_gallery_wrapper > .scroll-element div {display: block;height: 100%;left: 0;top: 0;width: 100%;}
.img_pop_gallery_wrapper > .scroll-element .scroll-element_size {background-color: #BBBBBB;background-color: rgba(0, 0, 0, 0.15);}
.img_pop_gallery_wrapper > .scroll-element .scroll-element_outer:hover .scroll-element_size {background-color: #666;background-color: rgba(0, 0, 0, 0.5);}
.img_pop_gallery_wrapper > .scroll-element.scroll-x {bottom: 0;height: 12px;left: 0;min-width: 100%;padding: 3px 0 2px;width: 100%; display:none}
.img_pop_gallery_wrapper > .scroll-element.scroll-y {height: 100%;min-height: 100%;padding: 0 2px 0 3px;right: -5px;top: 0;width: 25px;}
.img_pop_gallery_wrapper > .scroll-element .scroll-bar {background-color: #ccc;-webkit-border-radius: 18px;-moz-border-radius: 18px;border-radius: 18px;box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);}
.img_pop_gallery_wrapper > .scroll-element .scroll-element_outer:hover .scroll-bar {box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);}
.img_pop_gallery_wrapper > .scroll-content.scroll-scrolly_visible {left: -17px;margin-left: 17px;}
.img_pop_gallery_wrapper > .scroll-content.scroll-scrollx_visible {margin-top: 17px;top: -17px;}
.img_pop_gallery_wrapper > .scroll-element.scroll-x .scroll-bar {height: 10px;min-width: 10px;top: 1px;}
.img_pop_gallery_wrapper > .scroll-element.scroll-y .scroll-bar {left: 1px;min-height: 18px;width: 18px; height:18px !important}
.img_pop_gallery_wrapper > .scroll-element.scroll-x .scroll-element_outer {height: 15px;left: 5px;}
.img_pop_gallery_wrapper > .scroll-element.scroll-x .scroll-element_size {height: 2px;left: -10px;top: 5px;}
.img_pop_gallery_wrapper > .scroll-element.scroll-y .scroll-element_outer {top: 5px;width: 24px;}
.img_pop_gallery_wrapper > .scroll-element.scroll-y .scroll-element_size {left: 10px;top: -10px;width: 2px;}
.img_pop_gallery_wrapper > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size {left: -27px;}
.img_pop_gallery_wrapper > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size {top: -27px;}
.img_pop_gallery_wrapper > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_track {left: -27px;}
.img_pop_gallery_wrapper > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_track {top: -27px;}

.currImageGalleryScroll > .scroll-element,.currImageGalleryScroll > .scroll-element div {border: none;margin: 0;overflow: hidden;padding: 0;position: absolute;z-index: 10;}
.currImageGalleryScroll > .scroll-element {background-color: transparent;}
.currImageGalleryScroll > .scroll-element div {display: block;height: 100%;left: 0;top: 0;width: 100%;}
.currImageGalleryScroll > .scroll-element .scroll-element_size {background-color: #BBBBBB;background-color: rgba(0, 0, 0, 0.15);}
.currImageGalleryScroll > .scroll-element .scroll-element_outer:hover .scroll-element_size {background-color: #666;background-color: rgba(0, 0, 0, 0.5);}
.currImageGalleryScroll > .scroll-element.scroll-x {bottom: 0;height: 12px;left: 0;min-width: 100%;padding: 3px 0 2px;width: 100%; display:none}
.currImageGalleryScroll > .scroll-element.scroll-y {height: 100%;min-height: 100%;padding: 0 2px 0 3px;right: -5px;top: 0;width: 25px;}
.currImageGalleryScroll > .scroll-element .scroll-bar {background-color: #ccc;-webkit-border-radius: 18px;-moz-border-radius: 18px;border-radius: 18px;box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);}
.currImageGalleryScroll > .scroll-element .scroll-element_outer:hover .scroll-bar {box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);}
.currImageGalleryScroll > .scroll-content.scroll-scrolly_visible {left: -17px;margin-left: 17px;}
.currImageGalleryScroll > .scroll-content.scroll-scrollx_visible {margin-top: 17px;top: -17px;}
.currImageGalleryScroll > .scroll-element.scroll-x .scroll-bar {height: 10px;min-width: 10px;top: 1px;}
.currImageGalleryScroll > .scroll-element.scroll-y .scroll-bar {left: 1px;min-height: 18px;width: 18px; height:18px !important}
.currImageGalleryScroll > .scroll-element.scroll-x .scroll-element_outer {height: 15px;left: 5px;}
.currImageGalleryScroll > .scroll-element.scroll-x .scroll-element_size {height: 2px;left: -10px;top: 5px;}
.currImageGalleryScroll > .scroll-element.scroll-y .scroll-element_outer {top: 5px;width: 24px;}/*ST-1558*/
.currImageGalleryScroll > .scroll-element.scroll-y .scroll-element_size {left: 10px;top: -10px;width: 2px;}
.currImageGalleryScroll > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size {left: -27px;}
.currImageGalleryScroll > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size {top: -27px;}
.currImageGalleryScroll > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_track {left: -27px;}
.currImageGalleryScroll > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_track {top: -27px;}


/*
    RP ST-1759
    .alertbox.divBindingSingleCovMsg .modal-content{ width:395px !important;right:48px; }
    ----- Delet after ST-1870
*/

/*ST-1354.1,VR*/
a.cke_button__link{ width:57px !important; height:24px !important; border-radius:9px !important; padding:0px !important;}/*ST-1354, Add space in Link*/
.cke_combo .cke_combo_open{width: 24px !important;height: 24px !important;position: relative;}
.cke_combo_arrow{position: absolute !important;margin: 0px !important;left: 0px !important; width:24px !important; height:24px !important; }
.cke_combo__tokens.cke_combo:after{height: 29px !important;border: 0px !important;width: 1px;background: #979797;}
a.cke_button.cke_button__justifyleft, a.cke_button.cke_button__justifycenter, a.cke_button.cke_button__justifyright{width: 27px !important;height: 28px !important;padding: 0px 0px !important;}
a.cke_button.cke_button__justifyleft:hover, a.cke_button.cke_button__justifycenter:hover, a.cke_button.cke_button__justifyright:hover{padding: 0px 0px !important; }
a.cke_button.cke_button__bold, a.cke_button.cke_button__italic, a.cke_button.cke_button__underline{width: 21px !important;height: 28px !important;padding: 0px 0px !important;}
a.cke_button.cke_button__bold:hover, a.cke_button.cke_button__italic:hover, a.cke_button.cke_button__underline:hover{padding: 0px 0px !important; }
a.cke_button.cke_button__texttoolbar, .cke_combo.cke_combo__font a, .cke_combo.cke_combo__fontsize a, .cke_combo.cke_combo__fontsize a, a.cke_button.cke_button__textcolor, .cke_combo.cke_combo__lineheight a, .cke_combo.cke_combo__letterspacing a,a.cke_button.cke_button__link{ margin-top:2px !important}
a.cke_button.cke_button__texttoolbar .cke_button_icon{ width:13px !important}
.cke_toolbox span:nth-child(2) a.cke_button.cke_button__textcolor{ margin-top:2px !important}
a.cke_button.cke_button__texttoolbar:hover, .cke_combo.cke_combo__font a:hover, .cke_combo.cke_combo__fontsize a:hover, .cke_combo.cke_combo__fontsize a:hover, a.cke_button.cke_button__textcolor:hover, .cke_combo.cke_combo__lineheight a:hover, .cke_combo.cke_combo__letterspacing a:hover,a.cke_button.cke_button__link:hover{ margin-top:2px !important}
a.cke_button.cke_button__link, a.cke_button.cke_button__link:hover{ margin-top:1px !important}
/*RP ST-1752*/
.popupemailsent.alertbox .submit_flip{height: 30px!important;line-height: 30px!important;width: 144px!important;font-size: 14px!important;margin-bottom: 10px!important;}/*RP ST-1869*/

/*RP ST-1761*/
.fbookslistBetaicon{top: -68px;bottom: auto;right:9px;}/*ST-2086*/
.middle_endpaper{ position:relative}
.endpaperGVBetaicon{right: 11px;bottom: -86px;}
.sharefbBetaicon{    right: 0;bottom: auto;position: absolute !important;text-align: center;margin-top: 57px;left: 0;}
.myLibBetaicon{right: -25px;bottom: auto;position: absolute !important;margin-top: 10px;}
@media only screen and (max-width: 1450px) {
    .myLibBetaicon{right: 50px;}
}
@media only screen and (max-width: 1368px) {
    .new_flip_main_wrapper{ margin-top:40px}
    .fbookslistBetaicon{top: -108px;bottom: auto;right: 10px;}
    .myLibBetaicon{right: 125px;}
}
@media only screen and (max-width: 1330px) {
    .myLibBetaicon{right: 185px;}
}

a.cke_button__textcolor{border: 0px!important;}
/*RP ST-1729*/
#div45 .divEraserForAll img{ margin-left: 0px!important; }
/*VR ST-1832 START*/
.flip_first_heading h1,.main_heading h3{ border-bottom:0px; position:relative}
.flip_first_heading h1:after,.main_heading h3:after {content: "";position: absolute;width: 67px;height: 1px;background: #979797;bottom: 1px;left: 0px;right: 0px;margin: 0 auto;}
/*VR ST-1832 END*/
/*ST-1766*/
.frame_editor h2{ border-bottom:0px;position:relative}
.frame_editor h2::after{ content: '';position: absolute;width: 80px;height: 1px;background: #fff;bottom: -3px;left: 11px;right: 0px; margin:0 auto}
/*RP ST-1478 start*/
.cke_button__textcolor{ display:none !important}

/*ST-1876,BV*/
.cp2-cke-panel{width: 180px !important;/*height: 160px !important;*/}
.cp2-cke-panel {height:220px !important}
.cp2-cke-panel::-webkit-scrollbar { display: none; } 
.cke_toolbox span:nth-child(2) a.cke_button.cke_button__ckecp2bgcolor{ width:48px !important;margin-top: 2px !important; height: 25px !important;}
.cke_toolbox span:nth-child(2) a.cke_button__ckecp2bgcolor .cke_button_arrow{right: -11px  !important;height: 25px !important;}
/*ST-1860*/
@media screen and (max-width: 1380px) and (min-width: 1320px) {
    .cke_toolbox span:nth-child(2) a.cke_button__ckecp2bgcolor .cke_button_arrow{ height:24px !important}
}
/*RP ST-1478 start*/
#divImageForTN .beta_testing, #divImageForTN .Click-on-text-image{display:none !important;} /*ST-1777, BV*/
.setbullpenicon {color: #363836;font-size: 20px;text-decoration: none!important;display: inline-block;vertical-align: middle;font-family: 'GillSans-Light';margin: 0 0 0 7px;}/*RP ST-1868*/
 /*RP ST-1864 start*/
.cke-PLI15 .cke_editable, #div23 .cke_editable, .cke-PLI16 .cke_editable, .cke-PLI49 .cke_editable, .cke-PLI43 .cke_editable,
.cke-PLI51 .cke_editable, .cke-PLI92 .cke_editable, .cke-PLI211 .cke_editable, .cke-PLI67 .cke_editable, .cke-PLI80 .cke_editable,
.cke-PLI73 .cke_editable, .cke-PLI85 .cke_editable, .cke-PLI97 .cke_editable_inline, .cke-PLI103 .cke_editable, .cke-PLI100 .cke_editable {border: 3px solid transparent;border-bottom: none;}
#inline103, #inline51, #inline92, #inline211, #inline15 {border-bottom: none !important;}
/*RP ST-1864 end*/

/*RP ST-1880*/
#aBtnUploadEndPaperGV {font-size: 13px!important;width: 160px!important;padding: 0px!important;height: 30px;line-height: 30px;}
#abtnViewImageInUsespan{color:#417505}/*RP ST-1881*/
#abtnViewImageInUsespan1{color:#417505}/*RP ST-1881*/






/*PopUP CSS START- ST-1870*/

.modal.back-dropmodal { -webkit-transform: none !important; -moz-transform : none !important;-o-transform:none !important; -ms-transform:none !important; transform:none !important; max-height:100% !important; top:0 !important; left : 0 !important; background-color: rgba(255, 255, 255, 0.5); }/*RP ST-1869*/
/*.modal.back-dropmodal .modal-dialog { top: 15%}/*RP ST-1674*/
/*ST-1870 can remove !important for ST-1870 .modal.back-dropmodal .modal-dialog { top: 15% }RP ST-1674*/
.new_flip .modal-body { border: 2px solid #410166; }
/*-- Delete Afte 1870 - 7L */
.new_flip .modal-content { border: 0; border-radius: 0; text-align: center; box-shadow: none; }
/*ST-863, ST-1685*/
.new_flip .modal-content h1 { margin: 15px 0 20px !important; font-family: 'GillSans'; color: #4a4a4a; font-size: 25px; position: relative; display: inline-block; text-transform: uppercase;}/*RP ST-1674, VR, ST-1685,margin*/.new_flip .modal-content h1 img { top: 0px; left: 0px; }/*RP ST-1674*/
.new_flip .new_flip_name { position: relative; width: 320px; margin: 5px auto 35px;  }
/*PK 12/15/2017 - ST-892 : Change Underline Animation color*/
.new_flip .new_flip_name:after { content: ""; position: absolute; bottom: -2px; left: 50%; transform: translate(-50%, -50%); width: 0; height: 1px; background-color: #801FB8;transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s; }
.new_flip .new_flip_name.border_animation:after { width: 100%; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; }
.new_flip_name.border_animation:after { width: 100%; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; }
/*AC 10/16/2018 - ST-1281*/
.new_flip .new_flip_name .form-control { width: 320px; margin: 0 auto; border: 0; box-shadow: none; font-size: 20px; font-family: 'GillSans'; color: #000000; text-transform: uppercase; text-align: center; background-color: #fafafa; }
.new_flip .submit_flip { background: #fff; border: 1px solid #57307a; border-radius: 20px; text-transform: uppercase; text-decoration: none; display: inline-block; margin: 0 7px 5px; transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s; font-family: gill_sanssemibold ;font-size: 15px;color: #410260 ;padding: 6px 40px;}/* Second line RP ST-1869*, BV merger in as same class*/
.new_flip .submit_flip:hover { background-color: #57307a; color: #FFF !important; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; font-family: 'gill_sanssemibold' !important; font-size: 14px;}

/*-- Delet After 1870 1280L */
/*ST-1685*/
.alertbox{ z-index:9999; width:395px; min-height:249px;}
.modal.new_flip.alertbox .modal-dialog{width:395px; min-height:249px ;}

/*VR,ST-1527, 2019 05 07*/
.new_flip .modal-content h1.popupMainH1Text,.delete_old_flip .modal-content h1,#dvDeleteMultipleFlipBook .modal-content h1,.sendmailpopup .modal-body .popupMainH1Text,.feedback_title,div#BackCover_businessCardPopup .businesscardpopup .businesstitle, #dvRenamePortPage h1 {margin: 10px 0px 0px 0px !important;font-family: 'GillSans' !important;color: #434343 !important;font-size: 25px !important;position: relative;display: inline-block;text-transform: uppercase;font-weight: 500;} /*ST-1869 VR 07/June/2019*//*RP ST-1869*/
.modal-open{ overflow:inherit !important; padding-right:0px !important}
body .modal.color_picker_modal {position: absolute;width: 1500px !important;margin: 0 auto;left: 0px !important;}
body .modal.color_picker_modal .modal-dialog{top: 0px !important;margin: 15px 0 0px 622px !important;}
.modal.new_flip, .modal.delete_old_flip, .modal.back-dropmodal, .modal.modal_visible{position:absolute; margin:0 auto; top:0px; left:0px;}
.modal.new_flip .modal-dialog, .modal.delete_old_flip .modal-dialog, .modal.back-dropmodal .modal-dialog, .modal.modal_visible .modal-dialog{margin: 0px 0;left: 0px;}/*margin: 0px 0 !important;*/
#dvForgetComment .modal-dialog{text-align:center;height:254px; } /*ST-1869 VR 07/June/2019*/
#dvNewFlipBookName .modal-dialog, #dvOpenFlipBook .modal-dialog, #dvSaveMyFlipBook .modal-dialog,#dvPublishWarningPopup .modal-dialog{width:466px !important; min-height:300px;} 
/*ST-1869 VR 07/June/2019 START*/
#dvNewFlipBookName .modal-dialog .modal-body{ padding:13px 54px 26px;}
#dvNewFlipBookName h1.popupMainH1Text{margin: 9px auto 10px !important;}
#dvNewFlipBookName.new_flip .new_flip_name, #dvSaveMyFlipBook.new_flip .new_flip_namee, #dvNameMyFBPage.new_flip .new_flip_name{ margin:0 auto 37px; width:100% !important; border:none !important}
#dvNewFlipBookName .new_flip_name_span, #dvSaveMyFlipBook .new_flip_name_span, #dvNameMyFBPage .new_flip_name_span, #dvRenamePortPage .new_flip_name_span {margin-bottom: 0px;position: absolute;bottom: 70px;left: 0px;right: 0px;margin: 0px auto;}
#dvNewFlipBookName.new_flip .new_flip_name .form-control, #dvSaveMyFlipBook.new_flip .new_flip_name .form-control, #dvNameMyFBPage.new_flip .new_flip_name .form-control, #dvRenamePortPage.new_flip .new_flip_name .form-control{border-bottom: 2px solid #410166;border-radius: 0px; width:100%!important}
#dvNewFlipBookName .new_flip_name.border_animation:after, #dvSaveMyFlipBook .new_flip_name.border_animation:after, #dvNameMyFBPage .new_flip_name.border_animation:after, #dvRenamePortPage .new_flip_name.border_animation:after{ display:none}
#dvNewFlipBookName .submit_flip, #dvOpenFlipBook .submit_flip, #dvAutoSaveSettings .submit_flip, #dvSaveMyFlipBook .submit_flip, #dvFlipBookDeleteClearfb .submit_flip, #dvDeleteMultipleFlipBook .submit_flip,
.popup_btns_yes,.submit_flip,.quip_btn a,div#BackCover_businessCardPopup .businesscardpopup .bc-letsgobtn,#dvForgetComment .submit_flip, .popupcolorexist .submit_flip,.delete_old_flip .cancel_flip, 
.divBindingSingleCovMsg .submit_flip, #dvNameMyFBPage .submit_flip, .cancel_flip, #dvRestorePage .submit_flip, #dvRenamePortPage .submit_flip{ height:30px; width:144px; padding:0px; line-height:28px; text-align:center; margin-bottom:0px !important}

/*PopUp Specific */
.modal.sendmailpopup .modal-dialog{ width:434px !important; height:571px}
.sendemail_transition .field.new_flip_name {margin-bottom: 3px !important;border-bottom: 1px solid #9b9b9b !important;}/*RP ST-1674*/

/*ST-1869 VR 07/June/2019 START*/
.sendmailpopup .popupSubMainText{    font-family: 'GillSans' !important;font-size: 12px !important;color: #410166 !important;margin-bottom: 15px; line-height:11px; margin-top:0px;}
.sendmailpopup .modal-body .textfield{ line-height:21px;height: 22px !important;}
.sendmailpopup .modal-body textarea.textfield{ line-height:26px;height: 44px !important;}
.sendmailpopup .modal-body p{  font-family: 'GillSans' !important;  font-size: 14px!important; color:#cdcdcd!important; line-height:14px!important; margin-top:7px}
.sendmailpopup  .submit_flip{height:30px; width:129px; padding:2px; line-height:28px; text-align:center; margin-bottom:0px !important}/*RP ST-1869*/
.sendmailpopup .ShareMessageLabel{ padding:0px !important; margin-bottom:0px !important;font-family: 'GillSans' !important; font-size:17.2px!important; color:#410166;}
.sendmailpopup .closenew{ background:url(/images/share-flipbook-close.png) no-repeat; width: 15px !important;height: 15px !important;top: 15px;right: 15px !important;}
/*ST-1869 VR 07/June/2019 END*/


/*ST-1349, PK, 09/25/2018 added style for email popup*/
.sendmailpopup { }
.sendmailpopup .modal-body {background: #FAFAFA;text-align: left;padding-left: 29px; /*RP ST-1674*/padding-right: 29px; /*RP ST-1674*/}
.sendmailpopup .modal-body h1 {font-size: 20px;font-weight: 900;color: #000000;line-height: 27px;margin: 10px auto;padding: 0px;text-align: center;width: 100%;margin-top:0px!important;margin-bottom:0px !important}
.sendmailpopup .modal-body h1 img {position: relative;width: 55px; /*RP ST-1674*/left: auto;height: 50px; /*RP ST-1674*/object-fit: contain; /*RP ST-1674*/}
.sendmailpopup .modal-body h1 span {display: block}
.sendmailpopup .modal-body .close {/*margin-top: -5px;margin-right: -5px;*/font-size: 20px;color: #000000;position: absolute;right: 15px;}/*RP ST-1869*/
.sendmailpopup .modal-body .fieldname {font-size: 20px;font-weight: 500;color: #000000;height: 27px;text-align: left}
.sendmailpopup .modal-body .field {position: relative}
.sendmailpopup .modal-body .field .icon1 {position: absolute;background: url('/images/sendmailpopup-icon.png') no-repeat;width: 13px;height: 17px;left: -6px;z-index: 999;top: 10px;}
/*change for ST-1281*/
.sendmailpopup .modal-body .textfield {border: 0px !important;background: transparent;/*border-bottom: solid 1px #979797 !important;*/width: 100%;height: 20px;margin-top: 6px;margin-bottom: 25px;font-size: 22.6px; /*RP ST-1674*/font-family: 'GillSans';color: #000000;}
.sendmailpopup .modal-body .textfield:hover, .sendmailpopup .modal-body .textfield:focus, .sendmailpopup .modal-body .textfield:active {background: transparent;border: 0px !important;/*border-bottom: solid 1px #000000 !important;*/width: 100%}
.sendmailpopup .modal-body p {font-size: 16.9px; /*RP St-1674*/line-height: 24px;color: #410166;font-family: 'Avenir-Book'; /*RP St-1674*/text-align: center;font-weight: 300; /*RP St-1674*/height: 23px; /*RP St-1674*/}
.sendmailpopup .modal-body .submitSendpopup {width: 106px;height: 23px;margin-top: 10px;margin: 0 auto;margin-bottom: 10px;}
.sendmailpopup .modal-body .submitSendpopup div {width: 106px;height: 23px;background: url(/images/sendmailpopup-send.png) no-repeat;}
.sendmailpopup .modal-body .sendmailpopup-form {width: 100%}
.modal.sendmailpopup .modal-dialog {width: 450px; /*RP ST-1674*/}

/****644 styles***/
.delete_flip { margin: 20px; outline: 0; }
.delete_flip:focus, .delete_flip:active { outline: 0; }
/* ST-1370 , Updated Width to Auto , PK 10/08/2018*/
.delete_old_flip .modal-dialog { width: auto; /*top: 12%;*/}
.delete_old_flip .modal-content { border: 2px solid #410166; border-radius: 0; text-align: center; box-shadow: none; background:#fafafa; }
/* ST-1395 , PK 10/15/2018, Update Fontsize and Color*/
.delete_old_flip .modal-content h1 { margin: 8px; font-family: 'GillSans'; color: #000000;  font-size: 20px; position: relative; display: inline-block; text-transform: uppercase;}
.delete_old_flip .modal-content p, #dvRestorePage .modal-content p {  font-size: 18px; line-height: 30px; color: #4a4a4a; font-family: 'GillSans-Light'; margin: 0 0 8px;  font-weight: 300;}
.delete_old_flip .modal-content .clear-book p.last-p{ margin-bottom:0;}
.delete_old_flip .modal-content .clear-book h1{ margin-bottom:10px;}
/* ST-1370 , Updated Margin From Bottom For Buttons , PK 10/08/2018*/
.delete_old_flip .cancel_flip {font-family: 'gill_sanssemibold'; font-size: 14px !important; border: 1px solid #57307a; color: #410166; border-radius: 20px; text-transform: uppercase;text-decoration: none; display: inline-block; font-size: 14px; margin: 0px; transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s; }
.delete_old_flip .cancel_flip:hover { background-color: #57307a; color: #FFF; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; }
/*ST-1281 AC 10/16/2018*/
.delete_old_flip .delete_flip { font-family: gill_sanssemibold; border: 1px solid #57307a; background-color:#FFF !important; color: #410260 !important; border-radius: 20px; text-transform: uppercase; padding: 5px 30px; text-decoration: none; display: inline-block; font-size: 14px; margin: 0px; transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s;margin-bottom:10px }/*RP ST-1869*/
.delete_old_flip .delete_flip:hover { background-color: #57307a !important; color: #FFF !important; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; }
.delete_old_flip .delete_flip_btn { border: 1px solid #57307a; color: #6b448d; border-radius: 20px; text-transform: uppercase; padding: 5px 30px; text-decoration: none; display: inline-block; font-size: 14px; margin: 0px; transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s; }
.delete_old_flip .delete_flip_btn:hover { background-color: #57307a; color: #FFF; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; }
/****end 644 styles***/

/*ST-1281 */
.new_flip .btnCloseXPopup, .delete_old_flip .btnCloseXPopup { display: inline-block; position: absolute; right: 13px; top: 10px;  }
.alertbox .colorexisticon{ background:url(/images/color-exist.png) no-repeat; height:95px; width:83px; margin:0 auto;border-right: solid 1px #FAFAFA;}/*ST-1685,VR 2019 04 10*/
.alertbox .popupMainH1Text{  font-size:25px !important; text-align:center; width:321px; margin:19px auto 19px;}
.alertbox .submit_flip{  margin:16px 0px 4px 0px;}/*2019 04 09, margin-top 16*/
.alertbox .imagebullpenexiticon{ background:url(/images/bullpen-exist.png?v=2) no-repeat; height:73px; width:87px; margin: 10px auto 12px;}/*ST-1685,VR 2019 04 10*//*RP ST-1869*/
.alertbox .emailsuccessicon{ background:url(/images/email-sucess.png?v=2) no-repeat; height:91px; width:172px; margin:4px auto;margin-bottom:-10px;}/*RP ST-1869*/
.alertbox .notessuccessicon {background: url(/images/BindingPopup.png?v=2) no-repeat;height: 91px;width: 80px; margin: 0px 126px}/*RP ST-1759,RP ST-1869,  BV,ST-2077 , binding alert*/
.alertbox .improvingsuccessicon {background: url(/images/improve-flipbook.png?v=2) no-repeat;height: 76px;width: 96px; margin: 0px auto 11px}/*HN ST-1781*/
.alertbox .improvetext {width:471px; right: 3px; font-size:25px;font-family: GillSans;}/*HN ST-1781 add class*/
.alertbox .default {background: url(/images/improve-flipbook.png?v=2) no-repeat;height: 76px;width: 96px; margin: 0px auto 10px}/*RP ST-1869*/
.alertbox .spantext1:last-child{font-family: 'GillSans' !important;font-size: 18px !important;color: #4f4f4f !important; margin:7px auto 30px; display:block;text-transform: lowercase;}
/*ST-1685.1,VR*/
#dvAutoSaveSettings .notessuccessicon {background: url(/images/autosave-setting.png?v=2) no-repeat;height: 75px;width: 75px; margin: 0px auto}/*RP ST-1869*/
#dvThankYouFeedback .modal-dialog{ height: 166px;} /*ST-1527*/
.popupcolorexist h1.popupMainH1Text, .popupimageexist h1.popupMainH1Text, .popupemailsent h1.popupMainH1Text {  font-family: 'GillSans' !important; color: #4a4a4a!important; font-size: 25px!important; position: relative; display: inline-block; text-transform: uppercase; font-weight:inherit}
.popupimageexist .modal-body{ height:249px} /*ST-1869 VR 07/June/2019*/
.popupcolorexist .modal-body{ height: auto;width: 395px;padding: 24px 20px 26px;} /*ST-1869 VR 07/June/2019*//*RP ST-1869*/
#dvThankYouFeedback .modal-body{ height:174px;padding: 24px 0 28px;}/*HN ST-1805*/
#dvThankYouFeedback h2 { margin: 21px 0 0px !important; font-family: 'GillSans' !important; color: #4a4a4a!important; font-size: 25px!important; position: relative; text-transform: uppercase; font-weight:inherit;line-height: 29px; height:auto !important}/*HN 1805*/
/*ST-1685.1,VR, 2019 04 12*/
.popupcolorexist #aGOTITButton{margin-top: 14px;}
.popupcolorexist h1.popupMainH1Text{ width:314px!important; height:58px; margin: 15px 0 0px !important;line-height: 29px;}/*RP ST-1869*/
.popupimageexist.alertbox .imagebullpenexiticon{margin: 0px auto 0px;}/*HN ST-1805*/
.popupimageexist h1.popupMainH1Text{ width:291px; height:58px; margin: 17px 0 0px !important;line-height: 29px;}/*HN ST-1805*/
.popupemailsent h1.popupMainH1Text{width:auto; height:58px; margin: 19px 0 9px !important;line-height: 60px;}/*RP ST-1869*/
/*AC 10/16/2018 - ST-1281*/
.popupMainText{/*font-family: 'GillSans' !important;*/padding-top: 10px;/*margin-bottom:0px;font-size: 18px !important;color: #4A4A4A !important;line-height: initial;*/}
.popupSubMainText{font-family: 'GillSans' !important;/*RP ST-1674*/font-size:15px !important;/*RP ST-1674*/color:#717171 !important;/*RP ST-1674*/margin-top: -24px;/*RP ST-1674*/}
#dvOpenFlipBook .modal-dialog .modal-body{ padding:23px 95px 26px;}
#dvOpenFlipBook h1.popupMainH1Text{margin: 16px auto 17px !important;}
#dvOpenFlipBook .dropdown_option{ width:100%; margin:0px auto 34px}
#dvOpenFlipBook .modal-body img {max-height: 94px;}
#dvAutoSaveSettings .modal-dialog{width:395px; min-height:254px;}
#dvAutoSaveSettings .modal-dialog .modal-body{ padding:21px 21px 26px;}
#dvAutoSaveSettings h1.popupMainH1Text{margin: 15px auto 1px !important;}
#dvAutoSaveSettings .popupSubMainText{ margin:0 auto 30px !important;font-size: 18px !important;color: #4f4f4f !important;}
#dvSaveMyFlipBook .modal-dialog .modal-body{ padding:33px 54px 26px;}
#dvSaveMyFlipBook h1.popupMainH1Text{margin: 21px auto 10px !important;}
#dvSaveMyFlipBook .closenew, #dvDeleteMultipleFlipBook .closenew,  #divImgDeleteConfPopUp .closenew,#dvNameMyFBPage .closenew,#dvRenamePortPage .closenew{right: 10px;position: absolute;top: 10px;width:15px; height:15px}
#dvFlipBookDeleteClearfb .modal-dialog{width:565px !important; min-height:294px;}
#dvFlipBookDeleteClearfb .modal-dialog .modal-body,#dvDeleteMultipleFlipBook .modal-dialog .modal-body{ padding:17px 41px 24px;}
#dvFlipBookDeleteClearfb .modal-content h1,#dvDeleteMultipleFlipBook .modal-content h1{margin: 21px auto 0px !important; line-height:25px}
#dvFlipBookDeleteClearfb .close.closenew{ position:absolute; right:15px}
.delete_old_flip .modal-content p, #dvDeleteMultipleFlipBook .modal-content p{font-family: 'GillSans' !important;font-size: 18px !important;color: #4f4f4f !important; margin:16px auto 12px}
#dvDeleteMultipleFlipBook .modal-content p{ margin:2px auto 8px}
#dvDeleteMultipleFlipBook .modal-dialog{ width:395px !important; min-height:294px;}
#dvPublishWarningPopup .number_popup{ border:0px!important}
#dvPublishWarningPopup .number_popup .modal-content{border:2px solid #410166 !important;width:466px;height:300px;box-shadow:none}/*RP ST-1869*/
#dvPublishWarningPopup .We-are-missing-your{font-family: 'GillSans' !important;font-size: 18px !important;color: #4f4f4f !important; margin:4px auto 19px}/*RP ST-1869*/
#dvPublishWarningPopup .Do-you-want-to-enter{font-family: 'GillSans' !important;font-size: 18px !important;color: #4f4f4f !important; margin:0px auto 30px}
#dvSendmailPopup .modal-dialog{width:400px !important; height:537px; }
#dvSendmailPopup .modal-dialog .modal-content{ width:100% !important}
#dvSendmailPopup .modal-dialog .modal-body{ padding:32px 50px 26px;}
.sendmailpopup .modal-body{ padding-left:0px !important; padding-right:0px !important}
.sendmailpopup .modal-body h1 img{width:91px !important; height:74px !important}
.sendmailpopup .modal-body .popupMainH1Text{ margin:12px auto 37px !important; display:block}
.sendmailpopup .modal-body .sendmailpopup-form{width:310px !important; margin:0 auto}
.sendemail_transition .field.new_flip_name .textfield{font-family: GillSans;font-size: 16px;}
#dvAlertBox.SendmailPopuperror #alertboxicon{ display:none !important}
#divFeedbackPopUp,#divFeedbackPopUpWorkspace{position:absolute;top:0px}
#dvSaveFeedback .modal-dialog{width:400px !important; height:auto;}
#dvSaveFeedback .modal-body{ padding:20px 50px 26px;}
#dvSaveFeedback .textbtm{position: absolute;bottom: 15px;font-family: GillSans !important;font-size: 12px;color: #410166;}
#dvSaveFeedback .textcomment{font-family: GillSans !important;font-size: 17.2px;color: #410166;margin-top:0px;font-weight:500}/*RP ST-1869*/
#dvSaveFeedback textarea{ height:86px !important}
#dvSaveFeedback .feedback_title{ margin:12px auto 30px !important}
#dvSaveFeedback .col-sm-12{ padding:0px !important}
.quip_btn a{margin-top:40px}
div#BackCover_businessCardPopup .businesscardpopup{ width:400px; height:522px;border: solid 2px #410166;}
div#BackCover_businessCardPopup.new_flip .modal-body{ border: 0px;height:100%;padding:32px 38px 26px;}/*ST-1978*/
div#BackCover_businessCardPopup .businesscardpopup .businesstitle{font-family: 'GillSans';font-size: 25px;line-height: 25px;color: #410166;text-align: center;padding: 6px; margin:6px auto 35px !important}
div#BackCover_businessCardPopup .businesscardpopup .businesscardlogo{ height:95px !important; margin:0 auto !important}
div#BackCover_businessCardPopup .businesscardpopup .bc-form{ width:100% !important; margin:0 auto !important}
div#BackCover_businessCardPopup .businesscardpopup .bc-input{  font-family: GillSans; font-size: 20px ; color:#000;outline: none;width: 320px;border: 0px;line-height: 23px;border-bottom: solid 1px #410166;background: transparent;height: 23px}/*ST-1973*/
div#BackCover_businessCardPopup .businesscardpopup .bc-textbox{ position: relative; margin-bottom:35px}
div#BackCover_businessCardPopup .businesscardpopup .bc-letsgobtn{ border-radius: 25px;display: block;text-decoration: none;background: #fff;border: 1px solid #57307a;color: #410166 !important;text-transform: uppercase;transition: all ease-out 0.5s;-webkit-transition: all ease-out 0.5s;-moz-transition: all ease-out 0.5s;font-family: 'gill_sanssemibold';margin-top: 30px !important;margin-left: 85px;}
div#BackCover_businessCardPopup .businesscardpopup .businesscardlogo img {object-fit: contain;}
div#BackCover_businessCardPopup .businesscardpopup .bc-closebtn {position: absolute;right: 15px;top: 15px}
div#BackCover_businessCardPopup .businesscardpopup .bc-textbox .bc-fieldname {font-family: 'GillSans';font-size: 14px; line-height: 11px;color: #410166;float: left;padding: 2px 0px;}/*ST-1973*/
div#BackCover_businessCardPopup .businesscardpopup .bc-inpu:focus {border-color: inherit;-webkit-box-shadow: none;box-shadow: none;}
div#BackCover_businessCardPopup .businesscardpopup .bc-coutryarea {position: absolute;right: 0px;font-family: 'AvenirLTStd-Book';font-size: 10px;color: #5c5c5c;line-height: 14px;top: 8px}
div#BackCover_businessCardPopup .businesscardpopup .bc-coutryarea span {display: inline;vertical-align: middle}
div#BackCover_businessCardPopup .businesscardpopup .bc-textbox .bc-urltext {width: auto;float: left;font-family: 'GillSans';font-size: 16px; line-height: 23px;color: #8e8e8e;text-align: left;}
div#BackCover_businessCardPopup .businesscardpopup .CountrySelect #nonusa {float: right;margin-bottom: 0px;}
div#BackCover_businessCardPopup .businesscardpopup .CountrySelect #flag {cursor: pointer;position: relative;right: 0px;bottom: -13px;}
div#BackCover_businessCardPopup .businesscardpopup .CountrySelect { position: absolute;bottom: 20px;right: 24px;}
div#BackCover_businessCardPopup .businesscardpopup .CountrySelect .ctydw {top: 30px;left: -1px;min-width: 80px;}
div#BackCover_businessCardPopup .businesscardpopup .CountrySelect i {cursor: pointer;position: absolute;font-size: 25px;right: -22px;top: 8px;}
div#BackCover_businessCardPopup .businesscardpopup .CountrySelect select {width: 20px;border: none;float: right;background: transparent;}
div#BackCover_businessCardPopup .businesscardpopup .CountrySelect #flag img {width: 24.3px;height: 15px;float: right}
div#BackCover_businessCardPopup .businesscardpopup .bc-textbox .bc-urlinput {width: 270px ; float: left}/*ST-2084*/
div#BackCover_businessCardPopup .businesscardpopup .bc-textbox .bc-urlinput .bc-input {width: 100%}
div#BackCover_businessCardPopup .businesscardpopup .bc-textbox .bc-clear {clear: both}
div#BackCover_businessCardPopup .businesscardpopup .bc-textbox.bc-lastbox {margin-bottom: 0px}
div#BackCover_businessCardPopup .businesscardpopup .bc-letsgobtn:hover {background-color: #57307a;color: #FFF !important;transition: all ease-in 0.5s;-webkit-transition: all ease-in 0.5s;-moz-transition: all ease-in 0.5s;}

div#BackCover_businessCardPopup .bc-fieldname.error {color: #c01f2a !important;}
div#BackCover_businessCardPopup .bc-fieldname.success {color: #28a745 !important; padding-left:15px !important;}/*ST-2084*/
div#BackCover_businessCardPopup .bc-fieldname.success img {width: 10px;margin-top: -5px;}
#dvForgetComment .modal-body{ padding:22px 20px 26px !important}
#dvForgetComment.new_flip .modal-content h1.popupMainH1Text{ height:auto !important; margin-bottom:20px !important;margin-top:10px!important}/*RP ST-1869*/ 
#dvFlipBookPageDeleteClear .modal-dialog{width:395px; height:254px; }
#dvFlipBookPageDeleteClear .modal-dialog .modal-body{ padding:19px 21px 26px}
#dvFlipBookPageDeleteClear h1.popupSubMainText{margin:16px auto  29px !important}
#dvFlipBookPageDeleteClear h1.popupSubMainText > span{ display:block}
#dvFlipBookPageDeleteClear .btnleft{ float:left;margin-left:4px;}/*RP ST-1869*/
#dvFlipBookPageDeleteClear .btnright{ float:right;margin-right:4px}/*RP ST-1869*/
#dvFlipBookPageDeleteClear .btnclear{ clear:both}
.divBindingSingleCovMsg .modal-dialog{/*width:395px;*/ height:254px; }

.divBindingSingleCovMsg h1.popupMainH1Text{margin: 11px auto 30px !important;}
#dvNameMyFBPage .modal-dialog{width:466px !important; min-height:300px;}
#dvNameMyFBPage .modal-body,#dvRenamePortPage .modal-body{ padding:29px 54px 29px !important}
#dvNameMyFBPage h1.popupMainH1Text,#dvRenamePortPage h1.popupMainH1Text{margin: 20px auto 29px !important;}
#dvClearPop h1.popupMainText{ margin:5px auto 0px;margin-top: -2px!important;}/*RP ST-1869*/
#dvClearPop h1.popupMainText > span{ display:block}
#dvClearPop p{ margin:18px auto 24px;font-family: 'GillSans' !important;font-size: 18px !important;color: #4f4f4f !important; line-height:19px}
#dvClearPop p span{display:block;}
#dvRestorePage .modal-dialog{width:395px !important; min-height:254px;}
#dvRestorePage .modal-dialog .modal-body{ padding:17px 21px 26px;}
#dvRestorePage h1.popupMainH1Text{margin: 12px auto 35px !important; width:100%}/*RP ST-1869*/
#dvRestorePage .popupSubMainText{display:none}
#dvRenamePortPage h1 img{ width:92px !important; height:85px !important}
#dvRenamePortPage .modal-dialog{width:466px !important; min-height:300px;}
#dvRenamePortPage h1{ margin:0 auto !important}
#dvRenamePortPage.new_flip .new_flip_name{ margin:15px auto 27px; width:100% !important; border:none !important}
#dvRenamePortPage h1 span{ display:block; margin-top:10px}
#rightSideLICustomLayout {margin-right: 66px !important;}/*RP ST-1657*/
.insertpagebtn { padding: 0px !important; width: 95px !important; margin: 22px 22px 22px 16px !important;}/*RP ST-1657*/

#divImgDeleteConfPopUp .modal-content-ImgDeleteConf{margin: 0px!important;}
div[aria-describedby=divImageSelecterPopUp] {width: 1300px !important;top: 40px !important;left: 0px !important;right: 0px !important;margin: 0 auto !important;}
.global-modal_contents{/*width: 1500px !important;*/left: 0px !important;right: 0px !important;top: 0px !important;margin: 0 auto !important;}

/*ST-1869 VR 07/June/2019 END */
 

/*HN ST-1805 Start*/
.popupimageexist .modal-content {width: 391px;height: 254px;}
.popupimageexist .modal-content .modal-body {height: auto;padding: 24px 0px}
.popupimageexist.alertbox .submit_flip {padding: 0px !important;display: inline-block !important; margin: 26px 0px 0px 0px !important;font-size: 14px !important;color: #410166 !important;text-align: center !important;}
.popupimageexist.alertbox .submit_flip:hover {color: #fff !important}
#dvThankYouFeedback .modal-body img {width: 76px;}
.divSuccAutoCloseMsg .modal-dialog {width: 312px !important}
.divSuccAutoCloseMsg .modal-dialog .modal-content {width: 312px !important}
.divSuccAutoCloseMsg .modal-dialog .modal-body {width: 312px; height: 172px;padding: 34px 0px 43px;}
.divSuccAutoCloseMsg .modal-dialog .model-info-icon {margin-top: -10px;}
.divSuccAutoCloseMsg .popupMainH1Text {font-family: 'GillSans' !important;color: #4a4a4a !important;font-size: 25px !important;line-height: 29px !important;font-weight: normal !important;width: 100%;}
.divSuccAutoCloseMsg .popupMainH1Text.p-t-22 {padding-top: 50px !important;}
/*HN ST-1805 End*/

.alertbox.divBindingSingleCovMsg .modal-content{ width:395px; }/*RP ST-1869*/
.alertbox.divBindingSingleCovMsg .modal-body{ padding:22px 30px 19px; text-align:center; }/*RP ST-1759*/
.alertbox.divBindingSingleCovMsg .modal-body .popupMainH1Text{ width:100% !important; margin:0 !important;/*font-family: 'GillSans' !important;*/ /*color: #4a4a4a!important;*/ /*font-size: 25px!important;*/ line-height:29px !important; font-weight:normal !important}/*RP ST-1759*/
.alertbox.divBindingSingleCovMsg .submit_flip{ margin-top:30px;font-family: GillSans-SemiBold ;line-height: 31px;}/*RP ST-1869 RP ST-1759*/

/*RP ST-1869*/
#dvFlipBookDeleteClear .modal-content{width:565px;height:294px;}
#dvFlipBookDeleteClear .modal-content { position: fixed; top: 68px; right: 0; left: 86px; bottom: 0; margin: auto; }
#dvFlipBookDeleteClear .modal-body{width:565px;height:294px;}
#dvRenamePortPage .modal-content{width:466px;height:300px;}
#dvRenamePortPage .modal-body{width:466px;height:300px;}
#aUpdatePageName {margin-top: 13px;}
#ddlAutoSaveTime::-ms-expand{display:none}
#ddlAutoSaveTime{width: 36px;border-radius: 3px;height: 18px;font-size: 12px;font-weight: 600;text-align-last: left;padding: 0;margin-right: -3px;}
#ddlAutoSaveTime {width: 36px !important;height: 18px;line-height: 18px;font-size: 12px;border-radius: 3px;-webkit-appearance: none;padding-left: 11px;background:#fff url(/images/autosave-dropdown.png) no-repeat right;background-position: right 3px center; margin-right:-2px;border: solid 1px #979797;margin-left: 3px;}
#dvFlipBookDeleteClear div.modal-dialog .close.closenew {height: 15px;width: 15px;position: relative;left: -5px;opacity:1!important;}
#dvSaveMyFlipBook .close.closenew{right: 15px;top: 15px;}
#dvFlipBookPageDeleteClear .close.closenew,#dvClearPop .close.closenew{position: relative;top: -3px;right: -6px;}
#dvSaveFeedback .close.closenew{right: 15px!important;top: 15px;}
.endpapergridbar .flip_view ul li:last-child ul li:nth-child(4) { background-color: #7ed321; margin: 0 1px 0px 2px }
.icon_telephone {margin-top:11px;}
#cke_115_uiElement{margin-left:12px;}
/*RP ST-1869*/
/*RP ST-1880*/
#dvNameMyFlipBook .submit_flip {padding: 0px 10px;}

/*RP ST-1824 start*/
/*#dvBackPageSaveErrormsg .poptopright {top: 45% !important;left: 14% !important;}*/
.new_flip .poptopright .submit_flip{padding: 2px 40px!important;}
.new_flip .poptopright .modal-body{height: 150px;}

/*ST-1419*/
#divImgDeleteConfPopUp {z-index: 1002;position: absolute;margin: 0 auto;left: 0px;right: 0px;}
#divImgDeleteConfPopUp .modal-content-PagePopUp {background-color: #fafafa;   border: 2px solid #410166;padding: 15px;position: absolute;/*top: 50%;left: 50%;*/}/*ST-1310*/
#divImgDeleteConfPopUp .modal-content-ImgDeleteConf {width: 553px; height: 358px;z-index: 1008;margin: -171px 0 0 -321px; padding: 27px 77px 26px; /* height/2 width/2*/} /*ST-1869 VR 07/June/2019*/
#divImgDeleteConfPopUp .modal-content-PagePopUp .deleteicon{ margin:5px auto 15px; width:131px; height:130px; text-align:center; background:url(/images/delete-image-icon.png) no-repeat; line-height: 130px;}
#divImgDeleteConfPopUp .modal-content-PagePopUp .deleteicon img{ width:100px; height:100px}
#divImgDeleteConfPopUp .modal-content-PagePopUp .btn-delete { background: #fff; border: 1px solid #57307a; color: #410166 !important; border-radius: 20px; text-transform: uppercase; height:30px; width:144px; padding:0px; line-height:28px; text-align:center; margin-bottom:0px !important;text-decoration: none; display: inline-block; font-size: 14px; margin: 10px 7px 5px; transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s; font-family: 'gill_sanssemibold' !important}/*ST-1869 VR 07/June/2019*/
#divImgDeleteConfPopUp .modal-content-PagePopUp .btn-delete:hover { background-color: #57307a; color: #FFF !important; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; font-family: 'gill_sanssemibold' !important; font-size: 14px;}
#divImgDeleteConfPopUp .modal-content-PagePopUp h1{font-family: 'GillSans';  color: #434343;  font-size: 25px; line-height:25px; text-align:center; margin:0px; margin-bottom:13px} /*ST-1869 VR 07/June/2019*/
#divImgDeleteConfPopUp .modal-content-PagePopUp .divMsgConfText{ font-family:'GillSans';font-size: 18px;color: #4f4f4f; line-height:18px; margin-bottom:30px; text-align:center} /*ST-1869 VR 07/June/2019*/
#dvNameAlreadyExist .submit_flip {padding-top:1px;}
/*#divImgDeleteConfPopUp .modal-content-PagePopUp .divMsgConfText .divMsgConfTextSpanOr{ color:#ff00ff;font-family:'GillSans-SemiBold';}*/
#divImgDeleteConfPopUp .close {font-size:10px;top:22px;right:22px;}/*RP ST-1869*/
#dvConfirmBox #aConfirmOK{padding: 1px;margin-top: 12px;}/*ST-1870,BV 2019 07 15*/
#dvConfirmBox {z-index:10020;}/*ST-1879*/
.drag-Img-slider {width: 100%;height: 100%; overflow:hidden;} /*PM file , add overflow:hidden to hide overflow TIP */
/*ST-1903*/
#dvConfirmBox .spanSubMessage {display:none;}
#dvConfirmBox.CkeLink .spanSubMessage {display:block;}
.uploadendpaper ul .popover{left: 12px!important;}/*ST-1797*/
/*.uploadendpaper ul .popover{top: 14px !important;left: 21px;}*//*ST-1797*/
    
.CkeLink .model-info-icon {background: url(/images/close-window.png) no-repeat;width: 70px;height: 80px;display: block;margin: 0 auto;}
.CkeLink .modal-body {width: 453px;height: 302px;padding: 26px 47px 28px;}
.CkeLink .spanSubMessage{font-family: GillSans;font-size: 18px; margin: 17px 0px 0px;color: #4f4f4f;}
.CkeLink .submit_flip{font-family: GillSans-SemiBold ;font-size: 14px; margin-top:10px!important}
/*ST-1745*/
.divGridViewPage .mB{width: 280px !important; height:150px; padding-left:10px!important; padding-right:10px !important}
.divGridViewPage .img_wrapper iframe{ width:287px;height:140px;border: 0px;}
.divGridViewPage .img_wrapper.single_img iframe{ width:144px !important;height:140px !important;border: 0px;}
.divGridViewPage .img_wrapper.single_img + p{ width: 144px !important; margin: 0 auto; padding: 0px; text-align: right !important;padding-right: 5px;}
.grid_page_manage .divGridViewPage{width:100% !important}
.img_wrapper.grdViewDouble {outline:1px solid #979797; height:140px;}
/*ST-1975*/
/*Addedning Login Popu because its access in all the part of fb including preview*/

#FBPopUpSignIN .modal-content{border:2px solid #410166;width:360px; min-height:513px; margin:0 auto; background-color: #fafafa; border-radius:0px}
#FBPopUpSignIN .divRegPopUp_body{padding:26px 33px 34px; position:relative;font-family: 'GillSans-SemiBold';}
#FBPopUpSignIN .RegCloseXPopup{ position:absolute; right:15px; top:15px}
#FBPopUpSignIN .Regheading{font-size: 25px;color: #410166; line-height:30px;font-family: 'GillSans-SemiBold'; text-transform:uppercase; margin:7px auto 0px; text-align:center}
#FBPopUpSignIN .SubRegheading{font-size: 17px;color: #000000; line-height:23px;font-family: 'GillSans-Light'; margin:0px auto 20px; text-align:center}
#FBPopUpSignIN .Regform{ width:208px; margin:21px auto 25px}
#FBPopUpSignIN .Regform .Regfield{ margin-bottom:10px;text-align: left; position:relative}
#FBPopUpSignIN .Regform lable{font-family: 'GillSans';color: #410166;line-height: 15px;font-size: 10px; text-align:left;display: block; min-height:15px}
#FBPopUpSignIN .Regform .form-control{border:0px;border-bottom: 2px solid #410166;border-radius: 0px; width:100%!important; background:transparent;box-shadow: inset 0 0px 0px rgba(0,0,0,.075); padding-left:0px; padding-right:0px;font-family: 'GillSans-Light'; font-size:18px;color: #000000; height:25px; line-height:25px; padding:0px}
#FBPopUpSignIN .Regform .form-control.errorinput{border-bottom: 2px solid #c01f2a;}
#FBPopUpSignIN .Regform .form-control.sucessinput{border-bottom: 2px solid #0d802e;}
#FBPopUpSignIN .Regform .form-control:focus{box-shadow: inset 0 0px 0px rgba(0,0,0,.075)}
#FBPopUpSignIN .Regform .rembtext{margin-top:9px}
#FBPopUpSignIN .Regform .rembtext, #FBPopUpSignIN .Regform .rembtext span{font-family: 'GillSans-Light'; font-size:15px; color:#000000;text-align: left; }
#FBPopUpSignIN .Regform .signinbtn {display: block; background: #fff; border: 1px solid #57307a; border-radius: 20px; text-transform: uppercase; text-decoration: none; margin: 39px auto 5px; transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s; font-family: 'GillSans-SemiBold';font-size: 14px;color: #410260 ; width:160px; height:30px; line-height:28px}
#FBPopUpSignIN .Regform .signinbtn:hover { background-color: #57307a; color: #FFF !important; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; font-family: 'GillSans-SemiBold' !important; font-size: 14px;}
    #FBPopUpSignIN .Regform .signinbtn:focus {
        outline: none;
    }
#FBPopUpSignIN .Regfooterlink{font-family: 'GillSans-SemiBold';font-size: 14px; line-height:17px;  color: #410260; text-align:center; width:300px; margin:50px auto 0px}
#FBPopUpSignIN .Regfooterlink .Regfooterleft{ float:left; width:140px; cursor:pointer;}
#FBPopUpSignIN .Regfooterlink .Regfooterright{float:right; width:130px;cursor:pointer;}
#FBPopUpSignIN .Regfooterlink a{color: #410260; text-decoration:underline}
#FBPopUpSignIN .Regfooterlink a:hover{ color:#c01f2a}
#FBPopUpSignIN .Regfooterlink .Regfooterleft.active,
#FBPopUpSignIN .Regfooterlink .Regfooterleft.active a,
#FBPopUpSignIN .Regfooterlink .Regfooterleft.UserEmailErr, #FBPopUpSignIN .Regfooterlink .Regfooterleft.UserEmailErr a,
#FBPopUpSignIN .Regfooterlink .Regfooterleft:hover,#FBPopUpSignIN .Regfooterlink .Regfooterleft:hover a,#FBPopUpSignIN .Regfooterlink .Regfooterright:hover,#FBPopUpSignIN .Regfooterlink .Regfooterright:hover a{color:#c01f2a}
#FBPopUpSignIN .Regfooterlink .Regfooterclr{ clear:both; display:block; content:"";}
#FBPopUpSignIN #imgLoginShowHidePassWord.ImgshowPw {content:url(/images/joinus_pass-icon.png);}
#FBPopUpSignIN #imgLoginShowHidePassWord{content:url(/images/joinus_passlock-icon.png?V=2); cursor:pointer; width:24px; height:19px}
#FBPopUpSignIN .Regform .RegFtext{ position:relative}
#FBPopUpSignIN .Regform .RegFtext .floating-label {position: absolute;pointer-events: none;top: 15px;left: 0px;transition: 0.2s ease all;font-family: 'GillSans-Light'; color:#979797; font-size:18px}
#FBPopUpSignIN .Regform .RegFtext input:focus ~ .floating-label,#FBPopUpSignIN .Regform .RegFtext input.activeinput ~ .floating-label{top: 0px;left: 0px;font-size: 10px;opacity: 1;font-family: 'GillSans'; color:#410166}
#FBPopUpSignIN #imgLoginShowHidePassWord{ position:absolute; right:0px; top:15px}
#FBPopUpSignIN #imgRegShowHideUser{ position:absolute; right:0px; top:15px}
#FBPopUpSignIN .Regform .RegFtext .msgErrorUserId {position: absolute;top: 0px;left: 0px;font-size: 10px;opacity: 1;font-family: 'GillSans'; color:#c01f2a}
#FBPopUpSignIN .Regform .RegFtext input.activeinput{}
#FBPopUpSignIN .Regform .sublabel{font-family: 'GillSans'; color:#410166; font-size:12px; line-height:25px}
#FBPopUpSignIN .Regform .greentext{color:#0d802e;}
#FBPopUpSignIN .Regform .redtext{color:#c01f2a;}
#FBPopUpSignIN .Regform .checkbox{ margin-left:0px;}
#FBPopUpSignIN .mtb5{ margin-top:5px !important;margin-bottom: -5px !important;}
#FBPopUpSignIN .Regform .RegFtext .astrick{ color:#57307a;font-family: 'GillSans-SemiBold';}
#FBPopUpSignIN .Regform .sublabel.lblPwRquriment {color:#7e7e7e}
#FBPopUpSignIN [type="checkbox"]:not(:checked),#FBPopUpSignIN [type="checkbox"]:checked {position: absolute;opacity:0; width:15px; height:15px; z-index:999;top: 4px;}
#FBPopUpSignIN [type="checkbox"]:not(:checked) + label,#FBPopUpSignIN [type="checkbox"]:checked + label { position: relative;overflow: hidden;padding-left: 25px;cursor: pointer;display: inline-block;height: 25px;line-height: 25px;-webkit-user-select: none; -moz-user-select: none; -khtml-user-select: none;-ms-user-select: none;}/*ST-1754*/
#FBPopUpSignIN [type="checkbox"] + label:before,[type="checkbox"] + label:after {content: '';position: absolute;left: 0;z-index: 1;-webkit-transition: .2s;transition: .2s;}
#FBPopUpSignIN [type="checkbox"]:not(:checked) + label:before {top: 6px;width: 15px; height: 15px;border: 1px solid #979797;}
#FBPopUpSignIN [type="checkbox"]:not(:checked) + label:after {top: 6px;width: 15px; height: 15px;border: 1px solid #979797;z-index: 0;}
#FBPopUpSignIN [type="checkbox"]:checked + label:before {top: -2px;left: 3px; background:transparent url(/images/tick.png) no-repeat; width:22px; height:20px;z-index: 99;}/*ST-1754*/
#FBPopUpSignIN [type="checkbox"]:checked + label:after {top: 6px;width: 15px; height: 15px;border: 1px solid #979797;z-index: 0;}/*ST-1754*/
#FBPopUpSignIN [type="checkbox"]:disabled:not(:checked) + label:before,[type="checkbox"]:disabled:checked + label:before {top: 0;box-shadow: none;background-color: #444;width: 15px; height: 15px;border: 3px solid #444;-webkit-transform: rotateZ(0deg);transform: rotateZ(0deg);}
#FBPopUpSignIN [type="checkbox"]:disabled + label {}
#FBPopUpSignIN [type="checkbox"]:disabled:not(:checked) + label:hover:before {border-color: #979797;}
#FBPopUpSignIN .emailyes{ width:100%;position:absolute; top:-10px; right:0px; font-size:10px;font-family: 'GillSans'; color:#000000}
#FBPopUpSignIN .emailyes [type="checkbox"]:not(:checked) + label,#FBPopUpSignIN .emailyes [type="checkbox"]:checked + label{ padding-left:20px}
#FBPopUpSignIN .emailyes .emailyererror{ position:absolute; left:0px; color:#c01f2a;width: 200px;}
#FBPopUpSignIN .emailyes .ckbox{position: absolute;right: 0px;top: -15px;}
#FBPopUpSignIN .Regform .form-control.hideborder{border-bottom: 2px solid transparent;}
#FBPopUpSignIN .hightlightline1 { content: ""; position: absolute; top: 41px; left: 50%; transform: translate(-50%, -50%); width: 0; height: 2px; background-color: #801FB8; transition: all ease-in 1s; -webkit-transition: all ease-in 1s; -moz-transition: all ease-in 1s; }
#FBPopUpSignIN input.borderanmination  ~ .hightlightline1 { width: 100%;  }
#FBPopUpSignIN .normalTxt.hightlightline1{background-color: transparent;}
#FBPopUpSignIN .divPWErrorMsg {position:absolute; left:0px; color:#c01f2a;font-size: 10px;}



/*ST-1976*/

#FBPopUpJoinUS .modal-content{border:2px solid #410166;width:435px; min-height:640px; margin:0 auto; background-color: #fafafa; border-radius:0px}
#FBPopUpJoinUS .divRegPopUp_body{padding:21px 73px 31px; position:relative;font-family: 'GillSans-SemiBold';}
#FBPopUpJoinUS .RegCloseXPopup{ position:absolute; right:15px; top:15px}
#FBPopUpJoinUS .Regheading{font-size: 25px;color: #410166; line-height:30px;font-family: 'GillSans-SemiBold'; text-transform:uppercase; margin:7px auto 0px; text-align:center}
#FBPopUpJoinUS .SubRegheading{font-size: 17px;color: #000000; line-height:23px;font-family: 'GillSans-Light'; margin:0px auto 20px; text-align:center}
#FBPopUpJoinUS .Regform{ width:311px; margin:25px auto 25px}
#FBPopUpJoinUS .Regform .Regfield{ margin-bottom:20px;text-align: left; position:relative}
#FBPopUpJoinUS .Regform lable{font-family: 'GillSans';color: #410166;line-height: 15px;font-size: 10px; text-align:left;display: block; min-height:15px}
#FBPopUpJoinUS .Regform .form-control{border:0px;border-bottom: 2px solid #410166;border-radius: 0px; width:100%!important; background:transparent;box-shadow: inset 0 0px 0px rgba(0,0,0,.075); padding-left:0px; padding-right:0px;font-family: 'Neutra-Text-Book'; font-size:18px;color: #000000; height:25px; line-height:25px; padding:0px}
#FBPopUpJoinUS .Regform .form-control.errorinput{border-bottom: 2px solid #c01f2a;}/*ST-2092*/
#FBPopUpJoinUS .Regform .form-control.sucessinput{border-bottom: 2px solid #0d802e;}/*ST-2092*/
#FBPopUpJoinUS .Regform .form-control:focus{box-shadow: inset 0 0px 0px rgba(0,0,0,.075)}
#FBPopUpJoinUS .Regform .rembtext{margin-top:9px}
#FBPopUpJoinUS .Regform .rembtext, #FBPopUpJoinUS .Regform .rembtext span{font-family: 'GillSans-Light'; font-size:15px; color:#7e7e7e;text-align: left; }
#FBPopUpJoinUS .Regform .signinbtn {display: block; background: #fff; border: 1px solid #57307a; border-radius: 26px; text-transform: uppercase; text-decoration: none; margin: 35px auto 5px; transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s; font-family: 'GillSans-SemiBold';font-size: 22px;color: #410260 ; width:215px; height:40px; line-height:38px}
#FBPopUpJoinUS .Regform .signinbtn:hover { background-color: #57307a; color: #FFF !important; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; font-family: 'GillSans-SemiBold' !important; font-size: 22px;}
#FBPopUpJoinUS .Regfooterlink{font-family: 'GillSans-Light';font-size: 14px; line-height:17px;  color: #000000; text-align:center; width:300px; margin:50px auto 0px}
#FBPopUpJoinUS .footertext1{font-family: 'GillSans-SemiBold';color: #410260;}
#FBPopUpJoinUS .footertext2{ color:#000; font-size:13px;font-family: 'GillSans-Light';}
#FBPopUpJoinUS .Regfooterlink a{color: #000000; text-decoration:underline}
#FBPopUpJoinUS .Regfooterlink a:hover{ color:#000000}
#FBPopUpJoinUS .Regfooterlink .Regfooterclr{ clear:both; display:block; content:"";}
#FBPopUpJoinUS #imgRegShowHidePassWord.ImgshowPw {content:url(/images/joinus_pass-icon.png);}
#FBPopUpJoinUS #imgRegShowHidePassWord{content:url(/images/joinus_passlock-icon.png?V=2); cursor:pointer; width:24px; height:19px}
#FBPopUpJoinUS .Regform .RegFtext{ position:relative}
#FBPopUpJoinUS .Regform .RegFtext .floating-label {position: absolute;pointer-events: none;top: 14px;left: 0px;transition: 0.2s ease all;font-family: 'NeutraText-Light'; color:#7e7e7e; font-size:18px;font-weight: 500;}
#FBPopUpJoinUS .Regform .RegFtext input:focus ~ .floating-label,#FBPopUpJoinUS .Regform .RegFtext input.activeinput ~ .floating-label{top: 0px;left: 0px;font-size: 10px;opacity: 1;font-family: 'GillSans'; color:#410166}
#FBPopUpJoinUS #imgRegShowHidePassWord{ position:absolute; right:0px; top:15px}
#FBPopUpJoinUS #imgRegShowHideUser{ position:absolute; right:0px; top:15px}
#FBPopUpJoinUS .Regform .RegFtext .msgErrorUserId {position: absolute;top: 0px;left: 0px;font-size: 10px;opacity: 1;font-family: 'GillSans'; color:#c01f2a}
#FBPopUpJoinUS .Regform .RegFtext input.activeinput{}
#FBPopUpJoinUS .Regform .sublabel{font-family: 'Avenir-Light'; color:#7e7e7e; font-size:13px; line-height:25px;font-weight: 300;}/*ST-2092*/
#FBPopUpJoinUS .Regform .greentext{color:#0d802e;}
#FBPopUpJoinUS .Regform .redtext{color:#c01f2a;}
#FBPopUpJoinUS .Regform .checkbox{ margin-left:0px;}
#FBPopUpJoinUS .mtb5{ margin-top:5px !important;margin-bottom: -5px !important;}
#FBPopUpJoinUS .Regform .RegFtext .astrick{ position:absolute; left:-19px; background:url(/images/joinus-astrick.png) no-repeat; width:15px; height:15px; margin-top:5px}
#FBPopUpJoinUS .Regform .sublabel.lblPwRquriment {color:#7e7e7e; letter-spacing: -0.4px;}
#FBPopUpJoinUS .Regform .sublabel1 {color:#7e7e7e}
#FBPopUpJoinUS .iamtext{ text-align: center !important; margin-top:18px}
#FBPopUpJoinUS .iamtext .iama{font-family: 'GillSans-SemiBold';color: #410166; font-size:16px; display:inline-block}
#FBPopUpJoinUS .iamtext .iamcompany{font-family: 'Avenir-Light';color: #000; font-size:16px; display:inline-block; margin:0 25px;}
#FBPopUpJoinUS .iamtext .iamindividual{font-family: 'Avenir-Heavy';color: #000; font-size:16px; display:inline-block; text-decoration:underline}

#FBPopUpRecovery .modal-content{border:2px solid #410166;width:411px; min-height:440px; margin:0 auto; background-color: #fafafa; border-radius:0px}
#FBPopUpRecovery .divRegPopUp_body{padding:18px 25px 34px; position:relative;font-family: 'GillSans-SemiBold'; color:#000;font-size: 24px;}
#FBPopUpRecovery .RegCloseXPopup{ position:absolute; right:15px; top:15px}
#FBPopUpRecovery .Regheading{font-size: 24px;color: #410166; line-height:30px;font-family: 'GillSans-SemiBold'; text-transform:uppercase; margin:19px auto 0px; text-align:center}
#FBPopUpRecovery .SubRegheading{font-size: 17px;color: #000000; line-height:23px;font-family: 'GillSans-Light'; margin:0px auto 20px; text-align:center}
#FBPopUpRecovery .Regform{ width:361px; margin:19px auto 0px; text-align:center}
#FBPopUpRecovery .Regform .signinbtn {display: block; background: #fff; border: 1px solid #57307a; border-radius: 26px; text-transform: uppercase; text-decoration: none; margin: 30px auto 0px; transition: all ease-out 0.5s; -webkit-transition: all ease-out 0.5s; -moz-transition: all ease-out 0.5s; font-family: 'GillSans-SemiBold';font-size: 22px;color: #410260 ; width:213px; height:40px; line-height:38px}
#FBPopUpRecovery .Regform .signinbtn:hover { background-color: #57307a; color: #FFF !important; transition: all ease-in 0.5s; -webkit-transition: all ease-in 0.5s; -moz-transition: all ease-in 0.5s; font-family: 'GillSans-SemiBold' !important; font-size: 22px;}

/*ST-2036*/
.divLayout2SecParent{width: calc(100% - 0px) !important;}
.divLayout2 .Hamburger, .divLayout2 .trashcan{margin-right:5px !important}
.divLayout2 .Hamburger{top: -24px !important;}
.divLayout2 .trashcan{right: -17px !important; top:18px !important}
.divLayout2SecParent.divLayout2SecSmall {width: calc(100% - 200px) !important;}
.divLayout2SecParent.divLayout2SecSmall .Hamburger{top: -15px !important;right: 10px !important;}
.divLayout2 .divLayout2SecSmall .trashcan{right: -9px !important;}
.divLayout2 .eraserForAllDivimg{}/*margin-left: -12px !important;*/
.divLayout2 .divLayout2SecSub .eraserForAllDivimg{}/*margin-left: -20px !important;*/
.divLayout2 .divLayout2SecSmall .trashcan{top: -2px !important; right:0px !important;margin-top: -17px!important;}
.divLayout2SecParent.divLayout2SecSmall .Hamburger{  margin-top:-15px !important;margin-right: 0px !important;right: 25px !important;top: -5px !important;}
.divLayout2 .divLayout2SecSmall .eraserForAllDivimg{}/*margin-left: -30px !important;*/
/*.divLayout2 .bb-custom-side.frederich_img1 .content_wrap_in{padding-top: 24pt !important;}*/
.divLayout2 .bb-custom-side.frederich_img1 .content_wrap_in .Hamburger,.divLayout2 .bb-custom-side.frederich_img1 .content_wrap_in .trashcan{position: absolute; right: 0px!important; top: 2px !important;}
.divLayout2 .bb-custom-side.frederich_img1 .content_wrap_in .trashcan{right: 0px!important; top: 45px !important;z-index: 99999 !important}
.divLayout2 .bb-custom-side.frederich_img_Left .content_wrap .Hamburger,.divLayout2 .bb-custom-side.frederich_img_Left .content_wrap .trashcan{position: absolute; right: 0px!important; top: 2px !important;}
.divLayout2 .bb-custom-side.frederich_img_Left .content_wrap .trashcan{right: 0px!important; top: 42px !important;z-index: 99999 !important}
.divLayout2 .bb-custom-side.frederich_img_Left .content_wrap .divLayout2SecSmall .Hamburger,.divLayout2 .bb-custom-side.frederich_img_Left .content_wrap .divLayout2SecSmall .trashcan{position: absolute; right: 22px!important; top: 18px !important;}
.divLayout2 .bb-custom-side.frederich_img_Left .content_wrap .divLayout2SecSmall .trashcan{right: -1px!important; top: 20px !important;z-index: 99999 !important}

#overlay { width: 100% !important; height: 100% !important; background-color: #fff; z-index: 99999; color: #000; text-align: center; font-size: 24px; filter: alpha(opacity=80); -moz-opacity: .8; opacity: .8; position: fixed}
#overlay img { position: absolute; top: 0; right: 0; left: 0; bottom: 0; margin: auto !important;  width: 150px !important;}