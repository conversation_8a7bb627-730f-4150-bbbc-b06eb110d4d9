import React from 'react';
import ComponentStyle from './models/ComponentStyle';
import { DefaultTheme } from './models/ThemeProvider';
import createWarnTooManyClasses from './utils/createWarnTooManyClasses';
interface ExoticComponentWithDisplayName<P = any> extends React.ExoticComponent<P> {
    defaultProps?: Partial<P>;
    displayName?: string;
}
export type OmitNever<T> = {
    [K in keyof T as T[K] extends never ? never : K]: T[K];
};
export type Runtime = 'web' | 'native';
export { DefaultTheme };
export type AnyComponent<P = any> = ExoticComponentWithDisplayName<P> | React.ComponentType<P>;
export type KnownTarget = keyof JSX.IntrinsicElements | AnyComponent;
export type WebTarget = string | KnownTarget;
export type NativeTarget = AnyComponent;
export type StyledTarget<R extends Runtime> = R extends 'web' ? WebTarget : NativeTarget;
export interface StyledOptions<R extends Runtime, Props extends object> {
    attrs?: Attrs<Props>[];
    componentId?: R extends 'web' ? string : never;
    displayName?: string;
    parentComponentId?: R extends 'web' ? string : never;
    shouldForwardProp?: ShouldForwardProp<R>;
}
export type Dict<T> = {
    [key: string]: T;
};
export interface DataAttributes {
    [key: `data-${string}`]: unknown;
}
export interface ExecutionProps {
    /**
     * Dynamically adjust the rendered component or HTML tag, e.g.
     * ```
     * const StyledButton = styled.button``
     *
     * <StyledButton as="a" href="/foo">
     *   I'm an anchor now
     * </StyledButton>
     * ```
     */
    as?: KnownTarget;
    forwardedAs?: KnownTarget;
    theme?: DefaultTheme;
}
/**
 * ExecutionProps but with `theme` required.
 */
export interface ExecutionContext extends ExecutionProps {
    theme: DefaultTheme;
}
export interface StyleFunction<Props extends object> {
    (executionContext: ExecutionContext & Props): Interpolation<Props>;
}
export type Interpolation<Props extends object> = StyleFunction<Props> | StyledObject<Props> | TemplateStringsArray | string | number | false | undefined | null | Keyframes | IStyledComponent<'web', any> | Interpolation<Props>[];
export type Attrs<Props extends object = object> = (ExecutionProps & Props) | ((props: ExecutionContext & Props) => Props);
export type RuleSet<Props extends object> = Interpolation<Props>[];
export type Styles<Props extends object> = TemplateStringsArray | StyledObject<Props> | StyleFunction<Props>;
export type NameGenerator = (hash: number) => string;
export interface StyleSheet {
    create: Function;
}
export interface Keyframes {
    id: string;
    name: string;
    rules: string;
}
export interface Flattener<Props extends object> {
    (chunks: Interpolation<Props>[], executionContext: Object | null | undefined, styleSheet: Object | null | undefined): Interpolation<Props>[];
}
export type FlattenerResult<Props extends object> = RuleSet<Props> | number | string | string[] | IStyledComponent<'web', any, any> | Keyframes;
export interface Stringifier {
    (css: string, selector?: string, prefix?: string, componentId?: string): string[];
    hash: string;
}
export interface ShouldForwardProp<R extends Runtime> {
    (prop: string, elementToBeCreated: StyledTarget<R>): boolean;
}
export interface CommonStatics<R extends Runtime, Props extends object> {
    attrs: Attrs<Props>[];
    target: StyledTarget<R>;
    shouldForwardProp?: ShouldForwardProp<R>;
}
export interface IStyledStatics<R extends Runtime, OuterProps extends object> extends CommonStatics<R, OuterProps> {
    componentStyle: R extends 'web' ? ComponentStyle : never;
    foldedComponentIds: R extends 'web' ? string : never;
    inlineStyle: R extends 'native' ? InstanceType<IInlineStyleConstructor<OuterProps>> : never;
    target: StyledTarget<R>;
    styledComponentId: R extends 'web' ? string : never;
    warnTooManyClasses?: R extends 'web' ? ReturnType<typeof createWarnTooManyClasses> : never;
}
/**
 * Used by PolymorphicComponent to define prop override cascading order.
 */
export type PolymorphicComponentProps<R extends Runtime, E extends StyledTarget<R>, P extends object> = Omit<E extends KnownTarget ? Omit<P, keyof React.ComponentPropsWithRef<E>> & React.ComponentPropsWithRef<E> : P, 'as' | 'theme'> & {
    as?: P extends {
        as?: string | AnyComponent;
    } ? P['as'] : E;
    theme?: DefaultTheme;
};
/**
 * This type forms the signature for a forwardRef-enabled component that accepts
 * the "as" prop to dynamically change the underlying rendered JSX. The interface will
 * automatically attempt to extract props from the given rendering target to
 * get proper typing for any specialized props in the target component.
 */
export interface PolymorphicComponent<R extends Runtime, P extends object, FallbackComponent extends StyledTarget<R>> extends React.ForwardRefExoticComponent<P> {
    <E extends StyledTarget<R> = FallbackComponent>(props: PolymorphicComponentProps<R, E, P>): React.ReactElement | null;
}
export interface IStyledComponent<R extends Runtime, Target extends StyledTarget<R>, Props extends object = Target extends KnownTarget ? React.ComponentPropsWithRef<Target> : object> extends PolymorphicComponent<R, Props, Target>, IStyledStatics<R, Props> {
    defaultProps?: Partial<(Target extends KnownTarget ? ExecutionProps & Omit<React.ComponentProps<Target>, keyof ExecutionProps> : ExecutionProps) & Props>;
    toString: () => string;
}
export interface IStyledComponentFactory<R extends Runtime, Target extends StyledTarget<R>, OuterProps extends object, OuterStatics extends object = object> {
    <Props extends object = object, Statics extends object = object>(target: Target, options: StyledOptions<R, OuterProps & Props>, rules: RuleSet<OuterProps & Props>): IStyledComponent<R, Target, OuterProps & Props> & OuterStatics & Statics;
}
export interface IInlineStyleConstructor<Props extends object> {
    new (rules: RuleSet<Props>): IInlineStyle<Props>;
}
export interface IInlineStyle<Props extends object> {
    rules: RuleSet<Props>;
    generateStyleObject(executionContext: Object): Object;
}
export interface StyledObject<Props extends object> {
    [key: string]: string | number | StyleFunction<Props> | StyledObject<Props> | undefined;
}
/**
 * The `css` prop is not declared by default in the types as it would cause `css` to be present
 * on the types of anything that uses styled-components indirectly, even if they do not use the
 * babel plugin.
 *
 * To enable support for the `css` prop in TypeScript, create a `styled-components.d.ts` file in
 * your project source with the following contents:
 *
 * ```ts
 * import type { CSSProp } from "styled-components";
 *
 * declare module "react" {
 *  interface Attributes {
 *    css?: CSSProp;
 *  }
 * }
 * ```
 *
 * In order to get accurate typings for `props.theme` in `css` interpolations, see
 * {@link DefaultTheme}.
 */
export type CSSProp = RuleSet<any>;
export type NoInfer<T> = [T][T extends any ? 0 : never];
export type SubsetOnly<A, B> = {
    [K in keyof A]: K extends keyof B ? A[K] : never;
};
