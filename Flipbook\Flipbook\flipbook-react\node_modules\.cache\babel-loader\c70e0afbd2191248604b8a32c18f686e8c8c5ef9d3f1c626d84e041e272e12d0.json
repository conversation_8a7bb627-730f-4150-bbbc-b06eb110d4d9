{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10;import React from'react';import styled from'styled-components';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const HeaderContainer=styled.header(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 10px 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  position: relative;\\n  z-index: 100;\\n\"])));const LeftSection=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  gap: 30px;\\n\"])));const Logo=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  font-family: 'Brush Script MT', cursive;\\n  font-size: 28px;\\n  font-weight: bold;\\n  color: white;\\n  text-decoration: none;\\n  cursor: pointer;\\n  \\n  &:hover {\\n    opacity: 0.9;\\n  }\\n\"])));const Navigation=styled.nav(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 25px;\\n\"])));const NavItem=styled.a(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  color: white;\\n  text-decoration: none;\\n  font-size: 14px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  padding: 8px 12px;\\n  border-radius: 4px;\\n  transition: background-color 0.3s ease;\\n  cursor: pointer;\\n\\n  &:hover {\\n    background-color: rgba(255, 255, 255, 0.1);\\n  }\\n\"])));const RightSection=styled.div(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n\"])));const IconButton=styled.button(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  background: none;\\n  border: none;\\n  color: white;\\n  font-size: 18px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 4px;\\n  transition: background-color 0.3s ease;\\n\\n  &:hover {\\n    background-color: rgba(255, 255, 255, 0.1);\\n  }\\n\"])));const UserSection=styled.div(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n\"])));const UserAvatar=styled.div(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: bold;\\n  font-size: 14px;\\n  color: white;\\n  cursor: pointer;\\n  transition: transform 0.3s ease;\\n\\n  &:hover {\\n    transform: scale(1.1);\\n  }\\n\"])));const UserInfo=styled.div(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n\"])));const SignInText=styled.span(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.9);\\n  margin-bottom: 2px;\\n\"])));const JoinUsText=styled.span(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  font-size: 11px;\\n  color: rgba(255, 255, 255, 0.7);\\n\"])));const Header=()=>{return/*#__PURE__*/_jsxs(HeaderContainer,{children:[/*#__PURE__*/_jsxs(LeftSection,{children:[/*#__PURE__*/_jsx(Logo,{children:\"Flipbook\"}),/*#__PURE__*/_jsxs(Navigation,{children:[/*#__PURE__*/_jsx(NavItem,{href:\"#file\",children:\"FILE\"}),/*#__PURE__*/_jsx(NavItem,{href:\"#edit\",children:\"EDIT\"}),/*#__PURE__*/_jsx(NavItem,{href:\"#share\",children:\"SHARE!\"}),/*#__PURE__*/_jsx(NavItem,{href:\"#help\",children:\"HELP\"})]})]}),/*#__PURE__*/_jsxs(RightSection,{children:[/*#__PURE__*/_jsx(IconButton,{title:\"Tools\",children:\"\\uD83D\\uDD27\"}),/*#__PURE__*/_jsxs(UserSection,{children:[/*#__PURE__*/_jsx(UserAvatar,{children:\"FR\"}),/*#__PURE__*/_jsxs(UserInfo,{children:[/*#__PURE__*/_jsx(SignInText,{children:\"Sign In\"}),/*#__PURE__*/_jsx(JoinUsText,{children:\"Join Us\"})]})]})]})]});};export default Header;", "map": {"version": 3, "names": ["React", "styled", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header", "_templateObject", "_taggedTemplateLiteral", "LeftSection", "div", "_templateObject2", "Logo", "_templateObject3", "Navigation", "nav", "_templateObject4", "NavItem", "a", "_templateObject5", "RightSection", "_templateObject6", "IconButton", "button", "_templateObject7", "UserSection", "_templateObject8", "UserAvatar", "_templateObject9", "UserInfo", "_templateObject0", "SignInText", "span", "_templateObject1", "JoinUsText", "_templateObject10", "Header", "children", "href", "title"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/LandingPage/Header.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\n\nconst HeaderContainer = styled.header`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: relative;\n  z-index: 100;\n`;\n\nconst LeftSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 30px;\n`;\n\nconst Logo = styled.div`\n  font-family: 'Brush Script MT', cursive;\n  font-size: 28px;\n  font-weight: bold;\n  color: white;\n  text-decoration: none;\n  cursor: pointer;\n  \n  &:hover {\n    opacity: 0.9;\n  }\n`;\n\nconst Navigation = styled.nav`\n  display: flex;\n  gap: 25px;\n`;\n\nconst NavItem = styled.a`\n  color: white;\n  text-decoration: none;\n  font-size: 14px;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  padding: 8px 12px;\n  border-radius: 4px;\n  transition: background-color 0.3s ease;\n  cursor: pointer;\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n\nconst RightSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 15px;\n`;\n\nconst IconButton = styled.button`\n  background: none;\n  border: none;\n  color: white;\n  font-size: 18px;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 4px;\n  transition: background-color 0.3s ease;\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n\nconst UserSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 10px;\n`;\n\nconst UserAvatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 14px;\n  color: white;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n\n  &:hover {\n    transform: scale(1.1);\n  }\n`;\n\nconst UserInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n`;\n\nconst SignInText = styled.span`\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n`;\n\nconst JoinUsText = styled.span`\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.7);\n`;\n\nconst Header: React.FC = () => {\n  return (\n    <HeaderContainer>\n      <LeftSection>\n        <Logo>Flipbook</Logo>\n        <Navigation>\n          <NavItem href=\"#file\">FILE</NavItem>\n          <NavItem href=\"#edit\">EDIT</NavItem>\n          <NavItem href=\"#share\">SHARE!</NavItem>\n          <NavItem href=\"#help\">HELP</NavItem>\n        </Navigation>\n      </LeftSection>\n      \n      <RightSection>\n        <IconButton title=\"Tools\">\n          🔧\n        </IconButton>\n        \n        <UserSection>\n          <UserAvatar>\n            FR\n          </UserAvatar>\n          <UserInfo>\n            <SignInText>Sign In</SignInText>\n            <JoinUsText>Join Us</JoinUsText>\n          </UserInfo>\n        </UserSection>\n      </RightSection>\n    </HeaderContainer>\n  );\n};\n\nexport default Header;\n"], "mappings": "6WAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvC,KAAM,CAAAC,eAAe,CAAGL,MAAM,CAACM,MAAM,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,sRAUpC,CAED,KAAM,CAAAC,WAAW,CAAGT,MAAM,CAACU,GAAG,CAAAC,gBAAA,GAAAA,gBAAA,CAAAH,sBAAA,kEAI7B,CAED,KAAM,CAAAI,IAAI,CAAGZ,MAAM,CAACU,GAAG,CAAAG,gBAAA,GAAAA,gBAAA,CAAAL,sBAAA,wMAWtB,CAED,KAAM,CAAAM,UAAU,CAAGd,MAAM,CAACe,GAAG,CAAAC,gBAAA,GAAAA,gBAAA,CAAAR,sBAAA,0CAG5B,CAED,KAAM,CAAAS,OAAO,CAAGjB,MAAM,CAACkB,CAAC,CAAAC,gBAAA,GAAAA,gBAAA,CAAAX,sBAAA,uUAevB,CAED,KAAM,CAAAY,YAAY,CAAGpB,MAAM,CAACU,GAAG,CAAAW,gBAAA,GAAAA,gBAAA,CAAAb,sBAAA,kEAI9B,CAED,KAAM,CAAAc,UAAU,CAAGtB,MAAM,CAACuB,MAAM,CAAAC,gBAAA,GAAAA,gBAAA,CAAAhB,sBAAA,iQAa/B,CAED,KAAM,CAAAiB,WAAW,CAAGzB,MAAM,CAACU,GAAG,CAAAgB,gBAAA,GAAAA,gBAAA,CAAAlB,sBAAA,kEAI7B,CAED,KAAM,CAAAmB,UAAU,CAAG3B,MAAM,CAACU,GAAG,CAAAkB,gBAAA,GAAAA,gBAAA,CAAApB,sBAAA,qWAiB5B,CAED,KAAM,CAAAqB,QAAQ,CAAG7B,MAAM,CAACU,GAAG,CAAAoB,gBAAA,GAAAA,gBAAA,CAAAtB,sBAAA,mFAI1B,CAED,KAAM,CAAAuB,UAAU,CAAG/B,MAAM,CAACgC,IAAI,CAAAC,gBAAA,GAAAA,gBAAA,CAAAzB,sBAAA,yFAI7B,CAED,KAAM,CAAA0B,UAAU,CAAGlC,MAAM,CAACgC,IAAI,CAAAG,iBAAA,GAAAA,iBAAA,CAAA3B,sBAAA,kEAG7B,CAED,KAAM,CAAA4B,MAAgB,CAAGA,CAAA,GAAM,CAC7B,mBACEhC,KAAA,CAACC,eAAe,EAAAgC,QAAA,eACdjC,KAAA,CAACK,WAAW,EAAA4B,QAAA,eACVnC,IAAA,CAACU,IAAI,EAAAyB,QAAA,CAAC,UAAQ,CAAM,CAAC,cACrBjC,KAAA,CAACU,UAAU,EAAAuB,QAAA,eACTnC,IAAA,CAACe,OAAO,EAACqB,IAAI,CAAC,OAAO,CAAAD,QAAA,CAAC,MAAI,CAAS,CAAC,cACpCnC,IAAA,CAACe,OAAO,EAACqB,IAAI,CAAC,OAAO,CAAAD,QAAA,CAAC,MAAI,CAAS,CAAC,cACpCnC,IAAA,CAACe,OAAO,EAACqB,IAAI,CAAC,QAAQ,CAAAD,QAAA,CAAC,QAAM,CAAS,CAAC,cACvCnC,IAAA,CAACe,OAAO,EAACqB,IAAI,CAAC,OAAO,CAAAD,QAAA,CAAC,MAAI,CAAS,CAAC,EAC1B,CAAC,EACF,CAAC,cAEdjC,KAAA,CAACgB,YAAY,EAAAiB,QAAA,eACXnC,IAAA,CAACoB,UAAU,EAACiB,KAAK,CAAC,OAAO,CAAAF,QAAA,CAAC,cAE1B,CAAY,CAAC,cAEbjC,KAAA,CAACqB,WAAW,EAAAY,QAAA,eACVnC,IAAA,CAACyB,UAAU,EAAAU,QAAA,CAAC,IAEZ,CAAY,CAAC,cACbjC,KAAA,CAACyB,QAAQ,EAAAQ,QAAA,eACPnC,IAAA,CAAC6B,UAAU,EAAAM,QAAA,CAAC,SAAO,CAAY,CAAC,cAChCnC,IAAA,CAACgC,UAAU,EAAAG,QAAA,CAAC,SAAO,CAAY,CAAC,EACxB,CAAC,EACA,CAAC,EACF,CAAC,EACA,CAAC,CAEtB,CAAC,CAED,cAAe,CAAAD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}