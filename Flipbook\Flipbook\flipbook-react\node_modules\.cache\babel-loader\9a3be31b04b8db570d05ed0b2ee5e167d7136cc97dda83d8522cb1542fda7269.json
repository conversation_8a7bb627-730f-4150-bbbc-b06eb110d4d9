{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6;import React,{useState}from'react';import styled from'styled-components';import Header from'./Header';import Sidebar from'./Sidebar';import FlipbookGrid from'./FlipbookGrid';import CreateFlipbookModal from'./CreateFlipbookModal';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LandingPageContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  background-color: #f5f5f5;\\n\"])));const MainContent=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex: 1;\\n  overflow: hidden;\\n\"])));const ContentArea=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  flex: 1;\\n  padding: 20px;\\n  overflow-y: auto;\\n  background-color: #ffffff;\\n\"])));const Section=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  margin-bottom: 40px;\\n\"])));const SectionTitle=styled.h2(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  font-size: 18px;\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 20px;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n\"])));const CertificationBadge=styled.div(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  position: fixed;\\n  top: 80px;\\n  right: 20px;\\n  width: 100px;\\n  height: 100px;\\n  background-image: url('/images/certification-badge.png');\\n  background-size: contain;\\n  background-repeat: no-repeat;\\n  z-index: 10;\\n\"])));const LandingPage=()=>{const[isCreateModalOpen,setIsCreateModalOpen]=useState(false);// Mock data - replace with actual API calls\nconst myFlipbooks=[// User will see empty state initially\n];const inspirationFlipbooks=[{id:1,title:'Original Flipbook',thumbnail:'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop',isInspiration:true},{id:2,title:'Architecture Portfolio',thumbnail:'https://images.unsplash.com/photo-1487958449943-2429e8be8625?w=400&h=300&fit=crop',isInspiration:true},{id:3,title:'Design Showcase',thumbnail:'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop',isInspiration:true},{id:4,title:'Creative Portfolio',thumbnail:'https://images.unsplash.com/photo-1541746972996-4e0b0f93e586?w=400&h=300&fit=crop',isInspiration:true}];const handleCreateNew=()=>{setIsCreateModalOpen(true);};const handleCloseModal=()=>{setIsCreateModalOpen(false);};const handleCreateFlipbook=title=>{// Handle flipbook creation\nconsole.log('Creating flipbook:',title);setIsCreateModalOpen(false);};return/*#__PURE__*/_jsxs(LandingPageContainer,{children:[/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsxs(MainContent,{children:[/*#__PURE__*/_jsx(Sidebar,{}),/*#__PURE__*/_jsxs(ContentArea,{children:[/*#__PURE__*/_jsxs(Section,{children:[/*#__PURE__*/_jsx(SectionTitle,{children:\"My Flipbooks\"}),/*#__PURE__*/_jsx(FlipbookGrid,{flipbooks:myFlipbooks,showCreateNew:true,onCreateNew:handleCreateNew,onFlipbookClick:flipbook=>console.log('Open flipbook:',flipbook),onFlipbookEdit:flipbook=>console.log('Edit flipbook:',flipbook)})]}),/*#__PURE__*/_jsxs(Section,{children:[/*#__PURE__*/_jsx(SectionTitle,{children:\"Inspiration\"}),/*#__PURE__*/_jsx(FlipbookGrid,{flipbooks:inspirationFlipbooks,onFlipbookClick:flipbook=>console.log('Preview flipbook:',flipbook),onFlipbookCopy:flipbook=>console.log('Copy flipbook:',flipbook)})]})]})]}),/*#__PURE__*/_jsx(CertificationBadge,{}),isCreateModalOpen&&/*#__PURE__*/_jsx(CreateFlipbookModal,{onClose:handleCloseModal,onCreate:handleCreateFlipbook})]});};export default LandingPage;", "map": {"version": 3, "names": ["React", "useState", "styled", "Header", "Sidebar", "FlipbookGrid", "CreateFlipbookModal", "jsx", "_jsx", "jsxs", "_jsxs", "LandingPageContainer", "div", "_templateObject", "_taggedTemplateLiteral", "MainContent", "_templateObject2", "ContentArea", "_templateObject3", "Section", "_templateObject4", "SectionTitle", "h2", "_templateObject5", "CertificationBadge", "_templateObject6", "LandingPage", "isCreateModalOpen", "setIsCreateModalOpen", "myFlipbooks", "inspirationFlipbooks", "id", "title", "thumbnail", "isInspiration", "handleCreateNew", "handleCloseModal", "handleCreateFlipbook", "console", "log", "children", "flipbooks", "showCreateNew", "onCreateNew", "onFlipbookClick", "flipbook", "onFlipbookEdit", "onFlipbookCopy", "onClose", "onCreate"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/LandingPage/LandingPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport Header from './Header';\nimport Sidebar from './Sidebar';\nimport FlipbookGrid from './FlipbookGrid';\nimport CreateFlipbookModal from './CreateFlipbookModal';\n\nconst LandingPageContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #f5f5f5;\n`;\n\nconst MainContent = styled.div`\n  display: flex;\n  flex: 1;\n  overflow: hidden;\n`;\n\nconst ContentArea = styled.div`\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n  background-color: #ffffff;\n`;\n\nconst Section = styled.div`\n  margin-bottom: 40px;\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: 18px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 20px;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n`;\n\n\n\nconst CertificationBadge = styled.div`\n  position: fixed;\n  top: 80px;\n  right: 20px;\n  width: 100px;\n  height: 100px;\n  background-image: url('/images/certification-badge.png');\n  background-size: contain;\n  background-repeat: no-repeat;\n  z-index: 10;\n`;\n\ninterface Flipbook {\n  id: number;\n  title: string;\n  thumbnail: string;\n  isInspiration?: boolean;\n}\n\nconst LandingPage: React.FC = () => {\n  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);\n  \n  // Mock data - replace with actual API calls\n  const myFlipbooks: Flipbook[] = [\n    // User will see empty state initially\n  ];\n\n  const inspirationFlipbooks: Flipbook[] = [\n    {\n      id: 1,\n      title: 'Original Flipbook',\n      thumbnail: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop',\n      isInspiration: true\n    },\n    {\n      id: 2,\n      title: 'Architecture Portfolio',\n      thumbnail: 'https://images.unsplash.com/photo-1487958449943-2429e8be8625?w=400&h=300&fit=crop',\n      isInspiration: true\n    },\n    {\n      id: 3,\n      title: 'Design Showcase',\n      thumbnail: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop',\n      isInspiration: true\n    },\n    {\n      id: 4,\n      title: 'Creative Portfolio',\n      thumbnail: 'https://images.unsplash.com/photo-1541746972996-4e0b0f93e586?w=400&h=300&fit=crop',\n      isInspiration: true\n    }\n  ];\n\n  const handleCreateNew = () => {\n    setIsCreateModalOpen(true);\n  };\n\n  const handleCloseModal = () => {\n    setIsCreateModalOpen(false);\n  };\n\n  const handleCreateFlipbook = (title: string) => {\n    // Handle flipbook creation\n    console.log('Creating flipbook:', title);\n    setIsCreateModalOpen(false);\n  };\n\n  return (\n    <LandingPageContainer>\n      <Header />\n      <MainContent>\n        <Sidebar />\n        <ContentArea>\n          <Section>\n            <SectionTitle>My Flipbooks</SectionTitle>\n            <FlipbookGrid\n              flipbooks={myFlipbooks}\n              showCreateNew={true}\n              onCreateNew={handleCreateNew}\n              onFlipbookClick={(flipbook) => console.log('Open flipbook:', flipbook)}\n              onFlipbookEdit={(flipbook) => console.log('Edit flipbook:', flipbook)}\n            />\n          </Section>\n\n          <Section>\n            <SectionTitle>Inspiration</SectionTitle>\n            <FlipbookGrid\n              flipbooks={inspirationFlipbooks}\n              onFlipbookClick={(flipbook) => console.log('Preview flipbook:', flipbook)}\n              onFlipbookCopy={(flipbook) => console.log('Copy flipbook:', flipbook)}\n            />\n          </Section>\n        </ContentArea>\n      </MainContent>\n      \n      <CertificationBadge />\n      \n      {isCreateModalOpen && (\n        <CreateFlipbookModal\n          onClose={handleCloseModal}\n          onCreate={handleCreateFlipbook}\n        />\n      )}\n    </LandingPageContainer>\n  );\n};\n\nexport default LandingPage;\n"], "mappings": "sQAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,MAAM,KAAM,UAAU,CAC7B,MAAO,CAAAC,OAAO,KAAM,WAAW,CAC/B,MAAO,CAAAC,YAAY,KAAM,gBAAgB,CACzC,MAAO,CAAAC,mBAAmB,KAAM,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,KAAM,CAAAC,oBAAoB,CAAGT,MAAM,CAACU,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,uGAKtC,CAED,KAAM,CAAAC,WAAW,CAAGb,MAAM,CAACU,GAAG,CAAAI,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,6DAI7B,CAED,KAAM,CAAAG,WAAW,CAAGf,MAAM,CAACU,GAAG,CAAAM,gBAAA,GAAAA,gBAAA,CAAAJ,sBAAA,2FAK7B,CAED,KAAM,CAAAK,OAAO,CAAGjB,MAAM,CAACU,GAAG,CAAAQ,gBAAA,GAAAA,gBAAA,CAAAN,sBAAA,kCAEzB,CAED,KAAM,CAAAO,YAAY,CAAGnB,MAAM,CAACoB,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAT,sBAAA,iJAO7B,CAID,KAAM,CAAAU,kBAAkB,CAAGtB,MAAM,CAACU,GAAG,CAAAa,gBAAA,GAAAA,gBAAA,CAAAX,sBAAA,0OAUpC,CASD,KAAM,CAAAY,WAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CAEjE;AACA,KAAM,CAAA4B,WAAuB,CAAG,CAC9B;AAAA,CACD,CAED,KAAM,CAAAC,oBAAgC,CAAG,CACvC,CACEC,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,mBAAmB,CAC1BC,SAAS,CAAE,mFAAmF,CAC9FC,aAAa,CAAE,IACjB,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,wBAAwB,CAC/BC,SAAS,CAAE,mFAAmF,CAC9FC,aAAa,CAAE,IACjB,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,iBAAiB,CACxBC,SAAS,CAAE,gFAAgF,CAC3FC,aAAa,CAAE,IACjB,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,oBAAoB,CAC3BC,SAAS,CAAE,mFAAmF,CAC9FC,aAAa,CAAE,IACjB,CAAC,CACF,CAED,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5BP,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAQ,gBAAgB,CAAGA,CAAA,GAAM,CAC7BR,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAS,oBAAoB,CAAIL,KAAa,EAAK,CAC9C;AACAM,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAEP,KAAK,CAAC,CACxCJ,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAC,CAED,mBACElB,KAAA,CAACC,oBAAoB,EAAA6B,QAAA,eACnBhC,IAAA,CAACL,MAAM,GAAE,CAAC,cACVO,KAAA,CAACK,WAAW,EAAAyB,QAAA,eACVhC,IAAA,CAACJ,OAAO,GAAE,CAAC,cACXM,KAAA,CAACO,WAAW,EAAAuB,QAAA,eACV9B,KAAA,CAACS,OAAO,EAAAqB,QAAA,eACNhC,IAAA,CAACa,YAAY,EAAAmB,QAAA,CAAC,cAAY,CAAc,CAAC,cACzChC,IAAA,CAACH,YAAY,EACXoC,SAAS,CAAEZ,WAAY,CACvBa,aAAa,CAAE,IAAK,CACpBC,WAAW,CAAER,eAAgB,CAC7BS,eAAe,CAAGC,QAAQ,EAAKP,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAEM,QAAQ,CAAE,CACvEC,cAAc,CAAGD,QAAQ,EAAKP,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAEM,QAAQ,CAAE,CACvE,CAAC,EACK,CAAC,cAEVnC,KAAA,CAACS,OAAO,EAAAqB,QAAA,eACNhC,IAAA,CAACa,YAAY,EAAAmB,QAAA,CAAC,aAAW,CAAc,CAAC,cACxChC,IAAA,CAACH,YAAY,EACXoC,SAAS,CAAEX,oBAAqB,CAChCc,eAAe,CAAGC,QAAQ,EAAKP,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAEM,QAAQ,CAAE,CAC1EE,cAAc,CAAGF,QAAQ,EAAKP,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAEM,QAAQ,CAAE,CACvE,CAAC,EACK,CAAC,EACC,CAAC,EACH,CAAC,cAEdrC,IAAA,CAACgB,kBAAkB,GAAE,CAAC,CAErBG,iBAAiB,eAChBnB,IAAA,CAACF,mBAAmB,EAClB0C,OAAO,CAAEZ,gBAAiB,CAC1Ba,QAAQ,CAAEZ,oBAAqB,CAChC,CACF,EACmB,CAAC,CAE3B,CAAC,CAED,cAAe,CAAAX,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}