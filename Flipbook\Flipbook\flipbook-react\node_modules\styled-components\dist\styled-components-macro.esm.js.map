{"version": 3, "file": "styled-components-macro.esm.js", "sources": ["../src/macro/index.js"], "sourcesContent": ["// @flow\nimport { addDefault, addNamed } from '@babel/helper-module-imports';\nimport traverse from '@babel/traverse';\nimport { createMacro } from 'babel-plugin-macros';\nimport babelPlugin from 'babel-plugin-styled-components';\n\nfunction styledComponentsMacro({\n  references,\n  state,\n  babel: { types: t },\n  config: { importModuleName = 'styled-components', ...config } = {},\n}) {\n  const program = state.file.path;\n\n  // FIRST STEP : replace `styled-components/macro` by `styled-components\n  // references looks like this\n  // { default: [path, path], css: [path], ... }\n  let customImportName;\n  Object.keys(references).forEach(refName => {\n    // generate new identifier\n    let id;\n    if (refName === 'default') {\n      id = addDefault(program, importModuleName, { nameHint: 'styled' });\n      customImportName = id;\n    } else {\n      id = addNamed(program, refName, importModuleName, { nameHint: refName });\n    }\n\n    // update references with the new identifiers\n    references[refName].forEach(referencePath => {\n      // eslint-disable-next-line no-param-reassign\n      referencePath.node.name = id.name;\n    });\n  });\n\n  // SECOND STEP : apply babel-plugin-styled-components to the file\n  const stateWithOpts = {\n    ...state,\n    opts: {\n      ...config,\n      topLevelImportPaths: (config.topLevelImportPaths || []).concat(importModuleName),\n    },\n    customImportName,\n  };\n  traverse(program.parent, babelPlugin({ types: t }).visitor, undefined, stateWithOpts);\n}\n\nexport default createMacro(styledComponentsMacro, {\n  configName: 'styledComponents',\n});\n"], "names": ["createMacro", "customImportName", "references", "state", "t", "babel", "types", "config", "importModuleName", "program", "file", "path", "Object", "keys", "for<PERSON>ach", "refName", "id", "addDefault", "nameHint", "addNamed", "referencePath", "node", "name", "stateWithOpts", "opts", "topLevelImportPaths", "concat", "traverse", "parent", "babelPlugin", "visitor", "undefined", "config<PERSON><PERSON>"], "mappings": "sZA+CA,MAAeA,GAzCf,gBAWMC,EAVJC,IAAAA,WACAC,IAAAA,MACgBC,IAAhBC,MAASC,UACTC,uBAAgE,MAAtDC,iBAAAA,aAAmB,sBAAwBD,+JAE/CE,EAAUN,EAAMO,KAAKC,KAM3BC,OAAOC,KAAKX,GAAYY,SAAQ,SAAAC,OAE1BC,EACY,YAAZD,GACFC,EAAKC,EAAWR,EAASD,EAAkB,CAAEU,SAAU,WACvDjB,EAAmBe,GAEnBA,EAAKG,EAASV,EAASM,EAASP,EAAkB,CAAEU,SAAUH,IAIhEb,EAAWa,GAASD,SAAQ,SAAAM,GAE1BA,EAAcC,KAAKC,KAAON,EAAGM,eAK3BC,OACDpB,GACHqB,UACKjB,GACHkB,qBAAsBlB,EAAOkB,qBAAuB,IAAIC,OAAOlB,KAEjEP,iBAAAA,IAEF0B,EAASlB,EAAQmB,OAAQC,EAAY,CAAEvB,MAAOF,IAAK0B,aAASC,EAAWR,KAGvB,CAChDS,WAAY"}