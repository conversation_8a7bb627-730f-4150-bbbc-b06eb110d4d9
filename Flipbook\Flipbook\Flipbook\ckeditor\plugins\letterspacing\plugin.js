CKEDITOR.plugins.add( 'letterspacing', {
  requires: ['richcombo'],
  init: function( editor ) {
    var config = editor.config,
      lang = editor.lang.format;
    var trackings = [];
    var CkeLetterspacingVal;
    config.allowedContent = 'span'; //There may be a better way to do this.
    //ST-1668,BV
    trackings[0] = ["+50 px", "50px"];
    trackings[1] = ["+40 px", "40px"];
    trackings[2] = ["+30 px", "30px"];
    trackings[3] = ["+20 px", "20px"];
    trackings[4] = ["+15 px", "15px"];
    trackings[5] = ["+10 px", "10px"];
    trackings[6] = ["+9 px", "9px"];
    trackings[7] = ["+8 px", "8px"];
    trackings[8] = ["+7 px", "7px"];
    trackings[9] = ["+6 px", "6px"];
    trackings[10] = ["+5 px", "5px"];
    trackings[11] = ["+4 px", "4px"];
    trackings[12] = ["+3 px", "3px"];
    trackings[13] = ["+2 px", "2px"];
    trackings[14] = ["+1 px", "1px"];
    trackings[15] = ["0 px Standard", "0px"]; //ST-2049
    trackings[16] = ["-1 px", "-1px"];
    trackings[17] = ["-2 px", "-2px"];
    trackings[18] = ["-3 px", "-3px"];
    trackings[19] = ["-4 px", "-4px"];
    trackings[20] = ["-5 px", "-5px"];
    trackings[21] = ["-6 px", "-6px"];
    trackings[22] = ["-7 px", "-7px"];
    trackings[23] = ["-8 px", "-8px"];
    trackings[24] = ["-9 px", "-9px"];
    trackings[25] = ["-10 px", "-10px"];
    trackings[26] = ["-15 px", "-15px"];
    trackings[27] = ["-20 px", "-20px"];
    trackings[28] = ["-30 px", "-30px"];
    trackings[29] = ["-40 px", "-40px"];
    trackings[30] = ["-50 px", "-50px"];
    /*ST-1354*/
    //for (var i = -50; i < 51; i++) {
    //  trackings.push(String(i) + 'px');
    //}
     //ST-1480
      var styles = {};
      for (var this_letting in trackings) {
          var ckeLetterVal = $(trackings[this_letting])
          var vars = {}
          if (ckeLetterVal[1] != undefined) {
              vars["size"] = ckeLetterVal[1];
              styles[ckeLetterVal[1]] = new CKEDITOR.style(CKEDITOR.config.letterspacing_style, vars);
              styles[ckeLetterVal[1]]._.definition.name = ckeLetterVal[1];
          }
          else {
              vars["size"] = ckeLetterVal[0];
              styles[ckeLetterVal[0]] = new CKEDITOR.style(CKEDITOR.config.letterspacing_style, vars);
              styles[ckeLetterVal[0]]._.definition.name = ckeLetterVal[0];
          }
      }
    
    editor.ui.addRichCombo('letterspacing', {
      label: '0 Kerning',// 'Text Spacing' //ST-1688
      title: 'Change letter-spacing',
      toolbar: 'custgrp,' ,
      voiceLabel: 'Change letter-spacing',
      className: 'cke_format ckeKerningClass',
      multiSelect: false,

       /*panel: {
          css: [CKEDITOR.getUrl(CKEDITOR.skin.getPath('ckeKerning') + 'ckeKerning.css') ]
      },ST-1713*/
	  panel: {
          css: [config.contentsCss, CKEDITOR.getUrl(CKEDITOR.skin.getPath('editor') + 'editor.css'), CKEDITOR.getUrl(CKEDITOR.skin.getPath('ckeKerning') + 'ckeKerning.css') ]
      },

      init: function() {
         this.startGroup('Kerning');
          
	//ST-1668,BV
         for (var this_letting in trackings) {

             var ckeLetterVal = $(trackings[this_letting])
             if (ckeLetterVal[1] != undefined) {
                 this.add(ckeLetterVal[1], ckeLetterVal[0], ckeLetterVal[1]);
             }
             else {
                 this.add(ckeLetterVal[0], ckeLetterVal[0], ckeLetterVal[0]);
             }
            
      }
      
      },

      onClick: function (value) {
          debugger;
      CkeLetterspacingVal = value;//ST-1354
      editor.focus();
      editor.fire('saveSnapshot'); 
      var ep = editor.elementPath();      
          //setTimeout(function () { this.setValue(value); }, 200) //ST-1354
          setTimeout(function () { this.label = (value + 'Kerning '); }, 200) //ST-1354
          //ST-1354
          SetCkeKerningValueDefault(CkeLetterspacingVal);
          editor.fire('saveSnapshot');
//ST-1480
      var style = new CKEDITOR.style({ styles: { 'letter-spacing': value } });
      editor[style.checkActive(ep) ? 'removeStyle' : 'applyStyle'](style);
         
          //this.setValue(value);
        },
      //ST-1480
        onRender: function () {
            editor.on('selectionChange', function (ev) {
                var currentValue = this.getValue();
                var elementPath = ev.data.path, elements = elementPath.elements;
                for (var i = 0, element; i < elements.length; i++) {
                    element = elements[i];
                    for (var value in styles) {
                        if (styles[value].checkElementMatch(element, true, editor)) {
                            if (value != currentValue) {
                                SetCkeKerningValueDefault(value);
                            }
                            this.setValue(value);
                            SetCkeKerningValueDefault(value);
                            return;
                        }
                    }
                }
                
                this.setValue("0px");
                SetCkeKerningValueDefault("0px");
            }, this);
        }    
    });
  }
});
//ST-1480
CKEDITOR.config.letterspacing_style = {
    element: 'span',
    styles: { 'letter-spacing': '#(size)' },
    overrides: [{
        element: 'letter-spacing', attributes: { 'size': null }
    }]
};
