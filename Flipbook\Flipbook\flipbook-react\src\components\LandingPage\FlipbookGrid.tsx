import React from 'react';
import styled from 'styled-components';

const GridContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  padding: 20px 0;
`;

const FlipbookCard = styled.div`
  width: 200px;
  height: 250px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: white;
  position: relative;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
`;

const CreateNewCard = styled(FlipbookCard)`
  border: 2px dashed #d1d5db;
  background-color: #f9fafb;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &:hover {
    border-color: #3b82f6;
    background-color: #eff6ff;
  }
`;

const PlusIcon = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: #6b7280;
  margin-bottom: 16px;
  transition: all 0.3s ease;

  ${CreateNewCard}:hover & {
    background-color: #3b82f6;
    color: white;
  }
`;

const CreateText = styled.span`
  font-size: 14px;
  color: #6b7280;
  text-align: center;
  font-weight: 500;
  line-height: 1.4;

  ${CreateNewCard}:hover & {
    color: #3b82f6;
  }
`;

const FlipbookThumbnail = styled.div<{ backgroundImage?: string }>`
  width: 100%;
  height: 180px;
  background-image: ${props => props.backgroundImage ? `url(${props.backgroundImage})` : 'none'};
  background-size: cover;
  background-position: center;
  background-color: #f3f4f6;
  position: relative;

  ${props => !props.backgroundImage && `
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    font-size: 48px;
  `}
`;

const FlipbookInfo = styled.div`
  padding: 16px;
  height: 70px;
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

const FlipbookTitle = styled.h3`
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  text-align: center;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
`;

const ActionOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  
  ${FlipbookCard}:hover & {
    opacity: 1;
  }
`;

const ActionButton = styled.button`
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  margin: 0 5px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #2563eb;
  }
`;

interface Flipbook {
  id: number;
  title: string;
  thumbnail?: string;
  isInspiration?: boolean;
}

interface FlipbookGridProps {
  flipbooks: Flipbook[];
  showCreateNew?: boolean;
  onCreateNew?: () => void;
  onFlipbookClick?: (flipbook: Flipbook) => void;
  onFlipbookEdit?: (flipbook: Flipbook) => void;
  onFlipbookCopy?: (flipbook: Flipbook) => void;
}

const FlipbookGrid: React.FC<FlipbookGridProps> = ({
  flipbooks,
  showCreateNew = false,
  onCreateNew,
  onFlipbookClick,
  onFlipbookEdit,
  onFlipbookCopy
}) => {
  return (
    <GridContainer>
      {showCreateNew && (
        <CreateNewCard onClick={onCreateNew}>
          <PlusIcon>+</PlusIcon>
          <CreateText>Create New Flipbook</CreateText>
        </CreateNewCard>
      )}
      
      {flipbooks.map((flipbook) => (
        <FlipbookCard key={flipbook.id} onClick={() => onFlipbookClick?.(flipbook)}>
          <FlipbookThumbnail backgroundImage={flipbook.thumbnail}>
            {!flipbook.thumbnail && '📖'}
          </FlipbookThumbnail>
          <FlipbookInfo>
            <FlipbookTitle>{flipbook.title}</FlipbookTitle>
          </FlipbookInfo>
          
          <ActionOverlay>
            {flipbook.isInspiration ? (
              <ActionButton onClick={(e) => {
                e.stopPropagation();
                onFlipbookCopy?.(flipbook);
              }}>
                Copy to My Flipbooks
              </ActionButton>
            ) : (
              <>
                <ActionButton onClick={(e) => {
                  e.stopPropagation();
                  onFlipbookClick?.(flipbook);
                }}>
                  Open
                </ActionButton>
                <ActionButton onClick={(e) => {
                  e.stopPropagation();
                  onFlipbookEdit?.(flipbook);
                }}>
                  Edit
                </ActionButton>
              </>
            )}
          </ActionOverlay>
        </FlipbookCard>
      ))}
    </GridContainer>
  );
};

export default FlipbookGrid;
