import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Header from '../Header/Header';
import FrontPage from '../FrontPage/FrontPage';
import ResumePage from '../ResumePage/ResumePage';
import ContactDetails from '../ContactDetails/ContactDetails';
import NameFlipbook from '../NameFlipbook/NameFlipbook';
import AddPageModal from '../AddPageModal/AddPageModal';
import TextToolbar from '../TextToolbar/TextToolbar';
import { useAuth } from '../../hooks/useAuth';
import { useFlipbooks } from '../../hooks/useFlipbooks';
import { apiService } from '../../services/api.service';
import './FlipbookEditor.css';

interface FlipbookEditorProps {}

interface ResumeSection {
  id: string;
  title: string;
  content: string;
}

interface ContactInfo {
  name: string;
  mobile: string;
  email: string;
  flipbookUrl: string;
}

interface FlipbookData {
  title: string;
  subtitle: string;
  frontPageBackground: string;
  resumeSections: ResumeSection[];
  profileImage: string;
  contactInfo: ContactInfo;
}

const FlipbookEditor: React.FC<FlipbookEditorProps> = () => {
  const { portfolioId } = useParams<{ portfolioId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { userFlipbooks, updateFlipbook, refreshUserFlipbooks } = useFlipbooks();
  
  const [flipbookTitle, setFlipbookTitle] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(3); // Front Cover + 1 Content Page + Back Cover
  const [isNewFlipbook, setIsNewFlipbook] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [showContactModal, setShowContactModal] = useState(false);
  const [showNameModal, setShowNameModal] = useState(false);
  const [hasCreatedFlipbook, setHasCreatedFlipbook] = useState(false);
  const [showAddPageModal, setShowAddPageModal] = useState(false);
  const [draggedPageIndex, setDraggedPageIndex] = useState<number | null>(null);
  const [dragOverPageIndex, setDragOverPageIndex] = useState<number | null>(null);
  const [pageOrder, setPageOrder] = useState<number[]>([]);
  
  // Text formatting state
  const [activeTextEditor, setActiveTextEditor] = useState<HTMLDivElement | null>(null);
  const [currentTextFormatting, setCurrentTextFormatting] = useState({
    fontFamily: 'Arial',
    fontSize: '14',
    bold: false,
    italic: false,
    underline: false,
    textAlign: 'left' as 'left' | 'center' | 'right' | 'justify',
    color: '#000000',
    backgroundColor: 'transparent',
    lineHeight: '1.4',
    letterSpacing: '0'
  });
  const [showTextToolbar, setShowTextToolbar] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [pendingFormatting, setPendingFormatting] = useState<any>(null);
  
  // Flipbook content data
  const [flipbookData, setFlipbookData] = useState<FlipbookData>({
    title: 'Welcome to My Portfolio',
    subtitle: 'Professional Excellence in Every Detail',
    frontPageBackground: '',
    resumeSections: [
      { id: 'objective', title: 'Objective', content: '' },
      { id: 'education', title: 'Education', content: '' },
      { id: 'experience', title: 'Experience', content: '' },
      { id: 'skills', title: 'Skill Proficiencies', content: '' },
      { id: 'personal', title: 'Personal Statement', content: '' },
      { id: 'interests', title: 'Professional Interests', content: '' }
    ],
    profileImage: '',
    contactInfo: {
      name: 'Zara Irum',
      mobile: '************',
      email: '<EMAIL>',
      flipbookUrl: 'https://flipbook.franklinreport.com/2137 2236 <EMAIL>'
    }
  });

  useEffect(() => {
    // Check if we're creating a new flipbook (no portfolioId in URL)
    if (!portfolioId && !hasCreatedFlipbook) {
      setIsNewFlipbook(true);
      setShowNameModal(true);
    } else {
      setIsNewFlipbook(false);
      // Load existing flipbook data here
      const flipbook = userFlipbooks.find(fb => fb.PortfolioID.toString() === portfolioId);
      if (flipbook) {
        setFlipbookTitle(flipbook.PortfolioTitle);
        // Ensure minimum of 3 pages (front cover, content, back cover)
        const pageCount = Math.max(flipbook.PageCount || 3, 3);
        console.log('Loading flipbook with page count:', pageCount, 'from stored:', flipbook.PageCount);
        setTotalPages(pageCount);
      } else {
        // If flipbook not found in userFlipbooks, ensure we still have 3 pages minimum
        console.log('Flipbook not found, setting default 3 pages');
        setTotalPages(3);
      }
    }
  }, [portfolioId, userFlipbooks, hasCreatedFlipbook]);

  // Initialize page order when total pages changes
  useEffect(() => {
    console.log('Total pages changed to:', totalPages);
    setPageOrder(Array.from({ length: totalPages }, (_, i) => i));
  }, [totalPages]);

  const handleSave = async () => {
    if (!portfolioId) {
      // Handle case where no portfolio ID exists
      return;
    }

    setIsSaving(true);
    try {
      const success = await updateFlipbook({
        portfolioId: parseInt(portfolioId),
        title: flipbookTitle
      });
      
      if (success) {
        setLastSaved(new Date());
        // Refresh flipbooks to get latest data
        await refreshUserFlipbooks();
      }
    } catch (error) {
      console.error('Error saving flipbook:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleTitleEdit = () => {
    const newTitle = prompt('Enter new flipbook title:', flipbookTitle);
    if (newTitle && newTitle.trim()) {
      setFlipbookTitle(newTitle.trim());
    }
  };

  const handlePreview = () => {
    if (portfolioId) {
      navigate(`/viewer/${portfolioId}`);
    }
  };

  const handleClose = () => {
    navigate('/');
  };

  const handleAddPage = () => {
    setShowAddPageModal(true);
  };

  const handleSelectPageTemplate = (template: any) => {
    // Add the new page with the selected template
    setTotalPages(prev => prev + 1);
    // Navigate to the new page
    setCurrentPage(totalPages + 1);
    console.log('Selected template:', template);
  };

  // Page drag and drop handlers
  const handlePageDragStart = (e: React.DragEvent, pageIndex: number) => {
    // Don't allow dragging front cover (0) or back cover (last page)
    if (pageIndex === 0 || pageIndex === totalPages - 1) {
      e.preventDefault();
      return;
    }
    setDraggedPageIndex(pageIndex);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', pageIndex.toString());
  };

  const handlePageDragEnd = () => {
    setDraggedPageIndex(null);
    setDragOverPageIndex(null);
  };

  const handlePageDragOver = (e: React.DragEvent, targetIndex: number) => {
    e.preventDefault();
    // Don't allow dropping on front cover (0) or back cover (last page)
    if (targetIndex === 0 || targetIndex === totalPages - 1) {
      return;
    }
    e.dataTransfer.dropEffect = 'move';
    setDragOverPageIndex(targetIndex);
  };

  const handlePageDragLeave = () => {
    setDragOverPageIndex(null);
  };

  const handlePageDrop = (e: React.DragEvent, targetIndex: number) => {
    e.preventDefault();
    
    if (draggedPageIndex === null || draggedPageIndex === targetIndex) return;
    
    // Don't allow dropping on front cover (0) or back cover (last page)
    if (targetIndex === 0 || targetIndex === totalPages - 1) {
      return;
    }

    const newPageOrder = [...pageOrder];
    const draggedPage = newPageOrder[draggedPageIndex];
    
    // Remove dragged page
    newPageOrder.splice(draggedPageIndex, 1);
    
    // Insert at new position
    const insertIndex = draggedPageIndex < targetIndex ? targetIndex - 1 : targetIndex;
    newPageOrder.splice(insertIndex, 0, draggedPage);
    
    setPageOrder(newPageOrder);
    setDraggedPageIndex(null);
    setDragOverPageIndex(null);
    
    // Update current page if necessary
    if (currentPage === draggedPageIndex + 1) {
      setCurrentPage(insertIndex + 1);
    }
  };

  // Get page label based on original index
  const getPageLabel = (originalIndex: number) => {
    if (originalIndex === 0) return 'Front Cover';
    if (originalIndex === totalPages - 1) return 'Back Cover';
    return `Page ${originalIndex + 1}`;
  };

  // Check if a page is draggable
  const isPageDraggable = (index: number) => {
    return index !== 0 && index !== totalPages - 1;
  };

  // Text formatting handlers
  const handleTextEditorFocus = (editorRef: HTMLDivElement, formatting: any) => {
    setActiveTextEditor(editorRef);
    setShowTextToolbar(true);
    
    // Don't override formatting if we have pending formatting from toolbar
    if (!pendingFormatting && formatting) {
      setCurrentTextFormatting(formatting);
    }
  };

  const handleTextEditorBlur = (event?: React.FocusEvent) => {
    // Don't hide toolbar if clicking on the toolbar itself
    if (event?.relatedTarget) {
      const clickedElement = event.relatedTarget as HTMLElement;
      const toolbar = document.querySelector('.text-toolbar');
      if (toolbar && toolbar.contains(clickedElement)) {
        return; // Don't hide toolbar if clicking on it
      }
    }
    
    // Don't hide toolbar immediately - let it stay visible for a moment
    setTimeout(() => {
      // Double-check if we should still hide the toolbar
      const activeElement = document.activeElement as HTMLElement;
      const toolbar = document.querySelector('.text-toolbar');
      
      // Don't hide if focus is on toolbar or a text editor
      if (toolbar && toolbar.contains(activeElement)) {
        return;
      }
      
      if (activeElement && activeElement.classList.contains('text-editor')) {
        return;
      }
      
      setShowTextToolbar(false);
      setActiveTextEditor(null);
    }, 150);
  };

  const handleTextFormattingChange = (formatting: any) => {
    const newFormatting = { ...currentTextFormatting, ...formatting };
    setCurrentTextFormatting(newFormatting);
    setPendingFormatting(newFormatting);
    setIsTyping(false);
  };

  const handleApplyFormatting = (command: string, value?: string) => {
    if (!activeTextEditor) return;
    
    // Use the TextEditor's applyCommand method if available
    if ((activeTextEditor as any).applyCommand) {
      (activeTextEditor as any).applyCommand(command, value);
    } else {
      // Fallback to direct document.execCommand
      activeTextEditor.focus();
      
      switch (command) {
        case 'bold':
        case 'italic':
        case 'underline':
          document.execCommand(command, false);
          break;
        case 'fontSize':
          // Apply font size via CSS for better control
          const selection = window.getSelection();
          if (selection && selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            if (!range.collapsed) {
              const span = document.createElement('span');
              span.style.fontSize = value + 'pt';
              try {
                range.surroundContents(span);
              } catch (e) {
                // Fallback to execCommand if surroundContents fails
                document.execCommand('fontSize', false, '7');
                // Then apply CSS size
                const selectedElements = activeTextEditor.querySelectorAll('font[size="7"]');
                selectedElements.forEach(el => {
                  (el as HTMLElement).style.fontSize = value + 'pt';
                });
              }
            }
          }
          break;
        case 'fontName':
        case 'foreColor':
        case 'backColor':
        case 'justifyLeft':
        case 'justifyCenter':
        case 'justifyRight':
        case 'justifyFull':
        case 'insertUnorderedList':
        case 'insertOrderedList':
        case 'createLink':
          document.execCommand(command, false, value);
          break;
      }
    }
    
    // Set pending formatting for when user starts typing
    setPendingFormatting(currentTextFormatting);
  };
  
  const handleFormattingApplied = () => {
    // Clear pending formatting after it has been applied
    setPendingFormatting(null);
  };

  const handleNameSubmit = async (name: string) => {
    try {
      // Create the flipbook on the server with 3 pages (front cover, content, back cover)
      const response = await apiService.createNewFlipbook({ 
        title: name,
        pageCount: 3 
      });
      if (response.success && response.data) {
        const newPortfolioId = response.data;
        setFlipbookTitle(name);
        setShowNameModal(false);
        setHasCreatedFlipbook(true);
        
        // Refresh flipbooks to get latest data
        await refreshUserFlipbooks();
        
        // Update URL to include the new portfolio ID
        navigate(`/editor/${newPortfolioId}`, { replace: true });
      } else {
        console.error('Failed to create flipbook:', response.error);
        alert('Failed to create flipbook. Please try again.');
      }
    } catch (error) {
      console.error('Error creating flipbook:', error);
      alert('An error occurred while creating the flipbook.');
    }
  };

  const handleNameModalClose = () => {
    // If user closes modal without naming, redirect to dashboard
    navigate('/');
  };

  return (
    <div className="flipbook-editor">
      {/* Common Text Toolbar - Fixed at top */}
      <TextToolbar
        isVisible={showTextToolbar}
        currentFormatting={currentTextFormatting}
        onFormatChange={handleTextFormattingChange}
        onApplyFormatting={handleApplyFormatting}
      />
      
      <div className="editor-content">
        <div className="editor-sidebar">
          {/* Toolbar Icons */}
          <div className="toolbar-icons sidebar-section">
            <div className="icon-grid">
              <button className="icon-btn" title="Select">
                <span>📌</span>
              </button>
              <button className="icon-btn" title="Text">
                <span>A</span>
              </button>
              <button className="icon-btn" title="Image">
                <span>🖼️</span>
              </button>
              <button className="icon-btn" title="Shape">
                <span>⬛</span>
              </button>
              <button className="icon-btn" title="Line">
                <span>📏</span>
              </button>
              <button className="icon-btn" title="Crop">
                <span>✂️</span>
              </button>
            </div>
          </div>
          
          {/* Click & Drag Section */}
          <div className="click-drag-section">
            CLICK & DRAG<br/>TO REORDER PAGES
          </div>
          
          {/* Pages Section */}
          <div className="pages-section sidebar-section">
            {pageOrder.map((originalIndex, displayIndex) => {
              const isDraggable = isPageDraggable(originalIndex);
              const isDragging = draggedPageIndex === displayIndex;
              const isDragOver = dragOverPageIndex === displayIndex;
              
              return (
                <div 
                  key={`page-${originalIndex}`}
                  className={`page-thumbnail ${
                    currentPage === displayIndex + 1 ? 'active' : ''
                  } ${
                    isDragging ? 'dragging' : ''
                  } ${
                    isDragOver && isDraggable ? 'drag-over' : ''
                  } ${
                    !isDraggable ? 'not-draggable' : 'draggable'
                  }`}
                  draggable={isDraggable}
                  onClick={() => setCurrentPage(displayIndex + 1)}
                  onDragStart={(e) => handlePageDragStart(e, displayIndex)}
                  onDragEnd={handlePageDragEnd}
                  onDragOver={(e) => handlePageDragOver(e, displayIndex)}
                  onDragLeave={handlePageDragLeave}
                  onDrop={(e) => handlePageDrop(e, displayIndex)}
                  title={isDraggable ? `Drag to reorder - ${getPageLabel(originalIndex)}` : getPageLabel(originalIndex)}
                >
                  {isDraggable && (
                    <div className="page-drag-handle">
                      <span>⋮⋮</span>
                    </div>
                  )}
                  <span className="page-label">{getPageLabel(originalIndex)}</span>
                  {!isDraggable && (
                    <div className="fixed-page-indicator">
                      <span>📌</span>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
          
          {/* Add Page Section */}
          <div className="add-page-section">
            <button className="add-page-btn" onClick={handleAddPage}>
              + Add New Page
            </button>
          </div>
        </div>

        <div className="editor-canvas">
          <div className="canvas-container">
            {!flipbookTitle ? (
              <div className="waiting-for-name">
                <div className="placeholder-content">
                  <h2>Creating New Flipbook...</h2>
                  <p>Please name your flipbook to continue</p>
                </div>
              </div>
            ) : (
              <div className="page-canvas">
                {/* Pages Header */}
                <div className="pages-header">
                  <h1>PAGES</h1>
                  <h2 className="flipbook-title">{flipbookTitle}</h2>
                </div>
                
                {currentPage === 1 && totalPages >= 1 && (
                <FrontPage
                  title={flipbookTitle || flipbookData.title}
                  subtitle={flipbookData.subtitle}
                  backgroundImage={flipbookData.frontPageBackground}
                  onTitleChange={(title) => {
                    setFlipbookData(prev => ({ ...prev, title }));
                  }}
                  onSubtitleChange={(subtitle) => setFlipbookData(prev => ({ ...prev, subtitle }))}
                  onBackgroundImageChange={(imageUrl) => setFlipbookData(prev => ({ ...prev, frontPageBackground: imageUrl }))}
                  onNext={() => setCurrentPage(2)}
                />
              )}
              
              {currentPage === 2 && totalPages >= 2 && (
                <ResumePage
                  sections={flipbookData.resumeSections}
                  profileImage={flipbookData.profileImage}
                  onSectionChange={(sectionId, content) => {
                    setFlipbookData(prev => ({
                      ...prev,
                      resumeSections: prev.resumeSections.map(section =>
                        section.id === sectionId ? { ...section, content } : section
                      )
                    }));
                  }}
                  onSectionDelete={(sectionId) => {
                    setFlipbookData(prev => ({
                      ...prev,
                      resumeSections: prev.resumeSections.filter(section => section.id !== sectionId)
                    }));
                  }}
                  onSectionReorder={(newSections) => {
                    setFlipbookData(prev => ({
                      ...prev,
                      resumeSections: newSections
                    }));
                  }}
                  onProfileImageChange={(imageUrl) => setFlipbookData(prev => ({ ...prev, profileImage: imageUrl }))}
                  onTextEditorFocus={handleTextEditorFocus}
                  onTextEditorBlur={handleTextEditorBlur}
                  pendingFormatting={pendingFormatting}
                  onFormattingApplied={handleFormattingApplied}
                  onPrevious={() => setCurrentPage(1)}
                  onNext={() => setCurrentPage(3)}
                />
              )}
              
              {currentPage === 3 && totalPages >= 3 && (
                <div className="back-page">
                  <div className="back-page-content">
                    <div className="contact-info-section">
                      <h2>Contact Information</h2>
                      <div className="contact-card" onClick={() => setShowContactModal(true)}>
                        <div className="contact-item">
                          <span className="label">Name:</span>
                          <span className="value">{flipbookData.contactInfo.name}</span>
                        </div>
                        <div className="contact-item">
                          <span className="label">Mobile:</span>
                          <span className="value">{flipbookData.contactInfo.mobile}</span>
                        </div>
                        <div className="contact-item">
                          <span className="label">Email:</span>
                          <span className="value">{flipbookData.contactInfo.email}</span>
                        </div>
                        <div className="contact-item">
                          <span className="label">Portfolio URL:</span>
                          <span className="value">{flipbookData.contactInfo.flipbookUrl}</span>
                        </div>
                        <div className="edit-hint">
                          Click to edit contact details
                        </div>
                      </div>
                    </div>
                    
                    <div className="flipbook-branding">
                      <div className="brand-logo">📚 Flipbook</div>
                      <p>Created with Flipbook</p>
                    </div>
                    
                    <div className="navigation-controls">
                      <button className="nav-btn prev-btn" onClick={() => setCurrentPage(2)} title="Previous Page">
                        <span className="arrow-icon">←</span>
                      </button>
                    </div>
                  </div>
                </div>
              )}
              
              {currentPage > 3 && (
                <div className="page-content">
                  <h1>Page {currentPage}</h1>
                  <p>Additional page content can be added here.</p>
                  <div className="navigation-controls">
                    <button className="nav-btn prev-btn" onClick={() => setCurrentPage(Math.max(1, currentPage - 1))} title="Previous Page">
                      <span className="arrow-icon">←</span>
                    </button>
                    {currentPage < totalPages && (
                      <button className="nav-btn next-btn" onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))} title="Next Page">
                        <span className="arrow-icon">→</span>
                      </button>
                    )}
                  </div>
                </div>
              )}
              </div>
            )}
          </div>
        </div>

        {/* Properties panel can be added later if needed */}
      </div>
      
      {/* Contact Details Modal */}
      <ContactDetails
        isOpen={showContactModal}
        onClose={() => setShowContactModal(false)}
        contactInfo={flipbookData.contactInfo}
        onSave={(contactInfo) => {
          setFlipbookData(prev => ({ ...prev, contactInfo }));
          setShowContactModal(false);
        }}
      />
      
      {/* Name Flipbook Modal */}
      <NameFlipbook
        isOpen={showNameModal}
        onClose={handleNameModalClose}
        onSubmit={handleNameSubmit}
      />
      
      {/* Add Page Modal */}
      <AddPageModal
        isOpen={showAddPageModal}
        onClose={() => setShowAddPageModal(false)}
        onSelectTemplate={handleSelectPageTemplate}
      />
    </div>
  );
};

export default FlipbookEditor;
