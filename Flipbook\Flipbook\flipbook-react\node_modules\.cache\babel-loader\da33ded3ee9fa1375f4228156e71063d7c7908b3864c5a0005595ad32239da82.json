{"ast": null, "code": "var _jsxFileName = \"D:\\\\TradeWorks\\\\Flipbook\\\\Flipbook\\\\Flipbook\\\\flipbook-react\\\\src\\\\components\\\\LandingPage\\\\LandingPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport Header from './Header';\nimport Sidebar from './Sidebar';\nimport FlipbookGrid from './FlipbookGrid';\nimport CreateFlipbookModal from './CreateFlipbookModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LandingPageContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #f5f5f5;\n`;\n_c = LandingPageContainer;\nconst MainContent = styled.div`\n  display: flex;\n  flex: 1;\n  overflow: hidden;\n`;\n_c2 = MainContent;\nconst ContentArea = styled.div`\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n  background-color: #ffffff;\n`;\n_c3 = ContentArea;\nconst Section = styled.div`\n  margin-bottom: 40px;\n`;\n_c4 = Section;\nconst SectionTitle = styled.h2`\n  font-size: 18px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 20px;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n`;\n_c5 = SectionTitle;\nconst CertificationBadge = styled.div`\n  position: fixed;\n  top: 80px;\n  right: 20px;\n  width: 100px;\n  height: 100px;\n  background-image: url('/images/certification-badge.png');\n  background-size: contain;\n  background-repeat: no-repeat;\n  z-index: 10;\n`;\n_c6 = CertificationBadge;\nconst LandingPage = () => {\n  _s();\n  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);\n\n  // Mock data - replace with actual API calls\n  const myFlipbooks = [\n    // User will see empty state initially\n  ];\n  const inspirationFlipbooks = [{\n    id: 1,\n    title: 'Original Flipbook',\n    thumbnail: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop',\n    isInspiration: true\n  }, {\n    id: 2,\n    title: 'Architecture Portfolio',\n    thumbnail: 'https://images.unsplash.com/photo-1487958449943-2429e8be8625?w=400&h=300&fit=crop',\n    isInspiration: true\n  }, {\n    id: 3,\n    title: 'Design Showcase',\n    thumbnail: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop',\n    isInspiration: true\n  }, {\n    id: 4,\n    title: 'Creative Portfolio',\n    thumbnail: 'https://images.unsplash.com/photo-1541746972996-4e0b0f93e586?w=400&h=300&fit=crop',\n    isInspiration: true\n  }];\n  const handleCreateNew = () => {\n    setIsCreateModalOpen(true);\n  };\n  const handleCloseModal = () => {\n    setIsCreateModalOpen(false);\n  };\n  const handleCreateFlipbook = title => {\n    // Handle flipbook creation\n    console.log('Creating flipbook:', title);\n    setIsCreateModalOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(LandingPageContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ContentArea, {\n        children: [/*#__PURE__*/_jsxDEV(Section, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: \"My Flipbooks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FlipbookGrid, {\n            flipbooks: myFlipbooks,\n            showCreateNew: true,\n            onCreateNew: handleCreateNew,\n            onFlipbookClick: flipbook => console.log('Open flipbook:', flipbook),\n            onFlipbookEdit: flipbook => console.log('Edit flipbook:', flipbook)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Section, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: \"Inspiration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FlipbookGrid, {\n            flipbooks: inspirationFlipbooks,\n            onFlipbookClick: flipbook => console.log('Preview flipbook:', flipbook),\n            onFlipbookCopy: flipbook => console.log('Copy flipbook:', flipbook)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CertificationBadge, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), isCreateModalOpen && /*#__PURE__*/_jsxDEV(CreateFlipbookModal, {\n      onClose: handleCloseModal,\n      onCreate: handleCreateFlipbook\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 5\n  }, this);\n};\n_s(LandingPage, \"l2IDSeweBUwQvSH2cLV6t8CEkvE=\");\n_c7 = LandingPage;\nexport default LandingPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"LandingPageContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"ContentArea\");\n$RefreshReg$(_c4, \"Section\");\n$RefreshReg$(_c5, \"SectionTitle\");\n$RefreshReg$(_c6, \"CertificationBadge\");\n$RefreshReg$(_c7, \"LandingPage\");", "map": {"version": 3, "names": ["React", "useState", "styled", "Header", "Sidebar", "FlipbookGrid", "CreateFlipbookModal", "jsxDEV", "_jsxDEV", "LandingPageContainer", "div", "_c", "MainContent", "_c2", "ContentArea", "_c3", "Section", "_c4", "SectionTitle", "h2", "_c5", "CertificationBadge", "_c6", "LandingPage", "_s", "isCreateModalOpen", "setIsCreateModalOpen", "myFlipbooks", "inspirationFlipbooks", "id", "title", "thumbnail", "isInspiration", "handleCreateNew", "handleCloseModal", "handleCreateFlipbook", "console", "log", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flipbooks", "showCreateNew", "onCreateNew", "onFlipbookClick", "flipbook", "onFlipbookEdit", "onFlipbookCopy", "onClose", "onCreate", "_c7", "$RefreshReg$"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/LandingPage/LandingPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport Header from './Header';\nimport Sidebar from './Sidebar';\nimport FlipbookGrid from './FlipbookGrid';\nimport CreateFlipbookModal from './CreateFlipbookModal';\nimport { useFlipbooks } from '../../hooks/useFlipbooks';\nimport { useAuth } from '../../hooks/useAuth';\n\nconst LandingPageContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #f5f5f5;\n`;\n\nconst MainContent = styled.div`\n  display: flex;\n  flex: 1;\n  overflow: hidden;\n`;\n\nconst ContentArea = styled.div`\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n  background-color: #ffffff;\n`;\n\nconst Section = styled.div`\n  margin-bottom: 40px;\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: 18px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 20px;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n`;\n\n\n\nconst CertificationBadge = styled.div`\n  position: fixed;\n  top: 80px;\n  right: 20px;\n  width: 100px;\n  height: 100px;\n  background-image: url('/images/certification-badge.png');\n  background-size: contain;\n  background-repeat: no-repeat;\n  z-index: 10;\n`;\n\ninterface Flipbook {\n  id: number;\n  title: string;\n  thumbnail: string;\n  isInspiration?: boolean;\n}\n\nconst LandingPage: React.FC = () => {\n  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);\n  \n  // Mock data - replace with actual API calls\n  const myFlipbooks: Flipbook[] = [\n    // User will see empty state initially\n  ];\n\n  const inspirationFlipbooks: Flipbook[] = [\n    {\n      id: 1,\n      title: 'Original Flipbook',\n      thumbnail: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop',\n      isInspiration: true\n    },\n    {\n      id: 2,\n      title: 'Architecture Portfolio',\n      thumbnail: 'https://images.unsplash.com/photo-1487958449943-2429e8be8625?w=400&h=300&fit=crop',\n      isInspiration: true\n    },\n    {\n      id: 3,\n      title: 'Design Showcase',\n      thumbnail: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop',\n      isInspiration: true\n    },\n    {\n      id: 4,\n      title: 'Creative Portfolio',\n      thumbnail: 'https://images.unsplash.com/photo-1541746972996-4e0b0f93e586?w=400&h=300&fit=crop',\n      isInspiration: true\n    }\n  ];\n\n  const handleCreateNew = () => {\n    setIsCreateModalOpen(true);\n  };\n\n  const handleCloseModal = () => {\n    setIsCreateModalOpen(false);\n  };\n\n  const handleCreateFlipbook = (title: string) => {\n    // Handle flipbook creation\n    console.log('Creating flipbook:', title);\n    setIsCreateModalOpen(false);\n  };\n\n  return (\n    <LandingPageContainer>\n      <Header />\n      <MainContent>\n        <Sidebar />\n        <ContentArea>\n          <Section>\n            <SectionTitle>My Flipbooks</SectionTitle>\n            <FlipbookGrid\n              flipbooks={myFlipbooks}\n              showCreateNew={true}\n              onCreateNew={handleCreateNew}\n              onFlipbookClick={(flipbook) => console.log('Open flipbook:', flipbook)}\n              onFlipbookEdit={(flipbook) => console.log('Edit flipbook:', flipbook)}\n            />\n          </Section>\n\n          <Section>\n            <SectionTitle>Inspiration</SectionTitle>\n            <FlipbookGrid\n              flipbooks={inspirationFlipbooks}\n              onFlipbookClick={(flipbook) => console.log('Preview flipbook:', flipbook)}\n              onFlipbookCopy={(flipbook) => console.log('Copy flipbook:', flipbook)}\n            />\n          </Section>\n        </ContentArea>\n      </MainContent>\n      \n      <CertificationBadge />\n      \n      {isCreateModalOpen && (\n        <CreateFlipbookModal\n          onClose={handleCloseModal}\n          onCreate={handleCreateFlipbook}\n        />\n      )}\n    </LandingPageContainer>\n  );\n};\n\nexport default LandingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,mBAAmB,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAIxD,MAAMC,oBAAoB,GAAGP,MAAM,CAACQ,GAAG;AACvC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,oBAAoB;AAO1B,MAAMG,WAAW,GAAGV,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAJID,WAAW;AAMjB,MAAME,WAAW,GAAGZ,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GALID,WAAW;AAOjB,MAAME,OAAO,GAAGd,MAAM,CAACQ,GAAG;AAC1B;AACA,CAAC;AAACO,GAAA,GAFID,OAAO;AAIb,MAAME,YAAY,GAAGhB,MAAM,CAACiB,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,YAAY;AAWlB,MAAMG,kBAAkB,GAAGnB,MAAM,CAACQ,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GAVID,kBAAkB;AAmBxB,MAAME,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM0B,WAAuB,GAAG;IAC9B;EAAA,CACD;EAED,MAAMC,oBAAgC,GAAG,CACvC;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,mBAAmB;IAC1BC,SAAS,EAAE,mFAAmF;IAC9FC,aAAa,EAAE;EACjB,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,wBAAwB;IAC/BC,SAAS,EAAE,mFAAmF;IAC9FC,aAAa,EAAE;EACjB,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,iBAAiB;IACxBC,SAAS,EAAE,gFAAgF;IAC3FC,aAAa,EAAE;EACjB,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,oBAAoB;IAC3BC,SAAS,EAAE,mFAAmF;IAC9FC,aAAa,EAAE;EACjB,CAAC,CACF;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BP,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMQ,gBAAgB,GAAGA,CAAA,KAAM;IAC7BR,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMS,oBAAoB,GAAIL,KAAa,IAAK;IAC9C;IACAM,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEP,KAAK,CAAC;IACxCJ,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,oBACElB,OAAA,CAACC,oBAAoB;IAAA6B,QAAA,gBACnB9B,OAAA,CAACL,MAAM;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVlC,OAAA,CAACI,WAAW;MAAA0B,QAAA,gBACV9B,OAAA,CAACJ,OAAO;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXlC,OAAA,CAACM,WAAW;QAAAwB,QAAA,gBACV9B,OAAA,CAACQ,OAAO;UAAAsB,QAAA,gBACN9B,OAAA,CAACU,YAAY;YAAAoB,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACzClC,OAAA,CAACH,YAAY;YACXsC,SAAS,EAAEhB,WAAY;YACvBiB,aAAa,EAAE,IAAK;YACpBC,WAAW,EAAEZ,eAAgB;YAC7Ba,eAAe,EAAGC,QAAQ,IAAKX,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEU,QAAQ,CAAE;YACvEC,cAAc,EAAGD,QAAQ,IAAKX,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEU,QAAQ;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAEVlC,OAAA,CAACQ,OAAO;UAAAsB,QAAA,gBACN9B,OAAA,CAACU,YAAY;YAAAoB,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACxClC,OAAA,CAACH,YAAY;YACXsC,SAAS,EAAEf,oBAAqB;YAChCkB,eAAe,EAAGC,QAAQ,IAAKX,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEU,QAAQ,CAAE;YAC1EE,cAAc,EAAGF,QAAQ,IAAKX,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEU,QAAQ;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEdlC,OAAA,CAACa,kBAAkB;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAErBjB,iBAAiB,iBAChBjB,OAAA,CAACF,mBAAmB;MAClB4C,OAAO,EAAEhB,gBAAiB;MAC1BiB,QAAQ,EAAEhB;IAAqB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACmB,CAAC;AAE3B,CAAC;AAAClB,EAAA,CAvFID,WAAqB;AAAA6B,GAAA,GAArB7B,WAAqB;AAyF3B,eAAeA,WAAW;AAAC,IAAAZ,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAA8B,GAAA;AAAAC,YAAA,CAAA1C,EAAA;AAAA0C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAAtC,GAAA;AAAAsC,YAAA,CAAApC,GAAA;AAAAoC,YAAA,CAAAjC,GAAA;AAAAiC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}