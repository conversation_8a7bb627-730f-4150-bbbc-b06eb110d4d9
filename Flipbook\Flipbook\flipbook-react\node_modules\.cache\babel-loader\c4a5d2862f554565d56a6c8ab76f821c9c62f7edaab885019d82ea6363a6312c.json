{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0;import React from'react';import styled from'styled-components';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const GridContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\\n  gap: 20px;\\n  padding: 20px 0;\\n\"])));const FlipbookCard=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  width: 200px;\\n  height: 250px;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  background-color: white;\\n  position: relative;\\n\\n  &:hover {\\n    transform: translateY(-8px);\\n    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n  }\\n\"])));const CreateNewCard=styled(FlipbookCard)(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  border: 2px dashed #d1d5db;\\n  background-color: #f9fafb;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n\\n  &:hover {\\n    border-color: #3b82f6;\\n    background-color: #eff6ff;\\n  }\\n\"])));const PlusIcon=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  background-color: #e5e7eb;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 32px;\\n  color: #6b7280;\\n  margin-bottom: 16px;\\n  transition: all 0.3s ease;\\n\\n  \",\":hover & {\\n    background-color: #3b82f6;\\n    color: white;\\n  }\\n\"])),CreateNewCard);const CreateText=styled.span(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  font-size: 14px;\\n  color: #6b7280;\\n  text-align: center;\\n  font-weight: 500;\\n  line-height: 1.4;\\n\\n  \",\":hover & {\\n    color: #3b82f6;\\n  }\\n\"])),CreateNewCard);const FlipbookThumbnail=styled.div(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  height: 180px;\\n  background-image: \",\";\\n  background-size: cover;\\n  background-position: center;\\n  background-color: #f3f4f6;\\n  position: relative;\\n\\n  \",\"\\n\"])),props=>props.backgroundImage?\"url(\".concat(props.backgroundImage,\")\"):'none',props=>!props.backgroundImage&&\"\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    color: #9ca3af;\\n    font-size: 48px;\\n  \");const FlipbookInfo=styled.div(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  padding: 16px;\\n  height: 70px;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n\"])));const FlipbookTitle=styled.h3(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #1f2937;\\n  margin: 0;\\n  text-align: center;\\n  line-height: 1.4;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n\"])));const ActionOverlay=styled.div(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.7);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  \\n  \",\":hover & {\\n    opacity: 1;\\n  }\\n\"])),FlipbookCard);const ActionButton=styled.button(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  background-color: #3b82f6;\\n  color: white;\\n  border: none;\\n  padding: 10px 20px;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  margin: 0 5px;\\n  transition: background-color 0.3s ease;\\n\\n  &:hover {\\n    background-color: #2563eb;\\n  }\\n\"])));const FlipbookGrid=_ref=>{let{flipbooks,showCreateNew=false,onCreateNew,onFlipbookClick,onFlipbookEdit,onFlipbookCopy}=_ref;return/*#__PURE__*/_jsxs(GridContainer,{children:[showCreateNew&&/*#__PURE__*/_jsxs(CreateNewCard,{onClick:onCreateNew,children:[/*#__PURE__*/_jsx(PlusIcon,{children:\"+\"}),/*#__PURE__*/_jsx(CreateText,{children:\"Create New Flipbook\"})]}),flipbooks.map(flipbook=>/*#__PURE__*/_jsxs(FlipbookCard,{onClick:()=>onFlipbookClick===null||onFlipbookClick===void 0?void 0:onFlipbookClick(flipbook),children:[/*#__PURE__*/_jsx(FlipbookThumbnail,{backgroundImage:flipbook.thumbnail,children:!flipbook.thumbnail&&'📖'}),/*#__PURE__*/_jsx(FlipbookInfo,{children:/*#__PURE__*/_jsx(FlipbookTitle,{children:flipbook.title})}),/*#__PURE__*/_jsx(ActionOverlay,{children:flipbook.isInspiration?/*#__PURE__*/_jsx(ActionButton,{onClick:e=>{e.stopPropagation();onFlipbookCopy===null||onFlipbookCopy===void 0?void 0:onFlipbookCopy(flipbook);},children:\"Copy to My Flipbooks\"}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(ActionButton,{onClick:e=>{e.stopPropagation();onFlipbookClick===null||onFlipbookClick===void 0?void 0:onFlipbookClick(flipbook);},children:\"Open\"}),/*#__PURE__*/_jsx(ActionButton,{onClick:e=>{e.stopPropagation();onFlipbookEdit===null||onFlipbookEdit===void 0?void 0:onFlipbookEdit(flipbook);},children:\"Edit\"})]})})]},flipbook.id))]});};export default FlipbookGrid;", "map": {"version": 3, "names": ["React", "styled", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_templateObject", "_taggedTemplateLiteral", "FlipbookCard", "_templateObject2", "CreateNewCard", "_templateObject3", "PlusIcon", "_templateObject4", "CreateText", "span", "_templateObject5", "FlipbookThumbnail", "_templateObject6", "props", "backgroundImage", "concat", "FlipbookInfo", "_templateObject7", "FlipbookTitle", "h3", "_templateObject8", "ActionOverlay", "_templateObject9", "ActionButton", "button", "_templateObject0", "FlipbookGrid", "_ref", "flipbooks", "showCreateNew", "onCreateNew", "onFlipbookClick", "onFlipbookEdit", "onFlipbookCopy", "children", "onClick", "map", "flipbook", "thumbnail", "title", "isInspiration", "e", "stopPropagation", "id"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/LandingPage/FlipbookGrid.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\n\nconst GridContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 20px;\n  padding: 20px 0;\n`;\n\nconst FlipbookCard = styled.div`\n  width: 200px;\n  height: 250px;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background-color: white;\n  position: relative;\n\n  &:hover {\n    transform: translateY(-8px);\n    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n  }\n`;\n\nconst CreateNewCard = styled(FlipbookCard)`\n  border: 2px dashed #d1d5db;\n  background-color: #f9fafb;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    border-color: #3b82f6;\n    background-color: #eff6ff;\n  }\n`;\n\nconst PlusIcon = styled.div`\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  background-color: #e5e7eb;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 32px;\n  color: #6b7280;\n  margin-bottom: 16px;\n  transition: all 0.3s ease;\n\n  ${CreateNewCard}:hover & {\n    background-color: #3b82f6;\n    color: white;\n  }\n`;\n\nconst CreateText = styled.span`\n  font-size: 14px;\n  color: #6b7280;\n  text-align: center;\n  font-weight: 500;\n  line-height: 1.4;\n\n  ${CreateNewCard}:hover & {\n    color: #3b82f6;\n  }\n`;\n\nconst FlipbookThumbnail = styled.div<{ backgroundImage?: string }>`\n  width: 100%;\n  height: 180px;\n  background-image: ${props => props.backgroundImage ? `url(${props.backgroundImage})` : 'none'};\n  background-size: cover;\n  background-position: center;\n  background-color: #f3f4f6;\n  position: relative;\n\n  ${props => !props.backgroundImage && `\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: #9ca3af;\n    font-size: 48px;\n  `}\n`;\n\nconst FlipbookInfo = styled.div`\n  padding: 16px;\n  height: 70px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n`;\n\nconst FlipbookTitle = styled.h3`\n  font-size: 14px;\n  font-weight: 600;\n  color: #1f2937;\n  margin: 0;\n  text-align: center;\n  line-height: 1.4;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n`;\n\nconst ActionOverlay = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  \n  ${FlipbookCard}:hover & {\n    opacity: 1;\n  }\n`;\n\nconst ActionButton = styled.button`\n  background-color: #3b82f6;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  margin: 0 5px;\n  transition: background-color 0.3s ease;\n\n  &:hover {\n    background-color: #2563eb;\n  }\n`;\n\ninterface Flipbook {\n  id: number;\n  title: string;\n  thumbnail?: string;\n  isInspiration?: boolean;\n}\n\ninterface FlipbookGridProps {\n  flipbooks: Flipbook[];\n  showCreateNew?: boolean;\n  onCreateNew?: () => void;\n  onFlipbookClick?: (flipbook: Flipbook) => void;\n  onFlipbookEdit?: (flipbook: Flipbook) => void;\n  onFlipbookCopy?: (flipbook: Flipbook) => void | Promise<void>;\n}\n\nconst FlipbookGrid: React.FC<FlipbookGridProps> = ({\n  flipbooks,\n  showCreateNew = false,\n  onCreateNew,\n  onFlipbookClick,\n  onFlipbookEdit,\n  onFlipbookCopy\n}) => {\n  return (\n    <GridContainer>\n      {showCreateNew && (\n        <CreateNewCard onClick={onCreateNew}>\n          <PlusIcon>+</PlusIcon>\n          <CreateText>Create New Flipbook</CreateText>\n        </CreateNewCard>\n      )}\n      \n      {flipbooks.map((flipbook) => (\n        <FlipbookCard key={flipbook.id} onClick={() => onFlipbookClick?.(flipbook)}>\n          <FlipbookThumbnail backgroundImage={flipbook.thumbnail}>\n            {!flipbook.thumbnail && '📖'}\n          </FlipbookThumbnail>\n          <FlipbookInfo>\n            <FlipbookTitle>{flipbook.title}</FlipbookTitle>\n          </FlipbookInfo>\n          \n          <ActionOverlay>\n            {flipbook.isInspiration ? (\n              <ActionButton onClick={(e) => {\n                e.stopPropagation();\n                onFlipbookCopy?.(flipbook);\n              }}>\n                Copy to My Flipbooks\n              </ActionButton>\n            ) : (\n              <>\n                <ActionButton onClick={(e) => {\n                  e.stopPropagation();\n                  onFlipbookClick?.(flipbook);\n                }}>\n                  Open\n                </ActionButton>\n                <ActionButton onClick={(e) => {\n                  e.stopPropagation();\n                  onFlipbookEdit?.(flipbook);\n                }}>\n                  Edit\n                </ActionButton>\n              </>\n            )}\n          </ActionOverlay>\n        </FlipbookCard>\n      ))}\n    </GridContainer>\n  );\n};\n\nexport default FlipbookGrid;\n"], "mappings": "0UAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEvC,KAAM,CAAAC,aAAa,CAAGP,MAAM,CAACQ,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,+HAK/B,CAED,KAAM,CAAAC,YAAY,CAAGX,MAAM,CAACQ,GAAG,CAAAI,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,oVAe9B,CAED,KAAM,CAAAG,aAAa,CAAGb,MAAM,CAACW,YAAY,CAAC,CAAAG,gBAAA,GAAAA,gBAAA,CAAAJ,sBAAA,wPAYzC,CAED,KAAM,CAAAK,QAAQ,CAAGf,MAAM,CAACQ,GAAG,CAAAQ,gBAAA,GAAAA,gBAAA,CAAAN,sBAAA,8UAavBG,aAAa,CAIhB,CAED,KAAM,CAAAI,UAAU,CAAGjB,MAAM,CAACkB,IAAI,CAAAC,gBAAA,GAAAA,gBAAA,CAAAT,sBAAA,+JAO1BG,aAAa,CAGhB,CAED,KAAM,CAAAO,iBAAiB,CAAGpB,MAAM,CAACQ,GAAG,CAAAa,gBAAA,GAAAA,gBAAA,CAAAX,sBAAA,+LAGdY,KAAK,EAAIA,KAAK,CAACC,eAAe,QAAAC,MAAA,CAAUF,KAAK,CAACC,eAAe,MAAM,MAAM,CAM3FD,KAAK,EAAI,CAACA,KAAK,CAACC,eAAe,+HAMhC,CACF,CAED,KAAM,CAAAE,YAAY,CAAGzB,MAAM,CAACQ,GAAG,CAAAkB,gBAAA,GAAAA,gBAAA,CAAAhB,sBAAA,sHAM9B,CAED,KAAM,CAAAiB,aAAa,CAAG3B,MAAM,CAAC4B,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAnB,sBAAA,qQAY9B,CAED,KAAM,CAAAoB,aAAa,CAAG9B,MAAM,CAACQ,GAAG,CAAAuB,gBAAA,GAAAA,gBAAA,CAAArB,sBAAA,8RAa5BC,YAAY,CAGf,CAED,KAAM,CAAAqB,YAAY,CAAGhC,MAAM,CAACiC,MAAM,CAAAC,gBAAA,GAAAA,gBAAA,CAAAxB,sBAAA,sSAejC,CAkBD,KAAM,CAAAyB,YAAyC,CAAGC,IAAA,EAO5C,IAP6C,CACjDC,SAAS,CACTC,aAAa,CAAG,KAAK,CACrBC,WAAW,CACXC,eAAe,CACfC,cAAc,CACdC,cACF,CAAC,CAAAN,IAAA,CACC,mBACEhC,KAAA,CAACG,aAAa,EAAAoC,QAAA,EACXL,aAAa,eACZlC,KAAA,CAACS,aAAa,EAAC+B,OAAO,CAAEL,WAAY,CAAAI,QAAA,eAClCzC,IAAA,CAACa,QAAQ,EAAA4B,QAAA,CAAC,GAAC,CAAU,CAAC,cACtBzC,IAAA,CAACe,UAAU,EAAA0B,QAAA,CAAC,qBAAmB,CAAY,CAAC,EAC/B,CAChB,CAEAN,SAAS,CAACQ,GAAG,CAAEC,QAAQ,eACtB1C,KAAA,CAACO,YAAY,EAAmBiC,OAAO,CAAEA,CAAA,GAAMJ,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAGM,QAAQ,CAAE,CAAAH,QAAA,eACzEzC,IAAA,CAACkB,iBAAiB,EAACG,eAAe,CAAEuB,QAAQ,CAACC,SAAU,CAAAJ,QAAA,CACpD,CAACG,QAAQ,CAACC,SAAS,EAAI,IAAI,CACX,CAAC,cACpB7C,IAAA,CAACuB,YAAY,EAAAkB,QAAA,cACXzC,IAAA,CAACyB,aAAa,EAAAgB,QAAA,CAAEG,QAAQ,CAACE,KAAK,CAAgB,CAAC,CACnC,CAAC,cAEf9C,IAAA,CAAC4B,aAAa,EAAAa,QAAA,CACXG,QAAQ,CAACG,aAAa,cACrB/C,IAAA,CAAC8B,YAAY,EAACY,OAAO,CAAGM,CAAC,EAAK,CAC5BA,CAAC,CAACC,eAAe,CAAC,CAAC,CACnBT,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAGI,QAAQ,CAAC,CAC5B,CAAE,CAAAH,QAAA,CAAC,sBAEH,CAAc,CAAC,cAEfvC,KAAA,CAAAE,SAAA,EAAAqC,QAAA,eACEzC,IAAA,CAAC8B,YAAY,EAACY,OAAO,CAAGM,CAAC,EAAK,CAC5BA,CAAC,CAACC,eAAe,CAAC,CAAC,CACnBX,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAGM,QAAQ,CAAC,CAC7B,CAAE,CAAAH,QAAA,CAAC,MAEH,CAAc,CAAC,cACfzC,IAAA,CAAC8B,YAAY,EAACY,OAAO,CAAGM,CAAC,EAAK,CAC5BA,CAAC,CAACC,eAAe,CAAC,CAAC,CACnBV,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAGK,QAAQ,CAAC,CAC5B,CAAE,CAAAH,QAAA,CAAC,MAEH,CAAc,CAAC,EACf,CACH,CACY,CAAC,GAhCCG,QAAQ,CAACM,EAiCd,CACf,CAAC,EACW,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAjB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}