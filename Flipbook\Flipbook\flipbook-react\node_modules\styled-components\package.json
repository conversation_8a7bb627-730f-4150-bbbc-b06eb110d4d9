{"name": "styled-components", "version": "5.3.11", "description": "Visual primitives for the component age. Use the best bits of ES6 and CSS to style your apps without stress", "main": "dist/styled-components.cjs.js", "jsnext:main": "dist/styled-components.esm.js", "module": "dist/styled-components.esm.js", "react-native": "native/dist/styled-components.native.cjs.js", "browser": {"./dist/styled-components.esm.js": "./dist/styled-components.browser.esm.js", "./dist/styled-components.cjs.js": "./dist/styled-components.browser.cjs.js"}, "sideEffects": ["./src/base.js"], "scripts": {"generateErrors": "node scripts/generateErrorMap.js", "prebuild": "rimraf dist && npm run generateErrors", "build": "rollup -c", "postbuild": "npm run lint:size", "flow": "flow check", "flow:watch": "flow-watch", "pretest": "npm run generateErrors", "test": "npm run test:web && npm run test:native && npm run test:primitives", "test:web": "jest -c ../../scripts/jest/config.main.js", "test:native": "jest -c ../../scripts/jest/config.native.js", "test:primitives": "jest -c ../../scripts/jest/config.primitives.js", "test:integration": "jest -c ../../scripts/jest/config.integration.js --runInBand --forceExit", "format": "eslint ./**/*.js --fix", "lint": "eslint src", "lint:size": "bundlewatch", "prettier": "prettier */**/*.js --write", "prepublishOnly": "npm run build", "dev": "cross-env BABEL_ENV=cjs babel-node example/startServer.js"}, "repository": {"type": "git", "url": "git+https://github.com/styled-components/styled-components.git"}, "files": ["CODE_OF_CONDUCT.md", "CONTRIBUTING.md", "dist", "native", "primitives", "scripts", "test-utils", "macro"], "keywords": ["react", "css", "css-in-js", "styled-components", "babel-macro", "babel-macros", "styling"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/styled-components/styled-components/issues"}, "homepage": "https://styled-components.com", "dependencies": {"@babel/helper-module-imports": "^7.0.0", "@babel/traverse": "^7.4.5", "@emotion/is-prop-valid": "^1.1.0", "@emotion/stylis": "^0.8.4", "@emotion/unitless": "^0.7.4", "babel-plugin-styled-components": ">= 1.12.0", "css-to-react-native": "^3.0.0", "hoist-non-react-statics": "^3.0.0", "shallowequal": "^1.1.0", "supports-color": "^5.5.0"}, "peerDependencies": {"react": ">= 16.8.0", "react-dom": ">= 16.8.0", "react-is": ">= 16.8.0"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.0.0", "babel-eslint": "^10.0.1", "eslint-plugin-flowtype": "^5.2.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-react": "^7.20.6", "flow-bin": "^0.132.0", "jest-serializer-html": "^7.0.0", "js-beautify": "^1.13.0", "prop-types": "^15.7.2", "react": "^16.8.6", "react-dom": "^16.8.6", "react-frame-component": "^4.0.2", "react-is": "^16.8.6", "react-native": "^0.63.4", "react-primitives": "^0.8.0", "react-test-renderer": "^16.8.6", "rollup": "^1.13.1", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-terser": "^5.0.0", "stylis-plugin-rtl": "^1.0.0"}, "bundlewatch": {"files": [{"path": "./dist/styled-components.min.js", "maxSize": "13kB"}]}, "collective": {"type": "opencollective", "url": "https://opencollective.com/styled-components"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/styled-components"}, "engines": {"node": ">=10"}}