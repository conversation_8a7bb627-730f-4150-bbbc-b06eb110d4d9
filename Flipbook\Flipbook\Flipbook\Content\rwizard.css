﻿@font-face {
    font-family: 'GillSansCE-Roman';
    src: url('../fonts/Flipbook/GillSansCE-Roman.eot');
    src: url('../fonts/Flipbook/GillSansCE-Roman.woff2') format('woff2'), url('../fonts/Flipbook/GillSansCE-Roman.woff') format('woff'), url('../fonts/Flipbook/GillSansCE-Roman.ttf') format('truetype'), url('../fonts/Flipbook/GillSansCE-Roman.svg#GillSansCE-Roman') format('svg'), url('../fonts/Flipbook/GillSansCE-Roman.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'GillSans';
    src: url('../fonts/gillsans.eot');
    src: local(''), url('../fonts/gillsans.woff') format('woff'), url('../fonts/gillsans.ttf') format('truetype'), url('../fonts/gillsans.svg') format('svg');
    font-weight: normal;
    font-style: normal;
}


@font-face {
    font-family: 'GillSans-Light';
    src: url('../fonts/Flipbook/GillSans-Light.eot');
    src: url('../fonts/Flipbook/GillSans-Light.woff2') format('woff2'), url('../fonts/Flipbook/GillSans-Light.woff') format('woff'), url('../fonts/Flipbook/GillSans-Light.ttf') format('truetype'), url('../fonts/Flipbook/GillSans-Light.svg#GillSans-Light') format('svg'), url('../fonts/Flipbook/GillSans-Light.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'GillSans-SemiBold';
    src: url('../fonts/Flipbook/GillSans-SemiBold.eot');
    src: url('../fonts/Flipbook/gillsans-semibold-webfont.woff2') format('woff2'), url('../fonts/Flipbook/gillsans-semibold-webfont.woff') format('woff'), url('../fonts/Flipbook/GillSans-SemiBold.ttf') format('truetype'), url('../fonts/Flipbook/GillSans-SemiBold.svg#GillSans-SemiBold') format('svg'), url('../fonts/Flipbook/GillSans-SemiBold.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'GillSans-Bold';
    src: url('../fonts/GillSans-Bold.eot');
    src: url('../fonts/GillSans-Bold.woff2') format('woff2'), url('../fonts/GillSans-Bold.woff') format('woff'), url('../fonts/GillSans-Bold.ttf') format('truetype'), url('../fonts/GillSans-Bold.svg#GillSans-Bold') format('svg'), url('../fonts/GillSans-Bold.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Optima';
    src: url('../fonts/Flipbook/Optima.woff') format('woff'), url('../fonts/Flipbook/Optima.ttf') format('truetype'), url('../fonts/Flipbook/Optima.svg#Optima') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Optima-Regular';
    src: url('../fonts/Flipbook/Optima.woff') format('woff'), url('../fonts/Flipbook/Optima.ttf') format('truetype'), url('../fonts/Flipbook/Optima.svg#Optima') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'AvenirNextLTPro-Regular';
    src: url('../fonts/AvenirNextLTPro-Regular.eot') format('embedded-opentype');
    src: url('../fonts/AvenirNextLTPro-Regular.woff') format('woff'), url('../fonts/AvenirNextLTPro-Regular.ttf') format('truetype'), url('../fonts/AvenirNextLTPro-Regular.svg#AvenirNextLTPro-Regular') format('svg'), url('../fonts/AvenirNextLTPro-Regular.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'AvenirNext-UltraLight';
    src: url('../fonts/AvenirNext-UltraLight.eot');
    src: url('../fonts/AvenirNext-UltraLight.woff') format('woff'), url('../fonts/AvenirNext-UltraLight.ttf') format('truetype'), url('../fonts/AvenirNext-UltraLight.svg') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'AvenirNext-Heavy';
    src: url('../fonts/Avenir Next Heavy.eot');
    src: url('../fonts/Avenir Next Heavy.woff') format('woff'), url('../fonts/Avenir Next Heavy.ttf') format('truetype'), url('../fonts/Avenir Next Heavy.svg') format('svg');
    font-weight: normal;
    font-style: normal;
}

body {
    font-family: 'GillSans-Light';
    margin-top: 68px;
}

ul.nostyle {
    margin: 0px;
    padding: 0px;
    display: inline-block;
}

    ul.nostyle > li {
        list-style-type: none;
        padding: 0px;
        margin: 0px;
    }

ul.inlinelist {
    margin: 0px;
    padding: 0px;
    display: inline-block;
}

    ul.inlinelist > li {
        float: left;
        list-style-type: none;
    }

.block {
    display: block !important;
    clear: both;
}

.navbar-default {
    background-color: #000000;
    border-color: #000000;
}

    .navbar-default .navbar-brand {
        color: #ffffff;
    }
/*ST-1135, BV*/
.navbar > .container .navbar-brand img, .navbar > .container-fluid .navbar-brand img {
    margin-top: 5px;
}

.navbar-default .navbar-brand:hover,
.navbar-default .navbar-brand:focus {
    color: #ffffff;
}

.navbar-default .navbar-text {
    color: #ffffff;
}

.navbar-default .navbar-nav > li > a {
    color: #ffffff;
    font-family: 'Gill Sans', Calibri, 'Trebuchet MS', sans-serif;
    font-size: 20px;
    text-transform: uppercase;
}

    .navbar-default .navbar-nav > li > a:hover,
    .navbar-default .navbar-nav > li > a:focus {
        color: #ffffff;
    }
/*ST-1135, BV*/
.navbar-default .navbar-collapse, .navbar-default .navbar-form {
    margin-left: -15px !important;
}

.navbar-default .navbar-nav > li > a.membername {
    font-family: 'GillSans-SemiBold';
    font-size: 24px;
    text-transform: capitalize;
    padding-top: 15px;
}

.navbar-default .navbar-nav.vcard > li:first-child {
    height: 67px;
}

.navbar-default .navbar-nav > li > a.notyou {
    text-decoration: underline;
    font-size: 12px;
    text-transform: capitalize;
    padding-top: 0px;
    line-height: 0px;
    text-align: right;
    position: absolute;
    right: 0px;
    bottom: 12px;
}

.navbar-default .navbar-nav > li > a.memberpic {
    padding: 0px;
    top: 20px;
}

    .navbar-default .navbar-nav > li > a.memberpic > img {
        height: 67px;
    }

.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus {
    color: #ffffff;
    background-color: #000000;
    font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif;
    font-size: 20px;
}

.navbar-default .navbar-nav > .open > a,
.navbar-default .navbar-nav > .open > a:hover,
.navbar-default .navbar-nav > .open > a:focus {
    color: #ffffff;
    background-color: #000000;
}

.navbar-default .navbar-toggle {
    border-color: #000000;
}

    .navbar-default .navbar-toggle:hover,
    .navbar-default .navbar-toggle:focus {
        background-color: #000000;
    }

    .navbar-default .navbar-toggle .icon-bar {
        background-color: #ffffff;
    }

.navbar-default .navbar-collapse,
.navbar-default .navbar-form {
    border-color: #ffffff;
}

.navbar-default .navbar-link {
    color: #ffffff;
}

    .navbar-default .navbar-link:hover {
        color: #ffffff;
    }

.navbar {
    border-radius: 0px;
    border: 0px;
    min-height: 68px;
    margin-bottom: 0px;
}

.navbar-brand {
    padding: 0 15px;
    height: 68px;
    line-height: 68px;
}

.navbar-toggle {
    /* (68px - button height 34px) / 2 = 23px */
    margin-top: 17px;
    padding: 9px 10px !important;
}


.mainpage {
    background-color: #D6D3E1;
    padding-left: 0px;
    padding-right: 0px;
}

    .mainpage a.close {
        color: #979797;
    }

    .mainpage .row {
        /*margin-left: 0px;
        margin-right: 0px;*/
    }

    .mainpage .wizardstep {
        width: calc(100% - 125px);
        display: inline-block;
        float: left;
        /*background-color: #fff; /*ST-1135, BV*/
        min-height: 957px;
        background: #FFF;
        position: relative;
        z-index: 9;
    }

        .mainpage .wizardstep .wizardheading {
            text-align: center;
            font-size: 55px;
            color: #410266;
            font-family: AvenirNextLTPro-Regular;
            font-weight: lighter;
            text-transform: uppercase;
            margin-top: 30px;
        }

            .mainpage .wizardstep .wizardheading .wizardhat {
                position: relative;
            }

                .mainpage .wizardstep .wizardheading .wizardhat img {
                    position: absolute;
                    right: 0px;
                    top: -5px;
                }

        .mainpage .wizardstep .subheading {
            font-family: "GillSans-Light";
            font-size: 15px;
            color: #000;
            text-align: center;
        }

        .mainpage .wizardstep .wizardstepcontent {
            padding-left: 25px;
            padding-right: 25px;
            background-color: #fff;
            min-height: 957px;
        }

        .inputlabel-cat,
        .mainpage .wizardstep .steplabel {
            color: #410260;
            font-size: 14px;
            margin-bottom: 0px;
            text-transform: uppercase;
            font-weight: 100;
        }

        .mainpage .wizardstep .stepname {
            margin: 0px;
            margin-bottom: 15px;
            color: #410260;
            font-size: 28px;
            text-transform: uppercase;
            font-family: 'GillSans-Light';
        }

        .mainpage .wizardstep .inputlabel {
            font-size: 22px;
            color: #410260;
            font-family: 'Optima-Regular';
            font-weight: 200;
        }

            .mainpage .wizardstep .inputlabel .required {
                font-size: 40px;
                font-weight: bold;
                font-style: normal;
                line-height: 1px;
                vertical-align: middle;
            }

            .mainpage .wizardstep .inputlabel .help {
                color: #9B9B9B;
                font-size: 14px;
                font-weight: 100;
            }

            .mainpage .wizardstep .inputlabel.autofilllabel {
                float: left;
            }

        .mainpage .wizardstep .selectinput {
            border: 1px solid #9B9B9B;
            color: #9B9B9B;
            font-size: 15px;
            font-weight: 100;
            display: block;
            font-family: 'GillSans';
            position: relative;
            background: #FFF url(../images/RW_select-arrow-b.jpg) no-repeat scroll 100% center / 25px 25px;
        }

}

[type="radio"]:checked,
[type="radio"]:not(:checked) {
    position: absolute;
    left: -9999px;
}

    [type="radio"]:checked + label,
    [type="radio"]:not(:checked) + label {
        position: relative;
        padding-left: 28px;
        cursor: pointer;
        line-height: 20px;
        display: inline-block;
        color: #666;
    }

        [type="radio"]:checked + label:before,
        [type="radio"]:not(:checked) + label:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 18px;
            height: 18px;
            border: 1px solid #ddd;
            border-radius: 100%;
            background: #fff;
        }

        [type="radio"]:checked + label:after,
        [type="radio"]:not(:checked) + label:after {
            content: '';
            width: 12px;
            height: 12px;
            background: #F87DA9;
            position: absolute;
            top: 4px;
            left: 4px;
            border-radius: 100%;
            -webkit-transition: all 0.2s ease;
            transition: all 0.2s ease;
        }

        [type="radio"]:not(:checked) + label:after {
            opacity: 0;
            -webkit-transform: scale(0);
            transform: scale(0);
        }

        [type="radio"]:checked + label:after {
            opacity: 1;
            -webkit-transform: scale(1);
            transform: scale(1);
        }

.form-group-select {
    position: relative;
}

    .form-group-select select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

        .form-group-select select::-ms-expand {
            display: none;
        }


.mainpage .wizardstep .textinput {
    border: 1px solid #979797;
    font-size: 15px;
    font-weight: 100;
    width: 100%;
}



.mainpage .wizardstep .specialchk {
    padding-left: 20px;
    background-size: contain;
    cursor: pointer;
    width: 25px;
    height: 25px;
    border: solid 1px #979797;
    display: inline-block;
    position: relative;
}

    .mainpage .wizardstep .specialchk label {
        cursor: pointer;
    }

    .mainpage .wizardstep .specialchk.checked {
        padding-left: 20px;
        position: relative;
    }

        .mainpage .wizardstep .specialchk.checked:before {
            position: absolute;
            width: 30px;
            height: 30px;
            content: '';
            background-image: url(/images/check-mark.png);
            background-repeat: no-repeat;
            background-position: left;
            background-position-y: center;
            background-size: 30px 30px;
            left: 2px;
            top: -9px;
        }


    .mainpage .wizardstep .specialchk .hdnchk {
        display: none;
    }

[type="radio"]:checked,
[type="radio"]:not(:checked) {
    position: absolute;
    left: -9999px;
}

    [type="radio"]:checked + label,
    [type="radio"]:not(:checked) + label {
        position: relative;
        padding-left: 0;
        cursor: pointer;
        line-height: 44px;
        display: inline-block;
        color: #4a4a4a;
        margin: 0;
        font-size: 12px;
        font-family: 'GillSans';
        font-weight: 300;
        width: 100%;
    }

    .centerRWTemplate [type="radio"]:checked + label,
    .centerRWTemplate [type="radio"]:not(:checked) + label{line-height:16px;}

        [type="radio"]:checked + label:before,
        [type="radio"]:not(:checked) + label:before {
            content: '';
            position: absolute;
            left: 71px;
            top: -14px;
            width: 20px;
            height: 20px;
            border: solid 1px #a4a4a4;
            border-radius: 100%;
            background: #fff;
        }

        /*R1 ST-809 */
        #divCtTemplateListing [type="radio"]:checked + label:before,
        #divCtTemplateListing [type="radio"]:not(:checked) + label:before {
            content: '';
            position: absolute;
            left: 45.5%;
            top: -23px;
            width: 20px;
            height: 20px;
            border: solid 1px #a4a4a4;
            border-radius: 100%;
            background: #fff;
        }


        [type="radio"]:checked + label:after,
        [type="radio"]:not(:checked) + label:after {
            content: '';
            width: 14px;
            height: 14px;
            background: #410260;
            position: absolute;
            top: -11px;
            left: 74px;
            border-radius: 100%;
            -webkit-transition: all 0.2s ease;
            transition: all 0.2s ease;
        }

        /*R1 ST-809*/
        #divCtTemplateListing [type="radio"]:checked + label:after,
        #divCtTemplateListing [type="radio"]:not(:checked) + label:after {
            content: '';
            width: 14px;
            height: 14px;
            background: #410260;
            position: absolute;
            top: -20px;
            left: 47.5%;
            border-radius: 100%;
            -webkit-transition: all 0.2s ease;
            transition: all 0.2s ease;
        }


        [type="radio"]:not(:checked) + label:after {
            opacity: 0;
            -webkit-transform: scale(0);
            transform: scale(0);
        }

        [type="radio"]:checked + label:after {
            opacity: 1;
            -webkit-transform: scale(1);
            transform: scale(1);
        }

.mainpage .wizardstep ul.autofillchks {
    margin: 0px;
    padding: 0px;
}

    .mainpage .wizardstep ul.autofillchks li {
        list-style-type: none;
        float: left;
        padding-left: 60px;
        padding-top: 10px;
    }

.mainpage .wizardstep .autofillchks label {
    color: #000;
    font-family: AvenirNextLTPro-Regular;
    font-weight: 200;
    font-size: 16px;
    padding-left: 8px;
    position: relative;
    top: -6px;
}

.mainpage .wizardstep .forminput {
    margin-bottom: 30px;
    min-height: 38px;
    position: relative;
}

.name-error {
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    line-height: 1.14;
    color: #c01f2a;
    font-family: 'GillSans';
    position: absolute;
    bottom: -40px;
    width: 100%;
}

.mainpage .wizardstep .wizardtemplategridview {
    display: inline-block;
    position: relative;
    text-align: left;
    width: calc(100% - 200px);
    min-height: 789px;
}

.mainpage .wizardstep .wizardtemplatesingleview {
    display: inline-block;
    position: relative;
    text-align: left;
    width: calc(100% - 200px);
    padding-top: 40px;
    padding-left: 50px;
    padding-right: 40px;
    padding-bottom: 40px;
}

    .mainpage .wizardstep .wizardtemplatesingleview .pointerleft,
    .mainpage .wizardstep .wizardtemplategridview .pointerleft {
        position: absolute;
        top: 50%;
        left: 15px;
        margin-top: -40px;
        height: 80px;
    }

    .mainpage .wizardstep .wizardtemplatesingleview .pointerright,
    .mainpage .wizardstep .wizardtemplategridview .pointerright {
        position: absolute;
        top: 50%;
        right: 5px;
        margin-top: -40px;
        height: 80px;
    }

.mainpage .wizardstep .wizardtemplatesingleviewdetail {
    min-height: 789px;
    margin-top: 40px;
    margin-bottom: 40px;
    position: relative;
}

    .mainpage .wizardstep .wizardtemplatesingleviewdetail .detailheading {
        font-family: GillSansCE-Roman;
        font-size: 25px;
        text-transform: capitalize;
        color: #4A4A4A;
        font-weight: 400;
        margin-top: 0px;
    }

    .mainpage .wizardstep .wizardtemplatesingleviewdetail p {
        font-family: GillSans-SemiBold;
        font-size: 12px;
        color: #000;
        font-weight: 900;
    }

    .mainpage .wizardstep .wizardtemplatesingleviewdetail .templatesmalldescription {
        font-family: GillSans-Light;
        font-size: 20px;
        font-weight: 200;
        margin-bottom: 30px;
        color: #4A4A4A;
    }

    .mainpage .wizardstep .wizardtemplatesingleviewdetail .templatekeywords {
        font-family: GillSans-SemiBold;
        font-size: 12px;
    }

/*As pe zeplin *ST-717* 03/28/2018/*/
.mainpage .wizardstep .wizardtemplatesingleview .templatebigpreview {
    min-height: 789px;
    width: 609.3px;
    /*width: 100%;*/
    /*margin-top: 40px;
    margin-left: 50px;
    margin-right: 40px;
    margin-bottom: 40px;*/
    height: 788.5px;
}

.mainpage .wizardstep .wizardtemplatesingleview .templateselectorradio,
.mainpage .wizardstep .wizardtemplategridview .templateselectorradio {
    border: 1px solid #57307a;
    padding: 3px 8px;
    background-color: #fff;
    border-radius: 18px;
    display: inline-block;
    position: relative;
}

    .mainpage .wizardstep .wizardtemplatesingleview .templateselectorradio .belt,
    .mainpage .wizardstep .wizardtemplategridview .templateselectorradio .belt {
        position: absolute;
        right: -15px;
        top: -5px;
        display: none;
        height: 37px;
    }

    .mainpage .wizardstep .wizardtemplatesingleview .templateselectorradio.checked .belt,
    .mainpage .wizardstep .wizardtemplategridview .templateselectorradio.checked .belt {
        display: block;
    }

    .mainpage .wizardstep .wizardtemplatesingleview .templateselectorradio label,
    .mainpage .wizardstep .wizardtemplategridview .templateselectorradio label {
        margin-left: 15px;
        font-family: Optima;
        font-size: 18px;
        font-weight: 400;
        text-transform: uppercase;
        color: #410260;
    }

/*ST-540*/
.mainpage .wizardstep a.autofill {
    font-family: 'GillSans-Light';
    font-style: normal;
    font-stretch: normal;
    line-height: 14px;
    text-decoration: underline;
    letter-spacing: normal;
    font-size: 12px;
    color: #9B9B9B;
    text-transform: none;
}
/*ST-540*/
.mainpage .wizardstep .staricon {
    height: 20px;
    width: 20px;
    background-image: url(../images/Compas-Icon.png);
    background-repeat: no-repeat;
    background-position: center;
    display: inline-block;
    vertical-align: middle;
}

/*ST-540*/
.mainpage .wizardstep .personalinfoform {
    max-height: 729px;
    margin-top: 19px;
}
    /*ST-540*/
    .mainpage .wizardstep .personalinfoform .inputlabel, .mainpage .wizardstep .personalinfoform .forminputb {
        font-size: 14px;
        font-family: 'Optima-Regular';
        font-weight: normal;
        font-style: normal;
        font-stretch: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #410260;
    }
        /*ST-540*/
        .mainpage .wizardstep .personalinfoform .inputlabel .required {
            font-size: 20px;
        }
        /*ST-540*/
        .mainpage .wizardstep .personalinfoform .inputlabel .help {
            font-size: 12px;
        }
    /*ST-540*/
    .mainpage .wizardstep .personalinfoform .forminput {
        margin-bottom: 15px;
        display: inline-block;
        width: 50%;
        min-width: 270px;
    }
    /*ST-540*/
    .mainpage .wizardstep .personalinfoform .hintpanel {
        padding: 0px 10px;
        position: absolute;
        right: 25px;
    }

    .mainpage .wizardstep .personalinfoform .ptext {
        display: block;
        padding-left: 5px;
        font-family: 'GillSans-Light';
        font-size: 12px;
        font-style: normal;
        font-stretch: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #9b9b9b;
    }

    /*ST-540*/
    .mainpage .wizardstep .personalinfoform .hintpanel .resumenamehint {
        /*chhanda #1287 8/4/2018*/
        /*position: relative;
        top: -20px;
        left: 20px;*/
        padding: 20px 15px;
        border: 5px solid #57307A;
        max-width: 160px;
        font-family: GillSansCE-Roman;
        font-size: 14px;
        background-color: #F9F9F9;
    }

    /*ST-540*/
    .mainpage .wizardstep .personalinfoform .hintpanel .flipbookurlhint {
        /*chhanda #1287 8/4/2018*/
        /*position: relative;
        top: -40px;
        left: 40px;*/
        padding: 30px 10px;
        border: 5px solid #4E72B3;
        width: 200px;
        height: 200px;
        border-radius: 50%;
        text-align: center;
        font-family: GillSansCE-Roman;
        font-size: 14px;
        background-color: #F9F9F9;
    }

    /*ST-540*/
    .mainpage .wizardstep .personalinfoform .hintpanel .additionallinkhint {
        /*chhanda #1287 8/4/2018*/
        /*position: relative;
        top: -80px;
        left: 0px;*/
        position: absolute;
        top: 260px;
        right: 70px;
        border: 5px solid #979797;
        padding: 20px 15px;
        width: 160px;
        height: 160px;
        font-family: GillSansCE-Roman;
        font-size: 14px;
        background-color: #F9F9F9;
    }

    /*ST-540*/
    .mainpage .wizardstep .personalinfoform .hintpanel .resumevisiblityhint {
        /*chhanda #1287 8/4/2018*/
        /*position: relative;
        top: -100px;
        left: 30px;*/
        border: 5px solid #69986C;
        padding: 20px 15px;
        width: 220px;
        font-family: GillSansCE-Roman;
        font-size: 14px;
        background-color: #F9F9F9;
    }
    /*ST-540*/
    .mainpage .wizardstep .personalinfoform .formitem {
    }
    /*ST-540*/
    .mainpage .wizardstep .personalinfoform .forminput input.textinput {
        width: 100%;
        display: block;
    }
        /*ST-540*/
        .mainpage .wizardstep .personalinfoform .forminput input.textinput.error {
            border: 1px solid #C0202A;
        }
    /*ST-540*/
    .mainpage .wizardstep .personalinfoform .forminput .input-group .input-group-addon {
        padding: 0px 5px;
        border-radius: 0px;
        border: 1px solid #979797;
        border-left: 0px;
        background-color: transparent;
        font-family: GillSans-SemiBold;
        font-weight: bold;
        font-size: 22px;
        color: #410260;
    }


    /*ST-540, ST-552*/
    .mainpage .wizardstep .personalinfoform .forminput .resumevisibilitysquare,
    .mainpage .wizardstep .skillscontainer .resumevisibilitysquare {
        width: 100%;
        height: 70px;
    }
        /*ST-540, ST-552*/
        .mainpage .wizardstep .personalinfoform .forminput .resumevisibilitysquare .strip,
        .mainpage .wizardstep .skillscontainer .resumevisibilitysquare .strip {
            width: 20px;
            margin: 5px;
            height: 60px;
            display: inline-block;
            position: relative;
            border-radius: 15px;
            background-color: #fff;
            border: 1px solid #AFAFAF;
            position: relative;
            cursor: pointer;
            background-image: url(../images/vertical-chk-strip.png);
            background-size: contain;
            background-repeat: no-repeat;
        }

/*ST-566*/
.mainpage .wizardstep .skillscontainer .strip.horizontal {
    width: 40px;
    margin: 5px;
    height: 20px;
    display: inline-block;
    position: relative;
    border-radius: 15px;
    background-color: #D8D8D8;
    border: 1px solid #AFAFAF;
    position: relative;
    cursor: pointer;
}
/*ST-540, ST-552*/
.mainpage .wizardstep .personalinfoform .forminput .resumevisibilitysquare .strip .thumb,
.mainpage .wizardstep .skillscontainer .resumevisibilitysquare .strip .thumb {
    width: 18px;
    height: 18px;
    background: #57307A;
    -moz-border-radius: 50%;
    -webkit-border-radius: 50%;
    border-radius: 50%;
    display: inline-block;
    -webkit-transition: top 2s linear; /* For Safari 3.1 to 6.0 */
    transition: top 2s linear;
}

/*ST-566*/
.mainpage .wizardstep .skillscontainer .strip.horizontal .thumb {
    width: 18px;
    height: 18px;
    background: #57307A;
    -moz-border-radius: 50%;
    -webkit-border-radius: 50%;
    border-radius: 50%;
    display: inline-block;
    -webkit-transition: top 2s linear; /* For Safari 3.1 to 6.0 */
    transition: top 2s linear;
}

.nopadding {
    padding: 0px;
}

.txtcenter {
    text-align: center;
}

.fixtotop {
    position: absolute;
    top: 0px;
    left: 0px;
}

.fixtoright {
    position: absolute;
    top: 0px;
    left: auto;
    right: 5px;
}

.fixtobottom {
    position: absolute;
    top: auto;
    bottom: 0px;
    left: 0px;
}

.mainpage .wizardstep .button {
    color: #410260;
    background-color: #FFFFFF;
    border-color: #410260;
    text-transform: uppercase;
    border-radius: 15px;
    height: 32px;
    font-family: 'Optima-Regular';
    outline: none;
    min-width: 100px;
    position: relative;
}

    .mainpage .wizardstep .button .belt {
        background-image: url(../images/belt.png);
        background-position: center;
        background-repeat: no-repeat;
        background-size: 30px;
        width: 30px;
        height: 42px;
        position: absolute;
        right: -20px;
        top: -5px;
    }

    .mainpage .wizardstep .button .save {
        background-image: url(../images/save-icon.png);
        background-position: center;
        background-repeat: no-repeat;
        background-size: 15px;
        width: 15px;
        height: 15px;
        padding: 5px;
        display: inline-block;
    }

    .mainpage .wizardstep .button:hover,
    .mainpage .wizardstep .button:focus,
    .mainpage .wizardstep .button:active,
    .mainpage .wizardstep .button.active {
        color: #410260;
        background-color: #FFFFFF;
        border-color: #410260;
    }

    .mainpage .wizardstep .button:active,
    .mainpage .wizardstep .button.active {
        background-image: none;
    }

.mainpage .sidebar {
    float: left;
    /*width: 9.2%;*/
    background-color: #D6D3E1;
    min-height: 957px;
    padding-left: 0px;
    padding-right: 0px;
    display: inline-block;
    position: relative;
    width: 125px;
}

    .mainpage .sidebar .leftbottomsticker {
        display: inline;
        position: absolute;
        bottom: 8%;
        left: 0px;
        width: 100%;
        text-align: center;
    }

    .mainpage .sidebar .menu > li {
        list-style-type: none;
        padding-top: 20px;
        padding-bottom: 20px;
        position: relative;
    }

        .mainpage .sidebar .menu > li.done {
            background-image: url(/images/Check-mark.png);
            background-position-x: 20px;
            background-position-y: 15px;
            background-repeat: no-repeat;
        }

        .mainpage .sidebar .menu > li > .pointer {
            display: none;
        }

    .mainpage .sidebar .menu li.active > .pointer {
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 20px 0 20px 15px;
        border-color: transparent transparent transparent #d6d3e1;
        position: absolute;
        right: -15px;
        top: 40px;
        display: block;
        z-index: 1;
    }

    .mainpage .sidebar .menu {
        padding-left: 0px;
        padding-bottom: 0px;
        padding-top: 10px;
    }


        .mainpage .sidebar .menu > li > a,
        .mainpage .sidebar .menu > li > ul > li > a {
            text-align: center;
            font-size: 14px;
            text-transform: capitalize;
            display: block;
            color: #000;
        }

            .mainpage .sidebar .menu > li > a:hover,
            .mainpage .sidebar .menu > li > ul > li > a:hover {
                text-decoration: none;
            }

            .mainpage .sidebar .menu > li > a > span,
            .mainpage .sidebar .menu > li > ul > li > a {
                display: block;
                font-family: 'GillSans-Light';
            }

            .mainpage .sidebar .menu > li > a > img {
                max-height: 37px;
                display: inline;
            }

        /*ST-550*/
        .mainpage .sidebar .menu > li > ul {
            margin: 0px;
            padding: 0px;
        }
            /*ST-550*/
            .mainpage .sidebar .menu > li > ul > li > a {
                text-align: left;
            }
            /*ST-550*/
            .mainpage .sidebar .menu > li > ul > li {
                margin: 0px;
                padding: 0px;
                padding-left: 30px;
                padding-top: 10px;
            }

                /*ST-550*/
                /*BV,  ST-1207*/
                .mainpage .sidebar .menu > li > ul > li.done {
                    background-image: url(/images/Check-mark.png);
                    background-position-x: 14px;
                    background-position-y: 8px;
                    background-size: 20px;
                    background-repeat: no-repeat;
                }

.mainpage .wizardstep .filtersidebar {
    min-height: 957px;
    background-color: #E9EFF8;
    width: 200px;
    float: left;
    padding-top: 55px;
    padding-left: 15px;
    padding-right: 15px;
    position: relative;
    text-align: left;
}

    .mainpage .wizardstep .filtersidebar .filterheading {
        font-family: GillSansCE-Roman;
        font-size: 19px;
        font-weight: 400;
        text-align: center;
        display: block;
        text-transform: uppercase;
    }

    .mainpage .wizardstep .filtersidebar .filtersubheading {
        font-family: GillSansCE-Roman;
        font-size: 14px;
        font-weight: 400;
        text-align: left;
        display: block;
        text-transform: uppercase;
    }

    .mainpage .wizardstep .filtersidebar #chosenfilters {
        max-height: 300px;
        overflow-y: auto;
    }

    .mainpage .wizardstep .filtersidebar .filterpanel {
        padding-bottom: 10px;
        margin-bottom: 10px;
        border-bottom: 1px solid #A3A3A5;
        position: relative;
    }

        .mainpage .wizardstep .filtersidebar .filterpanel.fixtobottom {
            position: absolute;
            bottom: 10px;
            left: 0px;
            border-bottom: 0px;
        }

            .mainpage .wizardstep .filtersidebar .filterpanel.fixtobottom .templateview {
                margin: 0px;
                padding: 0px;
                margin-left: 30px;
            }

                .mainpage .wizardstep .filtersidebar .filterpanel.fixtobottom .templateview li {
                    list-style-type: none;
                    width: 57px;
                    height: 74px;
                    float: left;
                    text-align: center;
                }


                    .mainpage .wizardstep .filtersidebar .filterpanel.fixtobottom .templateview li label {
                        font-family: GillSans-SemiBold;
                        color: #000;
                        font-size: 12px;
                        text-transform: uppercase;
                        display: block;
                        font-weight: 700;
                    }

                    .mainpage .wizardstep .filtersidebar .filterpanel.fixtobottom .templateview li .glyphicon {
                        color: #fff;
                        font-size: 30px;
                        padding: 5px;
                        border: 3px solid transparent;
                    }

                    .mainpage .wizardstep .filtersidebar .filterpanel.fixtobottom .templateview li.active .glyphicon {
                        border: 3px solid #000;
                    }

        .mainpage .wizardstep .filtersidebar .filterpanel .filteroptionlist {
            margin: 0px;
            padding: 0px;
            margin-top: 10px;
            margin-bottom: 0px;
        }

        .mainpage .wizardstep .filtersidebar .filterpanel select {
            width: 100%;
        }

        .mainpage .wizardstep .filtersidebar .filterpanel a {
            font-family: GillSans-Light;
            font-size: 13px;
            color: #57307A;
        }

        .mainpage .wizardstep .filtersidebar .filterpanel .filterpanelcollapse {
            height: 25px;
            width: 25px;
            border-radius: 25px;
            color: #9B9B9B;
            border: 1px solid #9B9B9B;
            background-color: transparent;
            padding: 0px;
            position: absolute;
            top: 0px;
            right: 0px;
            outline: none;
        }

button:focus, button:active {
    outline: none;
}

.magicradio {
    cursor: pointer;
}

.mainpage .wizardstep .filtersidebar .filterpanel .filterpanelcollapse span {
    font-family: GillSans-Light;
    font-weight: 100;
    font-size: 25px;
}

.mainpage .wizardstep .filtersidebar .filterpanel a:hover,
.mainpage .wizardstep .filtersidebar .filterpanel a:focus,
.mainpage .wizardstep .filtersidebar .filterpanel a:visited {
    text-decoration: none;
}

.mainpage .wizardstep .filtersidebar .filterpanel .filteroptionlist > li {
    list-style-type: none;
    padding-left: 10px;
    margin-bottom: 10px;
}

    .mainpage .wizardstep .filtersidebar .filterpanel .filteroptionlist > li .specialchk img {
        margin-right: 10px;
    }

    .mainpage .wizardstep .filtersidebar .filterpanel .filteroptionlist > li label {
        font-family: GillSans-Light;
        font-size: 15px;
        font-weight: 100;
        color: #4A4A4A;
    }

.mainpage .wizardstep .inputerror {
    color: #C01F2A;
    font-size: 14px;
    font-family: GillSansCE-Roman;
    display: inline-block;
    font-weight: 500;
    background-image: url(../images/Exclamation-15x15.png);
    background-repeat: no-repeat;
    background-position-y: top;
    background-position-x: left;
    margin: 3px;
    padding-left: 20px;
}

#chosenfilters .selectedfilteroptionslist {
    margin: 0px;
    padding: 0px;
    margin-bottom: 20px;
}

    #chosenfilters .selectedfilteroptionslist li {
        list-style-type: none;
        display: inline-block;
        margin: 3px;
    }

        #chosenfilters .selectedfilteroptionslist li button {
            border-radius: 15px;
            font-family: GillSans-Light;
            font-size: 13px;
            color: #4A4A4A;
            background-color: #D8D8D8;
            padding: 3px 10px;
            border: none;
        }

/*ST711*/
.mainpage .wizardstep .imageeditormenu {
    margin: 0px;
    padding: 0px;
    height: 44px;
    width: 100%;
    display: table;
    background-color: #F7F7F7;
}

    /*ST711*/
    .mainpage .wizardstep .imageeditormenu li {
        margin: 0px;
        padding: 10px;
        display: table-cell;
        width: 20%;
        text-align: left;
        line-height: 24px;
    }

        /*ST711*/
        .mainpage .wizardstep .imageeditormenu li .subheading {
            margin: 0px;
            padding: 0px;
            font-family: 'GillSans-Light';
            font-size: 20px;
            color: #3A3B39;
            text-align: left;
            font-style: normal;
            font-stretch: normal;
            line-height: 24px;
            letter-spacing: normal;
        }

        /*ST711*/
        .mainpage .wizardstep .imageeditormenu li > a .crop {
            background-image: url(../images/crop.png);
            width: 24px;
            height: 24px;
            display: inline-block;
        }

        /*ST711*/
        .mainpage .wizardstep .imageeditormenu li > a {
            font-family: GillSans-SemiBold;
            font-size: 12px;
            color: #000;
            height: 25px;
            display: block;
            vertical-align: baseline;
            background-repeat: no-repeat;
            padding-left: 30px;
            padding-top: 0px;
            line-height: 25px;
            position: relative;
        }

            /*ST711*/
            .mainpage .wizardstep .imageeditormenu li > a.crop {
                /*background-image: url(../images/crop.png);*/
            }

                .mainpage .wizardstep .imageeditormenu li > a.crop span {
                    background-image: url(../images/crop.png);
                    background-repeat: no-repeat;
                    position: absolute;
                    width: 24px;
                    height: 24px;
                    left: 0px;
                }

            /*ST711*/
            .mainpage .wizardstep .imageeditormenu li > a.flip {
                padding-left: 35px;
            }

                .mainpage .wizardstep .imageeditormenu li > a.flip span {
                    background-image: url(../images/flip.png);
                    background-repeat: no-repeat;
                    position: absolute;
                    width: 28px;
                    height: 18px;
                    left: 0px;
                    top: 5px;
                }

            /*ST711*/
            .mainpage .wizardstep .imageeditormenu li > a.frame span {
                background-image: url(../images/frame.png);
                background-repeat: no-repeat;
                position: absolute;
                width: 24px;
                height: 24px;
                left: 0px;
            }

            /*ST711*/
            .mainpage .wizardstep .imageeditormenu li > a.opacity {
                padding-left: 35px;
            }

                .mainpage .wizardstep .imageeditormenu li > a.opacity span {
                    background-image: url(../images/opacity.png);
                    background-repeat: no-repeat;
                    position: absolute;
                    width: 27px;
                    height: 26px;
                    left: 0px;
                }

/*ST711*/
.mainpage .wizardstep .wizardstepcontent .imageuploadcontainer {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    align-items: flex-start;
    align-content: flex-start;
    width: 100%;
    margin-top: 44px;
}

    /*ST711*/
    .mainpage .wizardstep .wizardstepcontent .imageuploadcontainer > .imageupload {
        width: 190px;
        background-color: #F8FBFD;
        border: 1px solid #DFEAF7;
        position: relative;
        padding: 11px 8px 8px 8px;
        margin: 5px;
        margin-right: 0px;
    }

        .mainpage .wizardstep .wizardstepcontent .imageuploadcontainer > .imageupload:first-child {
            margin-left: 0px;
        }

        /*ST711*/
        .mainpage .wizardstep .wizardstepcontent .imageuploadcontainer > .imageupload.active {
            border: 1px solid #2A5BB4;
        }

        /*ST711*/
        .mainpage .wizardstep .wizardstepcontent .imageuploadcontainer > .imageupload h3 {
            font-family: 'GillSans';
            font-size: 14px;
            color: #410260;
            text-transform: uppercase;
            margin: 0px;
            padding: 0px;
            margin-bottom: 25px;
            margin-top: 0px;
            text-align: center;
            font-weight: normal;
            font-style: normal;
            font-stretch: normal;
            line-height: 16px;
            letter-spacing: normal;
        }

        .mainpage .wizardstep .wizardstepcontent .imageuploadcontainer > .imageupload .help {
            font-family: 'GillSans-Light';
            font-size: 12px;
            font-style: normal;
            font-stretch: normal;
            line-height: 20px;
            letter-spacing: 0.1px;
            text-align: left;
            color: #7a7a7a;
        }

.mainpage .wizardstep .wizardstepcontent .helpnote {
    font-family: 'GillSans-Light';
    font-size: 12px;
    font-style: normal;
    font-stretch: normal;
    line-height: 14px;
    letter-spacing: 0.1px;
    text-align: left;
    color: #7a7a7a;
    padding-right: 20px;
}
/*ST711*/
.mainpage .wizardstep .wizardstepcontent .imageuploadcontainer > .imageupload button.remove {
    font-family: GillSans-Light;
    font-size: 12px;
    color: #7A7A7A;
    position: absolute;
    background: none;
    border: none;
    right: 5px;
    top: 5px;
}

/*ST711*/
.mainpage .wizardstep .wizardstepcontent .imageuploadcontainer > .imageupload .magicfileuploadcontainer {
    position: relative;
    height: 25px;
}

    /*ST711*/
    .mainpage .wizardstep .wizardstepcontent .imageuploadcontainer > .imageupload .magicfileuploadcontainer input[type=file] {
        width: 100%;
        height: 25px;
        position: absolute;
        top: 0px;
        left: 0px;
        opacity: 0;
        z-index: 1;
    }
/*ST-711*/
.mainpage .wizardstep .wizardstepcontent .imageuploadcontainer > .imageupload .forminput .input-group .input-group-addon {
    padding: 0px 5px;
    border-radius: 0px;
    border: 1px solid #979797;
    border-left: 0px;
    background-color: transparent;
    font-family: GillSans-SemiBold;
    font-weight: bold;
    font-size: 22px;
    color: #410260;
    line-height: 18px;
}
/*ST711*/
.mainpage .wizardstep .wizardstepcontent .imageuploadcontainer > .imageupload .magicfileuploadcontainer .uploadicon {
    height: 15px;
}

/*ST711*/
.mainpage .wizardstep .wizardstepcontent .imageuploadcontainer > .imageupload .imageplaceholder {
    height: 180px;
    width: 180px;
    background-image: url(../images/picture-box.png);
    background-repeat: no-repeat;
    margin-top: 20px;
    background-size: cover;
}

    /*ST711*/
    .mainpage .wizardstep .wizardstepcontent .imageuploadcontainer > .imageupload .imageplaceholder > img {
        width: auto;
        height: auto;
        max-width: 100%;
        border: 1px solid #000;
    }

/*ST-549*/
.mainpage .wizardstep .wizardstepcontent .achievementscontainer {
    min-width: 380px;
    width: 68%;
    position: relative;
    background-color: #fff;
    z-index: 2;
}
/*ST-549*/
.mainpage .wizardstep .wizardstepcontent .purposehint {
    width: 200px;
    position: absolute;
    left: calc(100% - 200px);
    top: 80px;
    border: 3px solid #2A5AB4;
    padding: 10px;
    display: inline-block;
    font-family: GillSansCE-Roman;
    font-size: 13px;
    background-color: #F9F9F9;
}
    /*ST-549*/
    .mainpage .wizardstep .wizardstepcontent .purposehint a.close {
        position: absolute;
        right: 5px;
        top: 0px;
        font-size: 20px;
        font-family: GillSans-Light;
        font-weight: 100;
        opacity: 1;
    }

/*ST-549*/
.mainpage .wizardstep .wizardstepcontent .promotedemotehint {
    padding: 36px 8px;
    border: 3px solid #573079;
    width: 162px;
    height: 162px;
    border-radius: 50%;
    text-align: center;
    font-family: GillSansCE-Roman;
    font-size: 13px;
    background-color: #F9F9F9;
    position: absolute;
    top: 29%;
    right: -28px;
    z-index: 1;
}

/*ST-712*/
.mainpage .wizardstep .wizardstepcontent .twopageresumeshint {
    padding: 10px;
    border: 4px solid #4E71B3;
    width: 174px;
    font-family: GillSansCE-Roman;
    font-size: 13px;
    background-color: #F9F9F9;
    position: relative;
    left: calc(100% - 100px);
    z-index: 1;
}
    /*ST-713*/
    .mainpage .wizardstep .wizardstepcontent .twopageresumeshint.tag-promotedemotehint {
        position: absolute;
        top: calc(25% + 250px);
        left: auto;
        right: 5px;
    }
/*ST-712*/
.mainpage .wizardstep .twopageresumewarning {
    position: absolute;
    bottom: 5%;
    left: 70px;
}

/*ST-712, ST-552*/
.mainpage .wizardstep .achievementscontainer h6,
.mainpage .wizardstep .skillscontainer h6 {
    margin: 0px;
    padding: 0px;
    margin-left: 10px;
    margin-right: 20px;
    border: none;
    font-family: GillSans-Light;
    color: #9B9B9B;
    font-size: 15px;
    text-transform: uppercase;
    padding: 3px;
    padding-left: 10px;
}

/* ST-552*/
.mainpage .wizardstep .skillscontainer h6 {
    margin: 0px;
    padding: 0px;
}

/*ST-713*/
.mainpage .wizardstep .achievementscontainer h5 {
    margin: 0px;
    padding: 0px;
    margin-left: 10px;
    margin-right: 20px;
    border: none;
    font-family: GillSans-SemiBold;
    color: #9B9B9B;
    font-size: 15px;
    text-transform: uppercase;
    padding: 3px;
    padding-left: 10px;
    font-weight: bold;
}

/*ST-712*/
.glyphicon.glyphicon-one-fine-dot:before {
    content: "\25cf";
}
/*ST-549*/
.mainpage .wizardstep .wizardstepcontent .addcategoryhint {
    border: 4px solid #416518;
    width: 266px;
    padding: 5px;
    text-align: left;
    font-family: GillSansCE-Roman;
    font-size: 14px;
    background-color: #F9F9F9;
    position: absolute;
    top: -6px;
    right: -83px;
}
/*ST-537*/
.mainpage .wizardstep .wizardstepcontent .addinteresthint {
    position: relative;
    width: 216px;
    height: 216px;
    border-radius: 50%;
    margin-top: 8px;
    border: 3px solid #416518;
    text-align: left;
    font-family: 'GillSansCE-Roman';
    font-size: 13px;
    background-color: #F9F9F9;
    position: relative;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 16px;
    letter-spacing: normal;
    text-align: left;
    color: #6d6d6d;
    text-align: center;
}

    .mainpage .wizardstep .wizardstepcontent .addinteresthint .inntersthint_text {
        box-sizing: border-box;
        overflow-wrap: break-word;
        padding-top: 25px;
    }

/*ST-549*/
.mainpage .wizardstep .wizardstepcontent .promotedemotehint a.close {
    position: absolute;
    left: 45%;
    margin-left: -5px;
    top: 0px;
    font-family: 'GillSans-Light';
    font-size: 20px;
}
/*ST-549, ST-537*/
.mainpage .wizardstep .wizardstepcontent .addcategory a.close,
.mainpage .wizardstep .wizardstepcontent .addinteresthint a.close {
    position: absolute;
    right: 5px;
    top: 5px;
    font-size: 14px;
}

.mainpage .wizardstep .wizardstepcontent .addinteresthint a.close {
    left: 45%;
    font-size: 16px !important;
}
/*ST-549*/
.mainpage .wizardstep .wizardstepcontent a.help.blue {
    color: #410260;
    font-family: GillSansCE-Roman;
    font-size: 14px;
    text-transform: none;
}

/*ST-549*/
.mainpage .wizardstep .wizardstepcontent .categoryorder {
    margin: 0px;
    padding: 0px;
    margin-left: 10px;
    margin-right: 20px;
    margin-bottom: 25px;
}

    /*ST-549 ,ST-1198*/
    .mainpage .wizardstep .wizardstepcontent .categoryorderPageTwo > li,
    .mainpage .wizardstep .wizardstepcontent .categoryorderRight > li,
    .mainpage .wizardstep .wizardstepcontent .categoryorder > li {
        list-style-type: none;
        margin-bottom: 6px;
    }



        /*ST-549 ,ST-1198*/
        .mainpage .wizardstep .wizardstepcontent .categoryorderPageTwo > li > div.categoryitem,
        .mainpage .wizardstep .wizardstepcontent .categoryorderPageTwo > li .subcategoryorder > li > div.categoryitem,
        .mainpage .wizardstep .wizardstepcontent .categoryorderRight > li > div.categoryitem,
        .mainpage .wizardstep .wizardstepcontent .categoryorderRight > li .subcategoryorder > li > div.categoryitem,
        .mainpage .wizardstep .wizardstepcontent .categoryorder > li > div.categoryitem,
        .mainpage .wizardstep .wizardstepcontent .categoryorder > li .subcategoryorder > li > div.categoryitem {
            border: 1px solid #979797;
            font-family: 'GillSans-Light';
            color: #9B9B9B;
            font-size: 15px;
            text-transform: uppercase;
            padding: 0px 5px 0px 12px;
            padding-left: 10px;
            width: calc(100% - 35px);
            display: inline-block;
            margin-right: 5px;
            height: 25px;
            line-height: 25px;
        }
        /* ST-1198*/
        .mainpage .wizardstep .wizardstepcontent .categoryorderPageTwo > li > div.categoryitem,
        .mainpage .wizardstep .wizardstepcontent .categoryorderRight > li > div.categoryitem,
        .mainpage .wizardstep .wizardstepcontent .categoryorder > li > div.categoryitem {
            background-color: #FAFAFA;
        }
        /*ST-549  ,ST-1198*/
        .mainpage .wizardstep .wizardstepcontent .categoryorderPageTwo > li .subcategoryorder > li > div.categoryitem,
        .mainpage .wizardstep .wizardstepcontent .categoryorderRight > li .subcategoryorder > li > div.categoryitem,
        .mainpage .wizardstep .wizardstepcontent .categoryorder > li .subcategoryorder > li > div.categoryitem {
            text-transform: none;
        }
            /*ST-549*/

            .mainpage .wizardstep .wizardstepcontent .categoryorder > li > div.categoryitem .icon,
            .mainpage .wizardstep .wizardstepcontent .categoryorder > li .subcategoryorder > li > div.categoryitem .icon {
                /*margin-top: 3px;
                margin-left: 8px;*/
                cursor: pointer;
                font-weight: lighter;
                font-size: 12px;
            }

            /*ST-549, ST-537 ,ST-1198*/
            .mainpage .wizardstep .wizardstepcontent .categoryorderPageTwo > li > div.categoryitem .close,
            .mainpage .wizardstep .wizardstepcontent .categoryorderPageTwo > li .subcategoryorder > li > div.categoryitem .close,
            .mainpage .wizardstep .wizardstepcontent .categoryorderRight > li > div.categoryitem .close,
            .mainpage .wizardstep .wizardstepcontent .categoryorderRight > li .subcategoryorder > li > div.categoryitem .close,
            .mainpage .wizardstep .wizardstepcontent .categoryorder > li > div.categoryitem .close,
            .mainpage .wizardstep .wizardstepcontent .categoryorder > li .subcategoryorder > li > div.categoryitem .close,
            .mainpage .wizardstep .achievementscontainer .currentinterestlist > .currentinterest .close {
                padding: 5px;
            }

        /*ST-549*/
        .mainpage .wizardstep .wizardstepcontent .categoryorder > li .subcategoryorder {
            margin: 0px;
            padding: 0px;
            margin-left: 40px;
        }
            /*ST-549*/
            .mainpage .wizardstep .wizardstepcontent .categoryorder > li .subcategoryorder > li {
                list-style-type: none;
                margin-bottom: 0px;
            }


/*ST-549, ST-714, ST-534, ST-531, ST-1163*/
.mainpage .wizardstep .wizardstepcontent .button.addcategory,
.mainpage .wizardstep .wizardstepcontent .button.addhighlight,
.mainpage .wizardstep .wizardstepcontent .button.addaccreditation,
.mainpage .wizardstep .wizardstepcontent .button.addaffiliation,
.mainpage .wizardstep .wizardstepcontent .button.addLanguage,
.mainpage .wizardstep .wizardstepcontent .button.addcommunityservice {
    text-transform: none;
    height: 23px;
    color: #9B9B9B;
    border-color: #9B9B9B;
    padding-top: 0px;
}

/*ST-550*/
.mainpage .wizardstep .wizardstepcontent .underlinedheading {
    display: inline-block;
    min-width: 250px;
    max-width: 60%;
    border-bottom: 1px solid #410260;
    padding: 5px 0px;
    font-family: Optima;
    font-size: 24px;
    text-transform: uppercase;
    color: #410260;
}

    /*ST-552*/
    .mainpage .wizardstep .wizardstepcontent .underlinedheading.skillset {
        max-width: 100%;
        margin: 0px;
        margin-bottom: 15px;
    }

/*ST-550*/
.mainpage .wizardstep .wizardstepcontent .witheraser-right {
    background-image: url(../images/eraser.png);
    background-repeat: no-repeat;
    background-position-x: right;
    background-position-y: center;
    background-size: 20px;
}

/*ST-550*/
.mainpage .wizardstep .wizardstepcontent .achievementsform {
    background-color: #F8FBFD;
    padding: 5px;
    /*margin-top: 25px;*/
    max-width: 70%;
    position: relative;
}

    /*ST-714*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform.full {
        max-width: 100%;
    }
    /*ST-550*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform .forminput {
        margin-bottom: 5px;
    }

/*ST-534*/
.mainpage .wizardstep .wizardstepcontent .affiliationdetailslist {
    margin: 0px;
    padding: 0px;
}

    /*ST-534*/
    .mainpage .wizardstep .wizardstepcontent .affiliationdetailslist > li {
        list-style-type: none;
        margin-bottom: 5px;
    }

        /*ST-534*/
        .mainpage .wizardstep .wizardstepcontent .affiliationdetailslist > li > div.affiliationdetailitem {
            border: 2px solid #979797;
            font-family: GillSans-Light;
            color: #9B9B9B;
            font-size: 15px;
            text-transform: uppercase;
            padding: 3px;
            padding-left: 10px;
            width: calc(100% - 28px);
            display: inline-block;
            margin-right: 5px;
        }

            /*ST-534*/
            .mainpage .wizardstep .wizardstepcontent .affiliationdetailslist > li > div.affiliationdetailitem a.close {
                font-size: 14px;
                padding: 5px;
            }
        /*ST-534*/
        .mainpage .wizardstep .wizardstepcontent .affiliationdetailslist > li > span.fixtoright {
            font-size: 17px;
            top: 5px;
        }

/*ST-534*/
.mainpage .wizardstep .wizardstepcontent .dropdown.displayformat .btn.dropdown-toggle {
    padding: 0px 10px;
}
/*ST-534*/
.mainpage .wizardstep .wizardstepcontent .achievementsform .affiliationheading {
    font-family: Optima;
    font-size: 24px;
    text-transform: uppercase;
    color: #410260;
    padding-left: 10px;
    margin: 0px;
    margin-bottom: 15px;
}

/*ST-534*/
.mainpage .wizardstep .wizardstepcontent .affiliationform {
    padding: 10px;
    border: 1px solid #DFEAF7;
    padding-right: 40px;
}

/*ST-531*/
.mainpage .wizardstep .wizardstepcontent .communityserviceform {
    padding: 10px;
    border: 1px solid #DFEAF7;
    padding-right: 15px;
}

/*ST-537*/
.mainpage .wizardstep .wizardstepcontent .interestform {
    padding: 5px;
    border: 1px solid #DFEAF7;
}

/*ST-714*/
.mainpage .wizardstep .wizardstepcontent .wid70 {
    width: 70% !important;
}
/*General*/
.mainpage .wizardstep .wizardstepcontent .wid75 {
    width: 75% !important;
}

/*General*/
.mainpage .wizardstep .wizardstepcontent .wid80 {
    width: 80% !important;
}
/*General*/
.mainpage .wizardstep .wizardstepcontent .wid85 {
    width: 85% !important;
}

/*General*/
.mainpage .wizardstep .wizardstepcontent .wid100 {
    width: 100% !important;
}
/*General*/
.mainpage .wizardstep .wizardstepcontent .wid65 {
    width: 65% !important;
}
/*General*/
.mainpage .wizardstep .wizardstepcontent .wid90 {
    width: 90% !important;
}
/*General*/
.mainpage .wizardstep .wizardstepcontent .wid95 {
    width: 95% !important;
}
/*ST-714*/
.mainpage .wizardstep .wizardstepcontent .wid60 {
    width: 60% !important;
}

.mainpage .wizardstep .wizardstepcontent .wid50 {
    width: 50% !important;
}

.mainpage .wizardstep .wizardstepcontent .height200 {
    height: 200px !important;
}

.mainpage .wizardstep .wizardstepcontent .height100 {
    height: 100px !important;
}

.mainpage .wizardstep .wizardstepcontent .wid250 {
    width: 250px !important;
    height: 25px !important;
}

.mainpage .wizardstep .wizardstepcontent .wid280 {
    width: 280px !important;
    height: 25px !important;
}

.mainpage .wizardstep .wizardstepcontent .wid321 {
    width: 321px !important;
    height: 38px !important;
}

/*ST-550*/
.mainpage .wizardstep .wizardstepcontent .achievementsform .inputlabel {
    font-size: 14px;
    font-family: GillSansCE-Roman;
    color: #410260;
}
    /*ST-550*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform .inputlabel .required {
        font-size: 15px;
        vertical-align: middle;
    }
/*ST-550*/
.mainpage .wizardstep .wizardstepcontent .eraser,
.mainpage .wizardstep .wizardstepcontent .achievementsform .eraser,
.mainpage .wizardstep .wizardstepcontent #accreditationstab .eraser {
    background-image: url(../images/eraser.png);
    background-repeat: no-repeat;
    background-position-x: center;
    background-position-y: center;
    background-size: 18px;
    height: 20px;
    width: 20px;
    display: inline-block;
    margin-right: 5px;
}
/*ST-550*/
.mainpage .wizardstep .wizardstepcontent /*.achievementsform*/ .close {
    height: 20px;
    width: 20px;
    color: #979797;
    font-size: 14px;
    text-align: center;
    font-family: GillSans-Light;
    font-weight: 100;
    opacity: 1;
}

/*ST-550*/
.mainpage .wizardstep .wizardstepcontent .achievementsform .achievementformlevelhint .close {
    position: absolute;
    top: 45px;
    left: 50%;
    margin-left: -10px;
}

.mainpage .wizardstep .close:focus, .mainpage .wizardstep .close:hover {
    opacity: 1;
}
/*ST-550*/
.mainpage .wizardstep .wizardstepcontent .achievementsform select {
    height: 27px;
    width: 100%;
    display: block;
    color: #9b9b9b;
    font-size: 15px;
    text-align: left;
    border: 1px solid #979797;
    font-family: 'GillSans-Light';
}
    /*ST-550*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform select.institutionselect {
        padding-left: 25px;
        background-image: url(../images/columbia-logo-icon.png);
        background-position-x: left;
        background-position-y: center;
        background-repeat: no-repeat;
    }
    /*ST-550*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform select:focus {
        border: 1px solid #3770C6;
        outline: none;
    }
    /*ST-550*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform select.stateselect {
        max-width: 40%;
        display: inline-block;
    }

/*ST-550*/
.mainpage .wizardstep .wizardstepcontent .achievementsform a.notusa {
    font-family: Optima;
    color: #410260;
    text-transform: capitalize;
    font-size: 12px;
    text-decoration: underline;
}
/*ST-550*/
.mainpage .wizardstep .wizardstepcontent .achievementsform .textinput {
    padding: 0px 5px;
    height: 25px;
    line-height: 25px;
}

/*ST-550*/
.mainpage .wizardstep .wizardstepcontent .achievementsform .achievementformmonthhint {
    border: 5px solid #57307A;
    text-align: center;
    padding: 10px;
    font-family: GillSansCE-Roman;
    font-size: 14px;
    background-color: #F9F9F9;
    width: 125px;
    z-index: 1;
    position: absolute;
    left: 110%;
    top: 10px;
}
/*ST-714*/
.mainpage .wizardstep .wizardstepcontent .achievementsform.full .achievementformmonthhint {
    left: 60%;
    top: 145px;
}

/*ST-550*/
.mainpage .wizardstep .wizardstepcontent .achievementsform .achievementformmonthhint a.close {
    position: absolute;
    right: 5px;
    top: 5px;
    font-size: 20px;
    color: #9B9B9B;
    font-weight: 100;
}

/*ST-550*/
.mainpage .wizardstep .wizardstepcontent .achievementsform .achievementformyearshint {
    border: 3px solid #4E72B3;
    text-align: center;
    padding: 5px;
    padding-top: 20px;
    font-family: GillSansCE-Roman;
    font-size: 14px;
    background-color: #F9F9F9;
    width: 130px;
    height: 130px;
    border-radius: 50%;
    z-index: 1;
    position: absolute;
    left: 100%;
    top: 130px;
}
/*ST-714*/
.mainpage .wizardstep .wizardstepcontent .achievementsform.full .achievementformyearshint {
    left: 80%;
    top: 35px;
}

/*ST-550*/
.mainpage .wizardstep .wizardstepcontent .achievementsform .achievementformyearshint a.close {
    position: absolute;
    left: 50px;
    top: 0px;
    font-size: 20px;
    color: #9B9B9B;
    font-weight: 100;
}

/*ST-550*/
.mainpage .wizardstep .wizardstepcontent .achievementsform .achievementformlevelhint {
    position: absolute;
    border: 0px;
    padding: 0px;
    right: -220px;
    bottom: -120px;
    z-index: 1;
    background-color: transparent;
    background-image: url(../images/Level-Hint.png);
    background-position: center;
    background-repeat: no-repeat;
    width: 212px;
    height: 221px;
}

/*ST-714*/
.mainpage .wizardstep .wizardstepcontent .achievementsform.full .achievementformlevelhint {
    width: 190px;
    right: auto;
    left: 81%;
    top: 121px;
    bottom: auto;
    background-size: contain;
}

/*ST-714*/
.mainpage .wizardstep .wizardstepcontent .achievementsform .checkboxlist label {
    color: #9B9B9B;
    font-family: GillSans-Light;
    font-size: 15px;
    font-weight: normal;
}
/*ST-714*/
.mainpage .wizardstep .wizardstepcontent .achievementsform.full .extracurricularguidelineshint {
    border: 3px solid #4E72B3;
    text-align: center;
    padding: 30px;
    padding-top: 35px;
    font-family: GillSansCE-Roman;
    font-size: 14px;
    background-color: #F9F9F9;
    width: 250px;
    height: 250px;
    border-radius: 50%;
    z-index: 1;
    position: absolute;
    left: 62%;
    top: 320px;
    display: block;
}
/*ST-714*/
.mainpage .wizardstep .wizardstepcontent .achievementsform .extracurricularguidelineshint {
    display: none;
}
/*ST-714*/
.mainpage .wizardstep .wizardstepcontent .achievementsform .significanthllist > li > span.fixtoright {
    right: -25px;
    font-size: 17px;
    top: 0px;
}
/*ST-714*/
.mainpage .wizardstep .wizardstepcontent .achievementsform .significanthllist > li > input.textinput {
    margin-bottom: 5px;
}

/*ST-551*/
.mainpage .wizardstep .wizardstepcontent .achievementsform.full .resumesuccessplanhint {
    border: 3px solid #2A5BB4;
    text-align: center;
    padding: 10px;
    padding-top: 35px;
    font-family: GillSansCE-Roman;
    font-size: 13px;
    background-color: #F9F9F9;
    width: 240px;
    height: 240px;
    border-radius: 50%;
    z-index: 1;
    position: absolute;
    left: 62%;
    top: -200px;
    display: block;
}

    /*ST-551*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform.full .resumesuccessplanhint .close {
        position: absolute;
        left: 49%;
        top: 20px;
        display: block;
    }

.mainpage .wizardstep .wizardstepcontent .achievementsform.full .workexperiencetitlehint {
    border: 3px solid #979797;
    text-align: left;
    padding: 5px;
    font-family: GillSansCE-Roman;
    font-size: 13px;
    background-color: #F9F9F9;
    width: 200px;
    height: 180px;
    z-index: 1;
    position: absolute;
    left: 63%;
    top: 30px;
}
/*ST-551*/
.mainpage .wizardstep .wizardstepcontent .achievementsform.full .avgvsgreatresumehint {
    border: 3px solid #57307A;
    text-align: left;
    padding: 10px;
    font-family: GillSansCE-Roman;
    font-size: 13px;
    background-color: #F9F9F9;
    width: 215px;
    height: 200px;
    z-index: 1;
    position: absolute;
    left: 98%;
    top: -176px;
}

    /*ST-551*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform.full .avgvsgreatresumehint ul,
    .mainpage .wizardstep .wizardstepcontent .achievementsform.full .languagecodinghint ul,
    .mainpage .wizardstep .wizardstepcontent .achievementsform.full .parsmethodhint ul {
        margin: 0px;
        padding: 0px;
        margin-left: 20px;
    }

        /*ST-551*/
        .mainpage .wizardstep .wizardstepcontent .achievementsform.full .avgvsgreatresumehint ul > li,
        .mainpage .wizardstep .wizardstepcontent .achievementsform.full .languagecodinghint ul > li,
        .mainpage .wizardstep .wizardstepcontent .achievementsform.full .parsmethodhint ul > li {
            padding: 0px 10px 0px 0px;
        }
    /*ST-551*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform.full .avgvsgreatresumehint .close,
    .mainpage .wizardstep .wizardstepcontent .achievementsform.full .languagecodinghint .close,
    .mainpage .wizardstep .wizardstepcontent .achievementsform.full .parsmethodhint .close {
        position: absolute;
        right: 5px;
        top: 5px;
    }

/*ST-551*/
.mainpage .wizardstep .wizardstepcontent .achievementsform.full .languagecodinghint {
    border: 3px solid #69986C;
    text-align: left;
    padding: 5px;
    font-family: GillSansCE-Roman;
    font-size: 13px;
    background-color: #F9F9F9;
    width: 220px;
    height: 140px;
    z-index: 1;
    position: absolute;
    left: 65%;
    top: 220px;
}

/*ST-551*/
.mainpage .wizardstep .wizardstepcontent .achievementsform.full .parsmethodhint {
    border: 3px solid #57307A;
    text-align: left;
    padding: 5px;
    font-family: GillSansCE-Roman;
    font-size: 13px;
    background-color: #F9F9F9;
    width: 230px;
    z-index: 1;
    position: absolute;
    left: 30px;
    bottom: -90px;
}
/*ST-714*/
.mainpage .wizardstep .wizardstepcontent .achievementsform .extracurricularguidelineshint a.close {
    position: absolute;
    left: 50%;
    top: 15px;
    font-size: 20px;
    color: #9B9B9B;
    font-weight: lighter;
    margin-left: -10px;
}

/*ST-552*/
.mainpage .wizardstep .wizardstepcontent .skilltabs.nav > li > a {
    padding: 5px 20px;
    color: #410260;
    font-family: 'GillSans';
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 16px;
    letter-spacing: normal;
    text-align: left;
}

.mainpage .wizardstep .wizardstepcontent .skilltabs.nav > li > a {
    margin-left: -2px;
}

.mainpage .wizardstep .wizardstepcontent .skilltabs.nav > li:first-child > a {
    margin-left: 0px;
}

/*ST-552*/
.mainpage .wizardstep .wizardstepcontent .skilltabs.nav-tabs > li.active > a,
.mainpage .wizardstep .wizardstepcontent .skilltabs.nav-tabs > li.active > a:focus,
.mainpage .wizardstep .wizardstepcontent .skilltabs.nav-tabs > li.active > a:hover,
.mainpage .wizardstep .wizardstepcontent .skilltabs.nav-tabs > li > a,
.mainpage .wizardstep .wizardstepcontent .skilltabs.nav-tabs > li > a:focus,
.mainpage .wizardstep .wizardstepcontent .skilltabs.nav-tabs > li > a:hover {
    border-radius: 0px;
}

    /*ST-552*/
    .mainpage .wizardstep .wizardstepcontent .skilltabs.nav-tabs > li.active > a,
    .mainpage .wizardstep .wizardstepcontent .skilltabs.nav-tabs > li.active > a:focus,
    .mainpage .wizardstep .wizardstepcontent .skilltabs.nav-tabs > li.active > a:hover {
        background-color: #fff;
    }

    /*ST-552*/
    .mainpage .wizardstep .wizardstepcontent .skilltabs.nav-tabs > li > a,
    .mainpage .wizardstep .wizardstepcontent .skilltabs.nav-tabs > li > a:focus,
    .mainpage .wizardstep .wizardstepcontent .skilltabs.nav-tabs > li > a:hover {
        background-color: #D8D8D8;
    }

        .mainpage .wizardstep .wizardstepcontent .skilltabs.nav-tabs > li > a:focus,
        .mainpage .wizardstep .wizardstepcontent .skilltabs.nav-tabs > li > a:hover {
            background-color: #a7a7a7;
        }

/*ST-552*/
.mainpage .wizardstep .wizardstepcontent .achievementsform.tab-pane {
    background-color: #fff;
    padding: 10px 0px;
    margin: 0px;
}

    /*ST-552*/
    /*ST-568*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform.tab-pane .chooseskillscontainer, .mainpage .wizardstep .wizardstepcontent .achievementsform.tab-pane .chooselanguagecontainer {
        background-color: #f8fbfd;
        border: 1px solid #dfeaf7;
        padding: 15px;
        margin-bottom: 15px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
    }
    /*ST-568*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform.tab-pane .chooselanguagecontainer {
        margin-top: 20px;
        position: relative;
        display: block;
    }

    .mainpage .wizardstep .wizardstepcontent .achievementsform.tab-pane .chooselanguagecontainer {
        border: 1px solid #2a5bb4;
        margin-top: 10px;
    }
        /*1189*/
        /*ST-568*/
        .mainpage .wizardstep .wizardstepcontent .achievementsform.tab-pane .chooselanguagecontainer > .eraser {
            position: absolute;
            top: 5px;
            right: 5px;
        }

    /*ST-552*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform.tab-pane .chooseskillscontainer > .item {
        min-width: 150px;
        min-height: 30px;
        margin: 5px;
        position: relative;
    }

    /*ST-568*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform.tab-pane .chooselanguagecontainer > .item {
        width: 70%;
        /*margin: 5px;*/
        position: relative;
        background-color: #fff;
    }

    /*ST-552*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform.tab-pane .chooseskillscontainer > .item .checkboxlist > ul {
        width: 100%;
        max-height: 125px;
        min-height: 30px;
        overflow-y: auto;
    }

    /*ST-552*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform.tab-pane .chooseskillscontainer > .item a.close {
        position: absolute;
        right: 0px;
        top: 0px;
        font-size: 16px;
    }

    /*ST-552*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform.tab-pane .checkedinventorycontainer {
        background-color: #fff;
        border: 1px solid #979797;
        padding: 0px;
        margin-bottom: 15px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
    }

        /*ST-552*/
        .mainpage .wizardstep .wizardstepcontent .achievementsform.tab-pane .checkedinventorycontainer > .item {
            min-width: 48%;
            min-height: 25px;
            margin: 3px 5px;
            position: relative;
            color: #9B9B9B;
            font-size: 15px;
            text-transform: capitalize;
            background-color: #FAFAFA;
            border: 1px solid #D8D8D8;
            padding: 0px 9px;
            font-family: "GillSans-Light";
            font-style: normal;
            font-stretch: normal;
            letter-spacing: normal;
            line-height: 25px;
        }

            /*ST-552*/
            .mainpage .wizardstep .wizardstepcontent .achievementsform.tab-pane .checkedinventorycontainer > .item a.close {
                position: absolute;
                right: 0px;
                top: 5px;
                font-size: 14px;
            }

    /*ST-552*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform.tab-pane .resumedisplayscontainer {
        background-color: #D7DBEC;
        border: 1px solid #ADBAC9;
        padding: 0px;
        margin-bottom: 15px;
    }

    /*ST-552*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform.tab-pane .dropdown.specialselect button {
        background-color: #fff;
        border: 1px solid #979797;
        font-size: 13px;
        color: #9B9B9B;
        border-radius: 0px;
        min-width: 150px;
    }
    /*ST-552*/
    .mainpage .wizardstep .wizardstepcontent .achievementsform.tab-pane .dropdown.specialselect .dropdown-menu {
        border-radius: 0px;
    }

/*ST-565*/
.mainpage .wizardstep .rw-dialog {
    border: none;
    background-color: transparent;
    min-width: 77%;
    min-height: 60vh;
    position: fixed;
    top: 25%;
    left: 10%;
}
/*ST-565*/
.mainpage .wizardstep dialog.rw-dialog::backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
}

/*ST-565*/
.mainpage .wizardstep .langfluencycont {
    background-color: #fff;
    border: 3px solid #57307A;
    padding: 10px;
    position: relative;
}

/*ST-565*/
.mainpage .wizardstep .wizardstepcontent .langfluencylevellist {
    background-color: none;
    border: none;
    padding: 3px;
    margin: 5px;
    margin-top: 0px;
    margin-bottom: 15px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    max-height: 250px;
    overflow-y: auto;
    width: 640px;
}

    /*ST-565*/
    .mainpage .wizardstep .wizardstepcontent .langfluencylevellist > .item {
        width: 295px;
        height: 110px;
        margin: 5px;
        position: relative;
        background-color: #FAFAFA;
        border: 1px solid #E3E3E3;
        text-align: center;
    }

        /*ST-565*/
        .mainpage .wizardstep .wizardstepcontent .langfluencylevellist > .item .close {
            position: absolute;
            right: 5px;
            top: 5px;
        }

            /*ST-565*/
            .mainpage .wizardstep .wizardstepcontent .langfluencylevellist > .item .close:focus {
                outline: none;
            }

/*ST-565*/
.mainpage .wizardstep .wizardstepcontent .langfluencycont .forminput {
    margin-bottom: 0px;
}

/*ST-565*/
.mainpage .wizardstep .wizardstepcontent .langfluencycont .inputlabel {
    font-size: 14px;
}

    /*ST-565*/
    .mainpage .wizardstep .wizardstepcontent .langfluencycont .inputlabel .required {
        font-size: 27px;
        vertical-align: middle;
    }

/*ST-565*/
.mainpage .wizardstep .wizardstepcontent .langfluencylevellist .inputlabel {
    font-size: 15px;
    font-family: GillSans-Light;
    color: #000;
    padding: 5px 10px;
    display: block;
    border-bottom: 1px solid #E3E3E3;
    text-align: left;
}

/*ST-565*/
.mainpage .wizardstep .wizardstepcontent .languagelevelhint {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 3px solid #3C5896;
    padding: 14px;
    background-color: #fff;
    color: #6D6D6D;
    font-size: 13px;
    font-family: GillSans-Light;
    text-align: center;
    position: absolute;
    top: -60px;
    left: 20px;
}

/*ST-565*/
.mainpage .wizardstep .wizardstepcontent .languageratingshint {
    border: 3px solid #000;
    padding: 25px 15px 15px 15px;
    margin-top: 60px;
    margin-left: 30px;
    background-color: #FAFAFA;
}
    /*ST-565*/
    .mainpage .wizardstep .wizardstepcontent .languageratingshint table {
        width: 90%;
        font-family: GillSans-Light;
        font-size: 13px;
        color: #6D6D6D;
    }

        /*ST-565*/
        .mainpage .wizardstep .wizardstepcontent .languageratingshint table td {
            padding: 2px;
        }

        /*ST-565*/
        .mainpage .wizardstep .wizardstepcontent .languageratingshint table tr > td:first-child {
            padding-right: 20px;
        }
/*ST-565*/
.mainpage .radiolist .paddedradiocont {
    padding: 20px 0px;
    position: relative;
    text-align: center;
    width: 40px;
}

    /*ST-565*/
    .mainpage .radiolist .paddedradiocont .langlevel {
        font-family: GillSans-Light;
        font-size: 13px;
        color: #9B9B9B;
    }

/*ST-565*/
.mainpage .wizardstep .radiolist .paddedradiocont label.top {
    top: 0px;
    position: absolute;
}

/*ST-565*/
.mainpage .wizardstep .radiolist .paddedradiocont label.bottom {
    top: auto;
    bottom: 0px;
    left: 0px;
    position: absolute;
}

/*ST-566*/
.mainpage .wizardstep .wizardstepcontent .skillscontainer .achievementsform .languagelist {
    margin: 0px;
    padding: 0px;
    display: block;
}

    /*ST-566*/
    .mainpage .wizardstep .wizardstepcontent .skillscontainer .achievementsform .languagelist > li {
        list-style-type: none;
    }

        /*ST-566*/
        .mainpage .wizardstep .wizardstepcontent .skillscontainer .achievementsform .languagelist > li > .addedlanguage {
            padding: 0px 15px 0px;
            position: relative;
            background-color: #FAFAFA;
            border: 1px solid #979797;
            margin: 0px;
            margin-bottom: 5px;
            display: block; /*height: 70px;*/
            min-height: 48px;
        }

            /*ST-566*/
            .mainpage .wizardstep .wizardstepcontent .skillscontainer .achievementsform .languagelist > li > .addedlanguage > .lang-icon {
                width: 35px;
                height: 35px;
                padding: 6px;
                border-radius: 50%;
                border: 2px solid #979797;
                font-family: GillSans-SemiBold;
                font-size: 15px;
                text-transform: uppercase;
                color: #9B9B9B;
                display: inline-block;
                position: relative;
                top: -25px;
            }

            /*ST-566*/
            .mainpage .wizardstep .wizardstepcontent .skillscontainer .achievementsform .languagelist > li > .addedlanguage > label.inputlabel {
                text-transform: capitalize;
                font-family: GillSans-Light;
                font-size: 17px;
                color: #9B9B9B;
                position: relative;
                top: -25px;
            }

            /*ST-566*/
            .mainpage .wizardstep .wizardstepcontent .skillscontainer .achievementsform .languagelist > li > .addedlanguage > .close {
                position: absolute;
                top: 30%;
                right: 10px;
                left: auto;
                font-size: 18px;
            }

            /*ST-566 , ST-1163*/
            .mainpage .wizardstep .wizardstepcontent .skillscontainer .achievementsform .languagelist > li > .addedlanguage > .radiolist {
                margin-left: 27%;
            }
/*ST-566*/
.mainpage .wizardstep .wizardstepcontent .skillscontainer .achievementsform .overridewordshint {
    background-color: #FAFAFA;
    padding: 10px;
    width: 150px;
    border: 3px solid #69986C;
    font-family: GillSans-Light;
    font-size: 13px;
    color: #6D6D6D;
    position: relative;
    top: -72px;
    left: 75%;
}

    /*ST-566*/
    .mainpage .wizardstep .wizardstepcontent .skillscontainer .achievementsform .overridewordshint .close {
        position: absolute;
        top: 5px;
        right: 5px;
    }
/*ST-566*/
.mainpage .wizardstep .wizardstepcontent .skillscontainer .achievementsform .langoverridewords {
    width: 90%;
}

    /*ST-566*/
    .mainpage .wizardstep .wizardstepcontent .skillscontainer .achievementsform .langoverridewords td,
    .mainpage .wizardstep .wizardstepcontent .skillscontainer .achievementsform .langoverridewords th {
        padding: 3px;
        border: 1px solid #979797;
    }

    /*ST-566*/
    .mainpage .wizardstep .wizardstepcontent .skillscontainer .achievementsform .langoverridewords tr > td:first-child {
        color: #000;
        font-family: GillSans-Light;
        font-size: 15px;
    }

    /*ST-566*/
    .mainpage .wizardstep .wizardstepcontent .skillscontainer .achievementsform .langoverridewords tr > td:last-child {
        color: #9B9B9B;
        font-family: GillSans-Light;
        font-size: 15px;
    }

/*ST-566*/
.mainpage .wizardstep .wizardstepcontent .skillscontainer .achievementsform .languagedisplay {
    background-color: #D7DBEC;
    border: 1px solid #ADBAC9;
    padding: 5px;
}

/*ST-566*/
.mainpage .wizardstep .wizardstepcontent .skillscontainer .achievementsform .langoverridewords th {
    color: #410260;
    font-family: GillSansCE-Roman;
    font-size: 14px;
}

/*ST-552*/
.columns-icon {
    background-image: url(../images/Columns-icon.png);
    background-repeat: no-repeat;
    background-position-x: 5px;
    background-position-y: center;
    padding-left: 30px;
}

.plane-icon {
    background-image: url(../images/plane_icon.png);
    background-repeat: no-repeat;
    background-position-x: 5px;
    background-position-y: center;
    padding-left: 30px;
}

/*ST-552*/
.architrave-icon {
    background-image: url(../images/Architrave-icon.png);
    background-repeat: no-repeat;
    background-position-x: 5px;
    background-position-y: center;
    padding-left: 30px;
}

/*Everywhere*/
/*ST-714*/
.checkboxlist {
    border: 1px solid #979797;
}

    .checkboxlist:focus {
        border: 1px solid #2A5BB4;
    }

    .checkboxlist a {
        color: #57307A;
        font-family: GillSans-Light;
        font-size: 15px;
        text-decoration: none;
        padding-left: 5px;
    }

    .checkboxlist .selectedchecks {
        padding: 5px;
        border-bottom: 1px solid #979797;
        color: #9B9B9B;
        font-size: 15px;
        font-family: GillSans-Light;
        margin: 0px;
        display: block;
        min-height: 32px;
    }

    .checkboxlist .searchinput {
        padding: 5px;
        border: 0px;
        border-bottom: 1px solid #979797;
        color: #9B9B9B;
        font-size: 15px;
        font-family: GillSans-Light;
        margin: 0px;
        display: block;
        min-height: 32px;
    }

        .checkboxlist .searchinput:focus {
            outline: none;
        }

    .checkboxlist ul {
        display: block;
    }

        .checkboxlist ul > li {
            padding: 5px 5px 0px 5px;
        }

/*ST-537*/
.mainpage .wizardstep .achievementscontainer .currentinterestlist {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
}

    /*ST-537*/
    .mainpage .wizardstep .achievementscontainer .currentinterestlist > .currentinterest {
        border: 1px solid #ACACAC;
        flex-basis: calc( 50% - 10px);
        position: relative;
        margin: 5px 5px 0px 0px;
        background: #fff;
        color: #ACABAC;
        min-height: 25px;
    }

        /*ST-537*/
        .mainpage .wizardstep .achievementscontainer .currentinterestlist > .currentinterest.grey {
            background: #F5F5F5;
        }

        /*ST-537*/
        .mainpage .wizardstep .achievementscontainer .currentinterestlist > .currentinterest .lefticon {
            position: absolute;
            top: 2px;
            left: 5px;
        }

        /*ST-537*/
        .mainpage .wizardstep .achievementscontainer .currentinterestlist > .currentinterest.grey .text {
            display: block;
            padding-top: 3px;
            padding-left: 30px;
        }


        /*ST-537*/
        .mainpage .wizardstep .achievementscontainer .currentinterestlist > .currentinterest .text {
            display: block;
            padding-top: 3px;
            padding-left: 5px;
        }
/*ST-537*/
.blackspan {
    color: #000;
    color: #000;
    font-family: gillsans-regular;
    font-size: 12px;
}

.padding5 {
    padding: 5px;
}
/*Everywhere*/
.squashicon {
    height: 20px;
    width: 20px;
    background-image: url(../images/Squash-Icon.png);
    background-repeat: no-repeat;
    background-position: left center;
    background-size: contain;
    display: inline-block;
}

/*Everywhere*/
.photographyicon {
    height: 20px;
    width: 20px;
    background-image: url(../images/photography-icon.png);
    background-repeat: no-repeat;
    background-position: left center;
    background-size: contain;
    display: inline-block;
}

/*Everywhere*/
.pictureicon {
    height: 20px;
    width: 20px;
    background-image: url(../images/picture-icon.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    display: inline-block;
}
/*Everywhere*/
.snowbardingicon {
    height: 20px;
    width: 20px;
    background-image: url(../images/Snowbarding-icon.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    display: inline-block;
}

/*Everywhere*/
.typographyicon {
    height: 20px;
    width: 20px;
    background-image: url(../images/Typography-icon.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    display: inline-block;
}

/*Everywhere*/
.landscapeicon {
    height: 20px;
    width: 20px;
    background-image: url(../images/tree-icon.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    display: inline-block;
}

/*Everywhere*/
.bullets-icon {
    height: 20px;
    width: 20px;
    background-image: url(../images/bullets-icon.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    display: inline-block;
    vertical-align: bottom;
}
/*ST-549, Everywhere*/
.right {
    text-align: right;
}

.left {
    text-align: left;
}
/*Everywhere*/
.close {
    font-size: 20px;
    font-family: GillSans-Light;
    font-weight: 100;
    color: #000;
    opacity: 1;
}
/*Everywhere*/
.ellipse-icon {
    background-image: url(../images/ellipse-icon.png);
    height: 20px;
    width: 20px;
    display: inline-block;
    background-repeat: no-repeat;
    background-position: center;
}

.affiliationdetailitem .ellipse-icon {
    height: 25px;
}

.affiliationdetailitem .close {
    padding-top: 6px !important;
}
/*Everywhere*/
.up-arrow-icon {
    background-image: url(../images/Up-Arrow-Icon.png);
    height: 20px;
    width: 20px;
    display: inline-block;
    background-repeat: no-repeat;
    background-position: center;
}

/*Everywhere*/
.down-arrow-icon {
    background-image: url(../images/Down-Arrow-Icon.png);
    height: 20px;
    width: 20px;
    display: inline-block;
    background-repeat: no-repeat;
    background-position: center;
}
/*Everywhere*/
.pencil-icon {
    background-image: url(../images/Pencil-icon.png);
    height: 20px;
    width: 20px;
    display: inline-block;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 13px;
}

/*Everywhere*/
.line-item-icon {
    background-image: url(../images/Line-Items-Icon.png);
    height: 9px;
    width: 18px;
    display: inline-block;
    background-repeat: no-repeat;
    background-position: center;
}
/*Everywhere*/
.mainpage .wizardstep .btn.dropdown-toggle {
    border-radius: 0px;
    border: 1px solid #979797;
    padding: 3px 6px;
}

.mainpage .wizardstep ul.dropdown-menu {
    border-radius: 0px;
    border: 1px solid #979797;
    margin: 0px;
    padding: 0px;
}

.down-triangle {
    background-image: url(../images/Down-Open-Triangle-Icon.png);
    height: 20px;
    width: 20px;
    display: inline-block;
    background-repeat: no-repeat;
    background-position: center;
}
/*Everywhere*/
.capitalize {
    text-transform: uppercase !important;
}

/*Everywhere*/
.padding10 {
    padding: 10px !important;
}

/*Everywhere*/
.uppercase {
    text-transform: uppercase !important;
}

/*Everywhere*/
.textleft {
    text-align: left !important;
}
/*Everywhere*/
.gillsanslight {
    font-family: GillSans-Light !important;
}
/*Everywhere*/
.gillsansregular {
    font-family: GillSansCE-Roman !important;
}
/*Everywhere*/
.bold {
    font-weight: bold !important;
}

/*Everywhere*/
.font15 {
    font-size: 15px !important;
}

/*Everywhere*/
.mainpage .color1 {
    color: #9B9B9B !important;
}
/*Everyhere*/
.underline {
    text-decoration: underline;
}

/*Everyhere*/
.relative {
    position: relative !important;
}
/*Everywhere*/
.help {
    color: #9B9B9B;
    font-size: 14px;
    font-weight: 100;
}

.paragraph-icon {
    color: #412062;
    text-transform: capitalize;
    font-weight: bold;
    font-family: GillSansCE-Roman;
}

.resumewizardsticker {
    height: 82px;
    width: 94px;
    background-image: url('../images/Resume Wizard.png');
    background-repeat: no-repeat;
    display: inline-block;
}

.input-group-addon {
    padding: 0px 12px;
    font-size: 20px;
    font-weight: 900;
    line-height: unset;
    color: #422063;
    text-align: center;
    background-color: #fff;
    border: 1px solid #979797;
    border-radius: 0px;
}

    .input-group-addon a {
        color: #422063;
    }

        .input-group-addon a:hover {
            text-decoration: none;
        }
/* R23 - Bhaumik */
.clearfix {
    clear: both;
}

.wizardstep {
    padding: 20px;
    color: #410266;
}

    .wizardstep > h1 {
        text-transform: uppercase;
        font-size: 26px;
        margin-bottom: 30px;
        font-family: 'AvenirNextLTPro-Regular';
    }

        .wizardstep > h1 > span {
            font-size: 12px;
            display: block;
            margin-bottom: 3px;
            font-family: GillSans-SemiBold;
        }

.form-title {
    text-transform: uppercase;
    font-size: 18px;
    border-bottom: 1px solid #410266;
    line-height: 18px;
    font-family: 'AvenirNextLTPro-Regular';
}

    .form-title > em {
        font-family: sans-serif;
        font-style: normal;
        font-weight: 300;
    }

.form-content {
    padding: 15px;
}

p {
    font-weight: 500;
    font-size: 14px;
    letter-spacing: 0;
    text-transform: capitalize;
    font-family: GillSansCE-Roman;
    color: #410260;
}

.form-content > label {
    font-weight: 500;
    font-size: 14px;
    letter-spacing: 0;
    text-transform: capitalize;
    margin: 0;
    font-family: GillSansCE-Roman;
    color: #410260;
}

.fiels-wrap {
    position: relative;
    width: 410px;
    margin-bottom: 25px;
}

.field-input {
    width: 70%;
    display: inline-block;
    float: left;
}

    .field-input > input, .fiels-wrap .field-input > select {
        width: 100%;
        padding: 3px 7px;
        color: #5d5d5d;
        font-size: 16px;
        border: #e0e0e0 1px solid;
    }

.field-btn {
    display: inline-block;
    width: 27%;
    float: left;
    margin-left: 10px;
}

.btn-custom {
    padding: 7px 10px;
    text-align: center;
    border: 1px solid #410266;
    border-radius: 25px;
    display: inline-block;
    text-transform: uppercase;
    color: #410266;
    font-weight: 600;
    font-size: 15px;
    line-height: 13px;
    min-width: 90px;
}

    .btn-custom:hover, .btn-custom:focus {
        background: #410266;
        color: #fff;
        text-decoration: none;
    }

.success-block {
    color: #426618;
    font-weight: 500;
    font-size: 18px;
    text-transform: uppercase;
    font-family: GillSans-SemiBold;
    letter-spacing: 0;
}

    .success-block.position {
        position: absolute;
        right: -130px;
        top: 5px;
    }

    .success-block .check {
        font-family: GillSans-SemiBold;
        line-height: 10px;
        font-weight: 600;
        font-size: 40px;
    }
/*.social-check{width:50%}*/
.social-check .field-btn {
    width: 100%;
    text-align: center;
    float: none;
    margin: 15px 0;
}

.social-check ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

    .social-check ul li {
        width: 1%;
        display: table-cell;
        text-align: center;
    }

.social-check .success-block {
    text-align: center;
    margin-top: 15px;
}

.left-block {
    float: left;
    color: #f8b42f;
    font-family: AvenirNextLTPro-Regular;
    font-size: 23px;
    width: 284px;
    text-align: center;
    line-height: 22px;
    font-weight: bold;
}

.right-block {
    float: right;
    text-align: center;
    width: 300px;
}

.social-check input[type=checkbox] {
    display: block;
    margin: 15px auto 0 auto;
}
/*ST-890*/

.centerRWTemplate .slick-prev {
    display: inline-block;
    background: url(../images/left-angel.png) top center no-repeat;
    left: -34px;
    height: 85px;
    width: 30px;
    background-size: 60% 80% !important;
    background-color: transparent !important;
    border: 0;
    position: absolute;
    top: 120px;
    outline: 0;
}

.centerRWTemplate .slick-next {
    display: inline-block;
    height: 85px;
    width: 30px;
    background-size: 60% 80% !important;
    background-color: transparent !important;
    border: 0;
    position: absolute;
    top: 120px;
    outline: 0;
    background: url(../images/right-angel.png) top center no-repeat;
    right: -34px;
}

.ctTemplateLstImg {
    height: 220px;
    display: inline-block !important;
    margin-bottom: 17px;
    border: 2px solid #EEEEEE;
}

@media (min-width: 768px) {
    .navbar-right {
        margin-right: 0px;
    }

    .navbar-nav > li > a {
        /* (68px - line-height of 27px) / 2 = 26.5px */
        padding-top: 30.5px;
        padding-bottom: 10.5px;
        line-height: 27px;
    }
}

@media (max-width: 767px) {
    .mainpage .sidebar {
        width: 20%;
    }

    .mainpage .wizardstep {
        width: 80%;
    }

    .navbar-default .navbar-nav .open .dropdown-menu > li > a {
        color: #ffffff;
    }

        .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover,
        .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
            color: #ffffff;
        }

    .navbar-default .navbar-nav .open .dropdown-menu > .active > a,
    .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover,
    .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
        color: #ffffff;
        background-color: #000000;
    }

    .navbar-default .navbar-nav > li > a.membername {
        font-weight: 500;
        font-size: 24px;
        text-transform: capitalize;
        text-align: right;
        padding-bottom: 0px;
    }

    .navbar-default .navbar-nav.vcard > li:first-child {
        height: auto;
    }

    .navbar-default .navbar-nav > li > a.notyou {
        text-decoration: underline;
        font-size: 12px;
        text-transform: capitalize;
        padding-top: 0px;
        line-height: 0px;
        text-align: right;
        position: static;
        right: auto;
        bottom: auto;
    }

    .navbar-default .navbar-nav > li > a.memberpic {
        display: none;
    }

    .mainpage .wizardstep .inputlabel.autofilllabel {
        float: none;
    }

    .mainpage .wizardstep ul.autofillchks li {
        list-style-type: none;
        float: none;
        padding-left: 10px;
    }
}

/*ST-809*/
.CtTemplate-Item {
    float: left;
    margin-left: 13px;
    margin-top: 18px;
    margin-right: 9px;
    text-align:center;
}
/*.choosTmUL li {float: left;list-style-type: none;margin: 5px 10px 5px 10px;}*/
.choosTeChk {
}

#overlay {
    width: 100% !important;
    height: 100% !important;
    background-color: #fff;
    z-index: 100;
    color: #000;
    text-align: center;
    font-size: 24px;
    filter: alpha(opacity=80);
    -moz-opacity: .8;
    opacity: .8;
    position: fixed;
}

.CtRedioTemplateNameDiv {
    text-align: center;
    margin-top: 10px;
}

.slick-slide {
    /*width: auto !important;*/
}

.RwTempSelect {
    border: 2px solid #57307a;
    opacity: 0.60;
}

.mb50 {
    margin-bottom: 50px !important;
}

.CtTemplate-Item:focus {
    outline: none;
}
/*ST-1135, BV START */
.collapse .navbar-collapse {
    background-color: #000 !important;
}

.navbar-default .navbar-collapse, .navbar-default .navbar-form {
    border-color: #e7e7e7;
    background-color: #000 !important;
    color: #000 !important;
}

.navbar-default .navbar-nav > li > a {
    color: #fff !important;
}

.navbar-brand .trade-logo {
    width: 125px;
    height: 1006px;
}

p {
    font-weight: 500;
    font-size: 10px;
    letter-spacing: 0;
    text-transform: capitalize;
    font-family: GillSansCE-Roman;
    color: #410260;
}

.navbar-nav > li {
    float: left !important;
    background-color: #000 !important;
}

/*ST-1135, BV END */

/*ST-628*/
.Resume_main_wrapper {
    padding-left: 2%;
    float: left;
    width: 100%;
}

#dvDBPublishShare {
    width: 94%;
    padding-left: 5%;
    float: left;
}

.container-fluid {
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

.Resume_Add_wrapper ul {
    margin: 0;
    padding: 0;
}

    .Resume_Add_wrapper ul li {
        margin: 0 38px 5px 0;
    }

    .Resume_Add_wrapper ul li {
        margin: 0 38px 0 0;
        list-style-type: none;
        display: inline-block;
        vertical-align: top;
        text-align: center;
        position: relative;
        width: 161.3px;
    }

        .Resume_Add_wrapper ul li .Resume-Thumb {
            max-width: 100%;
            height: 100%;
        }

        .Resume_Add_wrapper ul li .Resume_Add {
            width: 161.3px;
            height: 203.8px;
            position: relative;
            border: 1px solid #969696;
            transition: all ease-out 0.3s;
            -webkit-transition: all ease-out 0.3s;
            -moz-transition: all ease-out 0.3s;
        }

            .Resume_Add_wrapper ul li .Resume_Add .hover_effect {
                background-color: rgba(255, 255, 255, 0.4);
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                opacity: 0;
                transition: all ease-out 0.3s;
                -webkit-transition: all ease-out 0.3s;
                -moz-transition: all ease-out 0.3s;
            }

        .Resume_Add_wrapper ul li p.Resume_subname {
            color: #410260;
            display: block;
            font-size: 14px;
            margin: 10px 0 0;
            font-family: 'GillSans';
            font-weight: normal;
        }

        .Resume_Add_wrapper ul li .Resume_Add:hover .close_icon img, .Resume_Add_wrapper ul li .Resume_Add .close_icon img {
            border: 0;
        }
        /*.Resume_Add_wrapper ul li .Resume_Add img { width: 70%; }*/
        .Resume_Add_wrapper ul li .Resume_Add .close_icon {
            position: absolute;
            right: 0;
            top: -1px;
        }

        .Resume_Add_wrapper ul li p.Resume_subname {
            color: #410260;
            display: block;
            font-size: 14px;
            margin: 10px 0 0;
            font-family: 'GillSans';
            font-weight: normal;
        }

        .Resume_Add_wrapper ul li .Resume_time {
            font-family: 'GillSans-SemiBold';
            font-size: 12px;
            font-weight: 600;
            line-height: 2.08;
            color: #9b9b9b;
        }

        .Resume_Add_wrapper ul li p.Resume_subname a img {
            width: 13px;
            margin-left: 6px;
            margin-top: -3px;
        }

.Resume_heading h2 {
    font-family: 'GillSans-Light';
    font-size: 28px;
    font-weight: 300;
    color: #410260;
}

/*POP UP*/
.YResume_Modal .modal-content {
    border: 0;
    border-radius: 0;
    text-align: center;
    box-shadow: none;
    margin-top: 200px;
}

.YResume_Modal .modal-body {
    border: 2px solid #410166;
}

.YResume_Modal .modal-content h1 {
    margin: 15px 0 20px;
    font-family: 'GillSans';
    color: #4a4a4a;
    font-size: 25px;
    position: relative;
    padding-left: 35px;
    display: inline-block;
    text-transform: uppercase;
}

    .YResume_Modal .modal-content h1 img {
        width: 47px;
        top: -8px;
        left: -25px;
        position: absolute;
    }

.YResume_Modal .YResume_Modal_name {
    margin-bottom: 5px;
}

.YResume_Modal .YResume_Modal_name {
    position: relative;
    width: 320px;
    margin: 20px auto 35px;
    border-bottom: 2px solid #ccc;
}

    .YResume_Modal .YResume_Modal_name .form-control {
        text-transform: none;
    }

    .YResume_Modal .YResume_Modal_name .form-control {
        width: 320px;
        margin: 0 auto;
        border: 0;
        box-shadow: none;
        font-size: 23px;
        font-family: 'GillSans';
        color: #000;
        text-transform: uppercase;
        text-align: center;
    }

.YResume_Modal .modal-body .YResume_Modal_name_span {
    margin-bottom: 25px;
}

.AlreadyExistsText {
    font-family: GillSans;
    font-size: 13px;
    font-weight: 600;
    text-align: center;
    color: #d0011b;
}

.YResume_Modal .submit_Resume {
    border: 1px solid #57307a;
    color: #6b448d;
    border-radius: 20px;
    text-transform: uppercase;
    padding: 5px 40px;
    text-decoration: none;
    display: inline-block;
    font-size: 15px;
    margin: 0 7px 5px;
    transition: all ease-out 0.5s;
    -webkit-transition: all ease-out 0.5s;
    -moz-transition: all ease-out 0.5s;
    outline: none; /*BV,Added outline 03/28/2018*/
}

.flashdarkbutton {
    color: #FFF !important;
    background-color: #6B448D;
    transition: all ease-in 0.5s;
    -webkit-transition: all ease-in 0.5s;
    -moz-transition: all ease-in 0.5s;
    text-decoration: none;
}

/*List / Grid Views ST-628 */
.binding_views {
    margin: 0;
    position: absolute;
    bottom: 9%;
    left: 24px;
    width: 175px;
    z-index: 11;
}

    .binding_views ul {
        padding: 0;
        margin: 0;
        line-height: 12px;
    }

        .binding_views ul li {
            margin: 0 0 0 6%;
            position: relative;
            vertical-align: top;
            width: 54.4px;
            display: inline-block;
        }

            .binding_views ul li .single_view_wrapper .single_view_right {
                background-color: #FFF;
                padding: 2px;
            }

.pick_your_binding ul {
    padding: 0;
    margin: 0 0 -20px 0;
    line-height: 12px;
}

.binding_views ul li .single_view_wrapper .single_view_right ul li {
    width: 41px;
    border: solid 1px #979797;
    margin: 5px 3.2px;
    display: inherit;
}

.binding_views ul li p {
    font-size: 12px;
    color: #000;
    margin: 5px 0 0;
    font-family: 'GillSans-SemiBold';
    text-transform: uppercase;
    text-align: center;
    font-weight: 600;
}

.binding_views ul li a {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.binding_views ul li .grid_view_wrapper, .binding_views ul li .single_view_wrapper {
    border: 1px solid #000;
    width: 54.4px;
    height: 39.4px;
}

    .binding_views ul li .grid_view_wrapper ul li {
        display: inline-block;
        margin: 2.2px 0 0;
    }

        .binding_views ul li .grid_view_wrapper ul li span {
            float: left;
            width: 13px;
            height: 10.7px;
            background-color: #FFF;
            border: solid 1px #979797;
            margin: 1.5px;
        }

.binding_views ul li .active {
    border: 3px solid #000;
}

    .binding_views ul li .active.single_view_wrapper .single_view_right ul li {
        margin: 4.7px 1px;
    }

        .binding_views ul li .active.single_view_wrapper .single_view_right ul li:last-child {
            margin-bottom: 0;
        }

.flashdarkbutton {
    color: #FFF !important;
    background-color: #6B448D;
    transition: all ease-in 0.5s;
    -webkit-transition: all ease-in 0.5s;
    -moz-transition: all ease-in 0.5s;
    text-decoration: none;
}

.Wizard-Advice-Always {
    width: 176.4px;
    height: 88px;
    font-family: GillSans;
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.57;
    letter-spacing: normal;
    text-align: left;
    color: #000000;
}

    .Wizard-Advice-Always .text-style-1 {
        color: #6e6e6e;
    }
/*ST-1137*/
.sideBar-Itme-done {
    background-image: url(/images/Check-mark.png);
    background-position-x: 18px;
    background-position-y: top;
    background-size: 20px;
    background-repeat: no-repeat;
}

select:focus, input:focus {
    border: solid 2px #215fcd !important;
}

.IsRequiredfieldSkip {
    border: solid 2px #c01f2a !important;
}

/*Work From Venkat Start*/
.mainpage .wizardstep .achievements-form .steplabel {
    color: #410260;
    font-family: "GillSans";
    font-size: 14px;
    font-stretch: normal;
    font-style: normal;
    font-weight: normal;
    letter-spacing: normal;
    line-height: normal;
    margin-bottom: 0;
    text-align: left;
    text-transform: uppercase;
}

.mainpage .wizardstep .achievements-form .stepname {
    color: #410260;
    font-family: "GillSans-Light";
    font-size: 28px;
    font-stretch: normal;
    font-style: normal;
    font-weight: 300;
    letter-spacing: normal;
    line-height: 32px;
    margin: 0;
    text-align: left;
    text-transform: uppercase;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .underlinedheading.skillset {
    max-width: 100%;
    margin: 0px;
    margin-bottom: 15px;
    line-height: 28px;
    font-family: 'Optima-Regular';
    font-size: 24px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    letter-spacing: normal;
    text-align: left;
    color: #410260;
    text-transform: none;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .categoryorder > li > div.categoryitem,
.mainpage .wizardstep .wizardstepcontent.achievements-form .categoryorder > li .subcategoryorder > li > div.categoryitem {
    border: 1px solid #adadad;
    font-family: 'GillSans-Light';
    color: #9b9b9b !important;
    font-size: 15px;
    text-transform: inherit !important;
    padding: 0px 3px;
    padding-left: 10px;
    width: calc(100% - 35px);
    display: inline-block;
    margin-right: 5px;
    background: #f6f6f6;
    font-weight: 300 !important;
    line-height: 25px;
    height: 25px;
}
.mainpage .wizardstep .wizardstepcontent.achievements-form .forminputb .categoryorder > li > div.categoryitem{ margin:0 0 -1px 0; background:#FFF;}

.mainpage .wizardstep .wizardstepcontent.achievements-form .categoryorder > li > div.categoryitem .ellipse-icon {
    height: 25px;
}

    .mainpage .wizardstep .wizardstepcontent.achievements-form .categoryorder > li > div.categoryitem .close {
        padding-top: 5px !important;
    }

    .mainpage .wizardstep .wizardstepcontent.achievements-form .categoryorder > li > div.categoryitem .close {
        font-size: 18px;
        padding: 0px 5px;
    }

.mainpage .wizardstep .wizardstepcontent.achievements-form .button.addcommunityservice, .mainpage .wizardstep .wizardstepcontent.achievements-form .button.addaccreditation, .mainpage .wizardstep .wizardstepcontent.achievements-form .button.addaffiliation {
    background-color: #ffffff;
    border: solid 1px #979797;
    height: 23px;
    border-radius: 11.5px;
    color: #9b9b9b;
    line-height: 23px;
    font-family: 'GillSans';
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    padding: 0px 9px 0px 8px;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .button.addcommunityservice, .mainpage .wizardstep .wizardstepcontent.achievements-form .button.addaccreditation {
}

    .mainpage .wizardstep .wizardstepcontent.achievements-form .button.addcommunityservice .glyphicon, .mainpage .wizardstep .wizardstepcontent.achievements-form .button.addaccreditation .glyphicon, .mainpage .wizardstep .wizardstepcontent.achievements-form .button.addaffiliation .glyphicon {
        color: #d8d8d8;
    }

.mainpage .wizardstep .wizardstepcontent.achievements-form .achievementsform .inputlabel {
    font-family: "GillSans";
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 16px;
    letter-spacing: normal;
    text-align: left;
    color: #410260;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .achievementsform input, .mainpage .wizardstep .wizardstepcontent.achievements-form .achievementsform select {
    margin-bottom: 5px;
}

.bluetextinput {
    border: 1px solid #2a5bb4 !important;
    color: #9b9b9b;
    font-family: 'GillSans-Light';
}

.bluetextinput2 {
    border: 1px solid #2a5bb4 !important;
    color: #000000 !important;
    font-family: 'GillSans-Light';
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .affiliationdetailslist > li > div.affiliationdetailitem {
    border: 1px solid #adadad;
    font-family: 'GillSans-Light';
    color: #9b9b9b;
    font-size: 15px;
    text-transform: none;
    padding: 0px 5px;
    padding-left: 10px;
    width: calc(100% - 35px);
    display: inline-block;
    margin-right: 5px;
    background: #fff;
    font-weight: 300;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    letter-spacing: normal;
    line-height: 25px;
    height: 25px;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .categoryorder > li > div.categoryitem .close {
    font-size: 14px;
    padding: 0px 5px;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .affiliationdetailslist {
    margin-bottom: 17px;
}

.achievements-form select:focus, .achievements-form input:focus {
    border: solid 1px #215fcd !important;
}

.mainpage .wizardstep .achievements-form .button.savebtn {
    background-color: #ffffff;
    border: solid 1px #57307a;
    border-radius: 12.5px;
    height: 25px;
    line-height: 25px;
    padding: 0px 20px;
    margin-top: 44px;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .dropdown.displayformat .btn.dropdown-toggle {
    padding: 1px 15px 2px;
    height: 25px;
}

.mainpage .wizardstep .achievements-form .forminput {
    margin-bottom: 0px;
}
/*.mainpage .wizardstep .achievements-form hr{ margin-top:20px; margin-bottom:12px}*/
.mainpage .wizardstep .wizardstepcontent.achievements-form .affiliationdetailslist > li > span.fixtoright {
    color: #9b9b9b;
    font-family: "GillSans-SemiBold";
    font-size: 15px;
    line-height: 25px;
    top: 0px;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .rightpadd {
    padding-right: 48px;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .rightpadd1 {
    padding-right: 40px;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .rightpadd2 {
    padding-right: 52px;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .achievementsform .close {
    font-size: 14px;
    padding: 0px 5px;
    height: auto;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .achievementsform .currentinterest .close {
    font-size: 14px;
    padding: 5px 5px;
    height: auto;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .achievementsform .close.big18 {
    font-size: 18px;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .achievementsform .close.big20 {
    font-size: 20px;
    vertical-align: top;
    margin-top: -1px;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .close.big181 {
    font-size: 18px;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .close.big182 {
    font-size: 22px;
    margin-top: -2px;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .achievementsform .affiliationheading {
    font-family: 'Optima-Regular';
    font-size: 24px;
    text-transform: inherit;
    color: #410260;
    margin: 0px;
    margin-bottom: 15px;
    font-stretch: normal;
    font-style: normal;
    font-weight: normal;
    letter-spacing: normal;
    line-height: 28px;
    padding-left: 0px;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .affiliationform {
    padding: 10px 15px 10px 10px;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .togglebox .dropdown.displayformat .btn.dropdown-toggle {
    padding: 0px 13px 0px;
    height: 25px;
    line-height: 20px;
}

.mainpage .wizardstep .choosetemplate .wizardheading {
    text-align: center;
    font-size: 55px;
    color: #410266;
    font-family: 'AvenirNext-UltraLight';
    font-weight: 200;
    text-transform: uppercase;
    margin-top: 30px;
    font-style: normal;
    font-stretch: normal;
    line-height: 70px;
    letter-spacing: normal;
}

.mainpage .wizardstep .choosetemplate .subheading {
    font-family: "GillSans-Light";
    font-size: 15px;
    color: #000;
    text-align: center;
    font-style: normal;
    font-stretch: normal;
    letter-spacing: normal;
    line-height: 18px;
}

.mainpage .wizardstep .choosetemplate .steplabel {
    font-family: "GillSans";
    font-size: 14px;
    margin-bottom: 0px;
    text-transform: uppercase;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 16px;
    letter-spacing: normal;
    text-align: left;
    color: #410260;
}

.mainpage .wizardstep .choosetemplate .stepname {
    margin: 0px;
    margin-bottom: 15px;
    color: #410260;
    text-transform: uppercase;
    font-family: 'GillSans-Light';
    font-size: 28px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 32px;
    letter-spacing: normal;
    text-align: left;
    color: #410260;
}

.mainpage .wizardstep .choosetemplate .inputlabel {
    font-size: 22px;
    color: #410260;
    font-family: 'Optima-Regular';
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 26px;
    letter-spacing: normal;
    text-align: center;
    color: #410260;
}

    .mainpage .wizardstep .choosetemplate .inputlabel .help {
        font-family: 'GillSans';
        font-size: 14px;
        font-weight: 300;
        font-style: normal;
        font-stretch: normal;
        line-height: 17px;
        letter-spacing: normal;
        text-align: left;
        color: #9b9b9b;
    }

.choosetemplate select:focus, .choosetemplate input:focus {
    border: solid 1px #215fcd !important;
}

.mainpage .wizardstep .choosetemplate .template-carousel {
    min-height: 250px;
    background-color: #eeeeee;
    width: 100%;
    border: 1px solid #979797;
    margin-bottom: 10px;
}

    .mainpage .wizardstep .choosetemplate .template-carousel .CtRedioTemplateNameDiv label {
        font-family: 'GillSans-Light';
        font-size: 12px;
        font-style: normal;
        font-stretch: normal;
        letter-spacing: normal;
        text-align: center;
        color: #4a4a4a;
    }

.mainpage .wizardstep .choosetemplate p {
    font-family: 'GillSans-Light';
    font-size: 16px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 18px;
    letter-spacing: normal;
    text-align: center;
    color: #676767;
}

.mainpage .wizardstep .choosetemplate .resumename {
    width: 321px;
    margin: 0 auto;
}

.mainpage .wizardstep .wizardstepcontent.choosetemplate .wid321 {
    width: 321px !important;
    height: 30px !important;
}

.mainpage .wizardstep .inputlabel.autofilllabel {
    line-height: 40px;
}

.relative {
    position: relative;
}

.beta-testing-icon {
    position: absolute;
    right: 0;
    top: 0px;
}

.mainpage .wizardstep .autofillchks label {
    color: #000;
    font-family: 'AvenirNextLTPro-Regular';
    font-weight: 200;
    font-size: 16px;
    padding-left: 8px;
    position: relative;
    top: -6px;
    font-style: normal;
    font-stretch: normal;
    line-height: 22px;
    letter-spacing: normal;
}

.singleview select:focus, .singleview input:focus {
    border: solid 1px #215fcd !important;
}

.singleview .filteropt {
    height: 25px;
}

.mainpage .wizardstep .singleview .filtersidebar .filterheading {
    font-family: 'GillSans';
    font-size: 20px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 23px;
    letter-spacing: normal;
    display: block;
    text-transform: uppercase;
    text-align: center;
    color: #000000;
}

.mainpage .wizardstep .singleview .filtersidebar .filtersubheading {
    font-family: 'GillSans';
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 16px;
    letter-spacing: normal;
    text-transform: uppercase;
    color: #000000;
}

.mainpage .wizardstep .singleview .filtersidebar .filterpanel .filteroptionlist > li label {
    font-family: 'GillSans-Light';
    font-style: normal;
    font-size: 15px;
    font-stretch: normal;
    line-height: 24px;
    letter-spacing: normal;
    text-align: left;
    color: #4a4a4a;
    display: inline-block;
    padding-left: 30px;
}

.mainpage .wizardstep .singleview .filtersidebar .filterpanel .filteroptionlist > li {
    position: relative;
}

.mainpage .wizardstep .singleview .specialchk {
    position: absolute;
}

.singleview #chosenfilters .selectedfilteroptionslist li button {
    border-radius: 15px;
    font-family: 'GillSans-Light';
    font-size: 13px;
    color: #4A4A4A;
    background-color: #D8D8D8;
    padding: 3px 10px;
    border: none;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    font-weight: 700;/*BV, 2018 07 27 - As suggest on 7/13*/
}

.mainpage .wizardstep .singleview .filtersidebar .filterpanel {
    padding-bottom: 10px;
    margin-bottom: 10px;
    border-bottom: 1px solid #d1d1d1;
    position: relative;
}

    .mainpage .wizardstep .singleview .filtersidebar .filterpanel.fixtobottom {
        border-bottom: 0px solid #d1d1d1;
        padding-top: 50px;
    }

.mainpage .wizardstep .singleview .betatestingicon {
    padding-bottom: 20px;
}

.mainpage .wizardstep .singleview [type="radio"] {
    left: 0px;
}

    .mainpage .wizardstep .singleview [type="radio"]:checked, .mainpage .wizardstep .singleview [type="radio"]:not(:checked) {
        position: absolute;
        left: -9999px;
    }

        .mainpage .wizardstep .singleview [type="radio"]:checked + label, .mainpage .wizardstep .singleview [type="radio"]:not(:checked) + label {
            position: relative;
            padding-left: 28px;
            cursor: pointer;
            line-height: 20px;
            display: inline-block;
            color: #666;
        }

            .mainpage .wizardstep .singleview [type="radio"]:checked + label:before, .mainpage .wizardstep .singleview [type="radio"]:not(:checked) + label:before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                width: 18px;
                height: 18px;
                border: 1px solid #ddd;
                border-radius: 100%;
                background: #fff;
            }

            .mainpage .wizardstep .singleview [type="radio"]:checked + label:after, .mainpage .wizardstep .singleview [type="radio"]:not(:checked) + label:after {
                content: '';
                width: 12px;
                height: 12px;
                background: #F87DA9;
                position: absolute;
                top: 4px;
                left: 4px;
                border-radius: 100%;
                -webkit-transition: all 0.2s ease;
                transition: all 0.2s ease;
            }

            .mainpage .wizardstep .singleview [type="radio"]:not(:checked) + label:after {
                opacity: 0;
                -webkit-transform: scale(0);
                transform: scale(0);
            }

            .mainpage .wizardstep .singleview [type="radio"]:checked + label:after {
                opacity: 1;
                -webkit-transform: scale(1);
                transform: scale(1);
            }

    .mainpage .wizardstep .singleview [type="radio"]:checked, .mainpage .wizardstep .singleview [type="radio"]:not(:checked) {
        position: absolute;
        left: -9999px;
    }

        .mainpage .wizardstep .singleview [type="radio"]:checked + label, .mainpage .wizardstep .singleview [type="radio"]:not(:checked) + label {
            position: relative;
            padding-left: 30px;
            padding-right: 10px;
            cursor: pointer;
            line-height: 22px;
            display: inline-block;
            color: #4a4a4a;
            margin: 0;
            font-size: 18px;
            font-family: 'Optima-Regular';
            font-weight: 300;
            width: 100%;
            text-transform: uppercase;
        }

        .mainpage .wizardstep .singleview [type="radio"]:checked + label {
            color: #410260;
        }

            .mainpage .wizardstep .singleview [type="radio"]:checked + label:before, .mainpage .wizardstep .singleview [type="radio"]:not(:checked) + label:before {
                content: '';
                position: absolute;
                left: 0px;
                top: 2px;
                width: 20px;
                height: 20px;
                border: solid 1px #a4a4a4;
                border-radius: 100%;
                background: #fff;
            }

            .mainpage .wizardstep .singleview [type="radio"]:checked + label:after, .mainpage .wizardstep .singleview [type="radio"]:not(:checked) + label:after {
                content: '';
                width: 14px;
                height: 14px;
                background: #410260;
                position: absolute;
                top: 5px;
                left: 3px;
                border-radius: 100%;
                -webkit-transition: all 0.2s ease;
                transition: all 0.2s ease;
            }

        .mainpage .wizardstep .singleview [type="radio"]:not(:checked) + label:after {
            opacity: 0;
            -webkit-transform: scale(0);
            transform: scale(0);
        }

        .mainpage .wizardstep .singleview [type="radio"]:checked + label:after {
            opacity: 1;
            -webkit-transform: scale(1);
            transform: scale(1);
        }

.mainpage .wizardstep .wizardtemplatesingleview .templateselectorradio label,
.mainpage .wizardstep .wizardtemplatesingleviewdetail .detailheading {
    font-family: 'GillSans';
    text-transform: capitalize;
    color: #4A4A4A;
    margin-top: 0px;
    font-size: 20px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 25px;
    letter-spacing: normal;
    text-align: left;
    color: #000000;
}

.mainpage .wizardstep .wizardtemplatesingleviewdetail p {
    font-family: 'GillSans-SemiBold';
    font-size: 12px;
    font-weight: 600;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #000000;
    margin: 10px 0px;
}

.mainpage .wizardstep .wizardtemplatesingleviewdetail img {
    max-width: 100%;
}

.wizardtemplategridview .wizardtemplategriditem {
    margin-bottom: 30px;
}

    .wizardtemplategridview .wizardtemplategriditem .img {
        border: solid 1px #9b9b9b;
        padding: 5px;
        margin: 0 auto;
        margin-bottom: 5px;
        position: relative;
    }

    .wizardtemplategridview .wizardtemplategriditem img {
        max-width: 100%;
    }

    .wizardtemplategridview .wizardtemplategriditem .wizardtemplategriditem_desc {
        font-family: 'GillSans-Light';
        font-size: 13px;
        font-style: normal;
        font-stretch: normal;
        line-height: 1.31;
        letter-spacing: normal;
        text-align: center;
        color: #4a4a4a;
        height: 34px; /*4/4,BV*/
    }

.mainpage .wizardstep .singleview .wizardtemplatesingleview .pointerright,
.mainpage .wizardstep .singleview .wizardtemplategridview .pointerright {
    position: absolute;
    top: 50%;
    right: 5px;
    margin-top: -100px;
    height: 117px;
}

.mainpage .wizardstep .singleview .wizardtemplatesingleview .pointerleft, .mainpage .wizardstep .singleview .wizardtemplategridview .pointerleft {
    position: absolute;
    top: 50%;
    left: 15px;
    margin-top: -100px;
    height: 117px;
}

.mainpage .wizardstep .wizardtemplatesingleview .templatebigpreview {
    border: 1px solid #808080;
    position: relative;
    padding: 20px;
}

    .mainpage .wizardstep .wizardtemplatesingleview .templatebigpreview img {
        max-width: 100%;
    }

.stillactive {
    font-family: 'GillSans-Light';
    font-size: 15px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 16px;
    letter-spacing: normal;
    text-align: left;
    color: #9b9b9b;
}

.mainpage .wizardstep .achievements-form .button.savebtn.affiliations_btn {
    margin-top: 20px;
}

.fixtobottom-right {
    right: 30px;
    left: auto;
}

.fullpageicon {
    position: absolute;
    bottom: 10px;
    right: 10px;
}

.closeicon {
    position: absolute;
    top: 10px;
    right: 10px;
}

.mainpage .wizardstep .filtersidebar .filterpanel.fixtobottom .templateview li.active img {
    border: 3px solid #000;
}

.mainpage .wizardstep .filtersidebar .filterpanel.fixtobottom .templateview li img {
    border: 1px solid #000;
}

.mainpage .wizardstep .filtersidebar .filterpanel.fixtobottom .templateview li img {
    height: 30px;
}

.mainpage .wizardstep .wizardheading .wizardhat img {
    position: absolute;
    right: 5px;
    top: -5px;
}

.asterisk {
    background: url(../images/asterisk.png) no-repeat;
    width: 17px;
    height: 16px;
    display: inline-block;
}

.asterisk-15 {
    background: url(../images/asteriskb-15x15.png) no-repeat;
    width: 15px;
    height: 15px;
    display: inline-block;
    vertical-align: top;
}

.asterisk-12 {
    background: url(../images/asteriskb-12x12.png) no-repeat;
    width: 12px;
    height: 11px;
    display: inline-block;
    vertical-align: middle;
    margin-top: -5px;
}

.padleft5 {
    padding-left: 5px;
}

.mainpage .wizardstep .savebuttonbox {
    text-align: right;
    margin-top: 87px;
    margin-right: 20px;
}

.mainpage .wizardstep .button.size1 {
    padding: 5px 28px;
    border-radius: 30px;
    font-family: 'Optima-Regular';
    font-size: 18px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #410260;
    line-height: 18px;
    margin-left: 13px;
}

.mainpage .wizardstep .button.size2 {
    padding: 6px 32px 7px;
    border-radius: 30px;
    font-family: 'Optima-Regular';
    font-size: 16px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #410260;
    line-height: 18px;
    margin-left: 13px;
}

.mainpage .wizardstep .headingcontent .steplabel {
    color: #410260;
    font-family: "GillSans";
    font-size: 14px;
    font-stretch: normal;
    font-style: normal;
    font-weight: normal;
    letter-spacing: normal;
    line-height: normal;
    margin-bottom: 0;
    text-align: left;
    text-transform: uppercase;
}

.mainpage .wizardstep .headingcontent .stepname {
    color: #410260;
    font-family: "GillSans-Light";
    font-size: 28px;
    font-stretch: normal;
    font-style: normal;
    font-weight: 300;
    letter-spacing: normal;
    line-height: 28px;
    margin: 0;
    text-align: left;
    text-transform: uppercase;
}

.inputlabel.categorylist-heading {
    margin: 19px 0px 13px;
    font-family: 'Optima-Regular';
    font-size: 24px !important;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 28px;
    letter-spacing: normal;
    text-align: left;
    color: #410260;
    margin-top: 19px;
}

.mainpage .wizardstep .chaticon {
    height: 20px;
    width: 23px;
    background-image: url(../images/chat-icon.png);
    background-repeat: no-repeat;
    background-position: center;
    display: inline-block;
    vertical-align: middle;
    position: relative;
}

.mainpage .wizardstep .wizardstepcontent .categoryorder {
    margin: 0px;
    padding: 0px;
    margin-left: 0px;
    margin-right: 20px;
    margin-bottom: 25px;
}
.mainpage .wizardstep .wizardstepcontent .forminputb .categoryorder {
    margin-right: 0px;
}
.mainpage .wizardstep .wizardstepcontent .achievementscontainer.category {
    margin-right: 15px;
    min-width: 360px !important;
    width: 65%;
}

.categorydetail {
    margin-top: 0px;
}

    .categorydetail .subheadinglevel1 {
        float: left;
        font-family: "GillSans-Light";
        font-size: 15px;
        font-weight: 300;
        font-style: normal;
        font-stretch: normal;
        line-height: 17px;
        letter-spacing: normal;
        text-align: left;
        color: #9b9b9b;
        text-transform: uppercase;
    }

    .categorydetail .subheadinglevel2 {
        float: right;
    }

        .categorydetail .subheadinglevel2 a.help.blue {
            font-family: "GillSans" !important;
            font-size: 12px !important;
            font-weight: normal;
            font-style: normal;
            font-stretch: normal;
            line-height: 16px;
            letter-spacing: normal;
            text-align: center;
            color: #410260;
        }

.clearboth {
    clear: both;
}

.mainpage .wizardstep .categorydetail .specialchk {
    vertical-align: middle;
}

.mainpage .wizardstep .wizardstepcontent .purposehint {
    width: 200px;
    position: absolute;
    left: calc(100% - 210px);
    top: 100px;
    border: 3px solid #4e72b3;
    padding: 15px 7px;
    display: inline-block;
    font-family: 'GillSansCE-Roman';
    font-size: 13px;
    letter-spacing: normal;
    background-color: #fafafa;
    right: 20px;
    color: #6d6d6d;
    line-height: 15px;
}

    .mainpage .wizardstep .wizardstepcontent .purposehint a.close {
        position: absolute;
        right: 5px;
        top: 0px;
        font-size: 20px;
        font-family: GillSans-Light;
        font-weight: 100;
        opacity: 1;
    }

.mainpage .wizardstep .wizardstepcontent .promotedemotehint {
    padding: 40px 8px;
    border: 3px solid #573079;
    width: 166px;
    height: 166px;
    border-radius: 50%;
    text-align: center;
    font-family: 'GillSansCE-Roman';
    font-size: 13px;
    letter-spacing: normal;
    line-height: 15px;
    background-color: #fafafa;
    position: absolute;
    top: 30%;
    right: -28px;
    z-index: 1;
    color: #6d6d6d;
}

    .mainpage .wizardstep .wizardstepcontent .promotedemotehint strong, .mainpage .wizardstep .wizardstepcontent .purposehint strong {
        color: #000000;
    }

.mainpage .wizardstep .wizardstepcontent .addcategoryhint {
    border: 4px solid #416518;
    width: 266px;
    padding: 5px;
    text-align: left;
    font-family: 'GillSansCE-Roman';
    font-size: 13px;
    letter-spacing: normal;
    line-height: 15px;
    background-color: #fafafa;
    position: absolute;
    top: 0px;
    right: -83px;
    color: #6d6d6d;
}

.mainpage .wizardstep .wizardstepcontent .underlinedheading {
    display: inline-block;
    min-width: 250px;
    max-width: 60%;
    border-bottom: 1px solid #410260;
    padding: 5px 0px;
    font-family: 'Optima-Regular';
    font-size: 24px;
    text-transform: uppercase;
    color: #410260;
    letter-spacing: normal;
}

.savebuttonboxedu {
    padding-top: 368px;
    text-align: center;
}

.affiliations_btn {
    font-size: 16px;
}

.mainpage .wizardstep .inputerror {
    margin-top: 10px;
    color: #C01F2A;
    font-size: 14px;
    font-family: 'GillSansCE-Roman';
    display: inline-block;
    font-weight: 500;
    background-image: url(../images/Exclamation-15x15.png);
    background-repeat: no-repeat;
    background-position-y: top 2px;
    background-position-x: left;
    padding-left: 20px;
    letter-spacing: normal;
}

.marginb45 {
    margin-bottom: 45px !important;
}

.margint45 {
    margin-top: 45px !important;
}

.margint36 {
    margin-top: 36px !important;
}

.margint33 {
    margin-top: 33px !important;
}

.mainpage .wizardstep .wizardstepcontent .achievementformmonthhint {
    border: 5px solid #57307A;
    text-align: center;
    padding: 10px;
    font-family: 'GillSans';
    font-size: 14px;
    background-color: #F9F9F9;
    width: 125px;
    z-index: 1;
    position: absolute;
    top: 10px;
    color: #4a4a4a;
}

.mainpage .wizardstep .wizardstepcontent .achievementformmonthhint {
    top: 25px;
    right: -20px;
}

.mainpage .wizardstep .wizardstepcontent .achievementformyearshint {
    border: 3px solid #4E72B3;
    text-align: center;
    padding: 5px;
    padding-top: 20px;
    font-family: 'GillSans';
    font-size: 14px;
    background-color: #F9F9F9;
    width: 130px;
    height: 130px;
    border-radius: 50%;
    z-index: 1;
    position: absolute;
    top: 130px;
    color: #4a4a4a;
}

.mainpage .wizardstep .wizardstepcontent .achievementformyearshint {
    top: 145px;
    right: 10px;
}

    .mainpage .wizardstep .wizardstepcontent .achievementformyearshint .close {
        left: 45%;
        position: absolute;
        top: 5px;
    }

.mainpage .wizardstep .wizardstepcontent .achievementformlevelhint {
    position: absolute;
    border: 0px;
    padding: 0px;
    right: -220px;
    bottom: -120px;
    z-index: 1;
    background-color: transparent;
    background-image: url(../images/Level-Hint.png);
    background-position: center;
    background-repeat: no-repeat;
    width: 212px;
    height: 221px;
}

.mainpage .wizardstep .wizardstepcontent .achievementformlevelhint {
    width: 190px;
    right: auto;
    right: -50px;
    top: 238px;
    bottom: auto;
    background-size: contain;
}

    .mainpage .wizardstep .wizardstepcontent .achievementformlevelhint .close {
        position: absolute;
        top: 45px;
        left: 50%;
        margin-left: -10px;
    }

.mainpage .wizardstep .achievements-form .forminputb {
    margin-bottom: 11px;
}

.mainpage .wizardstep .personalinfoform .forminput {
    width: 55%;
}

.mainpage .wizardstep .personalinfoform ul.inlinelist li {
    position: relative;
    line-height: 25px;
}

.mainpage .wizardstep .personalinfoform ul.inlinelist .specialchk {
    position: absolute;
    left: 0px;
}

.mainpage .wizardstep .personalinfoform ul.inlinelist li.paddleft {
    padding-left: 20px;
    padding-right: 5px;
}

.trianglebox1 {
    width: 0;
    height: 0;
    border-left: 100px solid transparent;
    border-right: 100px solid transparent;
    border-bottom: 188px solid #979797;
    position: absolute;
    z-index: 1;
    top: 206px;
    left: -10px;
}

    .trianglebox1 .inner-triangle {
        position: relative;
        top: 6px;
        left: -90px;
        width: 0;
        height: 0;
        border-left: 90px solid transparent;
        border-right: 90px solid transparent;
        border-bottom: 178px solid #fafafa;
    }

        .trianglebox1 .inner-triangle .text {
            width: 120px;
            position: absolute;
            top: 70px;
            left: -65px;
            font-family: 'GillSans';
            font-size: 14px;
            text-align: center;
            color: #4a4a4a;
            font-weight: normal;
            font-style: normal;
            font-stretch: normal;
            line-height: 15px;
            letter-spacing: normal;
        }

.mainpage .wizardstep .personalinfoform .hintpanel .resumevisiblityhint {
    /*chhanda #1287 8/4/2018*/
    position: absolute;
    top: 400px;
    right: -20px;
    border: 5px solid #69986C;
    padding: 20px 15px;
    width: 220px;
    font-family: 'GillSans';
    background-color: #F9F9F9;
    color: #4a4a4a;
    z-index: 2;
    font-size: 14px;
    background-color: #F9F9F9;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 15px;
    letter-spacing: normal;
    text-align: left;
    color: #4a4a4a;
}

.mainpage .wizardstep .personalinfoform .hintpanel .resumenamehint {
    /*chhanda #1287 8/4/2018*/
    /*position: relative;
    top: -20px;
    left: 20px;*/
    padding: 20px 15px;
    border: 5px solid #57307A;
    max-width: 160px;
    font-family: 'GillSans';
    background-color: #F9F9F9;
    color: #4a4a4a;
    font-size: 14px;
    background-color: #F9F9F9;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 15px;
    letter-spacing: normal;
    text-align: left;
    color: #4a4a4a;
}

.mainpage .wizardstep .personalinfoform .hintpanel .flipbookurlhint {
    /*chhanda #1287 8/4/2018*/
    position: absolute;
    top: 100px;
    right: -20px;
    padding: 30px 10px;
    border: 5px solid #4E72B3;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    text-align: center;
    font-family: 'GillSans';
    font-size: 14px;
    background-color: #F9F9F9;
    color: #4a4a4a;
    font-size: 14px;
    background-color: #F9F9F9;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 15px;
    letter-spacing: normal;
    color: #4a4a4a;
}

    .mainpage .wizardstep .personalinfoform .hintpanel .resumevisiblityhint strong, .trianglebox1 .inner-triangle .text strong, .mainpage .wizardstep .personalinfoform .hintpanel .resumenamehint strong, .mainpage .wizardstep .personalinfoform .hintpanel .flipbookurlhint strong {
        color: #000;
    }

.mainpage .wizardstep .personalinfoform .hintpanel .resumevisiblityhint .close {
    position: absolute;
    top: 10px;
    right: 0;
    font-size: 18px;
}

.mainpage .wizardstep .personalinfoform .hintpanel .trianglebox1 .close {
    position: absolute;
    top: 20px;
    left: -10px;
    font-size: 18px;
}

.mainpage .wizardstep .personalinfoform .hintpanel .flipbookurlhint .close {
    position: absolute;
    top: 10px;
    left: 43%;
    font-size: 18px;
}

.mainpage .wizardstep .personalinfoform .hintpanel .resumenamehint .close {
    position: absolute;
    top: 5px;
    right: 5px;
    font-size: 18px;
}

.mainpage .wizardstep .radiolistitem [type="radio"] {
    left: 0px;
}

    .mainpage .wizardstep .radiolistitem [type="radio"]:checked, .mainpage .wizardstep .radiolistitem [type="radio"]:not(:checked) {
        position: absolute;
        left: -9999px;
    }

        .mainpage .wizardstep .radiolistitem [type="radio"]:checked + label, .mainpage .wizardstep .radiolistitem [type="radio"]:not(:checked) + label {
            position: relative;
            padding-left: 28px;
            cursor: pointer;
            line-height: 20px;
            display: inline-block;
            color: #666;
        }

            .mainpage .wizardstep .radiolistitem [type="radio"]:checked + label:before, .mainpage .wizardstep .radiolistitem [type="radio"]:not(:checked) + label:before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                width: 18px;
                height: 18px;
                border: 1px solid #ddd;
                border-radius: 100%;
                background: #fff;
            }

            .mainpage .wizardstep .radiolistitem [type="radio"]:checked + label:after, .mainpage .wizardstep .radiolistitem [type="radio"]:not(:checked) + label:after {
                content: '';
                width: 12px;
                height: 12px;
                background: #F87DA9;
                position: absolute;
                top: 4px;
                left: 4px;
                border-radius: 100%;
                -webkit-transition: all 0.2s ease;
                transition: all 0.2s ease;
            }

            .mainpage .wizardstep .radiolistitem [type="radio"]:not(:checked) + label:after {
                opacity: 0;
                -webkit-transform: scale(0);
                transform: scale(0);
            }

            .mainpage .wizardstep .radiolistitem [type="radio"]:checked + label:after {
                opacity: 1;
                -webkit-transform: scale(1);
                transform: scale(1);
            }

    .mainpage .wizardstep .radiolistitem [type="radio"]:checked, .mainpage .wizardstep .radiolistitem [type="radio"]:not(:checked) {
        position: absolute;
        left: -9999px;
    }

        .mainpage .wizardstep .radiolistitem [type="radio"]:checked + label, .mainpage .wizardstep .radiolistitem [type="radio"]:not(:checked) + label {
            position: relative;
            padding-left: 30px;
            padding-right: 10px;
            cursor: pointer;
            line-height: 22px;
            display: inline-block;
            color: #4a4a4a;
            margin: 0;
            font-size: 18px;
            font-family: 'Optima-Regular';
            font-weight: 300;
            width: 100%;
            text-transform: uppercase;
        }

        .mainpage .wizardstep .radiolistitem [type="radio"]:checked + label {
            color: #410260;
        }

            .mainpage .wizardstep .radiolistitem [type="radio"]:checked + label:before, .mainpage .wizardstep .radiolistitem [type="radio"]:not(:checked) + label:before {
                content: '';
                position: absolute;
                left: 0px;
                top: 2px;
                width: 20px;
                height: 20px;
                border: solid 1px #a4a4a4;
                border-radius: 100%;
                background: #fff;
            }

            .mainpage .wizardstep .radiolistitem [type="radio"]:checked + label:after, .mainpage .wizardstep .radiolistitem [type="radio"]:not(:checked) + label:after {
                content: '';
                width: 14px;
                height: 14px;
                background: #410260;
                position: absolute;
                top: 5px;
                left: 3px;
                border-radius: 100%;
                -webkit-transition: all 0.2s ease;
                transition: all 0.2s ease;
            }

        .mainpage .wizardstep .radiolistitem [type="radio"]:not(:checked) + label:after {
            opacity: 0;
            -webkit-transform: scale(0);
            transform: scale(0);
        }

        .mainpage .wizardstep .radiolistitem [type="radio"]:checked + label:after {
            opacity: 1;
            -webkit-transform: scale(1);
            transform: scale(1);
        }

.mainpage .radiolist.radiolistitem .paddedradiocont {
    padding: 10px 0px;
}

.mainpage .radiolist.radiolistitem {
    width: 75%;
    margin-left: 25% !important;
    display: inline-block;
    text-align: right;
    padding-top: 5px;
}

    .mainpage .radiolist.radiolistitem li {
        display: inline-block;
        float: none;
        width: 15%;
        position: relative;
        text-align: center;
        font-family: 'GillSansCE-Roman';
        font-size: 9px;
        line-height: 10px;
        color: #9b9b9b;
        letter-spacing: normal;
    }

.langtitle {
    position: absolute;
    top: 5px;
}

.langicon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: 2px solid #979797;
    font-family: 'GillSans-SemiBold';
    font-size: 15px;
    text-transform: uppercase;
    color: #9B9B9B;
    line-height: 35px;
    text-align: center;
    display: inline-block;
}

label.inputlabellang {
    text-transform: capitalize;
    font-family: 'GillSans-Light';
    font-size: 17px;
    color: #9B9B9B;
    position: relative;
    line-height: 35px;
    font-weight: 500;
}

.extralandsubheading1 {
    margin-top: -15px;
    font-family: 'GillSans-Light';
    font-size: 15px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.67;
    letter-spacing: normal;
    text-align: left;
    color: #9b9b9b;
}

.mainpage .wizardstep .textinput2 {
    border-left: 0px;
    border-right: 0px;
    border-top: 0px;
    font-family: 'GillSans-Light';
    font-size: 15px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    letter-spacing: normal;
    text-align: left;
    color: #9b9b9b;
}

.mainpage .wizardstep .select2 {
    border-left: 0px !important;
    border-right: 0px !important;
    border-top: 0px !important;
}

.mainpage .wizardstep .wizardstepcontent .achievementsform .checkboxlist label {
    position: relative;
    font-family: 'GillSans-Light';
    font-size: 18px;
    letter-spacing: normal;
}

.mainpage .wizardstep .wizardstepcontent .achievementsform .checkboxlist.font15 label {
    position: relative;
    font-family: 'GillSans-Light';
    font-size: 15px;
    letter-spacing: normal;
    line-height: 15px;
}

.mainpage .wizardstep .wizardstepcontent .achievementsform .checkboxlist ul li {
    position: relative;
    padding-left: 25px;
    margin-left: 10px;
}

.mainpage .wizardstep .wizardstepcontent .achievementsform .checkboxlist .specialchk {
    position: absolute;
    left: 0px;
    top: 8px;
}

.mainpage .wizardstep .wizardstepcontent .achievementsform .checkboxlist .more {
    margin-left: 5px;
    color: #57307a;
    font-size: 15px;
    line-height: 18px;
    letter-spacing: normal;
    margin-bottom: 10px;
}

.mainpage .wizardstep .skillscontainer h6.h6text {
    margin: 13px 0px 5px;
    font-family: 'GillSans-Light';
    font-size: 15px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #9b9b9b;
}

.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 20px;
    vertical-align: middle;
}

    .switch input {
        display: none;
    }

    .switch .sliderround {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #fff;
        -webkit-transition: .4s;
        transition: .4s;
        border: solid 1px #adbac9;
    }

        .switch .sliderround:before {
            position: absolute;
            content: "";
            height: 10px;
            width: 10px;
            left: 4px;
            bottom: 4px;
            background-color: #57307A;
            -webkit-transition: .4s;
            transition: .4s;
        }

    .switch input:checked + .sliderround {
        background-color: #fff;
    }

    .switch input:focus + .sliderround {
        box-shadow: 0 0 1px #2196F3;
    }

    .switch input:checked + .sliderround:before {
        -webkit-transform: translateX(26px);
        -ms-transform: translateX(26px);
        transform: translateX(26px);
    }

    .switch .sliderround.round {
        border-radius: 34px;
    }

        .switch .sliderround.round:before {
            border-radius: 50%;
        }

.maringt15 {
    margin-top: 15px;
}

.maringt74 {
    margin-top: 74px;
}

.gillsanl15 {
    font-family: 'GillSans-Light';
    font-size: 15px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 17px;
    letter-spacing: -0.2px;
    text-align: left;
    color: #9b9b9b;
}

.paddtb1219 {
    padding: 12px 0px 19px;
}

.margt0 {
    margin-top: 0px !important;
}

.paddt65 {
    padding-top: 65px;
}

.note {
    font-family: 'GillSansCE-Roman';
    font-size: 13px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #000000;
}

.paddt120 {
    padding-top: 120px;
}

.ratedradio {
    float: left;
    margin-left: -50px;
}

.ratedlabel {
    float: left;
    padding-left: 50px;
}

.ratedrecom {
    padding-left: 0px;
    margin-left: -15px;
    margin-top: 2px;
    font-family: 'GillSans';
    font-size: 10px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #9b9b9b;
}

.paddt15 {
    padding-top: 15px;
}

button.dropdown {
    text-align: left;
    padding: 3px 5px;
    min-width: 200px !important;
    color: #9b9b9b !important;
}

    button.dropdown a {
        color: #9b9b9b !important;
        font-family: 'GillSans-Light';
        font-size: 15px;
        font-weight: 300;
        font-style: normal;
        font-stretch: normal;
        letter-spacing: normal;
        text-align: left;
    }

    button.dropdown .caret {
        position: absolute;
        right: 10px;
        top: 40%;
    }

button.dropdown1 {
    text-align: left;
    padding: 3px 30px 3px 5px !important;
    color: #9b9b9b !important;
}

    button.dropdown1 a {
        color: #9b9b9b !important;
        font-family: 'GillSans-Light';
        font-size: 15px;
        font-weight: 300;
        font-style: normal;
        font-stretch: normal;
        letter-spacing: normal;
        text-align: left;
    }

    button.dropdown1 .caret {
        position: absolute;
        right: 10px;
        top: 40%;
    }

.inputplussign {
    position: relative;
}

    .inputplussign .input-group-addon {
        font-size: 16px;
        position: absolute;
        right: -25px;
        top: 0px;
        width: 20px;
        height: 25px;
        line-height: 25px;
    }

 .personalinfoform .inputplussign .input-group-addon .glyphicon {margin-left: -6px;}
 /*ST-1188*/
.inputplussign .input-group-addon .glyphicon {margin-left: -10px;}

.mainpage .wizardstep .checkboxlist .specialchk {
    padding-left: 0px;
    background-size: contain;
    cursor: pointer;
    width: 13px;
    height: 13px;
    border: solid 1px #979797;
    display: inline-block;
    position: relative;
}

    .mainpage .wizardstep .checkboxlist .specialchk.checked:before {
        position: absolute;
        width: 30px;
        height: 30px;
        content: '';
        background-image: url(../images/icon4.png);
        background-repeat: no-repeat;
        background-position: left;
        background-position-y: center;
        background-size: 19px 16px;
        left: 0px;
        top: -12px;
    }

.marglm90 {
    margin-left: -90px;
}

.marglm40 {
    margin-left: -40px;
}

.mainpage .wizardstep .button.size3 {
    padding: 3px 28px;
    border-radius: 30px;
    height: 25px;
    font-family: 'Optima-Regular';
    font-size: 18px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 18px !important;
    letter-spacing: normal;
    text-align: center;
    color: #410260;
    line-height: 18px;
    margin-left: 13px;
}

.minheight {
    min-height: 0px;
}

.hr1 {
    border: 1px dashed #967EAB;
    margin: 25px 0px 24px 0px;
}

.hr2 {
    border: 1px dashed #967EAB;
    margin: 18px 0px 15px 0px;
}

.hr3 {
    border: 1px dashed #967EAB;
    margin: 56px 0px 19px 0px;
}

.mainpage .wizardstep .wizardstepcontent .button.addhighlight {
    color: #9b9b9b;
    font-family: "GillSans";
    font-size: 14px;
    font-stretch: normal;
    font-style: normal;
    font-weight: normal;
    height: 23px;
    letter-spacing: normal;
    line-height: normal;
    padding: 0 9px 0 12px;
    text-align: left;
}

.lineheight {
    line-height: 25px !important;
}

.mainpage .wizardstep .categoryorder .specialchk {
    padding-left: 0px !important;
    background-size: contain;
    cursor: pointer;
    width: 25px;
    height: 25px;
    border: solid 1px #979797;
    display: inline-block;
    position: relative;
}

.mainpage .wizardstep .categoryorder .specialchk {
    vertical-align: top;
}

.margb0 {
    margin-bottom: 0px !important;
}

.margt28 {
    margin-top: 28px;
}

.chaticon.marg1 {
    margin-left: 5px;
    margin-top: 3px;
}

.margt450px {
    margin-top: 450px !important;
}

.paddlr0 {
    padding-left: 0px;
    padding-right: 0px;
}

.chaticonab {
    position: relative !important;
}

    .chaticonab .chaticon {
        position: absolute;
        top: 1px;
    }

.margt8-b5 {
    margin-top: 8px !important;
    margin-bottom: 5px !important;
}

.margt20-b2 {
    margin-top: 20px !important;
    margin-bottom: 2px !important;
}

.margt30 {
    margin-top: 30px !important;
}

.margt12-b2 {
    margin-top: 12px !important;
    margin-bottom: 2px !important;
}

.margt32-b25 {
    margin-top: 32px !important;
    margin-bottom: 25px !important;
}

.margt34 {
    margin-top: 34px !important;
}

.margb24 {
    margin-bottom: 24px !important;
}

.lineheight24 {
    line-height: 24px24px !important;
}

.margt16 {
    margin-top: 16px !important;
}

.margt183 {
    margin-top: 183px !important;
}

.margt13-b2 {
    margin-top: 13px !important;
    margin-bottom: 2px !important;
}

.mainpage .wizardstep .radiolistitemlang [type="radio"] {
    left: 0px;
}

    .mainpage .wizardstep .radiolistitemlang [type="radio"]:checked, .mainpage .wizardstep .radiolistitemlang [type="radio"]:not(:checked) {
        position: absolute;
        left: -9999px;
    }

        .mainpage .wizardstep .radiolistitemlang [type="radio"]:checked + label, .mainpage .wizardstep .radiolistitemlang [type="radio"]:not(:checked) + label {
            position: relative;
            padding-left: 28px;
            cursor: pointer;
            line-height: 15px;
            display: inline-block;
            color: #666;
        }

            .mainpage .wizardstep .radiolistitemlang [type="radio"]:checked + label:before, .mainpage .wizardstep .radiolistitemlang [type="radio"]:not(:checked) + label:before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                width: 15px;
                height: 15px;
                border: 1px solid #ddd;
                border-radius: 100%;
                background: #fff;
            }

            .mainpage .wizardstep .radiolistitemlang [type="radio"]:checked + label:after, .mainpage .wizardstep .radiolistitemlang [type="radio"]:not(:checked) + label:after {
                content: '';
                width: 12px;
                height: 12px;
                background: #F87DA9;
                position: absolute;
                top: 4px;
                left: 4px;
                border-radius: 100%;
                -webkit-transition: all 0.2s ease;
                transition: all 0.2s ease;
            }

            .mainpage .wizardstep .radiolistitemlang [type="radio"]:not(:checked) + label:after {
                opacity: 0;
                -webkit-transform: scale(0);
                transform: scale(0);
            }

            .mainpage .wizardstep .radiolistitemlang [type="radio"]:checked + label:after {
                opacity: 1;
                -webkit-transform: scale(1);
                transform: scale(1);
            }

    .mainpage .wizardstep .radiolistitemlang [type="radio"]:checked, .mainpage .wizardstep .radiolistitemlang [type="radio"]:not(:checked) {
        position: absolute;
        left: -9999px;
    }

        .mainpage .wizardstep .radiolistitemlang [type="radio"]:checked + label, .mainpage .wizardstep .radiolistitemlang [type="radio"]:not(:checked) + label {
            position: relative;
            padding-left: 30px;
            padding-right: 10px;
            cursor: pointer;
            line-height: 15px;
            display: inline-block;
            color: #4a4a4a;
            margin: 0;
            font-size: 18px;
            font-family: 'Optima-Regular';
            font-weight: 300;
            width: 100%;
            text-transform: uppercase;
        }

        .mainpage .wizardstep .radiolistitemlang [type="radio"]:checked + label {
            color: #410260;
        }

            .mainpage .wizardstep .radiolistitemlang [type="radio"]:checked + label:before, .mainpage .wizardstep .radiolistitemlang [type="radio"]:not(:checked) + label:before {
                content: '';
                position: absolute;
                left: 0px;
                top: -2px;
                width: 15px;
                height: 15px;
                border: solid 1px #a4a4a4;
                border-radius: 100%;
                background: #fff;
            }

            .mainpage .wizardstep .radiolistitemlang [type="radio"]:checked + label:after, .mainpage .wizardstep .radiolistitemlang [type="radio"]:not(:checked) + label:after {
                content: '';
                width: 13px;
                height: 13px;
                background: #410260;
                position: absolute;
                top: -1px;
                left: 1px;
                border-radius: 100%;
                -webkit-transition: all 0.2s ease;
                transition: all 0.2s ease;
            }

        .mainpage .wizardstep .radiolistitemlang [type="radio"]:not(:checked) + label:after {
            opacity: 0;
            -webkit-transform: scale(0);
            transform: scale(0);
        }

        .mainpage .wizardstep .radiolistitemlang [type="radio"]:checked + label:after {
            opacity: 1;
            -webkit-transform: scale(1);
            transform: scale(1);
        }

.mainpage .radiolist.radiolistitemlang .paddedradiocont {
    padding: 0px 0px;
}

.mainpage .radiolist.radiolistitemlang {
    width: 75%;
    margin-left: 25%;
    display: inline-block;
    text-align: right;
    padding-top: 5px;
}

    .mainpage .radiolist.radiolistitemlang li {
        display: inline-block;
        float: none;
        width: 15%;
        position: relative;
        text-align: center;
        font-family: 'GillSansCE-Roman';
        font-size: 9px;
        line-height: 10px;
        color: #9b9b9b;
        letter-spacing: normal;
    }

.margtm3 {
    margin-top: -3px;
}

.mainpage .wizardstep .chaticon19 {
    height: 19px;
    width: 19px;
    background-image: url(../images/chat-icon-19x19.png);
    background-repeat: no-repeat;
    background-position: center;
    display: inline-block;
    vertical-align: middle;
}

.margt2 {
    margin-top: 2px !important;
}

.margl0 {
    margin-left: 0px !important;
}

.personalinfoform .inputerror .redok {
    height: 17px;
    width: 20px;
    background-image: url(../images/error-ok-red.png);
    background-repeat: no-repeat;
    background-position: center;
    display: inline-block;
    vertical-align: middle;
}

.personalinfoform .inputerror .greenok {
    height: 17px;
    width: 20px;
    background-image: url(../images/error-ok-green.png);
    background-repeat: no-repeat;
    background-position: center;
    display: inline-block;
    vertical-align: middle;
}

.mainpage .wizardstep .personalinfoform .hintpanel .resumenamehint {
    /*chhanda #1287 8/4/2018*/
    position: absolute;
    top: -20px;
    right: 50px;
    padding: 20px 15px;
    border: 5px solid #57307A;
    /*chhanda #1287 8/4/2018*/
    min-width: 160px;
    font-family: 'GillSans';
    font-size: 14px;
    background-color: #F9F9F9;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 15px;
    letter-spacing: normal;
    text-align: left;
    color: #4a4a4a;
}

.mainpage .wizardstep .inputerror {
    font-family: 'GillSans';
}

.margt37 {
    margin-top: 37px !important;
}

.boxcontent1 {
    padding: 13px 10px 42px 25px;
}

.resumecurrent {
    font-family: 'GillSans';
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #410260;
}

.linknewresume {
    margin: 8px 0px 4px;
    font-family: 'GillSans';
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #410260;
}

.mainpage .wizardstep .button.size4 {
    padding: 4px 25px 3px 26;
    border-radius: 30px;
    font-family: 'Optima-Regular';
    font-size: 15px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #410260;
    line-height: 15px;
}

.mainpage .wizardstep .button.size5 {
    padding: 4px 25px 3px 26;
    border-radius: 30px;
    font-family: 'Optima-Regular';
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #410260;
    line-height: 15px;
}

.success-block {
    color: #426618;
    font-weight: 500;
    font-size: 20px;
    text-transform: uppercase;
    font-family: 'GillSans-SemiBold';
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
}

    .success-block.position {
        position: absolute;
        right: -130px;
        top: 5px;
    }

    .success-block .check {
        font-family: GillSans-SemiBold;
        line-height: 10px;
        font-weight: 600;
        font-size: 40px;
    }

.field-input {
    width: 65%;
    display: inline-block;
    float: left;
}

.field-btn {
    display: inline-block;
    width: 29%;
    float: left;
    margin-left: 10px;
}

.field-input .downloadpdf {
    font-family: 'GillSans-Light';
    font-size: 15px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 28px;
    letter-spacing: normal;
    text-align: left;
    color: #9b9b9b;
}

.field-input > input {
    font-family: 'GillSans-Light';
    font-size: 15px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 25px;
    padding: 0px 10px;
}

.social-check {
    padding: 18px 0px 27px 0px;
}

    .social-check .field-btn {
        width: 100%;
        text-align: center;
        float: none;
        margin: 27px 0 17px;
    }

    .social-check ul {
        list-style: none;
        margin: 0;
        padding: 0;
    }

        .social-check ul li {
            width: 1%;
            display: table-cell;
            text-align: center;
        }

    .social-check .success-block {
        text-align: center;
        margin-top: 15px;
    }

.margt72 {
    margin-top: 72px !important;
}

.left-block {
    float: left;
    color: #f8b42f;
    font-family: 'AvenirNext-Heavy';
    font-size: 20px;
    font-weight: 900;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #f5a623;
    width: 250px;
    text-align: center;
    padding-top: 20px;
}

.right-block {
    float: right;
    text-align: center;
    width: 300px;
}

    .right-block p {
        font-family: 'GillSans';
        font-size: 14px;
        font-weight: normal;
        font-style: normal;
        font-stretch: normal;
        line-height: normal;
        letter-spacing: normal;
        color: #410260;
        margin-bottom: 11px;
    }

.mainpage .wizardstep .wizardstepcontent.achievements-form .underlinedheading.publishheading {
    max-width: 100%;
    margin: 0px;
    margin-bottom: 15px;
    line-height: 22px;
    font-family: 'Optima-Regular';
    font-size: 18px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    letter-spacing: normal;
    text-align: left;
    color: #410260;
    text-transform: none;
    padding-top: 0px;
}

.margt59 {
    margin-top: 59px !important;
}

.margt16-b9 {
    margin-top: 19px !important;
    margin-bottom: 9px !important;
}

.margt19 {
    margin-top: 19px !important;
}

.margt12-b11 {
    margin-top: 12px !important;
    margin-bottom: 11px !important;
}

.margt43-b9 {
    margin-top: 43px !important;
    margin-bottom: 9px !important;
}

.margt27-b3 {
    margin-top: 27px !important;
    margin-bottom: 3px !important;
}

.margt24 {
    margin-top: 24px !important;
}

.margl5 {
    margin-left: 5px;
}

.select3 {
    line-height: 38px !important;
    height: 38px !important;
}

.maringt20 {
    margin-top: 20px !important;
}

.forminputb .letsget {
    font-size: 12px;
}

.padlr5 {
    padding-left: 5px;
    padding-right: 5px;
}

.mainpage .wizardstep .personalinfoform .forminput .input-group .input-group-addon2 {
    right: 0px;
    border-radius: 0px;
    border: 1px solid #979797;
    background-color: transparent;
    font-weight: 300;
    font-size: 12px;
    color: #410260;
    border-bottom: 0px;
    height: 24px;
    width: 23px;
    text-align: center;
}

    .mainpage .wizardstep .personalinfoform .forminput .input-group .input-group-addon2 i {
        padding-left: 8px;
    }

.mainpage .wizardstep .checkboxlist1 .specialchk {
    margin-top: 6px;
    padding-left: 0px;
    background-size: contain;
    cursor: pointer;
    width: 13px;
    height: 13px;
    border: solid 1px #979797;
    display: inline-block;
    position: relative;
}

    .mainpage .wizardstep .checkboxlist1 .specialchk.checked:before {
        position: absolute;
        width: 30px;
        height: 30px;
        content: '';
        background-image: url(../images/icon4.png);
        background-repeat: no-repeat;
        background-position: left;
        background-position-y: center;
        background-size: 19px 16px;
        left: 0px;
        top: -12px;
    }

.color {
    color: #2c2c2c !important;
}

.mainpage .wizardstep .button.size6 {
    padding: 0px 25px 0px 26px !important;
    border-radius: 30px;
    font-family: 'Optima-Regular';
    font-size: 15px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #410260;
    line-height: 23px;
    height: 25px;
}

.success-block .check {
    font-family: GillSans-SemiBold;
    line-height: 10px;
    font-weight: 600;
    font-size: 30px;
    background: url('../images/greencheck.png') no-repeat left;
    width: 37px;
    height: 37px;
    position: absolute;
    left: 0px;
    bottom: 0px;
}

.success-block {
    padding-left: 26px;
}

    .success-block .check1 {
        font-family: GillSans-SemiBold;
        line-height: 10px;
        font-weight: 600;
        font-size: 36px;
    }

.mainpage .wizardstep .button.size7 {
    padding: 0px 25px 0px 26px !important;
    border-radius: 30px;
    font-family: 'Optima-Regular';
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #410260;
    line-height: 23px;
    height: 25px;
}

.mainpage .wizardstep .button.size8 {
    padding: 0px 15px 0px 16px !important;
    border-radius: 30px;
    font-family: 'Optima-Regular';
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #410260;
    line-height: 23px;
    height: 25px;
}

.mainpage .wizardstep .selectinput {
    height: 25px !important;
}

.mainpage .wizardstep .selectinput2 {
    border: 1px solid #9B9B9B;
    color: #9b9b9b;
    font-size: 14px;
    line-height: 25px;
    display: block;
    font-family: 'GillSans-Light';
    position: relative;
    background: #FFF url(../images/RW_select-arrow-b2.png) no-repeat scroll 100% center / 25px 25px;
    height: 25px !important;
}

.mainpage .wizardstep .wizardstepcontent .achievementsform .textinput {
    font-family: 'GillSans-Light';
    font-size: 15px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 25px;
    letter-spacing: normal;
    text-align: left;
    color: #9b9b9b;
}

.hrdash {
    background: url('../images/dashicon.png') repeat-x;
    height: 2px;
    border: 0px;
}

.margt20-b12 {
    margin-top: 20px !important;
    margin-bottom: 12px !important;
}

.maringt44 {
    margin-top: 44px !important;
}

.mainpage .wizardstep .filteroptionlist .specialchk {
    padding-left: 0px;
    background-size: contain;
    cursor: pointer;
    width: 13px;
    height: 13px;
    border: solid 1px #979797;
    display: inline-block;
    position: relative;
}

    .mainpage .wizardstep .filteroptionlist .specialchk.checked:before {
        position: absolute;
        width: 25px;
        height: 30px;
        content: '';
        background-image: url(../images/icon4.png);
        background-repeat: no-repeat;
        background-position: left;
        background-position-y: center;
        background-size: 19px 16px;
        left: 2px;
        top: -12px;
    }

.mainpage .wizardstep .filteroptionlist li {
    font-family: 'GillSans-Light';
    font-size: 15px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 15px;
    letter-spacing: normal;
    text-align: left;
    color: #4a4a4a;
    padding-left: 10px;
}

.mainpage .wizardstep .singleview .filtersidebar .filterpanel .filteroptionlist > li label {
    padding-left: 5px;
    line-height: 15px;
}

.selectedfilteroptionslist li button:after {
    display: inline-block;
    content: "\00d7";
    font-size: 14px;
    padding-left: 5px;
    vertical-align: middle;
}

.mainpage .wizardstep .filtersidebar .filterpanel .filterpanelcollapse {
    width: 17px;
    height: 17px;
    border-radius: 17px;
}

    .mainpage .wizardstep .filtersidebar .filterpanel .filterpanelcollapse .glyphicon {
        line-height: 17px;
        font-size: 18px;
        height: 17px;
        width: 17px;
        text-align: center;
        position: static;
        display: inherit;
    }

.wid55 {
    width: 55%;
}

.mrgr0 {
    margin-right: 0px !important;
}

.mainpage .wizardstep .social-check .specialchk {
    padding-left: 0px;
    background-size: contain;
    cursor: pointer;
    width: 13px;
    height: 13px;
    border: solid 1px #979797;
    display: block;
    position: relative;
    margin: 9px auto 0px;
}

    .mainpage .wizardstep .social-check .specialchk.checked:before {
        position: absolute;
        width: 25px;
        height: 30px;
        content: '';
        background-image: url(../images/icon4.png);
        background-repeat: no-repeat;
        background-position: left;
        background-position-y: center;
        background-size: 19px 16px;
        left: 0px;
        top: -12px;
    }

.mainpage .wizardstep .wizardstepcontent .achievementsform .institutionselect1 {
    background-image: url(../images/columbia-logo-icon.png);
    background-position-x: left;
    background-position-y: center;
    background-repeat: no-repeat;
    position: absolute;
    width: 17px;
    height: 17px;
    z-index: 1;
    left: 3px;
    top: 3px;
}

.margt20-b26 {
    margin-top: 20px !important;
    margin-bottom: 16px !important;
}

.AddHighlight-Icon, .mainpage .wizardstep .wizardstepcontent.achievements-form .button.addcommunityservice .glyphicon, .mainpage .wizardstep .wizardstepcontent.achievements-form .button.addaccreditation .glyphicon, .mainpage .wizardstep .wizardstepcontent.achievements-form .button.addaffiliation .glyphicon {
    position: absolute;
    top: 3px;
    left: 10px;
    background: url('../images/pluscircle.png') no-repeat;
    width: 16px;
    height: 16px;
}

.Edu-AddHightLight-btn, .mainpage .wizardstep .wizardstepcontent.achievements-form .button.addcommunityservice, .mainpage .wizardstep .wizardstepcontent.achievements-form .button.addaccreditation, .mainpage .wizardstep .wizardstepcontent.achievements-form .button.addaffiliation {
    padding-left: 32px;
    line-height: 23px;
}

.mainpage .wizardstep .filtersidebar .filterpanel a.morelink_filter {
    padding-left: 10px;
    font-family: 'GillSansCE-Roman';
    font-size: 13px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #57307a;
}

.mainpage .wizardstep .filtersidebar .filterpanel .filteroptionlist > li {
    margin-bottom: 5px;
}

.mainpage .wizardstep .personalinfoform .forminput input.textinput {
    font-family: 'GillSans-Light';
    font-size: 15px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 25px;
    letter-spacing: normal;
    text-align: left;
    color: #000000;
    height: 25px;
}

.mainpage .wizardstep .personalinfoform .forminput1 input.textinput {
    font-family: 'GillSans-Light';
    font-size: 15px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 25px;
    letter-spacing: normal;
    text-align: left;
    color: #000000;
    height: 25px;
}

.mainpage .wizardstep .personalinfoform .forminput input.textinputcolor, .mainpage .wizardstep .personalinfoform .forminput1 input.textinputcolor {
    color: #9b9b9b;
}

.margt47 {
    margin-top: 47px !important;
}

.notusa {
    font-family: 'Optima-Regular';
    text-decoration: underline;
    font-size: 12px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #9b9b9b;
}

.mainpage .wizardstep .personalinfoform .forminput1 input.textinput.error {
    border: 1px solid #C0202A;
}

.mainpage .wizardstep .button.size9 {
    padding: 0px 15px 0px 16px !important;
    border-radius: 30px;
    font-family: 'Optima-Regular';
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #410260;
    line-height: 30px;
    height: 32px;
}

.mainpage .wizardstep .button.size10 {
    padding: 0px 32px 0px;
    border-radius: 30px;
    font-family: 'Optima-Regular';
    font-size: 16px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #410260;
    line-height: 30px;
    height: 32px;
}

.mainpage .wizardstep .personalinfoform .forminput .resumevisibilitysquare, .mainpage .wizardstep .skillscontainer .resumevisibilitysquare {
    width: 100%;
    height: 45px;
}

    .mainpage .wizardstep .personalinfoform .forminput .resumevisibilitysquare .strip, .mainpage .wizardstep .skillscontainer .resumevisibilitysquare .strip {
        width: 16px;
        margin: 5px;
        height: 43px;
        display: inline-block;
        position: relative;
        border-radius: 15px;
        background-color: #fff;
        border: 1px solid #AFAFAF;
        position: relative;
        cursor: pointer;
        background-image: url(../images/vertical-chk-strip.png);
        background-size: contain;
        background-repeat: no-repeat;
    }

        .mainpage .wizardstep .personalinfoform .forminput .resumevisibilitysquare .strip .thumb, .mainpage .wizardstep .skillscontainer .resumevisibilitysquare .strip .thumb {
            width: 15px;
            height: 15px;
            background: #57307A;
            -moz-border-radius: 50%;
            -webkit-border-radius: 50%;
            border-radius: 50%;
            display: inline-block;
            -webkit-transition: top 2s linear; /* For Safari 3.1 to 6.0 */
            transition: top 2s linear;
        }

.margt15-b16 {
    margin-top: 15px !important;
    margin-bottom: 16px !important;
}

.mainpage .wizardstep .specialchk13 {
    background-size: contain;
    cursor: pointer;
    width: 13px;
    height: 13px;
    border: solid 1px #979797;
    display: inline-block;
    position: relative;
}

    .mainpage .wizardstep .specialchk13 label {
        cursor: pointer;
    }

    .mainpage .wizardstep .specialchk13.checked {
        position: relative;
    }

        .mainpage .wizardstep .specialchk13.checked:before {
            position: absolute;
            width: 30px;
            height: 30px;
            content: '';
            background-image: url(../images/icon4.png);
            background-repeat: no-repeat;
            background-position: left;
            background-position-y: center;
            background-size: 16px 16px;
            left: 0px;
            top: -12px;
        }

    .mainpage .wizardstep .specialchk13 .hdnchk {
        display: none;
    }

.aminvloved {
    font-family: 'GillSans-Light';
    font-size: 15px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.67;
    letter-spacing: normal;
    text-align: right;
    color: #9b9b9b;
}

.mainpage .wizardstep .wizardstepcontent .affiliationdetailslist.ath {
    margin: 0px;
    padding: 0px;
}

.mainpage .wizardstep .wizardstepcontent ul.affiliationdetailslist.ath {
    list-style: none;
}

.mainpage .wizardstep .wizardstepcontent .affiliationdetailslist.ath > li {
    margin-bottom: 5px;
    padding-left: 10px;
}

    .mainpage .wizardstep .wizardstepcontent .affiliationdetailslist.ath > li:before {
        content: '';
        background: url('../images/blankround.png') no-repeat;
        width: 9px;
        height: 9px;
        position: absolute;
        left: -8px;
        top: 30%;
    }

.mainpage .wizardstep .button.size11 {
    padding: 0px 10px 0px 18px;
    border-radius: 30px;
    font-family: 'Optima-Regular';
    font-size: 16px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #410260;
    line-height: 20px;
    height: 25px;
}

.margt345 {
    margin-top: 345px !important;
}

.mainpage .wizardstep .button.size12 {
    padding: 0px 32px 0px;
    border-radius: 30px;
    font-family: 'Optima-Regular';
    font-size: 18px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #410260;
    line-height: 30px;
    height: 32px;
}

.buttonbox {
    height: 25px;
}

    .buttonbox .caret {
        position: absolute;
        right: 5px;
        top: 8px;
        border-left: 7px solid transparent;
        border-right: 7px solid transparent;
        border-top: 10px dashed;
        color: #9b9b9b;
    }

/*Drop Box Start*/
.dropdowndiv ul {
    max-height: 213px;
    overflow-y: auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.dropdowndiv li {
    display: none;
}

.dropdowndiv .loadMore {
    cursor: pointer;
    margin-left: 10px;
}

    .dropdowndiv .loadMore:hover {
        color: black;
    }

.dropdowndiv .showLess {
    cursor: pointer;
    display: none;
    margin-left: 10px;
}

.btn-group.open .dropdown-toggle.selectinput {
    box-shadow: 0 0px 0px rgba(0, 0, 0, 0.125) inset;
}

.mainpage .wizardstep .dropdown-toggle.selectinput {
    background: #fff url("../images/RW_select-arrow-b.jpg") no-repeat scroll 100% center / 25px 25px;
    border: 1px solid #9b9b9b !important;
    color: #9b9b9b;
    display: block;
    font-family: "GillSans-Light";
    font-size: 15px;
    font-weight: 100 !important;
    position: relative;
    height: 25px !important;
    line-height: 15px;
    text-align: left;
}

.dropdown-menu.checkboxlist {
    box-shadow: 0 0px 0px rgba(0, 0, 0, 0.176);
    border-radius: 0px;
    background-color: #FAFAFA;
    padding: 0px;
    border-top: 0px;
    margin-top: 0px;
    border: solid 1px #979797; /*601 BV,4/12/2018*/
}

    .dropdown-menu.checkboxlist .inputplussign .input-group-addon {
        right: 2px;
        border-left: 1px solid #979797;
        border-right: 0px solid #979797;
    }

.mainpage .wizardstep .wizardstepcontent .achievementsform .dropdown-menu.checkboxlist .textinput, .mainpage .wizardstep .wizardstepcontent .achievementsform .dropdown-menu.checkboxlist .textinput:focus {
    border-left: 0px !important;
    border-right: 0px !important;
    margin-bottom: 0px;
}

.dropdown-menu.checkboxlist .donebtn {
    background: #fff;
    padding: 0px 0px;
    text-align: center;
}

    .dropdown-menu.checkboxlist .donebtn .addaccreditation {
        border: 0px !important;
        line-height: 25px;
        height: 25px;
    }

.mCSB_container li {
    list-style: none;
}

.mainpage .wizardstep .wizardstepcontent .achievementsform .checkboxlist.dropdowndiv label {
    position: relative;
    font-family: 'GillSans-Light';
    font-size: 18px;
    letter-spacing: normal;
}

.mainpage .wizardstep .wizardstepcontent .achievementsform .checkboxlist.font15.dropdowndiv label {
    position: relative;
    font-family: 'GillSans-Light';
    font-size: 15px;
    letter-spacing: normal;
    line-height: 15px;
}

.mainpage .wizardstep .wizardstepcontent .achievementsform .checkboxlist.dropdowndiv ul li {
    position: relative;
    padding-left: 20px;
    margin-left: 5px;
}

.mainpage .wizardstep .wizardstepcontent .achievementsform .checkboxlist.dropdowndiv .specialchk {
    position: absolute;
    left: 0px;
    top: 8px;
}

.mainpage .wizardstep .wizardstepcontent .achievementsform .checkboxlist.dropdowndiv .more {
    margin-left: 5px;
    color: #57307a;
    font-size: 15px;
    line-height: 18px;
    letter-spacing: normal;
    margin-bottom: 10px;
}

/*Drop Box End*/

.mainpage .wizardstep .wizardstepcontent .wid42 {
    width: 42% !important;
}

.mainpage .wizardstep .wizardstepcontent .achievementformmonthhint2 {
    border: 5px solid #57307A;
    text-align: center;
    padding: 10px;
    font-family: 'GillSans';
    font-size: 14px;
    line-height: 16px;
    background-color: #F9F9F9;
    width: 119px;
    height: 119px;
    z-index: 1;
    position: absolute;
    top: 10px;
    color: #4a4a4a;
}

.mainpage .wizardstep .wizardstepcontent .achievementformmonthhint2 {
    top: 135px;
    right: 85px;
}

.mainpage .wizardstep .wizardstepcontent .achievementformyearshint2 {
    border: 3px solid #4E72B3;
    text-align: center;
    padding: 5px;
    padding-top: 20px;
    font-family: 'GillSans';
    font-size: 14px;
    background-color: #F9F9F9;
    width: 133px;
    height: 133px;
    border-radius: 50%;
    z-index: 1;
    position: absolute;
    top: 130px;
    color: #4a4a4a;
}

.mainpage .wizardstep .wizardstepcontent .achievementformyearshint2 {
    top: 35px;
    right: -40px;
}

    .mainpage .wizardstep .wizardstepcontent .achievementformyearshint2 .close {
        left: 45%;
        position: absolute;
        top: 5px;
    }

.mainpage .wizardstep .wizardstepcontent .achievementformlevelhint2 {
    position: absolute;
    border: 0px;
    padding: 0px;
    right: -220px;
    bottom: -120px;
    z-index: 1;
    background-color: transparent;
    background-image: url(../images/Level-Hint.png);
    background-position: center;
    background-repeat: no-repeat;
    width: 212px;
    height: 221px;
}

.mainpage .wizardstep .wizardstepcontent .achievementformlevelhint2 {
    width: 190px;
    right: auto;
    right: -90px;
    top: 128px;
    bottom: auto;
    background-size: contain;
    z-index: 2;
}

    .mainpage .wizardstep .wizardstepcontent .achievementformlevelhint2 .close {
        position: absolute;
        top: 45px;
        left: 50%;
        margin-left: -10px;
    }

.mainpage .wizardstep .wizardstepcontent .achievementformguidehint2 {
    border: 3px solid #4E72B3;
    text-align: center;
    padding: 5px;
    padding-top: 40px;
    font-family: 'GillSansCE-Roman';
    font-size: 13px;
    background-color: #F9F9F9;
    width: 229px;
    height: 229px;
    border-radius: 50%;
    z-index: 1;
    position: absolute;
    top: 130px;
    color: #4a4a4a;
}

.mainpage .wizardstep .wizardstepcontent .achievementformguidehint2 {
    top: 315px;
    right: -15px;
}

    .mainpage .wizardstep .wizardstepcontent .achievementformguidehint2 .close {
        left: 40%;
        position: absolute;
        top: 15px;
    }

.margt40 {
    margin-top: 40px !important;
}

.margt30 {
    margin-top: 40px !important;
}

.recentgrade {
    font-family: 'GillSans-Light';
    font-size: 12px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #9b9b9b;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .achievementsform .inputlabel.chaticonab {
    margin-bottom: 0px;
}

.margt12 {
    margin-top: 12px !important;
}

.margtm20 {
    margin-top: -20px;
}

.List-icon {
    background-image: url(../images/list-icon.png);
    background-repeat: no-repeat;
    background-position-x: 0px;
    background-position-y: center;
    padding-left: 20px;
}

.template-carousel {
    position: relative;
}

    .template-carousel .control {
        position: absolute;
        right: -1px;
        bottom: -1px;
        border-top: solid 1px #979797;
        border-left: solid 1px #979797;
        height: 33px;
        width: 63px;
        background: #fff;
    }

        .template-carousel .control .innercontrol {
            border: solid 1px #979797;
            height: 30px;
            width: 60px;
            margin-top: 2px;
            margin-left: 2px;
            background: #EEEEEE;
            line-height: 25px;
            text-align: center;
        }

            .template-carousel .control .innercontrol .exploreicon {
                background-image: url(../images/explore-icon.png);
                background-repeat: no-repeat;
                height: 20px;
                width: 20px;
                display: inline-block;
                vertical-align: middle;
            }

            .template-carousel .control .innercontrol .moreicon {
                background-image: url(../images/more-icon.png);
                background-repeat: no-repeat;
                height: 20px;
                width: 23px;
                display: inline-block;
                vertical-align: middle;
            }

.mainpage .wizardstep .filtersidebar .filterpanel select {
    padding-right: 30px;
}

.mainpage .wizardstep .personalinfoform .forminput .resumevisibilitysquare .strip.graybg {
    width: 16px;
    margin: 5px;
    margin-top: 10px;
    margin-left: 10px;
    height: 43px;
    display: inline-block;
    position: relative;
    border-radius: 15px;
    background-color: #D8D8D8;
    border: 1px solid #AFAFAF;
    position: relative;
    cursor: pointer;
    background-image: url(../images/vertical-chk-strip.png);
    background-size: contain;
    background-repeat: no-repeat;
}

.selectfricon {
    background-image: url('../images/fr-lang-icon.png');
    background-repeat: no-repeat;
    position: absolute;
    width: 31px;
    height: 29px;
    z-index: 2;
    left: 3px;
    top: 5px;
}

.switch .dotleft {
    position: absolute;
    z-index: 1;
    left: 6px;
    width: 5px;
    height: 5px;
    background: #57307A;
    border-radius: 5px;
    top: 40%;
}

.switch .dotright {
    position: absolute;
    z-index: 1;
    right: 6px;
    width: 5px;
    height: 5px;
    background: #57307A;
    border-radius: 5px;
    top: 40%;
}

.mainpage .wizardstep .inputlabelsimibold {
    font-size: 14px;
    font-family: 'GillSans-Bold';
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #410260;
}

.mainpage .wizardstep .specialchk.specialchk15 {
    padding-left: 0px;
    background-size: contain;
    cursor: pointer;
    width: 13px;
    height: 13px;
    border: solid 1px #979797;
    display: inline-block;
    position: relative;
}

    .mainpage .wizardstep .specialchk.specialchk15 label {
        cursor: pointer;
    }

    .mainpage .wizardstep .specialchk.specialchk15.checked {
        padding-left: 0px;
        position: relative;
    }

        .mainpage .wizardstep .specialchk.specialchk15.checked:before {
            position: absolute;
            width: 16px;
            height: 16px;
            content: '';
            background-image: url(../images/icon4.png);
            background-repeat: no-repeat;
            background-position: left;
            background-position-y: center;
            background-size: 15px 15px;
            left: 2px;
            top: -5px;
        }

    .mainpage .wizardstep .specialchk.specialchk15 .hdnchk {
        display: none;
    }

.includeresume_text {
    display: inline-block;
    font-family: 'GillSans-Light';
    font-size: 15px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #000000;
    padding-left: 20px;
}

    .includeresume_text .specialchk15 {
        position: absolute !important;
    }

    .includeresume_text span {
        padding-left: 20px;
    }

.bluetextinput3 {
    border: solid 1px #4a4a4a !important;
    font-family: 'GillSans' !important;
    font-size: 14px !important;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #9b9b9b !important;
}

.margt273 {
    padding-top: 273px !important;
}

.hint_skillguideline {
    background-color: #fafafa;
    border: solid 3px #57307a;
    width: 205px;
    height: 190px;
    padding: 10px;
    position: absolute;
    top: -40px;
    left: 130px;
    font-family: "GillSansCE-Roman";
    font-size: 13px;
    font-stretch: normal;
    font-style: normal;
    font-weight: normal;
    letter-spacing: normal;
    line-height: 14px;
    color: #6d6d6d;
    text-align: left;
}

    .hint_skillguideline .close {
        font-size: 18px !important;
        position: absolute;
        right: 5px;
        top: 5px;
    }

    .hint_skillguideline .more {
        font-family: 'GillSansCE-Roman';
        font-size: 13px;
        font-weight: normal;
        font-style: normal;
        font-stretch: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #57307a;
    }

.resumelist {
    padding: 38px 6px 0px;
}

.newresume_heading {
    background: url(../images/new-resume-plus.png) no-repeat left center;
    padding: 7px 0px 7px 44px;
    font-family: 'GillSans';
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 28px;
    letter-spacing: normal;
    text-align: left;
    color: #410260;
    text-decoration: none !important;
}

.resumelist .resumetitle {
    font-family: 'GillSans';
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #410260;
}

    .resumelist .resumetitle a {
        font-family: 'GillSans';
        font-size: 14px;
        color: #410260;
        text-decoration: none !important;
    }

.resumelist .resumetemplate {
    font-family: 'GillSans-SemiBold';
    font-size: 12px;
    font-weight: 600;
    font-style: normal;
    font-stretch: normal;
    line-height: 16px;
    letter-spacing: normal;
    text-align: left;
    color: #9b9b9b;
}

.resumelist .resumedate {
    font-family: 'GillSans-SemiBold';
    font-size: 12px;
    font-weight: 600;
    font-style: normal;
    font-stretch: normal;
    line-height: 16px;
    letter-spacing: normal;
    text-align: left;
    color: #9b9b9b;
}

.resumelist .editicon {
    background: url(../images/edit-pen-14.png) no-repeat;
    width: 14px;
    height: 14px;
    margin-left: 8px;
    margin-top: 1px;
}

.marg18 {
    margin-top: 18px;
}

.resumelist .resumerow {
    margin-bottom: 19px;
}

.resumelist .line {
    border-bottom: solid 1px #979797;
    height: 1px;
    max-width: 403px;
    margin-left: 3%;
}

.resumelist .smalldesc {
    display: block;
    font-family: 'GillSans';
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #9b9b9b;
}

.listcontrol {
    margin-top: 560px;
    text-align: left;
}

    .listcontrol ul {
        ;
        list-style: none;
        margin: 0px;
        padding: 0px;
        font-family: 'GillSans-SemiBold';
        font-size: 12px;
        font-weight: 600;
        font-style: normal;
        font-stretch: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
    }

        .listcontrol ul li {
            width: 55px;
            display: inline-block;
            margin-right: 10px;
            text-align: center;
        }

.beta-testing-iconbottom {
    position: absolute;
    right: 50px;
    bottom: 50px;
}

.asterisk10x10 {
    background-image: url('../images/asteriskb-10x10.png');
    background-repeat: no-repeat;
    position: absolute;
    width: 10px;
    height: 10px;
    z-index: 2;
    left: 3px;
    top: 8px;
}

.padl12 {
    padding-left: 12px;
}

.aminvloved12 {
    font-family: 'GillSans-Light';
    font-size: 12px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 13px;
    letter-spacing: normal;
    text-align: right;
    color: #9b9b9b;
}

.textautoheight {
    overflow: hidden;
    padding: 0;
    outline: none;
    resize: none;
    line-height: 25px;
    height: 25px;
}

.researchdetail .icons {
    position: absolute;
    z-index: 1;
    right: 35px;
    top: 3px;
}

    .researchdetail .icons .close {
        font-size: 18px !important;
    }

.researchdetailitem {
    border: 1px solid #adadad;
    font-family: 'GillSans-Light';
    color: #9b9b9b;
    font-size: 15px;
    text-transform: none;
    padding: 0px 50px 0px 5px;
    padding-left: 10px;
    width: calc(100% - 35px);
    display: inline-block;
    margin-right: 5px;
    background: #fff;
    font-weight: 300;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    letter-spacing: normal;
    line-height: 25px;
    height: 25px;
}

.redtext {
    color: #c01f2a !important;
}

.margt206 {
    margin-top: 206px;
}

.margt26 {
    margin-top: 26px;
}

.trianglebox2 {
    width: 0;
    height: 0;
    border-left: 110px solid transparent;
    border-right: 110px solid transparent;
    border-bottom: 188px solid #c01f2a;
    position: absolute;
    z-index: 1;
    top: -0px;
    left: -40px;
}

    .trianglebox2 .inner-triangle {
        position: relative;
        top: 6px;
        left: -100px;
        width: 0;
        height: 0;
        border-left: 100px solid transparent;
        border-right: 100px solid transparent;
        border-bottom: 178px solid #fff;
    }

        .trianglebox2 .inner-triangle .text {
            width: 120px;
            position: absolute;
            top: 40px;
            left: -60px;
            font-family: 'GillSansCE-Roman';
            font-size: 13px;
            text-align: center;
            color: #000000;
            font-weight: normal;
            font-style: normal;
            font-stretch: normal;
            line-height: 15px;
            letter-spacing: normal;
        }

.trianglebox3 {
    width: 0;
    height: 0;
    border-left: 110px solid transparent;
    border-right: 110px solid transparent;
    border-bottom: 188px solid #c01f2a;
    position: absolute;
    z-index: 1;
    top: -0px;
    left: 10px;
}

    .trianglebox3 .inner-triangle {
        position: relative;
        top: 6px;
        left: -100px;
        width: 0;
        height: 0;
        border-left: 100px solid transparent;
        border-right: 100px solid transparent;
        border-bottom: 178px solid #fff;
    }

        .trianglebox3 .inner-triangle .text {
            width: 120px;
            position: absolute;
            top: 40px;
            left: -60px;
            font-family: 'GillSansCE-Roman';
            font-size: 13px;
            text-align: center;
            color: #000000;
            font-weight: normal;
            font-style: normal;
            font-stretch: normal;
            line-height: 15px;
            letter-spacing: normal;
        }

.translation, .translation-max {
    position: relative;
    width: 100%;
}

.source, .target {
    width: calc(100% - 35px);
    min-height: 25px;
    line-height: 20px;
    word-break: break-all;
    word-wrap: break-word;
    padding: 0px 50px 0px 5px;
    font-family: 'GillSans-Light' !important;
    color: #9b9b9b;
    font-size: 15px;
}

.source {
    margin-bottom: 6px;
}

.target {
    -webkit-appearance: none;
    overflow: hidden;
    white-space: pre-wrap;
    resize: none;
    background-color: #fff;
    border: 1px solid #979797; /* border-radius: 3px;*/
    color: #9b9b9b !important;
}

    .target:focus {
        border-color: #888;
        outline: none;
    }

.maxlength {
    position: absolute;
    right: 2px;
    bottom: 6px;
    padding: 2px 6px;
    font-size: 13px;
    color: #999;
}

.target .warning {
    background-color: #fdd;
}

.warning.maxlength {
    color: #f77;
    background-color: inherit;
}

.match {
    color: #09f;
}

.hoge {
    color: #4c9;
}

.fuga {
    color: #ff9d00;
}

.matchHighlight {
    background-color: #09f;
    opacity: .9;
}

.hogeHighlight {
    border-bottom: 2px solid #4c9;
    opacity: .6;
}

.fugaHighlight {
    background-color: #ff9d00;
    opacity: .6;
}

.tagsHighlight {
    background-color: #99f;
    opacity: .6;
}

.match.added, .hoge.added, .fuga.added {
    color: #999;
}

.mainpage .wizardstep .wizardtemplatesingleview .templatebigpreview {
    width: 585.3px;
    cursor: pointer;
}

    .mainpage .wizardstep .wizardtemplatesingleview .templatebigpreview.bordertemp {
        border: 3px solid #57307A;
    }
/*2018/04/26*/
.padt8-b14 {
    padding: 8px 0px 14px;
}

.whatwill {
    background: url('/images/briefcase-icon.png') no-repeat left;
    font-family: 'GillSans-Bold';
    font-size: 14px;
    font-weight: bold;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #410260;
    line-height: 19px;
    padding-top: 7px;
    padding-left: 35px;
}

.swapbox {
    background: #ecf0f5;
    width: 100%;
    padding: 11px;
    position: relative;
}

.mart10-b37 {
    margin: 10px 0px 37px;
}

.swapbox .title {
    font-family: 'GillSansCE-Roman';
    font-size: 15px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #57307a;
    border-bottom: solid 1px #57307a;
    padding-bottom: 1px;
    padding-top: 8px;
}

.mainpage .wizardstep .specialchk.specialchk21 {
    padding-left: 0px !important;
    background-size: contain;
    cursor: pointer;
    width: 21px;
    height: 21px;
    border: solid 1px #979797;
    background: #fff;
    display: inline-block;
    position: relative;
}

    .mainpage .wizardstep .specialchk.specialchk21 .hdnchk {
        display: none;
    }

    .mainpage .wizardstep .specialchk.specialchk21.checked:before {
        position: absolute;
        width: 22px;
        height: 22px;
        content: '';
        background-image: url(/images/check-mark.png);
        background-repeat: no-repeat;
        background-position: left;
        background-position-y: center;
        background-size: 22px 22px;
        left: 0px;
        top: -5px;
    }

.swapbox .title .checkarea {
    position: absolute;
    right: 11px;
    top: 14px;
}

.swapbox .personalinfo {
    padding: 15px 0px 0px;
}

    .swapbox .personalinfo p {
        font-family: 'GillSans-Light';
        font-size: 15px;
        font-weight: 300;
        font-style: normal;
        font-stretch: normal;
        letter-spacing: normal;
        text-align: left;
        color: #9b9b9b;
        margin-bottom: 0px;
    }

        .swapbox .personalinfo p strong {
            color: #000;
            font-family: 'GillSans-SemiBold';
        }

.subtitle {
    font-family: 'GillSans-SemiBold';
    font-size: 15px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #57307a;
    border-bottom: solid 2px #57307a;
    padding-bottom: 1px;
    padding-top: 8px;
}

.mart16-b16 {
    margin: 16px 0px 16px;
}

.swapbox .title span.arrow {
    background-image: url(/images/arrow-down.png);
    background-repeat: no-repeat;
    background-position: right;
    background-position-y: center;
    padding-right: 15px;
}

.swapbox .flipfly {
    position: absolute;
    top: 0px;
}

.swapbox .listing {
    padding-top: 25px;
    padding-bottom: 19px;
    padding-left: 14px;
}

    .swapbox .listing ul {
        margin: 0px;
        padding: 0px;
        list-style: none;
        width: 100%;
    }

        .swapbox .listing ul li {
            margin-bottom: 6px;
            position: relative;
            border: 1px solid #adadad;
            min-height: 25px;
            line-height: 25px;
            font-family: 'GillSans-Light';
            font-size: 15px;
            font-weight: 300;
            font-style: normal;
            font-stretch: normal;
            letter-spacing: normal;
            text-align: left;
            color: #9b9b9b;
            background: #fff;
            padding-left: 25px;
        }

.mainpage .wizardstep .specialchk.specialchk13 {
    padding-left: 0px !important;
    background-size: contain;
    cursor: pointer;
    width: 13px;
    height: 13px;
    border: solid 1px #979797;
    background: #fff;
    display: inline-block;
    position: relative;
}

    .mainpage .wizardstep .specialchk.specialchk13 .hdnchk {
        display: none;
    }

    .mainpage .wizardstep .specialchk.specialchk13.checked:before {
        position: absolute;
        width: 15px;
        height: 15px;
        content: '';
        background-image: url(/images/check-mark.png);
        background-repeat: no-repeat;
        background-position: left;
        background-position-y: center;
        background-size: 15px 15px;
        left: 0px;
        top: -5px;
    }

.swapbox .listing ul li .checkarea {
    position: absolute;
    right: 5px;
    top: 3px;
}

.swapbox .listing ul li strong {
    font-family: 'GillSans-SemiBold';
}

.scrollerbox .mCS-minimal-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
    background-color: #550C91 !important;
    background-color: rgba(84,11,145,1) !important;
    filter: "alpha(opacity=20)";
    -ms-filter: "alpha(opacity=20)";
}

.scrollerbox .mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
    width: 17px !important;
}

.scrollerbox .mCustomScrollbar {
    width: 97%;
}

.scrollerbox .mCSB_outside + .mCS-minimal.mCSB_scrollTools_vertical, .scrollerbox .mCSB_outside + .mCS-minimal-dark.mCSB_scrollTools_vertical {
    right: -25px !important;
}

@media (max-width: 768) {
    .beta-testing-icon {
        display: none;
    }
}

@media (max-width: 500px) {
    .mainpage .wizardstep .choosetemplate .resumename {
        width: 90%;
        margin: 0 auto;
    }

    .mainpage .wizardstep .wizardstepcontent.choosetemplate .wid321 {
        width: 90% !important;
        height: 30px !important;
    }
}
/*Work From Venkat End*/



/*ST-717 START*/
.wizardtemplatesingleview .slick-prev {
    display: inline-block;
    background: url(../images/BigpointerLeft.png) top center no-repeat;
    left: -59px;
    height: 85px;
    width: 45px;
    border: 0;
    position: absolute;
    top: 400px;
    outline: 0;
    margin-top: -100px;
    height: 117px;
    /*{left: 15px;}*/
}

.wizardtemplatesingleview .slick-next {
    display: inline-block;
    background: url(../images/Bigpointerright.png) top center no-repeat;
    right: -59px;
    height: 85px;
    width: 45px;
    border: 0;
    position: absolute;
    top: 400px;
    outline: 0;
    margin-top: -100px;
    height: 117px;
    /*background-size: 60% 80% !important;
    background-color: transparent !important;*/
}

.mainpage .wizardstep .filtersidebar #chosenfilters {
    max-height: 200px;
    overflow-y: auto;
}

.mainpage .wizardstep .wizardtemplatesingleview .templatebigpreview {
    padding: 0px;
}
/*ST-717 END*/
/*ST-718 START*/
.slick-GridView .slick-prev {
    display: inline-block;
    background: url(../images/BigpointerLeft.png) top center no-repeat;
    left: -59px;
    height: 85px;
    width: 45px;
    border: 0;
    position: absolute;
    top: 400px;
    outline: 0;
    margin-top: -100px;
    height: 117px;
}

.slick-GridView .slick-next {
    display: inline-block;
    background: url(../images/Bigpointerright.png) top center no-repeat;
    right: -59px;
    height: 85px;
    width: 45px;
    border: 0;
    position: absolute;
    top: 400px;
    outline: 0;
    margin-top: -100px;
    height: 117px;
}

.Select-Img-Template {
    border: solid 3px #57307a !important;
}
/*ST-718 START*/

/*ST-717,718 - 03/28/2018,04/01/2018*/
.Single-View-Image {
    width: 604px;
    height: 781px;
}

.Grid-View-Image-Parent {
    height: 362px;
    width: 280px;
}

.slick-GridView .slick-track .slick-slide {
    outline: none;
}

.singleview {
    margin-left: 14px;
}
/*Work From Venkat End*/

/* VIP 722*/
.skillSummaryHint {
    border: 5px solid #57307A;
    text-align: left;
    padding: 10px;
    font-family: GillSansCE-Roman;
    font-size: 14px;
    background-color: #F9F9F9;
    width: 250px;
    z-index: 1;
    position: absolute;
    left: 25%;
    top: 750px;
}

.mainpage .wizardstep .wizardstepcontent .objectiveAndSkillSUmmaryForm {
    background-color: #F8FBFD;
    padding: 5px;
    /*margin-top: 25px;*/
    /*max-width: 70%;*/
    position: relative;
}
/* VIP 722*/

/*VIP 725*/
.backgroundColor {
    background-color: #F8FBFD;
    padding: 5px;
    position: relative;
}

.chkHeaderSpan {
    top: 5px;
    position: relative;
}

.margin-top-7 {
    margin-top: 7px !important;
}

.margin-top-20 {
    margin-top: 20px !important;
}

.margin-top-60 {
    margin-top: 50px !important;
}

.margin-right-5 {
    margin-right: 5px !important;
}

.mainpage .wizardstep .wizardstepcontent.achievements-form .achievementsform .inputlabelSmall {
    font-family: "GillSans";
    font-size: 12px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 16px;
    letter-spacing: normal;
    text-align: left;
    color: #410260;
}

.downArrow-10 {
    height: 10px;
    display: inline;
}

.divBottomBorder {
    border-bottom: solid 1px #57307a;
    margin-bottom: 10px;
}

.divThickBottomBorder {
    border-bottom: solid 3px #57307a;
    margin-bottom: 10px;
}

.cloudArrowSmall {
    height: 30px;
    width: 30px;
    margin-top: -10px;
}
/*Off By Chhanda #1219 4/30/18*/
/*.cloudArrowLarge {
    position: relative;
    top: 275px;
    z-index: 1000;
    left: -25px;
    width:0px;
    height:0px;
}*/

/*Add By Chhanda #1219 4/30/18*/
.cloudArrowLarge {
    position: absolute;
    /*top: -350px;*/
    top: 200px;
    z-index: 1000;
    /*left: 398px;*/
    left: 395px;
    width: 0px;
    height: 0px;
}

/*::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    box-shadow: inset 0 0 3px grey;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: blue;
    border-radius: 10px;
}

    ::-webkit-scrollbar-thumb:hover {
        background: #b30000;
    }*/

.iFrameWrapper {
    width: 100% !important;
    height: 650px;
    padding: 0;
    overflow: hidden;
}

/*Off By Chhanda #1219 4/30/18*/
/*.scaled-frame {
    width: 1135px;
    height: 1330px;
    border: 0px;
    padding-left:20px;
}

.scaled-frame {
    zoom: 0.70;
    -moz-transform: scale(0.70);
    -moz-transform-origin: 0 0;
    -o-transform: scale(0.70);
    -o-transform-origin: 0 0;
    -webkit-transform: scale(0.70);
    -webkit-transform-origin: 0 0;
}*/

/*Add By Chhanda #1219 4/30/18*/
.scaled-frame {
    width: 1080px;
    height: 1340px;
    border: 0px;
    padding-left: 20px;
}

.scaled-frame {
    zoom: 0.0;
    -moz-transform: scale(0.40);
    -moz-transform-origin: 0 0;
    -o-transform: scale(0.40);
    -o-transform-origin: 0 0;
    -webkit-transform: scale(0.40);
    -webkit-transform-origin: 0 0;
}



.fontSize-11 {
    font-size: 11px !important;
}
/*@media screen and (-webkit-min-device-pixel-ratio:0) {
    .scaled-frame {
        zoom: 1;
    }
}*/

/*VIP 725*/


/*Chhanda #1177 4/6/2018*/
.Triangle {
    width: 190px;
    height: 181px;
    background-color: #ffffff;
    border: solid 3px #c01f2a;
}

.We-recomend-that-you {
    width: 109.2px;
    height: 75px;
    font-family: GillSansCE-Roman;
    font-size: 13px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #000000;
}

.Rectangle-9 {
    width: 68.9px;
    height: 22.2px;
    border-radius: 49px;
    border: solid 0.3px #000000;
}

.textautoheight {
    overflow: hidden;
    padding: 0;
    outline: none;
    resize: none;
    line-height: 20px;
    height: 20px;
    padding: 0px 5px;
}

/*Chhanda #1177 4/6/2018*/
/*BV,2018 04 09*/
.div-ChooseSkillI-btn {
    background-color: #f8fbfd;
    border: 1px solid #dfeaf7;
}

.div-ChooseSkillI-btn-child {
    border: none !important;
}

.div-ChooseSkillI-btn-Select {
    margin: 15px;
}

.row-margTop80 {
    margin-top: 80px;
}

.Fafa-Highlight {
    border: solid 2px #655d82 !important;
    background: #d6d3e1 !important;
}

.Fafa-Highlight-border {
    border: solid 2px #655d82 !important;
}
/*ST-1202*/
img.media-object {
    height: 18px;
}

.chkWithBtn-Done {
    border-top: solid 1px #979797;
}
/*BV,4/13/2018*/
#nestable .categoryorder {
    margin-right: 0px !important;
}

.AddNew-CatBtn {
    height: 26px !important;
    padding-left: 32px;
    line-height: 23px;
    margin-top: 25px;
    color: black !important;
}

.DDL-DownArrow {
    padding-right: 30px;
}

.dd3-item > div > span {
    font-weight: 700;
}

.dd3-item > div {
    margin-top: 10px;
}

.bootstrap-select.btn-group .btn .filter-option {
    width: 92% !important;
}


/*SHV Task:1195 */

.p {
    font-size: inherit !important;
}

.submit_flip {
    border: 1px solid #57307a;
    color: #6b448d;
    border-radius: 20px;
    text-transform: uppercase;
    padding: 5px 40px;
    text-decoration: none;
    display: inline-block;
    font-size: 15px;
    margin: 0 7px 5px;
    transition: all ease-out 0.5s;
    -webkit-transition: all ease-out 0.5s;
    -moz-transition: all ease-out 0.5s;
}

.modal {
    padding: 0;
    border-radius: 0;
    background-color: transparent;
    max-width: 100%;
    width: 100% !important;
    top: 0 !important;
    -webkit-transform: translate(0) !important;
    -moz-transform: translate(0) !important;
    -o-transform: translate(0) !important;
    -ms-transform: translate(0) !important;
    transform: translate(0) !important;
    margin: 0 auto;
}

.modal-dialog {
    top: 7%;
    width: 430px;
}

.modal-content {
    border: 0;
    border-radius: 0;
    text-align: center;
    box-shadow: none;
}

.modal-body {
    border: 2px solid #410166;
}

.modal-content h1 {
    margin: 15px 0 20px;
    font-family: 'GillSans';
    color: #4a4a4a;
    font-size: 25px;
    position: relative;
    padding-left: 35px;
    display: inline-block;
    text-transform: uppercase;
}

/*BV,ST-1162 START*/
.paddedRadioGrp-skillSet {
    padding: 5px 0px;
    position: relative;
    text-align: center;
    width: 25px;
}

.radiolistitemlang.SkillSet-ChkGrpUl {
    width: 90% !important;
    margin: 0px 5% !important;
}

.SkillSet-Level-Main {
    width: 175px;
    height: auto;
    background-color: #fafafa;
    border: solid 1px #e3e3e3;
}

.SkillSet-Level-Info {
    border-top: solid 1px #e3e3e3;
}

.SkillSet-Level-InfoUnder {
    padding-top: 5px;
}

.div-Edu-AddHighLight {
    z-index: 9;
}

.Skill-Grp-Main {
    margin-bottom: 15px;
    padding-right: 19px;
    padding-left: 0px;
}

.YResume_Modal .modal-content h1.Skill-popup-Header {
    font-family: GillSans;
    font-size: 20px;
    color: #410260;
}

#divSelectSkill .modal-content {
    width: 700px;
    margin-top: 15%;
}

#divSelectSkill .modal-body {
    padding-top: 0px;
    padding-left: 46px;
}

.Inv-SkillLevel-Main {
    float: right;
    margin-right: 15px;
}

.span-skill-Icon {
    width: 15px;
    height: 15px;
    margin-right: 5px;
    position: absolute;
    top: 6px;
    right: 23%;
}

.Skill-Round-Basic {
    border: solid 1px #57307a;
    background-color: #d6d3e1;
    border-radius: 50%;
}

.Skill-Round-Familiar {
    border: solid 1px #57307a;
    background-color: #b9a9c9;
    border-radius: 50%;
}

.Skill-Round-Proficient {
    border: solid 1px #57307a;
    background-color: #9b80b0;
    border-radius: 50%;
}

.Skill-Round-Advanced {
    border: solid 1px #57307a;
    background-color: #7c5697;
    border-radius: 50%;
}

.Skill-Round-Expert {
    border: solid 1px #57307a;
    background-color: #57307a;
    border-radius: 50%;
}

.Skill-Name {
    height: 12px;
    font-family: GillSans;
    color: #9b9b9b;
    font-size: 10px;
    font-weight: 600;
    position: absolute;
    left: 77.5%;
}

#divSelectSkill #btnSaveSkill {
    padding-left: 5px;
    padding-right: 20px;
    margin-right: 10px;
}

.SkillSet-ChkGrpUl [type="radio"].ChkSkillBasic:checked + label:after, .SkillSet-ChkGrpUl [type="radio"].ChkSkillBasic:not(:checked) + label:after {
    background: #d6d3e1 !important;
}

.SkillSet-ChkGrpUl [type="radio"].ChkSkillBasic:checked + label::after, .SkillSet-ChkGrpUl [type="radio"].ChkSkillBasic:not(:checked) + label::after {
    height: 18px;
    width: 18px;
    top: 1px;
    left: 1px;
}

.SkillSet-ChkGrpUl [type="radio"].ChkSkillFamilar:checked + label:after, .SkillSet-ChkGrpUl [type="radio"].ChkSkillFamilar:not(:checked) + label:after {
    background: #b9a9c9 !important;
}

.SkillSet-ChkGrpUl [type="radio"].ChkSkillFamilar:checked + label::after, .SkillSet-ChkGrpUl [type="radio"].ChkSkillFamilar:not(:checked) + label::after {
    height: 18px;
    width: 18px;
    top: 1px;
    left: 1px;
}

.SkillSet-ChkGrpUl [type="radio"].ChkSkillProficent:checked + label:after, .SkillSet-ChkGrpUl [type="radio"].ChkSkillProficent:not(:checked) + label:after {
    background: #9b80b0 !important;
}

.SkillSet-ChkGrpUl [type="radio"].ChkSkillProficent:checked + label::after, .SkillSet-ChkGrpUl [type="radio"].ChkSkillProficent:not(:checked) + label::after {
    height: 18px;
    width: 18px;
    top: 1px;
    left: 1px;
}

.SkillSet-ChkGrpUl [type="radio"].ChkSkillAdvanced:checked + label:after, .SkillSet-ChkGrpUl [type="radio"].ChkSkillAdvanced:not(:checked) + label:after {
    background: #7c5697 !important;
}

.SkillSet-ChkGrpUl [type="radio"].ChkSkillAdvanced:checked + label::after, .SkillSet-ChkGrpUl [type="radio"].ChkSkillAdvanced:not(:checked) + label::after {
    height: 18px;
    width: 18px;
    top: 1px;
    left: 1px;
}

.SkillSet-ChkGrpUl [type="radio"].ChkSkillExpert:checked + label:after, .SkillSet-ChkGrpUl [type="radio"].ChkSkillExpert:not(:checked) + label:after {
    background: #57307a !important;
    ;
}

.SkillSet-ChkGrpUl [type="radio"].ChkSkillExpert:checked + label::after, .SkillSet-ChkGrpUl [type="radio"].ChkSkillExpert:not(:checked) + label::after {
    height: 18px;
    width: 18px;
    top: 1px;
    left: 1px;
}

.radiolistitemlang.SkillSet-ChkGrpUl [type="radio"]:checked + label::before, .radiolistitemlang.SkillSet-ChkGrpUl [type="radio"]:not(:checked) + label::before {
    left: 0px;
    top: 0px;
}

.SkillSet-Level-Heding {
    font-family: 'GillSans-Light' !important;
    font-size: 15px !important;
    font-style: normal;
    font-stretch: normal;
    letter-spacing: normal;
    line-height: 25px !important;
    border-bottom: solid 1px #e3e3e3 !important;
    padding-left: 5%;
    color: #000000 !important;
    text-align: left;
}

.radiolistitemlang.SkillSet-ChkGrpUl {
    margin: 0px;
    padding: 0px;
    width: 100%;
    position: relative;
    height: 60px;
}

    .radiolistitemlang.SkillSet-ChkGrpUl li {
        position: relative;
        width: 20%;
        margin: 5px 0px;
        font-family: 'GillSansCE-Roman';
        color: #9b9b9b;
        font-size: 10px;
    }
/*BV,ST-1162 END*/
/*ST-1163 START*/
.mainpage .wizardstep .wizardstepcontent .button.addLanguage {
    margin-top: 15px;
}

.SkillSet-Level-Main.PopUp-Lang-Main {
    width: 290px;
}

#divLangFafaBox .radiolistitemlang [type="radio"].rbtnLangBasic:checked + label:after, #divLangFafaBox .radiolistitemlang [type="radio"].rbtnLangBasic:not(:checked) + label:after {
    background: #d6d3e1 !important;
}

#divLangFafaBox .radiolistitemlang [type="radio"].rbtnLangFunctional:checked + label:after, #divLangFafaBox .radiolistitemlang [type="radio"].rbtnLangFunctional:not(:checked) + label:after {
    background: #b9a9c9 !important;
}

#divLangFafaBox .radiolistitemlang [type="radio"].rbtnLangConversational:checked + label:after, #divLangFafaBox .radiolistitemlang [type="radio"].rbtnLangConversational:not(:checked) + label:after {
    background: #9b80b0 !important;
}

#divLangFafaBox .radiolistitemlang [type="radio"].rbtnLangProficient:checked + label:after, #divLangFafaBox .radiolistitemlang [type="radio"].rbtnLangProficient:not(:checked) + label:after {
    background: #7c5697 !important;
}

#divLangFafaBox .radiolistitemlang [type="radio"].rbtnLangFluent:checked + label:after, #divLangFafaBox .radiolistitemlang [type="radio"].rbtnLangFluent:not(:checked) + label:after {
    background: #57307a !important;
}

#divSelectSkill.YResume_Modal .modal-dialog {
    float: left;
    margin-left: 18%;
    width: auto;
}

#divSelectSkill.YResume_Modal .resumeskillnote {
    position: absolute;
    top: 40%;
    right: -320px;
    width: 280px;
    font-family: 'GillSans';
    z-index: 99;
}

    #divSelectSkill.YResume_Modal .resumeskillnote .noterating {
        border: solid 2px #000;
        padding: 10px;
        font-size: 12px;
        color: #808081;
        background: #fff;
    }

        #divSelectSkill.YResume_Modal .resumeskillnote .noterating .noteheading {
            color: #000;
            margin-top: 10px;
        }

        #divSelectSkill.YResume_Modal .resumeskillnote .noterating table {
            font-size: 12px;
            color: #808081;
            margin-top: 10px;
        }

            #divSelectSkill.YResume_Modal .resumeskillnote .noterating table td {
                padding: 3px 0px;
            }

    #divSelectSkill.YResume_Modal .resumeskillnote .hintemp {
        position: absolute;
        width: 150px;
        height: 150px;
        border: solid 2px #3A5791;
        font-size: 12px;
        color: #808081;
        ;
        background: #fff;
        border-radius: 50%;
        top: -130px;
        left: -40px;
    }

        #divSelectSkill.YResume_Modal .resumeskillnote .hintemp .innerhinttemp {
            padding: 5px;
            text-align: center;
            padding-top: 15px;
        }

.ss-yourlang {
    font-family: GillSans;
    font-size: 15px;
    font-weight: 300;
    color: #9b9b9b;
}
/*ST-1163 END*/
/*ST-1198 START*/
#divSkillSetLevelPopUpMain {
    margin-left: 0px;
}

.btnShowFirstPg {
    background: url(../images/left-angel.png) top center no-repeat;
    display: inline-block;
    left: 91px;
    height: 95px;
    width: 40px;
    background-size: 60% 80% !important;
    background-color: transparent !important;
    border: 0;
    position: absolute;
    top: 35%;
    outline: 0;
}
/*.btnShowSecondPg {position:absolute;top:300px; float:left;height:20px; right:-120px;}*/
.btnShowSecondPg {
    background: url(../images/right-angel.png) top center no-repeat;
    display: inline-block;
    left: 717px;
    height: 95px;
    width: 40px;
    background-size: 60% 80%;
    background-color: transparent !important;
    border: 0;
    position: absolute;
    top: 35%;
    outline: 0;
}

.OOAPageText {
    font-size: 15px;
    font-family: GillSansCE-Roman;
    font-weight: bold;
    color: #9b9b9b;
}

.OOAPage2Text {
    margin-top: 25px;
}

.OOARightText {
    margin-top: 25px;
}

.OOAPageSideText {
    font-size: 15px;
    font-family: GillSansCE-Roman;
    font-weight: 300;
    color: #9b9b9b;
}

.divPopUpInnerParent {
    background: rgba(255, 255, 255, 0.77);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
/*ST-1198 END*/

.categorylistdiv {
    width: 76.5%;
    border: solid 1px #979797;
    background: #fff; /*position:absolute; bottom:25px; z-index:9999;*/
    display: block;
    position: relative;
}

    .categorylistdiv .ulline {
        border-left: solid 1px #979797;
        position: absolute;
        height: 100%;
        left: 50%;
    }

    .categorylistdiv ul {
        columns: 2;
        -webkit-columns: 2;
        -moz-columns: 2;
        margin: 0px;
        list-style: none;
        padding: 0px;
        list-style-position: inside;
    }

        .categorylistdiv ul li {
            padding: 0px 8px; /* width:50%; display:inline-block*/
            display: inline-block;
            width: 100%;
            color: black;
        }

            .categorylistdiv ul li:hover {
                background: #E7EFFA;
            }
/*ST-917 START*/

.divImgPlaceHolder {
    /*background-image: url(../images/picture-box.png);*/
    /*background-repeat: no-repeat;*/
    margin-top: 20px;
    /*background-size: cover;*/
}

.imgPlaceHolder {
    height: 180px;
    width: 160px;
}


input[type=range] {
    -webkit-appearance: none;
    margin: 10px 0;
    width: 300px;
}

.mainpage .wizardstep .imageeditormenu li > a.Filter span {
    background-image: url(../images/ImgPopUp_filter.png);
    background-repeat: no-repeat;
    position: absolute;
    width: 45px;
    height: 24px;
    left: 0px;
}

.mainpage .wizardstep .imageeditormenu li > a.Adjust span {
    background-image: url(../images/ImgPopUp_adjust.png);
    background-repeat: no-repeat;
    position: absolute;
    width: 24px;
    height: 24px;
    left: 0px;
}

.mainpage .wizardstep .imageeditormenu li > a.Frame span {
    background-image: url(../images/frame.png);
    background-repeat: no-repeat;
    position: absolute;
    width: 24px;
    height: 24px;
    left: 0px;
}

#topImageToolBar .image_toolbar {
    height: 49.9px;
    padding-top: 7px !important;
    padding-bottom: 7px !important;
}

.image_toolbar ul.goto_img li#aGoToImageLibrary img {
    width: 34px;
    height: 34px;
    margin-right: 10px;
}

.modal.new_flip .modal-dialog {
    top: 7%;
    width: 430px;
}

.image_toolbar ul.blank_img li img {
    margin-right: 10px;
}

.tab_content.Tab3DisplayImgOnTop {
}

.cke_top, .cke_chrome {
    border: none !important;
}

/*ST-1144 START*/
/*PersonalInfoFormFull PersonalInfoFormBox PersonalInfoFormHalf PersonalInfoFormHalfFull*/
.mainpage .wizardstep .personalinfoform.PersonalInfoFormFull .forminput.CityState,
.mainpage .wizardstep .personalinfoform.PersonalInfoFormHalf .forminput.CityState {
    position: relative;
    top: -17px;
    left: 0;
    margin-right: 0;
}

.mainpage .wizardstep .personalinfoform.PersonalInfoFormFull .forminput {
    width: 47%;
    margin-right: 2%;
    min-width: inherit;
}

.mainpage .wizardstep .personalinfoform.PersonalInfoFormFull .hintpanel .resumevisiblityhint,
.mainpage .wizardstep .personalinfoform.PersonalInfoFormBox .hintpanel .resumevisiblityhint,
.mainpage .wizardstep .personalinfoform.PersonalInfoFormHalf .hintpanel .resumevisiblityhint,
.mainpage .wizardstep .personalinfoform.PersonalInfoFormHalfFull .hintpanel .resumevisiblityhint {
    top: -100px;
}

.mainpage .wizardstep .PersonalInfoFormBox .forminput.CityState,
.mainpage .wizardstep .personalinfoform.PersonalInfoFormHalfFull .forminput.CityState {
    position: relative;
    top: 5px;
}

.mainpage .wizardstep .personalinfoform.PersonalInfoFormHalf .forminput,
.mainpage .wizardstep .personalinfoform.PersonalInfoFormHalfFull .forminput,
.mainpage .wizardstep .personalinfoform.PersonalInfoFormHalfFull .forminput.half.allfull {
    width: 49%;
    margin-right: 2%;
    min-width: inherit;
}

    .mainpage .wizardstep .personalinfoform.PersonalInfoFormHalf .forminput.half,
    .mainpage .wizardstep .personalinfoform.PersonalInfoFormHalfFull .forminput.half {
        margin-right: 0;
        width: 49%;
    }

.mainpage .wizardstep .personalinfoform.PersonalInfoFormHalf .hintpanel .flipbookurlhint {
    top: 125px;
    /*chhanda #1287 8/4/2018*/
    right: 40px !important;
}

/*.mainpage .wizardstep .personalinfoform.PersonalInfoFormHalf .hintpanel .resumenamehint,*/
.mainpage .wizardstep .personalinfoform.PersonalInfoFormHalf .hintpanel .additionallinkhint {
    top: 140px;
    /*chhanda #1287 8/4/2018*/
    right: 60px !important;
}
/*chhanda #1287 8/4/2018*/
.mainpage .wizardstep .personalinfoform.PersonalInfoFormHalf .hintpanel .resumenamehint {
    top: 140px;
    right: 60px !important;
}

.mainpage .wizardstep .personalinfoform.PersonalInfoFormHalfFull .hintpanel .resumevisiblityhint {
    top: 135px;
    /*left: 0;*/
    /*chhanda #1287 8/4/2018*/
    right: 30px !important;
}
/*ST-1234*/
.mainpage .wizardstep .personalinfoform.PersonalInfoFormFull .forminput.PositionRight {
    float: right;
}

.mainpage .wizardstep .personalinfoform.PersonalInfoFormFull .forminput.PositionLeft {
    float: left;
}

/*ST-1272*/
.aSkillName {cursor:pointer;}

/*ST-1188*/


#divCompanyPopUp .modal-content {
    width: 700px;
    margin-top: 15%;
}

#divCompanyPopUp .modal-body {
    padding-top: 0px;
    padding-left: 46px;
}

#divCompanyPopUp #btnSaveSkill {
    padding-left: 5px;
    padding-right: 20px;
    margin-right: 10px;
}

#divCompanyPopUp.YResume_Modal .modal-dialog {
    float: left;
    margin-left: 18%;
    width: auto;
}
#divCompanyPopUp
/*ST-1293*/
.ulTempInsImageDisplay {list-style: none;margin: 0;padding: 0;}
.ulTempInsImageDisplay li{display: inline-block;width: 46%;margin: 2%;}
.imgAutoCompLogo {width:12%;}
.liAutoCompText {width:398px !important;    
    float:left;
    padding-bottom: 3px;
    padding-top: 3px !important;
    border-bottom: 1px solid #D6D3E1;
    line-height: 18px;}
    .ui-state-active, .liAutoCompText:hover {text-decoration:none !important; background:#D6D3E1 !important; border: 1px solid #D6D3E1 !important;}
.aliAutoCompText {float:right;width:88%;}
.aliAutoCompTextOnly{float:right;width:98%;}
.ui-autocomplete {width:398px !important; height:200px; overflow-y:scroll;overflow-x: hidden;}