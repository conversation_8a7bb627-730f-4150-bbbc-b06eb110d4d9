﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;

namespace Flipbook
{
    public class RouteConfig
    {
        public static void RegisterRoutes(RouteCollection routes)
        {
            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");
            routes.IgnoreRoute("{resource}.ashx/{*pathInfo}");


            #region '  Resume Wizard  '

            routes.MapRoute(
                name: "ChooseTemplate",
                url: "ResumeWizard/chooseTemplate/{ResumeId}/{TradeId}",
                defaults: new { controller = "ResumeWizard", action = "ChooseTemplateByID", TradeId = UrlParameter.Optional }
            );

            routes.MapRoute(
                name: "FilterTemplate",
                url: "ResumeWizard/FilterTemplate/{id}/{ViewType}",
                defaults: new { controller = "ResumeWizard", action = "FilterTemplate", id = UrlParameter.Optional, ViewType = UrlParameter.Optional }
            );

            routes.MapRoute(
                name: "SkillSet",
                url: "ResumeWizard/SkillSet/{id}/{PageId}",
                defaults: new { controller = "ResumeWizard", action = "SkillSet", id = UrlParameter.Optional, ViewType = UrlParameter.Optional }
            );

            #endregion

            #region ' Flip Book '
            //ST-1735
            routes.MapRoute(
               name: "FBPublish",
               url: "Flipbook/Publish/{id}",
               defaults: new { controller = "Publish", action = "Preview", ViewType = UrlParameter.Optional }
           );
            //ST-2097
            routes.MapRoute(
               name: "FBUserPublish",
               url: ("{id}"),
               defaults: new { controller = "Publish", action = "UserPreview", ViewType = UrlParameter.Optional }
           );

            //ST-1959
            routes.MapRoute(
               name: "FBInspiration",
               url: "Flipbook/Inspiration/{id}/{PgCount}",
               defaults: new { controller = "Publish", action = "Inspiration", ViewType = UrlParameter.Optional }
           );

            #endregion

            //ST - 1341 , PK , 10 / 05 / 2018 , Addded For Custom URL , 10/15/2018 Update Position
            routes.MapRoute(
             name: "fb",
             url: "fb/{url}",
             defaults: new { controller = "fb", action = "Index", url = UrlParameter.Optional }
             );
            //ST-2019
            routes.MapRoute(
                name: "Default",
                url: "{controller}/{action}/{id}",
                defaults: new { controller = "Flipbook", action = "MyFlipbooks", id = UrlParameter.Optional }
            );

            
        }
    }
}
