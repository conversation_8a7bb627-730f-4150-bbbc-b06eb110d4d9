{"ast": null, "code": "// Custom hook for authentication management\n// Matches the functionality from .NET AccountController\nimport{useState,useEffect,useCallback}from'react';import{apiService}from'../services/api.service';import{mockApiService}from'../services/mockApi.service';// Environment-based API service selection\nconst getApiService=()=>{return process.env.REACT_APP_USE_MOCK_API==='false'?apiService:mockApiService;};export const useAuth=()=>{// State management\nconst[user,setUser]=useState(null);const[loading,setLoading]=useState(true);const[signingIn,setSigningIn]=useState(false);const[signingOut,setSigningOut]=useState(false);const[error,setError]=useState(null);const api=getApiService();// Computed authentication state\nconst isAuthenticated=user!==null;// Clear error state\nconst clearError=useCallback(()=>{setError(null);},[]);// Check current authentication status - matches GetCurrentUser endpoint\nconst checkAuthStatus=useCallback(async()=>{try{setLoading(true);setError(null);const response=await api.getCurrentUser();if(response.success&&response.data){setUser(response.data);}else{setUser(null);// Don't set error for unauthenticated state\n}}catch(err){setUser(null);// Don't set error for network issues during auth check\nconsole.warn('Auth check failed:',err);}finally{setLoading(false);}},[api]);// Sign in user - matches FBLogin method\nconst signIn=useCallback(async function(email,password){let rememberMe=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;try{setSigningIn(true);setError(null);const response=await api.signIn(email,password,rememberMe);if(response.success&&response.data){setUser(response.data);return true;}else{// Handle specific error codes from .NET FBLogin method\nconst errorMessage=response.error||'Invalid login attempt';if(errorMessage==='0'||errorMessage==='Invalid Login Attempt.'){setError('Invalid email or password. Please try again.');}else if(errorMessage==='2'){setError('Your account is locked. Please contact support.');}else if(errorMessage==='3'){setError('User not found. Please check your email address.');}else{setError(errorMessage);}return false;}}catch(err){setError(err instanceof Error?err.message:'An error occurred during sign in');return false;}finally{setSigningIn(false);}},[api]);// Sign out user - matches LogOff endpoint\nconst signOut=useCallback(async()=>{try{setSigningOut(true);setError(null);await api.signOut();setUser(null);}catch(err){// Even if sign out fails on server, clear local state\nsetUser(null);console.warn('Sign out error:',err);}finally{setSigningOut(false);}},[api]);// Check authentication status on mount\nuseEffect(()=>{checkAuthStatus();},[checkAuthStatus]);return{// User state\nuser,isAuthenticated,// Loading states\nloading,signingIn,signingOut,// Error state\nerror,// Operations\nsignIn,signOut,checkAuthStatus,clearError};};", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "apiService", "mockApiService", "getApiService", "process", "env", "REACT_APP_USE_MOCK_API", "useAuth", "user", "setUser", "loading", "setLoading", "signingIn", "setSigningIn", "signingOut", "setSigningOut", "error", "setError", "api", "isAuthenticated", "clearError", "checkAuthStatus", "response", "getCurrentUser", "success", "data", "err", "console", "warn", "signIn", "email", "password", "rememberMe", "arguments", "length", "undefined", "errorMessage", "Error", "message", "signOut"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/hooks/useAuth.ts"], "sourcesContent": ["// Custom hook for authentication management\n// Matches the functionality from .NET AccountController\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { UserSession, ApiResponse } from '../types/flipbook.types';\nimport { apiService } from '../services/api.service';\nimport { mockApiService } from '../services/mockApi.service';\n\n// Environment-based API service selection\nconst getApiService = () => {\n  return process.env.REACT_APP_USE_MOCK_API === 'false' ? apiService : mockApiService;\n};\n\ninterface UseAuthReturn {\n  // User state\n  user: UserSession | null;\n  isAuthenticated: boolean;\n\n  // Loading states\n  loading: boolean;\n  signingIn: boolean;\n  signingOut: boolean;\n\n  // Error state\n  error: string | null;\n\n  // Operations\n  signIn: (email: string, password: string, rememberMe?: boolean) => Promise<boolean>;\n  signOut: () => Promise<void>;\n  checkAuthStatus: () => Promise<void>;\n  clearError: () => void;\n}\n\nexport const useAuth = (): UseAuthReturn => {\n  // State management\n  const [user, setUser] = useState<UserSession | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [signingIn, setSigningIn] = useState(false);\n  const [signingOut, setSigningOut] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const api = getApiService();\n\n  // Computed authentication state\n  const isAuthenticated = user !== null;\n\n  // Clear error state\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  // Check current authentication status - matches GetCurrentUser endpoint\n  const checkAuthStatus = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response: ApiResponse<UserSession> = await api.getCurrentUser();\n\n      if (response.success && response.data) {\n        setUser(response.data);\n      } else {\n        setUser(null);\n        // Don't set error for unauthenticated state\n      }\n    } catch (err) {\n      setUser(null);\n      // Don't set error for network issues during auth check\n      console.warn('Auth check failed:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [api]);\n\n  // Sign in user - matches FBLogin method\n  const signIn = useCallback(async (email: string, password: string, rememberMe: boolean = false): Promise<boolean> => {\n    try {\n      setSigningIn(true);\n      setError(null);\n\n      const response: ApiResponse<UserSession> = await api.signIn(email, password, rememberMe);\n\n      if (response.success && response.data) {\n        setUser(response.data);\n        return true;\n      } else {\n        // Handle specific error codes from .NET FBLogin method\n        const errorMessage = response.error || 'Invalid login attempt';\n        if (errorMessage === '0' || errorMessage === 'Invalid Login Attempt.') {\n          setError('Invalid email or password. Please try again.');\n        } else if (errorMessage === '2') {\n          setError('Your account is locked. Please contact support.');\n        } else if (errorMessage === '3') {\n          setError('User not found. Please check your email address.');\n        } else {\n          setError(errorMessage);\n        }\n        return false;\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred during sign in');\n      return false;\n    } finally {\n      setSigningIn(false);\n    }\n  }, [api]);\n\n  // Sign out user - matches LogOff endpoint\n  const signOut = useCallback(async () => {\n    try {\n      setSigningOut(true);\n      setError(null);\n\n      await api.signOut();\n      setUser(null);\n    } catch (err) {\n      // Even if sign out fails on server, clear local state\n      setUser(null);\n      console.warn('Sign out error:', err);\n    } finally {\n      setSigningOut(false);\n    }\n  }, [api]);\n\n  // Check authentication status on mount\n  useEffect(() => {\n    checkAuthStatus();\n  }, [checkAuthStatus]);\n\n  return {\n    // User state\n    user,\n    isAuthenticated,\n\n    // Loading states\n    loading,\n    signingIn,\n    signingOut,\n\n    // Error state\n    error,\n\n    // Operations\n    signIn,\n    signOut,\n    checkAuthStatus,\n    clearError,\n  };\n};\n"], "mappings": "AAAA;AACA;AAEA,OAASA,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CAExD,OAASC,UAAU,KAAQ,yBAAyB,CACpD,OAASC,cAAc,KAAQ,6BAA6B,CAE5D;AACA,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1B,MAAO,CAAAC,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAAK,OAAO,CAAGL,UAAU,CAAGC,cAAc,CACrF,CAAC,CAsBD,MAAO,MAAM,CAAAK,OAAO,CAAGA,CAAA,GAAqB,CAC1C;AACA,KAAM,CAACC,IAAI,CAAEC,OAAO,CAAC,CAAGX,QAAQ,CAAqB,IAAI,CAAC,CAC1D,KAAM,CAACY,OAAO,CAAEC,UAAU,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACc,SAAS,CAAEC,YAAY,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACgB,UAAU,CAAEC,aAAa,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACkB,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,QAAQ,CAAgB,IAAI,CAAC,CAEvD,KAAM,CAAAoB,GAAG,CAAGf,aAAa,CAAC,CAAC,CAE3B;AACA,KAAM,CAAAgB,eAAe,CAAGX,IAAI,GAAK,IAAI,CAErC;AACA,KAAM,CAAAY,UAAU,CAAGpB,WAAW,CAAC,IAAM,CACnCiB,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAI,eAAe,CAAGrB,WAAW,CAAC,SAAY,CAC9C,GAAI,CACFW,UAAU,CAAC,IAAI,CAAC,CAChBM,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAK,QAAkC,CAAG,KAAM,CAAAJ,GAAG,CAACK,cAAc,CAAC,CAAC,CAErE,GAAID,QAAQ,CAACE,OAAO,EAAIF,QAAQ,CAACG,IAAI,CAAE,CACrChB,OAAO,CAACa,QAAQ,CAACG,IAAI,CAAC,CACxB,CAAC,IAAM,CACLhB,OAAO,CAAC,IAAI,CAAC,CACb;AACF,CACF,CAAE,MAAOiB,GAAG,CAAE,CACZjB,OAAO,CAAC,IAAI,CAAC,CACb;AACAkB,OAAO,CAACC,IAAI,CAAC,oBAAoB,CAAEF,GAAG,CAAC,CACzC,CAAC,OAAS,CACRf,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,CAACO,GAAG,CAAC,CAAC,CAET;AACA,KAAM,CAAAW,MAAM,CAAG7B,WAAW,CAAC,eAAO8B,KAAa,CAAEC,QAAgB,CAAoD,IAAlD,CAAAC,UAAmB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC5F,GAAI,CACFpB,YAAY,CAAC,IAAI,CAAC,CAClBI,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAK,QAAkC,CAAG,KAAM,CAAAJ,GAAG,CAACW,MAAM,CAACC,KAAK,CAAEC,QAAQ,CAAEC,UAAU,CAAC,CAExF,GAAIV,QAAQ,CAACE,OAAO,EAAIF,QAAQ,CAACG,IAAI,CAAE,CACrChB,OAAO,CAACa,QAAQ,CAACG,IAAI,CAAC,CACtB,MAAO,KAAI,CACb,CAAC,IAAM,CACL;AACA,KAAM,CAAAW,YAAY,CAAGd,QAAQ,CAACN,KAAK,EAAI,uBAAuB,CAC9D,GAAIoB,YAAY,GAAK,GAAG,EAAIA,YAAY,GAAK,wBAAwB,CAAE,CACrEnB,QAAQ,CAAC,8CAA8C,CAAC,CAC1D,CAAC,IAAM,IAAImB,YAAY,GAAK,GAAG,CAAE,CAC/BnB,QAAQ,CAAC,iDAAiD,CAAC,CAC7D,CAAC,IAAM,IAAImB,YAAY,GAAK,GAAG,CAAE,CAC/BnB,QAAQ,CAAC,kDAAkD,CAAC,CAC9D,CAAC,IAAM,CACLA,QAAQ,CAACmB,YAAY,CAAC,CACxB,CACA,MAAO,MAAK,CACd,CACF,CAAE,MAAOV,GAAG,CAAE,CACZT,QAAQ,CAACS,GAAG,WAAY,CAAAW,KAAK,CAAGX,GAAG,CAACY,OAAO,CAAG,kCAAkC,CAAC,CACjF,MAAO,MAAK,CACd,CAAC,OAAS,CACRzB,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAAE,CAACK,GAAG,CAAC,CAAC,CAET;AACA,KAAM,CAAAqB,OAAO,CAAGvC,WAAW,CAAC,SAAY,CACtC,GAAI,CACFe,aAAa,CAAC,IAAI,CAAC,CACnBE,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAC,GAAG,CAACqB,OAAO,CAAC,CAAC,CACnB9B,OAAO,CAAC,IAAI,CAAC,CACf,CAAE,MAAOiB,GAAG,CAAE,CACZ;AACAjB,OAAO,CAAC,IAAI,CAAC,CACbkB,OAAO,CAACC,IAAI,CAAC,iBAAiB,CAAEF,GAAG,CAAC,CACtC,CAAC,OAAS,CACRX,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAAE,CAACG,GAAG,CAAC,CAAC,CAET;AACAnB,SAAS,CAAC,IAAM,CACdsB,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,CAACA,eAAe,CAAC,CAAC,CAErB,MAAO,CACL;AACAb,IAAI,CACJW,eAAe,CAEf;AACAT,OAAO,CACPE,SAAS,CACTE,UAAU,CAEV;AACAE,KAAK,CAEL;AACAa,MAAM,CACNU,OAAO,CACPlB,eAAe,CACfD,UACF,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}