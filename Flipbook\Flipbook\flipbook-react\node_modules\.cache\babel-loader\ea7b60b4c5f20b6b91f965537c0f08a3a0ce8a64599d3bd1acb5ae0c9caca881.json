{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import'./TextToolbar.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TextToolbar=_ref=>{let{isVisible,currentFormatting,onFormatChange,onApplyFormatting}=_ref;const[showColorPicker,setShowColorPicker]=useState(false);const[showBackgroundPicker,setShowBackgroundPicker]=useState(false);const colorPickerRef=useRef(null);const fontFamilies=['Arial','Times New Roman','Helvetica','Georgia','Verdana','Trebuchet MS','Courier New','Impact','Comic Sans MS','Amenir'];const fontSizes=['8','9','10','11','12','14','16','18','20','22','24','26','28','30','32','36','48','72'];const lineHeights=['1.0','1.2','1.4','1.6','1.8','2.0','2.5','3.0'];const letterSpacings=['0','0.5px','1px','1.5px','2px','3px','4px','5px'];const commonColors=['#000000','#FFFFFF','#FF0000','#00FF00','#0000FF','#FFFF00','#FF00FF','#00FFFF','#800000','#008000','#000080','#808000','#800080','#008080','#C0C0C0','#808080','#9999FF','#993366','#FFFFCC','#CCFFFF','#660066','#FF8080','#0066CC','#CCCCFF'];useEffect(()=>{const handleClickOutside=event=>{if(colorPickerRef.current&&!colorPickerRef.current.contains(event.target)){setShowColorPicker(false);setShowBackgroundPicker(false);}};document.addEventListener('mousedown',handleClickOutside);return()=>document.removeEventListener('mousedown',handleClickOutside);},[]);const handleFontFamilyChange=fontFamily=>{onFormatChange({fontFamily});onApplyFormatting('fontName',fontFamily);};const handleFontSizeChange=fontSize=>{onFormatChange({fontSize});onApplyFormatting('fontSize',fontSize);};const handleBoldClick=()=>{const newBold=!currentFormatting.bold;onFormatChange({bold:newBold});onApplyFormatting('bold');};const handleItalicClick=()=>{const newItalic=!currentFormatting.italic;onFormatChange({italic:newItalic});onApplyFormatting('italic');};const handleUnderlineClick=()=>{const newUnderline=!currentFormatting.underline;onFormatChange({underline:newUnderline});onApplyFormatting('underline');};const handleAlignClick=align=>{onFormatChange({textAlign:align});const commandMap={'left':'justifyLeft','center':'justifyCenter','right':'justifyRight','justify':'justifyFull'};onApplyFormatting(commandMap[align]);};const handleColorChange=color=>{onFormatChange({color});onApplyFormatting('foreColor',color);setShowColorPicker(false);};const handleBackgroundColorChange=color=>{onFormatChange({backgroundColor:color});onApplyFormatting('backColor',color);setShowBackgroundPicker(false);};const handleLineHeightChange=lineHeight=>{onFormatChange({lineHeight});};const handleLetterSpacingChange=letterSpacing=>{onFormatChange({letterSpacing});};const insertList=ordered=>{onApplyFormatting(ordered?'insertOrderedList':'insertUnorderedList');};const insertLink=()=>{const url=prompt('Enter URL:');if(url){onApplyFormatting('createLink',url);}};if(!isVisible)return null;return/*#__PURE__*/_jsxs(\"div\",{className:\"text-toolbar\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"toolbar-section\",children:/*#__PURE__*/_jsx(\"label\",{className:\"toolbar-label\",children:\"Text Toolbar\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"toolbar-section\",children:/*#__PURE__*/_jsx(\"select\",{className:\"font-family-select\",value:currentFormatting.fontFamily,onChange:e=>handleFontFamilyChange(e.target.value),children:fontFamilies.map(font=>/*#__PURE__*/_jsx(\"option\",{value:font,style:{fontFamily:font},children:font},font))})}),/*#__PURE__*/_jsx(\"div\",{className:\"toolbar-section\",children:/*#__PURE__*/_jsx(\"select\",{className:\"font-size-select\",value:currentFormatting.fontSize,onChange:e=>handleFontSizeChange(e.target.value),children:fontSizes.map(size=>/*#__PURE__*/_jsxs(\"option\",{value:size,children:[size,\"pt\"]},size))})}),/*#__PURE__*/_jsxs(\"div\",{className:\"toolbar-section format-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"format-btn \".concat(currentFormatting.bold?'active':''),onClick:handleBoldClick,title:\"Bold\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"B\"})}),/*#__PURE__*/_jsx(\"button\",{className:\"format-btn \".concat(currentFormatting.italic?'active':''),onClick:handleItalicClick,title:\"Italic\",children:/*#__PURE__*/_jsx(\"em\",{children:\"I\"})}),/*#__PURE__*/_jsx(\"button\",{className:\"format-btn \".concat(currentFormatting.underline?'active':''),onClick:handleUnderlineClick,title:\"Underline\",children:/*#__PURE__*/_jsx(\"u\",{children:\"U\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"toolbar-section alignment-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"format-btn \".concat(currentFormatting.textAlign==='left'?'active':''),onClick:()=>handleAlignClick('left'),title:\"Align Left\",children:\"\\u2310\"}),/*#__PURE__*/_jsx(\"button\",{className:\"format-btn \".concat(currentFormatting.textAlign==='center'?'active':''),onClick:()=>handleAlignClick('center'),title:\"Align Center\",children:\"\\u2261\"}),/*#__PURE__*/_jsx(\"button\",{className:\"format-btn \".concat(currentFormatting.textAlign==='right'?'active':''),onClick:()=>handleAlignClick('right'),title:\"Align Right\",children:\"\\xAC\"}),/*#__PURE__*/_jsx(\"button\",{className:\"format-btn \".concat(currentFormatting.textAlign==='justify'?'active':''),onClick:()=>handleAlignClick('justify'),title:\"Justify\",children:\"\\u2263\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"toolbar-section list-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"format-btn\",onClick:()=>insertList(false),title:\"Bullet List\",children:\"\\u2022\"}),/*#__PURE__*/_jsx(\"button\",{className:\"format-btn\",onClick:()=>insertList(true),title:\"Numbered List\",children:\"1.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"toolbar-section color-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"color-picker-container\",ref:colorPickerRef,children:[/*#__PURE__*/_jsx(\"button\",{className:\"color-btn\",onClick:()=>setShowColorPicker(!showColorPicker),title:\"Text Color\",children:/*#__PURE__*/_jsx(\"span\",{className:\"color-indicator\",style:{backgroundColor:currentFormatting.color},children:\"A\"})}),showColorPicker&&/*#__PURE__*/_jsx(\"div\",{className:\"color-picker\",children:/*#__PURE__*/_jsx(\"div\",{className:\"color-grid\",children:commonColors.map(color=>/*#__PURE__*/_jsx(\"button\",{className:\"color-option\",style:{backgroundColor:color},onClick:()=>handleColorChange(color),title:color},color))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"color-picker-container\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"color-btn\",onClick:()=>setShowBackgroundPicker(!showBackgroundPicker),title:\"Background Color\",children:/*#__PURE__*/_jsx(\"span\",{className:\"color-indicator bg-indicator\",style:{backgroundColor:currentFormatting.backgroundColor},children:\"\\u25A0\"})}),showBackgroundPicker&&/*#__PURE__*/_jsx(\"div\",{className:\"color-picker\",children:/*#__PURE__*/_jsx(\"div\",{className:\"color-grid\",children:commonColors.map(color=>/*#__PURE__*/_jsx(\"button\",{className:\"color-option\",style:{backgroundColor:color},onClick:()=>handleBackgroundColorChange(color),title:color},color))})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"toolbar-section\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"toolbar-micro-label\",children:\"Leading\"}),/*#__PURE__*/_jsx(\"select\",{className:\"line-height-select\",value:currentFormatting.lineHeight,onChange:e=>handleLineHeightChange(e.target.value),children:lineHeights.map(height=>/*#__PURE__*/_jsx(\"option\",{value:height,children:height},height))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"toolbar-section\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"toolbar-micro-label\",children:\"Kerning\"}),/*#__PURE__*/_jsx(\"select\",{className:\"letter-spacing-select\",value:currentFormatting.letterSpacing,onChange:e=>handleLetterSpacingChange(e.target.value),children:letterSpacings.map(spacing=>/*#__PURE__*/_jsx(\"option\",{value:spacing,children:spacing},spacing))})]}),/*#__PURE__*/_jsx(\"div\",{className:\"toolbar-section\",children:/*#__PURE__*/_jsx(\"button\",{className:\"format-btn link-btn\",onClick:insertLink,title:\"Insert Link\",children:\"\\uD83D\\uDD17\"})})]});};export default TextToolbar;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "TextToolbar", "_ref", "isVisible", "currentFormatting", "onFormatChange", "onApplyFormatting", "showColorPicker", "setShowColorPicker", "showBackgroundPicker", "setShowBackgroundPicker", "colorPickerRef", "fontFamilies", "fontSizes", "lineHeights", "letterSpacings", "commonColors", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "handleFontFamilyChange", "fontFamily", "handleFontSizeChange", "fontSize", "handleBoldClick", "newBold", "bold", "handleItalicClick", "newItalic", "italic", "handleUnderlineClick", "newUnderline", "underline", "handleAlignClick", "align", "textAlign", "commandMap", "handleColorChange", "color", "handleBackgroundColorChange", "backgroundColor", "handleLineHeightChange", "lineHeight", "handleLetterSpacingChange", "letterSpacing", "insertList", "ordered", "insertLink", "url", "prompt", "className", "children", "value", "onChange", "e", "map", "font", "style", "size", "concat", "onClick", "title", "ref", "height", "spacing"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/TextToolbar/TextToolbar.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './TextToolbar.css';\n\ninterface TextFormatting {\n  fontFamily: string;\n  fontSize: string;\n  bold: boolean;\n  italic: boolean;\n  underline: boolean;\n  textAlign: 'left' | 'center' | 'right' | 'justify';\n  color: string;\n  backgroundColor: string;\n  lineHeight: string;\n  letterSpacing: string;\n}\n\ninterface TextToolbarProps {\n  isVisible: boolean;\n  currentFormatting: TextFormatting;\n  onFormatChange: (formatting: Partial<TextFormatting>) => void;\n  onApplyFormatting: (command: string, value?: string) => void;\n}\n\nconst TextToolbar: React.FC<TextToolbarProps> = ({\n  isVisible,\n  currentFormatting,\n  onFormatChange,\n  onApplyFormatting\n}) => {\n  const [showColorPicker, setShowColorPicker] = useState(false);\n  const [showBackgroundPicker, setShowBackgroundPicker] = useState(false);\n  const colorPickerRef = useRef<HTMLDivElement>(null);\n\n  const fontFamilies = [\n    'Arial', 'Times New Roman', 'Helvetica', 'Georgia', 'Verdana',\n    'Trebuchet MS', 'Courier New', 'Impact', 'Comic Sans MS', 'Amenir'\n  ];\n\n  const fontSizes = [\n    '8', '9', '10', '11', '12', '14', '16', '18', '20', '22', '24', '26', '28', '30', '32', '36', '48', '72'\n  ];\n\n  const lineHeights = ['1.0', '1.2', '1.4', '1.6', '1.8', '2.0', '2.5', '3.0'];\n  const letterSpacings = ['0', '0.5px', '1px', '1.5px', '2px', '3px', '4px', '5px'];\n\n  const commonColors = [\n    '#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF',\n    '#800000', '#008000', '#000080', '#808000', '#800080', '#008080', '#C0C0C0', '#808080',\n    '#9999FF', '#993366', '#FFFFCC', '#CCFFFF', '#660066', '#FF8080', '#0066CC', '#CCCCFF'\n  ];\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (colorPickerRef.current && !colorPickerRef.current.contains(event.target as Node)) {\n        setShowColorPicker(false);\n        setShowBackgroundPicker(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const handleFontFamilyChange = (fontFamily: string) => {\n    onFormatChange({ fontFamily });\n    onApplyFormatting('fontName', fontFamily);\n  };\n\n  const handleFontSizeChange = (fontSize: string) => {\n    onFormatChange({ fontSize });\n    onApplyFormatting('fontSize', fontSize);\n  };\n\n  const handleBoldClick = () => {\n    const newBold = !currentFormatting.bold;\n    onFormatChange({ bold: newBold });\n    onApplyFormatting('bold');\n  };\n\n  const handleItalicClick = () => {\n    const newItalic = !currentFormatting.italic;\n    onFormatChange({ italic: newItalic });\n    onApplyFormatting('italic');\n  };\n\n  const handleUnderlineClick = () => {\n    const newUnderline = !currentFormatting.underline;\n    onFormatChange({ underline: newUnderline });\n    onApplyFormatting('underline');\n  };\n\n  const handleAlignClick = (align: 'left' | 'center' | 'right' | 'justify') => {\n    onFormatChange({ textAlign: align });\n    const commandMap: { [key: string]: string } = {\n      'left': 'justifyLeft',\n      'center': 'justifyCenter', \n      'right': 'justifyRight',\n      'justify': 'justifyFull'\n    };\n    onApplyFormatting(commandMap[align]);\n  };\n\n  const handleColorChange = (color: string) => {\n    onFormatChange({ color });\n    onApplyFormatting('foreColor', color);\n    setShowColorPicker(false);\n  };\n\n  const handleBackgroundColorChange = (color: string) => {\n    onFormatChange({ backgroundColor: color });\n    onApplyFormatting('backColor', color);\n    setShowBackgroundPicker(false);\n  };\n\n  const handleLineHeightChange = (lineHeight: string) => {\n    onFormatChange({ lineHeight });\n  };\n\n  const handleLetterSpacingChange = (letterSpacing: string) => {\n    onFormatChange({ letterSpacing });\n  };\n\n  const insertList = (ordered: boolean) => {\n    onApplyFormatting(ordered ? 'insertOrderedList' : 'insertUnorderedList');\n  };\n\n  const insertLink = () => {\n    const url = prompt('Enter URL:');\n    if (url) {\n      onApplyFormatting('createLink', url);\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"text-toolbar\">\n      <div className=\"toolbar-section\">\n        <label className=\"toolbar-label\">Text Toolbar</label>\n      </div>\n\n      {/* Font Family */}\n      <div className=\"toolbar-section\">\n        <select\n          className=\"font-family-select\"\n          value={currentFormatting.fontFamily}\n          onChange={(e) => handleFontFamilyChange(e.target.value)}\n        >\n          {fontFamilies.map(font => (\n            <option key={font} value={font} style={{ fontFamily: font }}>\n              {font}\n            </option>\n          ))}\n        </select>\n      </div>\n\n      {/* Font Size */}\n      <div className=\"toolbar-section\">\n        <select\n          className=\"font-size-select\"\n          value={currentFormatting.fontSize}\n          onChange={(e) => handleFontSizeChange(e.target.value)}\n        >\n          {fontSizes.map(size => (\n            <option key={size} value={size}>{size}pt</option>\n          ))}\n        </select>\n      </div>\n\n      {/* Bold, Italic, Underline */}\n      <div className=\"toolbar-section format-buttons\">\n        <button\n          className={`format-btn ${currentFormatting.bold ? 'active' : ''}`}\n          onClick={handleBoldClick}\n          title=\"Bold\"\n        >\n          <strong>B</strong>\n        </button>\n        <button\n          className={`format-btn ${currentFormatting.italic ? 'active' : ''}`}\n          onClick={handleItalicClick}\n          title=\"Italic\"\n        >\n          <em>I</em>\n        </button>\n        <button\n          className={`format-btn ${currentFormatting.underline ? 'active' : ''}`}\n          onClick={handleUnderlineClick}\n          title=\"Underline\"\n        >\n          <u>U</u>\n        </button>\n      </div>\n\n      {/* Text Alignment */}\n      <div className=\"toolbar-section alignment-buttons\">\n        <button\n          className={`format-btn ${currentFormatting.textAlign === 'left' ? 'active' : ''}`}\n          onClick={() => handleAlignClick('left')}\n          title=\"Align Left\"\n        >\n          ⌐\n        </button>\n        <button\n          className={`format-btn ${currentFormatting.textAlign === 'center' ? 'active' : ''}`}\n          onClick={() => handleAlignClick('center')}\n          title=\"Align Center\"\n        >\n          ≡\n        </button>\n        <button\n          className={`format-btn ${currentFormatting.textAlign === 'right' ? 'active' : ''}`}\n          onClick={() => handleAlignClick('right')}\n          title=\"Align Right\"\n        >\n          ¬\n        </button>\n        <button\n          className={`format-btn ${currentFormatting.textAlign === 'justify' ? 'active' : ''}`}\n          onClick={() => handleAlignClick('justify')}\n          title=\"Justify\"\n        >\n          ≣\n        </button>\n      </div>\n\n      {/* Lists */}\n      <div className=\"toolbar-section list-buttons\">\n        <button\n          className=\"format-btn\"\n          onClick={() => insertList(false)}\n          title=\"Bullet List\"\n        >\n          •\n        </button>\n        <button\n          className=\"format-btn\"\n          onClick={() => insertList(true)}\n          title=\"Numbered List\"\n        >\n          1.\n        </button>\n      </div>\n\n      {/* Colors */}\n      <div className=\"toolbar-section color-section\">\n        <div className=\"color-picker-container\" ref={colorPickerRef}>\n          <button\n            className=\"color-btn\"\n            onClick={() => setShowColorPicker(!showColorPicker)}\n            title=\"Text Color\"\n          >\n            <span className=\"color-indicator\" style={{ backgroundColor: currentFormatting.color }}>A</span>\n          </button>\n          {showColorPicker && (\n            <div className=\"color-picker\">\n              <div className=\"color-grid\">\n                {commonColors.map(color => (\n                  <button\n                    key={color}\n                    className=\"color-option\"\n                    style={{ backgroundColor: color }}\n                    onClick={() => handleColorChange(color)}\n                    title={color}\n                  />\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n\n        <div className=\"color-picker-container\">\n          <button\n            className=\"color-btn\"\n            onClick={() => setShowBackgroundPicker(!showBackgroundPicker)}\n            title=\"Background Color\"\n          >\n            <span className=\"color-indicator bg-indicator\" style={{ backgroundColor: currentFormatting.backgroundColor }}>■</span>\n          </button>\n          {showBackgroundPicker && (\n            <div className=\"color-picker\">\n              <div className=\"color-grid\">\n                {commonColors.map(color => (\n                  <button\n                    key={color}\n                    className=\"color-option\"\n                    style={{ backgroundColor: color }}\n                    onClick={() => handleBackgroundColorChange(color)}\n                    title={color}\n                  />\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Line Height */}\n      <div className=\"toolbar-section\">\n        <label className=\"toolbar-micro-label\">Leading</label>\n        <select\n          className=\"line-height-select\"\n          value={currentFormatting.lineHeight}\n          onChange={(e) => handleLineHeightChange(e.target.value)}\n        >\n          {lineHeights.map(height => (\n            <option key={height} value={height}>{height}</option>\n          ))}\n        </select>\n      </div>\n\n      {/* Letter Spacing */}\n      <div className=\"toolbar-section\">\n        <label className=\"toolbar-micro-label\">Kerning</label>\n        <select\n          className=\"letter-spacing-select\"\n          value={currentFormatting.letterSpacing}\n          onChange={(e) => handleLetterSpacingChange(e.target.value)}\n        >\n          {letterSpacings.map(spacing => (\n            <option key={spacing} value={spacing}>{spacing}</option>\n          ))}\n        </select>\n      </div>\n\n      {/* Link */}\n      <div className=\"toolbar-section\">\n        <button\n          className=\"format-btn link-btn\"\n          onClick={insertLink}\n          title=\"Insert Link\"\n        >\n          🔗\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default TextToolbar;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,MAAO,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAsB3B,KAAM,CAAAC,WAAuC,CAAGC,IAAA,EAK1C,IAL2C,CAC/CC,SAAS,CACTC,iBAAiB,CACjBC,cAAc,CACdC,iBACF,CAAC,CAAAJ,IAAA,CACC,KAAM,CAACK,eAAe,CAAEC,kBAAkB,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACe,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGhB,QAAQ,CAAC,KAAK,CAAC,CACvE,KAAM,CAAAiB,cAAc,CAAGhB,MAAM,CAAiB,IAAI,CAAC,CAEnD,KAAM,CAAAiB,YAAY,CAAG,CACnB,OAAO,CAAE,iBAAiB,CAAE,WAAW,CAAE,SAAS,CAAE,SAAS,CAC7D,cAAc,CAAE,aAAa,CAAE,QAAQ,CAAE,eAAe,CAAE,QAAQ,CACnE,CAED,KAAM,CAAAC,SAAS,CAAG,CAChB,GAAG,CAAE,GAAG,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CACzG,CAED,KAAM,CAAAC,WAAW,CAAG,CAAC,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAC,CAC5E,KAAM,CAAAC,cAAc,CAAG,CAAC,GAAG,CAAE,OAAO,CAAE,KAAK,CAAE,OAAO,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAC,CAEjF,KAAM,CAAAC,YAAY,CAAG,CACnB,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CACtF,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CACtF,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CACvF,CAEDpB,SAAS,CAAC,IAAM,CACd,KAAM,CAAAqB,kBAAkB,CAAIC,KAAiB,EAAK,CAChD,GAAIP,cAAc,CAACQ,OAAO,EAAI,CAACR,cAAc,CAACQ,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAc,CAAC,CAAE,CACpFb,kBAAkB,CAAC,KAAK,CAAC,CACzBE,uBAAuB,CAAC,KAAK,CAAC,CAChC,CACF,CAAC,CAEDY,QAAQ,CAACC,gBAAgB,CAAC,WAAW,CAAEN,kBAAkB,CAAC,CAC1D,MAAO,IAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,CAAEP,kBAAkB,CAAC,CAC5E,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAQ,sBAAsB,CAAIC,UAAkB,EAAK,CACrDrB,cAAc,CAAC,CAAEqB,UAAW,CAAC,CAAC,CAC9BpB,iBAAiB,CAAC,UAAU,CAAEoB,UAAU,CAAC,CAC3C,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAIC,QAAgB,EAAK,CACjDvB,cAAc,CAAC,CAAEuB,QAAS,CAAC,CAAC,CAC5BtB,iBAAiB,CAAC,UAAU,CAAEsB,QAAQ,CAAC,CACzC,CAAC,CAED,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,OAAO,CAAG,CAAC1B,iBAAiB,CAAC2B,IAAI,CACvC1B,cAAc,CAAC,CAAE0B,IAAI,CAAED,OAAQ,CAAC,CAAC,CACjCxB,iBAAiB,CAAC,MAAM,CAAC,CAC3B,CAAC,CAED,KAAM,CAAA0B,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAAC,SAAS,CAAG,CAAC7B,iBAAiB,CAAC8B,MAAM,CAC3C7B,cAAc,CAAC,CAAE6B,MAAM,CAAED,SAAU,CAAC,CAAC,CACrC3B,iBAAiB,CAAC,QAAQ,CAAC,CAC7B,CAAC,CAED,KAAM,CAAA6B,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAC,YAAY,CAAG,CAAChC,iBAAiB,CAACiC,SAAS,CACjDhC,cAAc,CAAC,CAAEgC,SAAS,CAAED,YAAa,CAAC,CAAC,CAC3C9B,iBAAiB,CAAC,WAAW,CAAC,CAChC,CAAC,CAED,KAAM,CAAAgC,gBAAgB,CAAIC,KAA8C,EAAK,CAC3ElC,cAAc,CAAC,CAAEmC,SAAS,CAAED,KAAM,CAAC,CAAC,CACpC,KAAM,CAAAE,UAAqC,CAAG,CAC5C,MAAM,CAAE,aAAa,CACrB,QAAQ,CAAE,eAAe,CACzB,OAAO,CAAE,cAAc,CACvB,SAAS,CAAE,aACb,CAAC,CACDnC,iBAAiB,CAACmC,UAAU,CAACF,KAAK,CAAC,CAAC,CACtC,CAAC,CAED,KAAM,CAAAG,iBAAiB,CAAIC,KAAa,EAAK,CAC3CtC,cAAc,CAAC,CAAEsC,KAAM,CAAC,CAAC,CACzBrC,iBAAiB,CAAC,WAAW,CAAEqC,KAAK,CAAC,CACrCnC,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAoC,2BAA2B,CAAID,KAAa,EAAK,CACrDtC,cAAc,CAAC,CAAEwC,eAAe,CAAEF,KAAM,CAAC,CAAC,CAC1CrC,iBAAiB,CAAC,WAAW,CAAEqC,KAAK,CAAC,CACrCjC,uBAAuB,CAAC,KAAK,CAAC,CAChC,CAAC,CAED,KAAM,CAAAoC,sBAAsB,CAAIC,UAAkB,EAAK,CACrD1C,cAAc,CAAC,CAAE0C,UAAW,CAAC,CAAC,CAChC,CAAC,CAED,KAAM,CAAAC,yBAAyB,CAAIC,aAAqB,EAAK,CAC3D5C,cAAc,CAAC,CAAE4C,aAAc,CAAC,CAAC,CACnC,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIC,OAAgB,EAAK,CACvC7C,iBAAiB,CAAC6C,OAAO,CAAG,mBAAmB,CAAG,qBAAqB,CAAC,CAC1E,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAAAC,GAAG,CAAGC,MAAM,CAAC,YAAY,CAAC,CAChC,GAAID,GAAG,CAAE,CACP/C,iBAAiB,CAAC,YAAY,CAAE+C,GAAG,CAAC,CACtC,CACF,CAAC,CAED,GAAI,CAAClD,SAAS,CAAE,MAAO,KAAI,CAE3B,mBACEH,KAAA,QAAKuD,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B1D,IAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B1D,IAAA,UAAOyD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,cAAY,CAAO,CAAC,CAClD,CAAC,cAGN1D,IAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B1D,IAAA,WACEyD,SAAS,CAAC,oBAAoB,CAC9BE,KAAK,CAAErD,iBAAiB,CAACsB,UAAW,CACpCgC,QAAQ,CAAGC,CAAC,EAAKlC,sBAAsB,CAACkC,CAAC,CAACtC,MAAM,CAACoC,KAAK,CAAE,CAAAD,QAAA,CAEvD5C,YAAY,CAACgD,GAAG,CAACC,IAAI,eACpB/D,IAAA,WAAmB2D,KAAK,CAAEI,IAAK,CAACC,KAAK,CAAE,CAAEpC,UAAU,CAAEmC,IAAK,CAAE,CAAAL,QAAA,CACzDK,IAAI,EADMA,IAEL,CACT,CAAC,CACI,CAAC,CACN,CAAC,cAGN/D,IAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B1D,IAAA,WACEyD,SAAS,CAAC,kBAAkB,CAC5BE,KAAK,CAAErD,iBAAiB,CAACwB,QAAS,CAClC8B,QAAQ,CAAGC,CAAC,EAAKhC,oBAAoB,CAACgC,CAAC,CAACtC,MAAM,CAACoC,KAAK,CAAE,CAAAD,QAAA,CAErD3C,SAAS,CAAC+C,GAAG,CAACG,IAAI,eACjB/D,KAAA,WAAmByD,KAAK,CAAEM,IAAK,CAAAP,QAAA,EAAEO,IAAI,CAAC,IAAE,GAA3BA,IAAmC,CACjD,CAAC,CACI,CAAC,CACN,CAAC,cAGN/D,KAAA,QAAKuD,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C1D,IAAA,WACEyD,SAAS,eAAAS,MAAA,CAAgB5D,iBAAiB,CAAC2B,IAAI,CAAG,QAAQ,CAAG,EAAE,CAAG,CAClEkC,OAAO,CAAEpC,eAAgB,CACzBqC,KAAK,CAAC,MAAM,CAAAV,QAAA,cAEZ1D,IAAA,WAAA0D,QAAA,CAAQ,GAAC,CAAQ,CAAC,CACZ,CAAC,cACT1D,IAAA,WACEyD,SAAS,eAAAS,MAAA,CAAgB5D,iBAAiB,CAAC8B,MAAM,CAAG,QAAQ,CAAG,EAAE,CAAG,CACpE+B,OAAO,CAAEjC,iBAAkB,CAC3BkC,KAAK,CAAC,QAAQ,CAAAV,QAAA,cAEd1D,IAAA,OAAA0D,QAAA,CAAI,GAAC,CAAI,CAAC,CACJ,CAAC,cACT1D,IAAA,WACEyD,SAAS,eAAAS,MAAA,CAAgB5D,iBAAiB,CAACiC,SAAS,CAAG,QAAQ,CAAG,EAAE,CAAG,CACvE4B,OAAO,CAAE9B,oBAAqB,CAC9B+B,KAAK,CAAC,WAAW,CAAAV,QAAA,cAEjB1D,IAAA,MAAA0D,QAAA,CAAG,GAAC,CAAG,CAAC,CACF,CAAC,EACN,CAAC,cAGNxD,KAAA,QAAKuD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD1D,IAAA,WACEyD,SAAS,eAAAS,MAAA,CAAgB5D,iBAAiB,CAACoC,SAAS,GAAK,MAAM,CAAG,QAAQ,CAAG,EAAE,CAAG,CAClFyB,OAAO,CAAEA,CAAA,GAAM3B,gBAAgB,CAAC,MAAM,CAAE,CACxC4B,KAAK,CAAC,YAAY,CAAAV,QAAA,CACnB,QAED,CAAQ,CAAC,cACT1D,IAAA,WACEyD,SAAS,eAAAS,MAAA,CAAgB5D,iBAAiB,CAACoC,SAAS,GAAK,QAAQ,CAAG,QAAQ,CAAG,EAAE,CAAG,CACpFyB,OAAO,CAAEA,CAAA,GAAM3B,gBAAgB,CAAC,QAAQ,CAAE,CAC1C4B,KAAK,CAAC,cAAc,CAAAV,QAAA,CACrB,QAED,CAAQ,CAAC,cACT1D,IAAA,WACEyD,SAAS,eAAAS,MAAA,CAAgB5D,iBAAiB,CAACoC,SAAS,GAAK,OAAO,CAAG,QAAQ,CAAG,EAAE,CAAG,CACnFyB,OAAO,CAAEA,CAAA,GAAM3B,gBAAgB,CAAC,OAAO,CAAE,CACzC4B,KAAK,CAAC,aAAa,CAAAV,QAAA,CACpB,MAED,CAAQ,CAAC,cACT1D,IAAA,WACEyD,SAAS,eAAAS,MAAA,CAAgB5D,iBAAiB,CAACoC,SAAS,GAAK,SAAS,CAAG,QAAQ,CAAG,EAAE,CAAG,CACrFyB,OAAO,CAAEA,CAAA,GAAM3B,gBAAgB,CAAC,SAAS,CAAE,CAC3C4B,KAAK,CAAC,SAAS,CAAAV,QAAA,CAChB,QAED,CAAQ,CAAC,EACN,CAAC,cAGNxD,KAAA,QAAKuD,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3C1D,IAAA,WACEyD,SAAS,CAAC,YAAY,CACtBU,OAAO,CAAEA,CAAA,GAAMf,UAAU,CAAC,KAAK,CAAE,CACjCgB,KAAK,CAAC,aAAa,CAAAV,QAAA,CACpB,QAED,CAAQ,CAAC,cACT1D,IAAA,WACEyD,SAAS,CAAC,YAAY,CACtBU,OAAO,CAAEA,CAAA,GAAMf,UAAU,CAAC,IAAI,CAAE,CAChCgB,KAAK,CAAC,eAAe,CAAAV,QAAA,CACtB,IAED,CAAQ,CAAC,EACN,CAAC,cAGNxD,KAAA,QAAKuD,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CxD,KAAA,QAAKuD,SAAS,CAAC,wBAAwB,CAACY,GAAG,CAAExD,cAAe,CAAA6C,QAAA,eAC1D1D,IAAA,WACEyD,SAAS,CAAC,WAAW,CACrBU,OAAO,CAAEA,CAAA,GAAMzD,kBAAkB,CAAC,CAACD,eAAe,CAAE,CACpD2D,KAAK,CAAC,YAAY,CAAAV,QAAA,cAElB1D,IAAA,SAAMyD,SAAS,CAAC,iBAAiB,CAACO,KAAK,CAAE,CAAEjB,eAAe,CAAEzC,iBAAiB,CAACuC,KAAM,CAAE,CAAAa,QAAA,CAAC,GAAC,CAAM,CAAC,CACzF,CAAC,CACRjD,eAAe,eACdT,IAAA,QAAKyD,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3B1D,IAAA,QAAKyD,SAAS,CAAC,YAAY,CAAAC,QAAA,CACxBxC,YAAY,CAAC4C,GAAG,CAACjB,KAAK,eACrB7C,IAAA,WAEEyD,SAAS,CAAC,cAAc,CACxBO,KAAK,CAAE,CAAEjB,eAAe,CAAEF,KAAM,CAAE,CAClCsB,OAAO,CAAEA,CAAA,GAAMvB,iBAAiB,CAACC,KAAK,CAAE,CACxCuB,KAAK,CAAEvB,KAAM,EAJRA,KAKN,CACF,CAAC,CACC,CAAC,CACH,CACN,EACE,CAAC,cAEN3C,KAAA,QAAKuD,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC1D,IAAA,WACEyD,SAAS,CAAC,WAAW,CACrBU,OAAO,CAAEA,CAAA,GAAMvD,uBAAuB,CAAC,CAACD,oBAAoB,CAAE,CAC9DyD,KAAK,CAAC,kBAAkB,CAAAV,QAAA,cAExB1D,IAAA,SAAMyD,SAAS,CAAC,8BAA8B,CAACO,KAAK,CAAE,CAAEjB,eAAe,CAAEzC,iBAAiB,CAACyC,eAAgB,CAAE,CAAAW,QAAA,CAAC,QAAC,CAAM,CAAC,CAChH,CAAC,CACR/C,oBAAoB,eACnBX,IAAA,QAAKyD,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3B1D,IAAA,QAAKyD,SAAS,CAAC,YAAY,CAAAC,QAAA,CACxBxC,YAAY,CAAC4C,GAAG,CAACjB,KAAK,eACrB7C,IAAA,WAEEyD,SAAS,CAAC,cAAc,CACxBO,KAAK,CAAE,CAAEjB,eAAe,CAAEF,KAAM,CAAE,CAClCsB,OAAO,CAAEA,CAAA,GAAMrB,2BAA2B,CAACD,KAAK,CAAE,CAClDuB,KAAK,CAAEvB,KAAM,EAJRA,KAKN,CACF,CAAC,CACC,CAAC,CACH,CACN,EACE,CAAC,EACH,CAAC,cAGN3C,KAAA,QAAKuD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B1D,IAAA,UAAOyD,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cACtD1D,IAAA,WACEyD,SAAS,CAAC,oBAAoB,CAC9BE,KAAK,CAAErD,iBAAiB,CAAC2C,UAAW,CACpCW,QAAQ,CAAGC,CAAC,EAAKb,sBAAsB,CAACa,CAAC,CAACtC,MAAM,CAACoC,KAAK,CAAE,CAAAD,QAAA,CAEvD1C,WAAW,CAAC8C,GAAG,CAACQ,MAAM,eACrBtE,IAAA,WAAqB2D,KAAK,CAAEW,MAAO,CAAAZ,QAAA,CAAEY,MAAM,EAA9BA,MAAuC,CACrD,CAAC,CACI,CAAC,EACN,CAAC,cAGNpE,KAAA,QAAKuD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B1D,IAAA,UAAOyD,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cACtD1D,IAAA,WACEyD,SAAS,CAAC,uBAAuB,CACjCE,KAAK,CAAErD,iBAAiB,CAAC6C,aAAc,CACvCS,QAAQ,CAAGC,CAAC,EAAKX,yBAAyB,CAACW,CAAC,CAACtC,MAAM,CAACoC,KAAK,CAAE,CAAAD,QAAA,CAE1DzC,cAAc,CAAC6C,GAAG,CAACS,OAAO,eACzBvE,IAAA,WAAsB2D,KAAK,CAAEY,OAAQ,CAAAb,QAAA,CAAEa,OAAO,EAAjCA,OAA0C,CACxD,CAAC,CACI,CAAC,EACN,CAAC,cAGNvE,IAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B1D,IAAA,WACEyD,SAAS,CAAC,qBAAqB,CAC/BU,OAAO,CAAEb,UAAW,CACpBc,KAAK,CAAC,aAAa,CAAAV,QAAA,CACpB,cAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAvD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}