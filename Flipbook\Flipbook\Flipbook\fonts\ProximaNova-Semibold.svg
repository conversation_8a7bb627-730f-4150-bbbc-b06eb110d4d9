<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg  PUBLIC '-//W3C//DTD SVG 1.1//EN'  'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>
<svg xmlns="http://www.w3.org/2000/svg" version="1.1">
<defs>
<font id="ProximaNova-Semibold" horiz-adv-x="574">
<font-face units-per-em="1000" bbox="-101 -199 912 898" x-height="483" underline-thickness="20" unicode-range="U+0020-00FE" ascent="790" font-weight="600" cap-height="667" panose-1="2 0 5 6 3 0 0 2 0 4" font-family="a" descent="-210" underline-position="-153"/>
<missing-glyph/>
<glyph horiz-adv-x="258" unicode=" " glyph-name="space"/>
<glyph d="M168 205h-91l-19 462h128zM123 -10q-29 0 -49.5 21t-20.5 49t21 48.5t49 20.5t48.5 -20.5t20.5 -48.5q0 -29 -20.5 -49.5t-48.5 -20.5z" horiz-adv-x="244" unicode="!" glyph-name="exclam"/>
<glyph d="M131 391h-48q-35 210 -35 227q0 25 17.5 42t41.5 17q25 0 42 -17t17 -42zM297 391h-48q-35 210 -35 227q0 25 17 42t42 17t42 -17t17 -42z" horiz-adv-x="380" unicode='&quot;' glyph-name="quotedbl"/>
<glyph d="M322 0h-80l59 175h-102l-58 -175h-80l59 175h-101l22 69h102l60 179h-102l21 69h104l58 175h80l-58 -175h101l58 175h80l-59 -175h102l-20 -69h-104l-61 -179h106l-22 -69h-107zM323 244l61 179h-101l-60 -179h100z" horiz-adv-x="607" unicode="#" glyph-name="numbersign"/>
<glyph d="M269 -100v90q-144 11 -233 104l66 91q68 -75 167 -90v194q-30 8 -52 14.5t-48.5 18t-44.5 25.5t-34 33t-24 44.5t-8 57.5q0 78 57.5 132t153.5 62v92h80v-93q120 -13 201 -91l-66 -87q-55 55 -135 71v-173q37 -10 63 -18.5t57.5 -25t50.5 -36t32.5 -50.5t13.5 -70 q0 -82 -55 -139t-162 -66v-90h-80zM446 184q0 31 -25 50.5t-72 33.5v-174q49 7 73 32.5t24 57.5zM178 491q0 -48 91 -75v157q-42 -5 -66.5 -27t-24.5 -55z" horiz-adv-x="605" unicode="$" glyph-name="dollar"/>
<glyph d="M194 346q-73 0 -119.5 47t-46.5 118t46.5 118.5t119.5 47.5q74 0 120 -47t46 -119q0 -71 -46 -118t-120 -47zM196 0h-67l426 667h68zM553 -12q-73 0 -119.5 47t-46.5 118t46.5 118.5t119.5 47.5t119.5 -47.5t46.5 -118.5t-46.5 -118t-119.5 -47zM194 413q39 0 64 27.5 t25 70.5q0 44 -25 72t-64 28q-38 0 -63 -28t-25 -72q0 -43 25 -70.5t63 -27.5zM553 55q39 0 64 27.5t25 70.5q0 44 -25 72t-64 28t-64 -28t-25 -72q0 -43 25 -70.5t64 -27.5z" horiz-adv-x="747" unicode="%" glyph-name="percent"/>
<glyph d="M628 0h-138q-34 32 -59 58q-83 -70 -187 -70q-93 0 -153.5 49.5t-60.5 140.5q0 71 38.5 116t106.5 79q-47 84 -47 147q0 67 51.5 112t129.5 45q70 0 117 -36.5t47 -99.5q0 -36 -13 -65t-40 -51.5t-50.5 -36t-62.5 -32.5q28 -39 66 -83q37 -45 66 -77q43 64 66 140l85 -39 q-41 -97 -92 -166q70 -74 130 -131zM258 72q58 0 115 47q-56 59 -82 93q-41 47 -74 95q-76 -50 -76 -120q0 -53 34 -84t83 -31zM233 517q0 -42 33 -100q56 26 85 53t29 65q0 30 -19 47.5t-48 17.5q-34 0 -57 -23.5t-23 -59.5z" horiz-adv-x="645" unicode="&amp;" glyph-name="ampersand"/>
<glyph d="M131 391h-48q-35 210 -35 227q0 25 17.5 42t41.5 17q25 0 42 -17t17 -42z" horiz-adv-x="214" unicode="&apos;" glyph-name="quotesingle"/>
<glyph d="M259 -149l-65 -50q-71 86 -111.5 203.5t-40.5 238.5t40.5 238.5t111.5 203.5l65 -49q-53 -98 -78 -188.5t-25 -204.5q0 -113 25 -204t78 -188z" horiz-adv-x="276" unicode="(" glyph-name="parenleft"/>
<glyph d="M18 -149l65 -50q71 86 111 203.5t40 238.5t-40 238.5t-111 203.5l-65 -49q52 -98 77 -189t25 -204q0 -112 -25 -203.5t-77 -188.5z" horiz-adv-x="277" unicode=")" glyph-name="parenright"/>
<glyph d="M201 376h-55l5 112l-94 -60l-28 48l99 51l-99 51l28 48l94 -61l-5 112h55l-5 -112l94 61l28 -48l-100 -51l100 -51l-28 -48l-94 60z" horiz-adv-x="347" unicode="*" glyph-name="asterisk"/>
<glyph d="M473 304h-185v-208h-74v208h-185v67h185v201h74v-201h185v-67z" horiz-adv-x="502" unicode="+" glyph-name="plus"/>
<glyph d="M198 38q0 -51 -25.5 -96t-67.5 -75l-48 39q25 15 46.5 42t25.5 52q-6 -3 -17 -3q-25 0 -42 18t-17 46t20 48t48 20q32 0 54.5 -24.5t22.5 -66.5z" horiz-adv-x="245" unicode="," glyph-name="comma"/>
<glyph d="M270 197h-240v90h240v-90z" horiz-adv-x="300" unicode="-" glyph-name="hyphen"/>
<glyph d="M123 -11q-28 0 -49 21t-21 50q0 28 21 48.5t49 20.5t49 -20.5t21 -48.5q0 -29 -21 -50t-49 -21z" horiz-adv-x="246" unicode="." glyph-name="period"/>
<glyph d="M78 -20h-78l237 707h78z" horiz-adv-x="315" unicode="/" glyph-name="slash"/>
<glyph d="M309 -12q-68 0 -120.5 30t-83.5 80t-47 110t-16 125t15.5 125t47 110t84 79.5t120.5 29.5q89 0 151 -52t88.5 -127.5t26.5 -164.5q0 -88 -26.5 -164t-88.5 -128.5t-151 -52.5zM309 92q76 0 111.5 68t35.5 173q0 68 -13.5 119.5t-47.5 86t-86 34.5t-86.5 -34.5t-48 -86 t-13.5 -119.5q0 -105 36 -173t112 -68z" horiz-adv-x="617" unicode="0" glyph-name="zero"/>
<glyph d="M309 0h-117v516l-109 -114l-68 71l192 194h102v-667z" horiz-adv-x="380" unicode="1" glyph-name="one"/>
<glyph d="M533 0h-483v93q211 160 286 235t75 144q0 49 -36 75t-86 26q-109 0 -180 -79l-68 76q43 52 108.5 79.5t138.5 27.5q102 0 171.5 -54.5t69.5 -150.5q0 -87 -71.5 -172t-219.5 -197h295v-103z" horiz-adv-x="595" unicode="2" glyph-name="two"/>
<glyph d="M276 -12q-84 0 -149.5 30t-102.5 78l64 77q32 -38 81.5 -59.5t102.5 -21.5q65 0 102 28t37 74q0 95 -148 95q-56 0 -76 -2v106q11 -1 76 -1q62 0 100 22t38 66t-38.5 68.5t-96.5 24.5q-101 0 -173 -74l-60 73q90 105 244 105q109 0 174.5 -48t65.5 -131 q0 -63 -43.5 -103.5t-100.5 -49.5q57 -6 106 -49t49 -113q0 -87 -68.5 -141t-183.5 -54z" unicode="3" glyph-name="three"/>
<glyph d="M463 0h-117v151h-318v94l274 422h161v-413h89v-103h-89v-151zM346 254v308l-203 -308h203z" horiz-adv-x="580" unicode="4" glyph-name="four"/>
<glyph d="M304 -12q-159 0 -247 103l67 81q68 -80 179 -80q62 0 99.5 33.5t37.5 84.5q0 53 -36.5 85.5t-97.5 32.5q-85 0 -146 -57l-83 24v372h437v-103h-320v-192q61 57 151 57q89 0 151.5 -58t62.5 -157q0 -102 -71 -164t-184 -62z" horiz-adv-x="600" unicode="5" glyph-name="five"/>
<glyph d="M317 -12q-94 0 -157.5 48.5t-90.5 124t-27 172.5q0 150 77 247t217 97q117 0 191 -74l-54 -88q-56 58 -137 58t-128.5 -64t-47.5 -156v-19q26 37 75 65.5t104 28.5q95 0 159.5 -57t64.5 -159q0 -95 -68.5 -159.5t-177.5 -64.5zM310 92q62 0 98 36t36 80q0 58 -39 88.5 t-97 30.5q-43 0 -82 -21t-64 -57q5 -63 41.5 -110t106.5 -47z" horiz-adv-x="601" unicode="6" glyph-name="six"/>
<glyph d="M250 0h-129l252 564h-346v103h481v-81z" horiz-adv-x="532" unicode="7" glyph-name="seven"/>
<glyph d="M298 -12q-108 0 -182 49t-74 135q0 60 42.5 105t107.5 67q-62 20 -100.5 59.5t-38.5 98.5q0 86 73 130.5t172 44.5t172 -44.5t73 -130.5q0 -59 -38.5 -98.5t-100.5 -59.5q65 -22 107.5 -67t42.5 -105q0 -86 -74 -135t-182 -49zM298 391q19 3 37.5 9t40 16.5t35 28 t13.5 39.5q0 42 -35 65.5t-91 23.5t-91 -23.5t-35 -65.5q0 -22 13.5 -39.5t35 -28t40 -16.5t37.5 -9zM298 92q58 0 97.5 26t39.5 69q0 45 -45.5 71.5t-91.5 33.5q-46 -7 -91.5 -33.5t-45.5 -71.5q0 -43 39 -69t98 -26z" horiz-adv-x="596" unicode="8" glyph-name="eight"/>
<glyph d="M266 -11q-116 0 -192 74l55 89q56 -59 137 -59q85 0 130.5 65t45.5 155q0 13 -1 19q-25 -37 -74 -65t-105 -28q-95 0 -159 56.5t-64 158.5q0 95 68 159.5t178 64.5q71 0 125 -27.5t86 -75.5t48 -108.5t16 -132.5q0 -151 -77 -248t-217 -97zM294 339q42 0 81 21.5t65 57.5 q-6 64 -42 110t-107 46q-62 0 -98 -35.5t-36 -80.5q0 -58 39 -88.5t98 -30.5z" horiz-adv-x="601" unicode="9" glyph-name="nine"/>
<glyph d="M123 351q-28 0 -49 20.5t-21 48.5t20.5 49t49.5 21t49.5 -21t20.5 -49t-21 -48.5t-49 -20.5zM123 -11q-28 0 -49 21t-21 50q0 28 21 48.5t49 20.5t49 -20.5t21 -48.5q0 -29 -21 -50t-49 -21z" horiz-adv-x="237" unicode=":" glyph-name="colon"/>
<glyph d="M123 351q-29 0 -49.5 20.5t-20.5 49.5q0 28 20.5 48.5t49.5 20.5t49.5 -20.5t20.5 -48.5q0 -29 -20.5 -49.5t-49.5 -20.5zM198 41q0 -51 -25.5 -96t-67.5 -75l-48 39q25 15 46.5 42t25.5 52q-4 -2 -17 -2q-25 0 -42 18t-17 45q0 28 20 48t48 20q32 0 54.5 -24.5 t22.5 -66.5z" horiz-adv-x="245" unicode=";" glyph-name="semicolon"/>
<glyph d="M473 90l-444 207v76l444 207v-80l-364 -166l364 -163v-81z" horiz-adv-x="502" unicode="&lt;" glyph-name="less"/>
<glyph d="M473 396h-444v67h444v-67zM473 205h-444v68h444v-68z" horiz-adv-x="502" unicode="=" glyph-name="equal"/>
<glyph d="M473 297l-444 -207v81l365 163l-365 166v80l444 -207v-76z" horiz-adv-x="502" unicode="&gt;" glyph-name="greater"/>
<glyph d="M189 196q-35 34 -35 86q0 34 16.5 61t40 45t47.5 34t40.5 36t16.5 43q0 31 -24.5 51.5t-70.5 20.5q-85 0 -139 -70l-68 75q80 99 218 99q93 0 148 -44t55 -113q0 -41 -18.5 -73.5t-44.5 -53t-52 -38.5t-44.5 -39t-18.5 -45q0 -29 22 -45zM225 -11q-28 0 -49 21t-21 50 q0 28 21 48.5t49 20.5t49 -20.5t21 -48.5q0 -29 -21 -50t-49 -21z" horiz-adv-x="459" unicode="?" glyph-name="question"/>
<glyph d="M352 -70q-136 0 -226.5 90.5t-90.5 223.5q0 160 120 277t277 117q140 0 227.5 -90t87.5 -221q0 -113 -57.5 -177.5t-129.5 -64.5q-41 0 -65.5 22.5t-28.5 54.5l-1 7q-27 -38 -67 -61t-83 -23q-71 0 -113 46t-42 120q0 100 70.5 174t159.5 74t127 -72l11 57h94l-54 -256 q-2 -10 -2 -22q0 -23 11.5 -35.5t29.5 -12.5q36 0 68.5 42.5t32.5 127.5q0 121 -76.5 197t-202.5 76q-143 0 -249.5 -107t-106.5 -248q0 -119 80.5 -199.5t203.5 -80.5q97 0 183 56l20 -29q-98 -63 -208 -63zM349 161q74 0 127 76l26 126q-9 23 -32 41.5t-58 18.5 q-65 0 -109.5 -50t-44.5 -115q0 -43 24.5 -70t66.5 -27z" horiz-adv-x="783" unicode="@" glyph-name="at"/>
<glyph d="M672 0h-133l-49 129h-306l-49 -129h-133l262 667h146zM457 232l-120 318l-120 -318h240z" horiz-adv-x="673" unicode="A" glyph-name="A"/>
<glyph d="M408 0h-337v667h328q89 0 139.5 -48.5t50.5 -121.5q0 -60 -33.5 -101.5t-82.5 -51.5q54 -8 91.5 -55t37.5 -108q0 -81 -51 -131t-143 -50zM376 392q44 0 68.5 23.5t24.5 62.5q0 38 -25 62t-68 24h-188v-172h188zM381 103q47 0 74 24.5t27 68.5q0 40 -27 66.5t-74 26.5 h-193v-186h193z" horiz-adv-x="649" unicode="B" glyph-name="B"/>
<glyph d="M392 -12q-150 0 -250 96.5t-100 248.5t100 248.5t250 96.5q176 0 267 -149l-99 -52q-25 43 -70.5 70t-97.5 27q-99 0 -164.5 -68t-65.5 -173q0 -104 65.5 -172.5t164.5 -68.5q52 0 97.5 27.5t70.5 69.5l100 -50q-96 -151 -268 -151z" horiz-adv-x="682" unicode="C" glyph-name="C"/>
<glyph d="M320 0h-249v667h248q156 0 252.5 -93t96.5 -240q0 -148 -96 -241t-252 -93zM319 103q105 0 167 66t62 165q0 100 -60.5 165t-167.5 65h-132v-461h131z" horiz-adv-x="710" unicode="D" glyph-name="D"/>
<glyph d="M528 0h-457v667h457v-103h-340v-172h333v-103h-333v-186h340v-103z" horiz-adv-x="577" unicode="E" glyph-name="E"/>
<glyph d="M188 0h-117v667h457v-103h-340v-172h333v-103h-333v-289z" horiz-adv-x="563" unicode="F" glyph-name="F"/>
<glyph d="M392 -13q-149 0 -249.5 96t-100.5 250t100.5 249.5t249.5 95.5q172 0 268 -138l-95 -55q-28 39 -74 64t-99 25q-99 0 -164.5 -68t-65.5 -173q0 -104 65.5 -172.5t164.5 -68.5q47 0 90 18t68 42v103h-200v103h316v-249q-109 -122 -274 -122z" horiz-adv-x="717" unicode="G" glyph-name="G"/>
<glyph d="M652 0h-117v292h-347v-292h-117v667h117v-272h347v272h117v-667z" horiz-adv-x="723" unicode="H" glyph-name="H"/>
<glyph d="M188 0h-117v667h117v-667z" horiz-adv-x="259" unicode="I" glyph-name="I"/>
<glyph d="M186 -12q-110 0 -178 71l55 89q49 -56 114 -56q53 0 85 33.5t32 87.5v454h117v-455q0 -110 -62 -167t-163 -57z" horiz-adv-x="482" unicode="J" glyph-name="J"/>
<glyph d="M615 0h-145l-223 282l-59 -69v-213h-117v667h117v-317l261 317h145l-271 -314z" horiz-adv-x="621" unicode="K" glyph-name="K"/>
<glyph d="M474 0h-411v667h117v-564h294v-103z" horiz-adv-x="506" unicode="L" glyph-name="L"/>
<glyph d="M764 0h-117v515l-204 -515h-50l-205 515v-515h-117v667h165l182 -458l181 458h165v-667z" horiz-adv-x="835" unicode="M" glyph-name="M"/>
<glyph d="M649 0h-113l-348 477v-477h-117v667h120l341 -462v462h117v-667z" horiz-adv-x="720" unicode="N" glyph-name="N"/>
<glyph d="M383 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM383 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" horiz-adv-x="765" unicode="O" glyph-name="O"/>
<glyph d="M188 0h-117v667h294q101 0 159 -60t58 -149q0 -88 -58 -148t-159 -60h-177v-250zM349 353q50 0 81.5 29t31.5 76q0 48 -31.5 77t-81.5 29h-161v-211h161z" horiz-adv-x="609" unicode="P" glyph-name="P"/>
<glyph d="M383 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5q0 -143 -89 -240l55 -62l-81 -68l-58 65q-75 -40 -168 -40zM383 92q51 0 94 20l-83 94l82 68l83 -94q45 64 45 153q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68 z" horiz-adv-x="765" unicode="Q" glyph-name="Q"/>
<glyph d="M588 0h-136l-147 249h-117v-249h-117v667h293q99 0 159 -58t60 -151q0 -83 -46.5 -134t-112.5 -62zM349 352q50 0 82 29.5t32 77.5q0 47 -32 76t-82 29h-161v-212h161z" horiz-adv-x="629" unicode="R" glyph-name="R"/>
<glyph d="M300 -12q-169 0 -272 106l66 91q85 -93 211 -93q67 0 100 27t33 65q0 32 -28.5 53.5t-72 31.5t-93.5 25t-93.5 33t-72 58t-28.5 97q0 85 67 140.5t176 55.5q153 0 249 -94l-66 -87q-77 77 -194 77q-51 0 -81.5 -23t-30.5 -60q0 -25 21.5 -42.5t55.5 -28t75.5 -20 t83 -24.5t75.5 -35.5t55.5 -58t21.5 -87.5q0 -89 -65.5 -148t-192.5 -59z" horiz-adv-x="594" unicode="S" glyph-name="S"/>
<glyph d="M347 0h-117v564h-202v103h521v-103h-202v-564z" horiz-adv-x="577" unicode="T" glyph-name="T"/>
<glyph d="M360 -12q-141 0 -215 74.5t-74 201.5v403h119v-400q0 -81 44 -128t126 -47t126 47t44 128v400h119v-403q0 -128 -73.5 -202t-215.5 -74z" horiz-adv-x="720" unicode="U" glyph-name="U"/>
<glyph d="M410 0h-146l-262 667h133l202 -539l202 539h133z" horiz-adv-x="673" unicode="V" glyph-name="V"/>
<glyph d="M706 0h-125l-129 493l-130 -493h-125l-190 667h130l130 -513l138 513h93l138 -513l130 513h131z" horiz-adv-x="903" unicode="W" glyph-name="W"/>
<glyph d="M658 0h-140l-187 261l-187 -261h-140l249 342l-234 325h140l172 -244l171 244h141l-235 -324z" horiz-adv-x="661" unicode="X" glyph-name="X"/>
<glyph d="M378 0h-117v277l-259 390h134l183 -285l183 285h134l-258 -390v-277z" horiz-adv-x="637" unicode="Y" glyph-name="Y"/>
<glyph d="M545 0h-500v95l340 469h-340v103h493v-94l-342 -470h349v-103z" horiz-adv-x="589" unicode="Z" glyph-name="Z"/>
<glyph d="M247 -190h-203v868h203v-72h-125v-724h125v-72z" horiz-adv-x="265" unicode="[" glyph-name="bracketleft"/>
<glyph d="M237 -20l-237 707h78l237 -707h-78z" horiz-adv-x="315" unicode="\" glyph-name="backslash"/>
<glyph d="M221 -190h-203v72h125v724h-125v72h203v-868z" horiz-adv-x="265" unicode="]" glyph-name="bracketright"/>
<glyph d="M418 333h-80l-119 261l-119 -261h-81l162 334h77z" horiz-adv-x="437" unicode="^" glyph-name="asciicircum"/>
<glyph d="M567 -112h-570v72h570v-72z" horiz-adv-x="564" unicode="_" glyph-name="underscore"/>
<glyph d="M469 0h-105v53q-56 -65 -157 -65q-65 0 -116 43t-51 117q0 78 50 118t117 40q103 0 157 -62v72q0 41 -31.5 65.5t-81.5 24.5q-82 0 -143 -62l-44 73q81 78 203 78q89 0 145.5 -41.5t56.5 -132.5v-321zM249 60q77 0 115 50v74q-38 50 -115 50q-45 0 -74.5 -24t-29.5 -63 t29.5 -63t74.5 -24z" horiz-adv-x="537" unicode="a" glyph-name="a"/>
<glyph d="M173 145q18 -28 53 -46t72 -18q62 0 99 45t37 116t-37 115.5t-99 44.5q-37 0 -72 -18.5t-53 -46.5v-192zM173 0h-105v667h105v-251q59 79 155 79q95 0 155 -69t60 -184q0 -118 -60 -186t-155 -68t-155 78v-66z" horiz-adv-x="581" unicode="b" glyph-name="b"/>
<glyph d="M288 -12q-109 0 -179 72t-70 182t70 181.5t179 71.5q121 0 185 -86l-69 -63q-40 56 -111 56q-65 0 -105.5 -44.5t-40.5 -115.5t40.5 -116t105.5 -45q70 0 111 58l69 -65q-65 -86 -185 -86z" horiz-adv-x="498" unicode="c" glyph-name="c"/>
<glyph d="M513 0h-105v66q-60 -78 -155 -78q-94 0 -154 68t-60 186q0 115 60 184t154 69q97 0 155 -79v251h105v-667zM284 81q36 0 71 19t53 46v191q-18 27 -53 46t-71 19q-62 0 -99.5 -44.5t-37.5 -115.5t37.5 -116t99.5 -45z" horiz-adv-x="581" unicode="d" glyph-name="d"/>
<glyph d="M292 -12q-111 0 -182 70t-71 184q0 106 69.5 179.5t175.5 73.5q107 0 172 -73.5t65 -188.5v-26h-372q6 -57 47 -95t106 -38q84 0 138 54l48 -69q-76 -71 -196 -71zM420 282q-3 50 -37.5 88.5t-99.5 38.5q-62 0 -97 -38.5t-38 -88.5h272z" horiz-adv-x="557" unicode="e" glyph-name="e"/>
<glyph d="M199 0h-105v392h-80v91h80v27q0 78 41.5 122.5t110.5 44.5q77 0 116 -42l-41 -65q-23 21 -55 21q-67 0 -67 -81v-27h98v-91h-98v-392z" horiz-adv-x="309" unicode="f" glyph-name="f"/>
<glyph d="M265 -196q-123 0 -203 72l49 76q55 -62 154 -62q60 0 101.5 32t41.5 103v55q-61 -81 -155 -81q-95 0 -154.5 66t-59.5 182t59 182t155 66t155 -79v67h105v-458q0 -62 -21.5 -107.5t-59 -69t-78.5 -34t-89 -10.5zM284 93q36 0 70.5 18.5t53.5 45.5v181q-19 27 -53.5 45.5 t-70.5 18.5q-63 0 -100 -42.5t-37 -112.5t37 -112t100 -42z" horiz-adv-x="581" unicode="g" glyph-name="g"/>
<glyph d="M500 0h-105v304q0 54 -24.5 76t-72.5 22q-38 0 -71 -18.5t-53 -45.5v-338h-105v667h105v-249q27 32 72 54.5t98 22.5q156 0 156 -153v-342z" horiz-adv-x="568" unicode="h" glyph-name="h"/>
<glyph d="M121 542q-27 0 -46 19t-19 46t19 46t46 19t46 -19t19 -46t-19 -46t-46 -19zM173 0h-105v483h105v-483z" horiz-adv-x="241" unicode="i" glyph-name="i"/>
<glyph d="M121 542q-27 0 -46 19t-19 46t19 46t46 19t46 -19t19 -46t-19 -46t-46 -19zM13 -196q-40 0 -64 8t-50 27l31 78q30 -27 67 -27q71 0 71 82v511h105v-511q0 -79 -40.5 -123.5t-119.5 -44.5z" horiz-adv-x="241" unicode="j" glyph-name="j"/>
<glyph d="M520 0h-132l-147 200l-68 -70v-130h-105v667h105v-417l213 233h130l-200 -219z" horiz-adv-x="527" unicode="k" glyph-name="k"/>
<glyph d="M173 0h-105v667h105v-667z" horiz-adv-x="241" unicode="l" glyph-name="l"/>
<glyph d="M766 0h-105v315q0 87 -80 87q-33 0 -64 -19t-48 -45v-338h-105v315q0 87 -79 87q-33 0 -63.5 -19t-48.5 -45v-338h-105v483h105v-65q20 27 64 52t94 25q52 0 86 -23.5t46 -64.5q22 35 67.5 61.5t96.5 26.5q67 0 103 -37t36 -109v-349z" horiz-adv-x="834" unicode="m" glyph-name="m"/>
<glyph d="M500 0h-105v302q0 54 -25.5 77t-72.5 23q-37 0 -70 -18.5t-54 -45.5v-338h-105v483h105v-65q27 32 72.5 54.5t98.5 22.5q77 0 116.5 -40t39.5 -115v-340z" horiz-adv-x="568" unicode="n" glyph-name="n"/>
<glyph d="M287 -12q-112 0 -180 73.5t-68 180.5q0 108 68 180.5t180 72.5t180 -72.5t68 -180.5q0 -107 -68 -180.5t-180 -73.5zM287 81q65 0 102 46.5t37 114.5q0 67 -37 113.5t-102 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5z" unicode="o" glyph-name="o"/>
<glyph d="M328 -12q-93 0 -155 79v-251h-105v667h105v-66q58 78 155 78q95 0 154.5 -68t59.5 -185t-59.5 -185.5t-154.5 -68.5zM297 81q62 0 99.5 45t37.5 116t-37.5 115.5t-99.5 44.5q-36 0 -71 -18.5t-53 -45.5v-191q18 -28 53 -47t71 -19z" horiz-adv-x="579" unicode="p" glyph-name="p"/>
<glyph d="M282 81q36 0 71 19t53 47v191q-18 27 -53 45.5t-71 18.5q-62 0 -99.5 -44.5t-37.5 -115.5t37.5 -116t99.5 -45zM251 -12q-95 0 -154.5 68.5t-59.5 185.5t59.5 185t154.5 68q96 0 155 -78v66h105v-667h-105v251q-62 -79 -155 -79z" horiz-adv-x="579" unicode="q" glyph-name="q"/>
<glyph d="M173 0h-105v483h105v-70q29 36 70.5 59t86.5 23v-104q-14 3 -32 3q-34 0 -71.5 -19.5t-53.5 -45.5v-329z" horiz-adv-x="347" unicode="r" glyph-name="r"/>
<glyph d="M234 -12q-129 0 -207 76l48 75q27 -28 72.5 -47.5t90.5 -19.5q46 0 71 17.5t25 46.5q0 26 -30 40.5t-73 23t-86 21t-73 44.5t-30 84q0 62 51 104t139 42q111 0 187 -67l-44 -74q-53 58 -143 58q-40 0 -64.5 -17t-24.5 -43q0 -23 30 -36t73 -21.5t86 -21.5t73 -47t30 -87 q0 -67 -53 -109t-148 -42z" horiz-adv-x="472" unicode="s" glyph-name="s"/>
<glyph d="M214 -12q-124 0 -124 125v279h-80v91h80v132h105v-132h98v-91h-98v-253q0 -26 12 -42t34 -16q30 0 46 18l25 -80q-35 -31 -98 -31z" horiz-adv-x="319" unicode="t" glyph-name="t"/>
<glyph d="M499 0h-105v64q-68 -76 -170 -76q-77 0 -116.5 39.5t-39.5 113.5v342h105v-303q0 -54 25 -76.5t73 -22.5q37 0 70 18.5t53 44.5v339h105v-483z" horiz-adv-x="567" unicode="u" glyph-name="u"/>
<glyph d="M308 0h-113l-197 483h113l141 -362l141 362h112z" horiz-adv-x="503" unicode="v" glyph-name="v"/>
<glyph d="M600 0h-110l-113 354l-112 -354h-110l-150 483h109l102 -351l115 351h93l114 -351l102 351h110z" horiz-adv-x="755" unicode="w" glyph-name="w"/>
<glyph d="M492 0h-118l-125 178l-126 -178h-118l178 248l-167 235h118l114 -163l115 163h118l-167 -235z" horiz-adv-x="497" unicode="x" glyph-name="x"/>
<glyph d="M48 -189l15 94q18 -8 40 -8q52 0 70 42l24 55l-199 489h113l141 -362l141 362h112l-233 -570q-24 -59 -64.5 -83.5t-100.5 -25.5q-34 0 -59 7z" horiz-adv-x="503" unicode="y" glyph-name="y"/>
<glyph d="M430 0h-383v80l238 312h-238v91h379v-76l-242 -315h246v-92z" horiz-adv-x="476" unicode="z" glyph-name="z"/>
<glyph d="M260 -190h-61q-58 0 -100 41t-42 107v187q0 29 -13.5 48.5t-38.5 19.5v62q25 0 38.5 19.5t13.5 48.5v186q0 66 42.5 107.5t99.5 41.5h61v-72h-61q-27 0 -46 -21t-19 -55v-197q0 -70 -45 -89q45 -19 45 -89v-196q0 -33 19 -55t46 -22h61v-72z" horiz-adv-x="277" unicode="{" glyph-name="braceleft"/>
<glyph d="M143 -20h-72v707h72v-707z" horiz-adv-x="214" unicode="|" glyph-name="bar"/>
<glyph d="M17 -190v72h61q27 0 46 22t19 55v196q0 70 45 89q-45 19 -45 89v197q0 34 -19 55t-46 21h-61v72h61q57 0 99.5 -41.5t42.5 -107.5v-186q0 -29 13.5 -48.5t38.5 -19.5v-62q-25 0 -38.5 -19.5t-13.5 -48.5v-187q0 -66 -42 -107t-100 -41h-61z" horiz-adv-x="277" unicode="}" glyph-name="braceright"/>
<glyph d="M408 667l73 -8q-5 -51 -13 -89.5t-23 -75.5t-42.5 -57t-65.5 -20q-36 0 -59.5 18.5t-35 45.5t-20 54t-21 45.5t-30.5 18.5q-55 0 -70 -183l-74 9q4 50 12 89t24 75.5t43 56.5t65 20q35 0 59 -19t35.5 -46t20 -53.5t21 -45.5t30.5 -19q54 0 71 184z" horiz-adv-x="507" unicode="~" glyph-name="asciitilde"/>
<glyph horiz-adv-x="258" unicode=" " glyph-name="nbspace"/>
<glyph d="M58 -184l18 463h91l19 -463h-128zM52 424q0 28 21 49t49 21t48.5 -20.5t20.5 -49.5q0 -28 -20.5 -48.5t-48.5 -20.5t-49 20.5t-21 48.5z" horiz-adv-x="244" unicode="¡" glyph-name="exclamdown"/>
<glyph d="M246 -100v91q-93 13 -150 82.5t-57 168.5q0 98 57 167t150 83v73h78v-72q95 -12 149 -84l-69 -63q-31 42 -80 53v-315q49 11 80 55l69 -65q-56 -73 -149 -84v-90h-78zM147 242q0 -57 26.5 -98.5t72.5 -55.5v307q-46 -15 -72.5 -55.5t-26.5 -97.5z" horiz-adv-x="498" unicode="¢" glyph-name="cent"/>
<glyph d="M22 269v69h83q-19 24 -29 40t-19.5 42.5t-9.5 54.5q0 87 70.5 144.5t163.5 57.5q160 0 219 -117l-93 -55q-12 33 -43 54.5t-70 21.5q-51 0 -85.5 -30t-34.5 -78q0 -21 7 -42t13 -32t22 -32.5t20 -28.5h147v-69h-116q3 -15 3 -30q0 -72 -70 -120q22 9 50 9 q32 0 72.5 -18.5t65.5 -18.5q30 0 54.5 11.5t35.5 26.5l48 -93q-49 -49 -142 -49q-47 0 -98.5 22.5t-82.5 22.5q-45 0 -116 -39l-40 83q118 54 118 136q0 28 -13 57h-130z" horiz-adv-x="536" unicode="£" glyph-name="sterling"/>
<glyph d="M486 137l-29 -29l-56 57q-60 -51 -140 -51t-140 51l-57 -57l-28 29l56 56q-50 63 -50 140q0 79 50 140l-56 57l28 28l57 -56q59 50 140 50q79 0 140 -50l56 56l29 -28l-56 -57q50 -61 50 -140q0 -77 -50 -140zM432 333q0 71 -50 121t-121 50t-121 -50t-50 -121t50 -121 t121 -50t121 50t50 121z" horiz-adv-x="522" unicode="¤" glyph-name="currency"/>
<glyph d="M378 0h-117v118h-238v68h238v91h-238v68h193l-214 322h134l183 -285l183 285h134l-213 -322h192v-68h-237v-91h237v-68h-237v-118z" horiz-adv-x="637" unicode="¥" glyph-name="yen"/>
<glyph d="M143 -20h-72v316h72v-316zM143 371h-72v316h72v-316z" horiz-adv-x="214" unicode="¦" glyph-name="brokenbar"/>
<glyph d="M435 321q0 -41 -23 -74.5t-65 -52.5q88 -37 88 -122q0 -71 -56 -112t-145 -41q-122 0 -206 78l47 68q66 -72 159 -72q45 0 72.5 18.5t27.5 51.5q0 25 -21.5 41.5t-54 23.5t-70.5 18.5t-70.5 24.5t-54 42.5t-21.5 72.5q0 47 30.5 79.5t74.5 45.5q-105 38 -105 126 q0 60 52 100t137 40q123 0 188 -69l-44 -62q-52 57 -139 57q-42 0 -67.5 -17.5t-25.5 -47.5q0 -26 30 -41t73 -23.5t86 -21.5t73 -46t30 -85zM331 304q0 33 -26 51t-70 29q-51 -12 -71.5 -33t-20.5 -51q0 -25 19.5 -42t39.5 -23.5t60 -15.5q69 31 69 85z" horiz-adv-x="477" unicode="§" glyph-name="section"/>
<glyph d="M306 608q0 -24 -17 -41t-42 -17q-24 0 -41 17t-17 41q0 25 17 42t41 17q25 0 42 -17t17 -42zM97 608q0 -24 -17 -41t-42 -17q-24 0 -41.5 17t-17.5 41q0 25 17.5 42t41.5 17q25 0 42 -17t17 -42z" horiz-adv-x="285" unicode="¨" glyph-name="dieresis"/>
<glyph d="M734 334q0 -143 -101 -244t-244 -101t-244 101.5t-101 243.5q0 143 101 244t244 101t244 -101t101 -244zM700 334q0 129 -91 220t-220 91q-128 0 -219.5 -91t-91.5 -220q0 -128 92 -219.5t219 -91.5q129 0 220 91.5t91 219.5zM512 218l29 -28q-58 -68 -148 -68 q-87 0 -147.5 61.5t-60.5 152.5t60.5 151t147.5 60q91 0 147 -66l-28 -29q-20 26 -52 42t-67 16q-68 0 -117 -49.5t-49 -125.5q0 -75 49 -125.5t117 -50.5q75 0 119 59z" horiz-adv-x="778" unicode="©" glyph-name="copyright"/>
<glyph d="M328 326h-77v34q-38 -42 -105 -42q-43 0 -77 28t-34 76q0 49 33.5 75.5t77.5 26.5q68 0 105 -39v46q0 26 -21 42t-53 16q-57 0 -94 -43l-31 51q55 51 137 51q61 0 100 -28t39 -91v-203zM178 368q49 0 73 32v44q-24 32 -73 32q-29 0 -47.5 -14.5t-18.5 -39.5t18.5 -39.5 t47.5 -14.5z" horiz-adv-x="382" unicode="ª" glyph-name="ordfeminine"/>
<glyph d="M460 63h-100l-160 180l160 177h100l-160 -177zM290 63h-100l-160 180l160 177h100l-160 -177z" horiz-adv-x="490" unicode="«" glyph-name="guillemotleft"/>
<glyph d="M473 463v-258h-70v191h-374v67h444z" horiz-adv-x="511" unicode="¬" glyph-name="logicalnot"/>
<glyph d="M459 465q0 -88 -62 -150t-150 -62t-150 62t-62 150t62 150t150 62q89 0 150.5 -61.5t61.5 -150.5zM429 465q0 76 -53 129t-129 53q-75 0 -128 -53t-53 -129q0 -74 53 -127.5t128 -53.5t128.5 53t53.5 128zM346 343h-41l-63 96h-43v-96h-33v243h99q32 0 55 -20.5t23 -53.5 q0 -35 -22.5 -53.5t-39.5 -18.5zM309 512q0 19 -13.5 32t-30.5 13h-66v-88h66q17 0 30.5 12.5t13.5 30.5z" horiz-adv-x="494" unicode="®" glyph-name="registered"/>
<glyph d="M281 547q0 -53 -38.5 -91t-91.5 -38q-54 0 -91.5 38t-37.5 91q0 54 37.5 92t91.5 38q53 0 91.5 -38t38.5 -92zM221 547q0 29 -20.5 49.5t-49.5 20.5q-28 0 -48.5 -20.5t-20.5 -49.5q0 -28 20 -48t49 -20t49.5 20t20.5 48z" horiz-adv-x="302" unicode="°" glyph-name="degree"/>
<glyph d="M473 325h-185v-207h-74v207h-185v68h185v200h74v-200h185v-68zM473 0h-444v68h444v-68z" horiz-adv-x="502" unicode="±" glyph-name="plusminus"/>
<glyph d="M353 421h-305v60q128 91 174.5 136t46.5 83q0 31 -21 46.5t-54 15.5q-34 0 -64 -14.5t-46 -35.5l-43 51q55 64 155 64q67 0 110.5 -31.5t43.5 -89.5q0 -51 -45 -101.5t-140 -118.5h188v-65z" horiz-adv-x="394" unicode="²" glyph-name="twosuperior"/>
<glyph d="M355 531q0 -52 -43.5 -84.5t-116.5 -32.5q-108 0 -158 64l41 52q43 -51 115 -51q38 0 59.5 15.5t21.5 41.5q0 58 -95 58q-36 0 -42 -1v64q7 -1 42 -1q89 0 89 54q0 25 -22.5 38.5t-57.5 13.5q-64 0 -108 -45l-38 47q56 63 154 63q70 0 111 -29t41 -79q0 -37 -26.5 -61 t-62.5 -30q35 -4 65.5 -29.5t30.5 -67.5z" horiz-adv-x="394" unicode="³" glyph-name="threesuperior"/>
<glyph d="M240 700l-165 -144h-75l140 144h100z" horiz-adv-x="240" unicode="´" glyph-name="acute"/>
<glyph d="M392 -100h-55v712h-83v-712h-55v423q-71 0 -121.5 50.5t-50.5 121.5t50.5 121.5t121.5 50.5h193v-767z" horiz-adv-x="449" unicode="¶" glyph-name="paragraph"/>
<glyph d="M106 -194q-65 0 -106 31l23 46q36 -29 81 -29q23 0 39 9.5t16 26.5q0 30 -32 30q-22 0 -35 -15l-40 23l30 83h55l-25 -64q16 11 36 11q28 0 47 -19.5t19 -49.5q0 -37 -31 -60t-77 -23z" horiz-adv-x="214" unicode="¸" glyph-name="cedilla"/>
<glyph d="M204 421h-81v297l-66 -69l-47 49l124 123h70v-400z" horiz-adv-x="264" unicode="¹" glyph-name="onesuperior"/>
<glyph d="M202 318q-75 0 -121.5 48t-46.5 117q0 70 46.5 117.5t121.5 47.5q77 0 123 -47.5t46 -117.5q0 -69 -46.5 -117t-122.5 -48zM202 381q42 0 66 28.5t24 73.5q0 46 -24 73.5t-66 27.5q-41 0 -64 -27.5t-23 -73.5q0 -45 23.5 -73.5t63.5 -28.5z" horiz-adv-x="405" unicode="º" glyph-name="ordmasculine"/>
<glyph d="M290 243l-160 -180h-100l160 180l-160 177h100zM460 243l-160 -180h-100l160 180l-160 177h100z" horiz-adv-x="490" unicode="»" glyph-name="guillemotright"/>
<glyph d="M619 667l-427 -667h-67l427 667h67zM777 92h-54v-92h-80v92h-197v57l165 251h112v-244h54v-64zM643 156v175l-117 -175h117zM204 267h-81v297l-66 -69l-47 49l124 123h70v-400z" horiz-adv-x="806" unicode="¼" glyph-name="onequarter"/>
<glyph d="M619 667l-427 -667h-67l427 667h67zM801 0h-305v60q128 91 174.5 136t46.5 83q0 31 -21 46.5t-54 15.5q-34 0 -64 -14.5t-46 -35.5l-43 51q55 64 155 64q67 0 110.5 -31.5t43.5 -89.5q0 -51 -45 -101.5t-140 -118.5h188v-65zM204 267h-81v297l-66 -69l-47 49l124 123h70 v-400z" horiz-adv-x="841" unicode="½" glyph-name="onehalf"/>
<glyph d="M716 667l-427 -667h-67l427 667h67zM873 92h-54v-92h-80v92h-197v57l165 251h112v-244h54v-64zM739 156v175l-117 -175h117zM355 377q0 -52 -43.5 -84.5t-116.5 -32.5q-108 0 -158 64l41 52q43 -51 115 -51q38 0 59.5 15.5t21.5 41.5q0 58 -95 58q-36 0 -42 -1v64 q7 -1 42 -1q89 0 89 54q0 25 -22.5 38.5t-57.5 13.5q-64 0 -108 -45l-38 47q56 63 154 63q70 0 111 -29t41 -79q0 -37 -26.5 -61t-62.5 -30q35 -4 65.5 -29.5t30.5 -67.5z" horiz-adv-x="903" unicode="¾" glyph-name="threequarters"/>
<glyph d="M270 286q35 -34 35 -86q0 -34 -16.5 -61t-40 -45t-47.5 -34t-40.5 -36.5t-16.5 -43.5q0 -31 24.5 -51.5t70.5 -20.5q84 0 139 70l68 -75q-81 -99 -218 -99q-93 0 -148 44t-55 113q0 41 18.5 74t44.5 53.5t52 38.5t44.5 39t18.5 45q0 29 -22 45zM234 494q28 0 49 -21 t21 -49t-21 -49t-49 -21t-49 21t-21 49t21 49t49 21z" horiz-adv-x="396" unicode="¿" glyph-name="questiondown"/>
<glyph d="M413 728h-75l-165 144h100zM672 0h-133l-49 129h-306l-49 -129h-133l262 667h146zM457 232l-120 318l-120 -318h240z" horiz-adv-x="673" unicode="À" glyph-name="Agrave"/>
<glyph d="M503 872l-165 -144h-75l140 144h100zM672 0h-133l-49 129h-306l-49 -129h-133l262 667h146zM457 232l-120 318l-120 -318h240z" horiz-adv-x="673" unicode="Á" glyph-name="Aacute"/>
<glyph d="M481 728h-66l-78 97l-74 -97h-67l93 144h96zM672 0h-133l-49 129h-306l-49 -129h-133l262 667h146zM457 232l-120 318l-120 -318h240z" horiz-adv-x="673" unicode="Â" glyph-name="Acircumflex"/>
<glyph d="M395 726q-29 0 -50 19.5t-36.5 39t-32.5 19.5q-41 0 -41 -72h-58q0 62 26.5 98t76.5 36q19 0 34.5 -8t25.5 -19.5t18.5 -23t19 -19.5t21.5 -8q18 0 30 18t12 54h57q0 -62 -26.5 -98t-76.5 -36zM672 0h-133l-49 129h-306l-49 -129h-133l262 667h146zM457 232l-120 318 l-120 -318h240z" horiz-adv-x="673" unicode="Ã" glyph-name="Atilde"/>
<glyph d="M501 780q0 -24 -17 -41t-42 -17q-24 0 -41 17t-17 41q0 25 17 42t41 17q25 0 42 -17t17 -42zM292 780q0 -24 -17 -41t-42 -17q-24 0 -41.5 17t-17.5 41q0 25 17.5 42t41.5 17q25 0 42 -17t17 -42zM672 0h-133l-49 129h-306l-49 -129h-133l262 667h146zM457 232l-120 318 l-120 -318h240z" horiz-adv-x="673" unicode="Ä" glyph-name="Adieresis"/>
<glyph d="M338 693q-42 0 -72.5 30t-30.5 72t30.5 72.5t72.5 30.5t71.5 -30.5t29.5 -72.5t-29.5 -72t-71.5 -30zM338 743q21 0 36 15.5t15 36.5q0 22 -15 37t-36 15q-22 0 -37 -15t-15 -37q0 -21 15 -36.5t37 -15.5zM672 0h-133l-49 129h-306l-49 -129h-133l262 667h146zM457 232 l-120 318l-120 -318h240z" horiz-adv-x="673" unicode="Å" glyph-name="Aring"/>
<glyph d="M912 0h-457v129h-246l-77 -129h-134l410 667h504v-103h-340v-172h333v-103h-333v-186h340v-103zM455 232v318l-191 -318h191z" horiz-adv-x="960" unicode="Æ" glyph-name="AE"/>
<glyph d="M386 -190q-65 0 -106 31l23 46q36 -29 81 -29q23 0 39 9.5t16 26.5q0 30 -32 30q-22 0 -35 -15l-40 23l21 58q-136 13 -223.5 107.5t-87.5 235.5q0 152 100 248.5t250 96.5q176 0 267 -149l-99 -52q-25 43 -70.5 70t-97.5 27q-99 0 -164.5 -68t-65.5 -173 q0 -104 65.5 -172.5t164.5 -68.5q52 0 97.5 27.5t70.5 69.5l100 -50q-92 -145 -254 -151l-14 -37q16 11 36 11q28 0 47 -19.5t19 -49.5q0 -37 -31 -60t-77 -23z" horiz-adv-x="682" unicode="Ç" glyph-name="Ccedilla"/>
<glyph d="M375 728h-75l-165 144h100zM528 0h-457v667h457v-103h-340v-172h333v-103h-333v-186h340v-103z" horiz-adv-x="577" unicode="È" glyph-name="Egrave"/>
<glyph d="M465 872l-165 -144h-75l140 144h100zM528 0h-457v667h457v-103h-340v-172h333v-103h-333v-186h340v-103z" horiz-adv-x="577" unicode="É" glyph-name="Eacute"/>
<glyph d="M442 728h-66l-78 97l-74 -97h-67l93 144h96zM528 0h-457v667h457v-103h-340v-172h333v-103h-333v-186h340v-103z" horiz-adv-x="577" unicode="Ê" glyph-name="Ecircumflex"/>
<glyph d="M464 780q0 -24 -17 -41t-42 -17q-24 0 -41 17t-17 41q0 25 17 42t41 17q25 0 42 -17t17 -42zM255 780q0 -24 -17 -41t-42 -17q-24 0 -41.5 17t-17.5 41q0 25 17.5 42t41.5 17q25 0 42 -17t17 -42zM528 0h-457v667h457v-103h-340v-172h333v-103h-333v-186h340v-103z" horiz-adv-x="577" unicode="Ë" glyph-name="Edieresis"/>
<glyph d="M204 728h-75l-165 144h100zM188 0h-117v667h117v-667z" horiz-adv-x="259" unicode="Ì" glyph-name="Igrave"/>
<glyph d="M297 872l-165 -144h-75l140 144h100zM188 0h-117v667h117v-667z" horiz-adv-x="259" unicode="Í" glyph-name="Iacute"/>
<glyph d="M272 728h-66l-78 97l-74 -97h-67l93 144h96zM188 0h-117v667h117v-667z" horiz-adv-x="259" unicode="Î" glyph-name="Icircumflex"/>
<glyph d="M293 779q0 -24 -17 -41t-42 -17q-24 0 -41 17t-17 41q0 25 17 42t41 17q25 0 42 -17t17 -42zM84 779q0 -24 -17 -41t-42 -17q-24 0 -41.5 17t-17.5 41q0 25 17.5 42t41.5 17q25 0 42 -17t17 -42zM188 0h-117v667h117v-667z" horiz-adv-x="259" unicode="Ï" glyph-name="Idieresis"/>
<glyph d="M346 0h-249v286h-87v85h87v296h248q156 0 252.5 -93t96.5 -240q0 -148 -96 -241t-252 -93zM373 286h-159v-183h131q105 0 167 66t62 165q0 100 -60.5 165t-167.5 65h-132v-193h159v-85z" horiz-adv-x="736" unicode="Ð" glyph-name="Eth"/>
<glyph d="M416 726q-30 0 -50.5 19.5t-36 39t-32.5 19.5q-41 0 -41 -72h-58q0 62 26.5 98t76.5 36q19 0 34.5 -8t25.5 -19.5t18.5 -23t19 -19.5t21.5 -8q18 0 30 18t12 54h57q0 -62 -26.5 -98t-76.5 -36zM649 0h-113l-348 477v-477h-117v667h120l341 -462v462h117v-667z" horiz-adv-x="720" unicode="Ñ" glyph-name="Ntilde"/>
<glyph d="M461 728h-75l-165 144h100zM383 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM383 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" horiz-adv-x="765" unicode="Ò" glyph-name="Ograve"/>
<glyph d="M549 872l-165 -144h-75l140 144h100zM383 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM383 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" horiz-adv-x="765" unicode="Ó" glyph-name="Oacute"/>
<glyph d="M528 728h-66l-78 97l-74 -97h-67l93 144h96zM383 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5t96 -247.5t-96 -247.5t-245 -97.5zM383 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" horiz-adv-x="765" unicode="Ô" glyph-name="Ocircumflex"/>
<glyph d="M441 726q-30 0 -50.5 19.5t-36 39t-32.5 19.5q-41 0 -41 -72h-58q0 62 26.5 98t76.5 36q19 0 34.5 -8t25.5 -19.5t18.5 -23t19 -19.5t21.5 -8q18 0 30 18t12 54h57q0 -62 -26.5 -98t-76.5 -36zM383 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5 t96 -247.5t-96 -247.5t-245 -97.5zM383 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" horiz-adv-x="765" unicode="Õ" glyph-name="Otilde"/>
<glyph d="M547 780q0 -24 -17 -41t-42 -17q-24 0 -41 17t-17 41q0 25 17 42t41 17q25 0 42 -17t17 -42zM338 780q0 -24 -17 -41t-42 -17q-24 0 -41.5 17t-17.5 41q0 25 17.5 42t41.5 17q25 0 42 -17t17 -42zM383 -12q-149 0 -245 97.5t-96 247.5t96 247.5t245 97.5t245 -97.5 t96 -247.5t-96 -247.5t-245 -97.5zM383 92q99 0 160 68.5t61 172.5q0 105 -61 173t-160 68q-100 0 -160.5 -68t-60.5 -173t60.5 -173t160.5 -68z" horiz-adv-x="765" unicode="Ö" glyph-name="Odieresis"/>
<glyph d="M396 142l-145 145l-144 -145l-48 48l145 144l-145 145l48 47l144 -144l145 144l47 -47l-144 -145l144 -144z" horiz-adv-x="502" unicode="×" glyph-name="multiply"/>
<glyph d="M383 -12q-101 0 -179 46l-25 -34h-87l55 76q-105 99 -105 257q0 150 96 247.5t245 97.5q94 0 170 -42l22 31h87l-51 -71q113 -99 113 -263q0 -150 -96 -247.5t-245 -97.5zM383 92q99 0 160 68.5t61 172.5t-60 172l-276 -383q51 -30 115 -30zM162 333q0 -98 52 -164 l275 379q-47 26 -106 26q-100 0 -160.5 -68t-60.5 -173z" horiz-adv-x="765" unicode="Ø" glyph-name="Oslash"/>
<glyph d="M437 728h-75l-165 144h100zM360 -12q-141 0 -215 74.5t-74 201.5v403h119v-400q0 -81 44 -128t126 -47t126 47t44 128v400h119v-403q0 -128 -73.5 -202t-215.5 -74z" horiz-adv-x="720" unicode="Ù" glyph-name="Ugrave"/>
<glyph d="M526 872l-165 -144h-75l140 144h100zM360 -12q-141 0 -215 74.5t-74 201.5v403h119v-400q0 -81 44 -128t126 -47t126 47t44 128v400h119v-403q0 -128 -73.5 -202t-215.5 -74z" horiz-adv-x="720" unicode="Ú" glyph-name="Uacute"/>
<glyph d="M508 728h-66l-78 97l-74 -97h-67l93 144h96zM360 -12q-141 0 -215 74.5t-74 201.5v403h119v-400q0 -81 44 -128t126 -47t126 47t44 128v400h119v-403q0 -128 -73.5 -202t-215.5 -74z" horiz-adv-x="720" unicode="Û" glyph-name="Ucircumflex"/>
<glyph d="M525 780q0 -24 -17 -41t-42 -17q-24 0 -41 17t-17 41q0 25 17 42t41 17q25 0 42 -17t17 -42zM316 780q0 -24 -17 -41t-42 -17q-24 0 -41.5 17t-17.5 41q0 25 17.5 42t41.5 17q25 0 42 -17t17 -42zM360 -12q-141 0 -215 74.5t-74 201.5v403h119v-400q0 -81 44 -128 t126 -47t126 47t44 128v400h119v-403q0 -128 -73.5 -202t-215.5 -74z" horiz-adv-x="720" unicode="Ü" glyph-name="Udieresis"/>
<glyph d="M487 872l-165 -144h-75l140 144h100zM378 0h-117v277l-259 390h134l183 -285l183 285h134l-258 -390v-277z" horiz-adv-x="637" unicode="Ý" glyph-name="Yacute"/>
<glyph d="M188 0h-117v667h117v-112h177q101 0 159 -60.5t58 -149.5t-58 -148.5t-159 -59.5h-177v-137zM349 240q50 0 81.5 29t31.5 76q0 48 -31.5 77.5t-81.5 29.5h-161v-212h161z" horiz-adv-x="609" unicode="Þ" glyph-name="Thorn"/>
<glyph d="M606 141q0 -66 -52 -109.5t-142 -43.5q-66 0 -109 20.5t-83 58.5l46 72q22 -28 61.5 -47t84.5 -19t69 18.5t24 45.5q0 25 -28 40t-68 24.5t-80 22.5t-68 44.5t-28 81.5q0 36 21 64.5t45.5 43.5t45.5 33.5t21 35.5q0 27 -26 42t-61 15q-45 0 -75 -27t-30 -72v-485h-105 v485q0 82 57.5 137t154.5 55q78 0 135.5 -37.5t57.5 -98.5q0 -37 -22 -66t-48 -44t-48 -35t-22 -43t28 -36t68 -21t80 -21.5t68 -47t28 -86.5z" horiz-adv-x="623" unicode="ß" glyph-name="germandbls"/>
<glyph d="M469 0h-105v53q-56 -65 -157 -65q-65 0 -116 43t-51 117q0 78 50 118t117 40q103 0 157 -62v72q0 41 -31.5 65.5t-81.5 24.5q-82 0 -143 -62l-44 73q81 78 203 78q89 0 145.5 -41.5t56.5 -132.5v-321zM249 60q77 0 115 50v74q-38 50 -115 50q-45 0 -74.5 -24t-29.5 -63 t29.5 -63t74.5 -24zM343 556h-75l-165 144h100z" horiz-adv-x="537" unicode="à" glyph-name="agrave"/>
<glyph d="M469 0h-105v53q-56 -65 -157 -65q-65 0 -116 43t-51 117q0 78 50 118t117 40q103 0 157 -62v72q0 41 -31.5 65.5t-81.5 24.5q-82 0 -143 -62l-44 73q81 78 203 78q89 0 145.5 -41.5t56.5 -132.5v-321zM249 60q77 0 115 50v74q-38 50 -115 50q-45 0 -74.5 -24t-29.5 -63 t29.5 -63t74.5 -24zM436 700l-165 -144h-75l140 144h100z" horiz-adv-x="537" unicode="á" glyph-name="aacute"/>
<glyph d="M469 0h-105v53q-56 -65 -157 -65q-65 0 -116 43t-51 117q0 78 50 118t117 40q103 0 157 -62v72q0 41 -31.5 65.5t-81.5 24.5q-82 0 -143 -62l-44 73q81 78 203 78q89 0 145.5 -41.5t56.5 -132.5v-321zM249 60q77 0 115 50v74q-38 50 -115 50q-45 0 -74.5 -24t-29.5 -63 t29.5 -63t74.5 -24zM414 556h-66l-78 97l-74 -97h-67l93 144h96z" horiz-adv-x="537" unicode="â" glyph-name="acircumflex"/>
<glyph d="M469 0h-105v53q-56 -65 -157 -65q-65 0 -116 43t-51 117q0 78 50 118t117 40q103 0 157 -62v72q0 41 -31.5 65.5t-81.5 24.5q-82 0 -143 -62l-44 73q81 78 203 78q89 0 145.5 -41.5t56.5 -132.5v-321zM249 60q77 0 115 50v74q-38 50 -115 50q-45 0 -74.5 -24t-29.5 -63 t29.5 -63t74.5 -24zM326 554q-30 0 -50.5 19.5t-36 39t-32.5 19.5q-41 0 -41 -72h-58q0 62 26.5 98t76.5 36q19 0 34.5 -8t25.5 -19.5t18.5 -23t19 -19.5t21.5 -8q18 0 30 18t12 54h57q0 -62 -26.5 -98t-76.5 -36z" horiz-adv-x="537" unicode="ã" glyph-name="atilde"/>
<glyph d="M469 0h-105v53q-56 -65 -157 -65q-65 0 -116 43t-51 117q0 78 50 118t117 40q103 0 157 -62v72q0 41 -31.5 65.5t-81.5 24.5q-82 0 -143 -62l-44 73q81 78 203 78q89 0 145.5 -41.5t56.5 -132.5v-321zM249 60q77 0 115 50v74q-38 50 -115 50q-45 0 -74.5 -24t-29.5 -63 t29.5 -63t74.5 -24zM430 608q0 -24 -17 -41t-42 -17q-24 0 -41 17t-17 41q0 25 17 42t41 17q25 0 42 -17t17 -42zM221 608q0 -24 -17 -41t-42 -17q-24 0 -41.5 17t-17.5 41q0 25 17.5 42t41.5 17q25 0 42 -17t17 -42z" horiz-adv-x="537" unicode="ä" glyph-name="adieresis"/>
<glyph d="M469 0h-105v53q-56 -65 -157 -65q-65 0 -116 43t-51 117q0 78 50 118t117 40q103 0 157 -62v72q0 41 -31.5 65.5t-81.5 24.5q-82 0 -143 -62l-44 73q81 78 203 78q89 0 145.5 -41.5t56.5 -132.5v-321zM249 60q77 0 115 50v74q-38 50 -115 50q-45 0 -74.5 -24t-29.5 -63 t29.5 -63t74.5 -24zM270 544q-42 0 -72.5 30t-30.5 72t30.5 72.5t72.5 30.5t71.5 -30.5t29.5 -72.5t-29.5 -72t-71.5 -30zM270 594q21 0 36 15.5t15 36.5q0 22 -15 37t-36 15q-22 0 -37 -15t-15 -37q0 -21 15 -36.5t37 -15.5z" horiz-adv-x="537" unicode="å" glyph-name="aring"/>
<glyph d="M842 207h-372q6 -57 46.5 -94t106.5 -37q82 0 138 54l48 -71q-76 -71 -196 -71q-128 0 -200 93q-73 -93 -198 -93q-70 0 -122.5 42t-52.5 117q0 76 48.5 117.5t118.5 41.5q102 0 157 -61v71q0 41 -31.5 65.5t-81.5 24.5q-82 0 -143 -62l-44 73q81 78 203 78 q131 0 165 -90q61 90 178 90q104 0 168 -73.5t64 -188.5v-26zM469 282h272q-3 50 -37.5 88.5t-99.5 38.5q-62 0 -97 -38.5t-38 -88.5zM376 129q-12 22 -12 53q-38 52 -115 52q-45 0 -74.5 -24t-29.5 -63t29.5 -63t74.5 -24q87 0 127 69z" horiz-adv-x="878" unicode="æ" glyph-name="ae"/>
<glyph d="M291 -192q-65 0 -106 31l23 46q36 -29 81 -29q23 0 39 9.5t16 26.5q0 30 -32 30q-22 0 -35 -15l-40 23l21 60q-97 10 -158 80t-61 172q0 110 70 181.5t179 71.5q121 0 185 -86l-69 -63q-40 56 -111 56q-65 0 -105.5 -44.5t-40.5 -115.5t40.5 -116t105.5 -45q70 0 111 58 l69 -65q-58 -76 -161 -85l-15 -40q16 11 36 11q28 0 47 -19.5t19 -49.5q0 -37 -31 -60t-77 -23z" horiz-adv-x="498" unicode="ç" glyph-name="ccedilla"/>
<glyph d="M292 -12q-111 0 -182 70t-71 184q0 106 69.5 179.5t175.5 73.5q107 0 172 -73.5t65 -188.5v-26h-372q6 -57 47 -95t106 -38q84 0 138 54l48 -69q-76 -71 -196 -71zM420 282q-3 50 -37.5 88.5t-99.5 38.5q-62 0 -97 -38.5t-38 -88.5h272zM359 556h-75l-165 144h100z" horiz-adv-x="557" unicode="è" glyph-name="egrave"/>
<glyph d="M292 -12q-111 0 -182 70t-71 184q0 106 69.5 179.5t175.5 73.5q107 0 172 -73.5t65 -188.5v-26h-372q6 -57 47 -95t106 -38q84 0 138 54l48 -69q-76 -71 -196 -71zM420 282q-3 50 -37.5 88.5t-99.5 38.5q-62 0 -97 -38.5t-38 -88.5h272zM450 700l-165 -144h-75l140 144 h100z" horiz-adv-x="557" unicode="é" glyph-name="eacute"/>
<glyph d="M292 -12q-111 0 -182 70t-71 184q0 106 69.5 179.5t175.5 73.5q107 0 172 -73.5t65 -188.5v-26h-372q6 -57 47 -95t106 -38q84 0 138 54l48 -69q-76 -71 -196 -71zM420 282q-3 50 -37.5 88.5t-99.5 38.5q-62 0 -97 -38.5t-38 -88.5h272zM428 556h-66l-78 97l-74 -97h-67 l93 144h96z" horiz-adv-x="557" unicode="ê" glyph-name="ecircumflex"/>
<glyph d="M292 -12q-111 0 -182 70t-71 184q0 106 69.5 179.5t175.5 73.5q107 0 172 -73.5t65 -188.5v-26h-372q6 -57 47 -95t106 -38q84 0 138 54l48 -69q-76 -71 -196 -71zM420 282q-3 50 -37.5 88.5t-99.5 38.5q-62 0 -97 -38.5t-38 -88.5h272zM448 608q0 -24 -17 -41t-42 -17 q-24 0 -41 17t-17 41q0 25 17 42t41 17q25 0 42 -17t17 -42zM239 608q0 -24 -17 -41t-42 -17q-24 0 -41.5 17t-17.5 41q0 25 17.5 42t41.5 17q25 0 42 -17t17 -42z" horiz-adv-x="557" unicode="ë" glyph-name="edieresis"/>
<glyph d="M195 556h-75l-165 144h100zM173 0h-105v483h105v-483z" horiz-adv-x="241" unicode="ì" glyph-name="igrave"/>
<glyph d="M286 700l-165 -144h-75l140 144h100zM173 0h-105v483h105v-483z" horiz-adv-x="241" unicode="í" glyph-name="iacute"/>
<glyph d="M263 556h-66l-78 97l-74 -97h-67l93 144h96zM173 0h-105v483h105v-483z" horiz-adv-x="241" unicode="î" glyph-name="icircumflex"/>
<glyph d="M284 608q0 -24 -17 -41t-42 -17q-24 0 -41 17t-17 41q0 25 17 42t41 17q25 0 42 -17t17 -42zM75 608q0 -24 -17 -41t-42 -17q-24 0 -41.5 17t-17.5 41q0 25 17.5 42t41.5 17q25 0 42 -17t17 -42zM173 0h-105v483h105v-483z" horiz-adv-x="241" unicode="ï" glyph-name="idieresis"/>
<glyph d="M111 481l-21 52l112 49q-37 24 -68 43l57 85q73 -45 119 -80l105 46l22 -50l-79 -35q177 -155 177 -341q0 -117 -67 -189.5t-181 -72.5q-111 0 -179.5 68t-68.5 173q0 104 61.5 171.5t154.5 67.5q101 0 159 -90q-42 83 -158 167zM287 81q65 0 102 43t37 105t-37 104.5 t-102 42.5q-64 0 -101.5 -43t-37.5 -104q0 -62 37.5 -105t101.5 -43z" unicode="ð" glyph-name="eth"/>
<glyph d="M505 0h-105v302q0 54 -25.5 77t-72.5 23q-37 0 -70 -18.5t-54 -45.5v-338h-105v483h105v-65q27 32 72.5 54.5t98.5 22.5q77 0 116.5 -40t39.5 -115v-340zM342 554q-30 0 -50.5 19.5t-36 39t-32.5 19.5q-41 0 -41 -72h-58q0 62 26.5 98t76.5 36q19 0 34.5 -8t25.5 -19.5 t18.5 -23t19 -19.5t21.5 -8q18 0 30 18t12 54h57q0 -62 -26.5 -98t-76.5 -36z" horiz-adv-x="568" unicode="ñ" glyph-name="ntilde"/>
<glyph d="M287 -12q-112 0 -180 73.5t-68 180.5q0 108 68 180.5t180 72.5t180 -72.5t68 -180.5q0 -107 -68 -180.5t-180 -73.5zM287 81q65 0 102 46.5t37 114.5q0 67 -37 113.5t-102 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5zM362 556h-75l-165 144 h100z" unicode="ò" glyph-name="ograve"/>
<glyph d="M287 -12q-112 0 -180 73.5t-68 180.5q0 108 68 180.5t180 72.5t180 -72.5t68 -180.5q0 -107 -68 -180.5t-180 -73.5zM287 81q65 0 102 46.5t37 114.5q0 67 -37 113.5t-102 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5zM452 700l-165 -144h-75 l140 144h100z" unicode="ó" glyph-name="oacute"/>
<glyph d="M287 -12q-112 0 -180 73.5t-68 180.5q0 108 68 180.5t180 72.5t180 -72.5t68 -180.5q0 -107 -68 -180.5t-180 -73.5zM287 81q65 0 102 46.5t37 114.5q0 67 -37 113.5t-102 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5zM431 556h-66l-78 97 l-74 -97h-67l93 144h96z" unicode="ô" glyph-name="ocircumflex"/>
<glyph d="M287 -12q-112 0 -180 73.5t-68 180.5q0 108 68 180.5t180 72.5t180 -72.5t68 -180.5q0 -107 -68 -180.5t-180 -73.5zM287 81q65 0 102 46.5t37 114.5q0 67 -37 113.5t-102 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5zM345 554q-29 0 -50 19.5 t-36.5 39t-32.5 19.5q-41 0 -41 -72h-58q0 62 26.5 98t76.5 36q19 0 34.5 -8t25.5 -19.5t18.5 -23t19 -19.5t21.5 -8q18 0 30 18t12 54h57q0 -62 -26.5 -98t-76.5 -36z" unicode="õ" glyph-name="otilde"/>
<glyph d="M287 -12q-112 0 -180 73.5t-68 180.5q0 108 68 180.5t180 72.5t180 -72.5t68 -180.5q0 -107 -68 -180.5t-180 -73.5zM287 81q65 0 102 46.5t37 114.5q0 67 -37 113.5t-102 46.5q-64 0 -101.5 -46.5t-37.5 -113.5q0 -68 37.5 -114.5t101.5 -46.5zM450 608q0 -24 -17 -41 t-42 -17q-24 0 -41 17t-17 41q0 25 17 42t41 17q25 0 42 -17t17 -42zM241 608q0 -24 -17 -41t-42 -17q-24 0 -41.5 17t-17.5 41q0 25 17.5 42t41.5 17q25 0 42 -17t17 -42z" unicode="ö" glyph-name="odieresis"/>
<glyph d="M310 521q0 -23 -16 -39.5t-38 -16.5t-38.5 16.5t-16.5 39.5q0 22 16 37.5t39 15.5t38.5 -15.5t15.5 -37.5zM482 304h-453v67h453v-67zM310 152q0 -23 -16 -39t-38 -16t-38.5 16.5t-16.5 38.5t16 38t39 16q22 0 38 -16t16 -38z" horiz-adv-x="511" unicode="÷" glyph-name="divide"/>
<glyph d="M119 0h-68l53 64q-65 71 -65 178q0 108 68 180.5t180 72.5q77 0 139 -39l23 27h69l-51 -61q68 -71 68 -180q0 -107 -68 -180.5t-180 -73.5q-84 0 -143 42zM287 81q65 0 102 46.5t37 114.5q0 55 -26 98l-193 -233q36 -26 80 -26zM148 242q0 -56 24 -96l191 233 q-33 23 -76 23q-64 0 -101.5 -46.5t-37.5 -113.5z" unicode="ø" glyph-name="oslash"/>
<glyph d="M499 0h-105v64q-68 -76 -170 -76q-77 0 -116.5 39.5t-39.5 113.5v342h105v-303q0 -54 25 -76.5t73 -22.5q37 0 70 18.5t53 44.5v339h105v-483zM361 556h-75l-165 144h100z" horiz-adv-x="567" unicode="ù" glyph-name="ugrave"/>
<glyph d="M499 0h-105v64q-68 -76 -170 -76q-77 0 -116.5 39.5t-39.5 113.5v342h105v-303q0 -54 25 -76.5t73 -22.5q37 0 70 18.5t53 44.5v339h105v-483zM450 700l-165 -144h-75l140 144h100z" horiz-adv-x="567" unicode="ú" glyph-name="uacute"/>
<glyph d="M499 0h-105v64q-68 -76 -170 -76q-77 0 -116.5 39.5t-39.5 113.5v342h105v-303q0 -54 25 -76.5t73 -22.5q37 0 70 18.5t53 44.5v339h105v-483zM427 556h-66l-78 97l-74 -97h-67l93 144h96z" horiz-adv-x="567" unicode="û" glyph-name="ucircumflex"/>
<glyph d="M499 0h-105v64q-68 -76 -170 -76q-77 0 -116.5 39.5t-39.5 113.5v342h105v-303q0 -54 25 -76.5t73 -22.5q37 0 70 18.5t53 44.5v339h105v-483zM450 608q0 -24 -17 -41t-42 -17q-24 0 -41 17t-17 41q0 25 17 42t41 17q25 0 42 -17t17 -42zM241 608q0 -24 -17 -41t-42 -17 q-24 0 -41.5 17t-17.5 41q0 25 17.5 42t41.5 17q25 0 42 -17t17 -42z" horiz-adv-x="567" unicode="ü" glyph-name="udieresis"/>
<glyph d="M48 -189l15 94q18 -8 40 -8q52 0 70 42l24 55l-199 489h113l141 -362l141 362h112l-233 -570q-24 -59 -64.5 -83.5t-100.5 -25.5q-34 0 -59 7zM417 700l-165 -144h-75l140 144h100z" horiz-adv-x="503" unicode="ý" glyph-name="yacute"/>
<glyph d="M328 -12q-93 0 -155 79v-251h-105v851h105v-250q58 78 155 78q95 0 154.5 -68t59.5 -185t-59.5 -185.5t-154.5 -68.5zM297 81q62 0 99.5 45t37.5 116t-37.5 115.5t-99.5 44.5q-36 0 -71 -18.5t-53 -45.5v-191q18 -28 53 -47t71 -19z" horiz-adv-x="579" unicode="þ" glyph-name="thorn"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="J,Jcircumflex" k="-15"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" k="10"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="T,Tcommaaccent,Tcaron,Tbar" k="65"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" k="35"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="30"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" k="35"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="V" k="50"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="W,Wcircumflex" k="45"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="Y,Yacute,Ycircumflex,Ydieresis" k="65"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="question" k="75"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="asterisk" k="95"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="bullet.case" k="35"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="hyphen.case,endash.case,emdash.case,periodcentered.case" k="45"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="dagger" k="95"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="daggerdbl" k="35"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="95"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="f,longs,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1" k="15"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="t,tcommaaccent,tcaron,tbar" k="18"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="u,uogonek,y.alt1" k="13"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="v" k="20"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="w" k="15"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="y,yacute,ydieresis,ycircumflex" k="20"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="15"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp" k="10"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="V.smcp" k="20"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="Wcircumflex.smcp,W.smcp" k="20"/>
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta" g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" k="50"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="100"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" k="45"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="15"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" k="30"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="V" k="50"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="W,Wcircumflex" k="50"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="Y,Yacute,Ycircumflex,Ydieresis" k="75"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="question" k="45"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="asterisk" k="60"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="dagger" k="75"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="daggerdbl" k="20"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="80"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="15"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp" k="10"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="V.smcp" k="30"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="Wcircumflex.smcp,W.smcp" k="20"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" k="40"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="Jcircumflex.smcp,J.smcp" k="-10"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="bullet" k="10"/>
<hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp" g2="question.smcp" k="45"/>
<hkern g1="B" g2="T,Tcommaaccent,Tcaron,Tbar" k="15"/>
<hkern g1="B" g2="V" k="15"/>
<hkern g1="B" g2="W,Wcircumflex" k="10"/>
<hkern g1="B" g2="Y,Yacute,Ycircumflex,Ydieresis" k="30"/>
<hkern g1="B" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="10"/>
<hkern g1="B" g2="Jcircumflex.smcp,J.smcp" k="15"/>
<hkern g1="B" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="10"/>
<hkern g1="B.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="40"/>
<hkern g1="B.smcp" g2="V" k="20"/>
<hkern g1="B.smcp" g2="Y,Yacute,Ycircumflex,Ydieresis" k="40"/>
<hkern g1="B.smcp" g2="question" k="45"/>
<hkern g1="B.smcp" g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" k="25"/>
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" g2="V" k="3"/>
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" g2="W,Wcircumflex" k="5"/>
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" g2="Y,Yacute,Ycircumflex,Ydieresis" k="20"/>
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" g2="question" k="5"/>
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" g2="bullet.case" k="5"/>
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" g2="hyphen.case,endash.case,emdash.case,periodcentered.case" k="10"/>
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="5"/>
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="5"/>
<hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="65"/>
<hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp" g2="V" k="15"/>
<hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp" g2="W,Wcircumflex" k="15"/>
<hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp" g2="Y,Yacute,Ycircumflex,Ydieresis" k="45"/>
<hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp" g2="question" k="15"/>
<hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="10"/>
<hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp" g2="Wcircumflex.smcp,W.smcp" k="5"/>
<hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp" g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" k="5"/>
<hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp" g2="X.smcp" k="5"/>
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="J,Jcircumflex" k="20"/>
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="T,Tcommaaccent,Tcaron,Tbar" k="25"/>
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="V" k="20"/>
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="W,Wcircumflex" k="20"/>
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="Y,Yacute,Ycircumflex,Ydieresis" k="40"/>
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="question" k="20"/>
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="10"/>
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="Jcircumflex.smcp,J.smcp" k="20"/>
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="30"/>
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="35"/>
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="X" k="30"/>
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="Z,Zacute,Zdotaccent,Zcaron" k="15"/>
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="25"/>
<hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="75"/>
<hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp" g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" k="30"/>
<hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp" g2="V" k="50"/>
<hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp" g2="W,Wcircumflex" k="30"/>
<hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp" g2="Y,Yacute,Ycircumflex,Ydieresis" k="80"/>
<hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp" g2="question" k="40"/>
<hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="20"/>
<hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp" g2="V.smcp" k="20"/>
<hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp" g2="Wcircumflex.smcp,W.smcp" k="25"/>
<hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp" g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" k="30"/>
<hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp" g2="Jcircumflex.smcp,J.smcp" k="15"/>
<hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="15"/>
<hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="15"/>
<hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp" g2="X.smcp" k="25"/>
<hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp" g2="X" k="50"/>
<hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="15"/>
<hkern g1="Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,OE.smcp,E.smcp,AE.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="10"/>
<hkern g1="F" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="40"/>
<hkern g1="F" g2="J,Jcircumflex" k="65"/>
<hkern g1="F" g2="ampersand" k="10"/>
<hkern g1="F" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="50"/>
<hkern g1="F" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="45"/>
<hkern g1="F" g2="Jcircumflex.smcp,J.smcp" k="60"/>
<hkern g1="F" g2="X.smcp" k="10"/>
<hkern g1="F" g2="ampersand.smcp" k="25"/>
<hkern g1="F.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="10"/>
<hkern g1="F.smcp" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="25"/>
<hkern g1="F.smcp" g2="J,Jcircumflex" k="40"/>
<hkern g1="F.smcp" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="15"/>
<hkern g1="F.smcp" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="25"/>
<hkern g1="F.smcp" g2="Jcircumflex.smcp,J.smcp" k="50"/>
<hkern g1="F.smcp" g2="ampersand.smcp" k="5"/>
<hkern g1="F.smcp" g2="X" k="25"/>
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" g2="T,Tcommaaccent,Tcaron,Tbar" k="5"/>
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="5"/>
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" g2="X" k="5"/>
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" g2="V" k="10"/>
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" g2="W,Wcircumflex" k="5"/>
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" g2="Y,Yacute,Ycircumflex,Ydieresis" k="15"/>
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" g2="question" k="10"/>
<hkern g1="Gbreve.smcp,Gcommaaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1" g2="T,Tcommaaccent,Tcaron,Tbar" k="40"/>
<hkern g1="Gbreve.smcp,Gcommaaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1" g2="Y,Yacute,Ycircumflex,Ydieresis" k="10"/>
<hkern g1="Gbreve.smcp,Gcommaaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1" g2="question" k="25"/>
<hkern g1="Gbreve.smcp,Gcommaaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="5"/>
<hkern g1="Gbreve.smcp,Gcommaaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1" g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" k="5"/>
<hkern g1="Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="45"/>
<hkern g1="Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp" g2="V" k="20"/>
<hkern g1="Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp" g2="W,Wcircumflex" k="5"/>
<hkern g1="Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp" g2="Y,Yacute,Ycircumflex,Ydieresis" k="35"/>
<hkern g1="Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp" g2="question" k="5"/>
<hkern g1="K,Kcommaaccent" g2="X.smcp" k="20"/>
<hkern g1="K,Kcommaaccent" g2="ampersand.smcp" k="5"/>
<hkern g1="K,Kcommaaccent" g2="Y,Yacute,Ycircumflex,Ydieresis" k="15"/>
<hkern g1="K,Kcommaaccent" g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" k="60"/>
<hkern g1="K,Kcommaaccent" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" k="50"/>
<hkern g1="K,Kcommaaccent" g2="bullet.case" k="70"/>
<hkern g1="K,Kcommaaccent" g2="hyphen.case,endash.case,emdash.case,periodcentered.case" k="70"/>
<hkern g1="K,Kcommaaccent" g2="a,ae,aogonek,aeacute" k="15"/>
<hkern g1="K,Kcommaaccent" g2="c,d,e,g,o,q,ccedilla,eth,oslash,dcaron,eogonek,oe,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1" k="30"/>
<hkern g1="K,Kcommaaccent" g2="f,longs,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1" k="30"/>
<hkern g1="K,Kcommaaccent" g2="t,tcommaaccent,tcaron,tbar" k="45"/>
<hkern g1="K,Kcommaaccent" g2="u,uogonek,y.alt1" k="20"/>
<hkern g1="K,Kcommaaccent" g2="v" k="60"/>
<hkern g1="K,Kcommaaccent" g2="w" k="40"/>
<hkern g1="K,Kcommaaccent" g2="x" k="30"/>
<hkern g1="K,Kcommaaccent" g2="y,yacute,ydieresis,ycircumflex" k="60"/>
<hkern g1="K,Kcommaaccent" g2="hyphen,periodcentered,endash,emdash" k="50"/>
<hkern g1="K,Kcommaaccent" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="55"/>
<hkern g1="K,Kcommaaccent" g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp" k="20"/>
<hkern g1="K,Kcommaaccent" g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" k="55"/>
<hkern g1="K,Kcommaaccent" g2="Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp" k="50"/>
<hkern g1="K,Kcommaaccent" g2="V.smcp" k="45"/>
<hkern g1="K,Kcommaaccent" g2="Wcircumflex.smcp,W.smcp" k="40"/>
<hkern g1="Kcommaaccent.smcp,K.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="25"/>
<hkern g1="Kcommaaccent.smcp,K.smcp" g2="V" k="20"/>
<hkern g1="Kcommaaccent.smcp,K.smcp" g2="W,Wcircumflex" k="15"/>
<hkern g1="Kcommaaccent.smcp,K.smcp" g2="Y,Yacute,Ycircumflex,Ydieresis" k="20"/>
<hkern g1="Kcommaaccent.smcp,K.smcp" g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" k="45"/>
<hkern g1="Kcommaaccent.smcp,K.smcp" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" k="20"/>
<hkern g1="Kcommaaccent.smcp,K.smcp" g2="hyphen,periodcentered,endash,emdash" k="35"/>
<hkern g1="Kcommaaccent.smcp,K.smcp" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="45"/>
<hkern g1="Kcommaaccent.smcp,K.smcp" g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" k="15"/>
<hkern g1="Kcommaaccent.smcp,K.smcp" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="20"/>
<hkern g1="Kcommaaccent.smcp,K.smcp" g2="bullet" k="45"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="T,Tcommaaccent,Tcaron,Tbar" k="100"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="ampersand" k="5"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="V" k="90"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="W,Wcircumflex" k="70"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="Y,Yacute,Ycircumflex,Ydieresis" k="125"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="question" k="110"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="130"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" k="90"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" k="30"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="bullet.case" k="110"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="hyphen.case,endash.case,emdash.case,periodcentered.case" k="75"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="a,ae,aogonek,aeacute" k="10"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="c,d,e,g,o,q,ccedilla,eth,oslash,dcaron,eogonek,oe,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1" k="15"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="t,tcommaaccent,tcaron,tbar" k="35"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="u,uogonek,y.alt1" k="10"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="v" k="55"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="w" k="35"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="y,yacute,ydieresis,ycircumflex" k="55"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="35"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" k="75"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp" k="20"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="V.smcp" k="55"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="Wcircumflex.smcp,W.smcp" k="45"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="25"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="asterisk" k="170"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="dagger" k="130"/>
<hkern g1="L,Lacute,Lcommaaccent,Lslash" g2="daggerdbl" k="95"/>
<hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp" g2="asterisk" k="115"/>
<hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="15"/>
<hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" k="30"/>
<hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="105"/>
<hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp" g2="V" k="95"/>
<hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp" g2="W,Wcircumflex" k="50"/>
<hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp" g2="Y,Yacute,Ycircumflex,Ydieresis" k="120"/>
<hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="110"/>
<hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp" g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" k="85"/>
<hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp" g2="V.smcp" k="70"/>
<hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp" g2="Wcircumflex.smcp,W.smcp" k="60"/>
<hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp" g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" k="100"/>
<hkern g1="P" g2="Y,Yacute,Ycircumflex,Ydieresis" k="5"/>
<hkern g1="P" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="75"/>
<hkern g1="P" g2="J,Jcircumflex" k="115"/>
<hkern g1="P" g2="X" k="15"/>
<hkern g1="P" g2="ampersand" k="30"/>
<hkern g1="P" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="90"/>
<hkern g1="P" g2="a,ae,aogonek,aeacute" k="20"/>
<hkern g1="P" g2="c,d,e,g,o,q,ccedilla,eth,oslash,dcaron,eogonek,oe,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1" k="15"/>
<hkern g1="P" g2="hyphen,periodcentered,endash,emdash" k="30"/>
<hkern g1="P" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="80"/>
<hkern g1="P" g2="Jcircumflex.smcp,J.smcp" k="100"/>
<hkern g1="P" g2="ampersand.smcp" k="40"/>
<hkern g1="P.smcp" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="50"/>
<hkern g1="P.smcp" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="45"/>
<hkern g1="P.smcp" g2="Jcircumflex.smcp,J.smcp" k="75"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="15"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="T,Tcommaaccent,Tcaron,Tbar" k="10"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="V" k="10"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="Y,Yacute,Ycircumflex,Ydieresis" k="17"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="5"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" k="5"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="ampersand" k="5"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="a,ae,aogonek,aeacute" k="20"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="c,d,e,g,o,q,ccedilla,eth,oslash,dcaron,eogonek,oe,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1" k="30"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="ampersand.smcp" k="15"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="bullet.case" k="10"/>
<hkern g1="R,Racute,Rcommaaccent,Rcaron" g2="s,scedilla,scommaaccent" k="10"/>
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" g2="T,Tcommaaccent,Tcaron,Tbar" k="10"/>
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" g2="Y,Yacute,Ycircumflex,Ydieresis" k="10"/>
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" k="10"/>
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" g2="V.smcp" k="5"/>
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" g2="Wcircumflex.smcp,W.smcp" k="5"/>
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" k="20"/>
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="10"/>
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" g2="t,tcommaaccent,tcaron,tbar" k="15"/>
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" g2="v" k="10"/>
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" g2="w" k="5"/>
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" g2="x" k="15"/>
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" g2="y,yacute,ydieresis,ycircumflex" k="10"/>
<hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="55"/>
<hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp" g2="V" k="20"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="75"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" k="25"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" k="20"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="V.smcp" k="25"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="Wcircumflex.smcp,W.smcp" k="25"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" k="25"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="65"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="J,Jcircumflex" k="85"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="ampersand" k="55"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="95"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="a,ae,aogonek,aeacute" k="85"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="c,d,e,g,o,q,ccedilla,eth,oslash,dcaron,eogonek,oe,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1" k="105"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="hyphen,periodcentered,endash,emdash" k="60"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="100"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="Jcircumflex.smcp,J.smcp" k="105"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="ampersand.smcp" k="95"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="bullet.case" k="70"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="s,scedilla,scommaaccent" k="85"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="v" k="40"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="w" k="40"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="x" k="50"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="y,yacute,ydieresis,ycircumflex" k="40"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" k="5"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="colon,semicolon" k="50"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="hyphen.case,endash.case,emdash.case,periodcentered.case" k="50"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="f,longs,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1" k="25"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="m,n,p,r,z,ncommaaccent,eng,rcommaaccent" k="75"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp" k="45"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp" k="55"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp" k="40"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="X.smcp" k="25"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp" k="30"/>
<hkern g1="T,Tcommaaccent,Tcaron,Tbar" g2="u,uogonek,y.alt1" k="75"/>
<hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="30"/>
<hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="20"/>
<hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" g2="V" k="5"/>
<hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="35"/>
<hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" g2="ampersand" k="40"/>
<hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="60"/>
<hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="45"/>
<hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" g2="Jcircumflex.smcp,J.smcp" k="75"/>
<hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" g2="ampersand.smcp" k="45"/>
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="30"/>
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g2="J,Jcircumflex" k="20"/>
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="30"/>
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="15"/>
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" g2="Jcircumflex.smcp,J.smcp" k="20"/>
<hkern g1="IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="10"/>
<hkern g1="IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="40"/>
<hkern g1="IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp" g2="V" k="20"/>
<hkern g1="IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="10"/>
<hkern g1="IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="10"/>
<hkern g1="V" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="50"/>
<hkern g1="V" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="50"/>
<hkern g1="V" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="90"/>
<hkern g1="V" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" k="20"/>
<hkern g1="V" g2="J,Jcircumflex" k="95"/>
<hkern g1="V" g2="ampersand" k="35"/>
<hkern g1="V" g2="hyphen.case,endash.case,emdash.case,periodcentered.case" k="30"/>
<hkern g1="V" g2="bullet.case" k="45"/>
<hkern g1="V" g2="a,ae,aogonek,aeacute" k="65"/>
<hkern g1="V" g2="c,d,e,g,o,q,ccedilla,eth,oslash,dcaron,eogonek,oe,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1" k="60"/>
<hkern g1="V" g2="f,longs,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1" k="25"/>
<hkern g1="V" g2="m,n,p,r,z,ncommaaccent,eng,rcommaaccent" k="50"/>
<hkern g1="V" g2="s,scedilla,scommaaccent" k="45"/>
<hkern g1="V" g2="t,tcommaaccent,tcaron,tbar" k="15"/>
<hkern g1="V" g2="u,uogonek,y.alt1" k="50"/>
<hkern g1="V" g2="v" k="15"/>
<hkern g1="V" g2="w" k="10"/>
<hkern g1="V" g2="x" k="25"/>
<hkern g1="V" g2="y,yacute,ydieresis,ycircumflex" k="10"/>
<hkern g1="V" g2="hyphen,periodcentered,endash,emdash" k="55"/>
<hkern g1="V" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="50"/>
<hkern g1="V" g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp" k="20"/>
<hkern g1="V" g2="Jcircumflex.smcp,J.smcp" k="95"/>
<hkern g1="V" g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp" k="25"/>
<hkern g1="V" g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" k="5"/>
<hkern g1="V" g2="Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp" k="20"/>
<hkern g1="V" g2="ampersand.smcp" k="55"/>
<hkern g1="V.smcp" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="20"/>
<hkern g1="V.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="25"/>
<hkern g1="V.smcp" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="30"/>
<hkern g1="V.smcp" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="30"/>
<hkern g1="V.smcp" g2="ampersand" k="30"/>
<hkern g1="V.smcp" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="20"/>
<hkern g1="V.smcp" g2="Jcircumflex.smcp,J.smcp" k="75"/>
<hkern g1="W" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="45"/>
<hkern g1="W" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="50"/>
<hkern g1="W" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="65"/>
<hkern g1="W" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" k="20"/>
<hkern g1="W" g2="J,Jcircumflex" k="50"/>
<hkern g1="W" g2="ampersand" k="20"/>
<hkern g1="W" g2="hyphen.case,endash.case,emdash.case,periodcentered.case" k="5"/>
<hkern g1="W" g2="bullet.case" k="20"/>
<hkern g1="W" g2="a,ae,aogonek,aeacute" k="40"/>
<hkern g1="W" g2="c,d,e,g,o,q,ccedilla,eth,oslash,dcaron,eogonek,oe,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1" k="40"/>
<hkern g1="W" g2="f,longs,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1" k="15"/>
<hkern g1="W" g2="m,n,p,r,z,ncommaaccent,eng,rcommaaccent" k="30"/>
<hkern g1="W" g2="s,scedilla,scommaaccent" k="25"/>
<hkern g1="W" g2="t,tcommaaccent,tcaron,tbar" k="15"/>
<hkern g1="W" g2="u,uogonek,y.alt1" k="30"/>
<hkern g1="W" g2="v" k="5"/>
<hkern g1="W" g2="x" k="25"/>
<hkern g1="W" g2="y,yacute,ydieresis,ycircumflex" k="5"/>
<hkern g1="W" g2="hyphen,periodcentered,endash,emdash" k="35"/>
<hkern g1="W" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="30"/>
<hkern g1="W" g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp" k="5"/>
<hkern g1="W" g2="Jcircumflex.smcp,J.smcp" k="50"/>
<hkern g1="W.smcp" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="20"/>
<hkern g1="W.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="25"/>
<hkern g1="W.smcp" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="20"/>
<hkern g1="W.smcp" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="45"/>
<hkern g1="W.smcp" g2="ampersand" k="15"/>
<hkern g1="W.smcp" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="25"/>
<hkern g1="W.smcp" g2="Jcircumflex.smcp,J.smcp" k="45"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="65"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="75"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="90"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" k="40"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="J,Jcircumflex" k="115"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="ampersand" k="60"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="hyphen.case,endash.case,emdash.case,periodcentered.case" k="95"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="bullet.case" k="85"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="a,ae,aogonek,aeacute" k="115"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="c,d,e,g,o,q,ccedilla,eth,oslash,dcaron,eogonek,oe,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1" k="125"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="f,longs,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1" k="55"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="m,n,p,r,z,ncommaaccent,eng,rcommaaccent" k="90"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="s,scedilla,scommaaccent" k="100"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="t,tcommaaccent,tcaron,tbar" k="30"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="u,uogonek,y.alt1" k="90"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="v" k="60"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="w" k="60"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="x" k="75"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="y,yacute,ydieresis,ycircumflex" k="60"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="hyphen,periodcentered,endash,emdash" k="120"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="80"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp" k="35"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="Jcircumflex.smcp,J.smcp" k="120"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp" k="45"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" k="20"/>
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" g2="colon,semicolon" k="65"/>
<hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="50"/>
<hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="25"/>
<hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="40"/>
<hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="70"/>
<hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" g2="ampersand" k="45"/>
<hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="30"/>
<hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" g2="Jcircumflex.smcp,J.smcp" k="95"/>
<hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" g2="ampersand.smcp" k="55"/>
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" k="15"/>
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" g2="bullet.case" k="50"/>
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" g2="hyphen.case,endash.case,emdash.case,periodcentered.case" k="60"/>
<hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="30"/>
<hkern g1="a,h,m,n,aogonek,hcircumflex,ncommaaccent,napostrophe,f_h" g2="T,Tcommaaccent,Tcaron,Tbar" k="100"/>
<hkern g1="a,h,m,n,aogonek,hcircumflex,ncommaaccent,napostrophe,f_h" g2="question" k="40"/>
<hkern g1="a,h,m,n,aogonek,hcircumflex,ncommaaccent,napostrophe,f_h" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="10"/>
<hkern g1="a,h,m,n,aogonek,hcircumflex,ncommaaccent,napostrophe,f_h" g2="Y,Yacute,Ycircumflex,Ydieresis" k="110"/>
<hkern g1="a,h,m,n,aogonek,hcircumflex,ncommaaccent,napostrophe,f_h" g2="V" k="70"/>
<hkern g1="a,h,m,n,aogonek,hcircumflex,ncommaaccent,napostrophe,f_h" g2="W,Wcircumflex" k="45"/>
<hkern g1="ampersand" g2="T,Tcommaaccent,Tcaron,Tbar" k="70"/>
<hkern g1="ampersand" g2="Y,Yacute,Ycircumflex,Ydieresis" k="85"/>
<hkern g1="ampersand" g2="V" k="50"/>
<hkern g1="ampersand" g2="W,Wcircumflex" k="40"/>
<hkern g1="ampersand" g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" k="60"/>
<hkern g1="ampersand" g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" k="60"/>
<hkern g1="ampersand" g2="V.smcp" k="40"/>
<hkern g1="ampersand" g2="Wcircumflex.smcp,W.smcp" k="10"/>
<hkern g1="ampersand.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="95"/>
<hkern g1="ampersand.smcp" g2="V" k="70"/>
<hkern g1="ampersand.smcp" g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" k="60"/>
<hkern g1="ampersand.smcp" g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" k="60"/>
<hkern g1="asterisk" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="95"/>
<hkern g1="asterisk" g2="J,Jcircumflex" k="120"/>
<hkern g1="asterisk" g2="Jcircumflex.smcp,J.smcp" k="105"/>
<hkern g1="asterisk" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="60"/>
<hkern g1="b,o,p,oslash,thorn,f_b" g2="T,Tcommaaccent,Tcaron,Tbar" k="105"/>
<hkern g1="b,o,p,oslash,thorn,f_b" g2="question" k="50"/>
<hkern g1="b,o,p,oslash,thorn,f_b" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="10"/>
<hkern g1="b,o,p,oslash,thorn,f_b" g2="Y,Yacute,Ycircumflex,Ydieresis" k="120"/>
<hkern g1="b,o,p,oslash,thorn,f_b" g2="V" k="60"/>
<hkern g1="b,o,p,oslash,thorn,f_b" g2="W,Wcircumflex" k="40"/>
<hkern g1="b,o,p,oslash,thorn,f_b" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="5"/>
<hkern g1="b,o,p,oslash,thorn,f_b" g2="x" k="35"/>
<hkern g1="bullet" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="10"/>
<hkern g1="bullet.case" g2="T,Tcommaaccent,Tcaron,Tbar" k="70"/>
<hkern g1="bullet.case" g2="Y,Yacute,Ycircumflex,Ydieresis" k="85"/>
<hkern g1="bullet.case" g2="V" k="45"/>
<hkern g1="bullet.case" g2="W,Wcircumflex" k="20"/>
<hkern g1="bullet.case" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="35"/>
<hkern g1="bullet.case" g2="J,Jcircumflex" k="80"/>
<hkern g1="bullet.case" g2="X" k="60"/>
<hkern g1="bullet.case" g2="Z,Zacute,Zdotaccent,Zcaron" k="50"/>
<hkern g1="c,cent,ccedilla" g2="T,Tcommaaccent,Tcaron,Tbar" k="75"/>
<hkern g1="c,cent,ccedilla" g2="question" k="25"/>
<hkern g1="c,cent,ccedilla" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="5"/>
<hkern g1="c,cent,ccedilla" g2="Y,Yacute,Ycircumflex,Ydieresis" k="65"/>
<hkern g1="c,cent,ccedilla" g2="V" k="40"/>
<hkern g1="c,cent,ccedilla" g2="W,Wcircumflex" k="25"/>
<hkern g1="colon,semicolon" g2="T,Tcommaaccent,Tcaron,Tbar" k="50"/>
<hkern g1="colon,semicolon" g2="Y,Yacute,Ycircumflex,Ydieresis" k="65"/>
<hkern g1="e,ae,eogonek,oe,aeacute,ae.alt1,aeacute.alt1" g2="T,Tcommaaccent,Tcaron,Tbar" k="95"/>
<hkern g1="e,ae,eogonek,oe,aeacute,ae.alt1,aeacute.alt1" g2="question" k="40"/>
<hkern g1="e,ae,eogonek,oe,aeacute,ae.alt1,aeacute.alt1" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="5"/>
<hkern g1="e,ae,eogonek,oe,aeacute,ae.alt1,aeacute.alt1" g2="Y,Yacute,Ycircumflex,Ydieresis" k="110"/>
<hkern g1="e,ae,eogonek,oe,aeacute,ae.alt1,aeacute.alt1" g2="V" k="55"/>
<hkern g1="e,ae,eogonek,oe,aeacute,ae.alt1,aeacute.alt1" g2="W,Wcircumflex" k="40"/>
<hkern g1="e,ae,eogonek,oe,aeacute,ae.alt1,aeacute.alt1" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="5"/>
<hkern g1="e,ae,eogonek,oe,aeacute,ae.alt1,aeacute.alt1" g2="asterisk" k="15"/>
<hkern g1="eth" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="5"/>
<hkern g1="f,longs,f_f,f.alt1" g2="T,Tcommaaccent,Tcaron,Tbar" k="-40"/>
<hkern g1="f,longs,f_f,f.alt1" g2="question" k="-55"/>
<hkern g1="f,longs,f_f,f.alt1" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="-65"/>
<hkern g1="f,longs,f_f,f.alt1" g2="Y,Yacute,Ycircumflex,Ydieresis" k="-60"/>
<hkern g1="f,longs,f_f,f.alt1" g2="V" k="-65"/>
<hkern g1="f,longs,f_f,f.alt1" g2="W,Wcircumflex" k="-65"/>
<hkern g1="f,longs,f_f,f.alt1" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="45"/>
<hkern g1="f,longs,f_f,f.alt1" g2="X" k="-40"/>
<hkern g1="f,longs,f_f,f.alt1" g2="Z,Zacute,Zdotaccent,Zcaron" k="-40"/>
<hkern g1="f,longs,f_f,f.alt1" g2="asterisk" k="-65"/>
<hkern g1="f,longs,f_f,f.alt1" g2="ampersand" k="5"/>
<hkern g1="f,longs,f_f,f.alt1" g2="ampersand.smcp" k="5"/>
<hkern g1="f,longs,f_f,f.alt1" g2="parenright,bracketright,braceright,parenright.case,bracketright.case,braceright.case" k="-70"/>
<hkern g1="f,longs,f_f,f.alt1" g2="exclam,B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Imacron,Iogonek,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Racute,Rcommaaccent,Rcaron" k="-40"/>
<hkern g1="f,longs,f_f,f.alt1" g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" k="-25"/>
<hkern g1="f,longs,f_f,f.alt1" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="-40"/>
<hkern g1="g,q,eng,y.alt1" g2="T,Tcommaaccent,Tcaron,Tbar" k="75"/>
<hkern g1="g,q,eng,y.alt1" g2="question" k="25"/>
<hkern g1="g,q,eng,y.alt1" g2="Y,Yacute,Ycircumflex,Ydieresis" k="90"/>
<hkern g1="g,q,eng,y.alt1" g2="V" k="50"/>
<hkern g1="g,q,eng,y.alt1" g2="W,Wcircumflex" k="30"/>
<hkern g1="g,q,eng,y.alt1" g2="j,jcircumflex" k="-50"/>
<hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case" g2="T,Tcommaaccent,Tcaron,Tbar" k="50"/>
<hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case" g2="Y,Yacute,Ycircumflex,Ydieresis" k="95"/>
<hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case" g2="V" k="30"/>
<hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case" g2="W,Wcircumflex" k="5"/>
<hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="45"/>
<hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case" g2="J,Jcircumflex" k="65"/>
<hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case" g2="X" k="75"/>
<hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case" g2="Z,Zacute,Zdotaccent,Zcaron" k="60"/>
<hkern g1="hyphen,periodcentered,endash,emdash" g2="Y,Yacute,Ycircumflex,Ydieresis" k="115"/>
<hkern g1="hyphen,periodcentered,endash,emdash" g2="V" k="55"/>
<hkern g1="hyphen,periodcentered,endash,emdash" g2="W,Wcircumflex" k="35"/>
<hkern g1="hyphen,periodcentered,endash,emdash" g2="X" k="45"/>
<hkern g1="k,kcommaaccent,kgreenlandic,f_k" g2="T,Tcommaaccent,Tcaron,Tbar" k="55"/>
<hkern g1="k,kcommaaccent,kgreenlandic,f_k" g2="Y,Yacute,Ycircumflex,Ydieresis" k="70"/>
<hkern g1="k,kcommaaccent,kgreenlandic,f_k" g2="V" k="25"/>
<hkern g1="k,kcommaaccent,kgreenlandic,f_k" g2="W,Wcircumflex" k="25"/>
<hkern g1="k,kcommaaccent,kgreenlandic,f_k" g2="c,d,e,g,o,q,ccedilla,eth,oslash,dcaron,eogonek,oe,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1" k="5"/>
<hkern g1="k,kcommaaccent,kgreenlandic,f_k" g2="bullet" k="25"/>
<hkern g1="k,kcommaaccent,kgreenlandic,f_k" g2="hyphen,periodcentered,endash,emdash" k="25"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="T,Tcommaaccent,Tcaron,Tbar" k="95"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" k="30"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="V" k="85"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="W,Wcircumflex" k="65"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="15"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="c,d,e,g,o,q,ccedilla,eth,oslash,dcaron,eogonek,oe,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1" k="5"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="f,longs,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1" k="25"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="t,tcommaaccent,tcaron,tbar" k="25"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="v" k="65"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="w" k="40"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="y,yacute,ydieresis,ycircumflex" k="55"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="one" k="90"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="zero,six" k="25"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="seven" k="70"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp" k="60"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="V.smcp" k="30"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp" k="10"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="Wcircumflex.smcp,W.smcp" k="45"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="Y,Yacute,Ycircumflex,Ydieresis" k="90"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp" k="70"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="80"/>
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" g2="j,jcircumflex" k="-40"/>
<hkern g1="questiondown" g2="j,jcircumflex" k="-160"/>
<hkern g1="questiondown.case" g2="T,Tcommaaccent,Tcaron,Tbar" k="100"/>
<hkern g1="questiondown.case" g2="V" k="75"/>
<hkern g1="questiondown.case" g2="W,Wcircumflex" k="65"/>
<hkern g1="questiondown.case" g2="Y,Yacute,Ycircumflex,Ydieresis" k="95"/>
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="20"/>
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="100"/>
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" k="5"/>
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp" k="80"/>
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" g2="s,scedilla,scommaaccent" k="30"/>
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" g2="J,Jcircumflex" k="100"/>
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="80"/>
<hkern g1="r,racute,rcommaaccent,rcaron" g2="T,Tcommaaccent,Tcaron,Tbar" k="40"/>
<hkern g1="r,racute,rcommaaccent,rcaron" g2="V" k="25"/>
<hkern g1="r,racute,rcommaaccent,rcaron" g2="W,Wcircumflex" k="10"/>
<hkern g1="r,racute,rcommaaccent,rcaron" g2="Y,Yacute,Ycircumflex,Ydieresis" k="50"/>
<hkern g1="r,racute,rcommaaccent,rcaron" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="85"/>
<hkern g1="r,racute,rcommaaccent,rcaron" g2="X" k="15"/>
<hkern g1="s,scedilla,scommaaccent" g2="T,Tcommaaccent,Tcaron,Tbar" k="80"/>
<hkern g1="s,scedilla,scommaaccent" g2="V" k="45"/>
<hkern g1="s,scedilla,scommaaccent" g2="W,Wcircumflex" k="45"/>
<hkern g1="s,scedilla,scommaaccent" g2="Y,Yacute,Ycircumflex,Ydieresis" k="95"/>
<hkern g1="s,scedilla,scommaaccent" g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark" k="20"/>
<hkern g1="s,scedilla,scommaaccent" g2="X" k="5"/>
<hkern g1="s,scedilla,scommaaccent" g2="question" k="45"/>
<hkern g1="t,tcommaaccent,tbar" g2="T,Tcommaaccent,Tcaron,Tbar" k="40"/>
<hkern g1="t,tcommaaccent,tbar" g2="V" k="30"/>
<hkern g1="t,tcommaaccent,tbar" g2="W,Wcircumflex" k="15"/>
<hkern g1="t,tcommaaccent,tbar" g2="Y,Yacute,Ycircumflex,Ydieresis" k="35"/>
<hkern g1="u,z,uogonek,a.alt1,aogonek.alt1" g2="T,Tcommaaccent,Tcaron,Tbar" k="75"/>
<hkern g1="u,z,uogonek,a.alt1,aogonek.alt1" g2="V" k="50"/>
<hkern g1="u,z,uogonek,a.alt1,aogonek.alt1" g2="W,Wcircumflex" k="30"/>
<hkern g1="u,z,uogonek,a.alt1,aogonek.alt1" g2="Y,Yacute,Ycircumflex,Ydieresis" k="90"/>
<hkern g1="u,z,uogonek,a.alt1,aogonek.alt1" g2="question" k="25"/>
<hkern g1="v" g2="T,Tcommaaccent,Tcaron,Tbar" k="40"/>
<hkern g1="v" g2="V" k="15"/>
<hkern g1="v" g2="W,Wcircumflex" k="5"/>
<hkern g1="v" g2="Y,Yacute,Ycircumflex,Ydieresis" k="60"/>
<hkern g1="v" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="20"/>
<hkern g1="v" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="65"/>
<hkern g1="v" g2="X" k="35"/>
<hkern g1="w" g2="T,Tcommaaccent,Tcaron,Tbar" k="40"/>
<hkern g1="w" g2="V" k="10"/>
<hkern g1="w" g2="Y,Yacute,Ycircumflex,Ydieresis" k="60"/>
<hkern g1="w" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="15"/>
<hkern g1="w" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="40"/>
<hkern g1="w" g2="X" k="40"/>
<hkern g1="y,yacute,ydieresis,ycircumflex" g2="T,Tcommaaccent,Tcaron,Tbar" k="40"/>
<hkern g1="y,yacute,ydieresis,ycircumflex" g2="V" k="10"/>
<hkern g1="y,yacute,ydieresis,ycircumflex" g2="W,Wcircumflex" k="5"/>
<hkern g1="y,yacute,ydieresis,ycircumflex" g2="Y,Yacute,Ycircumflex,Ydieresis" k="60"/>
<hkern g1="y,yacute,ydieresis,ycircumflex" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta" k="20"/>
<hkern g1="y,yacute,ydieresis,ycircumflex" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="55"/>
<hkern g1="y,yacute,ydieresis,ycircumflex" g2="X" k="35"/>
<hkern g1="y,yacute,ydieresis,ycircumflex" g2="question" k="5"/>
<hkern g1="X" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="50"/>
<hkern g1="X" g2="f,longs,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1" k="30"/>
<hkern g1="X" g2="t,tcommaaccent,tcaron,tbar" k="30"/>
<hkern g1="X" g2="v" k="35"/>
<hkern g1="X" g2="w" k="40"/>
<hkern g1="X" g2="y,yacute,ydieresis,ycircumflex" k="35"/>
<hkern g1="X" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1" k="30"/>
<hkern g1="X" g2="bullet.case" k="60"/>
<hkern g1="X" g2="hyphen.case,endash.case,emdash.case,periodcentered.case" k="75"/>
<hkern g1="X" g2="hyphen,periodcentered,endash,emdash" k="45"/>
<hkern g1="X.smcp" g2="T,Tcommaaccent,Tcaron,Tbar" k="25"/>
<hkern g1="X.smcp" g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1" k="25"/>
<hkern g1="X.smcp" g2="ampersand" k="20"/>
<hkern g1="zero,nine" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="25"/>
<hkern g1="seven" g2="comma,period,quotesinglbase,quotedblbase,ellipsis" k="95"/>
<hkern g1="x" g2="T,Tcommaaccent,Tcaron,Tbar" k="50"/>
<hkern g1="x" g2="V" k="25"/>
<hkern g1="x" g2="W,Wcircumflex" k="25"/>
<hkern g1="x" g2="c,d,e,g,o,q,ccedilla,eth,oslash,dcaron,eogonek,oe,a.alt1,aogonek.alt1,ae.alt1,aeacute.alt1" k="35"/>
<hkern g1="x" g2="Y,Yacute,Ycircumflex,Ydieresis" k="75"/>
<hkern g1="parenleft,bracketleft,braceleft,parenleft.case,bracketleft.case,braceleft.case" g2="j,jcircumflex" k="-110"/>
<hkern g1="one.numr" g2="fraction" k="-25"/>
<hkern g1="seven.numr" g2="fraction" k="50"/>
<hkern g1="fraction" g2="four.dnom" k="50"/>
<hkern g1="fraction" g2="one.dnom" k="-10"/>
</font>
</defs>
</svg>
