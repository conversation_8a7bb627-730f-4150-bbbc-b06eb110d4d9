<!DOCTYPE html>
<!--
Copyright (c) 2003-2012, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
-->
<html>
<head>
	<meta charset="utf-8">
	<title>Shared-Space Plugin &mdash; CKEditor Sample</title>
	<script src="../../../ckeditor.js"></script>
	<link rel="stylesheet" href="css/sample.css">
	<meta name="ckeditor-sample-name" content="Shared-Space plugin">
	<meta name="ckeditor-sample-group" content="Plugins">
	<meta name="ckeditor-sample-description" content="Having the toolbar and the bottom bar spaces shared by different editor instances.">
</head>
<body>
	 
 
	<div id="top">
		<!-- This div will handle all toolbars -->
	</div>
</br></br></br></br></br>
		 
</br></br></br></br></br>
		  
		<div id="framed1" style="width: 49%; float: left; margin-bottom: 20px;"></div>
		<div id="framed2" style="width: 49%; float: right; margin-bottom: 20px;"></div>
</br></br></br></br></br>
		 
</br></br></br></br></br>
		 

		<div contenteditable="true" id="inline1" style="width: 49%; float: left;display:none;">
			
		</div>
		<div contenteditable="true" id="inline2" style="width: 49%; float: right;display:none;">
			 
		</div>

 

	<div id="bottom" style="display:none;">
		<!-- This div will handle all toolbars -->
	</div>

	<script>

		// Turn off automatic editor creation first.
		CKEDITOR.disableAutoInline = true;

		CKEDITOR.inline( 'inline1', {
			extraPlugins: 'sharedspace',
			removePlugins: 'floatingspace,resize',
			sharedSpaces: {
				top: 'top',
				bottom: 'bottom'
			}
		});

		CKEDITOR.inline( 'inline2', {
			extraPlugins: 'sharedspace',
			removePlugins: 'floatingspace,resize',
			sharedSpaces: {
				top: 'top',
				bottom: 'bottom'
			}
		});

		CKEDITOR.appendTo( 'framed1', {
				extraPlugins: 'sharedspace',
				removePlugins: 'maximize,resize',
				sharedSpaces: {
					top: 'top',
					bottom: 'bottom'
				}
			},
			document.getElementById( 'inline1' ).innerHTML
		);

		CKEDITOR.appendTo( 'framed2', {
				extraPlugins: 'sharedspace',
				removePlugins: 'maximize,resize',
				sharedSpaces: {
					top: 'top',
					bottom: 'bottom'
				}
			},
			document.getElementById( 'inline2' ).innerHTML
		);

	</script>

	 
</body>
</html>
