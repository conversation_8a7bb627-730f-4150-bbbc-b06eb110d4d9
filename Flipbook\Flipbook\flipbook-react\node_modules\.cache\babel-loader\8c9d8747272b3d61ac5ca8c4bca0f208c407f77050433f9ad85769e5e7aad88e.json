{"ast": null, "code": "var _jsxFileName = \"D:\\\\TradeWorks\\\\Flipbook\\\\Flipbook\\\\Flipbook\\\\flipbook-react\\\\src\\\\components\\\\FrontPage\\\\FrontPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport TextEditor from '../TextEditor/TextEditor';\nimport ImageLibrary from '../ImageLibrary/ImageLibrary';\nimport './FrontPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FrontPage = ({\n  title,\n  subtitle,\n  backgroundImage,\n  onTitleChange,\n  onSubtitleChange,\n  onBackgroundImageChange,\n  onNext\n}) => {\n  _s();\n  const [activeEditor, setActiveEditor] = useState(null);\n  const [showImageLibrary, setShowImageLibrary] = useState(false);\n  const handleImageUploadClick = () => {\n    setShowImageLibrary(true);\n  };\n  const handleImageSelect = imageUrl => {\n    onBackgroundImageChange(imageUrl);\n    setShowImageLibrary(false);\n  };\n  const handleEditorFocus = editor => {\n    setActiveEditor(editor);\n  };\n  const handleEditorBlur = () => {\n    setActiveEditor(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"front-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"pages-title\",\n        children: \"PAGES\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flipbook-title\",\n        children: /*#__PURE__*/_jsxDEV(TextEditor, {\n          value: title,\n          onChange: onTitleChange,\n          placeholder: \"Hanash\",\n          isActive: activeEditor === 'title',\n          onFocus: () => handleEditorFocus('title'),\n          onBlur: handleEditorBlur,\n          className: \"title-editor\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      onClick: handleImageUploadClick,\n      style: {\n        backgroundImage: backgroundImage ? `url(${backgroundImage})` : undefined\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"center-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"name-section\",\n          children: /*#__PURE__*/_jsxDEV(TextEditor, {\n            value: subtitle || \"ZARA IRUM\",\n            onChange: onSubtitleChange,\n            placeholder: \"ZARA IRUM\",\n            isActive: activeEditor === 'subtitle',\n            onFocus: () => handleEditorFocus('subtitle'),\n            onBlur: handleEditorBlur,\n            className: \"main-name-editor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profession-title\",\n          children: \"ARCHITECT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), !backgroundImage && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"upload-hint\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Tip: A single click on an image will open your Image editing tools. A double click will open your Image toolbar and Rollover\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navigation-controls\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"next-page-btn\",\n        onClick: onNext,\n        children: \"\\u2192\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ImageLibrary, {\n      isOpen: showImageLibrary,\n      onClose: () => setShowImageLibrary(false),\n      onSelectImage: handleImageSelect\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n_s(FrontPage, \"dnNhol+1SmfCM+zLLJzsemBVpt0=\");\n_c = FrontPage;\nexport default FrontPage;\nvar _c;\n$RefreshReg$(_c, \"FrontPage\");", "map": {"version": 3, "names": ["React", "useState", "TextEditor", "ImageLibrary", "jsxDEV", "_jsxDEV", "FrontPage", "title", "subtitle", "backgroundImage", "onTitleChange", "onSubtitleChange", "onBackgroundImageChange", "onNext", "_s", "activeEditor", "setActiveEditor", "showImageLibrary", "setShowImageLibrary", "handleImageUploadClick", "handleImageSelect", "imageUrl", "handleEditorFocus", "editor", "handleEditorBlur", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "placeholder", "isActive", "onFocus", "onBlur", "onClick", "style", "undefined", "isOpen", "onClose", "onSelectImage", "_c", "$RefreshReg$"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/FrontPage/FrontPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport TextEditor from '../TextEditor/TextEditor';\nimport ImageLibrary from '../ImageLibrary/ImageLibrary';\nimport './FrontPage.css';\n\ninterface FrontPageProps {\n  title: string;\n  subtitle: string;\n  backgroundImage: string;\n  onTitleChange: (title: string) => void;\n  onSubtitleChange: (subtitle: string) => void;\n  onBackgroundImageChange: (imageUrl: string) => void;\n  onNext: () => void;\n}\n\nconst FrontPage: React.FC<FrontPageProps> = ({\n  title,\n  subtitle,\n  backgroundImage,\n  onTitleChange,\n  onSubtitleChange,\n  onBackgroundImageChange,\n  onNext\n}) => {\n  const [activeEditor, setActiveEditor] = useState<'title' | 'subtitle' | 'name' | null>(null);\n  const [showImageLibrary, setShowImageLibrary] = useState(false);\n\n  const handleImageUploadClick = () => {\n    setShowImageLibrary(true);\n  };\n\n  const handleImageSelect = (imageUrl: string) => {\n    onBackgroundImageChange(imageUrl);\n    setShowImageLibrary(false);\n  };\n\n  const handleEditorFocus = (editor: 'title' | 'subtitle' | 'name') => {\n    setActiveEditor(editor);\n  };\n\n  const handleEditorBlur = () => {\n    setActiveEditor(null);\n  };\n\n  return (\n    <div className=\"front-page\">\n      {/* Top Header */}\n      <div className=\"page-header\">\n        <h1 className=\"pages-title\">PAGES</h1>\n        <div className=\"flipbook-title\">\n          <TextEditor\n            value={title}\n            onChange={onTitleChange}\n            placeholder=\"Hanash\"\n            isActive={activeEditor === 'title'}\n            onFocus={() => handleEditorFocus('title')}\n            onBlur={handleEditorBlur}\n            className=\"title-editor\"\n          />\n        </div>\n      </div>\n\n      {/* Main Content Area */}\n      <div\n        className=\"main-content\"\n        onClick={handleImageUploadClick}\n        style={{\n          backgroundImage: backgroundImage ? `url(${backgroundImage})` : undefined\n        }}\n      >\n        {/* Center Content */}\n        <div className=\"center-content\">\n          <div className=\"name-section\">\n            <TextEditor\n              value={subtitle || \"ZARA IRUM\"}\n              onChange={onSubtitleChange}\n              placeholder=\"ZARA IRUM\"\n              isActive={activeEditor === 'subtitle'}\n              onFocus={() => handleEditorFocus('subtitle')}\n              onBlur={handleEditorBlur}\n              className=\"main-name-editor\"\n            />\n          </div>\n          <div className=\"profession-title\">\n            ARCHITECT\n          </div>\n        </div>\n\n        {/* Upload Hint */}\n        {!backgroundImage && (\n          <div className=\"upload-hint\">\n            <span>Tip: A single click on an image will open your Image editing tools. A double click will open your Image toolbar and Rollover</span>\n          </div>\n        )}\n      </div>\n\n      {/* Navigation Arrow */}\n      <div className=\"navigation-controls\">\n        <button className=\"next-page-btn\" onClick={onNext}>\n          →\n        </button>\n      </div>\n      \n      {/* Image Library Modal */}\n      <ImageLibrary\n        isOpen={showImageLibrary}\n        onClose={() => setShowImageLibrary(false)}\n        onSelectImage={handleImageSelect}\n      />\n    </div>\n  );\n};\n\nexport default FrontPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAYzB,MAAMC,SAAmC,GAAGA,CAAC;EAC3CC,KAAK;EACLC,QAAQ;EACRC,eAAe;EACfC,aAAa;EACbC,gBAAgB;EAChBC,uBAAuB;EACvBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAuC,IAAI,CAAC;EAC5F,MAAM,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMkB,sBAAsB,GAAGA,CAAA,KAAM;IACnCD,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAME,iBAAiB,GAAIC,QAAgB,IAAK;IAC9CT,uBAAuB,CAACS,QAAQ,CAAC;IACjCH,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMI,iBAAiB,GAAIC,MAAqC,IAAK;IACnEP,eAAe,CAACO,MAAM,CAAC;EACzB,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BR,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,oBACEX,OAAA;IAAKoB,SAAS,EAAC,YAAY;IAAAC,QAAA,gBAEzBrB,OAAA;MAAKoB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BrB,OAAA;QAAIoB,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtCzB,OAAA;QAAKoB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BrB,OAAA,CAACH,UAAU;UACT6B,KAAK,EAAExB,KAAM;UACbyB,QAAQ,EAAEtB,aAAc;UACxBuB,WAAW,EAAC,QAAQ;UACpBC,QAAQ,EAAEnB,YAAY,KAAK,OAAQ;UACnCoB,OAAO,EAAEA,CAAA,KAAMb,iBAAiB,CAAC,OAAO,CAAE;UAC1Cc,MAAM,EAAEZ,gBAAiB;UACzBC,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzB,OAAA;MACEoB,SAAS,EAAC,cAAc;MACxBY,OAAO,EAAElB,sBAAuB;MAChCmB,KAAK,EAAE;QACL7B,eAAe,EAAEA,eAAe,GAAG,OAAOA,eAAe,GAAG,GAAG8B;MACjE,CAAE;MAAAb,QAAA,gBAGFrB,OAAA;QAAKoB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BrB,OAAA;UAAKoB,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BrB,OAAA,CAACH,UAAU;YACT6B,KAAK,EAAEvB,QAAQ,IAAI,WAAY;YAC/BwB,QAAQ,EAAErB,gBAAiB;YAC3BsB,WAAW,EAAC,WAAW;YACvBC,QAAQ,EAAEnB,YAAY,KAAK,UAAW;YACtCoB,OAAO,EAAEA,CAAA,KAAMb,iBAAiB,CAAC,UAAU,CAAE;YAC7Cc,MAAM,EAAEZ,gBAAiB;YACzBC,SAAS,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNzB,OAAA;UAAKoB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL,CAACrB,eAAe,iBACfJ,OAAA;QAAKoB,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BrB,OAAA;UAAAqB,QAAA,EAAM;QAA4H;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtI,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNzB,OAAA;MAAKoB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAClCrB,OAAA;QAAQoB,SAAS,EAAC,eAAe;QAACY,OAAO,EAAExB,MAAO;QAAAa,QAAA,EAAC;MAEnD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNzB,OAAA,CAACF,YAAY;MACXqC,MAAM,EAAEvB,gBAAiB;MACzBwB,OAAO,EAAEA,CAAA,KAAMvB,mBAAmB,CAAC,KAAK,CAAE;MAC1CwB,aAAa,EAAEtB;IAAkB;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAChB,EAAA,CAhGIR,SAAmC;AAAAqC,EAAA,GAAnCrC,SAAmC;AAkGzC,eAAeA,SAAS;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}