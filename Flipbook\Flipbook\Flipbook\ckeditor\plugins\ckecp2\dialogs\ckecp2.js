﻿
var className = '';

function AddCustomClassCP2(editorName)
{
    className = '.cke_editor_' + editorName + '_dialog'
    setTimeout(function () {
        $(className).addClass('cp2box');
    },0);
    
}
CKEDITOR.dialog.add('colorpickerDialog', function (editor) {

    var colorstr = getColors(editor.name);
    //AddCustomClassCP2(editor.name);//ST-1819 - call the cp2 method / function to add colors.


    return {
        title: '',
        minWidth: 400,
        minHeight: 200,
        className: 'cp2-cke-Body2',
        panel: {
        //css: [editor.config.contentsCss, CKEDITOR.getUrl(CKEDITOR.skin.getPath('editor') + 'editor.css'), CKEDITOR.getUrl(CKEDITOR.skin.getPath('colorpicker') + 'colorpicker.css')] -- File Not Creaded ,BV getting 404
            css: [CKEDITOR.getUrl(CKEDITOR.skin.getPath('colorpicker') + 'colorpicker.css')]
                ,className: "cp2-cke-panel"
        },
        contents: [
            {
                        id: 'general', label: 'Settings', className: "testclass1 cp2-cke-Body3",
                        elements:
                            [
                                {
                                    type: 'html', html: `<div class="innderbox divcp2StandardColr cp2-cke-Body">
                                                            <div class="standardcolor"> 
                                                                <div data-content="Our curated palette" class="titlecolor popupoverright" data-original-title="" title="">Standard Colors <span class="spanStandColName" style="color: rgb(0, 0, 0);">Black</span></div>
                                                                <div class="clear"></div>`+ colorstr +`
                                                            
<span class="cp2colortran"><img src="/images/transparent.png" style="width:18px"></span></div>
                                                                    <div style="clear:both"></div>



                                                                </div>`
                                }
                            ]
                    }
             
        ]
    };
});