{"version": 3, "file": "styled-components.native.esm.js", "sources": ["../../src/utils/generateAlphabeticName.js", "../../src/utils/hash.js", "../../src/utils/generateComponentId.js", "../../src/utils/getComponentName.js", "../../src/utils/isFunction.js", "../../src/utils/isStatelessFunction.js", "../../src/utils/isPlainObject.js", "../../src/utils/isStyledComponent.js", "../../src/constants.js", "../../src/utils/empties.js", "../../src/utils/errors.js", "../../src/utils/error.js", "../../src/sheet/GroupedTag.js", "../../src/sheet/GroupIDAllocator.js", "../../src/sheet/Rehydration.js", "../../src/utils/nonce.js", "../../src/sheet/dom.js", "../../src/sheet/Tag.js", "../../src/sheet/Sheet.js", "../../src/utils/stylisPluginInsertRule.js", "../../src/utils/stylis.js", "../../src/models/StyleSheetManager.js", "../../src/models/Keyframes.js", "../../src/utils/hyphenateStyleName.js", "../../src/utils/addUnitIfNeeded.js", "../../src/utils/flatten.js", "../../src/vendor/postcss/warn-once.js", "../../src/vendor/postcss/tokenize.js", "../../src/vendor/postcss/terminal-highlight.js", "../../src/vendor/postcss/css-syntax-error.js", "../../src/vendor/postcss/stringifier.js", "../../src/vendor/postcss/stringify.js", "../../src/vendor/postcss/node.js", "../../src/vendor/postcss/declaration.js", "../../src/vendor/postcss/comment.js", "../../src/vendor/postcss/parser.js", "../../src/vendor/postcss/parse.js", "../../src/vendor/postcss/container.js", "../../src/vendor/postcss/at-rule.js", "../../src/vendor/postcss/list.js", "../../src/vendor/postcss/rule.js", "../../src/vendor/postcss/warning.js", "../../src/vendor/postcss/result.js", "../../src/vendor/postcss/lazy-result.js", "../../src/vendor/postcss/processor.js", "../../src/vendor/postcss/root.js", "../../src/vendor/postcss/input.js", "../../src/vendor/postcss-safe-parser/safe-parser.js", "../../src/vendor/postcss-safe-parser/parse.js", "../../src/models/InlineStyle.js", "../../src/utils/mixinDeep.js", "../../src/utils/determineTheme.js", "../../src/utils/isTag.js", "../../src/utils/generateDisplayName.js", "../../src/models/ThemeProvider.js", "../../src/models/StyledNativeComponent.js", "../../src/utils/interleave.js", "../../src/constructors/css.js", "../../src/constructors/constructWithOptions.js", "../../src/hoc/withTheme.js", "../../src/hooks/useTheme.js", "../../src/native/index.js"], "sourcesContent": ["// @flow\n/* eslint-disable no-bitwise */\n\nconst AD_REPLACER_R = /(a)(d)/gi;\n\n/* This is the \"capacity\" of our alphabet i.e. 2x26 for all letters plus their capitalised\n * counterparts */\nconst charsLength = 52;\n\n/* start at 75 for 'a' until 'z' (25) and then start at 65 for capitalised letters */\nconst getAlphabeticChar = (code: number): string =>\n  String.fromCharCode(code + (code > 25 ? 39 : 97));\n\n/* input a number, usually a hash and convert it to base-52 */\nexport default function generateAlphabeticName(code: number): string {\n  let name = '';\n  let x;\n\n  /* get a char and divide by alphabet-length */\n  for (x = Math.abs(code); x > charsLength; x = (x / charsLength) | 0) {\n    name = getAlphabeticChar(x % charsLength) + name;\n  }\n\n  return (getAlphabeticChar(x % charsLength) + name).replace(AD_REPLACER_R, '$1-$2');\n}\n", "// @flow\n/* eslint-disable */\n\nexport const SEED = 5381;\n\n// When we have separate strings it's useful to run a progressive\n// version of djb2 where we pretend that we're still looping over\n// the same string\nexport const phash = (h: number, x: string): number => {\n  let i = x.length;\n\n  while (i) {\n    h = (h * 33) ^ x.charCodeAt(--i);\n  }\n\n  return h;\n};\n\n// This is a djb2 hashing function\nexport const hash = (x: string): number => {\n  return phash(SEED, x);\n};\n", "// @flow\n/* eslint-disable */\nimport generateAlphabeticName from './generateAlphabeticName';\nimport { hash } from './hash';\n\nexport default (str: string): string => {\n  return generateAlphabeticName(hash(str) >>> 0);\n};\n", "// @flow\nimport type { IStyledComponent } from '../types';\n\nexport default function getComponentName(\n  target: $PropertyType<IStyledComponent, 'target'>\n): string {\n  return (\n    (process.env.NODE_ENV !== 'production' ? typeof target === 'string' && target : false) ||\n    // $FlowFixMe\n    target.displayName ||\n    // $FlowFixMe\n    target.name ||\n    'Component'\n  );\n}\n", "// @flow\nexport default function isFunction(test: any): boolean %checks {\n  return typeof test === 'function';\n}\n", "// @flow\nexport default function isStatelessFunction(test: any): boolean {\n  return (\n    typeof test === 'function'\n    && !(\n      test.prototype\n      && test.prototype.isReactComponent\n    )\n  );\n}\n", "// @flow\nimport { typeOf } from 'react-is';\n\nexport default (x: any): boolean =>\n  x !== null &&\n  typeof x === 'object' &&\n  (x.toString ? x.toString() : Object.prototype.toString.call(x)) === '[object Object]' &&\n  !typeOf(x);\n", "// @flow\nexport default function isStyledComponent(target: any): boolean %checks {\n  return target && typeof target.styledComponentId === 'string';\n}\n", "// @flow\n\ndeclare var SC_DISABLE_SPEEDY: ?boolean;\ndeclare var __VERSION__: string;\n\nexport const SC_ATTR: string =\n  (typeof process !== 'undefined' &&\n    typeof process.env !== 'undefined' &&\n    (process.env.REACT_APP_SC_ATTR || process.env.SC_ATTR)) ||\n  'data-styled';\n\nexport const SC_ATTR_ACTIVE = 'active';\nexport const SC_ATTR_VERSION = 'data-styled-version';\nexport const SC_VERSION = __VERSION__;\nexport const SPLITTER = '/*!sc*/\\n';\n\nexport const IS_BROWSER = typeof window !== 'undefined' && 'HTMLElement' in window;\n\nexport const DISABLE_SPEEDY = Boolean(\n  typeof SC_DISABLE_SPEEDY === 'boolean'\n    ? SC_DISABLE_SPEEDY\n    : typeof process !== 'undefined' && typeof process.env !== 'undefined'\n    ? typeof process.env.REACT_APP_SC_DISABLE_SPEEDY !== 'undefined' &&\n      process.env.REACT_APP_SC_DISABLE_SPEEDY !== ''\n      ? process.env.REACT_APP_SC_DISABLE_SPEEDY === 'false'\n        ? false\n        : process.env.REACT_APP_SC_DISABLE_SPEEDY\n      : typeof process.env.SC_DISABLE_SPEEDY !== 'undefined' && process.env.SC_DISABLE_SPEEDY !== ''\n      ? process.env.SC_DISABLE_SPEEDY === 'false'\n        ? false\n        : process.env.SC_DISABLE_SPEEDY\n      : process.env.NODE_ENV !== 'production'\n    : false\n);\n\n// Shared empty execution context when generating static styles\nexport const STATIC_EXECUTION_CONTEXT = {};\n", "// @flow\nexport const EMPTY_ARRAY = Object.freeze([]);\nexport const EMPTY_OBJECT = Object.freeze({});\n", "export default {\"1\":\"Cannot create styled-component for component: %s.\\n\\n\",\"2\":\"Can't collect styles once you've consumed a `ServerStyleSheet`'s styles! `ServerStyleSheet` is a one off instance for each server-side render cycle.\\n\\n- Are you trying to reuse it across renders?\\n- Are you accidentally calling collectStyles twice?\\n\\n\",\"3\":\"Streaming SSR is only supported in a Node.js environment; Please do not try to call this method in the browser.\\n\\n\",\"4\":\"The `StyleSheetManager` expects a valid target or sheet prop!\\n\\n- Does this error occur on the client and is your target falsy?\\n- Does this error occur on the server and is the sheet falsy?\\n\\n\",\"5\":\"The clone method cannot be used on the client!\\n\\n- Are you running in a client-like environment on the server?\\n- Are you trying to run SSR on the client?\\n\\n\",\"6\":\"Trying to insert a new style tag, but the given Node is unmounted!\\n\\n- Are you using a custom target that isn't mounted?\\n- Does your document not have a valid head element?\\n- Have you accidentally removed a style tag manually?\\n\\n\",\"7\":\"ThemeProvider: Please return an object from your \\\"theme\\\" prop function, e.g.\\n\\n```js\\ntheme={() => ({})}\\n```\\n\\n\",\"8\":\"ThemeProvider: Please make your \\\"theme\\\" prop an object.\\n\\n\",\"9\":\"Missing document `<head>`\\n\\n\",\"10\":\"Cannot find a StyleSheet instance. Usually this happens if there are multiple copies of styled-components loaded at once. Check out this issue for how to troubleshoot and fix the common cases where this situation can happen: https://github.com/styled-components/styled-components/issues/1941#issuecomment-417862021\\n\\n\",\"11\":\"_This error was replaced with a dev-time warning, it will be deleted for v4 final._ [createGlobalStyle] received children which will not be rendered. Please use the component without passing children elements.\\n\\n\",\"12\":\"It seems you are interpolating a keyframe declaration (%s) into an untagged string. This was supported in styled-components v3, but is not longer supported in v4 as keyframes are now injected on-demand. Please wrap your string in the css\\\\`\\\\` helper which ensures the styles are injected correctly. See https://www.styled-components.com/docs/api#css\\n\\n\",\"13\":\"%s is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.\\n\\n\",\"14\":\"ThemeProvider: \\\"theme\\\" prop is required.\\n\\n\",\"15\":\"A stylis plugin has been supplied that is not named. We need a name for each plugin to be able to prevent styling collisions between different stylis configurations within the same app. Before you pass your plugin to `<StyleSheetManager stylisPlugins={[]}>`, please make sure each plugin is uniquely-named, e.g.\\n\\n```js\\nObject.defineProperty(importedPlugin, 'name', { value: 'some-unique-name' });\\n```\\n\\n\",\"16\":\"Reached the limit of how many styled components may be created at group %s.\\nYou may only create up to 1,073,741,824 components. If you're creating components dynamically,\\nas for instance in your render method then you may be running into this limitation.\\n\\n\",\"17\":\"CSSStyleSheet could not be found on HTMLStyleElement.\\nHas styled-components' style tag been unmounted or altered by another script?\\n\"};", "// @flow\nimport errorMap from './errors';\n\nconst ERRORS = process.env.NODE_ENV !== 'production' ? errorMap : {};\n\n/**\n * super basic version of sprintf\n */\nfunction format(...args) {\n  let a = args[0];\n  const b = [];\n\n  for (let c = 1, len = args.length; c < len; c += 1) {\n    b.push(args[c]);\n  }\n\n  b.forEach(d => {\n    a = a.replace(/%[a-z]/, d);\n  });\n\n  return a;\n}\n\n/**\n * Create an error file out of errors.md for development and a simple web link to the full errors\n * in production mode.\n */\nexport default function throwStyledComponentsError(\n  code: string | number,\n  ...interpolations: Array<any>\n) {\n  if (process.env.NODE_ENV === 'production') {\n    throw new Error(\n      `An error occurred. See https://git.io/JUIaE#${code} for more information.${\n        interpolations.length > 0 ? ` Args: ${interpolations.join(', ')}` : ''\n      }`\n    );\n  } else {\n    throw new Error(format(ERRORS[code], ...interpolations).trim());\n  }\n}\n", "// @flow\n/* eslint-disable no-use-before-define */\n\nimport type { GroupedTag, Tag } from './types';\nimport { SPLITTER } from '../constants';\nimport throwStyledError from '../utils/error';\n\n/** Create a GroupedTag with an underlying Tag implementation */\nexport const makeGroupedTag = (tag: Tag): GroupedTag => {\n  return new DefaultGroupedTag(tag);\n};\n\nconst BASE_SIZE = 1 << 9;\n\nclass DefaultGroupedTag implements GroupedTag {\n  groupSizes: Uint32Array;\n\n  length: number;\n\n  tag: Tag;\n\n  constructor(tag: Tag) {\n    this.groupSizes = new Uint32Array(BASE_SIZE);\n    this.length = BASE_SIZE;\n    this.tag = tag;\n  }\n\n  indexOfGroup(group: number): number {\n    let index = 0;\n    for (let i = 0; i < group; i++) {\n      index += this.groupSizes[i];\n    }\n\n    return index;\n  }\n\n  insertRules(group: number, rules: string[]): void {\n    if (group >= this.groupSizes.length) {\n      const oldBuffer = this.groupSizes;\n      const oldSize = oldBuffer.length;\n\n      let newSize = oldSize;\n      while (group >= newSize) {\n        newSize <<= 1;\n        if (newSize < 0) {\n          throwStyledError(16, `${group}`);\n        }\n      }\n\n      this.groupSizes = new Uint32Array(newSize);\n      this.groupSizes.set(oldBuffer);\n      this.length = newSize;\n\n      for (let i = oldSize; i < newSize; i++) {\n        this.groupSizes[i] = 0;\n      }\n    }\n\n    let ruleIndex = this.indexOfGroup(group + 1);\n    for (let i = 0, l = rules.length; i < l; i++) {\n      if (this.tag.insertRule(ruleIndex, rules[i])) {\n        this.groupSizes[group]++;\n        ruleIndex++;\n      }\n    }\n  }\n\n  clearGroup(group: number): void {\n    if (group < this.length) {\n      const length = this.groupSizes[group];\n      const startIndex = this.indexOfGroup(group);\n      const endIndex = startIndex + length;\n\n      this.groupSizes[group] = 0;\n\n      for (let i = startIndex; i < endIndex; i++) {\n        this.tag.deleteRule(startIndex);\n      }\n    }\n  }\n\n  getGroup(group: number): string {\n    let css = '';\n    if (group >= this.length || this.groupSizes[group] === 0) {\n      return css;\n    }\n\n    const length = this.groupSizes[group];\n    const startIndex = this.indexOfGroup(group);\n    const endIndex = startIndex + length;\n\n    for (let i = startIndex; i < endIndex; i++) {\n      css += `${this.tag.getRule(i)}${SPLITTER}`;\n    }\n\n    return css;\n  }\n}\n", "// @flow\n\nimport throwStyledError from '../utils/error';\n\nconst MAX_SMI = 1 << 31 - 1;\n\nlet groupIDRegister: Map<string, number> = new Map();\nlet reverseRegister: Map<number, string> = new Map();\nlet nextFreeGroup = 1;\n\nexport const resetGroupIds = () => {\n  groupIDRegister = new Map();\n  reverseRegister = new Map();\n  nextFreeGroup = 1;\n};\n\nexport const getGroupForId = (id: string): number => {\n  if (groupIDRegister.has(id)) {\n    return (groupIDRegister.get(id): any);\n  }\n\n  while (reverseRegister.has(nextFreeGroup)) {\n    nextFreeGroup++;\n  }\n\n  const group = nextFreeGroup++;\n\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    ((group | 0) < 0 || group > MAX_SMI)\n  ) {\n    throwStyledError(16, `${group}`);\n  }\n\n  groupIDRegister.set(id, group);\n  reverseRegister.set(group, id);\n  return group;\n};\n\nexport const getIdForGroup = (group: number): void | string => {\n  return reverseRegister.get(group);\n};\n\nexport const setGroupForId = (id: string, group: number) => {\n  if (group >= nextFreeGroup) {\n    nextFreeGroup = group + 1;\n  }\n\n  groupIDRegister.set(id, group);\n  reverseRegister.set(group, id);\n};\n", "// @flow\n\nimport { SPLITTER, SC_ATTR, SC_ATTR_ACTIVE, SC_ATTR_VERSION, SC_VERSION } from '../constants';\nimport { getIdForGroup, setGroupForId } from './GroupIDAllocator';\nimport type { Sheet } from './types';\n\nconst SELECTOR = `style[${SC_ATTR}][${SC_ATTR_VERSION}=\"${SC_VERSION}\"]`;\nconst MARKER_RE = new RegExp(`^${SC_ATTR}\\\\.g(\\\\d+)\\\\[id=\"([\\\\w\\\\d-]+)\"\\\\].*?\"([^\"]*)`);\n\nexport const outputSheet = (sheet: Sheet) => {\n  const tag = sheet.getTag();\n  const { length } = tag;\n\n  let css = '';\n  for (let group = 0; group < length; group++) {\n    const id = getIdForGroup(group);\n    if (id === undefined) continue;\n\n    const names = sheet.names.get(id);\n    const rules = tag.getGroup(group);\n    if (!names || !rules || !names.size) continue;\n\n    const selector = `${SC_ATTR}.g${group}[id=\"${id}\"]`;\n\n    let content = '';\n    if (names !== undefined) {\n      names.forEach(name => {\n        if (name.length > 0) {\n          content += `${name},`;\n        }\n      });\n    }\n\n    // NOTE: It's easier to collect rules and have the marker\n    // after the actual rules to simplify the rehydration\n    css += `${rules}${selector}{content:\"${content}\"}${SPLITTER}`;\n  }\n\n  return css;\n};\n\nconst rehydrateNamesFromContent = (sheet: Sheet, id: string, content: string) => {\n  const names = content.split(',');\n  let name;\n\n  for (let i = 0, l = names.length; i < l; i++) {\n    // eslint-disable-next-line\n    if ((name = names[i])) {\n      sheet.registerName(id, name);\n    }\n  }\n};\n\nconst rehydrateSheetFromTag = (sheet: Sheet, style: HTMLStyleElement) => {\n  const parts = (style.textContent || '').split(SPLITTER);\n  const rules: string[] = [];\n\n  for (let i = 0, l = parts.length; i < l; i++) {\n    const part = parts[i].trim();\n    if (!part) continue;\n\n    const marker = part.match(MARKER_RE);\n\n    if (marker) {\n      const group = parseInt(marker[1], 10) | 0;\n      const id = marker[2];\n\n      if (group !== 0) {\n        // Rehydrate componentId to group index mapping\n        setGroupForId(id, group);\n        // Rehydrate names and rules\n        // looks like: data-styled.g11[id=\"idA\"]{content:\"nameA,\"}\n        rehydrateNamesFromContent(sheet, id, marker[3]);\n        sheet.getTag().insertRules(group, rules);\n      }\n\n      rules.length = 0;\n    } else {\n      rules.push(part);\n    }\n  }\n};\n\nexport const rehydrateSheet = (sheet: Sheet) => {\n  const nodes = document.querySelectorAll(SELECTOR);\n\n  for (let i = 0, l = nodes.length; i < l; i++) {\n    const node = ((nodes[i]: any): HTMLStyleElement);\n    if (node && node.getAttribute(SC_ATTR) !== SC_ATTR_ACTIVE) {\n      rehydrateSheetFromTag(sheet, node);\n\n      if (node.parentNode) {\n        node.parentNode.removeChild(node);\n      }\n    }\n  }\n};\n", "// @flow\n/* eslint-disable camelcase, no-undef */\n\ndeclare var __webpack_nonce__: string;\n\nconst getNonce = () => {\n  return typeof __webpack_nonce__ !== 'undefined' ? __webpack_nonce__ : null;\n};\n\nexport default getNonce;\n", "// @flow\n\nimport { SC_ATTR, SC_ATTR_ACTIVE, SC_ATTR_VERSION, SC_VERSION } from '../constants';\nimport getNonce from '../utils/nonce';\nimport throwStyledError from '../utils/error';\n\nconst ELEMENT_TYPE = 1; /* Node.ELEMENT_TYPE */\n\n/** Find last style element if any inside target */\nconst findLastStyleTag = (target: HTMLElement): void | HTMLStyleElement => {\n  const { childNodes } = target;\n\n  for (let i = childNodes.length; i >= 0; i--) {\n    const child = ((childNodes[i]: any): ?HTMLElement);\n    if (child && child.nodeType === ELEMENT_TYPE && child.hasAttribute(SC_ATTR)) {\n      return ((child: any): HTMLStyleElement);\n    }\n  }\n\n  return undefined;\n};\n\n/** Create a style element inside `target` or <head> after the last */\nexport const makeStyleTag = (target?: HTMLElement): HTMLStyleElement => {\n  const head = ((document.head: any): HTMLElement);\n  const parent = target || head;\n  const style = document.createElement('style');\n  const prevStyle = findLastStyleTag(parent);\n  const nextSibling = prevStyle !== undefined ? prevStyle.nextSibling : null;\n\n  style.setAttribute(SC_ATTR, SC_ATTR_ACTIVE);\n  style.setAttribute(SC_ATTR_VERSION, SC_VERSION);\n\n  const nonce = getNonce();\n\n  if (nonce) style.setAttribute('nonce', nonce);\n\n  parent.insertBefore(style, nextSibling);\n\n  return style;\n};\n\n/** Get the CSSStyleSheet instance for a given style element */\nexport const getSheet = (tag: HTMLStyleElement): CSSStyleSheet => {\n  if (tag.sheet) {\n    return ((tag.sheet: any): CSSStyleSheet);\n  }\n\n  // Avoid Firefox quirk where the style element might not have a sheet property\n  const { styleSheets } = document;\n  for (let i = 0, l = styleSheets.length; i < l; i++) {\n    const sheet = styleSheets[i];\n    if (sheet.ownerNode === tag) {\n      return ((sheet: any): CSSStyleSheet);\n    }\n  }\n\n  throwStyledError(17);\n  return (undefined: any);\n};\n", "// @flow\n/* eslint-disable no-use-before-define */\n\nimport { makeStyleTag, getSheet } from './dom';\nimport type { SheetOptions, Tag } from './types';\n\n/** Create a CSSStyleSheet-like tag depending on the environment */\nexport const makeTag = ({ isServer, useCSSOMInjection, target }: SheetOptions): Tag => {\n  if (isServer) {\n    return new VirtualTag(target);\n  } else if (useCSSOMInjection) {\n    return new CSSOMTag(target);\n  } else {\n    return new TextTag(target);\n  }\n};\n\nexport class CSSOMTag implements Tag {\n  element: HTMLStyleElement;\n\n  sheet: CSSStyleSheet;\n\n  length: number;\n\n  constructor(target?: HTMLElement) {\n    const element = (this.element = makeStyleTag(target));\n\n    // Avoid Edge bug where empty style elements don't create sheets\n    element.appendChild(document.createTextNode(''));\n\n    this.sheet = getSheet(element);\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string): boolean {\n    try {\n      this.sheet.insertRule(rule, index);\n      this.length++;\n      return true;\n    } catch (_error) {\n      return false;\n    }\n  }\n\n  deleteRule(index: number): void {\n    this.sheet.deleteRule(index);\n    this.length--;\n  }\n\n  getRule(index: number): string {\n    const rule = this.sheet.cssRules[index];\n    // Avoid IE11 quirk where cssText is inaccessible on some invalid rules\n    if (rule !== undefined && typeof rule.cssText === 'string') {\n      return rule.cssText;\n    } else {\n      return '';\n    }\n  }\n}\n\n/** A Tag that emulates the CSSStyleSheet API but uses text nodes */\nexport class TextTag implements Tag {\n  element: HTMLStyleElement;\n\n  nodes: NodeList<Node>;\n\n  length: number;\n\n  constructor(target?: HTMLElement) {\n    const element = (this.element = makeStyleTag(target));\n    this.nodes = element.childNodes;\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string): boolean {\n    if (index <= this.length && index >= 0) {\n      const node = document.createTextNode(rule);\n      const refNode = this.nodes[index];\n      this.element.insertBefore(node, refNode || null);\n      this.length++;\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  deleteRule(index: number): void {\n    this.element.removeChild(this.nodes[index]);\n    this.length--;\n  }\n\n  getRule(index: number): string {\n    if (index < this.length) {\n      return this.nodes[index].textContent;\n    } else {\n      return '';\n    }\n  }\n}\n\n/** A completely virtual (server-side) Tag that doesn't manipulate the DOM */\nexport class VirtualTag implements Tag {\n  rules: string[];\n\n  length: number;\n\n  constructor(_target?: HTMLElement) {\n    this.rules = [];\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string): boolean {\n    if (index <= this.length) {\n      this.rules.splice(index, 0, rule);\n      this.length++;\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  deleteRule(index: number): void {\n    this.rules.splice(index, 1);\n    this.length--;\n  }\n\n  getRule(index: number): string {\n    if (index < this.length) {\n      return this.rules[index];\n    } else {\n      return '';\n    }\n  }\n}\n", "// @flow\nimport { DISABLE_SPEEDY, IS_BROWSER } from '../constants';\nimport { EMPTY_OBJECT } from '../utils/empties';\nimport { makeGroupedTag } from './GroupedTag';\nimport { getGroupForId } from './GroupIDAllocator';\nimport { outputSheet, rehydrateSheet } from './Rehydration';\nimport { makeTag } from './Tag';\nimport type { GroupedTag, Sheet, SheetOptions } from './types';\n\nlet SHOULD_REHYDRATE = IS_BROWSER;\n\ntype SheetConstructorArgs = {\n  isServer?: boolean,\n  useCSSOMInjection?: boolean,\n  target?: HTMLElement,\n};\n\ntype GlobalStylesAllocationMap = { [key: string]: number };\ntype NamesAllocationMap = Map<string, Set<string>>;\n\nconst defaultOptions: SheetOptions = {\n  isServer: !IS_BROWSER,\n  useCSSOMInjection: !DISABLE_SPEEDY,\n};\n\n/** Contains the main stylesheet logic for stringification and caching */\nexport default class StyleSheet implements Sheet {\n  gs: GlobalStylesAllocationMap;\n\n  names: NamesAllocationMap;\n\n  options: SheetOptions;\n\n  server: boolean;\n\n  tag: void | GroupedTag;\n\n  /** Register a group ID to give it an index */\n  static registerId(id: string): number {\n    return getGroupForId(id);\n  }\n\n  constructor(\n    options: SheetConstructorArgs = EMPTY_OBJECT,\n    globalStyles?: GlobalStylesAllocationMap = {},\n    names?: NamesAllocationMap\n  ) {\n    this.options = {\n      ...defaultOptions,\n      ...options,\n    };\n\n    this.gs = globalStyles;\n    this.names = new Map(names);\n    this.server = !!options.isServer;\n\n    // We rehydrate only once and use the sheet that is created first\n    if (!this.server && IS_BROWSER && SHOULD_REHYDRATE) {\n      SHOULD_REHYDRATE = false;\n      rehydrateSheet(this);\n    }\n  }\n\n  reconstructWithOptions(options: SheetConstructorArgs, withNames?: boolean = true) {\n    return new StyleSheet(\n      { ...this.options, ...options },\n      this.gs,\n      (withNames && this.names) || undefined\n    );\n  }\n\n  allocateGSInstance(id: string) {\n    return (this.gs[id] = (this.gs[id] || 0) + 1);\n  }\n\n  /** Lazily initialises a GroupedTag for when it's actually needed */\n  getTag(): GroupedTag {\n    return this.tag || (this.tag = makeGroupedTag(makeTag(this.options)));\n  }\n\n  /** Check whether a name is known for caching */\n  hasNameForId(id: string, name: string): boolean {\n    return this.names.has(id) && (this.names.get(id): any).has(name);\n  }\n\n  /** Mark a group's name as known for caching */\n  registerName(id: string, name: string) {\n    getGroupForId(id);\n\n    if (!this.names.has(id)) {\n      const groupNames = new Set();\n      groupNames.add(name);\n      this.names.set(id, groupNames);\n    } else {\n      (this.names.get(id): any).add(name);\n    }\n  }\n\n  /** Insert new rules which also marks the name as known */\n  insertRules(id: string, name: string, rules: string[]) {\n    this.registerName(id, name);\n    this.getTag().insertRules(getGroupForId(id), rules);\n  }\n\n  /** Clears all cached names for a given group ID */\n  clearNames(id: string) {\n    if (this.names.has(id)) {\n      (this.names.get(id): any).clear();\n    }\n  }\n\n  /** Clears all rules for a given group ID */\n  clearRules(id: string) {\n    this.getTag().clearGroup(getGroupForId(id));\n    this.clearNames(id);\n  }\n\n  /** Clears the entire tag which deletes all rules but not its names */\n  clearTag() {\n    // NOTE: This does not clear the names, since it's only used during SSR\n    // so that we can continuously output only new rules\n    this.tag = undefined;\n  }\n\n  /** Outputs the current sheet as a CSS string with markers for SSR */\n  toString(): string {\n    return outputSheet(this);\n  }\n}\n", "/**\n * MIT License\n *\n * Copyright (c) 2016 Sultan Tarimo\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy of\n * this software and associated documentation files (the \"Software\"),\n * to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or\n * sell copies of the Software and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n * OR IMPLIED INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\n * WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR\n * IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n/* eslint-disable */\n\nexport default function(insertRule) {\n  const delimiter = '/*|*/';\n  const needle = `${delimiter}}`;\n\n  function toSheet(block) {\n    if (block) {\n      try {\n        insertRule(`${block}}`);\n      } catch (e) {}\n    }\n  }\n\n  return function ruleSheet(\n    context,\n    content,\n    selectors,\n    parents,\n    line,\n    column,\n    length,\n    ns,\n    depth,\n    at\n  ) {\n    switch (context) {\n      // property\n      case 1:\n        // @import\n        if (depth === 0 && content.charCodeAt(0) === 64) return insertRule(`${content};`), '';\n        break;\n      // selector\n      case 2:\n        if (ns === 0) return content + delimiter;\n        break;\n      // at-rule\n      case 3:\n        switch (ns) {\n          // @font-face, @page\n          case 102:\n          case 112:\n            return insertRule(selectors[0] + content), '';\n          default:\n            return content + (at === 0 ? delimiter : '');\n        }\n      case -2:\n        content.split(needle).forEach(toSheet);\n    }\n  };\n}\n", "import Stylis from '@emotion/stylis';\nimport { type Stringifier } from '../types';\nimport { EMPTY_ARRAY, EMPTY_OBJECT } from './empties';\nimport throwStyledError from './error';\nimport { phash, SEED } from './hash';\nimport insertRulePlugin from './stylisPluginInsertRule';\n\nconst COMMENT_REGEX = /^\\s*\\/\\/.*$/gm;\nconst COMPLEX_SELECTOR_PREFIX = [':', '[', '.', '#'];\n\ntype StylisInstanceConstructorArgs = {\n  options?: Object,\n  plugins?: Array<Function>,\n};\n\nexport default function createStylisInstance({\n  options = EMPTY_OBJECT,\n  plugins = EMPTY_ARRAY,\n}: StylisInstanceConstructorArgs = EMPTY_OBJECT) {\n  const stylis = new Stylis(options);\n\n  // Wrap `insertRulePlugin to build a list of rules,\n  // and then make our own plugin to return the rules. This\n  // makes it easier to hook into the existing SSR architecture\n\n  let parsingRules = [];\n\n  // eslint-disable-next-line consistent-return\n  const returnRulesPlugin = context => {\n    if (context === -2) {\n      const parsedRules = parsingRules;\n      parsingRules = [];\n      return parsedRules;\n    }\n  };\n\n  const parseRulesPlugin = insertRulePlugin(rule => {\n    parsingRules.push(rule);\n  });\n\n  let _componentId: string;\n  let _selector: string;\n  let _selectorRegexp: RegExp;\n  let _consecutiveSelfRefRegExp: RegExp;\n\n  const selfReferenceReplacer = (match, offset, string) => {\n    if (\n      // do not replace the first occurrence if it is complex (has a modifier)\n      (offset === 0 ? COMPLEX_SELECTOR_PREFIX.indexOf(string[_selector.length]) === -1 : true) &&\n      // no consecutive self refs (.b.b); that is a precedence boost and treated differently\n      !string.match(_consecutiveSelfRefRegExp)\n    ) {\n      return `.${_componentId}`;\n    }\n\n    return match;\n  };\n\n  /**\n   * When writing a style like\n   *\n   * & + & {\n   *   color: red;\n   * }\n   *\n   * The second ampersand should be a reference to the static component class. stylis\n   * has no knowledge of static class so we have to intelligently replace the base selector.\n   *\n   * https://github.com/thysultan/stylis.js/tree/v3.5.4#plugins <- more info about the context phase values\n   * \"2\" means this plugin is taking effect at the very end after all other processing is complete\n   */\n  const selfReferenceReplacementPlugin = (context, _, selectors) => {\n    if (context === 2 && selectors.length && selectors[0].lastIndexOf(_selector) > 0) {\n      // eslint-disable-next-line no-param-reassign\n      selectors[0] = selectors[0].replace(_selectorRegexp, selfReferenceReplacer);\n    }\n  };\n\n  stylis.use([...plugins, selfReferenceReplacementPlugin, parseRulesPlugin, returnRulesPlugin]);\n\n  function stringifyRules(css, selector, prefix, componentId = '&'): Stringifier {\n    const flatCSS = css.replace(COMMENT_REGEX, '');\n    const cssStr = selector && prefix ? `${prefix} ${selector} { ${flatCSS} }` : flatCSS;\n\n    // stylis has no concept of state to be passed to plugins\n    // but since JS is single-threaded, we can rely on that to ensure\n    // these properties stay in sync with the current stylis run\n    _componentId = componentId;\n    _selector = selector;\n    _selectorRegexp = new RegExp(`\\\\${_selector}\\\\b`, 'g');\n    _consecutiveSelfRefRegExp = new RegExp(`(\\\\${_selector}\\\\b){2,}`);\n\n    return stylis(prefix || !selector ? '' : selector, cssStr);\n  }\n\n  stringifyRules.hash = plugins.length\n    ? plugins\n        .reduce((acc, plugin) => {\n          if (!plugin.name) {\n            throwStyledError(15);\n          }\n\n          return phash(acc, plugin.name);\n        }, SEED)\n        .toString()\n    : '';\n\n  return stringifyRules;\n}\n", "// @flow\nimport React, { type Context, type Node, useContext, useEffect, useMemo, useState } from 'react';\nimport shallowequal from 'shallowequal';\nimport StyleSheet from '../sheet';\nimport type { Stringifier } from '../types';\nimport createStylisInstance from '../utils/stylis';\n\ntype Props = {\n  children?: Node,\n  disableCSSOMInjection?: boolean,\n  disableVendorPrefixes?: boolean,\n  sheet?: StyleSheet,\n  stylisPlugins?: Array<Function>,\n  target?: HTMLElement,\n};\n\nexport const StyleSheetContext: Context<StyleSheet | void> = React.createContext();\nexport const StyleSheetConsumer = StyleSheetContext.Consumer;\nexport const StylisContext: Context<Stringifier | void> = React.createContext();\nexport const StylisConsumer = StylisContext.Consumer;\n\nexport const masterSheet: StyleSheet = new StyleSheet();\nexport const masterStylis: Stringifier = createStylisInstance();\n\nexport function useStyleSheet(): StyleSheet {\n  return useContext(StyleSheetContext) || masterSheet;\n}\n\nexport function useStylis(): Stringifier {\n  return useContext(StylisContext) || masterStylis;\n}\n\nexport default function StyleSheetManager(props: Props) {\n  const [plugins, setPlugins] = useState(props.stylisPlugins);\n  const contextStyleSheet = useStyleSheet();\n\n  const styleSheet = useMemo(() => {\n    let sheet = contextStyleSheet;\n\n    if (props.sheet) {\n      // eslint-disable-next-line prefer-destructuring\n      sheet = props.sheet;\n    } else if (props.target) {\n      sheet = sheet.reconstructWithOptions({ target: props.target }, false);\n    }\n\n    if (props.disableCSSOMInjection) {\n      sheet = sheet.reconstructWithOptions({ useCSSOMInjection: false });\n    }\n\n    return sheet;\n  }, [props.disableCSSOMInjection, props.sheet, props.target]);\n\n  const stylis = useMemo(\n    () =>\n      createStylisInstance({\n        options: { prefix: !props.disableVendorPrefixes },\n        plugins,\n      }),\n    [props.disableVendorPrefixes, plugins]\n  );\n\n  useEffect(() => {\n    if (!shallowequal(plugins, props.stylisPlugins)) setPlugins(props.stylisPlugins);\n  }, [props.stylisPlugins]);\n\n  return (\n    <StyleSheetContext.Provider value={styleSheet}>\n      <StylisContext.Provider value={stylis}>\n        {process.env.NODE_ENV !== 'production'\n          ? React.Children.only(props.children)\n          : props.children}\n      </StylisContext.Provider>\n    </StyleSheetContext.Provider>\n  );\n}\n", "// @flow\nimport StyleSheet from '../sheet';\nimport { type Stringifier } from '../types';\nimport throwStyledError from '../utils/error';\nimport { masterStylis } from './StyleSheetManager';\n\nexport default class Keyframes {\n  id: string;\n\n  name: string;\n\n  rules: string;\n\n  constructor(name: string, rules: string) {\n    this.name = name;\n    this.id = `sc-keyframes-${name}`;\n    this.rules = rules;\n  }\n\n  inject = (styleSheet: StyleSheet, stylisInstance: Stringifier = masterStylis) => {\n    const resolvedName = this.name + stylisInstance.hash;\n\n    if (!styleSheet.hasNameForId(this.id, resolvedName)) {\n      styleSheet.insertRules(\n        this.id,\n        resolvedName,\n        stylisInstance(this.rules, resolvedName, '@keyframes')\n      );\n    }\n  };\n\n  toString = () => {\n    return throwStyledError(12, String(this.name));\n  };\n\n  getName(stylisInstance: Stringifier = masterStylis) {\n    return this.name + stylisInstance.hash;\n  }\n}\n", "// @flow\n\n/**\n * inlined version of\n * https://github.com/facebook/fbjs/blob/master/packages/fbjs/src/core/hyphenateStyleName.js\n */\n\nconst uppercaseCheck = /([A-Z])/;\nconst uppercasePattern = /([A-Z])/g;\nconst msPattern = /^ms-/;\nconst prefixAndLowerCase = (char: string): string => `-${char.toLowerCase()}`;\n\n/**\n * Hyphenates a camelcased CSS property name, for example:\n *\n *   > hyphenateStyleName('backgroundColor')\n *   < \"background-color\"\n *   > hyphenateStyleName('MozTransition')\n *   < \"-moz-transition\"\n *   > hyphenateStyleName('msTransition')\n *   < \"-ms-transition\"\n *\n * As Modernizr suggests (http://modernizr.com/docs/#prefixed), an `ms` prefix\n * is converted to `-ms-`.\n *\n * @param {string} string\n * @return {string}\n */\nexport default function hyphenateStyleName(string: string): string {\n  return uppercaseCheck.test(string)\n  ? string\n    .replace(uppercasePattern, prefixAndLowerCase)\n    .replace(msPattern, '-ms-')\n  : string;\n}\n", "// @flow\nimport unitless from '@emotion/unitless';\n\n// Taken from https://github.com/facebook/react/blob/b87aabdfe1b7461e7331abb3601d9e6bb27544bc/packages/react-dom/src/shared/dangerousStyleValue.js\nexport default function addUnitIfNeeded(name: string, value: any): any {\n  // https://github.com/amilajack/eslint-plugin-flowtype-errors/issues/133\n  // $FlowFixMe\n  if (value == null || typeof value === 'boolean' || value === '') {\n    return '';\n  }\n\n  if (typeof value === 'number' && value !== 0 && !(name in unitless) && !name.startsWith('--')) {\n    return `${value}px`; // Presumes implicit 'px' suffix for unitless numbers except for CSS variables\n  }\n\n  return String(value).trim();\n}\n", "// @flow\nimport { isElement } from 'react-is';\nimport getComponentName from './getComponentName';\nimport isFunction from './isFunction';\nimport isStatelessFunction from './isStatelessFunction';\nimport isPlainObject from './isPlainObject';\nimport isStyledComponent from './isStyledComponent';\nimport Keyframes from '../models/Keyframes';\nimport hyphenate from './hyphenateStyleName';\nimport addUnitIfNeeded from './addUnitIfNeeded';\nimport { type Stringifier } from '../types';\n\n/**\n * It's falsish not falsy because 0 is allowed.\n */\nconst isFalsish = chunk => chunk === undefined || chunk === null || chunk === false || chunk === '';\n\nexport const objToCssArray = (obj: Object, prevKey?: string): Array<string | Function> => {\n  const rules = [];\n\n  for (const key in obj) {\n    if (!obj.hasOwnProperty(key) || isFalsish(obj[key])) continue;\n\n    if ((Array.isArray(obj[key]) && obj[key].isCss) || isFunction(obj[key])) {\n      rules.push(`${hyphenate(key)}:`, obj[key], ';');\n    } else if (isPlainObject(obj[key])) {\n      rules.push(...objToCssArray(obj[key], key));\n    } else {\n      rules.push(`${hyphenate(key)}: ${addUnitIfNeeded(key, obj[key])};`);\n    }\n  }\n\n  return prevKey ? [`${prevKey} {`, ...rules, '}'] : rules;\n};\n\nexport default function flatten(\n  chunk: any,\n  executionContext: ?Object,\n  styleSheet: ?Object,\n  stylisInstance: ?Stringifier\n): any {\n  if (Array.isArray(chunk)) {\n    const ruleSet = [];\n\n    for (let i = 0, len = chunk.length, result; i < len; i += 1) {\n      result = flatten(chunk[i], executionContext, styleSheet, stylisInstance);\n\n      if (result === '') continue;\n      else if (Array.isArray(result)) ruleSet.push(...result);\n      else ruleSet.push(result);\n    }\n\n    return ruleSet;\n  }\n\n  if (isFalsish(chunk)) {\n    return '';\n  }\n\n  /* Handle other components */\n  if (isStyledComponent(chunk)) {\n    return `.${chunk.styledComponentId}`;\n  }\n\n  /* Either execute or defer the function */\n  if (isFunction(chunk)) {\n    if (isStatelessFunction(chunk) && executionContext) {\n      const result = chunk(executionContext);\n\n      if (process.env.NODE_ENV !== 'production' && isElement(result)) {\n        // eslint-disable-next-line no-console\n        console.warn(\n          `${getComponentName(\n            chunk\n          )} is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.`\n        );\n      }\n\n      return flatten(result, executionContext, styleSheet, stylisInstance);\n    } else return chunk;\n  }\n\n  if (chunk instanceof Keyframes) {\n    if (styleSheet) {\n      chunk.inject(styleSheet, stylisInstance);\n      return chunk.getName(stylisInstance);\n    } else return chunk;\n  }\n\n  /* Handle objects */\n  return isPlainObject(chunk) ? objToCssArray(chunk) : chunk.toString();\n}\n", "// @flow\nconst printed = {};\n\nexport default function warnOnce(message) {\n  if (printed[message]) return;\n  printed[message] = true;\n\n  if (typeof console !== 'undefined' && console.warn) console.warn(message);\n}\n", "// @flow\nconst SINGLE_QUOTE = \"'\".charCodeAt(0);\nconst DOUBLE_QUOTE = '\"'.charCodeAt(0);\nconst BACKSLASH = '\\\\'.charCodeAt(0);\nconst SLASH = '/'.charCodeAt(0);\nconst NEWLINE = '\\n'.charCodeAt(0);\nconst SPACE = ' '.charCodeAt(0);\nconst FEED = '\\f'.charCodeAt(0);\nconst TAB = '\\t'.charCodeAt(0);\nconst CR = '\\r'.charCodeAt(0);\nconst OPEN_SQUARE = '['.charCodeAt(0);\nconst CLOSE_SQUARE = ']'.charCodeAt(0);\nconst OPEN_PARENTHESES = '('.charCodeAt(0);\nconst CLOSE_PARENTHESES = ')'.charCodeAt(0);\nconst OPEN_CURLY = '{'.charCodeAt(0);\nconst CLOSE_CURLY = '}'.charCodeAt(0);\nconst SEMICOLON = ';'.charCodeAt(0);\nconst ASTERISK = '*'.charCodeAt(0);\nconst COLON = ':'.charCodeAt(0);\nconst AT = '@'.charCodeAt(0);\n\nconst RE_AT_END = /[ \\n\\t\\r\\f\\{\\(\\)'\"\\\\;/\\[\\]#]/g;\nconst RE_WORD_END = /[ \\n\\t\\r\\f\\(\\)\\{\\}:;@!'\"\\\\\\]\\[#]|\\/(?=\\*)/g;\nconst RE_BAD_BRACKET = /.[\\\\\\/\\(\"'\\n]/;\n\nexport default function tokenize(input, options = {}) {\n  const tokens = [];\n  const css = input.css.valueOf();\n\n  const ignore = options.ignoreErrors;\n\n  let code,\n    next,\n    quote,\n    lines,\n    last,\n    content,\n    escape,\n    nextLine,\n    nextOffset,\n    escaped,\n    escapePos,\n    prev,\n    n;\n\n  const length = css.length;\n  let offset = -1;\n  let line = 1;\n  let pos = 0;\n\n  function unclosed(what) {\n    throw input.error(`Unclosed ${what}`, line, pos - offset);\n  }\n\n  while (pos < length) {\n    code = css.charCodeAt(pos);\n\n    if (code === NEWLINE || code === FEED || (code === CR && css.charCodeAt(pos + 1) !== NEWLINE)) {\n      offset = pos;\n      line += 1;\n    }\n\n    switch (code) {\n      case NEWLINE:\n      case SPACE:\n      case TAB:\n      case CR:\n      case FEED:\n        next = pos;\n        do {\n          next += 1;\n          code = css.charCodeAt(next);\n          if (code === NEWLINE) {\n            offset = next;\n            line += 1;\n          }\n        } while (\n          code === SPACE ||\n          code === NEWLINE ||\n          code === TAB ||\n          code === CR ||\n          code === FEED\n        );\n\n        tokens.push(['space', css.slice(pos, next)]);\n        pos = next - 1;\n        break;\n\n      case OPEN_SQUARE:\n        tokens.push(['[', '[', line, pos - offset]);\n        break;\n\n      case CLOSE_SQUARE:\n        tokens.push([']', ']', line, pos - offset]);\n        break;\n\n      case OPEN_CURLY:\n        tokens.push(['{', '{', line, pos - offset]);\n        break;\n\n      case CLOSE_CURLY:\n        tokens.push(['}', '}', line, pos - offset]);\n        break;\n\n      case COLON:\n        tokens.push([':', ':', line, pos - offset]);\n        break;\n\n      case SEMICOLON:\n        tokens.push([';', ';', line, pos - offset]);\n        break;\n\n      case OPEN_PARENTHESES:\n        prev = tokens.length ? tokens[tokens.length - 1][1] : '';\n        n = css.charCodeAt(pos + 1);\n        if (\n          prev === 'url' &&\n          n !== SINGLE_QUOTE &&\n          n !== DOUBLE_QUOTE &&\n          n !== SPACE &&\n          n !== NEWLINE &&\n          n !== TAB &&\n          n !== FEED &&\n          n !== CR\n        ) {\n          next = pos;\n          do {\n            escaped = false;\n            next = css.indexOf(')', next + 1);\n            if (next === -1) {\n              if (ignore) {\n                next = pos;\n                break;\n              } else {\n                unclosed('bracket');\n              }\n            }\n            escapePos = next;\n            while (css.charCodeAt(escapePos - 1) === BACKSLASH) {\n              escapePos -= 1;\n              escaped = !escaped;\n            }\n          } while (escaped);\n\n          tokens.push([\n            'brackets',\n            css.slice(pos, next + 1),\n            line,\n            pos - offset,\n            line,\n            next - offset,\n          ]);\n          pos = next;\n        } else {\n          next = css.indexOf(')', pos + 1);\n          content = css.slice(pos, next + 1);\n\n          if (next === -1 || RE_BAD_BRACKET.test(content)) {\n            tokens.push(['(', '(', line, pos - offset]);\n          } else {\n            tokens.push(['brackets', content, line, pos - offset, line, next - offset]);\n            pos = next;\n          }\n        }\n\n        break;\n\n      case CLOSE_PARENTHESES:\n        tokens.push([')', ')', line, pos - offset]);\n        break;\n\n      case SINGLE_QUOTE:\n      case DOUBLE_QUOTE:\n        quote = code === SINGLE_QUOTE ? \"'\" : '\"';\n        next = pos;\n        do {\n          escaped = false;\n          next = css.indexOf(quote, next + 1);\n          if (next === -1) {\n            if (ignore) {\n              next = pos + 1;\n              break;\n            } else {\n              unclosed('quote');\n            }\n          }\n          escapePos = next;\n          while (css.charCodeAt(escapePos - 1) === BACKSLASH) {\n            escapePos -= 1;\n            escaped = !escaped;\n          }\n        } while (escaped);\n\n        content = css.slice(pos, next + 1);\n        lines = content.split('\\n');\n        last = lines.length - 1;\n\n        if (last > 0) {\n          nextLine = line + last;\n          nextOffset = next - lines[last].length;\n        } else {\n          nextLine = line;\n          nextOffset = offset;\n        }\n\n        tokens.push([\n          'string',\n          css.slice(pos, next + 1),\n          line,\n          pos - offset,\n          nextLine,\n          next - nextOffset,\n        ]);\n\n        offset = nextOffset;\n        line = nextLine;\n        pos = next;\n        break;\n\n      case AT:\n        RE_AT_END.lastIndex = pos + 1;\n        RE_AT_END.test(css);\n        if (RE_AT_END.lastIndex === 0) {\n          next = css.length - 1;\n        } else {\n          next = RE_AT_END.lastIndex - 2;\n        }\n        tokens.push(['at-word', css.slice(pos, next + 1), line, pos - offset, line, next - offset]);\n        pos = next;\n        break;\n\n      case BACKSLASH:\n        next = pos;\n        escape = true;\n        while (css.charCodeAt(next + 1) === BACKSLASH) {\n          next += 1;\n          escape = !escape;\n        }\n        code = css.charCodeAt(next + 1);\n        if (\n          escape &&\n          (code !== SLASH &&\n            code !== SPACE &&\n            code !== NEWLINE &&\n            code !== TAB &&\n            code !== CR &&\n            code !== FEED)\n        ) {\n          next += 1;\n        }\n        tokens.push(['word', css.slice(pos, next + 1), line, pos - offset, line, next - offset]);\n        pos = next;\n        break;\n\n      default:\n        if (code === SLASH && css.charCodeAt(pos + 1) === ASTERISK) {\n          next = css.indexOf('*/', pos + 2) + 1;\n          if (next === 0) {\n            if (ignore) {\n              next = css.length;\n            } else {\n              unclosed('comment');\n            }\n          }\n\n          content = css.slice(pos, next + 1);\n          lines = content.split('\\n');\n          last = lines.length - 1;\n\n          if (last > 0) {\n            nextLine = line + last;\n            nextOffset = next - lines[last].length;\n          } else {\n            nextLine = line;\n            nextOffset = offset;\n          }\n\n          tokens.push(['comment', content, line, pos - offset, nextLine, next - nextOffset]);\n\n          offset = nextOffset;\n          line = nextLine;\n          pos = next;\n        } else {\n          RE_WORD_END.lastIndex = pos + 1;\n          RE_WORD_END.test(css);\n          if (RE_WORD_END.lastIndex === 0) {\n            next = css.length - 1;\n          } else {\n            next = RE_WORD_END.lastIndex - 2;\n          }\n\n          tokens.push(['word', css.slice(pos, next + 1), line, pos - offset, line, next - offset]);\n          pos = next;\n        }\n\n        break;\n    }\n\n    pos++;\n  }\n\n  return tokens;\n}\n", "// @flow\nimport tokenize from './tokenize';\nimport Input from './input';\n\nconst HIGHLIGHT_THEME = {\n  brackets: [36, 39], // cyan\n  string: [31, 39], // red\n  'at-word': [31, 39], // red\n  comment: [90, 39], // gray\n  '{': [32, 39], // green\n  '}': [32, 39], // green\n  ':': [1, 22], // bold\n  ';': [1, 22], // bold\n  '(': [1, 22], // bold\n  ')': [1, 22], // bold\n};\n\nfunction code(color) {\n  return `\\u001b[${color}m`;\n}\n\nfunction terminalHighlight(css) {\n  const tokens = tokenize(new Input(css), { ignoreErrors: true });\n  const result = [];\n  tokens.forEach(token => {\n    const color = HIGHLIGHT_THEME[token[0]];\n    if (color) {\n      result.push(\n        token[1]\n          .split(/\\r?\\n/)\n          .map(i => code(color[0]) + i + code(color[1]))\n          .join('\\n')\n      );\n    } else {\n      result.push(token[1]);\n    }\n  });\n  return result.join('');\n}\n\nexport default terminalHighlight;\n", "// @flow\nimport supports<PERSON><PERSON>r from 'supports-color';\n\nimport terminalHighlight from './terminal-highlight';\nimport warnOnce from './warn-once';\n\n/**\n * The CSS parser throws this error for broken CSS.\n *\n * Custom parsers can throw this error for broken custom syntax using\n * the {@link Node#error} method.\n *\n * PostCSS will use the input source map to detect the original error location.\n * If you wrote a Sass file, compiled it to CSS and then parsed it with PostCSS,\n * PostCSS will show the original position in the Sass file.\n *\n * If you need the position in the PostCSS input\n * (e.g., to debug the previous compiler), use `error.input.file`.\n *\n * @example\n * // Catching and checking syntax error\n * try {\n *   postcss.parse('a{')\n * } catch (error) {\n *   if ( error.name === 'CssSyntaxError' ) {\n *     error //=> CssSyntaxError\n *   }\n * }\n *\n * @example\n * // Raising error from plugin\n * throw node.error('Unknown variable', { plugin: 'postcss-vars' });\n */\nclass CssSyntaxError {\n  /**\n   * @param {string} message  - error message\n   * @param {number} [line]   - source line of the error\n   * @param {number} [column] - source column of the error\n   * @param {string} [source] - source code of the broken file\n   * @param {string} [file]   - absolute path to the broken file\n   * @param {string} [plugin] - PostCSS plugin name, if error came from plugin\n   */\n  constructor(message, line, column, source, file, plugin) {\n    /**\n     * @member {string} - Always equal to `'CssSyntaxError'`. You should\n     *                    always check error type\n     *                    by `error.name === 'CssSyntaxError'` instead of\n     *                    `error instanceof CssSyntaxError`, because\n     *                    npm could have several PostCSS versions.\n     *\n     * @example\n     * if ( error.name === 'CssSyntaxError' ) {\n     *   error //=> CssSyntaxError\n     * }\n     */\n    this.name = 'CssSyntaxError';\n    /**\n     * @member {string} - Error message.\n     *\n     * @example\n     * error.message //=> 'Unclosed block'\n     */\n    this.reason = message;\n\n    if (file) {\n      /**\n       * @member {string} - Absolute path to the broken file.\n       *\n       * @example\n       * error.file       //=> 'a.sass'\n       * error.input.file //=> 'a.css'\n       */\n      this.file = file;\n    }\n    if (source) {\n      /**\n       * @member {string} - Source code of the broken file.\n       *\n       * @example\n       * error.source       //=> 'a { b {} }'\n       * error.input.column //=> 'a b { }'\n       */\n      this.source = source;\n    }\n    if (plugin) {\n      /**\n       * @member {string} - Plugin name, if error came from plugin.\n       *\n       * @example\n       * error.plugin //=> 'postcss-vars'\n       */\n      this.plugin = plugin;\n    }\n    if (typeof line !== 'undefined' && typeof column !== 'undefined') {\n      /**\n       * @member {number} - Source line of the error.\n       *\n       * @example\n       * error.line       //=> 2\n       * error.input.line //=> 4\n       */\n      this.line = line;\n      /**\n       * @member {number} - Source column of the error.\n       *\n       * @example\n       * error.column       //=> 1\n       * error.input.column //=> 4\n       */\n      this.column = column;\n    }\n\n    this.setMessage();\n\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, CssSyntaxError);\n    }\n  }\n\n  setMessage() {\n    /**\n     * @member {string} - Full error text in the GNU error format\n     *                    with plugin, file, line and column.\n     *\n     * @example\n     * error.message //=> 'a.css:1:1: Unclosed block'\n     */\n    this.message = this.plugin ? `${this.plugin}: ` : '';\n    this.message += this.file ? this.file : '<css input>';\n    if (typeof this.line !== 'undefined') {\n      this.message += `:${this.line}:${this.column}`;\n    }\n    this.message += `: ${this.reason}`;\n  }\n\n  /**\n   * Returns a few lines of CSS source that caused the error.\n   *\n   * If the CSS has an input source map without `sourceContent`,\n   * this method will return an empty string.\n   *\n   * @param {boolean} [color] whether arrow will be colored red by terminal\n   *                          color codes. By default, PostCSS will detect\n   *                          color support by `process.stdout.isTTY`\n   *                          and `process.env.NODE_DISABLE_COLORS`.\n   *\n   * @example\n   * error.showSourceCode() //=> \"  4 | }\n   *                        //      5 | a {\n   *                        //    > 6 |   bad\n   *                        //        |   ^\n   *                        //      7 | }\n   *                        //      8 | b {\"\n   *\n   * @return {string} few lines of CSS source that caused the error\n   */\n  showSourceCode(color) {\n    if (!this.source) return '';\n\n    let css = this.source;\n    if (typeof color === 'undefined') color = supportsColor;\n    if (color) css = terminalHighlight(css);\n\n    const lines = css.split(/\\r?\\n/);\n    const start = Math.max(this.line - 3, 0);\n    const end = Math.min(this.line + 2, lines.length);\n\n    const maxWidth = String(end).length;\n\n    return lines\n      .slice(start, end)\n      .map((line, index) => {\n        const number = start + 1 + index;\n        const padded = ` ${number}`.slice(-maxWidth);\n        const gutter = ` ${padded} | `;\n        if (number === this.line) {\n          const spacing =\n            gutter.replace(/\\d/g, ' ') + line.slice(0, this.column - 1).replace(/[^\\t]/g, ' ');\n          return `>${gutter}${line}\\n ${spacing}^`;\n        } else {\n          return ` ${gutter}${line}`;\n        }\n      })\n      .join('\\n');\n  }\n\n  /**\n   * Returns error position, message and source code of the broken part.\n   *\n   * @example\n   * error.toString() //=> \"CssSyntaxError: app.css:1:1: Unclosed block\n   *                  //    > 1 | a {\n   *                  //        | ^\"\n   *\n   * @return {string} error position, message and source code\n   */\n  toString() {\n    let code = this.showSourceCode();\n    if (code) {\n      code = `\\n\\n${code}\\n`;\n    }\n    return `${this.name}: ${this.message}${code}`;\n  }\n\n  get generated() {\n    warnOnce('CssSyntaxError#generated is deprecated. Use input instead.');\n    return this.input;\n  }\n\n  /**\n   * @memberof CssSyntaxError#\n   * @member {Input} input - Input object with PostCSS internal information\n   *                         about input file. If input has source map\n   *                         from previous tool, PostCSS will use origin\n   *                         (for example, Sass) source. You can use this\n   *                         object to get PostCSS input source.\n   *\n   * @example\n   * error.input.file //=> 'a.css'\n   * error.file       //=> 'a.sass'\n   */\n}\n\nexport default CssSyntaxError;\n", "// @flow\n/* eslint-disable valid-jsdoc */\n\nconst defaultRaw = {\n  colon: ': ',\n  indent: '    ',\n  beforeDecl: '\\n',\n  beforeRule: '\\n',\n  beforeOpen: ' ',\n  beforeClose: '\\n',\n  beforeComment: '\\n',\n  after: '\\n',\n  emptyBody: '',\n  commentLeft: ' ',\n  commentRight: ' ',\n};\n\nfunction capitalize(str) {\n  return str[0].toUpperCase() + str.slice(1);\n}\n\nclass Stringifier {\n  constructor(builder) {\n    this.builder = builder;\n  }\n\n  stringify(node, semicolon) {\n    this[node.type](node, semicolon);\n  }\n\n  root(node) {\n    this.body(node);\n    if (node.raws.after) this.builder(node.raws.after);\n  }\n\n  comment(node) {\n    const left = this.raw(node, 'left', 'commentLeft');\n    const right = this.raw(node, 'right', 'commentRight');\n    this.builder(`/*${left}${node.text}${right}*/`, node);\n  }\n\n  decl(node, semicolon) {\n    const between = this.raw(node, 'between', 'colon');\n    let string = node.prop + between + this.rawValue(node, 'value');\n\n    if (node.important) {\n      string += node.raws.important || ' !important';\n    }\n\n    if (semicolon) string += ';';\n    this.builder(string, node);\n  }\n\n  rule(node) {\n    this.block(node, this.rawValue(node, 'selector'));\n  }\n\n  atrule(node, semicolon) {\n    let name = `@${node.name}`;\n    const params = node.params ? this.rawValue(node, 'params') : '';\n\n    if (typeof node.raws.afterName !== 'undefined') {\n      name += node.raws.afterName;\n    } else if (params) {\n      name += ' ';\n    }\n\n    if (node.nodes) {\n      this.block(node, name + params);\n    } else {\n      const end = (node.raws.between || '') + (semicolon ? ';' : '');\n      this.builder(name + params + end, node);\n    }\n  }\n\n  body(node) {\n    let last = node.nodes.length - 1;\n    while (last > 0) {\n      if (node.nodes[last].type !== 'comment') break;\n      last -= 1;\n    }\n\n    const semicolon = this.raw(node, 'semicolon');\n    for (let i = 0; i < node.nodes.length; i++) {\n      const child = node.nodes[i];\n      const before = this.raw(child, 'before');\n      if (before) this.builder(before);\n      this.stringify(child, last !== i || semicolon);\n    }\n  }\n\n  block(node, start) {\n    const between = this.raw(node, 'between', 'beforeOpen');\n    this.builder(`${start + between}{`, node, 'start');\n\n    let after;\n    if (node.nodes && node.nodes.length) {\n      this.body(node);\n      after = this.raw(node, 'after');\n    } else {\n      after = this.raw(node, 'after', 'emptyBody');\n    }\n\n    if (after) this.builder(after);\n    this.builder('}', node, 'end');\n  }\n\n  raw(node, own, detect) {\n    let value;\n    if (!detect) detect = own;\n\n    // Already had\n    if (own) {\n      value = node.raws[own];\n      if (typeof value !== 'undefined') return value;\n    }\n\n    const parent = node.parent;\n\n    // Hack for first rule in CSS\n    if (detect === 'before') {\n      if (!parent || (parent.type === 'root' && parent.first === node)) {\n        return '';\n      }\n    }\n\n    // Floating child without parent\n    if (!parent) return defaultRaw[detect];\n\n    // Detect style by other nodes\n    const root = node.root();\n    if (!root.rawCache) root.rawCache = {};\n    if (typeof root.rawCache[detect] !== 'undefined') {\n      return root.rawCache[detect];\n    }\n\n    if (detect === 'before' || detect === 'after') {\n      return this.beforeAfter(node, detect);\n    } else {\n      const method = `raw${capitalize(detect)}`;\n      if (this[method]) {\n        value = this[method](root, node);\n      } else {\n        root.walk(i => {\n          value = i.raws[own];\n          if (typeof value !== 'undefined') return false;\n        });\n      }\n    }\n\n    if (typeof value === 'undefined') value = defaultRaw[detect];\n\n    root.rawCache[detect] = value;\n    return value;\n  }\n\n  rawSemicolon(root) {\n    let value;\n    root.walk(i => {\n      if (i.nodes && i.nodes.length && i.last.type === 'decl') {\n        value = i.raws.semicolon;\n        if (typeof value !== 'undefined') return false;\n      }\n    });\n    return value;\n  }\n\n  rawEmptyBody(root) {\n    let value;\n    root.walk(i => {\n      if (i.nodes && i.nodes.length === 0) {\n        value = i.raws.after;\n        if (typeof value !== 'undefined') return false;\n      }\n    });\n    return value;\n  }\n\n  rawIndent(root) {\n    if (root.raws.indent) return root.raws.indent;\n    let value;\n    root.walk(i => {\n      const p = i.parent;\n      if (p && p !== root && p.parent && p.parent === root) {\n        if (typeof i.raws.before !== 'undefined') {\n          const parts = i.raws.before.split('\\n');\n          value = parts[parts.length - 1];\n          value = value.replace(/[^\\s]/g, '');\n          return false;\n        }\n      }\n    });\n    return value;\n  }\n\n  rawBeforeComment(root, node) {\n    let value;\n    root.walkComments(i => {\n      if (typeof i.raws.before !== 'undefined') {\n        value = i.raws.before;\n        if (value.indexOf('\\n') !== -1) {\n          value = value.replace(/[^\\n]+$/, '');\n        }\n        return false;\n      }\n    });\n    if (typeof value === 'undefined') {\n      value = this.raw(node, null, 'beforeDecl');\n    }\n    return value;\n  }\n\n  rawBeforeDecl(root, node) {\n    let value;\n    root.walkDecls(i => {\n      if (typeof i.raws.before !== 'undefined') {\n        value = i.raws.before;\n        if (value.indexOf('\\n') !== -1) {\n          value = value.replace(/[^\\n]+$/, '');\n        }\n        return false;\n      }\n    });\n    if (typeof value === 'undefined') {\n      value = this.raw(node, null, 'beforeRule');\n    }\n    return value;\n  }\n\n  rawBeforeRule(root) {\n    let value;\n    root.walk(i => {\n      if (i.nodes && (i.parent !== root || root.first !== i)) {\n        if (typeof i.raws.before !== 'undefined') {\n          value = i.raws.before;\n          if (value.indexOf('\\n') !== -1) {\n            value = value.replace(/[^\\n]+$/, '');\n          }\n          return false;\n        }\n      }\n    });\n    return value;\n  }\n\n  rawBeforeClose(root) {\n    let value;\n    root.walk(i => {\n      if (i.nodes && i.nodes.length > 0) {\n        if (typeof i.raws.after !== 'undefined') {\n          value = i.raws.after;\n          if (value.indexOf('\\n') !== -1) {\n            value = value.replace(/[^\\n]+$/, '');\n          }\n          return false;\n        }\n      }\n    });\n    return value;\n  }\n\n  rawBeforeOpen(root) {\n    let value;\n    root.walk(i => {\n      if (i.type !== 'decl') {\n        value = i.raws.between;\n        if (typeof value !== 'undefined') return false;\n      }\n    });\n    return value;\n  }\n\n  rawColon(root) {\n    let value;\n    root.walkDecls(i => {\n      if (typeof i.raws.between !== 'undefined') {\n        value = i.raws.between.replace(/[^\\s:]/g, '');\n        return false;\n      }\n    });\n    return value;\n  }\n\n  beforeAfter(node, detect) {\n    let value;\n    if (node.type === 'decl') {\n      value = this.raw(node, null, 'beforeDecl');\n    } else if (node.type === 'comment') {\n      value = this.raw(node, null, 'beforeComment');\n    } else if (detect === 'before') {\n      value = this.raw(node, null, 'beforeRule');\n    } else {\n      value = this.raw(node, null, 'beforeClose');\n    }\n\n    let buf = node.parent;\n    let depth = 0;\n    while (buf && buf.type !== 'root') {\n      depth += 1;\n      buf = buf.parent;\n    }\n\n    if (value.indexOf('\\n') !== -1) {\n      const indent = this.raw(node, null, 'indent');\n      if (indent.length) {\n        for (let step = 0; step < depth; step++) value += indent;\n      }\n    }\n\n    return value;\n  }\n\n  rawValue(node, prop) {\n    const value = node[prop];\n    const raw = node.raws[prop];\n    if (raw && raw.value === value) {\n      return raw.raw;\n    } else {\n      return value;\n    }\n  }\n}\n\nexport default Stringifier;\n", "// @flow\nimport Stringifier from './stringifier';\n\nexport default function stringify(node, builder) {\n  const str = new Stringifier(builder);\n  str.stringify(node);\n}\n", "// @flow\nimport CssSyntaxError from './css-syntax-error';\nimport Stringifier from './stringifier';\nimport stringify from './stringify';\nimport warnOnce from './warn-once';\n\n/**\n * @typedef {object} position\n * @property {number} line   - source line in file\n * @property {number} column - source column in file\n */\n\n/**\n * @typedef {object} source\n * @property {Input} input    - {@link Input} with input file\n * @property {position} start - The starting position of the node’s source\n * @property {position} end   - The ending position of the node’s source\n */\n\nconst cloneNode = function(obj, parent) {\n  const cloned = new obj.constructor();\n\n  for (const i in obj) {\n    if (!obj.hasOwnProperty(i)) continue;\n    let value = obj[i];\n    const type = typeof value;\n\n    if (i === 'parent' && type === 'object') {\n      if (parent) cloned[i] = parent;\n    } else if (i === 'source') {\n      cloned[i] = value;\n    } else if (value instanceof Array) {\n      cloned[i] = value.map(j => cloneNode(j, cloned));\n    } else if (i !== 'before' && i !== 'after' && i !== 'between' && i !== 'semicolon') {\n      if (type === 'object' && value !== null) value = cloneNode(value);\n      cloned[i] = value;\n    }\n  }\n\n  return cloned;\n};\n\n/**\n * All node classes inherit the following common methods.\n *\n * @abstract\n */\nclass Node {\n  /**\n   * @param {object} [defaults] - value for node properties\n   */\n  constructor(defaults = {}) {\n    this.raws = {};\n    for (const name in defaults) {\n      this[name] = defaults[name];\n    }\n  }\n\n  /**\n   * Returns a CssSyntaxError instance containing the original position\n   * of the node in the source, showing line and column numbers and also\n   * a small excerpt to facilitate debugging.\n   *\n   * If present, an input source map will be used to get the original position\n   * of the source, even from a previous compilation step\n   * (e.g., from Sass compilation).\n   *\n   * This method produces very useful error messages.\n   *\n   * @param {string} message     - error description\n   * @param {object} [opts]      - options\n   * @param {string} opts.plugin - plugin name that created this error.\n   *                               PostCSS will set it automatically.\n   * @param {string} opts.word   - a word inside a node’s string that should\n   *                               be highlighted as the source of the error\n   * @param {number} opts.index  - an index inside a node’s string that should\n   *                               be highlighted as the source of the error\n   *\n   * @return {CssSyntaxError} error object to throw it\n   *\n   * @example\n   * if ( !variables[name] ) {\n   *   throw decl.error('Unknown variable ' + name, { word: name });\n   *   // CssSyntaxError: postcss-vars:a.sass:4:3: Unknown variable $black\n   *   //   color: $black\n   *   // a\n   *   //          ^\n   *   //   background: white\n   * }\n   */\n  error(message, opts = {}) {\n    if (this.source) {\n      const pos = this.positionBy(opts);\n      return this.source.input.error(message, pos.line, pos.column, opts);\n    } else {\n      return new CssSyntaxError(message);\n    }\n  }\n\n  /**\n   * This method is provided as a convenience wrapper for {@link Result#warn}.\n   *\n   * @param {Result} result      - the {@link Result} instance\n   *                               that will receive the warning\n   * @param {string} text        - warning message\n   * @param {object} [opts]      - options\n   * @param {string} opts.plugin - plugin name that created this warning.\n   *                               PostCSS will set it automatically.\n   * @param {string} opts.word   - a word inside a node’s string that should\n   *                               be highlighted as the source of the warning\n   * @param {number} opts.index  - an index inside a node’s string that should\n   *                               be highlighted as the source of the warning\n   *\n   * @return {Warning} created warning object\n   *\n   * @example\n   * const plugin = postcss.plugin('postcss-deprecated', () => {\n   *   return (root, result) => {\n   *     root.walkDecls('bad', decl => {\n   *       decl.warn(result, 'Deprecated property bad');\n   *     });\n   *   };\n   * });\n   */\n  warn(result, text, opts) {\n    const data = { node: this };\n    for (const i in opts) data[i] = opts[i];\n    return result.warn(text, data);\n  }\n\n  /**\n   * Removes the node from its parent and cleans the parent properties\n   * from the node and its children.\n   *\n   * @example\n   * if ( decl.prop.match(/^-webkit-/) ) {\n   *   decl.remove();\n   * }\n   *\n   * @return {Node} node to make calls chain\n   */\n  remove() {\n    if (this.parent) {\n      this.parent.removeChild(this);\n    }\n    this.parent = undefined;\n    return this;\n  }\n\n  /**\n   * Returns a CSS string representing the node.\n   *\n   * @param {stringifier|syntax} [stringifier] - a syntax to use\n   *                                             in string generation\n   *\n   * @return {string} CSS string of this node\n   *\n   * @example\n   * postcss.rule({ selector: 'a' }).toString() //=> \"a {}\"\n   */\n  toString(stringifier = stringify) {\n    if (stringifier.stringify) stringifier = stringifier.stringify;\n    let result = '';\n    stringifier(this, i => {\n      result += i;\n    });\n    return result;\n  }\n\n  /**\n   * Returns a clone of the node.\n   *\n   * The resulting cloned node and its (cloned) children will have\n   * a clean parent and code style properties.\n   *\n   * @param {object} [overrides] - new properties to override in the clone.\n   *\n   * @example\n   * const cloned = decl.clone({ prop: '-moz-' + decl.prop });\n   * cloned.raws.before  //=> undefined\n   * cloned.parent       //=> undefined\n   * cloned.toString()   //=> -moz-transform: scale(0)\n   *\n   * @return {Node} clone of the node\n   */\n  clone(overrides = {}) {\n    const cloned = cloneNode(this);\n    for (const name in overrides) {\n      cloned[name] = overrides[name];\n    }\n    return cloned;\n  }\n\n  /**\n   * Shortcut to clone the node and insert the resulting cloned node\n   * before the current node.\n   *\n   * @param {object} [overrides] - new properties to override in the clone.\n   *\n   * @example\n   * decl.cloneBefore({ prop: '-moz-' + decl.prop });\n   *\n   * @return {Node} - new node\n   */\n  cloneBefore(overrides = {}) {\n    const cloned = this.clone(overrides);\n    this.parent.insertBefore(this, cloned);\n    return cloned;\n  }\n\n  /**\n   * Shortcut to clone the node and insert the resulting cloned node\n   * after the current node.\n   *\n   * @param {object} [overrides] - new properties to override in the clone.\n   *\n   * @return {Node} - new node\n   */\n  cloneAfter(overrides = {}) {\n    const cloned = this.clone(overrides);\n    this.parent.insertAfter(this, cloned);\n    return cloned;\n  }\n\n  /**\n   * Inserts node(s) before the current node and removes the current node.\n   *\n   * @param {...Node} nodes - node(s) to replace current one\n   *\n   * @example\n   * if ( atrule.name == 'mixin' ) {\n   *   atrule.replaceWith(mixinRules[atrule.params]);\n   * }\n   *\n   * @return {Node} current node to methods chain\n   */\n  replaceWith(...nodes) {\n    if (this.parent) {\n      nodes.forEach(node => {\n        this.parent.insertBefore(this, node);\n      });\n\n      this.remove();\n    }\n\n    return this;\n  }\n\n  /**\n   * Removes the node from its current parent and inserts it\n   * at the end of `newParent`.\n   *\n   * This will clean the `before` and `after` code {@link Node#raws} data\n   * from the node and replace them with the indentation style of `newParent`.\n   * It will also clean the `between` property\n   * if `newParent` is in another {@link Root}.\n   *\n   * @param {Container} newParent - container node where the current node\n   *                                will be moved\n   *\n   * @example\n   * atrule.moveTo(atrule.root());\n   *\n   * @return {Node} current node to methods chain\n   */\n  moveTo(newParent) {\n    this.cleanRaws(this.root() === newParent.root());\n    this.remove();\n    newParent.append(this);\n    return this;\n  }\n\n  /**\n   * Removes the node from its current parent and inserts it into\n   * a new parent before `otherNode`.\n   *\n   * This will also clean the node’s code style properties just as it would\n   * in {@link Node#moveTo}.\n   *\n   * @param {Node} otherNode - node that will be before current node\n   *\n   * @return {Node} current node to methods chain\n   */\n  moveBefore(otherNode) {\n    this.cleanRaws(this.root() === otherNode.root());\n    this.remove();\n    otherNode.parent.insertBefore(otherNode, this);\n    return this;\n  }\n\n  /**\n   * Removes the node from its current parent and inserts it into\n   * a new parent after `otherNode`.\n   *\n   * This will also clean the node’s code style properties just as it would\n   * in {@link Node#moveTo}.\n   *\n   * @param {Node} otherNode - node that will be after current node\n   *\n   * @return {Node} current node to methods chain\n   */\n  moveAfter(otherNode) {\n    this.cleanRaws(this.root() === otherNode.root());\n    this.remove();\n    otherNode.parent.insertAfter(otherNode, this);\n    return this;\n  }\n\n  /**\n   * Returns the next child of the node’s parent.\n   * Returns `undefined` if the current node is the last child.\n   *\n   * @return {Node|undefined} next node\n   *\n   * @example\n   * if ( comment.text === 'delete next' ) {\n   *   const next = comment.next();\n   *   if ( next ) {\n   *     next.remove();\n   *   }\n   * }\n   */\n  next() {\n    const index = this.parent.index(this);\n    return this.parent.nodes[index + 1];\n  }\n\n  /**\n   * Returns the previous child of the node’s parent.\n   * Returns `undefined` if the current node is the first child.\n   *\n   * @return {Node|undefined} previous node\n   *\n   * @example\n   * const annotation = decl.prev();\n   * if ( annotation.type == 'comment' ) {\n   *  readAnnotation(annotation.text);\n   * }\n   */\n  prev() {\n    const index = this.parent.index(this);\n    return this.parent.nodes[index - 1];\n  }\n\n  toJSON() {\n    const fixed = {};\n\n    for (const name in this) {\n      if (!this.hasOwnProperty(name)) continue;\n      if (name === 'parent') continue;\n      const value = this[name];\n\n      if (value instanceof Array) {\n        fixed[name] = value.map(i => {\n          if (typeof i === 'object' && i.toJSON) {\n            return i.toJSON();\n          } else {\n            return i;\n          }\n        });\n      } else if (typeof value === 'object' && value.toJSON) {\n        fixed[name] = value.toJSON();\n      } else {\n        fixed[name] = value;\n      }\n    }\n\n    return fixed;\n  }\n\n  /**\n   * Returns a {@link Node#raws} value. If the node is missing\n   * the code style property (because the node was manually built or cloned),\n   * PostCSS will try to autodetect the code style property by looking\n   * at other nodes in the tree.\n   *\n   * @param {string} prop          - name of code style property\n   * @param {string} [defaultType] - name of default value, it can be missed\n   *                                 if the value is the same as prop\n   *\n   * @example\n   * const root = postcss.parse('a { background: white }');\n   * root.nodes[0].append({ prop: 'color', value: 'black' });\n   * root.nodes[0].nodes[1].raws.before   //=> undefined\n   * root.nodes[0].nodes[1].raw('before') //=> ' '\n   *\n   * @return {string} code style value\n   */\n  raw(prop, defaultType) {\n    const str = new Stringifier();\n    return str.raw(this, prop, defaultType);\n  }\n\n  /**\n   * Finds the Root instance of the node’s tree.\n   *\n   * @example\n   * root.nodes[0].nodes[0].root() === root\n   *\n   * @return {Root} root parent\n   */\n  root() {\n    let result = this;\n    while (result.parent) result = result.parent;\n    return result;\n  }\n\n  cleanRaws(keepBetween) {\n    delete this.raws.before;\n    delete this.raws.after;\n    if (!keepBetween) delete this.raws.between;\n  }\n\n  positionInside(index) {\n    const string = this.toString();\n    let column = this.source.start.column;\n    let line = this.source.start.line;\n\n    for (let i = 0; i < index; i++) {\n      if (string[i] === '\\n') {\n        column = 1;\n        line += 1;\n      } else {\n        column += 1;\n      }\n    }\n\n    return { line, column };\n  }\n\n  positionBy(opts) {\n    let pos = this.source.start;\n    if (opts.index) {\n      pos = this.positionInside(opts.index);\n    } else if (opts.word) {\n      const index = this.toString().indexOf(opts.word);\n      if (index !== -1) pos = this.positionInside(index);\n    }\n    return pos;\n  }\n\n  removeSelf() {\n    warnOnce('Node#removeSelf is deprecated. Use Node#remove.');\n    return this.remove();\n  }\n\n  replace(nodes) {\n    warnOnce('Node#replace is deprecated. Use Node#replaceWith');\n    return this.replaceWith(nodes);\n  }\n\n  style(own, detect) {\n    warnOnce('Node#style() is deprecated. Use Node#raw()');\n    return this.raw(own, detect);\n  }\n\n  cleanStyles(keepBetween) {\n    warnOnce('Node#cleanStyles() is deprecated. Use Node#cleanRaws()');\n    return this.cleanRaws(keepBetween);\n  }\n\n  get before() {\n    warnOnce('Node#before is deprecated. Use Node#raws.before');\n    return this.raws.before;\n  }\n\n  set before(val) {\n    warnOnce('Node#before is deprecated. Use Node#raws.before');\n    this.raws.before = val;\n  }\n\n  get between() {\n    warnOnce('Node#between is deprecated. Use Node#raws.between');\n    return this.raws.between;\n  }\n\n  set between(val) {\n    warnOnce('Node#between is deprecated. Use Node#raws.between');\n    this.raws.between = val;\n  }\n\n  /**\n   * @memberof Node#\n   * @member {string} type - String representing the node’s type.\n   *                         Possible values are `root`, `atrule`, `rule`,\n   *                         `decl`, or `comment`.\n   *\n   * @example\n   * postcss.decl({ prop: 'color', value: 'black' }).type //=> 'decl'\n   */\n\n  /**\n   * @memberof Node#\n   * @member {Container} parent - the node’s parent node.\n   *\n   * @example\n   * root.nodes[0].parent == root;\n   */\n\n  /**\n   * @memberof Node#\n   * @member {source} source - the input source of the node\n   *\n   * The property is used in source map generation.\n   *\n   * If you create a node manually (e.g., with `postcss.decl()`),\n   * that node will not have a `source` property and will be absent\n   * from the source map. For this reason, the plugin developer should\n   * consider cloning nodes to create new ones (in which case the new node’s\n   * source will reference the original, cloned node) or setting\n   * the `source` property manually.\n   *\n   * ```js\n   * // Bad\n   * const prefixed = postcss.decl({\n   *   prop: '-moz-' + decl.prop,\n   *   value: decl.value\n   * });\n   *\n   * // Good\n   * const prefixed = decl.clone({ prop: '-moz-' + decl.prop });\n   * ```\n   *\n   * ```js\n   * if ( atrule.name == 'add-link' ) {\n   *   const rule = postcss.rule({ selector: 'a', source: atrule.source });\n   *   atrule.parent.insertBefore(atrule, rule);\n   * }\n   * ```\n   *\n   * @example\n   * decl.source.input.from //=> '/home/<USER>/a.sass'\n   * decl.source.start      //=> { line: 10, column: 2 }\n   * decl.source.end        //=> { line: 10, column: 12 }\n   */\n\n  /**\n   * @memberof Node#\n   * @member {object} raws - Information to generate byte-to-byte equal\n   *                         node string as it was in the origin input.\n   *\n   * Every parser saves its own properties,\n   * but the default CSS parser uses:\n   *\n   * * `before`: the space symbols before the node. It also stores `*`\n   *   and `_` symbols before the declaration (IE hack).\n   * * `after`: the space symbols after the last child of the node\n   *   to the end of the node.\n   * * `between`: the symbols between the property and value\n   *   for declarations, selector and `{` for rules, or last parameter\n   *   and `{` for at-rules.\n   * * `semicolon`: contains true if the last child has\n   *   an (optional) semicolon.\n   * * `afterName`: the space between the at-rule name and its parameters.\n   * * `left`: the space symbols between `/*` and the comment’s text.\n   * * `right`: the space symbols between the comment’s text\n   *   and <code>*&#47;</code>.\n   * * `important`: the content of the important statement,\n   *   if it is not just `!important`.\n   *\n   * PostCSS cleans selectors, declaration values and at-rule parameters\n   * from comments and extra spaces, but it stores origin content in raws\n   * properties. As such, if you don’t change a declaration’s value,\n   * PostCSS will use the raw value with comments.\n   *\n   * @example\n   * const root = postcss.parse('a {\\n  color:black\\n}')\n   * root.first.first.raws //=> { before: '\\n  ', between: ':' }\n   */\n}\n\nexport default Node;\n", "// @flow\nimport warnOnce from './warn-once';\nimport Node from './node';\n\n/**\n * Represents a CSS declaration.\n *\n * @extends Node\n *\n * @example\n * const root = postcss.parse('a { color: black }');\n * const decl = root.first.first;\n * decl.type       //=> 'decl'\n * decl.toString() //=> ' color: black'\n */\nclass Declaration extends Node {\n  constructor(defaults) {\n    super(defaults);\n    this.type = 'decl';\n  }\n\n  get _value() {\n    warnOnce('Node#_value was deprecated. Use Node#raws.value');\n    return this.raws.value;\n  }\n\n  set _value(val) {\n    warnOnce('Node#_value was deprecated. Use Node#raws.value');\n    this.raws.value = val;\n  }\n\n  get _important() {\n    warnOnce('Node#_important was deprecated. Use Node#raws.important');\n    return this.raws.important;\n  }\n\n  set _important(val) {\n    warnOnce('Node#_important was deprecated. Use Node#raws.important');\n    this.raws.important = val;\n  }\n\n  /**\n   * @memberof Declaration#\n   * @member {string} prop - the declaration’s property name\n   *\n   * @example\n   * const root = postcss.parse('a { color: black }');\n   * const decl = root.first.first;\n   * decl.prop //=> 'color'\n   */\n\n  /**\n   * @memberof Declaration#\n   * @member {string} value - the declaration’s value\n   *\n   * @example\n   * const root = postcss.parse('a { color: black }');\n   * const decl = root.first.first;\n   * decl.value //=> 'black'\n   */\n\n  /**\n   * @memberof Declaration#\n   * @member {boolean} important - `true` if the declaration\n   *                               has an !important annotation.\n   *\n   * @example\n   * const root = postcss.parse('a { color: black !important; color: red }');\n   * root.first.first.important //=> true\n   * root.first.last.important  //=> undefined\n   */\n\n  /**\n   * @memberof Declaration#\n   * @member {object} raws - Information to generate byte-to-byte equal\n   *                         node string as it was in the origin input.\n   *\n   * Every parser saves its own properties,\n   * but the default CSS parser uses:\n   *\n   * * `before`: the space symbols before the node. It also stores `*`\n   *   and `_` symbols before the declaration (IE hack).\n   * * `between`: the symbols between the property and value\n   *   for declarations, selector and `{` for rules, or last parameter\n   *   and `{` for at-rules.\n   * * `important`: the content of the important statement,\n   *   if it is not just `!important`.\n   *\n   * PostCSS cleans declaration from comments and extra spaces,\n   * but it stores origin content in raws properties.\n   * As such, if you don’t change a declaration’s value,\n   * PostCSS will use the raw value with comments.\n   *\n   * @example\n   * const root = postcss.parse('a {\\n  color:black\\n}')\n   * root.first.first.raws //=> { before: '\\n  ', between: ':' }\n   */\n}\n\nexport default Declaration;\n", "// @flow\nimport warnOnce from './warn-once';\nimport Node from './node';\n\n/**\n * Represents a comment between declarations or statements (rule and at-rules).\n *\n * Comments inside selectors, at-rule parameters, or declaration values\n * will be stored in the `raws` properties explained above.\n *\n * @extends Node\n */\nclass Comment extends Node {\n  constructor(defaults) {\n    super(defaults);\n    this.type = 'comment';\n  }\n\n  get left() {\n    warnOnce('Comment#left was deprecated. Use Comment#raws.left');\n    return this.raws.left;\n  }\n\n  set left(val) {\n    warnOnce('Comment#left was deprecated. Use Comment#raws.left');\n    this.raws.left = val;\n  }\n\n  get right() {\n    warnOnce('Comment#right was deprecated. Use Comment#raws.right');\n    return this.raws.right;\n  }\n\n  set right(val) {\n    warnOnce('Comment#right was deprecated. Use Comment#raws.right');\n    this.raws.right = val;\n  }\n\n  /**\n   * @memberof Comment#\n   * @member {string} text - the comment’s text\n   */\n\n  /**\n   * @memberof Comment#\n   * @member {object} raws - Information to generate byte-to-byte equal\n   *                         node string as it was in the origin input.\n   *\n   * Every parser saves its own properties,\n   * but the default CSS parser uses:\n   *\n   * * `before`: the space symbols before the node.\n   * * `left`: the space symbols between `/*` and the comment’s text.\n   * * `right`: the space symbols between the comment’s text.\n   */\n}\n\nexport default Comment;\n", "// @flow\nimport Declaration from './declaration';\nimport tokenizer from './tokenize';\nimport Comment from './comment';\nimport AtRule from './at-rule';\nimport Root from './root';\nimport Rule from './rule';\n\nexport default class Parser {\n  constructor(input) {\n    this.input = input;\n\n    this.pos = 0;\n    this.root = new Root();\n    this.current = this.root;\n    this.spaces = '';\n    this.semicolon = false;\n\n    this.root.source = { input, start: { line: 1, column: 1 } };\n  }\n\n  tokenize() {\n    this.tokens = tokenizer(this.input);\n  }\n\n  loop() {\n    let token;\n    while (this.pos < this.tokens.length) {\n      token = this.tokens[this.pos];\n\n      switch (token[0]) {\n        case 'space':\n        case ';':\n          this.spaces += token[1];\n          break;\n\n        case '}':\n          this.end(token);\n          break;\n\n        case 'comment':\n          this.comment(token);\n          break;\n\n        case 'at-word':\n          this.atrule(token);\n          break;\n\n        case '{':\n          this.emptyRule(token);\n          break;\n\n        default:\n          this.other();\n          break;\n      }\n\n      this.pos += 1;\n    }\n    this.endFile();\n  }\n\n  comment(token) {\n    const node = new Comment();\n    this.init(node, token[2], token[3]);\n    node.source.end = { line: token[4], column: token[5] };\n\n    const text = token[1].slice(2, -2);\n    if (/^\\s*$/.test(text)) {\n      node.text = '';\n      node.raws.left = text;\n      node.raws.right = '';\n    } else {\n      const match = text.match(/^(\\s*)([^]*[^\\s])(\\s*)$/);\n      node.text = match[2];\n      node.raws.left = match[1];\n      node.raws.right = match[3];\n    }\n  }\n\n  emptyRule(token) {\n    const node = new Rule();\n    this.init(node, token[2], token[3]);\n    node.selector = '';\n    node.raws.between = '';\n    this.current = node;\n  }\n\n  other() {\n    let token;\n    let end = false;\n    let type = null;\n    let colon = false;\n    let bracket = null;\n    const brackets = [];\n\n    const start = this.pos;\n    while (this.pos < this.tokens.length) {\n      token = this.tokens[this.pos];\n      type = token[0];\n\n      if (type === '(' || type === '[') {\n        if (!bracket) bracket = token;\n        brackets.push(type === '(' ? ')' : ']');\n      } else if (brackets.length === 0) {\n        if (type === ';') {\n          if (colon) {\n            this.decl(this.tokens.slice(start, this.pos + 1));\n            return;\n          } else {\n            break;\n          }\n        } else if (type === '{') {\n          this.rule(this.tokens.slice(start, this.pos + 1));\n          return;\n        } else if (type === '}') {\n          this.pos -= 1;\n          end = true;\n          break;\n        } else if (type === ':') {\n          colon = true;\n        }\n      } else if (type === brackets[brackets.length - 1]) {\n        brackets.pop();\n        if (brackets.length === 0) bracket = null;\n      }\n\n      this.pos += 1;\n    }\n    if (this.pos === this.tokens.length) {\n      this.pos -= 1;\n      end = true;\n    }\n\n    if (brackets.length > 0) this.unclosedBracket(bracket);\n\n    if (end && colon) {\n      while (this.pos > start) {\n        token = this.tokens[this.pos][0];\n        if (token !== 'space' && token !== 'comment') break;\n        this.pos -= 1;\n      }\n      this.decl(this.tokens.slice(start, this.pos + 1));\n      return;\n    }\n\n    this.unknownWord(start);\n  }\n\n  rule(tokens) {\n    tokens.pop();\n\n    const node = new Rule();\n    this.init(node, tokens[0][2], tokens[0][3]);\n\n    node.raws.between = this.spacesFromEnd(tokens);\n    this.raw(node, 'selector', tokens);\n    this.current = node;\n  }\n\n  decl(tokens) {\n    const node = new Declaration();\n    this.init(node);\n\n    const last = tokens[tokens.length - 1];\n    if (last[0] === ';') {\n      this.semicolon = true;\n      tokens.pop();\n    }\n    if (last[4]) {\n      node.source.end = { line: last[4], column: last[5] };\n    } else {\n      node.source.end = { line: last[2], column: last[3] };\n    }\n\n    while (tokens[0][0] !== 'word') {\n      node.raws.before += tokens.shift()[1];\n    }\n    node.source.start = { line: tokens[0][2], column: tokens[0][3] };\n\n    node.prop = '';\n    while (tokens.length) {\n      const type = tokens[0][0];\n      if (type === ':' || type === 'space' || type === 'comment') {\n        break;\n      }\n      node.prop += tokens.shift()[1];\n    }\n\n    node.raws.between = '';\n\n    let token;\n    while (tokens.length) {\n      token = tokens.shift();\n\n      if (token[0] === ':') {\n        node.raws.between += token[1];\n        break;\n      } else {\n        node.raws.between += token[1];\n      }\n    }\n\n    if (node.prop[0] === '_' || node.prop[0] === '*') {\n      node.raws.before += node.prop[0];\n      node.prop = node.prop.slice(1);\n    }\n    node.raws.between += this.spacesFromStart(tokens);\n    this.precheckMissedSemicolon(tokens);\n\n    for (let i = tokens.length - 1; i > 0; i--) {\n      token = tokens[i];\n      if (token[1] === '!important') {\n        node.important = true;\n        let string = this.stringFrom(tokens, i);\n        string = this.spacesFromEnd(tokens) + string;\n        if (string !== ' !important') node.raws.important = string;\n        break;\n      } else if (token[1] === 'important') {\n        const cache = tokens.slice(0);\n        let str = '';\n        for (let j = i; j > 0; j--) {\n          const type = cache[j][0];\n          if (str.trim().indexOf('!') === 0 && type !== 'space') {\n            break;\n          }\n          str = cache.pop()[1] + str;\n        }\n        if (str.trim().indexOf('!') === 0) {\n          node.important = true;\n          node.raws.important = str;\n          tokens = cache;\n        }\n      }\n\n      if (token[0] !== 'space' && token[0] !== 'comment') {\n        break;\n      }\n    }\n\n    this.raw(node, 'value', tokens);\n\n    if (node.value.indexOf(':') !== -1) this.checkMissedSemicolon(tokens);\n  }\n\n  atrule(token) {\n    const node = new AtRule();\n    node.name = token[1].slice(1);\n    if (node.name === '') {\n      this.unnamedAtrule(node, token);\n    }\n    this.init(node, token[2], token[3]);\n\n    let last = false;\n    let open = false;\n    const params = [];\n\n    this.pos += 1;\n    while (this.pos < this.tokens.length) {\n      token = this.tokens[this.pos];\n\n      if (token[0] === ';') {\n        node.source.end = { line: token[2], column: token[3] };\n        this.semicolon = true;\n        break;\n      } else if (token[0] === '{') {\n        open = true;\n        break;\n      } else if (token[0] === '}') {\n        this.end(token);\n        break;\n      } else {\n        params.push(token);\n      }\n\n      this.pos += 1;\n    }\n    if (this.pos === this.tokens.length) {\n      last = true;\n    }\n\n    node.raws.between = this.spacesFromEnd(params);\n    if (params.length) {\n      node.raws.afterName = this.spacesFromStart(params);\n      this.raw(node, 'params', params);\n      if (last) {\n        token = params[params.length - 1];\n        node.source.end = { line: token[4], column: token[5] };\n        this.spaces = node.raws.between;\n        node.raws.between = '';\n      }\n    } else {\n      node.raws.afterName = '';\n      node.params = '';\n    }\n\n    if (open) {\n      node.nodes = [];\n      this.current = node;\n    }\n  }\n\n  end(token) {\n    if (this.current.nodes && this.current.nodes.length) {\n      this.current.raws.semicolon = this.semicolon;\n    }\n    this.semicolon = false;\n\n    this.current.raws.after = (this.current.raws.after || '') + this.spaces;\n    this.spaces = '';\n\n    if (this.current.parent) {\n      this.current.source.end = { line: token[2], column: token[3] };\n      this.current = this.current.parent;\n    } else {\n      this.unexpectedClose(token);\n    }\n  }\n\n  endFile() {\n    if (this.current.parent) this.unclosedBlock();\n    if (this.current.nodes && this.current.nodes.length) {\n      this.current.raws.semicolon = this.semicolon;\n    }\n    this.current.raws.after = (this.current.raws.after || '') + this.spaces;\n  }\n\n  // Helpers\n\n  init(node, line, column) {\n    this.current.push(node);\n\n    node.source = { start: { line, column }, input: this.input };\n    node.raws.before = this.spaces;\n    this.spaces = '';\n    if (node.type !== 'comment') this.semicolon = false;\n  }\n\n  raw(node, prop, tokens) {\n    let token, type;\n    const length = tokens.length;\n    let value = '';\n    let clean = true;\n    for (let i = 0; i < length; i += 1) {\n      token = tokens[i];\n      type = token[0];\n      if (type === 'comment' || (type === 'space' && i === length - 1)) {\n        clean = false;\n      } else {\n        value += token[1];\n      }\n    }\n    if (!clean) {\n      const raw = tokens.reduce((all, i) => all + i[1], '');\n      node.raws[prop] = { value, raw };\n    }\n    node[prop] = value;\n  }\n\n  spacesFromEnd(tokens) {\n    let lastTokenType;\n    let spaces = '';\n    while (tokens.length) {\n      lastTokenType = tokens[tokens.length - 1][0];\n      if (lastTokenType !== 'space' && lastTokenType !== 'comment') break;\n      spaces = tokens.pop()[1] + spaces;\n    }\n    return spaces;\n  }\n\n  spacesFromStart(tokens) {\n    let next;\n    let spaces = '';\n    while (tokens.length) {\n      next = tokens[0][0];\n      if (next !== 'space' && next !== 'comment') break;\n      spaces += tokens.shift()[1];\n    }\n    return spaces;\n  }\n\n  stringFrom(tokens, from) {\n    let result = '';\n    for (let i = from; i < tokens.length; i++) {\n      result += tokens[i][1];\n    }\n    tokens.splice(from, tokens.length - from);\n    return result;\n  }\n\n  colon(tokens) {\n    let brackets = 0;\n    let token, type, prev;\n    for (let i = 0; i < tokens.length; i++) {\n      token = tokens[i];\n      type = token[0];\n\n      if (type === '(') {\n        brackets += 1;\n      } else if (type === ')') {\n        brackets -= 1;\n      } else if (brackets === 0 && type === ':') {\n        if (!prev) {\n          this.doubleColon(token);\n        } else if (prev[0] === 'word' && prev[1] === 'progid') {\n          continue;\n        } else {\n          return i;\n        }\n      }\n\n      prev = token;\n    }\n    return false;\n  }\n\n  // Errors\n\n  unclosedBracket(bracket) {\n    throw this.input.error('Unclosed bracket', bracket[2], bracket[3]);\n  }\n\n  unknownWord(start) {\n    const token = this.tokens[start];\n    throw this.input.error('Unknown word', token[2], token[3]);\n  }\n\n  unexpectedClose(token) {\n    throw this.input.error('Unexpected }', token[2], token[3]);\n  }\n\n  unclosedBlock() {\n    const pos = this.current.source.start;\n    throw this.input.error('Unclosed block', pos.line, pos.column);\n  }\n\n  doubleColon(token) {\n    throw this.input.error('Double colon', token[2], token[3]);\n  }\n\n  unnamedAtrule(node, token) {\n    throw this.input.error('At-rule without name', token[2], token[3]);\n  }\n\n  precheckMissedSemicolon(tokens) {\n    // Hook for Safe Parser\n    tokens;\n  }\n\n  checkMissedSemicolon(tokens) {\n    const colon = this.colon(tokens);\n    if (colon === false) return;\n\n    let founded = 0;\n    let token;\n    for (let j = colon - 1; j >= 0; j--) {\n      token = tokens[j];\n      if (token[0] !== 'space') {\n        founded += 1;\n        if (founded === 2) break;\n      }\n    }\n    throw this.input.error('Missed semicolon', token[2], token[3]);\n  }\n}\n", "// @flow\nimport Parser from './parser';\nimport Input from './input';\n\nexport default function parse(css, opts) {\n  if (opts && opts.safe) {\n    throw new Error('Option safe was removed. ' + 'Use parser: require(\"postcss-safe-parser\")');\n  }\n\n  const input = new Input(css, opts);\n\n  const parser = new Parser(input);\n  try {\n    parser.tokenize();\n    parser.loop();\n  } catch (e) {\n    if (e.name === 'CssSyntaxError' && opts && opts.from) {\n      if (/\\.scss$/i.test(opts.from)) {\n        e.message +=\n          '\\nYou tried to parse SCSS with ' +\n          'the standard CSS parser; ' +\n          'try again with the postcss-scss parser';\n      } else if (/\\.less$/i.test(opts.from)) {\n        e.message +=\n          '\\nYou tried to parse Less with ' +\n          'the standard CSS parser; ' +\n          'try again with the postcss-less parser';\n      }\n    }\n    throw e;\n  }\n\n  return parser.root;\n}\n", "// @flow\nimport parse from './parse';\nimport Root from './root';\nimport Rule from './rule';\nimport AtRule from './at-rule';\nimport Declaration from './declaration';\nimport warnOnce from './warn-once';\nimport Comment from './comment';\nimport Node from './node';\n\nfunction cleanSource(nodes) {\n  return nodes.map(i => {\n    if (i.nodes) i.nodes = cleanSource(i.nodes);\n    delete i.source;\n    return i;\n  });\n}\n\n/**\n * @callback childCondition\n * @param {Node} node    - container child\n * @param {number} index - child index\n * @param {Node[]} nodes - all container children\n * @return {boolean}\n */\n\n/**\n * @callback childIterator\n * @param {Node} node    - container child\n * @param {number} index - child index\n * @return {false|undefined} returning `false` will break iteration\n */\n\n/**\n * The {@link Root}, {@link AtRule}, and {@link Rule} container nodes\n * inherit some common methods to help work with their children.\n *\n * Note that all containers can store any content. If you write a rule inside\n * a rule, PostCSS will parse it.\n *\n * @extends Node\n * @abstract\n */\nclass Container extends Node {\n  push(child) {\n    child.parent = this;\n    this.nodes.push(child);\n    return this;\n  }\n\n  /**\n   * Iterates through the container’s immediate children,\n   * calling `callback` for each child.\n   *\n   * Returning `false` in the callback will break iteration.\n   *\n   * This method only iterates through the container’s immediate children.\n   * If you need to recursively iterate through all the container’s descendant\n   * nodes, use {@link Container#walk}.\n   *\n   * Unlike the for `{}`-cycle or `Array#forEach` this iterator is safe\n   * if you are mutating the array of child nodes during iteration.\n   * PostCSS will adjust the current index to match the mutations.\n   *\n   * @param {childIterator} callback - iterator receives each node and index\n   *\n   * @return {false|undefined} returns `false` if iteration was broke\n   *\n   * @example\n   * const root = postcss.parse('a { color: black; z-index: 1 }');\n   * const rule = root.first;\n   *\n   * for ( let decl of rule.nodes ) {\n   *     decl.cloneBefore({ prop: '-webkit-' + decl.prop });\n   *     // Cycle will be infinite, because cloneBefore moves the current node\n   *     // to the next index\n   * }\n   *\n   * rule.each(decl => {\n   *     decl.cloneBefore({ prop: '-webkit-' + decl.prop });\n   *     // Will be executed only for color and z-index\n   * });\n   */\n  each(callback) {\n    if (!this.lastEach) this.lastEach = 0;\n    if (!this.indexes) this.indexes = {};\n\n    this.lastEach += 1;\n    const id = this.lastEach;\n    this.indexes[id] = 0;\n\n    if (!this.nodes) return undefined;\n\n    let index, result;\n    while (this.indexes[id] < this.nodes.length) {\n      index = this.indexes[id];\n      result = callback(this.nodes[index], index);\n      if (result === false) break;\n\n      this.indexes[id] += 1;\n    }\n\n    delete this.indexes[id];\n\n    return result;\n  }\n\n  /**\n   * Traverses the container’s descendant nodes, calling callback\n   * for each node.\n   *\n   * Like container.each(), this method is safe to use\n   * if you are mutating arrays during iteration.\n   *\n   * If you only need to iterate through the container’s immediate children,\n   * use {@link Container#each}.\n   *\n   * @param {childIterator} callback - iterator receives each node and index\n   *\n   * @return {false|undefined} returns `false` if iteration was broke\n   *\n   * @example\n   * root.walk(node => {\n   *   // Traverses all descendant nodes.\n   * });\n   */\n  walk(callback) {\n    return this.each((child, i) => {\n      let result = callback(child, i);\n      if (result !== false && child.walk) {\n        result = child.walk(callback);\n      }\n      return result;\n    });\n  }\n\n  /**\n   * Traverses the container’s descendant nodes, calling callback\n   * for each declaration node.\n   *\n   * If you pass a filter, iteration will only happen over declarations\n   * with matching properties.\n   *\n   * Like {@link Container#each}, this method is safe\n   * to use if you are mutating arrays during iteration.\n   *\n   * @param {string|RegExp} [prop]   - string or regular expression\n   *                                   to filter declarations by property name\n   * @param {childIterator} callback - iterator receives each node and index\n   *\n   * @return {false|undefined} returns `false` if iteration was broke\n   *\n   * @example\n   * root.walkDecls(decl => {\n   *   checkPropertySupport(decl.prop);\n   * });\n   *\n   * root.walkDecls('border-radius', decl => {\n   *   decl.remove();\n   * });\n   *\n   * root.walkDecls(/^background/, decl => {\n   *   decl.value = takeFirstColorFromGradient(decl.value);\n   * });\n   */\n  walkDecls(prop, callback) {\n    if (!callback) {\n      callback = prop;\n      return this.walk((child, i) => {\n        if (child.type === 'decl') {\n          return callback(child, i);\n        }\n      });\n    } else if (prop instanceof RegExp) {\n      return this.walk((child, i) => {\n        if (child.type === 'decl' && prop.test(child.prop)) {\n          return callback(child, i);\n        }\n      });\n    } else {\n      return this.walk((child, i) => {\n        if (child.type === 'decl' && child.prop === prop) {\n          return callback(child, i);\n        }\n      });\n    }\n  }\n\n  /**\n   * Traverses the container’s descendant nodes, calling callback\n   * for each rule node.\n   *\n   * If you pass a filter, iteration will only happen over rules\n   * with matching selectors.\n   *\n   * Like {@link Container#each}, this method is safe\n   * to use if you are mutating arrays during iteration.\n   *\n   * @param {string|RegExp} [selector] - string or regular expression\n   *                                     to filter rules by selector\n   * @param {childIterator} callback   - iterator receives each node and index\n   *\n   * @return {false|undefined} returns `false` if iteration was broke\n   *\n   * @example\n   * const selectors = [];\n   * root.walkRules(rule => {\n   *   selectors.push(rule.selector);\n   * });\n   * console.log(`Your CSS uses ${selectors.length} selectors`);\n   */\n  walkRules(selector, callback) {\n    if (!callback) {\n      callback = selector;\n\n      return this.walk((child, i) => {\n        if (child.type === 'rule') {\n          return callback(child, i);\n        }\n      });\n    } else if (selector instanceof RegExp) {\n      return this.walk((child, i) => {\n        if (child.type === 'rule' && selector.test(child.selector)) {\n          return callback(child, i);\n        }\n      });\n    } else {\n      return this.walk((child, i) => {\n        if (child.type === 'rule' && child.selector === selector) {\n          return callback(child, i);\n        }\n      });\n    }\n  }\n\n  /**\n   * Traverses the container’s descendant nodes, calling callback\n   * for each at-rule node.\n   *\n   * If you pass a filter, iteration will only happen over at-rules\n   * that have matching names.\n   *\n   * Like {@link Container#each}, this method is safe\n   * to use if you are mutating arrays during iteration.\n   *\n   * @param {string|RegExp} [name]   - string or regular expression\n   *                                   to filter at-rules by name\n   * @param {childIterator} callback - iterator receives each node and index\n   *\n   * @return {false|undefined} returns `false` if iteration was broke\n   *\n   * @example\n   * root.walkAtRules(rule => {\n   *   if ( isOld(rule.name) ) rule.remove();\n   * });\n   *\n   * let first = false;\n   * root.walkAtRules('charset', rule => {\n   *   if ( !first ) {\n   *     first = true;\n   *   } else {\n   *     rule.remove();\n   *   }\n   * });\n   */\n  walkAtRules(name, callback) {\n    if (!callback) {\n      callback = name;\n      return this.walk((child, i) => {\n        if (child.type === 'atrule') {\n          return callback(child, i);\n        }\n      });\n    } else if (name instanceof RegExp) {\n      return this.walk((child, i) => {\n        if (child.type === 'atrule' && name.test(child.name)) {\n          return callback(child, i);\n        }\n      });\n    } else {\n      return this.walk((child, i) => {\n        if (child.type === 'atrule' && child.name === name) {\n          return callback(child, i);\n        }\n      });\n    }\n  }\n\n  /**\n   * Traverses the container’s descendant nodes, calling callback\n   * for each comment node.\n   *\n   * Like {@link Container#each}, this method is safe\n   * to use if you are mutating arrays during iteration.\n   *\n   * @param {childIterator} callback - iterator receives each node and index\n   *\n   * @return {false|undefined} returns `false` if iteration was broke\n   *\n   * @example\n   * root.walkComments(comment => {\n   *   comment.remove();\n   * });\n   */\n  walkComments(callback) {\n    return this.walk((child, i) => {\n      if (child.type === 'comment') {\n        return callback(child, i);\n      }\n    });\n  }\n\n  /**\n   * Inserts new nodes to the start of the container.\n   *\n   * @param {...(Node|object|string|Node[])} children - new nodes\n   *\n   * @return {Node} this node for methods chain\n   *\n   * @example\n   * const decl1 = postcss.decl({ prop: 'color', value: 'black' });\n   * const decl2 = postcss.decl({ prop: 'background-color', value: 'white' });\n   * rule.append(decl1, decl2);\n   *\n   * root.append({ name: 'charset', params: '\"UTF-8\"' });  // at-rule\n   * root.append({ selector: 'a' });                       // rule\n   * rule.append({ prop: 'color', value: 'black' });       // declaration\n   * rule.append({ text: 'Comment' })                      // comment\n   *\n   * root.append('a {}');\n   * root.first.append('color: black; z-index: 1');\n   */\n  append(...children) {\n    children.forEach(child => {\n      const nodes = this.normalize(child, this.last);\n      nodes.forEach(node => this.nodes.push(node));\n    });\n    return this;\n  }\n\n  /**\n   * Inserts new nodes to the end of the container.\n   *\n   * @param {...(Node|object|string|Node[])} children - new nodes\n   *\n   * @return {Node} this node for methods chain\n   *\n   * @example\n   * const decl1 = postcss.decl({ prop: 'color', value: 'black' });\n   * const decl2 = postcss.decl({ prop: 'background-color', value: 'white' });\n   * rule.prepend(decl1, decl2);\n   *\n   * root.append({ name: 'charset', params: '\"UTF-8\"' });  // at-rule\n   * root.append({ selector: 'a' });                       // rule\n   * rule.append({ prop: 'color', value: 'black' });       // declaration\n   * rule.append({ text: 'Comment' })                      // comment\n   *\n   * root.append('a {}');\n   * root.first.append('color: black; z-index: 1');\n   */\n  prepend(...children) {\n    children = children.reverse();\n    children.forEach(child => {\n      const nodes = this.normalize(child, this.first, 'prepend').reverse();\n      nodes.forEach(node => this.nodes.unshift(node));\n      for (const id in this.indexes) {\n        this.indexes[id] = this.indexes[id] + nodes.length;\n      }\n    });\n    return this;\n  }\n\n  cleanRaws(keepBetween) {\n    super.cleanRaws(keepBetween);\n    if (this.nodes) {\n      this.nodes.forEach(node => node.cleanRaws(keepBetween));\n    }\n  }\n\n  /**\n   * Insert new node before old node within the container.\n   *\n   * @param {Node|number} exist             - child or child’s index.\n   * @param {Node|object|string|Node[]} add - new node\n   *\n   * @return {Node} this node for methods chain\n   *\n   * @example\n   * rule.insertBefore(decl, decl.clone({ prop: '-webkit-' + decl.prop }));\n   */\n  insertBefore(exist, add) {\n    exist = this.index(exist);\n\n    const type = exist === 0 ? 'prepend' : false;\n    const nodes = this.normalize(add, this.nodes[exist], type).reverse();\n    nodes.forEach(node => this.nodes.splice(exist, 0, node));\n\n    let index;\n    for (const id in this.indexes) {\n      index = this.indexes[id];\n      if (exist <= index) {\n        this.indexes[id] = index + nodes.length;\n      }\n    }\n\n    return this;\n  }\n\n  /**\n   * Insert new node after old node within the container.\n   *\n   * @param {Node|number} exist             - child or child’s index\n   * @param {Node|object|string|Node[]} add - new node\n   *\n   * @return {Node} this node for methods chain\n   */\n  insertAfter(exist, add) {\n    exist = this.index(exist);\n\n    const nodes = this.normalize(add, this.nodes[exist]).reverse();\n    nodes.forEach(node => this.nodes.splice(exist + 1, 0, node));\n\n    let index;\n    for (const id in this.indexes) {\n      index = this.indexes[id];\n      if (exist < index) {\n        this.indexes[id] = index + nodes.length;\n      }\n    }\n\n    return this;\n  }\n\n  remove(child) {\n    if (typeof child !== 'undefined') {\n      warnOnce('Container#remove is deprecated. ' + 'Use Container#removeChild');\n      this.removeChild(child);\n    } else {\n      super.remove();\n    }\n    return this;\n  }\n\n  /**\n   * Removes node from the container and cleans the parent properties\n   * from the node and its children.\n   *\n   * @param {Node|number} child - child or child’s index\n   *\n   * @return {Node} this node for methods chain\n   *\n   * @example\n   * rule.nodes.length  //=> 5\n   * rule.removeChild(decl);\n   * rule.nodes.length  //=> 4\n   * decl.parent        //=> undefined\n   */\n  removeChild(child) {\n    child = this.index(child);\n    this.nodes[child].parent = undefined;\n    this.nodes.splice(child, 1);\n\n    let index;\n    for (const id in this.indexes) {\n      index = this.indexes[id];\n      if (index >= child) {\n        this.indexes[id] = index - 1;\n      }\n    }\n\n    return this;\n  }\n\n  /**\n   * Removes all children from the container\n   * and cleans their parent properties.\n   *\n   * @return {Node} this node for methods chain\n   *\n   * @example\n   * rule.removeAll();\n   * rule.nodes.length //=> 0\n   */\n  removeAll() {\n    this.nodes.forEach(node => (node.parent = undefined));\n    this.nodes = [];\n    return this;\n  }\n\n  /**\n     * Passes all declaration values within the container that match pattern\n     * through callback, replacing those values with the returned result\n     * of callback.\n     *\n     * This method is useful if you are using a custom unit or function\n     * and need to iterate through all values.\n     *\n     * @param {string|RegExp} pattern      - replace pattern\n     * @param {object} opts                - options to speed up the search\n     * @param {string|string[]} opts.props - an array of property names\n     * @param {string} opts.fast           - string that’s used\n     *                                       to narrow down values and speed up\n                                             the regexp search\n     * @param {function|string} callback   - string to replace pattern\n     *                                       or callback that returns a new\n     *                                       value.\n     *                                       The callback will receive\n     *                                       the same arguments as those\n     *                                       passed to a function parameter\n     *                                       of `String#replace`.\n     *\n     * @return {Node} this node for methods chain\n     *\n     * @example\n     * root.replaceValues(/\\d+rem/, { fast: 'rem' }, string => {\n     *   return 15 * parseInt(string) + 'px';\n     * });\n     */\n  replaceValues(pattern, opts, callback) {\n    if (!callback) {\n      callback = opts;\n      opts = {};\n    }\n\n    this.walkDecls(decl => {\n      if (opts.props && opts.props.indexOf(decl.prop) === -1) return;\n      if (opts.fast && decl.value.indexOf(opts.fast) === -1) return;\n\n      decl.value = decl.value.replace(pattern, callback);\n    });\n\n    return this;\n  }\n\n  /**\n   * Returns `true` if callback returns `true`\n   * for all of the container’s children.\n   *\n   * @param {childCondition} condition - iterator returns true or false.\n   *\n   * @return {boolean} is every child pass condition\n   *\n   * @example\n   * const noPrefixes = rule.every(i => i.prop[0] !== '-');\n   */\n  every(condition) {\n    return this.nodes.every(condition);\n  }\n\n  /**\n   * Returns `true` if callback returns `true` for (at least) one\n   * of the container’s children.\n   *\n   * @param {childCondition} condition - iterator returns true or false.\n   *\n   * @return {boolean} is some child pass condition\n   *\n   * @example\n   * const hasPrefix = rule.some(i => i.prop[0] === '-');\n   */\n  some(condition) {\n    return this.nodes.some(condition);\n  }\n\n  /**\n   * Returns a `child`’s index within the {@link Container#nodes} array.\n   *\n   * @param {Node} child - child of the current container.\n   *\n   * @return {number} child index\n   *\n   * @example\n   * rule.index( rule.nodes[2] ) //=> 2\n   */\n  index(child) {\n    if (typeof child === 'number') {\n      return child;\n    } else {\n      return this.nodes.indexOf(child);\n    }\n  }\n\n  /**\n   * The container’s first child.\n   *\n   * @type {Node}\n   *\n   * @example\n   * rule.first == rules.nodes[0];\n   */\n  get first() {\n    if (!this.nodes) return undefined;\n    return this.nodes[0];\n  }\n\n  /**\n   * The container’s last child.\n   *\n   * @type {Node}\n   *\n   * @example\n   * rule.last == rule.nodes[rule.nodes.length - 1];\n   */\n  get last() {\n    if (!this.nodes) return undefined;\n    return this.nodes[this.nodes.length - 1];\n  }\n\n  normalize(nodes, sample) {\n    if (typeof nodes === 'string') {\n      nodes = cleanSource(parse(nodes).nodes);\n    } else if (!Array.isArray(nodes)) {\n      if (nodes.type === 'root') {\n        nodes = nodes.nodes;\n      } else if (nodes.type) {\n        nodes = [nodes];\n      } else if (nodes.prop) {\n        if (typeof nodes.value === 'undefined') {\n          throw new Error('Value field is missed in node creation');\n        } else if (typeof nodes.value !== 'string') {\n          nodes.value = String(nodes.value);\n        }\n        nodes = [new Declaration(nodes)];\n      } else if (nodes.selector) {\n        nodes = [new Rule(nodes)];\n      } else if (nodes.name) {\n        nodes = [new AtRule(nodes)];\n      } else if (nodes.text) {\n        nodes = [new Comment(nodes)];\n      } else {\n        throw new Error('Unknown node type in node creation');\n      }\n    }\n\n    const processed = nodes.map(i => {\n      if (typeof i.raws === 'undefined') i = this.rebuild(i);\n\n      if (i.parent) i = i.clone();\n      if (typeof i.raws.before === 'undefined') {\n        if (sample && typeof sample.raws.before !== 'undefined') {\n          i.raws.before = sample.raws.before.replace(/[^\\s]/g, '');\n        }\n      }\n      i.parent = this;\n      return i;\n    });\n\n    return processed;\n  }\n\n  rebuild(node, parent) {\n    let fix;\n    if (node.type === 'root') {\n      fix = new Root();\n    } else if (node.type === 'atrule') {\n      fix = new AtRule();\n    } else if (node.type === 'rule') {\n      fix = new Rule();\n    } else if (node.type === 'decl') {\n      fix = new Declaration();\n    } else if (node.type === 'comment') {\n      fix = new Comment();\n    }\n\n    for (const i in node) {\n      if (i === 'nodes') {\n        fix.nodes = node.nodes.map(j => this.rebuild(j, fix));\n      } else if (i === 'parent' && parent) {\n        fix.parent = parent;\n      } else if (node.hasOwnProperty(i)) {\n        fix[i] = node[i];\n      }\n    }\n\n    return fix;\n  }\n\n  eachInside(callback) {\n    warnOnce('Container#eachInside is deprecated. ' + 'Use Container#walk instead.');\n    return this.walk(callback);\n  }\n\n  eachDecl(prop, callback) {\n    warnOnce('Container#eachDecl is deprecated. ' + 'Use Container#walkDecls instead.');\n    return this.walkDecls(prop, callback);\n  }\n\n  eachRule(selector, callback) {\n    warnOnce('Container#eachRule is deprecated. ' + 'Use Container#walkRules instead.');\n    return this.walkRules(selector, callback);\n  }\n\n  eachAtRule(name, callback) {\n    warnOnce('Container#eachAtRule is deprecated. ' + 'Use Container#walkAtRules instead.');\n    return this.walkAtRules(name, callback);\n  }\n\n  eachComment(callback) {\n    warnOnce('Container#eachComment is deprecated. ' + 'Use Container#walkComments instead.');\n    return this.walkComments(callback);\n  }\n\n  get semicolon() {\n    warnOnce('Node#semicolon is deprecated. Use Node#raws.semicolon');\n    return this.raws.semicolon;\n  }\n\n  set semicolon(val) {\n    warnOnce('Node#semicolon is deprecated. Use Node#raws.semicolon');\n    this.raws.semicolon = val;\n  }\n\n  get after() {\n    warnOnce('Node#after is deprecated. Use Node#raws.after');\n    return this.raws.after;\n  }\n\n  set after(val) {\n    warnOnce('Node#after is deprecated. Use Node#raws.after');\n    this.raws.after = val;\n  }\n\n  /**\n   * @memberof Container#\n   * @member {Node[]} nodes - an array containing the container’s children\n   *\n   * @example\n   * const root = postcss.parse('a { color: black }');\n   * root.nodes.length           //=> 1\n   * root.nodes[0].selector      //=> 'a'\n   * root.nodes[0].nodes[0].prop //=> 'color'\n   */\n}\n\nexport default Container;\n", "// @flow\nimport Container from './container';\nimport warnOnce from './warn-once';\n\n/**\n * Represents an at-rule.\n *\n * If it’s followed in the CSS by a {} block, this node will have\n * a nodes property representing its children.\n *\n * @extends Container\n *\n * @example\n * const root = postcss.parse('@charset \"UTF-8\"; @media print {}');\n *\n * const charset = root.first;\n * charset.type  //=> 'atrule'\n * charset.nodes //=> undefined\n *\n * const media = root.last;\n * media.nodes   //=> []\n */\nclass AtRule extends Container {\n  constructor(defaults) {\n    super(defaults);\n    this.type = 'atrule';\n  }\n\n  append(...children) {\n    if (!this.nodes) this.nodes = [];\n    return super.append(...children);\n  }\n\n  prepend(...children) {\n    if (!this.nodes) this.nodes = [];\n    return super.prepend(...children);\n  }\n\n  get afterName() {\n    warnOnce('AtRule#afterName was deprecated. Use AtRule#raws.afterName');\n    return this.raws.afterName;\n  }\n\n  set afterName(val) {\n    warnOnce('AtRule#afterName was deprecated. Use AtRule#raws.afterName');\n    this.raws.afterName = val;\n  }\n\n  get _params() {\n    warnOnce('AtRule#_params was deprecated. Use AtRule#raws.params');\n    return this.raws.params;\n  }\n\n  set _params(val) {\n    warnOnce('AtRule#_params was deprecated. Use AtRule#raws.params');\n    this.raws.params = val;\n  }\n\n  /**\n   * @memberof AtRule#\n   * @member {string} name - the at-rule’s name immediately follows the `@`\n   *\n   * @example\n   * const root  = postcss.parse('@media print {}');\n   * media.name //=> 'media'\n   * const media = root.first;\n   */\n\n  /**\n   * @memberof AtRule#\n   * @member {string} params - the at-rule’s parameters, the values\n   *                           that follow the at-rule’s name but precede\n   *                           any {} block\n   *\n   * @example\n   * const root  = postcss.parse('@media print, screen {}');\n   * const media = root.first;\n   * media.params //=> 'print, screen'\n   */\n\n  /**\n   * @memberof AtRule#\n   * @member {object} raws - Information to generate byte-to-byte equal\n   *                         node string as it was in the origin input.\n   *\n   * Every parser saves its own properties,\n   * but the default CSS parser uses:\n   *\n   * * `before`: the space symbols before the node. It also stores `*`\n   *   and `_` symbols before the declaration (IE hack).\n   * * `after`: the space symbols after the last child of the node\n   *   to the end of the node.\n   * * `between`: the symbols between the property and value\n   *   for declarations, selector and `{` for rules, or last parameter\n   *   and `{` for at-rules.\n   * * `semicolon`: contains true if the last child has\n   *   an (optional) semicolon.\n   * * `afterName`: the space between the at-rule name and its parameters.\n   *\n   * PostCSS cleans at-rule parameters from comments and extra spaces,\n   * but it stores origin content in raws properties.\n   * As such, if you don’t change a declaration’s value,\n   * PostCSS will use the raw value with comments.\n   *\n   * @example\n   * const root = postcss.parse('  @media\\nprint {\\n}')\n   * root.first.first.raws //=> { before: '  ',\n   *                       //     between: ' ',\n   *                       //     afterName: '\\n',\n   *                       //     after: '\\n' }\n   */\n}\n\nexport default AtRule;\n", "// @flow\n/**\n * Contains helpers for safely splitting lists of CSS values,\n * preserving parentheses and quotes.\n *\n * @example\n * const list = postcss.list;\n *\n * @namespace list\n */\nconst list = {\n  split(string, separators, last) {\n    const array = [];\n    let current = '';\n    let split = false;\n\n    let func = 0;\n    let quote = false;\n    let escape = false;\n\n    for (let i = 0; i < string.length; i++) {\n      const letter = string[i];\n\n      if (quote) {\n        if (escape) {\n          escape = false;\n        } else if (letter === '\\\\') {\n          escape = true;\n        } else if (letter === quote) {\n          quote = false;\n        }\n      } else if (letter === '\"' || letter === \"'\") {\n        quote = letter;\n      } else if (letter === '(') {\n        func += 1;\n      } else if (letter === ')') {\n        if (func > 0) func -= 1;\n      } else if (func === 0) {\n        if (separators.indexOf(letter) !== -1) split = true;\n      }\n\n      if (split) {\n        if (current !== '') array.push(current.trim());\n        current = '';\n        split = false;\n      } else {\n        current += letter;\n      }\n    }\n\n    if (last || current !== '') array.push(current.trim());\n    return array;\n  },\n\n  /**\n   * Safely splits space-separated values (such as those for `background`,\n   * `border-radius`, and other shorthand properties).\n   *\n   * @param {string} string - space-separated values\n   *\n   * @return {string[]} splitted values\n   *\n   * @example\n   * postcss.list.space('1px calc(10% + 1px)') //=> ['1px', 'calc(10% + 1px)']\n   */\n  space(string) {\n    const spaces = [' ', '\\n', '\\t'];\n    return list.split(string, spaces);\n  },\n\n  /**\n   * Safely splits comma-separated values (such as those for `transition-*`\n   * and `background` properties).\n   *\n   * @param {string} string - comma-separated values\n   *\n   * @return {string[]} splitted values\n   *\n   * @example\n   * postcss.list.comma('black, linear-gradient(white, black)')\n   * //=> ['black', 'linear-gradient(white, black)']\n   */\n  comma(string) {\n    const comma = ',';\n    return list.split(string, [comma], true);\n  },\n};\n\nexport default list;\n", "// @flow\nimport './at-rule'; // break cyclical dependency deadlock – #87\n\nimport Container from './container';\nimport warnOnce from './warn-once';\nimport list from './list';\n\n/**\n * Represents a CSS rule: a selector followed by a declaration block.\n *\n * @extends Container\n *\n * @example\n * const root = postcss.parse('a{}');\n * const rule = root.first;\n * rule.type       //=> 'rule'\n * rule.toString() //=> 'a{}'\n */\nclass Rule extends Container {\n  constructor(defaults) {\n    super(defaults);\n    this.type = 'rule';\n    if (!this.nodes) this.nodes = [];\n  }\n\n  /**\n   * An array containing the rule’s individual selectors.\n   * Groups of selectors are split at commas.\n   *\n   * @type {string[]}\n   *\n   * @example\n   * const root = postcss.parse('a, b { }');\n   * const rule = root.first;\n   *\n   * rule.selector  //=> 'a, b'\n   * rule.selectors //=> ['a', 'b']\n   *\n   * rule.selectors = ['a', 'strong'];\n   * rule.selector //=> 'a, strong'\n   */\n  get selectors() {\n    return list.comma(this.selector);\n  }\n\n  set selectors(values) {\n    const match = this.selector ? this.selector.match(/,\\s*/) : null;\n    const sep = match ? match[0] : `,${this.raw('between', 'beforeOpen')}`;\n    this.selector = values.join(sep);\n  }\n\n  get _selector() {\n    warnOnce('Rule#_selector is deprecated. Use Rule#raws.selector');\n    return this.raws.selector;\n  }\n\n  set _selector(val) {\n    warnOnce('Rule#_selector is deprecated. Use Rule#raws.selector');\n    this.raws.selector = val;\n  }\n\n  /**\n   * @memberof Rule#\n   * @member {string} selector - the rule’s full selector represented\n   *                             as a string\n   *\n   * @example\n   * const root = postcss.parse('a, b { }');\n   * const rule = root.first;\n   * rule.selector //=> 'a, b'\n   */\n\n  /**\n   * @memberof Rule#\n   * @member {object} raws - Information to generate byte-to-byte equal\n   *                         node string as it was in the origin input.\n   *\n   * Every parser saves its own properties,\n   * but the default CSS parser uses:\n   *\n   * * `before`: the space symbols before the node. It also stores `*`\n   *   and `_` symbols before the declaration (IE hack).\n   * * `after`: the space symbols after the last child of the node\n   *   to the end of the node.\n   * * `between`: the symbols between the property and value\n   *   for declarations, selector and `{` for rules, or last parameter\n   *   and `{` for at-rules.\n   * * `semicolon`: contains true if the last child has\n   *   an (optional) semicolon.\n   *\n   * PostCSS cleans selectors from comments and extra spaces,\n   * but it stores origin content in raws properties.\n   * As such, if you don’t change a declaration’s value,\n   * PostCSS will use the raw value with comments.\n   *\n   * @example\n   * const root = postcss.parse('a {\\n  color:black\\n}')\n   * root.first.first.raws //=> { before: '', between: ' ', after: '\\n' }\n   */\n}\n\nexport default Rule;\n", "// @flow\n/**\n * Represents a plugin’s warning. It can be created using {@link Node#warn}.\n *\n * @example\n * if ( decl.important ) {\n *     decl.warn(result, 'Avoid !important', { word: '!important' });\n * }\n */\nclass Warning {\n  /**\n   * @param {string} text        - warning message\n   * @param {Object} [opts]      - warning options\n   * @param {Node}   opts.node   - CSS node that caused the warning\n   * @param {string} opts.word   - word in CSS source that caused the warning\n   * @param {number} opts.index  - index in CSS node string that caused\n   *                               the warning\n   * @param {string} opts.plugin - name of the plugin that created\n   *                               this warning. {@link Result#warn} fills\n   *                               this property automatically.\n   */\n  constructor(text, opts = {}) {\n    /**\n     * @member {string} - Type to filter warnings from\n     *                    {@link Result#messages}. Always equal\n     *                    to `\"warning\"`.\n     *\n     * @example\n     * const nonWarning = result.messages.filter(i => i.type !== 'warning')\n     */\n    this.type = 'warning';\n    /**\n     * @member {string} - The warning message.\n     *\n     * @example\n     * warning.text //=> 'Try to avoid !important'\n     */\n    this.text = text;\n\n    if (opts.node && opts.node.source) {\n      const pos = opts.node.positionBy(opts);\n      /**\n       * @member {number} - Line in the input file\n       *                    with this warning’s source\n       *\n       * @example\n       * warning.line //=> 5\n       */\n      this.line = pos.line;\n      /**\n       * @member {number} - Column in the input file\n       *                    with this warning’s source.\n       *\n       * @example\n       * warning.column //=> 6\n       */\n      this.column = pos.column;\n    }\n\n    for (const opt in opts) this[opt] = opts[opt];\n  }\n\n  /**\n   * Returns a warning position and message.\n   *\n   * @example\n   * warning.toString() //=> 'postcss-lint:a.css:10:14: Avoid !important'\n   *\n   * @return {string} warning position and message\n   */\n  toString() {\n    if (this.node) {\n      return this.node.error(this.text, {\n        plugin: this.plugin,\n        index: this.index,\n        word: this.word,\n      }).message;\n    } else if (this.plugin) {\n      return `${this.plugin}: ${this.text}`;\n    } else {\n      return this.text;\n    }\n  }\n\n  /**\n   * @memberof Warning#\n   * @member {string} plugin - The name of the plugin that created\n   *                           it will fill this property automatically.\n   *                           this warning. When you call {@link Node#warn}\n   *\n   * @example\n   * warning.plugin //=> 'postcss-important'\n   */\n\n  /**\n   * @memberof Warning#\n   * @member {Node} node - Contains the CSS node that caused the warning.\n   *\n   * @example\n   * warning.node.toString() //=> 'color: white !important'\n   */\n}\n\nexport default Warning;\n", "// @flow\nimport Warning from './warning';\n\n/**\n * @typedef  {object} Message\n * @property {string} type   - message type\n * @property {string} plugin - source PostCSS plugin name\n */\n\n/**\n * Provides the result of the PostCSS transformations.\n *\n * A Result instance is returned by {@link LazyResult#then}\n * or {@link Root#toResult} methods.\n *\n * @example\n * postcss([cssnext]).process(css).then(function (result) {\n *    console.log(result.css);\n * });\n *\n * @example\n * var result2 = postcss.parse(css).toResult();\n */\nclass Result {\n  /**\n   * @param {Processor} processor - processor used for this transformation.\n   * @param {Root}      root      - Root node after all transformations.\n   * @param {processOptions} opts - options from the {@link Processor#process}\n   *                                or {@link Root#toResult}\n   */\n  constructor(processor, root, opts) {\n    /**\n     * @member {Processor} - The Processor instance used\n     *                       for this transformation.\n     *\n     * @example\n     * for ( let plugin of result.processor.plugins) {\n     *   if ( plugin.postcssPlugin === 'postcss-bad' ) {\n     *     throw 'postcss-good is incompatible with postcss-bad';\n     *   }\n     * });\n     */\n    this.processor = processor;\n    /**\n     * @member {Message[]} - Contains messages from plugins\n     *                       (e.g., warnings or custom messages).\n     *                       Each message should have type\n     *                       and plugin properties.\n     *\n     * @example\n     * postcss.plugin('postcss-min-browser', () => {\n     *   return (root, result) => {\n     *     var browsers = detectMinBrowsersByCanIUse(root);\n     *     result.messages.push({\n     *       type:    'min-browser',\n     *       plugin:  'postcss-min-browser',\n     *       browsers: browsers\n     *     });\n     *   };\n     * });\n     */\n    this.messages = [];\n    /**\n     * @member {Root} - Root node after all transformations.\n     *\n     * @example\n     * root.toResult().root == root;\n     */\n    this.root = root;\n    /**\n     * @member {processOptions} - Options from the {@link Processor#process}\n     *                            or {@link Root#toResult} call\n     *                            that produced this Result instance.\n     *\n     * @example\n     * root.toResult(opts).opts == opts;\n     */\n    this.opts = opts;\n    /**\n     * @member {string} - A CSS string representing of {@link Result#root}.\n     *\n     * @example\n     * postcss.parse('a{}').toResult().css //=> \"a{}\"\n     */\n    this.css = undefined;\n    /**\n     * @member {SourceMapGenerator} - An instance of `SourceMapGenerator`\n     *                                class from the `source-map` library,\n     *                                representing changes\n     *                                to the {@link Result#root} instance.\n     *\n     * @example\n     * result.map.toJSON() //=> { version: 3, file: 'a.css', … }\n     *\n     * @example\n     * if ( result.map ) {\n     *   fs.writeFileSync(result.opts.to + '.map', result.map.toString());\n     * }\n     */\n    this.map = undefined;\n  }\n\n  /**\n   * Returns for @{link Result#css} content.\n   *\n   * @example\n   * result + '' === result.css\n   *\n   * @return {string} string representing of {@link Result#root}\n   */\n  toString() {\n    return this.css;\n  }\n\n  /**\n   * Creates an instance of {@link Warning} and adds it\n   * to {@link Result#messages}.\n   *\n   * @param {string} text        - warning message\n   * @param {Object} [opts]      - warning options\n   * @param {Node}   opts.node   - CSS node that caused the warning\n   * @param {string} opts.word   - word in CSS source that caused the warning\n   * @param {number} opts.index  - index in CSS node string that caused\n   *                               the warning\n   * @param {string} opts.plugin - name of the plugin that created\n   *                               this warning. {@link Result#warn} fills\n   *                               this property automatically.\n   *\n   * @return {Warning} created warning\n   */\n  warn(text, opts = {}) {\n    if (!opts.plugin) {\n      if (this.lastPlugin && this.lastPlugin.postcssPlugin) {\n        opts.plugin = this.lastPlugin.postcssPlugin;\n      }\n    }\n\n    const warning = new Warning(text, opts);\n    this.messages.push(warning);\n\n    return warning;\n  }\n\n  /**\n   * Returns warnings from plugins. Filters {@link Warning} instances\n   * from {@link Result#messages}.\n   *\n   * @example\n   * result.warnings().forEach(warn => {\n   *   console.warn(warn.toString());\n   * });\n   *\n   * @return {Warning[]} warnings from plugins\n   */\n  warnings() {\n    return this.messages.filter(i => i.type === 'warning');\n  }\n\n  /**\n   * An alias for the {@link Result#css} property.\n   * Use it with syntaxes that generate non-CSS output.\n   * @type {string}\n   *\n   * @example\n   * result.css === result.content;\n   */\n  get content() {\n    return this.css;\n  }\n}\n\nexport default Result;\n", "// @flow\nimport stringify from './stringify';\nimport warnOnce from './warn-once';\nimport Result from './result';\nimport parse from './parse';\n\nfunction isPromise(obj) {\n  return typeof obj === 'object' && typeof obj.then === 'function';\n}\n\n/**\n * @callback onFulfilled\n * @param {Result} result\n */\n\n/**\n * @callback onRejected\n * @param {Error} error\n */\n\n/**\n * A Promise proxy for the result of PostCSS transformations.\n *\n * A `LazyResult` instance is returned by {@link Processor#process}.\n *\n * @example\n * const lazy = postcss([cssnext]).process(css);\n */\nclass LazyResult {\n  constructor(processor, css, opts) {\n    this.stringified = false;\n    this.processed = false;\n\n    let root;\n    if (typeof css === 'object' && css.type === 'root') {\n      root = css;\n    } else if (css instanceof LazyResult || css instanceof Result) {\n      root = css.root;\n      if (css.map) {\n        if (typeof opts.map === 'undefined') opts.map = {};\n        if (!opts.map.inline) opts.map.inline = false;\n        opts.map.prev = css.map;\n      }\n    } else {\n      let parser = parse;\n      if (opts.syntax) parser = opts.syntax.parse;\n      if (opts.parser) parser = opts.parser;\n      if (parser.parse) parser = parser.parse;\n\n      try {\n        root = parser(css, opts);\n      } catch (error) {\n        this.error = error;\n      }\n    }\n\n    this.result = new Result(processor, root, opts);\n  }\n\n  /**\n   * Returns a {@link Processor} instance, which will be used\n   * for CSS transformations.\n   * @type {Processor}\n   */\n  get processor() {\n    return this.result.processor;\n  }\n\n  /**\n   * Options from the {@link Processor#process} call.\n   * @type {processOptions}\n   */\n  get opts() {\n    return this.result.opts;\n  }\n\n  /**\n   * Processes input CSS through synchronous plugins, converts `Root`\n   * to a CSS string and returns {@link Result#css}.\n   *\n   * This property will only work with synchronous plugins.\n   * If the processor contains any asynchronous plugins\n   * it will throw an error. This is why this method is only\n   * for debug purpose, you should always use {@link LazyResult#then}.\n   *\n   * @type {string}\n   * @see Result#css\n   */\n  get css() {\n    return this.stringify().css;\n  }\n\n  /**\n   * An alias for the `css` property. Use it with syntaxes\n   * that generate non-CSS output.\n   *\n   * This property will only work with synchronous plugins.\n   * If the processor contains any asynchronous plugins\n   * it will throw an error. This is why this method is only\n   * for debug purpose, you should always use {@link LazyResult#then}.\n   *\n   * @type {string}\n   * @see Result#content\n   */\n  get content() {\n    return this.stringify().content;\n  }\n\n  /**\n   * Processes input CSS through synchronous plugins\n   * and returns {@link Result#map}.\n   *\n   * This property will only work with synchronous plugins.\n   * If the processor contains any asynchronous plugins\n   * it will throw an error. This is why this method is only\n   * for debug purpose, you should always use {@link LazyResult#then}.\n   *\n   * @type {SourceMapGenerator}\n   * @see Result#map\n   */\n  get map() {\n    return this.stringify().map;\n  }\n\n  /**\n   * Processes input CSS through synchronous plugins\n   * and returns {@link Result#root}.\n   *\n   * This property will only work with synchronous plugins. If the processor\n   * contains any asynchronous plugins it will throw an error.\n   *\n   * This is why this method is only for debug purpose,\n   * you should always use {@link LazyResult#then}.\n   *\n   * @type {Root}\n   * @see Result#root\n   */\n  get root() {\n    return this.sync().root;\n  }\n\n  /**\n   * Processes input CSS through synchronous plugins\n   * and returns {@link Result#messages}.\n   *\n   * This property will only work with synchronous plugins. If the processor\n   * contains any asynchronous plugins it will throw an error.\n   *\n   * This is why this method is only for debug purpose,\n   * you should always use {@link LazyResult#then}.\n   *\n   * @type {Message[]}\n   * @see Result#messages\n   */\n  get messages() {\n    return this.sync().messages;\n  }\n\n  /**\n   * Processes input CSS through synchronous plugins\n   * and calls {@link Result#warnings()}.\n   *\n   * @return {Warning[]} warnings from plugins\n   */\n  warnings() {\n    return this.sync().warnings();\n  }\n\n  /**\n   * Alias for the {@link LazyResult#css} property.\n   *\n   * @example\n   * lazy + '' === lazy.css;\n   *\n   * @return {string} output CSS\n   */\n  toString() {\n    return this.css;\n  }\n\n  /**\n   * Processes input CSS through synchronous and asynchronous plugins\n   * and calls `onFulfilled` with a Result instance. If a plugin throws\n   * an error, the `onRejected` callback will be executed.\n   *\n   * It implements standard Promise API.\n   *\n   * @param {onFulfilled} onFulfilled - callback will be executed\n   *                                    when all plugins will finish work\n   * @param {onRejected}  onRejected  - callback will be execited on any error\n   *\n   * @return {Promise} Promise API to make queue\n   *\n   * @example\n   * postcss([cssnext]).process(css).then(result => {\n   *   console.log(result.css);\n   * });\n   */\n  then(onFulfilled, onRejected) {\n    return this.async().then(onFulfilled, onRejected);\n  }\n\n  /**\n   * Processes input CSS through synchronous and asynchronous plugins\n   * and calls onRejected for each error thrown in any plugin.\n   *\n   * It implements standard Promise API.\n   *\n   * @param {onRejected} onRejected - callback will be execited on any error\n   *\n   * @return {Promise} Promise API to make queue\n   *\n   * @example\n   * postcss([cssnext]).process(css).then(result => {\n   *   console.log(result.css);\n   * }).catch(error => {\n   *   console.error(error);\n   * });\n   */\n  catch(onRejected) {\n    return this.async().catch(onRejected);\n  }\n\n  handleError(error, plugin) {\n    try {\n      this.error = error;\n      if (error.name === 'CssSyntaxError' && !error.plugin) {\n        error.plugin = plugin.postcssPlugin;\n        error.setMessage();\n      } else if (plugin.postcssVersion) {\n        const pluginName = plugin.postcssPlugin;\n        const pluginVer = plugin.postcssVersion;\n        const runtimeVer = this.result.processor.version;\n        const a = pluginVer.split('.');\n        const b = runtimeVer.split('.');\n\n        if (a[0] !== b[0] || parseInt(a[1]) > parseInt(b[1])) {\n          warnOnce(\n            `${'Your current PostCSS version ' + 'is '}${runtimeVer}, but ${pluginName} ` +\n              `uses ${pluginVer}. Perhaps this is ` +\n              `the source of the error below.`\n          );\n        }\n      }\n    } catch (err) {\n      if (console && console.error) console.error(err);\n    }\n  }\n\n  asyncTick(resolve, reject) {\n    if (this.plugin >= this.processor.plugins.length) {\n      this.processed = true;\n      return resolve();\n    }\n\n    try {\n      const plugin = this.processor.plugins[this.plugin];\n      const promise = this.run(plugin);\n      this.plugin += 1;\n\n      if (isPromise(promise)) {\n        promise\n          .then(() => {\n            this.asyncTick(resolve, reject);\n          })\n          .catch(error => {\n            this.handleError(error, plugin);\n            this.processed = true;\n            reject(error);\n          });\n      } else {\n        this.asyncTick(resolve, reject);\n      }\n    } catch (error) {\n      this.processed = true;\n      reject(error);\n    }\n  }\n\n  async() {\n    if (this.processed) {\n      return new Promise((resolve, reject) => {\n        if (this.error) {\n          reject(this.error);\n        } else {\n          resolve(this.stringify());\n        }\n      });\n    }\n    if (this.processing) {\n      return this.processing;\n    }\n\n    this.processing = new Promise((resolve, reject) => {\n      if (this.error) return reject(this.error);\n      this.plugin = 0;\n      this.asyncTick(resolve, reject);\n    }).then(() => {\n      this.processed = true;\n      return this.stringify();\n    });\n\n    return this.processing;\n  }\n\n  sync() {\n    if (this.processed) return this.result;\n    this.processed = true;\n\n    if (this.processing) {\n      throw new Error('Use process(css).then(cb) to work with async plugins');\n    }\n\n    if (this.error) throw this.error;\n\n    this.result.processor.plugins.forEach(plugin => {\n      const promise = this.run(plugin);\n      if (isPromise(promise)) {\n        throw new Error('Use process(css).then(cb) to work with async plugins');\n      }\n    });\n\n    return this.result;\n  }\n\n  run(plugin) {\n    this.result.lastPlugin = plugin;\n\n    try {\n      return plugin(this.result.root, this.result);\n    } catch (error) {\n      this.handleError(error, plugin);\n      throw error;\n    }\n  }\n\n  stringify() {\n    if (this.stringified) return this.result;\n    this.stringified = true;\n\n    this.sync();\n\n    const opts = this.result.opts;\n    let str = stringify;\n    if (opts.syntax) str = opts.syntax.stringify;\n    if (opts.stringifier) str = opts.stringifier;\n    if (str.stringify) str = str.stringify;\n\n    let result = '';\n    str(this.root, i => {\n      result += i;\n    });\n    this.result.css = result;\n\n    return this.result;\n  }\n}\n\nexport default LazyResult;\n", "// @flow\nimport <PERSON><PERSON><PERSON><PERSON><PERSON> from './lazy-result';\n\n/**\n * @callback builder\n * @param {string} part          - part of generated CSS connected to this node\n * @param {Node}   node          - AST node\n * @param {\"start\"|\"end\"} [type] - node’s part type\n */\n\n/**\n * @callback parser\n *\n * @param {string|toString} css   - string with input CSS or any object\n *                                  with toString() method, like a Buffer\n * @param {processOptions} [opts] - options with only `from` and `map` keys\n *\n * @return {Root} PostCSS AST\n */\n\n/**\n * @callback stringifier\n *\n * @param {Node} node       - start node for stringifing. Usually {@link Root}.\n * @param {builder} builder - function to concatenate CSS from node’s parts\n *                            or generate string and source map\n *\n * @return {void}\n */\n\n/**\n * @typedef {object} syntax\n * @property {parser} parse          - function to generate AST by string\n * @property {stringifier} stringify - function to generate string by AST\n */\n\n/**\n * @typedef {object} toString\n * @property {function} toString\n */\n\n/**\n * @callback pluginFunction\n * @param {Root} root     - parsed input CSS\n * @param {Result} result - result to set warnings or check other plugins\n */\n\n/**\n * @typedef {object} Plugin\n * @property {function} postcss - PostCSS plugin function\n */\n\n/**\n * @typedef {object} processOptions\n * @property {string} from             - the path of the CSS source file.\n *                                       You should always set `from`,\n *                                       because it is used in source map\n *                                       generation and syntax error messages.\n * @property {string} to               - the path where you’ll put the output\n *                                       CSS file. You should always set `to`\n *                                       to generate correct source maps.\n * @property {parser} parser           - function to generate AST by string\n * @property {stringifier} stringifier - class to generate string by AST\n * @property {syntax} syntax           - object with `parse` and `stringify`\n * @property {object} map              - source map options\n * @property {boolean} map.inline                    - does source map should\n *                                                     be embedded in the output\n *                                                     CSS as a base64-encoded\n *                                                     comment\n * @property {string|object|false|function} map.prev - source map content\n *                                                     from a previous\n *                                                     processing step\n *                                                     (for example, Sass).\n *                                                     PostCSS will try to find\n *                                                     previous map\n *                                                     automatically, so you\n *                                                     could disable it by\n *                                                     `false` value.\n * @property {boolean} map.sourcesContent            - does PostCSS should set\n *                                                     the origin content to map\n * @property {string|false} map.annotation           - does PostCSS should set\n *                                                     annotation comment to map\n * @property {string} map.from                       - override `from` in map’s\n *                                                     `sources`\n */\n\n/**\n * Contains plugins to process CSS. Create one `Processor` instance,\n * initialize its plugins, and then use that instance on numerous CSS files.\n *\n * @example\n * const processor = postcss([autoprefixer, precss]);\n * processor.process(css1).then(result => console.log(result.css));\n * processor.process(css2).then(result => console.log(result.css));\n */\nclass Processor {\n  /**\n   * @param {Array.<Plugin|pluginFunction>|Processor} plugins - PostCSS\n   *        plugins. See {@link Processor#use} for plugin format.\n   */\n  constructor(plugins = []) {\n    /**\n     * @member {string} - Current PostCSS version.\n     *\n     * @example\n     * if ( result.processor.version.split('.')[0] !== '5' ) {\n     *   throw new Error('This plugin works only with PostCSS 5');\n     * }\n     */\n    this.version = '5.2.0';\n    /**\n     * @member {pluginFunction[]} - Plugins added to this processor.\n     *\n     * @example\n     * const processor = postcss([autoprefixer, precss]);\n     * processor.plugins.length //=> 2\n     */\n    this.plugins = this.normalize(plugins);\n  }\n\n  /**\n   * Adds a plugin to be used as a CSS processor.\n   *\n   * PostCSS plugin can be in 4 formats:\n   * * A plugin created by {@link postcss.plugin} method.\n   * * A function. PostCSS will pass the function a @{link Root}\n   *   as the first argument and current {@link Result} instance\n   *   as the second.\n   * * An object with a `postcss` method. PostCSS will use that method\n   *   as described in #2.\n   * * Another {@link Processor} instance. PostCSS will copy plugins\n   *   from that instance into this one.\n   *\n   * Plugins can also be added by passing them as arguments when creating\n   * a `postcss` instance (see [`postcss(plugins)`]).\n   *\n   * Asynchronous plugins should return a `Promise` instance.\n   *\n   * @param {Plugin|pluginFunction|Processor} plugin - PostCSS plugin\n   *                                                   or {@link Processor}\n   *                                                   with plugins\n   *\n   * @example\n   * const processor = postcss()\n   *   .use(autoprefixer)\n   *   .use(precss);\n   *\n   * @return {Processes} current processor to make methods chain\n   */\n  use(plugin) {\n    this.plugins = this.plugins.concat(this.normalize([plugin]));\n    return this;\n  }\n\n  /**\n   * Parses source CSS and returns a {@link LazyResult} Promise proxy.\n   * Because some plugins can be asynchronous it doesn’t make\n   * any transformations. Transformations will be applied\n   * in the {@link LazyResult} methods.\n   *\n   * @param {string|toString|Result} css - String with input CSS or\n   *                                       any object with a `toString()`\n   *                                       method, like a Buffer.\n   *                                       Optionally, send a {@link Result}\n   *                                       instance and the processor will\n   *                                       take the {@link Root} from it.\n   * @param {processOptions} [opts]      - options\n   *\n   * @return {LazyResult} Promise proxy\n   *\n   * @example\n   * processor.process(css, { from: 'a.css', to: 'a.out.css' })\n   *   .then(result => {\n   *      console.log(result.css);\n   *   });\n   */\n  process(css, opts = {}) {\n    return new LazyResult(this, css, opts);\n  }\n\n  normalize(plugins) {\n    let normalized = [];\n    plugins.forEach(i => {\n      if (i.postcss) i = i.postcss;\n\n      if (typeof i === 'object' && Array.isArray(i.plugins)) {\n        normalized = normalized.concat(i.plugins);\n      } else if (typeof i === 'function') {\n        normalized.push(i);\n      } else {\n        throw new Error(`${i} is not a PostCSS plugin`);\n      }\n    });\n    return normalized;\n  }\n}\n\nexport default Processor;\n", "// @flow\nimport './rule'; // break cyclical dependency deadlock – #87\n\nimport Container from './container';\nimport Lazy<PERSON><PERSON>ult from './lazy-result';\nimport Processor from './processor';\nimport warnOnce from './warn-once';\n\n/**\n * Represents a CSS file and contains all its parsed nodes.\n *\n * @extends Container\n *\n * @example\n * const root = postcss.parse('a{color:black} b{z-index:2}');\n * root.type         //=> 'root'\n * root.nodes.length //=> 2\n */\nclass Root extends Container {\n  constructor(defaults) {\n    super(defaults);\n    this.type = 'root';\n    if (!this.nodes) this.nodes = [];\n  }\n\n  removeChild(child) {\n    child = this.index(child);\n\n    if (child === 0 && this.nodes.length > 1) {\n      this.nodes[1].raws.before = this.nodes[child].raws.before;\n    }\n\n    return super.removeChild(child);\n  }\n\n  normalize(child, sample, type) {\n    const nodes = super.normalize(child);\n\n    if (sample) {\n      if (type === 'prepend') {\n        if (this.nodes.length > 1) {\n          sample.raws.before = this.nodes[1].raws.before;\n        } else {\n          delete sample.raws.before;\n        }\n      } else if (this.first !== sample) {\n        nodes.forEach(node => {\n          node.raws.before = sample.raws.before;\n        });\n      }\n    }\n\n    return nodes;\n  }\n\n  /**\n   * Returns a {@link Result} instance representing the root’s CSS.\n   *\n   * @param {processOptions} [opts] - options with only `to` and `map` keys\n   *\n   * @return {Result} result with current root’s CSS\n   *\n   * @example\n   * const root1 = postcss.parse(css1, { from: 'a.css' });\n   * const root2 = postcss.parse(css2, { from: 'b.css' });\n   * root1.append(root2);\n   * const result = root1.toResult({ to: 'all.css', map: true });\n   */\n  toResult(opts = {}) {\n    const lazy = new LazyResult(new Processor(), this, opts);\n    return lazy.stringify();\n  }\n\n  remove(child) {\n    warnOnce('Root#remove is deprecated. Use Root#removeChild');\n    this.removeChild(child);\n  }\n\n  prevMap() {\n    warnOnce('Root#prevMap is deprecated. Use Root#source.input.map');\n    return this.source.input.map;\n  }\n\n  /**\n   * @memberof Root#\n   * @member {object} raws - Information to generate byte-to-byte equal\n   *                         node string as it was in the origin input.\n   *\n   * Every parser saves its own properties,\n   * but the default CSS parser uses:\n   *\n   * * `after`: the space symbols after the last child to the end of file.\n   * * `semicolon`: is the last child has an (optional) semicolon.\n   *\n   * @example\n   * postcss.parse('a {}\\n').raws //=> { after: '\\n' }\n   * postcss.parse('a {}').raws   //=> { after: '' }\n   */\n}\n\nexport default Root;\n", "// @flow\nimport './root'; // break cyclical dependency deadlock – #87\n\nimport CssSyntaxError from './css-syntax-error';\n// import PreviousMap    from './previous-map';\n\nlet sequence = 0;\n\n/**\n * @typedef  {object} filePosition\n * @property {string} file   - path to file\n * @property {number} line   - source line in file\n * @property {number} column - source column in file\n */\n\n/**\n * Represents the source CSS.\n *\n * @example\n * const root  = postcss.parse(css, { from: file });\n * const input = root.source.input;\n */\nclass Input {\n  /**\n   * @param {string} css    - input CSS source\n   * @param {object} [opts] - {@link Processor#process} options\n   */\n  constructor(css, opts = {}) {\n    /**\n     * @member {string} - input CSS source\n     *\n     * @example\n     * const input = postcss.parse('a{}', { from: file }).input;\n     * input.css //=> \"a{}\";\n     */\n    this.css = css.toString();\n\n    if (this.css[0] === '\\uFEFF' || this.css[0] === '\\uFFFE') {\n      this.css = this.css.slice(1);\n    }\n\n    if (opts.from) {\n      if (/^\\w+:\\/\\//.test(opts.from)) {\n        /**\n         * @member {string} - The absolute path to the CSS source file\n         *                    defined with the `from` option.\n         *\n         * @example\n         * const root = postcss.parse(css, { from: 'a.css' });\n         * root.source.input.file //=> '/home/<USER>/a.css'\n         */\n        this.file = opts.from;\n      } else {\n        this.file = path.resolve(opts.from);\n      }\n    }\n\n    /*\n        let map = new PreviousMap(this.css, opts);\n        if ( map.text ) {\n            /!**\n             * @member {PreviousMap} - The input source map passed from\n             *                         a compilation step before PostCSS\n             *                         (for example, from Sass compiler).\n             *\n             * @example\n             * root.source.input.map.consumer().sources //=> ['a.sass']\n             *!/\n            this.map = map;\n            let file = map.consumer().file;\n            if ( !this.file && file ) this.file = this.mapResolve(file);\n        }\n*/\n\n    if (!this.file) {\n      sequence += 1;\n      /**\n       * @member {string} - The unique ID of the CSS source. It will be\n       *                    created if `from` option is not provided\n       *                    (because PostCSS does not know the file path).\n       *\n       * @example\n       * const root = postcss.parse(css);\n       * root.source.input.file //=> undefined\n       * root.source.input.id   //=> \"<input css 1>\"\n       */\n      this.id = `<input css ${sequence}>`;\n    }\n    if (this.map) this.map.file = this.from;\n  }\n\n  error(message, line, column, opts = {}) {\n    let result;\n    const origin = this.origin(line, column);\n    if (origin) {\n      result = new CssSyntaxError(\n        message,\n        origin.line,\n        origin.column,\n        origin.source,\n        origin.file,\n        opts.plugin\n      );\n    } else {\n      result = new CssSyntaxError(message, line, column, this.css, this.file, opts.plugin);\n    }\n\n    result.input = { line, column, source: this.css };\n    if (this.file) result.input.file = this.file;\n\n    return result;\n  }\n\n  /**\n   * Reads the input source map and returns a symbol position\n   * in the input source (e.g., in a Sass file that was compiled\n   * to CSS before being passed to PostCSS).\n   *\n   * @param {number} line   - line in input CSS\n   * @param {number} column - column in input CSS\n   *\n   * @return {filePosition} position in input source\n   *\n   * @example\n   * root.source.input.origin(1, 1) //=> { file: 'a.css', line: 3, column: 1 }\n   */\n  origin(line, column) {\n    if (!this.map) return false;\n    const consumer = this.map.consumer();\n\n    const from = consumer.originalPositionFor({ line, column });\n    if (!from.source) return false;\n\n    const result = {\n      file: this.mapResolve(from.source),\n      line: from.line,\n      column: from.column,\n    };\n\n    const source = consumer.sourceContentFor(from.source);\n    if (source) result.source = source;\n\n    return result;\n  }\n\n  mapResolve(file) {\n    if (/^\\w+:\\/\\//.test(file)) {\n      return file;\n    } else {\n      return path.resolve(this.map.consumer().sourceRoot || '.', file);\n    }\n  }\n\n  /**\n   * The CSS source identifier. Contains {@link Input#file} if the user\n   * set the `from` option, or {@link Input#id} if they did not.\n   * @type {string}\n   *\n   * @example\n   * const root = postcss.parse(css, { from: 'a.css' });\n   * root.source.input.from //=> \"/home/<USER>/a.css\"\n   *\n   * const root = postcss.parse(css);\n   * root.source.input.from //=> \"<input css 1>\"\n   */\n  get from() {\n    return this.file || this.id;\n  }\n}\n\nexport default Input;\n", "// @flow\nimport tokenize from '../postcss/tokenize';\nimport Comment from '../postcss/comment';\nimport Parser from '../postcss/parser';\n\nexport default class SafeParser extends Parser {\n  tokenize() {\n    this.tokens = tokenize(this.input, { ignoreErrors: true });\n  }\n\n  comment(token) {\n    const node = new Comment();\n    this.init(node, token[2], token[3]);\n    node.source.end = { line: token[4], column: token[5] };\n\n    let text = token[1].slice(2);\n    if (text.slice(-2) === '*/') text = text.slice(0, -2);\n\n    if (/^\\s*$/.test(text)) {\n      node.text = '';\n      node.raws.left = text;\n      node.raws.right = '';\n    } else {\n      const match = text.match(/^(\\s*)([^]*[^\\s])(\\s*)$/);\n      node.text = match[2];\n      node.raws.left = match[1];\n      node.raws.right = match[3];\n    }\n  }\n\n  unclosedBracket() {}\n\n  unknownWord(start) {\n    const buffer = this.tokens.slice(start, this.pos + 1);\n    this.spaces += buffer.map(i => i[1]).join('');\n  }\n\n  unexpectedClose() {\n    this.current.raws.after += '}';\n  }\n\n  doubleColon() {}\n\n  unnamedAtrule(node) {\n    node.name = '';\n  }\n\n  precheckMissedSemicolon(tokens) {\n    const colon = this.colon(tokens);\n    if (colon === false) return;\n\n    let split;\n    for (split = colon - 1; split >= 0; split--) {\n      if (tokens[split][0] === 'word') break;\n    }\n    for (split -= 1; split >= 0; split--) {\n      if (tokens[split][0] !== 'space') {\n        split += 1;\n        break;\n      }\n    }\n    const other = tokens.splice(split, tokens.length - split);\n    this.decl(other);\n  }\n\n  checkMissedSemicolon() {}\n\n  endFile() {\n    if (this.current.nodes && this.current.nodes.length) {\n      this.current.raws.semicolon = this.semicolon;\n    }\n    this.current.raws.after = (this.current.raws.after || '') + this.spaces;\n\n    while (this.current.parent) {\n      this.current = this.current.parent;\n      this.current.raws.after = '';\n    }\n  }\n}\n", "// @flow\nimport Input from '../postcss/input';\n\nimport SafeParser from './safe-parser';\n\nexport default function safeParse(css, opts) {\n  const input = new Input(css, opts);\n\n  const parser = new SafeParser(input);\n  parser.tokenize();\n  parser.loop();\n\n  return parser.root;\n}\n", "// @flow\n/* eslint-disable import/no-unresolved */\nimport transformDeclPairs from 'css-to-react-native';\n\nimport generateComponentId from '../utils/generateComponentId';\nimport type { RuleSet, StyleSheet } from '../types';\nimport flatten from '../utils/flatten';\n// $FlowFixMe\nimport parse from '../vendor/postcss-safe-parser/parse';\n\nlet generated = {};\n\nexport const resetStyleCache = () => {\n  generated = {};\n};\n\n/*\n InlineStyle takes arbitrary CSS and generates a flat object\n */\nexport default (styleSheet: StyleSheet) => {\n  class InlineStyle {\n    rules: RuleSet;\n\n    constructor(rules: RuleSet) {\n      this.rules = rules;\n    }\n\n    generateStyleObject(executionContext: Object) {\n      const flatCSS = flatten(this.rules, executionContext).join('');\n\n      const hash = generateComponentId(flatCSS);\n      if (!generated[hash]) {\n        const root = parse(flatCSS);\n        const declPairs = [];\n        root.each(node => {\n          if (node.type === 'decl') {\n            declPairs.push([node.prop, node.value]);\n          } else if (process.env.NODE_ENV !== 'production' && node.type !== 'comment') {\n            /* eslint-disable no-console */\n            console.warn(`Node of type ${node.type} not supported as an inline style`);\n          }\n        });\n        // RN currently does not support differing values for the corner radii of Image\n        // components (but does for View). It is almost impossible to tell whether we'll have\n        // support, so we'll just disable multiple values here.\n        // https://github.com/styled-components/css-to-react-native/issues/11\n        const styleObject = transformDeclPairs(declPairs, [\n          'borderRadius',\n          'borderWidth',\n          'borderColor',\n          'borderStyle',\n        ]);\n        const styles = styleSheet.create({\n          generated: styleObject,\n        });\n        generated[hash] = styles.generated;\n      }\n      return generated[hash];\n    }\n  }\n\n  return InlineStyle;\n};\n", "/* eslint-disable */\n/**\n  mixin-deep; https://github.com/jonschlinkert/mixin-deep\n  Inlined such that it will be consistently transpiled to an IE-compatible syntax.\n\n  The MIT License (MIT)\n\n  Copyright (c) 2014-present, <PERSON>.\n\n  Permission is hereby granted, free of charge, to any person obtaining a copy\n  of this software and associated documentation files (the \"Software\"), to deal\n  in the Software without restriction, including without limitation the rights\n  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n  copies of the Software, and to permit persons to whom the Software is\n  furnished to do so, subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be included in\n  all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n  LIABILITY, WH<PERSON>HER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n  THE SOFTWARE.\n*/\n\nconst isObject = val => {\n  return (\n    typeof val === 'function' || (typeof val === 'object' && val !== null && !Array.isArray(val))\n  );\n};\n\nconst isValidKey = key => {\n  return key !== '__proto__' && key !== 'constructor' && key !== 'prototype';\n};\n\nfunction mixin(target, val, key) {\n  const obj = target[key];\n  if (isObject(val) && isObject(obj)) {\n    mixinDeep(obj, val);\n  } else {\n    target[key] = val;\n  }\n}\n\nexport default function mixinDeep(target, ...rest) {\n  for (const obj of rest) {\n    if (isObject(obj)) {\n      for (const key in obj) {\n        if (isValidKey(key)) {\n          mixin(target, obj[key], key);\n        }\n      }\n    }\n  }\n\n  return target;\n}\n", "// @flow\nimport { EMPTY_OBJECT } from './empties';\n\ntype Props = {\n  theme?: any,\n};\n\nexport default (props: Props, providedTheme: any, defaultProps: any = EMPTY_OBJECT) => {\n  return (props.theme !== defaultProps.theme && props.theme) || providedTheme || defaultProps.theme;\n};\n", "// @flow\nimport type { IStyledComponent } from '../types';\n\nexport default function isTag(target: $PropertyType<IStyledComponent, 'target'>): boolean %checks {\n  return (\n    typeof target === 'string' &&\n    (process.env.NODE_ENV !== 'production'\n      ? target.charAt(0) === target.charAt(0).toLowerCase()\n      : true)\n  );\n}\n", "// @flow\nimport type { IStyledComponent } from '../types';\nimport getComponentName from './getComponentName';\nimport isTag from './isTag';\n\nexport default function generateDisplayName(\n  target: $PropertyType<IStyledComponent, 'target'>\n): string {\n  return isTag(target) ? `styled.${target}` : `Styled(${getComponentName(target)})`;\n}\n", "// @flow\nimport React, { useContext, useMemo, type Element, type Context } from 'react';\nimport throwStyledError from '../utils/error';\nimport isFunction from '../utils/isFunction';\n\nexport type Theme = { [key: string]: mixed };\n\ntype ThemeArgument = Theme | ((outerTheme?: Theme) => Theme);\n\ntype Props = {\n  children?: Element<any>,\n  theme: ThemeArgument,\n};\n\nexport const ThemeContext: Context<Theme | void> = React.createContext();\n\nexport const ThemeConsumer = ThemeContext.Consumer;\n\nfunction mergeTheme(theme: ThemeArgument, outerTheme?: Theme): Theme {\n  if (!theme) {\n    return throwStyledError(14);\n  }\n\n  if (isFunction(theme)) {\n    const mergedTheme = theme(outerTheme);\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      (mergedTheme === null || Array.isArray(mergedTheme) || typeof mergedTheme !== 'object')\n    ) {\n      return throwStyledError(7);\n    }\n\n    return mergedTheme;\n  }\n\n  if (Array.isArray(theme) || typeof theme !== 'object') {\n    return throwStyledError(8);\n  }\n\n  return outerTheme ? { ...outerTheme, ...theme } : theme;\n}\n\n/**\n * Provide a theme to an entire react component tree via context\n */\nexport default function ThemeProvider(props: Props) {\n  const outerTheme = useContext(ThemeContext);\n  const themeContext = useMemo(() => mergeTheme(props.theme, outerTheme), [\n    props.theme,\n    outerTheme,\n  ]);\n\n  if (!props.children) {\n    return null;\n  }\n\n  return <ThemeContext.Provider value={themeContext}>{props.children}</ThemeContext.Provider>;\n}\n", "// @flow\nimport React, { createElement, Component } from 'react';\nimport hoist from 'hoist-non-react-statics';\nimport merge from '../utils/mixinDeep';\nimport determineTheme from '../utils/determineTheme';\nimport { EMPTY_ARRAY, EMPTY_OBJECT } from '../utils/empties';\nimport generateDisplayName from '../utils/generateDisplayName';\nimport isFunction from '../utils/isFunction';\nimport isTag from '../utils/isTag';\nimport isStyledComponent from '../utils/isStyledComponent';\nimport { ThemeConsumer } from './ThemeProvider';\n\nimport type { Theme } from './ThemeProvider';\nimport type { Attrs, RuleSet, Target } from '../types';\n\n// NOTE: no hooks available for react-native yet;\n// if the user makes use of ThemeProvider or StyleSheetManager things will break.\n\n// Validator defaults to true if not in HTML/DOM env\nconst validAttr = () => true;\n\nclass StyledNativeComponent extends Component<*, *> {\n  root: ?Object;\n\n  attrs = {};\n\n  render() {\n    return (\n      <ThemeConsumer>\n        {(theme?: Theme) => {\n          const {\n            $as: transientAsProp,\n            as: renderAs,\n            forwardedComponent,\n            forwardedAs,\n            forwardedRef,\n            testID,\n            style = [],\n            ...props\n          } = this.props;\n\n          const { defaultProps, target, shouldForwardProp } = forwardedComponent;\n          const elementToBeRendered =\n            this.attrs.$as || this.attrs.as || transientAsProp || renderAs || target;\n\n          const generatedStyles = this.generateAndInjectStyles(\n            determineTheme(this.props, theme, defaultProps) || EMPTY_OBJECT,\n            this.props\n          );\n\n          const isTargetTag = isTag(elementToBeRendered);\n          const computedProps = this.attrs !== props ? { ...props, ...this.attrs } : props;\n          const propFilterFn = shouldForwardProp || (isTargetTag && validAttr);\n          const propsForElement = {};\n          let key;\n\n          for (key in computedProps) {\n            if (key[0] === '$' || key === 'as') continue;\n            else if (key === 'forwardedAs') {\n              propsForElement.as = props[key];\n            } else if (!propFilterFn || propFilterFn(key, validAttr)) {\n              // Don't pass through filtered tags through to native elements\n              propsForElement[key] = computedProps[key];\n            }\n          }\n\n          propsForElement.style = typeof style === 'function' ?\n          (state) => {\n            return [generatedStyles].concat(style(state))\n          }\n          : [generatedStyles].concat(style);\n          propsForElement.testID = testID || propsForElement.testID;\n\n          if (forwardedRef) propsForElement.ref = forwardedRef;\n          if (forwardedAs) propsForElement.as = forwardedAs;\n\n          return createElement(elementToBeRendered, propsForElement);\n        }}\n      </ThemeConsumer>\n    );\n  }\n\n  buildExecutionContext(theme: ?Object, props: Object, attrs: Attrs) {\n    const context = { ...props, theme };\n\n    if (!attrs.length) return context;\n\n    this.attrs = {};\n\n    attrs.forEach(attrDef => {\n      let resolvedAttrDef = attrDef;\n      let attr;\n      let key;\n\n      if (isFunction(resolvedAttrDef)) {\n        resolvedAttrDef = resolvedAttrDef(context);\n      }\n\n      /* eslint-disable guard-for-in */\n      for (key in resolvedAttrDef) {\n        attr = resolvedAttrDef[key];\n        this.attrs[key] = attr;\n        context[key] = attr;\n      }\n      /* eslint-enable */\n    });\n\n    return context;\n  }\n\n  generateAndInjectStyles(theme: any, props: any) {\n    const { inlineStyle } = props.forwardedComponent;\n\n    const executionContext = this.buildExecutionContext(\n      theme,\n      props,\n      props.forwardedComponent.attrs\n    );\n\n    return inlineStyle.generateStyleObject(executionContext);\n  }\n\n  setNativeProps(nativeProps: Object) {\n    if (this.root !== undefined) {\n      // $FlowFixMe\n      this.root.setNativeProps(nativeProps);\n    } else if (process.env.NODE_ENV !== 'production') {\n      // eslint-disable-next-line no-console\n      console.warn(\n        'setNativeProps was called on a Styled Component wrapping a stateless functional component.'\n      );\n    }\n  }\n}\n\nexport default (InlineStyle: Function) => {\n  const createStyledNativeComponent = (target: Target, options: Object, rules: RuleSet) => {\n    const {\n      attrs = EMPTY_ARRAY,\n      displayName = generateDisplayName(target),\n      ParentComponent = StyledNativeComponent,\n    } = options;\n\n    const isClass = !isTag(target);\n    const isTargetStyledComp = isStyledComponent(target);\n\n    // $FlowFixMe\n    const WrappedStyledNativeComponent = React.forwardRef((props, ref) => (\n      <ParentComponent\n        {...props}\n        forwardedComponent={WrappedStyledNativeComponent}\n        forwardedRef={ref}\n      />\n    ));\n\n    const finalAttrs =\n      // $FlowFixMe\n      isTargetStyledComp && target.attrs\n        ? Array.prototype.concat(target.attrs, attrs).filter(Boolean)\n        : attrs;\n\n    // eslint-disable-next-line prefer-destructuring\n    let shouldForwardProp = options.shouldForwardProp;\n\n    // $FlowFixMe\n    if (isTargetStyledComp && target.shouldForwardProp) {\n      if (shouldForwardProp) {\n        // compose nested shouldForwardProp calls\n        shouldForwardProp = (prop, filterFn, elementToBeCreated) =>\n          // $FlowFixMe\n          target.shouldForwardProp(prop, filterFn, elementToBeCreated) &&\n          options.shouldForwardProp(prop, filterFn, elementToBeCreated);\n      } else {\n        // eslint-disable-next-line prefer-destructuring\n        shouldForwardProp = target.shouldForwardProp;\n      }\n    }\n\n    /**\n     * forwardRef creates a new interim component, which we'll take advantage of\n     * instead of extending ParentComponent to create _another_ interim class\n     */\n\n    // $FlowFixMe\n    WrappedStyledNativeComponent.attrs = finalAttrs;\n\n    WrappedStyledNativeComponent.displayName = displayName;\n\n    // $FlowFixMe\n    WrappedStyledNativeComponent.shouldForwardProp = shouldForwardProp;\n\n    // $FlowFixMe\n    WrappedStyledNativeComponent.inlineStyle = new InlineStyle(\n      // $FlowFixMe\n      isTargetStyledComp ? target.inlineStyle.rules.concat(rules) : rules\n    );\n\n    // $FlowFixMe\n    WrappedStyledNativeComponent.styledComponentId = 'StyledNativeComponent';\n    // $FlowFixMe\n    WrappedStyledNativeComponent.target = isTargetStyledComp\n      ? // $FlowFixMe\n        target.target\n      : target;\n    // $FlowFixMe\n    WrappedStyledNativeComponent.withComponent = function withComponent(tag: Target) {\n      const { displayName: _, componentId: __, ...optionsToCopy } = options;\n      const newOptions = {\n        ...optionsToCopy,\n        attrs: finalAttrs,\n        ParentComponent,\n      };\n\n      return createStyledNativeComponent(tag, newOptions, rules);\n    };\n\n    // $FlowFixMe\n    Object.defineProperty(WrappedStyledNativeComponent, 'defaultProps', {\n      get() {\n        return this._foldedDefaultProps;\n      },\n\n      set(obj) {\n        // $FlowFixMe\n        this._foldedDefaultProps = isTargetStyledComp ? merge({}, target.defaultProps, obj) : obj;\n      },\n    });\n\n    if (isClass) {\n      hoist(WrappedStyledNativeComponent, (target: any), {\n        // all SC-specific things should not be hoisted\n        attrs: true,\n        displayName: true,\n        shouldForwardProp: true,\n        inlineStyle: true,\n        styledComponentId: true,\n        target: true,\n        withComponent: true,\n      });\n    }\n\n    return WrappedStyledNativeComponent;\n  };\n\n  return createStyledNativeComponent;\n};\n", "// @flow\nimport type { Interpolation } from '../types';\n\nexport default (\n  strings: Array<string>,\n  interpolations: Array<Interpolation>\n): Array<Interpolation> => {\n  const result = [strings[0]];\n\n  for (let i = 0, len = interpolations.length; i < len; i += 1) {\n    result.push(interpolations[i], strings[i + 1]);\n  }\n\n  return result;\n};\n", "// @flow\nimport interleave from '../utils/interleave';\nimport isPlainObject from '../utils/isPlainObject';\nimport { EMPTY_ARRAY } from '../utils/empties';\nimport isFunction from '../utils/isFunction';\nimport flatten from '../utils/flatten';\nimport type { Interpolation, RuleSet, Styles } from '../types';\n\n/**\n * Used when flattening object styles to determine if we should\n * expand an array of styles.\n */\nconst addTag = arg => {\n  if (Array.isArray(arg)) {\n    // eslint-disable-next-line no-param-reassign\n    arg.isCss = true;\n  }\n  return arg;\n};\n\nexport default function css(styles: Styles, ...interpolations: Array<Interpolation>): RuleSet {\n  if (isFunction(styles) || isPlainObject(styles)) {\n    // $FlowFixMe\n    return addTag(flatten(interleave(EMPTY_ARRAY, [styles, ...interpolations])));\n  }\n\n  if (interpolations.length === 0 && styles.length === 1 && typeof styles[0] === 'string') {\n    // $FlowFixMe\n    return styles;\n  }\n\n  // $FlowFixMe\n  return addTag(flatten(interleave(styles, interpolations)));\n}\n", "// @flow\nimport { isValidElementType } from 'react-is';\nimport css from './css';\nimport throwStyledError from '../utils/error';\nimport { EMPTY_OBJECT } from '../utils/empties';\n\nimport type { Target } from '../types';\n\nexport default function constructWithOptions(\n  componentConstructor: Function,\n  tag: Target,\n  options: Object = EMPTY_OBJECT\n) {\n  if (!isValidElementType(tag)) {\n    return throwStyledError(1, String(tag));\n  }\n\n  /* This is callable directly as a template function */\n  // $FlowFixMe: Not typed to avoid destructuring arguments\n  const templateFunction = (...args) => componentConstructor(tag, options, css(...args));\n\n  /* If config methods are called, wrap up a new template function and merge options */\n  templateFunction.withConfig = config =>\n    constructWithOptions(componentConstructor, tag, { ...options, ...config });\n\n  /* Modify/inject new props at runtime */\n  templateFunction.attrs = attrs =>\n    constructWithOptions(componentConstructor, tag, {\n      ...options,\n      attrs: Array.prototype.concat(options.attrs, attrs).filter(Boolean),\n    });\n\n  return templateFunction;\n}\n", "// @flow\nimport React, { useContext, type AbstractComponent } from 'react';\nimport hoistStatics from 'hoist-non-react-statics';\nimport { ThemeContext } from '../models/ThemeProvider';\nimport determineTheme from '../utils/determineTheme';\nimport getComponentName from '../utils/getComponentName';\n\n// NOTE: this would be the correct signature:\n// export default <Config: { theme?: any }, Instance>(\n//  Component: AbstractComponent<Config, Instance>\n// ): AbstractComponent<$Diff<Config, { theme?: any }> & { theme?: any }, Instance>\n//\n// but the old build system tooling doesn't support the syntax\n\nexport default (Component: AbstractComponent<*, *>) => {\n  // $FlowFixMe This should be React.forwardRef<Config, Instance>\n  const WithTheme = React.forwardRef((props, ref) => {\n    const theme = useContext(ThemeContext);\n    // $FlowFixMe defaultProps isn't declared so it can be inferrable\n    const { defaultProps } = Component;\n    const themeProp = determineTheme(props, theme, defaultProps);\n\n    if (process.env.NODE_ENV !== 'production' && themeProp === undefined) {\n      // eslint-disable-next-line no-console\n      console.warn(\n        `[withTheme] You are not using a ThemeProvider nor passing a theme prop or a theme in defaultProps in component class \"${getComponentName(\n          Component\n        )}\"`\n      );\n    }\n\n    return <Component {...props} theme={themeProp} ref={ref} />;\n  });\n\n  hoistStatics(WithTheme, Component);\n\n  WithTheme.displayName = `WithTheme(${getComponentName(Component)})`;\n\n  return WithTheme;\n};\n", "// @flow\nimport { useContext } from 'react';\nimport { ThemeContext } from '../models/ThemeProvider';\n\nconst useTheme = () => useContext(ThemeContext);\n\nexport default useTheme;\n", "// @flow\n\n/* eslint-disable import/no-unresolved */\nimport _InlineStyle from '../models/InlineStyle';\nimport _StyledNativeComponent from '../models/StyledNativeComponent';\n\nimport css from '../constructors/css';\nimport constructWithOptions from '../constructors/constructWithOptions';\nimport ThemeProvider, { ThemeConsumer, ThemeContext } from '../models/ThemeProvider';\nimport withTheme from '../hoc/withTheme';\nimport useTheme from '../hooks/useTheme';\nimport isStyledComponent from '../utils/isStyledComponent';\n\nimport type { Target } from '../types';\n\nconst reactNative = require('react-native');\n\nconst InlineStyle = _InlineStyle(reactNative.StyleSheet);\nconst StyledNativeComponent = _StyledNativeComponent(InlineStyle);\nconst styled = (tag: Target) => constructWithOptions(StyledNativeComponent, tag);\n\n/* React native lazy-requires each of these modules for some reason, so let's\n *  assume it's for a good reason and not eagerly load them all */\nconst aliases = `ActivityIndicator ActivityIndicatorIOS ART Button DatePickerIOS DrawerLayoutAndroid\n Image ImageBackground ImageEditor ImageStore KeyboardAvoidingView ListView MapView Modal NavigatorIOS\n Picker PickerIOS ProgressBarAndroid ProgressViewIOS ScrollView SegmentedControlIOS Slider\n SliderIOS SnapshotViewIOS Switch RecyclerViewBackedScrollView RefreshControl SafeAreaView StatusBar\n SwipeableListView SwitchAndroid SwitchIOS TabBarIOS Text TextInput ToastAndroid ToolbarAndroid\n Touchable TouchableHighlight TouchableNativeFeedback TouchableOpacity TouchableWithoutFeedback\n View ViewPagerAndroid WebView FlatList SectionList VirtualizedList Pressable`;\n\n/* Define a getter for each alias which simply gets the reactNative component\n * and passes it to styled */\naliases.split(/\\s+/m).forEach(alias =>\n  Object.defineProperty(styled, alias, {\n    enumerable: true,\n    configurable: false,\n    get() {\n      return styled(reactNative[alias]);\n    },\n  })\n);\n\nexport { css, isStyledComponent, ThemeProvider, ThemeConsumer, ThemeContext, withTheme, useTheme };\nexport default styled;\n"], "names": ["AD_REPLACER_R", "chars<PERSON><PERSON><PERSON>", "getAlphabeticChar", "code", "String", "fromCharCode", "generateAlphabeticName", "name", "x", "Math", "abs", "replace", "SEED", "phash", "h", "i", "length", "charCodeAt", "hash", "str", "getComponentName", "target", "process", "env", "NODE_ENV", "displayName", "isFunction", "test", "isStatelessFunction", "prototype", "isReactComponent", "toString", "Object", "call", "typeOf", "isStyledComponent", "styledComponentId", "SC_ATTR", "REACT_APP_SC_ATTR", "SC_ATTR_ACTIVE", "SC_ATTR_VERSION", "SC_VERSION", "__VERSION__", "SPLITTER", "IS_BROWSER", "window", "DISABLE_SPEEDY", "Boolean", "SC_DISABLE_SPEEDY", "REACT_APP_SC_DISABLE_SPEEDY", "EMPTY_ARRAY", "freeze", "EMPTY_OBJECT", "ERRORS", "errorMap", "format", "a", "b", "c", "len", "push", "for<PERSON>ach", "d", "throwStyledComponentsError", "interpolations", "Error", "join", "trim", "makeGroupedTag", "tag", "DefaultGroupedTag", "BASE_SIZE", "groupSizes", "Uint32Array", "indexOfGroup", "group", "index", "insertRules", "rules", "<PERSON><PERSON><PERSON><PERSON>", "oldSize", "newSize", "throwStyledError", "set", "ruleIndex", "l", "insertRule", "clearGroup", "startIndex", "endIndex", "deleteRule", "getGroup", "css", "getRule", "MAX_SMI", "groupIDRegister", "Map", "reverseRegister", "nextFreeGroup", "getGroupForId", "id", "has", "get", "getIdForGroup", "setGroupForId", "SELECTOR", "MARKER_RE", "RegExp", "outputSheet", "sheet", "getTag", "undefined", "names", "size", "selector", "content", "rehydrateNamesFromContent", "split", "registerName", "rehydrateSheetFromTag", "style", "parts", "textContent", "part", "marker", "match", "parseInt", "rehydrateSheet", "nodes", "document", "querySelectorAll", "node", "getAttribute", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "getNonce", "__webpack_nonce__", "ELEMENT_TYPE", "findLastStyleTag", "childNodes", "child", "nodeType", "hasAttribute", "makeStyleTag", "head", "parent", "createElement", "prevStyle", "nextS<PERSON>ling", "setAttribute", "nonce", "insertBefore", "getSheet", "styleSheets", "ownerNode", "makeTag", "isServer", "useCSSOMInjection", "VirtualTag", "CSSOMTag", "TextTag", "element", "append<PERSON><PERSON><PERSON>", "createTextNode", "rule", "_error", "cssRules", "cssText", "refNode", "_target", "splice", "SHOULD_REHYDRATE", "defaultOptions", "StyleSheet", "registerId", "options", "globalStyles", "gs", "server", "reconstructWithOptions", "with<PERSON><PERSON>s", "allocateGSInstance", "hasNameForId", "groupNames", "Set", "add", "clearNames", "clear", "clearRules", "clearTag", "delimiter", "needle", "toSheet", "block", "e", "ruleSheet", "context", "selectors", "parents", "line", "column", "ns", "depth", "at", "COMMENT_REGEX", "COMPLEX_SELECTOR_PREFIX", "createStylisInstance", "plugins", "stylis", "<PERSON><PERSON><PERSON>", "parsingRules", "returnRulesPlugin", "parsedRules", "parseRulesPlugin", "insertRulePlugin", "_componentId", "_selector", "_selectorRegexp", "_consecutiveSelfRefRegExp", "selfReferenceReplacer", "offset", "string", "indexOf", "selfReferenceReplacementPlugin", "_", "lastIndexOf", "use", "stringifyRules", "prefix", "componentId", "flatCSS", "cssStr", "reduce", "acc", "plugin", "StyleSheetContext", "React", "createContext", "StyleSheetConsumer", "Consumer", "StylisContext", "StylisConsumer", "masterSheet", "master<PERSON><PERSON><PERSON>", "Keyframes", "inject", "styleSheet", "stylisInstance", "resolvedName", "getName", "uppercaseCheck", "uppercasePattern", "msPattern", "prefixAndLowerCase", "char", "toLowerCase", "hyphenateStyleName", "addUnitIfNeeded", "value", "unitless", "startsWith", "isFalsish", "chunk", "objToCssArray", "obj", "prev<PERSON><PERSON>", "key", "hasOwnProperty", "Array", "isArray", "isCss", "hyphenate", "isPlainObject", "flatten", "executionContext", "ruleSet", "result", "isElement", "console", "warn", "printed", "warnOnce", "message", "SINGLE_QUOTE", "DOUBLE_QUOTE", "BACKSLASH", "SLASH", "NEWLINE", "SPACE", "FEED", "TAB", "CR", "OPEN_SQUARE", "CLOSE_SQUARE", "OPEN_PARENTHESES", "CLOSE_PARENTHESES", "OPEN_CURLY", "CLOSE_CURLY", "SEMICOLON", "ASTERISK", "COLON", "AT", "RE_AT_END", "RE_WORD_END", "RE_BAD_BRACKET", "tokenize", "input", "tokens", "valueOf", "ignore", "ignoreErrors", "next", "quote", "lines", "last", "escape", "nextLine", "nextOffset", "escaped", "escapePos", "prev", "n", "pos", "unclosed", "what", "error", "slice", "lastIndex", "HIGHLIGHT_THEME", "brackets", "comment", "color", "terminalHighlight", "Input", "token", "map", "CssSyntaxError", "source", "file", "reason", "setMessage", "captureStackTrace", "showSourceCode", "supportsColor", "start", "max", "end", "min", "max<PERSON><PERSON><PERSON>", "number", "padded", "gutter", "spacing", "defaultRaw", "colon", "indent", "beforeDecl", "beforeRule", "beforeOpen", "beforeClose", "beforeComment", "after", "emptyBody", "commentLeft", "commentRight", "capitalize", "toUpperCase", "Stringifier", "builder", "stringify", "semicolon", "type", "root", "body", "raws", "left", "raw", "right", "text", "decl", "between", "prop", "rawValue", "important", "at<PERSON>le", "params", "after<PERSON>ame", "before", "own", "detect", "first", "rawCache", "beforeAfter", "method", "walk", "rawSemicolon", "rawEmptyBody", "rawIndent", "p", "rawBeforeComment", "walkComments", "rawBeforeDecl", "walkDecls", "rawBeforeRule", "rawBeforeClose", "rawBeforeOpen", "rawColon", "buf", "step", "cloneNode", "cloned", "constructor", "j", "Node", "defaults", "opts", "positionBy", "data", "remove", "stringifier", "clone", "overrides", "cloneBefore", "cloneAfter", "insertAfter", "replaceWith", "moveTo", "newParent", "cleanRaws", "append", "moveBefore", "otherNode", "moveAfter", "toJSON", "fixed", "defaultType", "keepBetween", "positionInside", "word", "removeSelf", "cleanStyles", "val", "Declaration", "Comment", "<PERSON><PERSON><PERSON>", "Root", "current", "spaces", "tokenizer", "loop", "emptyRule", "other", "endFile", "init", "Rule", "bracket", "pop", "unclosedBracket", "unknown<PERSON><PERSON>", "spacesFromEnd", "shift", "spacesFromStart", "precheckMissedSemicolon", "stringFrom", "cache", "checkMissedSemicolon", "AtRule", "unnamedAtrule", "open", "unexpectedClose", "unclosedBlock", "clean", "all", "lastTokenType", "from", "doubleColon", "founded", "parse", "safe", "parser", "cleanSource", "Container", "each", "callback", "lastEach", "indexes", "walkRules", "walkAtRules", "children", "normalize", "prepend", "reverse", "unshift", "exist", "removeAll", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "props", "fast", "every", "condition", "some", "sample", "processed", "rebuild", "fix", "eachInside", "eachDecl", "eachRule", "eachAtRule", "eachComment", "list", "separators", "array", "func", "letter", "space", "comma", "values", "sep", "Warning", "opt", "Result", "processor", "messages", "lastPlugin", "postcssPlugin", "warning", "warnings", "filter", "isPromise", "then", "LazyResult", "stringified", "inline", "syntax", "sync", "onFulfilled", "onRejected", "async", "handleError", "postcssVersion", "pluginName", "pluginVer", "runtimeVer", "version", "err", "asyncTick", "resolve", "reject", "promise", "run", "Promise", "processing", "Processor", "concat", "normalized", "postcss", "toResult", "lazy", "prevMap", "sequence", "path", "origin", "consumer", "originalPositionFor", "mapResolve", "sourceContentFor", "sourceRoot", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "safeParse", "generated", "InlineStyle", "generateStyleObject", "generateComponentId", "declPairs", "styleObject", "transformDeclPairs", "styles", "create", "isObject", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "mixin", "mixinDeep", "rest", "providedTheme", "defaultProps", "theme", "isTag", "char<PERSON>t", "generateDisplayName", "ThemeContext", "ThemeConsumer", "mergeTheme", "outerTheme", "mergedTheme", "ThemeProvider", "useContext", "themeContext", "useMemo", "validAttr", "StyledNativeComponent", "attrs", "render", "transientAsProp", "$as", "renderAs", "as", "forwardedComponent", "forwardedAs", "forwardedRef", "testID", "shouldForwardProp", "elementToBeRendered", "generatedStyles", "generateAndInjectStyles", "determineTheme", "isTargetTag", "computedProps", "propFilterFn", "propsForElement", "state", "ref", "buildExecutionContext", "attrDef", "resolvedAttrDef", "attr", "inlineStyle", "setNativeProps", "nativeProps", "Component", "createStyledNativeComponent", "ParentComponent", "isClass", "isTargetStyledComp", "WrappedStyledNativeComponent", "forwardRef", "finalAttrs", "filterFn", "elementToBeCreated", "withComponent", "__", "optionsToCopy", "newOptions", "defineProperty", "_foldedDefaultProps", "merge", "hoist", "strings", "addTag", "arg", "interleave", "constructWithOptions", "componentConstructor", "isValidElementType", "templateFunction", "withConfig", "config", "WithTheme", "themeProp", "hoistStatics", "useTheme", "reactNative", "require", "_InlineStyle", "_StyledNativeComponent", "styled", "aliases", "alias", "enumerable", "configurable"], "mappings": ";;;;;;;;;AAAA;;AACA;AAEA,IAAMA,aAAa,GAAG,UAAtB;AAEA;;;AAEA,IAAMC,WAAW,GAAG,EAApB;AAEA;;AACA,IAAMC,iBAAiB,GAAG,SAApBA,iBAAoB,CAACC,IAAD;AAAA,SACxBC,MAAM,CAACC,YAAP,CAAoBF,IAAI,IAAIA,IAAI,GAAG,EAAP,GAAY,EAAZ,GAAiB,EAArB,CAAxB,CADwB;AAAA,CAA1B;AAGA;;;AACe,SAASG,sBAAT,CAAgCH,IAAhC,EAAsD;AACnE,MAAII,IAAI,GAAG,EAAX;AACA,MAAIC,CAAJ;AAEA;;AACA,OAAKA,CAAC,GAAGC,IAAI,CAACC,GAAL,CAASP,IAAT,CAAT,EAAyBK,CAAC,GAAGP,WAA7B,EAA0CO,CAAC,GAAIA,CAAC,GAAGP,WAAL,GAAoB,CAAlE,EAAqE;AACnEM,IAAAA,IAAI,GAAGL,iBAAiB,CAACM,CAAC,GAAGP,WAAL,CAAjB,GAAqCM,IAA5C;AACD;;AAED,SAAO,CAACL,iBAAiB,CAACM,CAAC,GAAGP,WAAL,CAAjB,GAAqCM,IAAtC,EAA4CI,OAA5C,CAAoDX,aAApD,EAAmE,OAAnE,CAAP;AACD;;ACxBD;;AACA;AAEA,AAAO,IAAMY,IAAI,GAAG,IAAb;AAGP;AACA;;AACA,AAAO,IAAMC,KAAK,GAAG,SAARA,KAAQ,CAACC,CAAD,EAAYN,CAAZ,EAAkC;AACrD,MAAIO,CAAC,GAAGP,CAAC,CAACQ,MAAV;;AAEA,SAAOD,CAAP,EAAU;AACRD,IAAAA,CAAC,GAAIA,CAAC,GAAG,EAAL,GAAWN,CAAC,CAACS,UAAF,CAAa,EAAEF,CAAf,CAAf;AACD;;AAED,SAAOD,CAAP;AACD,CARM;;AAWP,AAAO,IAAMI,IAAI,GAAG,SAAPA,IAAO,CAACV,CAAD,EAAuB;AACzC,SAAOK,KAAK,CAACD,IAAD,EAAOJ,CAAP,CAAZ;AACD,CAFM;;ACnBP;AAKA,2BAAe,UAACW,GAAD,EAAyB;AACtC,SAAOb,sBAAsB,CAACY,IAAI,CAACC,GAAD,CAAJ,KAAc,CAAf,CAA7B;AACD,CAFD;;ACLA;AAGA,AAAe,SAASC,gBAAT,CACbC,MADa,EAEL;AACR,SACE,CAACC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAAzB,GAAwC,OAAOH,MAAP,KAAkB,QAAlB,IAA8BA,MAAtE,GAA+E,KAAhF;AAEAA,EAAAA,MAAM,CAACI,WAFP;AAIAJ,EAAAA,MAAM,CAACd,IAJP,IAKA,WANF;AAQD;;ACdD;AACA,AAAe,SAASmB,UAAT,CAAoBC,IAApB,EAAgD;AAC7D,SAAO,OAAOA,IAAP,KAAgB,UAAvB;AACD;;ACHD;AACA,AAAe,SAASC,mBAAT,CAA6BD,IAA7B,EAAiD;AAC9D,SACE,OAAOA,IAAP,KAAgB,UAAhB,IACG,EACDA,IAAI,CAACE,SAAL,IACGF,IAAI,CAACE,SAAL,CAAeC,gBAFjB,CAFL;AAOD;;ACTD;AACA,AAEA,qBAAe,UAACtB,CAAD;AAAA,SACbA,CAAC,KAAK,IAAN,IACA,OAAOA,CAAP,KAAa,QADb,IAEA,CAACA,CAAC,CAACuB,QAAF,GAAavB,CAAC,CAACuB,QAAF,EAAb,GAA4BC,MAAM,CAACH,SAAP,CAAiBE,QAAjB,CAA0BE,IAA1B,CAA+BzB,CAA/B,CAA7B,MAAoE,iBAFpE,IAGA,CAAC0B,MAAM,CAAC1B,CAAD,CAJM;AAAA,CAAf;;ACHA;AACA,AAAe,SAAS2B,iBAAT,CAA2Bd,MAA3B,EAAyD;AACtE,SAAOA,MAAM,IAAI,OAAOA,MAAM,CAACe,iBAAd,KAAoC,QAArD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACHD;AAKA,AAAO,IAAMC,OAAO,GACjB,OAAOf,OAAP,KAAmB,WAAnB,IACC,OAAOA,OAAO,CAACC,GAAf,KAAuB,WADxB,KAEED,OAAO,CAACC,GAAR,CAAYe,iBAAZ,IAAiChB,OAAO,CAACC,GAAR,CAAYc,OAF/C,CAAD,IAGA,aAJK;AAMP,AAAO,IAAME,cAAc,GAAG,QAAvB;AACP,AAAO,IAAMC,eAAe,GAAG,qBAAxB;AACP,AAAO,IAAMC,UAAU,GAAGC,QAAnB;AACP,AAAO,IAAMC,QAAQ,GAAG,WAAjB;AAEP,AAAO,IAAMC,UAAU,GAAG,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,iBAAiBA,MAArE;AAEP,AAAO,IAAMC,cAAc,GAAGC,OAAO,CACnC,OAAOC,iBAAP,KAA6B,SAA7B,GACIA,iBADJ,GAEI,OAAO1B,OAAP,KAAmB,WAAnB,IAAkC,OAAOA,OAAO,CAACC,GAAf,KAAuB,WAAzD,GACA,OAAOD,OAAO,CAACC,GAAR,CAAY0B,2BAAnB,KAAmD,WAAnD,IACA3B,OAAO,CAACC,GAAR,CAAY0B,2BAAZ,KAA4C,EAD5C,GAEE3B,OAAO,CAACC,GAAR,CAAY0B,2BAAZ,KAA4C,OAA5C,GACE,KADF,GAEE3B,OAAO,CAACC,GAAR,CAAY0B,2BAJhB,GAKE,OAAO3B,OAAO,CAACC,GAAR,CAAYyB,iBAAnB,KAAyC,WAAzC,IAAwD1B,OAAO,CAACC,GAAR,CAAYyB,iBAAZ,KAAkC,EAA1F,GACA1B,OAAO,CAACC,GAAR,CAAYyB,iBAAZ,KAAkC,OAAlC,GACE,KADF,GAEE1B,OAAO,CAACC,GAAR,CAAYyB,iBAHd,GAIA1B,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAV3B,GAWA,KAd+B,CAA9B;;AClBP;AACA,AAAO,IAAM0B,WAAW,GAAGlB,MAAM,CAACmB,MAAP,CAAc,EAAd,CAApB;AACP,AAAO,IAAMC,YAAY,GAAGpB,MAAM,CAACmB,MAAP,CAAc,EAAd,CAArB;;ACFP,eAAe;AAAC,OAAI,uDAAL;AAA6D,OAAI,+PAAjE;AAAiU,OAAI,qHAArU;AAA2b,OAAI,qMAA/b;AAAqoB,OAAI,iKAAzoB;AAA2yB,OAAI,2OAA/yB;AAA2hC,OAAI,sHAA/hC;AAAspC,OAAI,+DAA1pC;AAA0tC,OAAI,+BAA9tC;AAA8vC,QAAK,gUAAnwC;AAAokD,QAAK,uNAAzkD;AAAiyD,QAAK,oWAAtyD;AAA2oE,QAAK,wLAAhpE;AAAy0E,QAAK,gDAA90E;AAA+3E,QAAK,0ZAAp4E;AAA+xF,QAAK,sQAApyF;AAA2iG,QAAK;AAAhjG,CAAf;;ACAA;AACA,AAEA,IAAME,MAAM,GAAG/B,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAAzB,GAAwC8B,QAAxC,GAAmD,EAAlE;AAEA;;;;AAGA,SAASC,MAAT,GAAyB;AACvB,MAAIC,CAAC,mDAAL;AACA,MAAMC,CAAC,GAAG,EAAV;;AAEA,OAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAG,UAAK3C,MAA3B,EAAmC0C,CAAC,GAAGC,GAAvC,EAA4CD,CAAC,IAAI,CAAjD,EAAoD;AAClDD,IAAAA,CAAC,CAACG,IAAF,CAAYF,CAAZ,4BAAYA,CAAZ,yBAAYA,CAAZ;AACD;;AAEDD,EAAAA,CAAC,CAACI,OAAF,CAAU,UAAAC,CAAC,EAAI;AACbN,IAAAA,CAAC,GAAGA,CAAC,CAAC7C,OAAF,CAAU,QAAV,EAAoBmD,CAApB,CAAJ;AACD,GAFD;AAIA,SAAON,CAAP;AACD;AAED;;;;;;AAIA,AAAe,SAASO,0BAAT,CACb5D,IADa,EAGb;AAAA,oCADG6D,cACH;AADGA,IAAAA,cACH;AAAA;;AACA,MAAI1C,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzC,UAAM,IAAIyC,KAAJ,kDAC2C9D,IAD3C,+BAEF6D,cAAc,CAAChD,MAAf,GAAwB,CAAxB,eAAsCgD,cAAc,CAACE,IAAf,CAAoB,IAApB,CAAtC,GAAoE,EAFlE,EAAN;AAKD,GAND,MAMO;AACL,UAAM,IAAID,KAAJ,CAAUV,MAAM,MAAN,UAAOF,MAAM,CAAClD,IAAD,CAAb,SAAwB6D,cAAxB,GAAwCG,IAAxC,EAAV,CAAN;AACD;AACF;;ACxCD;AAOA;;AACA,AAAO,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,GAAD,EAA0B;AACtD,SAAO,IAAIC,iBAAJ,CAAsBD,GAAtB,CAAP;AACD,CAFM;AAIP,IAAME,SAAS,GAAG,KAAK,CAAvB;;IAEMD;AAOJ,6BAAYD,GAAZ,EAAsB;AACpB,SAAKG,UAAL,GAAkB,IAAIC,WAAJ,CAAgBF,SAAhB,CAAlB;AACA,SAAKvD,MAAL,GAAcuD,SAAd;AACA,SAAKF,GAAL,GAAWA,GAAX;AACD;;;;SAEDK,eAAA,sBAAaC,KAAb,EAAoC;AAClC,QAAIC,KAAK,GAAG,CAAZ;;AACA,SAAK,IAAI7D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4D,KAApB,EAA2B5D,CAAC,EAA5B,EAAgC;AAC9B6D,MAAAA,KAAK,IAAI,KAAKJ,UAAL,CAAgBzD,CAAhB,CAAT;AACD;;AAED,WAAO6D,KAAP;AACD;;SAEDC,cAAA,qBAAYF,KAAZ,EAA2BG,KAA3B,EAAkD;AAChD,QAAIH,KAAK,IAAI,KAAKH,UAAL,CAAgBxD,MAA7B,EAAqC;AACnC,UAAM+D,SAAS,GAAG,KAAKP,UAAvB;AACA,UAAMQ,OAAO,GAAGD,SAAS,CAAC/D,MAA1B;AAEA,UAAIiE,OAAO,GAAGD,OAAd;;AACA,aAAOL,KAAK,IAAIM,OAAhB,EAAyB;AACvBA,QAAAA,OAAO,KAAK,CAAZ;;AACA,YAAIA,OAAO,GAAG,CAAd,EAAiB;AACfC,UAAAA,0BAAgB,CAAC,EAAD,OAAQP,KAAR,CAAhB;AACD;AACF;;AAED,WAAKH,UAAL,GAAkB,IAAIC,WAAJ,CAAgBQ,OAAhB,CAAlB;AACA,WAAKT,UAAL,CAAgBW,GAAhB,CAAoBJ,SAApB;AACA,WAAK/D,MAAL,GAAciE,OAAd;;AAEA,WAAK,IAAIlE,CAAC,GAAGiE,OAAb,EAAsBjE,CAAC,GAAGkE,OAA1B,EAAmClE,CAAC,EAApC,EAAwC;AACtC,aAAKyD,UAAL,CAAgBzD,CAAhB,IAAqB,CAArB;AACD;AACF;;AAED,QAAIqE,SAAS,GAAG,KAAKV,YAAL,CAAkBC,KAAK,GAAG,CAA1B,CAAhB;;AACA,SAAK,IAAI5D,EAAC,GAAG,CAAR,EAAWsE,CAAC,GAAGP,KAAK,CAAC9D,MAA1B,EAAkCD,EAAC,GAAGsE,CAAtC,EAAyCtE,EAAC,EAA1C,EAA8C;AAC5C,UAAI,KAAKsD,GAAL,CAASiB,UAAT,CAAoBF,SAApB,EAA+BN,KAAK,CAAC/D,EAAD,CAApC,CAAJ,EAA8C;AAC5C,aAAKyD,UAAL,CAAgBG,KAAhB;AACAS,QAAAA,SAAS;AACV;AACF;AACF;;SAEDG,aAAA,oBAAWZ,KAAX,EAAgC;AAC9B,QAAIA,KAAK,GAAG,KAAK3D,MAAjB,EAAyB;AACvB,UAAMA,MAAM,GAAG,KAAKwD,UAAL,CAAgBG,KAAhB,CAAf;AACA,UAAMa,UAAU,GAAG,KAAKd,YAAL,CAAkBC,KAAlB,CAAnB;AACA,UAAMc,QAAQ,GAAGD,UAAU,GAAGxE,MAA9B;AAEA,WAAKwD,UAAL,CAAgBG,KAAhB,IAAyB,CAAzB;;AAEA,WAAK,IAAI5D,CAAC,GAAGyE,UAAb,EAAyBzE,CAAC,GAAG0E,QAA7B,EAAuC1E,CAAC,EAAxC,EAA4C;AAC1C,aAAKsD,GAAL,CAASqB,UAAT,CAAoBF,UAApB;AACD;AACF;AACF;;SAEDG,WAAA,kBAAShB,KAAT,EAAgC;AAC9B,QAAIiB,GAAG,GAAG,EAAV;;AACA,QAAIjB,KAAK,IAAI,KAAK3D,MAAd,IAAwB,KAAKwD,UAAL,CAAgBG,KAAhB,MAA2B,CAAvD,EAA0D;AACxD,aAAOiB,GAAP;AACD;;AAED,QAAM5E,MAAM,GAAG,KAAKwD,UAAL,CAAgBG,KAAhB,CAAf;AACA,QAAMa,UAAU,GAAG,KAAKd,YAAL,CAAkBC,KAAlB,CAAnB;AACA,QAAMc,QAAQ,GAAGD,UAAU,GAAGxE,MAA9B;;AAEA,SAAK,IAAID,CAAC,GAAGyE,UAAb,EAAyBzE,CAAC,GAAG0E,QAA7B,EAAuC1E,CAAC,EAAxC,EAA4C;AAC1C6E,MAAAA,GAAG,SAAO,KAAKvB,GAAL,CAASwB,OAAT,CAAiB9E,CAAjB,CAAP,GAA6B4B,QAAhC;AACD;;AAED,WAAOiD,GAAP;AACD;;;;;AChGH;AAEA,AAEA,IAAME,OAAO,GAAG,KAAK,KAAK,CAA1B;AAEA,IAAIC,eAAe,GAAwB,IAAIC,GAAJ,EAA3C;AACA,IAAIC,eAAe,GAAwB,IAAID,GAAJ,EAA3C;AACA,IAAIE,aAAa,GAAG,CAApB;AAEA,AAMO,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAACC,EAAD,EAAwB;AACnD,MAAIL,eAAe,CAACM,GAAhB,CAAoBD,EAApB,CAAJ,EAA6B;AAC3B,WAAQL,eAAe,CAACO,GAAhB,CAAoBF,EAApB,CAAR;AACD;;AAED,SAAOH,eAAe,CAACI,GAAhB,CAAoBH,aAApB,CAAP,EAA2C;AACzCA,IAAAA,aAAa;AACd;;AAED,MAAMvB,KAAK,GAAGuB,aAAa,EAA3B;;AAEA,MACE5E,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAAzB,KACC,CAACmD,KAAK,GAAG,CAAT,IAAc,CAAd,IAAmBA,KAAK,GAAGmB,OAD5B,CADF,EAGE;AACAZ,IAAAA,0BAAgB,CAAC,EAAD,OAAQP,KAAR,CAAhB;AACD;;AAEDoB,EAAAA,eAAe,CAACZ,GAAhB,CAAoBiB,EAApB,EAAwBzB,KAAxB;AACAsB,EAAAA,eAAe,CAACd,GAAhB,CAAoBR,KAApB,EAA2ByB,EAA3B;AACA,SAAOzB,KAAP;AACD,CArBM;AAuBP,AAAO,IAAM4B,aAAa,GAAG,SAAhBA,aAAgB,CAAC5B,KAAD,EAAkC;AAC7D,SAAOsB,eAAe,CAACK,GAAhB,CAAoB3B,KAApB,CAAP;AACD,CAFM;AAIP,AAAO,IAAM6B,aAAa,GAAG,SAAhBA,aAAgB,CAACJ,EAAD,EAAazB,KAAb,EAA+B;AAC1D,MAAIA,KAAK,IAAIuB,aAAb,EAA4B;AAC1BA,IAAAA,aAAa,GAAGvB,KAAK,GAAG,CAAxB;AACD;;AAEDoB,EAAAA,eAAe,CAACZ,GAAhB,CAAoBiB,EAApB,EAAwBzB,KAAxB;AACAsB,EAAAA,eAAe,CAACd,GAAhB,CAAoBR,KAApB,EAA2ByB,EAA3B;AACD,CAPM;;AC3CP;AAEA,AAIA,IAAMK,QAAQ,cAAYpE,OAAZ,UAAwBG,eAAxB,WAA4CC,UAA5C,QAAd;AACA,IAAMiE,SAAS,GAAG,IAAIC,MAAJ,OAAetE,OAAf,sDAAlB;AAEA,AAAO,IAAMuE,WAAW,GAAG,SAAdA,WAAc,CAACC,KAAD,EAAkB;AAC3C,MAAMxC,GAAG,GAAGwC,KAAK,CAACC,MAAN,EAAZ;AAD2C,MAEnC9F,MAFmC,GAExBqD,GAFwB,CAEnCrD,MAFmC;AAI3C,MAAI4E,GAAG,GAAG,EAAV;;AACA,OAAK,IAAIjB,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG3D,MAA5B,EAAoC2D,KAAK,EAAzC,EAA6C;AAC3C,QAAMyB,EAAE,GAAGG,aAAa,CAAC5B,KAAD,CAAxB;AACA,QAAIyB,EAAE,KAAKW,SAAX,EAAsB;AAEtB,QAAMC,KAAK,GAAGH,KAAK,CAACG,KAAN,CAAYV,GAAZ,CAAgBF,EAAhB,CAAd;AACA,QAAMtB,KAAK,GAAGT,GAAG,CAACsB,QAAJ,CAAahB,KAAb,CAAd;AACA,QAAI,CAACqC,KAAD,IAAU,CAAClC,KAAX,IAAoB,CAACkC,KAAK,CAACC,IAA/B,EAAqC;AAErC,QAAMC,QAAQ,GAAM7E,OAAN,UAAkBsC,KAAlB,cAA+ByB,EAA/B,QAAd;AAEA,QAAIe,OAAO,GAAG,EAAd;;AACA,QAAIH,KAAK,KAAKD,SAAd,EAAyB;AACvBC,MAAAA,KAAK,CAACnD,OAAN,CAAc,UAAAtD,IAAI,EAAI;AACpB,YAAIA,IAAI,CAACS,MAAL,GAAc,CAAlB,EAAqB;AACnBmG,UAAAA,OAAO,IAAO5G,IAAP,MAAP;AACD;AACF,OAJD;AAKD,KAjB0C;AAoB3C;;;AACAqF,IAAAA,GAAG,SAAOd,KAAP,GAAeoC,QAAf,mBAAoCC,OAApC,WAAgDxE,QAAnD;AACD;;AAED,SAAOiD,GAAP;AACD,CA9BM;;AAgCP,IAAMwB,yBAAyB,GAAG,SAA5BA,yBAA4B,CAACP,KAAD,EAAeT,EAAf,EAA2Be,OAA3B,EAA+C;AAC/E,MAAMH,KAAK,GAAGG,OAAO,CAACE,KAAR,CAAc,GAAd,CAAd;AACA,MAAI9G,IAAJ;;AAEA,OAAK,IAAIQ,CAAC,GAAG,CAAR,EAAWsE,CAAC,GAAG2B,KAAK,CAAChG,MAA1B,EAAkCD,CAAC,GAAGsE,CAAtC,EAAyCtE,CAAC,EAA1C,EAA8C;AAC5C;AACA,QAAKR,IAAI,GAAGyG,KAAK,CAACjG,CAAD,CAAjB,EAAuB;AACrB8F,MAAAA,KAAK,CAACS,YAAN,CAAmBlB,EAAnB,EAAuB7F,IAAvB;AACD;AACF;AACF,CAVD;;AAYA,IAAMgH,qBAAqB,GAAG,SAAxBA,qBAAwB,CAACV,KAAD,EAAeW,KAAf,EAA2C;AACvE,MAAMC,KAAK,GAAG,CAACD,KAAK,CAACE,WAAN,IAAqB,EAAtB,EAA0BL,KAA1B,CAAgC1E,QAAhC,CAAd;AACA,MAAMmC,KAAK,GAAa,EAAxB;;AAEA,OAAK,IAAI/D,CAAC,GAAG,CAAR,EAAWsE,CAAC,GAAGoC,KAAK,CAACzG,MAA1B,EAAkCD,CAAC,GAAGsE,CAAtC,EAAyCtE,CAAC,EAA1C,EAA8C;AAC5C,QAAM4G,IAAI,GAAGF,KAAK,CAAC1G,CAAD,CAAL,CAASoD,IAAT,EAAb;AACA,QAAI,CAACwD,IAAL,EAAW;AAEX,QAAMC,MAAM,GAAGD,IAAI,CAACE,KAAL,CAAWnB,SAAX,CAAf;;AAEA,QAAIkB,MAAJ,EAAY;AACV,UAAMjD,KAAK,GAAGmD,QAAQ,CAACF,MAAM,CAAC,CAAD,CAAP,EAAY,EAAZ,CAAR,GAA0B,CAAxC;AACA,UAAMxB,EAAE,GAAGwB,MAAM,CAAC,CAAD,CAAjB;;AAEA,UAAIjD,KAAK,KAAK,CAAd,EAAiB;AACf;AACA6B,QAAAA,aAAa,CAACJ,EAAD,EAAKzB,KAAL,CAAb,CAFe;AAIf;;AACAyC,QAAAA,yBAAyB,CAACP,KAAD,EAAQT,EAAR,EAAYwB,MAAM,CAAC,CAAD,CAAlB,CAAzB;AACAf,QAAAA,KAAK,CAACC,MAAN,GAAejC,WAAf,CAA2BF,KAA3B,EAAkCG,KAAlC;AACD;;AAEDA,MAAAA,KAAK,CAAC9D,MAAN,GAAe,CAAf;AACD,KAdD,MAcO;AACL8D,MAAAA,KAAK,CAAClB,IAAN,CAAW+D,IAAX;AACD;AACF;AACF,CA5BD;;AA8BA,AAAO,IAAMI,cAAc,GAAG,SAAjBA,cAAiB,CAAClB,KAAD,EAAkB;AAC9C,MAAMmB,KAAK,GAAGC,QAAQ,CAACC,gBAAT,CAA0BzB,QAA1B,CAAd;;AAEA,OAAK,IAAI1F,CAAC,GAAG,CAAR,EAAWsE,CAAC,GAAG2C,KAAK,CAAChH,MAA1B,EAAkCD,CAAC,GAAGsE,CAAtC,EAAyCtE,CAAC,EAA1C,EAA8C;AAC5C,QAAMoH,IAAI,GAAKH,KAAK,CAACjH,CAAD,CAApB;;AACA,QAAIoH,IAAI,IAAIA,IAAI,CAACC,YAAL,CAAkB/F,OAAlB,MAA+BE,cAA3C,EAA2D;AACzDgF,MAAAA,qBAAqB,CAACV,KAAD,EAAQsB,IAAR,CAArB;;AAEA,UAAIA,IAAI,CAACE,UAAT,EAAqB;AACnBF,QAAAA,IAAI,CAACE,UAAL,CAAgBC,WAAhB,CAA4BH,IAA5B;AACD;AACF;AACF;AACF,CAbM;;ACnFP;;AACA;AAIA,IAAMI,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,SAAO,OAAOC,iBAAP,KAA6B,WAA7B,GAA2CA,iBAA3C,GAA+D,IAAtE;AACD,CAFD;;ACLA;AAEA,AAIA,IAAMC,YAAY,GAAG,CAArB;AAAwB;;AAExB;;AACA,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACrH,MAAD,EAAkD;AAAA,MACjEsH,UADiE,GAClDtH,MADkD,CACjEsH,UADiE;;AAGzE,OAAK,IAAI5H,CAAC,GAAG4H,UAAU,CAAC3H,MAAxB,EAAgCD,CAAC,IAAI,CAArC,EAAwCA,CAAC,EAAzC,EAA6C;AAC3C,QAAM6H,KAAK,GAAKD,UAAU,CAAC5H,CAAD,CAA1B;;AACA,QAAI6H,KAAK,IAAIA,KAAK,CAACC,QAAN,KAAmBJ,YAA5B,IAA4CG,KAAK,CAACE,YAAN,CAAmBzG,OAAnB,CAAhD,EAA6E;AAC3E,aAASuG,KAAT;AACD;AACF;;AAED,SAAO7B,SAAP;AACD,CAXD;AAaA;;;AACA,AAAO,IAAMgC,YAAY,GAAG,SAAfA,YAAe,CAAC1H,MAAD,EAA4C;AACtE,MAAM2H,IAAI,GAAKf,QAAQ,CAACe,IAAxB;AACA,MAAMC,MAAM,GAAG5H,MAAM,IAAI2H,IAAzB;AACA,MAAMxB,KAAK,GAAGS,QAAQ,CAACiB,aAAT,CAAuB,OAAvB,CAAd;AACA,MAAMC,SAAS,GAAGT,gBAAgB,CAACO,MAAD,CAAlC;AACA,MAAMG,WAAW,GAAGD,SAAS,KAAKpC,SAAd,GAA0BoC,SAAS,CAACC,WAApC,GAAkD,IAAtE;AAEA5B,EAAAA,KAAK,CAAC6B,YAAN,CAAmBhH,OAAnB,EAA4BE,cAA5B;AACAiF,EAAAA,KAAK,CAAC6B,YAAN,CAAmB7G,eAAnB,EAAoCC,UAApC;AAEA,MAAM6G,KAAK,GAAGf,QAAQ,EAAtB;AAEA,MAAIe,KAAJ,EAAW9B,KAAK,CAAC6B,YAAN,CAAmB,OAAnB,EAA4BC,KAA5B;AAEXL,EAAAA,MAAM,CAACM,YAAP,CAAoB/B,KAApB,EAA2B4B,WAA3B;AAEA,SAAO5B,KAAP;AACD,CAjBM;AAmBP;;AACA,AAAO,IAAMgC,QAAQ,GAAG,SAAXA,QAAW,CAACnF,GAAD,EAA0C;AAChE,MAAIA,GAAG,CAACwC,KAAR,EAAe;AACb,WAASxC,GAAG,CAACwC,KAAb;AACD,GAH+D;;;AAAA,kBAMxCoB,QANwC;AAAA,MAMxDwB,WANwD,aAMxDA,WANwD;;AAOhE,OAAK,IAAI1I,CAAC,GAAG,CAAR,EAAWsE,CAAC,GAAGoE,WAAW,CAACzI,MAAhC,EAAwCD,CAAC,GAAGsE,CAA5C,EAA+CtE,CAAC,EAAhD,EAAoD;AAClD,QAAM8F,KAAK,GAAG4C,WAAW,CAAC1I,CAAD,CAAzB;;AACA,QAAI8F,KAAK,CAAC6C,SAAN,KAAoBrF,GAAxB,EAA6B;AAC3B,aAASwC,KAAT;AACD;AACF;;AAED3B,EAAAA,0BAAgB,CAAC,EAAD,CAAhB;AACA,SAAQ6B,SAAR;AACD,CAhBM;;AC3CP;AAMA;;AACA,AAAO,IAAM4C,OAAO,GAAG,SAAVA,OAAU,OAAgE;AAAA,MAA7DC,QAA6D,QAA7DA,QAA6D;AAAA,MAAnDC,iBAAmD,QAAnDA,iBAAmD;AAAA,MAAhCxI,MAAgC,QAAhCA,MAAgC;;AACrF,MAAIuI,QAAJ,EAAc;AACZ,WAAO,IAAIE,UAAJ,CAAezI,MAAf,CAAP;AACD,GAFD,MAEO,IAAIwI,iBAAJ,EAAuB;AAC5B,WAAO,IAAIE,QAAJ,CAAa1I,MAAb,CAAP;AACD,GAFM,MAEA;AACL,WAAO,IAAI2I,OAAJ,CAAY3I,MAAZ,CAAP;AACD;AACF,CARM;AAUP,IAAa0I,QAAb;AAOE,oBAAY1I,MAAZ,EAAkC;AAChC,QAAM4I,OAAO,GAAI,KAAKA,OAAL,GAAelB,YAAY,CAAC1H,MAAD,CAA5C,CADgC;;AAIhC4I,IAAAA,OAAO,CAACC,WAAR,CAAoBjC,QAAQ,CAACkC,cAAT,CAAwB,EAAxB,CAApB;AAEA,SAAKtD,KAAL,GAAa2C,QAAQ,CAACS,OAAD,CAArB;AACA,SAAKjJ,MAAL,GAAc,CAAd;AACD;;AAfH;;AAAA,SAiBEsE,UAjBF,GAiBE,oBAAWV,KAAX,EAA0BwF,IAA1B,EAAiD;AAC/C,QAAI;AACF,WAAKvD,KAAL,CAAWvB,UAAX,CAAsB8E,IAAtB,EAA4BxF,KAA5B;AACA,WAAK5D,MAAL;AACA,aAAO,IAAP;AACD,KAJD,CAIE,OAAOqJ,MAAP,EAAe;AACf,aAAO,KAAP;AACD;AACF,GAzBH;;AAAA,SA2BE3E,UA3BF,GA2BE,oBAAWd,KAAX,EAAgC;AAC9B,SAAKiC,KAAL,CAAWnB,UAAX,CAAsBd,KAAtB;AACA,SAAK5D,MAAL;AACD,GA9BH;;AAAA,SAgCE6E,OAhCF,GAgCE,iBAAQjB,KAAR,EAA+B;AAC7B,QAAMwF,IAAI,GAAG,KAAKvD,KAAL,CAAWyD,QAAX,CAAoB1F,KAApB,CAAb,CAD6B;;AAG7B,QAAIwF,IAAI,KAAKrD,SAAT,IAAsB,OAAOqD,IAAI,CAACG,OAAZ,KAAwB,QAAlD,EAA4D;AAC1D,aAAOH,IAAI,CAACG,OAAZ;AACD,KAFD,MAEO;AACL,aAAO,EAAP;AACD;AACF,GAxCH;;AAAA;AAAA;AA2CA;;AACA,IAAaP,OAAb;AAOE,mBAAY3I,MAAZ,EAAkC;AAChC,QAAM4I,OAAO,GAAI,KAAKA,OAAL,GAAelB,YAAY,CAAC1H,MAAD,CAA5C;AACA,SAAK2G,KAAL,GAAaiC,OAAO,CAACtB,UAArB;AACA,SAAK3H,MAAL,GAAc,CAAd;AACD;;AAXH;;AAAA,UAaEsE,UAbF,GAaE,oBAAWV,KAAX,EAA0BwF,IAA1B,EAAiD;AAC/C,QAAIxF,KAAK,IAAI,KAAK5D,MAAd,IAAwB4D,KAAK,IAAI,CAArC,EAAwC;AACtC,UAAMuD,IAAI,GAAGF,QAAQ,CAACkC,cAAT,CAAwBC,IAAxB,CAAb;AACA,UAAMI,OAAO,GAAG,KAAKxC,KAAL,CAAWpD,KAAX,CAAhB;AACA,WAAKqF,OAAL,CAAaV,YAAb,CAA0BpB,IAA1B,EAAgCqC,OAAO,IAAI,IAA3C;AACA,WAAKxJ,MAAL;AACA,aAAO,IAAP;AACD,KAND,MAMO;AACL,aAAO,KAAP;AACD;AACF,GAvBH;;AAAA,UAyBE0E,UAzBF,GAyBE,oBAAWd,KAAX,EAAgC;AAC9B,SAAKqF,OAAL,CAAa3B,WAAb,CAAyB,KAAKN,KAAL,CAAWpD,KAAX,CAAzB;AACA,SAAK5D,MAAL;AACD,GA5BH;;AAAA,UA8BE6E,OA9BF,GA8BE,iBAAQjB,KAAR,EAA+B;AAC7B,QAAIA,KAAK,GAAG,KAAK5D,MAAjB,EAAyB;AACvB,aAAO,KAAKgH,KAAL,CAAWpD,KAAX,EAAkB8C,WAAzB;AACD,KAFD,MAEO;AACL,aAAO,EAAP;AACD;AACF,GApCH;;AAAA;AAAA;AAuCA;;AACA,IAAaoC,UAAb;AAKE,sBAAYW,OAAZ,EAAmC;AACjC,SAAK3F,KAAL,GAAa,EAAb;AACA,SAAK9D,MAAL,GAAc,CAAd;AACD;;AARH;;AAAA,UAUEsE,UAVF,GAUE,oBAAWV,KAAX,EAA0BwF,IAA1B,EAAiD;AAC/C,QAAIxF,KAAK,IAAI,KAAK5D,MAAlB,EAA0B;AACxB,WAAK8D,KAAL,CAAW4F,MAAX,CAAkB9F,KAAlB,EAAyB,CAAzB,EAA4BwF,IAA5B;AACA,WAAKpJ,MAAL;AACA,aAAO,IAAP;AACD,KAJD,MAIO;AACL,aAAO,KAAP;AACD;AACF,GAlBH;;AAAA,UAoBE0E,UApBF,GAoBE,oBAAWd,KAAX,EAAgC;AAC9B,SAAKE,KAAL,CAAW4F,MAAX,CAAkB9F,KAAlB,EAAyB,CAAzB;AACA,SAAK5D,MAAL;AACD,GAvBH;;AAAA,UAyBE6E,OAzBF,GAyBE,iBAAQjB,KAAR,EAA+B;AAC7B,QAAIA,KAAK,GAAG,KAAK5D,MAAjB,EAAyB;AACvB,aAAO,KAAK8D,KAAL,CAAWF,KAAX,CAAP;AACD,KAFD,MAEO;AACL,aAAO,EAAP;AACD;AACF,GA/BH;;AAAA;AAAA;;AC5FA,IAAI+F,gBAAgB,GAAG/H,UAAvB;AAWA,IAAMgI,cAAc,GAAiB;AACnChB,EAAAA,QAAQ,EAAE,CAAChH,UADwB;AAEnCiH,EAAAA,iBAAiB,EAAE,CAAC/G;AAFe,CAArC;AAKA;;IACqB+H;AAWnB;aACOC,aAAP,oBAAkB1E,EAAlB,EAAsC;AACpC,WAAOD,aAAa,CAACC,EAAD,CAApB;AACD;;AAED,sBACE2E,OADF,EAEEC,YAFF,EAGEhE,KAHF,EAIE;AAAA,QAHA+D,OAGA;AAHAA,MAAAA,OAGA,GAHgC3H,YAGhC;AAAA;;AAAA,QAFA4H,YAEA;AAFAA,MAAAA,YAEA,GAF2C,EAE3C;AAAA;;AACA,SAAKD,OAAL,gBACKH,cADL,MAEKG,OAFL;AAKA,SAAKE,EAAL,GAAUD,YAAV;AACA,SAAKhE,KAAL,GAAa,IAAIhB,GAAJ,CAAQgB,KAAR,CAAb;AACA,SAAKkE,MAAL,GAAc,CAAC,CAACH,OAAO,CAACnB,QAAxB,CARA;;AAWA,QAAI,CAAC,KAAKsB,MAAN,IAAgBtI,UAAhB,IAA8B+H,gBAAlC,EAAoD;AAClDA,MAAAA,gBAAgB,GAAG,KAAnB;AACA5C,MAAAA,cAAc,CAAC,IAAD,CAAd;AACD;AACF;;;;SAEDoD,yBAAA,gCAAuBJ,OAAvB,EAAsDK,SAAtD,EAAkF;AAAA,QAA5BA,SAA4B;AAA5BA,MAAAA,SAA4B,GAAN,IAAM;AAAA;;AAChF,WAAO,IAAIP,UAAJ,cACA,KAAKE,OADL,MACiBA,OADjB,GAEL,KAAKE,EAFA,EAGJG,SAAS,IAAI,KAAKpE,KAAnB,IAA6BD,SAHxB,CAAP;AAKD;;SAEDsE,qBAAA,4BAAmBjF,EAAnB,EAA+B;AAC7B,WAAQ,KAAK6E,EAAL,CAAQ7E,EAAR,IAAc,CAAC,KAAK6E,EAAL,CAAQ7E,EAAR,KAAe,CAAhB,IAAqB,CAA3C;AACD;AAED;;;SACAU,SAAA,kBAAqB;AACnB,WAAO,KAAKzC,GAAL,KAAa,KAAKA,GAAL,GAAWD,cAAc,CAACuF,OAAO,CAAC,KAAKoB,OAAN,CAAR,CAAtC,CAAP;AACD;AAED;;;SACAO,eAAA,sBAAalF,EAAb,EAAyB7F,IAAzB,EAAgD;AAC9C,WAAO,KAAKyG,KAAL,CAAWX,GAAX,CAAeD,EAAf,KAAuB,KAAKY,KAAL,CAAWV,GAAX,CAAeF,EAAf,CAAD,CAA0BC,GAA1B,CAA8B9F,IAA9B,CAA7B;AACD;AAED;;;SACA+G,eAAA,sBAAalB,EAAb,EAAyB7F,IAAzB,EAAuC;AACrC4F,IAAAA,aAAa,CAACC,EAAD,CAAb;;AAEA,QAAI,CAAC,KAAKY,KAAL,CAAWX,GAAX,CAAeD,EAAf,CAAL,EAAyB;AACvB,UAAMmF,UAAU,GAAG,IAAIC,GAAJ,EAAnB;AACAD,MAAAA,UAAU,CAACE,GAAX,CAAelL,IAAf;AACA,WAAKyG,KAAL,CAAW7B,GAAX,CAAeiB,EAAf,EAAmBmF,UAAnB;AACD,KAJD,MAIO;AACJ,WAAKvE,KAAL,CAAWV,GAAX,CAAeF,EAAf,CAAD,CAA0BqF,GAA1B,CAA8BlL,IAA9B;AACD;AACF;AAED;;;SACAsE,cAAA,qBAAYuB,EAAZ,EAAwB7F,IAAxB,EAAsCuE,KAAtC,EAAuD;AACrD,SAAKwC,YAAL,CAAkBlB,EAAlB,EAAsB7F,IAAtB;AACA,SAAKuG,MAAL,GAAcjC,WAAd,CAA0BsB,aAAa,CAACC,EAAD,CAAvC,EAA6CtB,KAA7C;AACD;AAED;;;SACA4G,aAAA,oBAAWtF,EAAX,EAAuB;AACrB,QAAI,KAAKY,KAAL,CAAWX,GAAX,CAAeD,EAAf,CAAJ,EAAwB;AACrB,WAAKY,KAAL,CAAWV,GAAX,CAAeF,EAAf,CAAD,CAA0BuF,KAA1B;AACD;AACF;AAED;;;SACAC,aAAA,oBAAWxF,EAAX,EAAuB;AACrB,SAAKU,MAAL,GAAcvB,UAAd,CAAyBY,aAAa,CAACC,EAAD,CAAtC;AACA,SAAKsF,UAAL,CAAgBtF,EAAhB;AACD;AAED;;;SACAyF,WAAA,oBAAW;AACT;AACA;AACA,SAAKxH,GAAL,GAAW0C,SAAX;AACD;AAED;;;SACAhF,WAAA,oBAAmB;AACjB,WAAO6E,WAAW,CAAC,IAAD,CAAlB;AACD;;;;;AC/HH;;;;;;;;;;;;;;;;;;;;;AAoBA;AAEA,AAAe,2BAAStB,UAAT,EAAqB;AAClC,MAAMwG,SAAS,GAAG,OAAlB;AACA,MAAMC,MAAM,GAAMD,SAAN,MAAZ;;AAEA,WAASE,OAAT,CAAiBC,KAAjB,EAAwB;AACtB,QAAIA,KAAJ,EAAW;AACT,UAAI;AACF3G,QAAAA,UAAU,CAAI2G,KAAJ,OAAV;AACD,OAFD,CAEE,OAAOC,CAAP,EAAU;AACb;AACF;;AAED,SAAO,SAASC,SAAT,CACLC,OADK,EAELjF,OAFK,EAGLkF,SAHK,EAILC,OAJK,EAKLC,IALK,EAMLC,MANK,EAOLxL,MAPK,EAQLyL,EARK,EASLC,KATK,EAULC,EAVK,EAWL;AACA,YAAQP,OAAR;AACE;AACA,WAAK,CAAL;AACE;AACA,YAAIM,KAAK,KAAK,CAAV,IAAevF,OAAO,CAAClG,UAAR,CAAmB,CAAnB,MAA0B,EAA7C,EAAiD,OAAOqE,UAAU,CAAI6B,OAAJ,OAAV,EAA2B,EAAlC;AACjD;AACF;;AACA,WAAK,CAAL;AACE,YAAIsF,EAAE,KAAK,CAAX,EAAc,OAAOtF,OAAO,GAAG2E,SAAjB;AACd;AACF;;AACA,WAAK,CAAL;AACE,gBAAQW,EAAR;AACE;AACA,eAAK,GAAL;AACA,eAAK,GAAL;AACE,mBAAOnH,UAAU,CAAC+G,SAAS,CAAC,CAAD,CAAT,GAAelF,OAAhB,CAAV,EAAoC,EAA3C;;AACF;AACE,mBAAOA,OAAO,IAAIwF,EAAE,KAAK,CAAP,GAAWb,SAAX,GAAuB,EAA3B,CAAd;AANJ;;AAQF,WAAK,CAAC,CAAN;AACE3E,QAAAA,OAAO,CAACE,KAAR,CAAc0E,MAAd,EAAsBlI,OAAtB,CAA8BmI,OAA9B;AArBJ;AAuBD,GAnCD;AAoCD;;AC/DD,IAAMY,aAAa,GAAG,eAAtB;AACA,IAAMC,uBAAuB,GAAG,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAhC;AAOA,AAAe,SAASC,oBAAT,QAGkC;AAAA,gCAAd1J,YAAc;AAAA,0BAF/C2H,OAE+C;AAAA,MAF/CA,OAE+C,6BAFrC3H,YAEqC;AAAA,0BAD/C2J,OAC+C;AAAA,MAD/CA,OAC+C,6BADrC7J,WACqC;;AAC/C,MAAM8J,MAAM,GAAG,IAAIC,MAAJ,CAAWlC,OAAX,CAAf,CAD+C;AAI/C;AACA;;AAEA,MAAImC,YAAY,GAAG,EAAnB,CAP+C;;AAU/C,MAAMC,iBAAiB,GAAG,SAApBA,iBAAoB,CAAAf,OAAO,EAAI;AACnC,QAAIA,OAAO,KAAK,CAAC,CAAjB,EAAoB;AAClB,UAAMgB,WAAW,GAAGF,YAApB;AACAA,MAAAA,YAAY,GAAG,EAAf;AACA,aAAOE,WAAP;AACD;AACF,GAND;;AAQA,MAAMC,gBAAgB,GAAGC,gBAAgB,CAAC,UAAAlD,IAAI,EAAI;AAChD8C,IAAAA,YAAY,CAACtJ,IAAb,CAAkBwG,IAAlB;AACD,GAFwC,CAAzC;;AAIA,MAAImD,YAAJ;;AACA,MAAIC,SAAJ;;AACA,MAAIC,eAAJ;;AACA,MAAIC,yBAAJ;;AAEA,MAAMC,qBAAqB,GAAG,SAAxBA,qBAAwB,CAAC9F,KAAD,EAAQ+F,MAAR,EAAgBC,MAAhB,EAA2B;AACvD;AAEE,KAACD,MAAM,KAAK,CAAX,GAAef,uBAAuB,CAACiB,OAAxB,CAAgCD,MAAM,CAACL,SAAS,CAACxM,MAAX,CAAtC,MAA8D,CAAC,CAA9E,GAAkF,IAAnF;AAEA,KAAC6M,MAAM,CAAChG,KAAP,CAAa6F,yBAAb,CAJH,EAKE;AACA,mBAAWH,YAAX;AACD;;AAED,WAAO1F,KAAP;AACD,GAXD;AAaA;;;;;;;;;;;;;;;AAaA,MAAMkG,8BAA8B,GAAG,SAAjCA,8BAAiC,CAAC3B,OAAD,EAAU4B,CAAV,EAAa3B,SAAb,EAA2B;AAChE,QAAID,OAAO,KAAK,CAAZ,IAAiBC,SAAS,CAACrL,MAA3B,IAAqCqL,SAAS,CAAC,CAAD,CAAT,CAAa4B,WAAb,CAAyBT,SAAzB,IAAsC,CAA/E,EAAkF;AAChF;AACAnB,MAAAA,SAAS,CAAC,CAAD,CAAT,GAAeA,SAAS,CAAC,CAAD,CAAT,CAAa1L,OAAb,CAAqB8M,eAArB,EAAsCE,qBAAtC,CAAf;AACD;AACF,GALD;;AAOAX,EAAAA,MAAM,CAACkB,GAAP,WAAenB,OAAf,GAAwBgB,8BAAxB,EAAwDV,gBAAxD,EAA0EF,iBAA1E;;AAEA,WAASgB,cAAT,CAAwBvI,GAAxB,EAA6BsB,QAA7B,EAAuCkH,MAAvC,EAA+CC,WAA/C,EAA+E;AAAA,QAAhCA,WAAgC;AAAhCA,MAAAA,WAAgC,GAAlB,GAAkB;AAAA;;AAC7E,QAAMC,OAAO,GAAG1I,GAAG,CAACjF,OAAJ,CAAYiM,aAAZ,EAA2B,EAA3B,CAAhB;AACA,QAAM2B,MAAM,GAAGrH,QAAQ,IAAIkH,MAAZ,GAAwBA,MAAxB,SAAkClH,QAAlC,WAAgDoH,OAAhD,UAA8DA,OAA7E,CAF6E;AAK7E;AACA;;AACAf,IAAAA,YAAY,GAAGc,WAAf;AACAb,IAAAA,SAAS,GAAGtG,QAAZ;AACAuG,IAAAA,eAAe,GAAG,IAAI9G,MAAJ,QAAgB6G,SAAhB,UAAgC,GAAhC,CAAlB;AACAE,IAAAA,yBAAyB,GAAG,IAAI/G,MAAJ,SAAiB6G,SAAjB,cAA5B;AAEA,WAAOR,MAAM,CAACoB,MAAM,IAAI,CAAClH,QAAX,GAAsB,EAAtB,GAA2BA,QAA5B,EAAsCqH,MAAtC,CAAb;AACD;;AAEDJ,EAAAA,cAAc,CAACjN,IAAf,GAAsB6L,OAAO,CAAC/L,MAAR,GAClB+L,OAAO,CACJyB,MADH,CACU,UAACC,GAAD,EAAMC,MAAN,EAAiB;AACvB,QAAI,CAACA,MAAM,CAACnO,IAAZ,EAAkB;AAChB2E,MAAAA,0BAAgB,CAAC,EAAD,CAAhB;AACD;;AAED,WAAOrE,KAAK,CAAC4N,GAAD,EAAMC,MAAM,CAACnO,IAAb,CAAZ;AACD,GAPH,EAOKK,IAPL,EAQGmB,QARH,EADkB,GAUlB,EAVJ;AAYA,SAAOoM,cAAP;AACD;;AC5GD;AACA,AAeO,IAAMQ,iBAAiB,GAA+BC,KAAK,CAACC,aAAN,EAAtD;AACP,AAAO,IAAMC,kBAAkB,GAAGH,iBAAiB,CAACI,QAA7C;AACP,AAAO,IAAMC,aAAa,GAAgCJ,KAAK,CAACC,aAAN,EAAnD;AACP,AAAO,IAAMI,cAAc,GAAGD,aAAa,CAACD,QAArC;AAEP,AAAO,IAAMG,WAAW,GAAe,IAAIrE,UAAJ,EAAhC;AACP,AAAO,IAAMsE,YAAY,GAAgBrC,oBAAoB,EAAtD;;ACtBP;AACA;IAKqBsC;AAOnB,qBAAY7O,IAAZ,EAA0BuE,KAA1B,EAAyC;AAAA;;AAAA,SAMzCuK,MANyC,GAMhC,UAACC,UAAD,EAAyBC,cAAzB,EAAwE;AAAA,UAA/CA,cAA+C;AAA/CA,QAAAA,cAA+C,GAAjBJ,YAAiB;AAAA;;AAC/E,UAAMK,YAAY,GAAG,KAAI,CAACjP,IAAL,GAAYgP,cAAc,CAACrO,IAAhD;;AAEA,UAAI,CAACoO,UAAU,CAAChE,YAAX,CAAwB,KAAI,CAAClF,EAA7B,EAAiCoJ,YAAjC,CAAL,EAAqD;AACnDF,QAAAA,UAAU,CAACzK,WAAX,CACE,KAAI,CAACuB,EADP,EAEEoJ,YAFF,EAGED,cAAc,CAAC,KAAI,CAACzK,KAAN,EAAa0K,YAAb,EAA2B,YAA3B,CAHhB;AAKD;AACF,KAhBwC;;AAAA,SAkBzCzN,QAlByC,GAkB9B,YAAM;AACf,aAAOmD,0BAAgB,CAAC,EAAD,EAAK9E,MAAM,CAAC,KAAI,CAACG,IAAN,CAAX,CAAvB;AACD,KApBwC;;AACvC,SAAKA,IAAL,GAAYA,IAAZ;AACA,SAAK6F,EAAL,qBAA0B7F,IAA1B;AACA,SAAKuE,KAAL,GAAaA,KAAb;AACD;;;;SAkBD2K,UAAA,iBAAQF,cAAR,EAAoD;AAAA,QAA5CA,cAA4C;AAA5CA,MAAAA,cAA4C,GAAdJ,YAAc;AAAA;;AAClD,WAAO,KAAK5O,IAAL,GAAYgP,cAAc,CAACrO,IAAlC;AACD;;;;;ACrCH;;AAEA;;;;AAKA,IAAMwO,cAAc,GAAG,SAAvB;AACA,IAAMC,gBAAgB,GAAG,UAAzB;AACA,IAAMC,SAAS,GAAG,MAAlB;;AACA,IAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,CAACC,KAAD;AAAA,eAA8BA,KAAI,CAACC,WAAL,EAA9B;AAAA,CAA3B;AAEA;;;;;;;;;;;;;;;;;;AAgBA,AAAe,SAASC,kBAAT,CAA4BnC,MAA5B,EAAoD;AACjE,SAAO6B,cAAc,CAAC/N,IAAf,CAAoBkM,MAApB,IACLA,MAAM,CACLlN,OADD,CACSgP,gBADT,EAC2BE,kBAD3B,EAEClP,OAFD,CAESiP,SAFT,EAEoB,MAFpB,CADK,GAIL/B,MAJF;AAKD;;AClCD;AACA;AAGA,AAAe,SAASoC,eAAT,CAAyB1P,IAAzB,EAAuC2P,KAAvC,EAAwD;AACrE;AACA;AACA,MAAIA,KAAK,IAAI,IAAT,IAAiB,OAAOA,KAAP,KAAiB,SAAlC,IAA+CA,KAAK,KAAK,EAA7D,EAAiE;AAC/D,WAAO,EAAP;AACD;;AAED,MAAI,OAAOA,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,KAAK,CAAvC,IAA4C,EAAE3P,IAAI,IAAI4P,QAAV,CAA5C,IAAmE,CAAC5P,IAAI,CAAC6P,UAAL,CAAgB,IAAhB,CAAxE,EAA+F;AAC7F,WAAUF,KAAV,QAD6F;AAE9F;;AAED,SAAO9P,MAAM,CAAC8P,KAAD,CAAN,CAAc/L,IAAd,EAAP;AACD;;AChBD;AACA,AAWA;;;;AAGA,IAAMkM,SAAS,GAAG,SAAZA,SAAY,CAAAC,KAAK;AAAA,SAAIA,KAAK,KAAKvJ,SAAV,IAAuBuJ,KAAK,KAAK,IAAjC,IAAyCA,KAAK,KAAK,KAAnD,IAA4DA,KAAK,KAAK,EAA1E;AAAA,CAAvB;;AAEA,AAAO,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAACC,GAAD,EAAcC,OAAd,EAA6D;AACxF,MAAM3L,KAAK,GAAG,EAAd;;AAEA,OAAK,IAAM4L,GAAX,IAAkBF,GAAlB,EAAuB;AACrB,QAAI,CAACA,GAAG,CAACG,cAAJ,CAAmBD,GAAnB,CAAD,IAA4BL,SAAS,CAACG,GAAG,CAACE,GAAD,CAAJ,CAAzC,EAAqD;;AAErD,QAAKE,KAAK,CAACC,OAAN,CAAcL,GAAG,CAACE,GAAD,CAAjB,KAA2BF,GAAG,CAACE,GAAD,CAAH,CAASI,KAArC,IAA+CpP,UAAU,CAAC8O,GAAG,CAACE,GAAD,CAAJ,CAA7D,EAAyE;AACvE5L,MAAAA,KAAK,CAAClB,IAAN,CAAcmN,kBAAS,CAACL,GAAD,CAAvB,QAAiCF,GAAG,CAACE,GAAD,CAApC,EAA2C,GAA3C;AACD,KAFD,MAEO,IAAIM,aAAa,CAACR,GAAG,CAACE,GAAD,CAAJ,CAAjB,EAA6B;AAClC5L,MAAAA,KAAK,CAAClB,IAAN,OAAAkB,KAAK,EAASyL,aAAa,CAACC,GAAG,CAACE,GAAD,CAAJ,EAAWA,GAAX,CAAtB,CAAL;AACD,KAFM,MAEA;AACL5L,MAAAA,KAAK,CAAClB,IAAN,CAAcmN,kBAAS,CAACL,GAAD,CAAvB,UAAiCT,eAAe,CAACS,GAAD,EAAMF,GAAG,CAACE,GAAD,CAAT,CAAhD;AACD;AACF;;AAED,SAAOD,OAAO,IAAOA,OAAP,gBAAuB3L,KAAvB,GAA8B,GAA9B,KAAqCA,KAAnD;AACD,CAhBM;AAkBP,AAAe,SAASmM,OAAT,CACbX,KADa,EAEbY,gBAFa,EAGb5B,UAHa,EAIbC,cAJa,EAKR;AACL,MAAIqB,KAAK,CAACC,OAAN,CAAcP,KAAd,CAAJ,EAA0B;AACxB,QAAMa,OAAO,GAAG,EAAhB;;AAEA,SAAK,IAAIpQ,CAAC,GAAG,CAAR,EAAW4C,GAAG,GAAG2M,KAAK,CAACtP,MAAvB,EAA+BoQ,MAApC,EAA4CrQ,CAAC,GAAG4C,GAAhD,EAAqD5C,CAAC,IAAI,CAA1D,EAA6D;AAC3DqQ,MAAAA,MAAM,GAAGH,OAAO,CAACX,KAAK,CAACvP,CAAD,CAAN,EAAWmQ,gBAAX,EAA6B5B,UAA7B,EAAyCC,cAAzC,CAAhB;AAEA,UAAI6B,MAAM,KAAK,EAAf,EAAmB,SAAnB,KACK,IAAIR,KAAK,CAACC,OAAN,CAAcO,MAAd,CAAJ,EAA2BD,OAAO,CAACvN,IAAR,OAAAuN,OAAO,EAASC,MAAT,CAAP,CAA3B,KACAD,OAAO,CAACvN,IAAR,CAAawN,MAAb;AACN;;AAED,WAAOD,OAAP;AACD;;AAED,MAAId,SAAS,CAACC,KAAD,CAAb,EAAsB;AACpB,WAAO,EAAP;AACD;AAED;;;AACA,MAAInO,iBAAiB,CAACmO,KAAD,CAArB,EAA8B;AAC5B,iBAAWA,KAAK,CAAClO,iBAAjB;AACD;AAED;;;AACA,MAAIV,UAAU,CAAC4O,KAAD,CAAd,EAAuB;AACrB,QAAI1O,mBAAmB,CAAC0O,KAAD,CAAnB,IAA8BY,gBAAlC,EAAoD;AAClD,UAAME,OAAM,GAAGd,KAAK,CAACY,gBAAD,CAApB;;AAEA,UAAI5P,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAAzB,IAAyC6P,SAAS,CAACD,OAAD,CAAtD,EAAgE;AAC9D;AACAE,QAAAA,OAAO,CAACC,IAAR,CACKnQ,gBAAgB,CACjBkP,KADiB,CADrB;AAKD;;AAED,aAAOW,OAAO,CAACG,OAAD,EAASF,gBAAT,EAA2B5B,UAA3B,EAAuCC,cAAvC,CAAd;AACD,KAbD,MAaO,OAAOe,KAAP;AACR;;AAED,MAAIA,KAAK,YAAYlB,SAArB,EAAgC;AAC9B,QAAIE,UAAJ,EAAgB;AACdgB,MAAAA,KAAK,CAACjB,MAAN,CAAaC,UAAb,EAAyBC,cAAzB;AACA,aAAOe,KAAK,CAACb,OAAN,CAAcF,cAAd,CAAP;AACD,KAHD,MAGO,OAAOe,KAAP;AACR;AAED;;;AACA,SAAOU,aAAa,CAACV,KAAD,CAAb,GAAuBC,aAAa,CAACD,KAAD,CAApC,GAA8CA,KAAK,CAACvO,QAAN,EAArD;AACD;;AC3FD;AACA,IAAMyP,OAAO,GAAG,EAAhB;AAEA,AAAe,SAASC,QAAT,CAAkBC,OAAlB,EAA2B;AACxC,MAAIF,OAAO,CAACE,OAAD,CAAX,EAAsB;AACtBF,EAAAA,OAAO,CAACE,OAAD,CAAP,GAAmB,IAAnB;AAEA,MAAI,OAAOJ,OAAP,KAAmB,WAAnB,IAAkCA,OAAO,CAACC,IAA9C,EAAoDD,OAAO,CAACC,IAAR,CAAaG,OAAb;AACrD;;ACRD;AACA,IAAMC,YAAY,GAAG,IAAI1Q,UAAJ,CAAe,CAAf,CAArB;AACA,IAAM2Q,YAAY,GAAG,IAAI3Q,UAAJ,CAAe,CAAf,CAArB;AACA,IAAM4Q,SAAS,GAAG,KAAK5Q,UAAL,CAAgB,CAAhB,CAAlB;AACA,IAAM6Q,KAAK,GAAG,IAAI7Q,UAAJ,CAAe,CAAf,CAAd;AACA,IAAM8Q,OAAO,GAAG,KAAK9Q,UAAL,CAAgB,CAAhB,CAAhB;AACA,IAAM+Q,KAAK,GAAG,IAAI/Q,UAAJ,CAAe,CAAf,CAAd;AACA,IAAMgR,IAAI,GAAG,KAAKhR,UAAL,CAAgB,CAAhB,CAAb;AACA,IAAMiR,GAAG,GAAG,KAAKjR,UAAL,CAAgB,CAAhB,CAAZ;AACA,IAAMkR,EAAE,GAAG,KAAKlR,UAAL,CAAgB,CAAhB,CAAX;AACA,IAAMmR,WAAW,GAAG,IAAInR,UAAJ,CAAe,CAAf,CAApB;AACA,IAAMoR,YAAY,GAAG,IAAIpR,UAAJ,CAAe,CAAf,CAArB;AACA,IAAMqR,gBAAgB,GAAG,IAAIrR,UAAJ,CAAe,CAAf,CAAzB;AACA,IAAMsR,iBAAiB,GAAG,IAAItR,UAAJ,CAAe,CAAf,CAA1B;AACA,IAAMuR,UAAU,GAAG,IAAIvR,UAAJ,CAAe,CAAf,CAAnB;AACA,IAAMwR,WAAW,GAAG,IAAIxR,UAAJ,CAAe,CAAf,CAApB;AACA,IAAMyR,SAAS,GAAG,IAAIzR,UAAJ,CAAe,CAAf,CAAlB;AACA,IAAM0R,QAAQ,GAAG,IAAI1R,UAAJ,CAAe,CAAf,CAAjB;AACA,IAAM2R,KAAK,GAAG,IAAI3R,UAAJ,CAAe,CAAf,CAAd;AACA,IAAM4R,EAAE,GAAG,IAAI5R,UAAJ,CAAe,CAAf,CAAX;AAEA,IAAM6R,SAAS,GAAG,+BAAlB;AACA,IAAMC,WAAW,GAAG,4CAApB;AACA,IAAMC,cAAc,GAAG,eAAvB;AAEA,AAAe,SAASC,QAAT,CAAkBC,KAAlB,EAAyBnI,OAAzB,EAAuC;AAAA,MAAdA,OAAc;AAAdA,IAAAA,OAAc,GAAJ,EAAI;AAAA;;AACpD,MAAMoI,MAAM,GAAG,EAAf;AACA,MAAMvN,GAAG,GAAGsN,KAAK,CAACtN,GAAN,CAAUwN,OAAV,EAAZ;AAEA,MAAMC,MAAM,GAAGtI,OAAO,CAACuI,YAAvB;AAEA,MAAInT,IAAJ,EACEoT,IADF,EAEEC,KAFF,EAGEC,KAHF,EAIEC,IAJF,EAKEvM,OALF,EAMEwM,MANF,EAOEC,QAPF,EAQEC,UARF,EASEC,OATF,EAUEC,SAVF,EAWEC,IAXF,EAYEC,CAZF;AAcA,MAAMjT,MAAM,GAAG4E,GAAG,CAAC5E,MAAnB;AACA,MAAI4M,MAAM,GAAG,CAAC,CAAd;AACA,MAAIrB,IAAI,GAAG,CAAX;AACA,MAAI2H,GAAG,GAAG,CAAV;;AAEA,WAASC,QAAT,CAAkBC,IAAlB,EAAwB;AACtB,UAAMlB,KAAK,CAACmB,KAAN,eAAwBD,IAAxB,EAAgC7H,IAAhC,EAAsC2H,GAAG,GAAGtG,MAA5C,CAAN;AACD;;AAED,SAAOsG,GAAG,GAAGlT,MAAb,EAAqB;AACnBb,IAAAA,IAAI,GAAGyF,GAAG,CAAC3E,UAAJ,CAAeiT,GAAf,CAAP;;AAEA,QAAI/T,IAAI,KAAK4R,OAAT,IAAoB5R,IAAI,KAAK8R,IAA7B,IAAsC9R,IAAI,KAAKgS,EAAT,IAAevM,GAAG,CAAC3E,UAAJ,CAAeiT,GAAG,GAAG,CAArB,MAA4BnC,OAArF,EAA+F;AAC7FnE,MAAAA,MAAM,GAAGsG,GAAT;AACA3H,MAAAA,IAAI,IAAI,CAAR;AACD;;AAED,YAAQpM,IAAR;AACE,WAAK4R,OAAL;AACA,WAAKC,KAAL;AACA,WAAKE,GAAL;AACA,WAAKC,EAAL;AACA,WAAKF,IAAL;AACEsB,QAAAA,IAAI,GAAGW,GAAP;;AACA,WAAG;AACDX,UAAAA,IAAI,IAAI,CAAR;AACApT,UAAAA,IAAI,GAAGyF,GAAG,CAAC3E,UAAJ,CAAesS,IAAf,CAAP;;AACA,cAAIpT,IAAI,KAAK4R,OAAb,EAAsB;AACpBnE,YAAAA,MAAM,GAAG2F,IAAT;AACAhH,YAAAA,IAAI,IAAI,CAAR;AACD;AACF,SAPD,QAQEpM,IAAI,KAAK6R,KAAT,IACA7R,IAAI,KAAK4R,OADT,IAEA5R,IAAI,KAAK+R,GAFT,IAGA/R,IAAI,KAAKgS,EAHT,IAIAhS,IAAI,KAAK8R,IAZX;;AAeAkB,QAAAA,MAAM,CAACvP,IAAP,CAAY,CAAC,OAAD,EAAUgC,GAAG,CAAC0O,KAAJ,CAAUJ,GAAV,EAAeX,IAAf,CAAV,CAAZ;AACAW,QAAAA,GAAG,GAAGX,IAAI,GAAG,CAAb;AACA;;AAEF,WAAKnB,WAAL;AACEe,QAAAA,MAAM,CAACvP,IAAP,CAAY,CAAC,GAAD,EAAM,GAAN,EAAW2I,IAAX,EAAiB2H,GAAG,GAAGtG,MAAvB,CAAZ;AACA;;AAEF,WAAKyE,YAAL;AACEc,QAAAA,MAAM,CAACvP,IAAP,CAAY,CAAC,GAAD,EAAM,GAAN,EAAW2I,IAAX,EAAiB2H,GAAG,GAAGtG,MAAvB,CAAZ;AACA;;AAEF,WAAK4E,UAAL;AACEW,QAAAA,MAAM,CAACvP,IAAP,CAAY,CAAC,GAAD,EAAM,GAAN,EAAW2I,IAAX,EAAiB2H,GAAG,GAAGtG,MAAvB,CAAZ;AACA;;AAEF,WAAK6E,WAAL;AACEU,QAAAA,MAAM,CAACvP,IAAP,CAAY,CAAC,GAAD,EAAM,GAAN,EAAW2I,IAAX,EAAiB2H,GAAG,GAAGtG,MAAvB,CAAZ;AACA;;AAEF,WAAKgF,KAAL;AACEO,QAAAA,MAAM,CAACvP,IAAP,CAAY,CAAC,GAAD,EAAM,GAAN,EAAW2I,IAAX,EAAiB2H,GAAG,GAAGtG,MAAvB,CAAZ;AACA;;AAEF,WAAK8E,SAAL;AACES,QAAAA,MAAM,CAACvP,IAAP,CAAY,CAAC,GAAD,EAAM,GAAN,EAAW2I,IAAX,EAAiB2H,GAAG,GAAGtG,MAAvB,CAAZ;AACA;;AAEF,WAAK0E,gBAAL;AACE0B,QAAAA,IAAI,GAAGb,MAAM,CAACnS,MAAP,GAAgBmS,MAAM,CAACA,MAAM,CAACnS,MAAP,GAAgB,CAAjB,CAAN,CAA0B,CAA1B,CAAhB,GAA+C,EAAtD;AACAiT,QAAAA,CAAC,GAAGrO,GAAG,CAAC3E,UAAJ,CAAeiT,GAAG,GAAG,CAArB,CAAJ;;AACA,YACEF,IAAI,KAAK,KAAT,IACAC,CAAC,KAAKtC,YADN,IAEAsC,CAAC,KAAKrC,YAFN,IAGAqC,CAAC,KAAKjC,KAHN,IAIAiC,CAAC,KAAKlC,OAJN,IAKAkC,CAAC,KAAK/B,GALN,IAMA+B,CAAC,KAAKhC,IANN,IAOAgC,CAAC,KAAK9B,EARR,EASE;AACAoB,UAAAA,IAAI,GAAGW,GAAP;;AACA,aAAG;AACDJ,YAAAA,OAAO,GAAG,KAAV;AACAP,YAAAA,IAAI,GAAG3N,GAAG,CAACkI,OAAJ,CAAY,GAAZ,EAAiByF,IAAI,GAAG,CAAxB,CAAP;;AACA,gBAAIA,IAAI,KAAK,CAAC,CAAd,EAAiB;AACf,kBAAIF,MAAJ,EAAY;AACVE,gBAAAA,IAAI,GAAGW,GAAP;AACA;AACD,eAHD,MAGO;AACLC,gBAAAA,QAAQ,CAAC,SAAD,CAAR;AACD;AACF;;AACDJ,YAAAA,SAAS,GAAGR,IAAZ;;AACA,mBAAO3N,GAAG,CAAC3E,UAAJ,CAAe8S,SAAS,GAAG,CAA3B,MAAkClC,SAAzC,EAAoD;AAClDkC,cAAAA,SAAS,IAAI,CAAb;AACAD,cAAAA,OAAO,GAAG,CAACA,OAAX;AACD;AACF,WAhBD,QAgBSA,OAhBT;;AAkBAX,UAAAA,MAAM,CAACvP,IAAP,CAAY,CACV,UADU,EAEVgC,GAAG,CAAC0O,KAAJ,CAAUJ,GAAV,EAAeX,IAAI,GAAG,CAAtB,CAFU,EAGVhH,IAHU,EAIV2H,GAAG,GAAGtG,MAJI,EAKVrB,IALU,EAMVgH,IAAI,GAAG3F,MANG,CAAZ;AAQAsG,UAAAA,GAAG,GAAGX,IAAN;AACD,SAtCD,MAsCO;AACLA,UAAAA,IAAI,GAAG3N,GAAG,CAACkI,OAAJ,CAAY,GAAZ,EAAiBoG,GAAG,GAAG,CAAvB,CAAP;AACA/M,UAAAA,OAAO,GAAGvB,GAAG,CAAC0O,KAAJ,CAAUJ,GAAV,EAAeX,IAAI,GAAG,CAAtB,CAAV;;AAEA,cAAIA,IAAI,KAAK,CAAC,CAAV,IAAeP,cAAc,CAACrR,IAAf,CAAoBwF,OAApB,CAAnB,EAAiD;AAC/CgM,YAAAA,MAAM,CAACvP,IAAP,CAAY,CAAC,GAAD,EAAM,GAAN,EAAW2I,IAAX,EAAiB2H,GAAG,GAAGtG,MAAvB,CAAZ;AACD,WAFD,MAEO;AACLuF,YAAAA,MAAM,CAACvP,IAAP,CAAY,CAAC,UAAD,EAAauD,OAAb,EAAsBoF,IAAtB,EAA4B2H,GAAG,GAAGtG,MAAlC,EAA0CrB,IAA1C,EAAgDgH,IAAI,GAAG3F,MAAvD,CAAZ;AACAsG,YAAAA,GAAG,GAAGX,IAAN;AACD;AACF;;AAED;;AAEF,WAAKhB,iBAAL;AACEY,QAAAA,MAAM,CAACvP,IAAP,CAAY,CAAC,GAAD,EAAM,GAAN,EAAW2I,IAAX,EAAiB2H,GAAG,GAAGtG,MAAvB,CAAZ;AACA;;AAEF,WAAK+D,YAAL;AACA,WAAKC,YAAL;AACE4B,QAAAA,KAAK,GAAGrT,IAAI,KAAKwR,YAAT,GAAwB,GAAxB,GAA8B,GAAtC;AACA4B,QAAAA,IAAI,GAAGW,GAAP;;AACA,WAAG;AACDJ,UAAAA,OAAO,GAAG,KAAV;AACAP,UAAAA,IAAI,GAAG3N,GAAG,CAACkI,OAAJ,CAAY0F,KAAZ,EAAmBD,IAAI,GAAG,CAA1B,CAAP;;AACA,cAAIA,IAAI,KAAK,CAAC,CAAd,EAAiB;AACf,gBAAIF,MAAJ,EAAY;AACVE,cAAAA,IAAI,GAAGW,GAAG,GAAG,CAAb;AACA;AACD,aAHD,MAGO;AACLC,cAAAA,QAAQ,CAAC,OAAD,CAAR;AACD;AACF;;AACDJ,UAAAA,SAAS,GAAGR,IAAZ;;AACA,iBAAO3N,GAAG,CAAC3E,UAAJ,CAAe8S,SAAS,GAAG,CAA3B,MAAkClC,SAAzC,EAAoD;AAClDkC,YAAAA,SAAS,IAAI,CAAb;AACAD,YAAAA,OAAO,GAAG,CAACA,OAAX;AACD;AACF,SAhBD,QAgBSA,OAhBT;;AAkBA3M,QAAAA,OAAO,GAAGvB,GAAG,CAAC0O,KAAJ,CAAUJ,GAAV,EAAeX,IAAI,GAAG,CAAtB,CAAV;AACAE,QAAAA,KAAK,GAAGtM,OAAO,CAACE,KAAR,CAAc,IAAd,CAAR;AACAqM,QAAAA,IAAI,GAAGD,KAAK,CAACzS,MAAN,GAAe,CAAtB;;AAEA,YAAI0S,IAAI,GAAG,CAAX,EAAc;AACZE,UAAAA,QAAQ,GAAGrH,IAAI,GAAGmH,IAAlB;AACAG,UAAAA,UAAU,GAAGN,IAAI,GAAGE,KAAK,CAACC,IAAD,CAAL,CAAY1S,MAAhC;AACD,SAHD,MAGO;AACL4S,UAAAA,QAAQ,GAAGrH,IAAX;AACAsH,UAAAA,UAAU,GAAGjG,MAAb;AACD;;AAEDuF,QAAAA,MAAM,CAACvP,IAAP,CAAY,CACV,QADU,EAEVgC,GAAG,CAAC0O,KAAJ,CAAUJ,GAAV,EAAeX,IAAI,GAAG,CAAtB,CAFU,EAGVhH,IAHU,EAIV2H,GAAG,GAAGtG,MAJI,EAKVgG,QALU,EAMVL,IAAI,GAAGM,UANG,CAAZ;AASAjG,QAAAA,MAAM,GAAGiG,UAAT;AACAtH,QAAAA,IAAI,GAAGqH,QAAP;AACAM,QAAAA,GAAG,GAAGX,IAAN;AACA;;AAEF,WAAKV,EAAL;AACEC,QAAAA,SAAS,CAACyB,SAAV,GAAsBL,GAAG,GAAG,CAA5B;AACApB,QAAAA,SAAS,CAACnR,IAAV,CAAeiE,GAAf;;AACA,YAAIkN,SAAS,CAACyB,SAAV,KAAwB,CAA5B,EAA+B;AAC7BhB,UAAAA,IAAI,GAAG3N,GAAG,CAAC5E,MAAJ,GAAa,CAApB;AACD,SAFD,MAEO;AACLuS,UAAAA,IAAI,GAAGT,SAAS,CAACyB,SAAV,GAAsB,CAA7B;AACD;;AACDpB,QAAAA,MAAM,CAACvP,IAAP,CAAY,CAAC,SAAD,EAAYgC,GAAG,CAAC0O,KAAJ,CAAUJ,GAAV,EAAeX,IAAI,GAAG,CAAtB,CAAZ,EAAsChH,IAAtC,EAA4C2H,GAAG,GAAGtG,MAAlD,EAA0DrB,IAA1D,EAAgEgH,IAAI,GAAG3F,MAAvE,CAAZ;AACAsG,QAAAA,GAAG,GAAGX,IAAN;AACA;;AAEF,WAAK1B,SAAL;AACE0B,QAAAA,IAAI,GAAGW,GAAP;AACAP,QAAAA,MAAM,GAAG,IAAT;;AACA,eAAO/N,GAAG,CAAC3E,UAAJ,CAAesS,IAAI,GAAG,CAAtB,MAA6B1B,SAApC,EAA+C;AAC7C0B,UAAAA,IAAI,IAAI,CAAR;AACAI,UAAAA,MAAM,GAAG,CAACA,MAAV;AACD;;AACDxT,QAAAA,IAAI,GAAGyF,GAAG,CAAC3E,UAAJ,CAAesS,IAAI,GAAG,CAAtB,CAAP;;AACA,YACEI,MAAM,IACLxT,IAAI,KAAK2R,KAAT,IACC3R,IAAI,KAAK6R,KADV,IAEC7R,IAAI,KAAK4R,OAFV,IAGC5R,IAAI,KAAK+R,GAHV,IAIC/R,IAAI,KAAKgS,EAJV,IAKChS,IAAI,KAAK8R,IAPb,EAQE;AACAsB,UAAAA,IAAI,IAAI,CAAR;AACD;;AACDJ,QAAAA,MAAM,CAACvP,IAAP,CAAY,CAAC,MAAD,EAASgC,GAAG,CAAC0O,KAAJ,CAAUJ,GAAV,EAAeX,IAAI,GAAG,CAAtB,CAAT,EAAmChH,IAAnC,EAAyC2H,GAAG,GAAGtG,MAA/C,EAAuDrB,IAAvD,EAA6DgH,IAAI,GAAG3F,MAApE,CAAZ;AACAsG,QAAAA,GAAG,GAAGX,IAAN;AACA;;AAEF;AACE,YAAIpT,IAAI,KAAK2R,KAAT,IAAkBlM,GAAG,CAAC3E,UAAJ,CAAeiT,GAAG,GAAG,CAArB,MAA4BvB,QAAlD,EAA4D;AAC1DY,UAAAA,IAAI,GAAG3N,GAAG,CAACkI,OAAJ,CAAY,IAAZ,EAAkBoG,GAAG,GAAG,CAAxB,IAA6B,CAApC;;AACA,cAAIX,IAAI,KAAK,CAAb,EAAgB;AACd,gBAAIF,MAAJ,EAAY;AACVE,cAAAA,IAAI,GAAG3N,GAAG,CAAC5E,MAAX;AACD,aAFD,MAEO;AACLmT,cAAAA,QAAQ,CAAC,SAAD,CAAR;AACD;AACF;;AAEDhN,UAAAA,OAAO,GAAGvB,GAAG,CAAC0O,KAAJ,CAAUJ,GAAV,EAAeX,IAAI,GAAG,CAAtB,CAAV;AACAE,UAAAA,KAAK,GAAGtM,OAAO,CAACE,KAAR,CAAc,IAAd,CAAR;AACAqM,UAAAA,IAAI,GAAGD,KAAK,CAACzS,MAAN,GAAe,CAAtB;;AAEA,cAAI0S,IAAI,GAAG,CAAX,EAAc;AACZE,YAAAA,QAAQ,GAAGrH,IAAI,GAAGmH,IAAlB;AACAG,YAAAA,UAAU,GAAGN,IAAI,GAAGE,KAAK,CAACC,IAAD,CAAL,CAAY1S,MAAhC;AACD,WAHD,MAGO;AACL4S,YAAAA,QAAQ,GAAGrH,IAAX;AACAsH,YAAAA,UAAU,GAAGjG,MAAb;AACD;;AAEDuF,UAAAA,MAAM,CAACvP,IAAP,CAAY,CAAC,SAAD,EAAYuD,OAAZ,EAAqBoF,IAArB,EAA2B2H,GAAG,GAAGtG,MAAjC,EAAyCgG,QAAzC,EAAmDL,IAAI,GAAGM,UAA1D,CAAZ;AAEAjG,UAAAA,MAAM,GAAGiG,UAAT;AACAtH,UAAAA,IAAI,GAAGqH,QAAP;AACAM,UAAAA,GAAG,GAAGX,IAAN;AACD,SA3BD,MA2BO;AACLR,UAAAA,WAAW,CAACwB,SAAZ,GAAwBL,GAAG,GAAG,CAA9B;AACAnB,UAAAA,WAAW,CAACpR,IAAZ,CAAiBiE,GAAjB;;AACA,cAAImN,WAAW,CAACwB,SAAZ,KAA0B,CAA9B,EAAiC;AAC/BhB,YAAAA,IAAI,GAAG3N,GAAG,CAAC5E,MAAJ,GAAa,CAApB;AACD,WAFD,MAEO;AACLuS,YAAAA,IAAI,GAAGR,WAAW,CAACwB,SAAZ,GAAwB,CAA/B;AACD;;AAEDpB,UAAAA,MAAM,CAACvP,IAAP,CAAY,CAAC,MAAD,EAASgC,GAAG,CAAC0O,KAAJ,CAAUJ,GAAV,EAAeX,IAAI,GAAG,CAAtB,CAAT,EAAmChH,IAAnC,EAAyC2H,GAAG,GAAGtG,MAA/C,EAAuDrB,IAAvD,EAA6DgH,IAAI,GAAG3F,MAApE,CAAZ;AACAsG,UAAAA,GAAG,GAAGX,IAAN;AACD;;AAED;AAzOJ;;AA4OAW,IAAAA,GAAG;AACJ;;AAED,SAAOf,MAAP;AACD;;AC9SD;AACA,AAGA,IAAMqB,eAAe,GAAG;AACtBC,EAAAA,QAAQ,EAAE,CAAC,EAAD,EAAK,EAAL,CADY;AACF;AACpB5G,EAAAA,MAAM,EAAE,CAAC,EAAD,EAAK,EAAL,CAFc;AAEJ;AAClB,aAAW,CAAC,EAAD,EAAK,EAAL,CAHW;AAGD;AACrB6G,EAAAA,OAAO,EAAE,CAAC,EAAD,EAAK,EAAL,CAJa;AAIH;AACnB,OAAK,CAAC,EAAD,EAAK,EAAL,CALiB;AAKP;AACf,OAAK,CAAC,EAAD,EAAK,EAAL,CANiB;AAMP;AACf,OAAK,CAAC,CAAD,EAAI,EAAJ,CAPiB;AAOR;AACd,OAAK,CAAC,CAAD,EAAI,EAAJ,CARiB;AAQR;AACd,OAAK,CAAC,CAAD,EAAI,EAAJ,CATiB;AASR;AACd,OAAK,CAAC,CAAD,EAAI,EAAJ,CAViB;;AAAA,CAAxB;;AAaA,SAASvU,IAAT,CAAcwU,KAAd,EAAqB;AACnB,mBAAiBA,KAAjB;AACD;;AAED,SAASC,iBAAT,CAA2BhP,GAA3B,EAAgC;AAC9B,MAAMuN,MAAM,GAAGF,QAAQ,CAAC,IAAI4B,KAAJ,CAAUjP,GAAV,CAAD,EAAiB;AAAE0N,IAAAA,YAAY,EAAE;AAAhB,GAAjB,CAAvB;AACA,MAAMlC,MAAM,GAAG,EAAf;AACA+B,EAAAA,MAAM,CAACtP,OAAP,CAAe,UAAAiR,KAAK,EAAI;AACtB,QAAMH,KAAK,GAAGH,eAAe,CAACM,KAAK,CAAC,CAAD,CAAN,CAA7B;;AACA,QAAIH,KAAJ,EAAW;AACTvD,MAAAA,MAAM,CAACxN,IAAP,CACEkR,KAAK,CAAC,CAAD,CAAL,CACGzN,KADH,CACS,OADT,EAEG0N,GAFH,CAEO,UAAAhU,CAAC;AAAA,eAAIZ,IAAI,CAACwU,KAAK,CAAC,CAAD,CAAN,CAAJ,GAAiB5T,CAAjB,GAAqBZ,IAAI,CAACwU,KAAK,CAAC,CAAD,CAAN,CAA7B;AAAA,OAFR,EAGGzQ,IAHH,CAGQ,IAHR,CADF;AAMD,KAPD,MAOO;AACLkN,MAAAA,MAAM,CAACxN,IAAP,CAAYkR,KAAK,CAAC,CAAD,CAAjB;AACD;AACF,GAZD;AAaA,SAAO1D,MAAM,CAAClN,IAAP,CAAY,EAAZ,CAAP;AACD;;AChCD;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2BM8Q;AACJ;;;;;;;;AAQA,0BAAYtD,OAAZ,EAAqBnF,IAArB,EAA2BC,MAA3B,EAAmCyI,MAAnC,EAA2CC,IAA3C,EAAiDxG,MAAjD,EAAyD;AACvD;;;;;;;;;;;;AAYA,SAAKnO,IAAL,GAAY,gBAAZ;AACA;;;;;;;AAMA,SAAK4U,MAAL,GAAczD,OAAd;;AAEA,QAAIwD,IAAJ,EAAU;AACR;;;;;;;AAOA,WAAKA,IAAL,GAAYA,IAAZ;AACD;;AACD,QAAID,MAAJ,EAAY;AACV;;;;;;;AAOA,WAAKA,MAAL,GAAcA,MAAd;AACD;;AACD,QAAIvG,MAAJ,EAAY;AACV;;;;;;AAMA,WAAKA,MAAL,GAAcA,MAAd;AACD;;AACD,QAAI,OAAOnC,IAAP,KAAgB,WAAhB,IAA+B,OAAOC,MAAP,KAAkB,WAArD,EAAkE;AAChE;;;;;;;AAOA,WAAKD,IAAL,GAAYA,IAAZ;AACA;;;;;;;;AAOA,WAAKC,MAAL,GAAcA,MAAd;AACD;;AAED,SAAK4I,UAAL;;AAEA,QAAInR,KAAK,CAACoR,iBAAV,EAA6B;AAC3BpR,MAAAA,KAAK,CAACoR,iBAAN,CAAwB,IAAxB,EAA8BL,cAA9B;AACD;AACF;;;;SAEDI,aAAA,sBAAa;AACX;;;;;;;AAOA,SAAK1D,OAAL,GAAe,KAAKhD,MAAL,GAAiB,KAAKA,MAAtB,UAAmC,EAAlD;AACA,SAAKgD,OAAL,IAAgB,KAAKwD,IAAL,GAAY,KAAKA,IAAjB,GAAwB,aAAxC;;AACA,QAAI,OAAO,KAAK3I,IAAZ,KAAqB,WAAzB,EAAsC;AACpC,WAAKmF,OAAL,UAAoB,KAAKnF,IAAzB,SAAiC,KAAKC,MAAtC;AACD;;AACD,SAAKkF,OAAL,WAAqB,KAAKyD,MAA1B;AACD;AAED;;;;;;;;;;;;;;;;;;;;;;;SAqBAG,iBAAA,wBAAeX,KAAf,EAAsB;AAAA;;AACpB,QAAI,CAAC,KAAKM,MAAV,EAAkB,OAAO,EAAP;AAElB,QAAIrP,GAAG,GAAG,KAAKqP,MAAf;AACA,QAAI,OAAON,KAAP,KAAiB,WAArB,EAAkCA,KAAK,GAAGY,aAAR;AAClC,QAAIZ,KAAJ,EAAW/O,GAAG,GAAGgP,iBAAiB,CAAChP,GAAD,CAAvB;AAEX,QAAM6N,KAAK,GAAG7N,GAAG,CAACyB,KAAJ,CAAU,OAAV,CAAd;AACA,QAAMmO,KAAK,GAAG/U,IAAI,CAACgV,GAAL,CAAS,KAAKlJ,IAAL,GAAY,CAArB,EAAwB,CAAxB,CAAd;AACA,QAAMmJ,GAAG,GAAGjV,IAAI,CAACkV,GAAL,CAAS,KAAKpJ,IAAL,GAAY,CAArB,EAAwBkH,KAAK,CAACzS,MAA9B,CAAZ;AAEA,QAAM4U,QAAQ,GAAGxV,MAAM,CAACsV,GAAD,CAAN,CAAY1U,MAA7B;AAEA,WAAOyS,KAAK,CACTa,KADI,CACEkB,KADF,EACSE,GADT,EAEJX,GAFI,CAEA,UAACxI,IAAD,EAAO3H,KAAP,EAAiB;AACpB,UAAMiR,MAAM,GAAGL,KAAK,GAAG,CAAR,GAAY5Q,KAA3B;AACA,UAAMkR,MAAM,GAAG,OAAID,MAAJ,EAAavB,KAAb,CAAmB,CAACsB,QAApB,CAAf;AACA,UAAMG,MAAM,SAAOD,MAAP,QAAZ;;AACA,UAAID,MAAM,KAAK,KAAI,CAACtJ,IAApB,EAA0B;AACxB,YAAMyJ,OAAO,GACXD,MAAM,CAACpV,OAAP,CAAe,KAAf,EAAsB,GAAtB,IAA6B4L,IAAI,CAAC+H,KAAL,CAAW,CAAX,EAAc,KAAI,CAAC9H,MAAL,GAAc,CAA5B,EAA+B7L,OAA/B,CAAuC,QAAvC,EAAiD,GAAjD,CAD/B;AAEA,qBAAWoV,MAAX,GAAoBxJ,IAApB,WAA8ByJ,OAA9B;AACD,OAJD,MAIO;AACL,qBAAWD,MAAX,GAAoBxJ,IAApB;AACD;AACF,KAbI,EAcJrI,IAdI,CAcC,IAdD,CAAP;AAeD;AAED;;;;;;;;;;;;SAUAnC,WAAA,oBAAW;AACT,QAAI5B,IAAI,GAAG,KAAKmV,cAAL,EAAX;;AACA,QAAInV,IAAJ,EAAU;AACRA,MAAAA,IAAI,YAAUA,IAAV,OAAJ;AACD;;AACD,WAAU,KAAKI,IAAf,UAAwB,KAAKmR,OAA7B,GAAuCvR,IAAvC;AACD;;;;wBAEe;AACdsR,MAAAA,QAAQ,CAAC,4DAAD,CAAR;AACA,aAAO,KAAKyB,KAAZ;AACD;AAED;;;;;;;;;;;;;;;;;;ACjNF;;AACA;AAEA,IAAM+C,UAAU,GAAG;AACjBC,EAAAA,KAAK,EAAE,IADU;AAEjBC,EAAAA,MAAM,EAAE,MAFS;AAGjBC,EAAAA,UAAU,EAAE,IAHK;AAIjBC,EAAAA,UAAU,EAAE,IAJK;AAKjBC,EAAAA,UAAU,EAAE,GALK;AAMjBC,EAAAA,WAAW,EAAE,IANI;AAOjBC,EAAAA,aAAa,EAAE,IAPE;AAQjBC,EAAAA,KAAK,EAAE,IARU;AASjBC,EAAAA,SAAS,EAAE,EATM;AAUjBC,EAAAA,WAAW,EAAE,GAVI;AAWjBC,EAAAA,YAAY,EAAE;AAXG,CAAnB;;AAcA,SAASC,UAAT,CAAoB1V,GAApB,EAAyB;AACvB,SAAOA,GAAG,CAAC,CAAD,CAAH,CAAO2V,WAAP,KAAuB3V,GAAG,CAACmT,KAAJ,CAAU,CAAV,CAA9B;AACD;;IAEKyC;AACJ,uBAAYC,OAAZ,EAAqB;AACnB,SAAKA,OAAL,GAAeA,OAAf;AACD;;;;SAEDC,YAAA,mBAAU9O,IAAV,EAAgB+O,SAAhB,EAA2B;AACzB,SAAK/O,IAAI,CAACgP,IAAV,EAAgBhP,IAAhB,EAAsB+O,SAAtB;AACD;;SAEDE,OAAA,cAAKjP,IAAL,EAAW;AACT,SAAKkP,IAAL,CAAUlP,IAAV;AACA,QAAIA,IAAI,CAACmP,IAAL,CAAUb,KAAd,EAAqB,KAAKO,OAAL,CAAa7O,IAAI,CAACmP,IAAL,CAAUb,KAAvB;AACtB;;SAED/B,UAAA,iBAAQvM,IAAR,EAAc;AACZ,QAAMoP,IAAI,GAAG,KAAKC,GAAL,CAASrP,IAAT,EAAe,MAAf,EAAuB,aAAvB,CAAb;AACA,QAAMsP,KAAK,GAAG,KAAKD,GAAL,CAASrP,IAAT,EAAe,OAAf,EAAwB,cAAxB,CAAd;AACA,SAAK6O,OAAL,QAAkBO,IAAlB,GAAyBpP,IAAI,CAACuP,IAA9B,GAAqCD,KAArC,SAAgDtP,IAAhD;AACD;;SAEDwP,OAAA,cAAKxP,IAAL,EAAW+O,SAAX,EAAsB;AACpB,QAAMU,OAAO,GAAG,KAAKJ,GAAL,CAASrP,IAAT,EAAe,SAAf,EAA0B,OAA1B,CAAhB;AACA,QAAI0F,MAAM,GAAG1F,IAAI,CAAC0P,IAAL,GAAYD,OAAZ,GAAsB,KAAKE,QAAL,CAAc3P,IAAd,EAAoB,OAApB,CAAnC;;AAEA,QAAIA,IAAI,CAAC4P,SAAT,EAAoB;AAClBlK,MAAAA,MAAM,IAAI1F,IAAI,CAACmP,IAAL,CAAUS,SAAV,IAAuB,aAAjC;AACD;;AAED,QAAIb,SAAJ,EAAerJ,MAAM,IAAI,GAAV;AACf,SAAKmJ,OAAL,CAAanJ,MAAb,EAAqB1F,IAArB;AACD;;SAEDiC,OAAA,cAAKjC,IAAL,EAAW;AACT,SAAK8D,KAAL,CAAW9D,IAAX,EAAiB,KAAK2P,QAAL,CAAc3P,IAAd,EAAoB,UAApB,CAAjB;AACD;;SAED6P,SAAA,gBAAO7P,IAAP,EAAa+O,SAAb,EAAwB;AACtB,QAAI3W,IAAI,SAAO4H,IAAI,CAAC5H,IAApB;AACA,QAAM0X,MAAM,GAAG9P,IAAI,CAAC8P,MAAL,GAAc,KAAKH,QAAL,CAAc3P,IAAd,EAAoB,QAApB,CAAd,GAA8C,EAA7D;;AAEA,QAAI,OAAOA,IAAI,CAACmP,IAAL,CAAUY,SAAjB,KAA+B,WAAnC,EAAgD;AAC9C3X,MAAAA,IAAI,IAAI4H,IAAI,CAACmP,IAAL,CAAUY,SAAlB;AACD,KAFD,MAEO,IAAID,MAAJ,EAAY;AACjB1X,MAAAA,IAAI,IAAI,GAAR;AACD;;AAED,QAAI4H,IAAI,CAACH,KAAT,EAAgB;AACd,WAAKiE,KAAL,CAAW9D,IAAX,EAAiB5H,IAAI,GAAG0X,MAAxB;AACD,KAFD,MAEO;AACL,UAAMvC,GAAG,GAAG,CAACvN,IAAI,CAACmP,IAAL,CAAUM,OAAV,IAAqB,EAAtB,KAA6BV,SAAS,GAAG,GAAH,GAAS,EAA/C,CAAZ;AACA,WAAKF,OAAL,CAAazW,IAAI,GAAG0X,MAAP,GAAgBvC,GAA7B,EAAkCvN,IAAlC;AACD;AACF;;SAEDkP,OAAA,cAAKlP,IAAL,EAAW;AACT,QAAIuL,IAAI,GAAGvL,IAAI,CAACH,KAAL,CAAWhH,MAAX,GAAoB,CAA/B;;AACA,WAAO0S,IAAI,GAAG,CAAd,EAAiB;AACf,UAAIvL,IAAI,CAACH,KAAL,CAAW0L,IAAX,EAAiByD,IAAjB,KAA0B,SAA9B,EAAyC;AACzCzD,MAAAA,IAAI,IAAI,CAAR;AACD;;AAED,QAAMwD,SAAS,GAAG,KAAKM,GAAL,CAASrP,IAAT,EAAe,WAAf,CAAlB;;AACA,SAAK,IAAIpH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoH,IAAI,CAACH,KAAL,CAAWhH,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;AAC1C,UAAM6H,KAAK,GAAGT,IAAI,CAACH,KAAL,CAAWjH,CAAX,CAAd;AACA,UAAMoX,MAAM,GAAG,KAAKX,GAAL,CAAS5O,KAAT,EAAgB,QAAhB,CAAf;AACA,UAAIuP,MAAJ,EAAY,KAAKnB,OAAL,CAAamB,MAAb;AACZ,WAAKlB,SAAL,CAAerO,KAAf,EAAsB8K,IAAI,KAAK3S,CAAT,IAAcmW,SAApC;AACD;AACF;;SAEDjL,QAAA,eAAM9D,IAAN,EAAYqN,KAAZ,EAAmB;AACjB,QAAMoC,OAAO,GAAG,KAAKJ,GAAL,CAASrP,IAAT,EAAe,SAAf,EAA0B,YAA1B,CAAhB;AACA,SAAK6O,OAAL,CAAgBxB,KAAK,GAAGoC,OAAxB,QAAoCzP,IAApC,EAA0C,OAA1C;AAEA,QAAIsO,KAAJ;;AACA,QAAItO,IAAI,CAACH,KAAL,IAAcG,IAAI,CAACH,KAAL,CAAWhH,MAA7B,EAAqC;AACnC,WAAKqW,IAAL,CAAUlP,IAAV;AACAsO,MAAAA,KAAK,GAAG,KAAKe,GAAL,CAASrP,IAAT,EAAe,OAAf,CAAR;AACD,KAHD,MAGO;AACLsO,MAAAA,KAAK,GAAG,KAAKe,GAAL,CAASrP,IAAT,EAAe,OAAf,EAAwB,WAAxB,CAAR;AACD;;AAED,QAAIsO,KAAJ,EAAW,KAAKO,OAAL,CAAaP,KAAb;AACX,SAAKO,OAAL,CAAa,GAAb,EAAkB7O,IAAlB,EAAwB,KAAxB;AACD;;SAEDqP,MAAA,aAAIrP,IAAJ,EAAUiQ,GAAV,EAAeC,MAAf,EAAuB;AACrB,QAAInI,KAAJ;AACA,QAAI,CAACmI,MAAL,EAAaA,MAAM,GAAGD,GAAT,CAFQ;;AAKrB,QAAIA,GAAJ,EAAS;AACPlI,MAAAA,KAAK,GAAG/H,IAAI,CAACmP,IAAL,CAAUc,GAAV,CAAR;AACA,UAAI,OAAOlI,KAAP,KAAiB,WAArB,EAAkC,OAAOA,KAAP;AACnC;;AAED,QAAMjH,MAAM,GAAGd,IAAI,CAACc,MAApB,CAVqB;;AAarB,QAAIoP,MAAM,KAAK,QAAf,EAAyB;AACvB,UAAI,CAACpP,MAAD,IAAYA,MAAM,CAACkO,IAAP,KAAgB,MAAhB,IAA0BlO,MAAM,CAACqP,KAAP,KAAiBnQ,IAA3D,EAAkE;AAChE,eAAO,EAAP;AACD;AACF,KAjBoB;;;AAoBrB,QAAI,CAACc,MAAL,EAAa,OAAOgN,UAAU,CAACoC,MAAD,CAAjB,CApBQ;;AAuBrB,QAAMjB,IAAI,GAAGjP,IAAI,CAACiP,IAAL,EAAb;AACA,QAAI,CAACA,IAAI,CAACmB,QAAV,EAAoBnB,IAAI,CAACmB,QAAL,GAAgB,EAAhB;;AACpB,QAAI,OAAOnB,IAAI,CAACmB,QAAL,CAAcF,MAAd,CAAP,KAAiC,WAArC,EAAkD;AAChD,aAAOjB,IAAI,CAACmB,QAAL,CAAcF,MAAd,CAAP;AACD;;AAED,QAAIA,MAAM,KAAK,QAAX,IAAuBA,MAAM,KAAK,OAAtC,EAA+C;AAC7C,aAAO,KAAKG,WAAL,CAAiBrQ,IAAjB,EAAuBkQ,MAAvB,CAAP;AACD,KAFD,MAEO;AACL,UAAMI,MAAM,WAAS5B,UAAU,CAACwB,MAAD,CAA/B;;AACA,UAAI,KAAKI,MAAL,CAAJ,EAAkB;AAChBvI,QAAAA,KAAK,GAAG,KAAKuI,MAAL,EAAarB,IAAb,EAAmBjP,IAAnB,CAAR;AACD,OAFD,MAEO;AACLiP,QAAAA,IAAI,CAACsB,IAAL,CAAU,UAAA3X,CAAC,EAAI;AACbmP,UAAAA,KAAK,GAAGnP,CAAC,CAACuW,IAAF,CAAOc,GAAP,CAAR;AACA,cAAI,OAAOlI,KAAP,KAAiB,WAArB,EAAkC,OAAO,KAAP;AACnC,SAHD;AAID;AACF;;AAED,QAAI,OAAOA,KAAP,KAAiB,WAArB,EAAkCA,KAAK,GAAG+F,UAAU,CAACoC,MAAD,CAAlB;AAElCjB,IAAAA,IAAI,CAACmB,QAAL,CAAcF,MAAd,IAAwBnI,KAAxB;AACA,WAAOA,KAAP;AACD;;SAEDyI,eAAA,sBAAavB,IAAb,EAAmB;AACjB,QAAIlH,KAAJ;AACAkH,IAAAA,IAAI,CAACsB,IAAL,CAAU,UAAA3X,CAAC,EAAI;AACb,UAAIA,CAAC,CAACiH,KAAF,IAAWjH,CAAC,CAACiH,KAAF,CAAQhH,MAAnB,IAA6BD,CAAC,CAAC2S,IAAF,CAAOyD,IAAP,KAAgB,MAAjD,EAAyD;AACvDjH,QAAAA,KAAK,GAAGnP,CAAC,CAACuW,IAAF,CAAOJ,SAAf;AACA,YAAI,OAAOhH,KAAP,KAAiB,WAArB,EAAkC,OAAO,KAAP;AACnC;AACF,KALD;AAMA,WAAOA,KAAP;AACD;;SAED0I,eAAA,sBAAaxB,IAAb,EAAmB;AACjB,QAAIlH,KAAJ;AACAkH,IAAAA,IAAI,CAACsB,IAAL,CAAU,UAAA3X,CAAC,EAAI;AACb,UAAIA,CAAC,CAACiH,KAAF,IAAWjH,CAAC,CAACiH,KAAF,CAAQhH,MAAR,KAAmB,CAAlC,EAAqC;AACnCkP,QAAAA,KAAK,GAAGnP,CAAC,CAACuW,IAAF,CAAOb,KAAf;AACA,YAAI,OAAOvG,KAAP,KAAiB,WAArB,EAAkC,OAAO,KAAP;AACnC;AACF,KALD;AAMA,WAAOA,KAAP;AACD;;SAED2I,YAAA,mBAAUzB,IAAV,EAAgB;AACd,QAAIA,IAAI,CAACE,IAAL,CAAUnB,MAAd,EAAsB,OAAOiB,IAAI,CAACE,IAAL,CAAUnB,MAAjB;AACtB,QAAIjG,KAAJ;AACAkH,IAAAA,IAAI,CAACsB,IAAL,CAAU,UAAA3X,CAAC,EAAI;AACb,UAAM+X,CAAC,GAAG/X,CAAC,CAACkI,MAAZ;;AACA,UAAI6P,CAAC,IAAIA,CAAC,KAAK1B,IAAX,IAAmB0B,CAAC,CAAC7P,MAArB,IAA+B6P,CAAC,CAAC7P,MAAF,KAAamO,IAAhD,EAAsD;AACpD,YAAI,OAAOrW,CAAC,CAACuW,IAAF,CAAOa,MAAd,KAAyB,WAA7B,EAA0C;AACxC,cAAM1Q,KAAK,GAAG1G,CAAC,CAACuW,IAAF,CAAOa,MAAP,CAAc9Q,KAAd,CAAoB,IAApB,CAAd;AACA6I,UAAAA,KAAK,GAAGzI,KAAK,CAACA,KAAK,CAACzG,MAAN,GAAe,CAAhB,CAAb;AACAkP,UAAAA,KAAK,GAAGA,KAAK,CAACvP,OAAN,CAAc,QAAd,EAAwB,EAAxB,CAAR;AACA,iBAAO,KAAP;AACD;AACF;AACF,KAVD;AAWA,WAAOuP,KAAP;AACD;;SAED6I,mBAAA,0BAAiB3B,IAAjB,EAAuBjP,IAAvB,EAA6B;AAC3B,QAAI+H,KAAJ;AACAkH,IAAAA,IAAI,CAAC4B,YAAL,CAAkB,UAAAjY,CAAC,EAAI;AACrB,UAAI,OAAOA,CAAC,CAACuW,IAAF,CAAOa,MAAd,KAAyB,WAA7B,EAA0C;AACxCjI,QAAAA,KAAK,GAAGnP,CAAC,CAACuW,IAAF,CAAOa,MAAf;;AACA,YAAIjI,KAAK,CAACpC,OAAN,CAAc,IAAd,MAAwB,CAAC,CAA7B,EAAgC;AAC9BoC,UAAAA,KAAK,GAAGA,KAAK,CAACvP,OAAN,CAAc,SAAd,EAAyB,EAAzB,CAAR;AACD;;AACD,eAAO,KAAP;AACD;AACF,KARD;;AASA,QAAI,OAAOuP,KAAP,KAAiB,WAArB,EAAkC;AAChCA,MAAAA,KAAK,GAAG,KAAKsH,GAAL,CAASrP,IAAT,EAAe,IAAf,EAAqB,YAArB,CAAR;AACD;;AACD,WAAO+H,KAAP;AACD;;SAED+I,gBAAA,uBAAc7B,IAAd,EAAoBjP,IAApB,EAA0B;AACxB,QAAI+H,KAAJ;AACAkH,IAAAA,IAAI,CAAC8B,SAAL,CAAe,UAAAnY,CAAC,EAAI;AAClB,UAAI,OAAOA,CAAC,CAACuW,IAAF,CAAOa,MAAd,KAAyB,WAA7B,EAA0C;AACxCjI,QAAAA,KAAK,GAAGnP,CAAC,CAACuW,IAAF,CAAOa,MAAf;;AACA,YAAIjI,KAAK,CAACpC,OAAN,CAAc,IAAd,MAAwB,CAAC,CAA7B,EAAgC;AAC9BoC,UAAAA,KAAK,GAAGA,KAAK,CAACvP,OAAN,CAAc,SAAd,EAAyB,EAAzB,CAAR;AACD;;AACD,eAAO,KAAP;AACD;AACF,KARD;;AASA,QAAI,OAAOuP,KAAP,KAAiB,WAArB,EAAkC;AAChCA,MAAAA,KAAK,GAAG,KAAKsH,GAAL,CAASrP,IAAT,EAAe,IAAf,EAAqB,YAArB,CAAR;AACD;;AACD,WAAO+H,KAAP;AACD;;SAEDiJ,gBAAA,uBAAc/B,IAAd,EAAoB;AAClB,QAAIlH,KAAJ;AACAkH,IAAAA,IAAI,CAACsB,IAAL,CAAU,UAAA3X,CAAC,EAAI;AACb,UAAIA,CAAC,CAACiH,KAAF,KAAYjH,CAAC,CAACkI,MAAF,KAAamO,IAAb,IAAqBA,IAAI,CAACkB,KAAL,KAAevX,CAAhD,CAAJ,EAAwD;AACtD,YAAI,OAAOA,CAAC,CAACuW,IAAF,CAAOa,MAAd,KAAyB,WAA7B,EAA0C;AACxCjI,UAAAA,KAAK,GAAGnP,CAAC,CAACuW,IAAF,CAAOa,MAAf;;AACA,cAAIjI,KAAK,CAACpC,OAAN,CAAc,IAAd,MAAwB,CAAC,CAA7B,EAAgC;AAC9BoC,YAAAA,KAAK,GAAGA,KAAK,CAACvP,OAAN,CAAc,SAAd,EAAyB,EAAzB,CAAR;AACD;;AACD,iBAAO,KAAP;AACD;AACF;AACF,KAVD;AAWA,WAAOuP,KAAP;AACD;;SAEDkJ,iBAAA,wBAAehC,IAAf,EAAqB;AACnB,QAAIlH,KAAJ;AACAkH,IAAAA,IAAI,CAACsB,IAAL,CAAU,UAAA3X,CAAC,EAAI;AACb,UAAIA,CAAC,CAACiH,KAAF,IAAWjH,CAAC,CAACiH,KAAF,CAAQhH,MAAR,GAAiB,CAAhC,EAAmC;AACjC,YAAI,OAAOD,CAAC,CAACuW,IAAF,CAAOb,KAAd,KAAwB,WAA5B,EAAyC;AACvCvG,UAAAA,KAAK,GAAGnP,CAAC,CAACuW,IAAF,CAAOb,KAAf;;AACA,cAAIvG,KAAK,CAACpC,OAAN,CAAc,IAAd,MAAwB,CAAC,CAA7B,EAAgC;AAC9BoC,YAAAA,KAAK,GAAGA,KAAK,CAACvP,OAAN,CAAc,SAAd,EAAyB,EAAzB,CAAR;AACD;;AACD,iBAAO,KAAP;AACD;AACF;AACF,KAVD;AAWA,WAAOuP,KAAP;AACD;;SAEDmJ,gBAAA,uBAAcjC,IAAd,EAAoB;AAClB,QAAIlH,KAAJ;AACAkH,IAAAA,IAAI,CAACsB,IAAL,CAAU,UAAA3X,CAAC,EAAI;AACb,UAAIA,CAAC,CAACoW,IAAF,KAAW,MAAf,EAAuB;AACrBjH,QAAAA,KAAK,GAAGnP,CAAC,CAACuW,IAAF,CAAOM,OAAf;AACA,YAAI,OAAO1H,KAAP,KAAiB,WAArB,EAAkC,OAAO,KAAP;AACnC;AACF,KALD;AAMA,WAAOA,KAAP;AACD;;SAEDoJ,WAAA,kBAASlC,IAAT,EAAe;AACb,QAAIlH,KAAJ;AACAkH,IAAAA,IAAI,CAAC8B,SAAL,CAAe,UAAAnY,CAAC,EAAI;AAClB,UAAI,OAAOA,CAAC,CAACuW,IAAF,CAAOM,OAAd,KAA0B,WAA9B,EAA2C;AACzC1H,QAAAA,KAAK,GAAGnP,CAAC,CAACuW,IAAF,CAAOM,OAAP,CAAejX,OAAf,CAAuB,SAAvB,EAAkC,EAAlC,CAAR;AACA,eAAO,KAAP;AACD;AACF,KALD;AAMA,WAAOuP,KAAP;AACD;;SAEDsI,cAAA,qBAAYrQ,IAAZ,EAAkBkQ,MAAlB,EAA0B;AACxB,QAAInI,KAAJ;;AACA,QAAI/H,IAAI,CAACgP,IAAL,KAAc,MAAlB,EAA0B;AACxBjH,MAAAA,KAAK,GAAG,KAAKsH,GAAL,CAASrP,IAAT,EAAe,IAAf,EAAqB,YAArB,CAAR;AACD,KAFD,MAEO,IAAIA,IAAI,CAACgP,IAAL,KAAc,SAAlB,EAA6B;AAClCjH,MAAAA,KAAK,GAAG,KAAKsH,GAAL,CAASrP,IAAT,EAAe,IAAf,EAAqB,eAArB,CAAR;AACD,KAFM,MAEA,IAAIkQ,MAAM,KAAK,QAAf,EAAyB;AAC9BnI,MAAAA,KAAK,GAAG,KAAKsH,GAAL,CAASrP,IAAT,EAAe,IAAf,EAAqB,YAArB,CAAR;AACD,KAFM,MAEA;AACL+H,MAAAA,KAAK,GAAG,KAAKsH,GAAL,CAASrP,IAAT,EAAe,IAAf,EAAqB,aAArB,CAAR;AACD;;AAED,QAAIoR,GAAG,GAAGpR,IAAI,CAACc,MAAf;AACA,QAAIyD,KAAK,GAAG,CAAZ;;AACA,WAAO6M,GAAG,IAAIA,GAAG,CAACpC,IAAJ,KAAa,MAA3B,EAAmC;AACjCzK,MAAAA,KAAK,IAAI,CAAT;AACA6M,MAAAA,GAAG,GAAGA,GAAG,CAACtQ,MAAV;AACD;;AAED,QAAIiH,KAAK,CAACpC,OAAN,CAAc,IAAd,MAAwB,CAAC,CAA7B,EAAgC;AAC9B,UAAMqI,MAAM,GAAG,KAAKqB,GAAL,CAASrP,IAAT,EAAe,IAAf,EAAqB,QAArB,CAAf;;AACA,UAAIgO,MAAM,CAACnV,MAAX,EAAmB;AACjB,aAAK,IAAIwY,IAAI,GAAG,CAAhB,EAAmBA,IAAI,GAAG9M,KAA1B,EAAiC8M,IAAI,EAArC;AAAyCtJ,UAAAA,KAAK,IAAIiG,MAAT;AAAzC;AACD;AACF;;AAED,WAAOjG,KAAP;AACD;;SAED4H,WAAA,kBAAS3P,IAAT,EAAe0P,IAAf,EAAqB;AACnB,QAAM3H,KAAK,GAAG/H,IAAI,CAAC0P,IAAD,CAAlB;AACA,QAAML,GAAG,GAAGrP,IAAI,CAACmP,IAAL,CAAUO,IAAV,CAAZ;;AACA,QAAIL,GAAG,IAAIA,GAAG,CAACtH,KAAJ,KAAcA,KAAzB,EAAgC;AAC9B,aAAOsH,GAAG,CAACA,GAAX;AACD,KAFD,MAEO;AACL,aAAOtH,KAAP;AACD;AACF;;;;;AChUH;AACA,AAEe,SAAS+G,SAAT,CAAmB9O,IAAnB,EAAyB6O,OAAzB,EAAkC;AAC/C,MAAM7V,GAAG,GAAG,IAAI4V,WAAJ,CAAgBC,OAAhB,CAAZ;AACA7V,EAAAA,GAAG,CAAC8V,SAAJ,CAAc9O,IAAd;AACD;;ACAD;;;;;;AAMA;;;;;;;AAOA,IAAMsR,SAAS,GAAG,SAAZA,SAAY,CAASjJ,GAAT,EAAcvH,MAAd,EAAsB;AACtC,MAAMyQ,MAAM,GAAG,IAAIlJ,GAAG,CAACmJ,WAAR,EAAf;;AAEA,OAAK,IAAM5Y,CAAX,IAAgByP,GAAhB,EAAqB;AACnB,QAAI,CAACA,GAAG,CAACG,cAAJ,CAAmB5P,CAAnB,CAAL,EAA4B;AAC5B,QAAImP,KAAK,GAAGM,GAAG,CAACzP,CAAD,CAAf;AACA,QAAMoW,IAAI,GAAG,OAAOjH,KAApB;;AAEA,QAAInP,CAAC,KAAK,QAAN,IAAkBoW,IAAI,KAAK,QAA/B,EAAyC;AACvC,UAAIlO,MAAJ,EAAYyQ,MAAM,CAAC3Y,CAAD,CAAN,GAAYkI,MAAZ;AACb,KAFD,MAEO,IAAIlI,CAAC,KAAK,QAAV,EAAoB;AACzB2Y,MAAAA,MAAM,CAAC3Y,CAAD,CAAN,GAAYmP,KAAZ;AACD,KAFM,MAEA,IAAIA,KAAK,YAAYU,KAArB,EAA4B;AACjC8I,MAAAA,MAAM,CAAC3Y,CAAD,CAAN,GAAYmP,KAAK,CAAC6E,GAAN,CAAU,UAAA6E,CAAC;AAAA,eAAIH,SAAS,CAACG,CAAD,EAAIF,MAAJ,CAAb;AAAA,OAAX,CAAZ;AACD,KAFM,MAEA,IAAI3Y,CAAC,KAAK,QAAN,IAAkBA,CAAC,KAAK,OAAxB,IAAmCA,CAAC,KAAK,SAAzC,IAAsDA,CAAC,KAAK,WAAhE,EAA6E;AAClF,UAAIoW,IAAI,KAAK,QAAT,IAAqBjH,KAAK,KAAK,IAAnC,EAAyCA,KAAK,GAAGuJ,SAAS,CAACvJ,KAAD,CAAjB;AACzCwJ,MAAAA,MAAM,CAAC3Y,CAAD,CAAN,GAAYmP,KAAZ;AACD;AACF;;AAED,SAAOwJ,MAAP;AACD,CArBD;AAuBA;;;;;;;IAKMG;AACJ;;;AAGA,gBAAYC,QAAZ,EAA2B;AAAA,QAAfA,QAAe;AAAfA,MAAAA,QAAe,GAAJ,EAAI;AAAA;;AACzB,SAAKxC,IAAL,GAAY,EAAZ;;AACA,SAAK,IAAM/W,IAAX,IAAmBuZ,QAAnB,EAA6B;AAC3B,WAAKvZ,IAAL,IAAauZ,QAAQ,CAACvZ,IAAD,CAArB;AACD;AACF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAgCA8T,QAAA,eAAM3C,OAAN,EAAeqI,IAAf,EAA0B;AAAA,QAAXA,IAAW;AAAXA,MAAAA,IAAW,GAAJ,EAAI;AAAA;;AACxB,QAAI,KAAK9E,MAAT,EAAiB;AACf,UAAMf,GAAG,GAAG,KAAK8F,UAAL,CAAgBD,IAAhB,CAAZ;AACA,aAAO,KAAK9E,MAAL,CAAY/B,KAAZ,CAAkBmB,KAAlB,CAAwB3C,OAAxB,EAAiCwC,GAAG,CAAC3H,IAArC,EAA2C2H,GAAG,CAAC1H,MAA/C,EAAuDuN,IAAvD,CAAP;AACD,KAHD,MAGO;AACL,aAAO,IAAI/E,cAAJ,CAAmBtD,OAAnB,CAAP;AACD;AACF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;SAyBAH,OAAA,cAAKH,MAAL,EAAasG,IAAb,EAAmBqC,IAAnB,EAAyB;AACvB,QAAME,IAAI,GAAG;AAAE9R,MAAAA,IAAI,EAAE;AAAR,KAAb;;AACA,SAAK,IAAMpH,CAAX,IAAgBgZ,IAAhB;AAAsBE,MAAAA,IAAI,CAAClZ,CAAD,CAAJ,GAAUgZ,IAAI,CAAChZ,CAAD,CAAd;AAAtB;;AACA,WAAOqQ,MAAM,CAACG,IAAP,CAAYmG,IAAZ,EAAkBuC,IAAlB,CAAP;AACD;AAED;;;;;;;;;;;;;SAWAC,SAAA,kBAAS;AACP,QAAI,KAAKjR,MAAT,EAAiB;AACf,WAAKA,MAAL,CAAYX,WAAZ,CAAwB,IAAxB;AACD;;AACD,SAAKW,MAAL,GAAclC,SAAd;AACA,WAAO,IAAP;AACD;AAED;;;;;;;;;;;;;SAWAhF,WAAA,kBAASoY,WAAT,EAAkC;AAAA,QAAzBA,WAAyB;AAAzBA,MAAAA,WAAyB,GAAXlD,SAAW;AAAA;;AAChC,QAAIkD,WAAW,CAAClD,SAAhB,EAA2BkD,WAAW,GAAGA,WAAW,CAAClD,SAA1B;AAC3B,QAAI7F,MAAM,GAAG,EAAb;AACA+I,IAAAA,WAAW,CAAC,IAAD,EAAO,UAAApZ,CAAC,EAAI;AACrBqQ,MAAAA,MAAM,IAAIrQ,CAAV;AACD,KAFU,CAAX;AAGA,WAAOqQ,MAAP;AACD;AAED;;;;;;;;;;;;;;;;;;SAgBAgJ,QAAA,eAAMC,SAAN,EAAsB;AAAA,QAAhBA,SAAgB;AAAhBA,MAAAA,SAAgB,GAAJ,EAAI;AAAA;;AACpB,QAAMX,MAAM,GAAGD,SAAS,CAAC,IAAD,CAAxB;;AACA,SAAK,IAAMlZ,IAAX,IAAmB8Z,SAAnB,EAA8B;AAC5BX,MAAAA,MAAM,CAACnZ,IAAD,CAAN,GAAe8Z,SAAS,CAAC9Z,IAAD,CAAxB;AACD;;AACD,WAAOmZ,MAAP;AACD;AAED;;;;;;;;;;;;;SAWAY,cAAA,qBAAYD,SAAZ,EAA4B;AAAA,QAAhBA,SAAgB;AAAhBA,MAAAA,SAAgB,GAAJ,EAAI;AAAA;;AAC1B,QAAMX,MAAM,GAAG,KAAKU,KAAL,CAAWC,SAAX,CAAf;AACA,SAAKpR,MAAL,CAAYM,YAAZ,CAAyB,IAAzB,EAA+BmQ,MAA/B;AACA,WAAOA,MAAP;AACD;AAED;;;;;;;;;;SAQAa,aAAA,oBAAWF,SAAX,EAA2B;AAAA,QAAhBA,SAAgB;AAAhBA,MAAAA,SAAgB,GAAJ,EAAI;AAAA;;AACzB,QAAMX,MAAM,GAAG,KAAKU,KAAL,CAAWC,SAAX,CAAf;AACA,SAAKpR,MAAL,CAAYuR,WAAZ,CAAwB,IAAxB,EAA8Bd,MAA9B;AACA,WAAOA,MAAP;AACD;AAED;;;;;;;;;;;;;;SAYAe,cAAA,uBAAsB;AAAA;;AACpB,QAAI,KAAKxR,MAAT,EAAiB;AAAA,wCADJjB,KACI;AADJA,QAAAA,KACI;AAAA;;AACfA,MAAAA,KAAK,CAACnE,OAAN,CAAc,UAAAsE,IAAI,EAAI;AACpB,QAAA,KAAI,CAACc,MAAL,CAAYM,YAAZ,CAAyB,KAAzB,EAA+BpB,IAA/B;AACD,OAFD;AAIA,WAAK+R,MAAL;AACD;;AAED,WAAO,IAAP;AACD;AAED;;;;;;;;;;;;;;;;;;;SAiBAQ,SAAA,gBAAOC,SAAP,EAAkB;AAChB,SAAKC,SAAL,CAAe,KAAKxD,IAAL,OAAgBuD,SAAS,CAACvD,IAAV,EAA/B;AACA,SAAK8C,MAAL;AACAS,IAAAA,SAAS,CAACE,MAAV,CAAiB,IAAjB;AACA,WAAO,IAAP;AACD;AAED;;;;;;;;;;;;;SAWAC,aAAA,oBAAWC,SAAX,EAAsB;AACpB,SAAKH,SAAL,CAAe,KAAKxD,IAAL,OAAgB2D,SAAS,CAAC3D,IAAV,EAA/B;AACA,SAAK8C,MAAL;AACAa,IAAAA,SAAS,CAAC9R,MAAV,CAAiBM,YAAjB,CAA8BwR,SAA9B,EAAyC,IAAzC;AACA,WAAO,IAAP;AACD;AAED;;;;;;;;;;;;;SAWAC,YAAA,mBAAUD,SAAV,EAAqB;AACnB,SAAKH,SAAL,CAAe,KAAKxD,IAAL,OAAgB2D,SAAS,CAAC3D,IAAV,EAA/B;AACA,SAAK8C,MAAL;AACAa,IAAAA,SAAS,CAAC9R,MAAV,CAAiBuR,WAAjB,CAA6BO,SAA7B,EAAwC,IAAxC;AACA,WAAO,IAAP;AACD;AAED;;;;;;;;;;;;;;;;SAcAxH,OAAA,gBAAO;AACL,QAAM3O,KAAK,GAAG,KAAKqE,MAAL,CAAYrE,KAAZ,CAAkB,IAAlB,CAAd;AACA,WAAO,KAAKqE,MAAL,CAAYjB,KAAZ,CAAkBpD,KAAK,GAAG,CAA1B,CAAP;AACD;AAED;;;;;;;;;;;;;;SAYAoP,OAAA,gBAAO;AACL,QAAMpP,KAAK,GAAG,KAAKqE,MAAL,CAAYrE,KAAZ,CAAkB,IAAlB,CAAd;AACA,WAAO,KAAKqE,MAAL,CAAYjB,KAAZ,CAAkBpD,KAAK,GAAG,CAA1B,CAAP;AACD;;SAEDqW,SAAA,kBAAS;AACP,QAAMC,KAAK,GAAG,EAAd;;AAEA,SAAK,IAAM3a,IAAX,IAAmB,IAAnB,EAAyB;AACvB,UAAI,CAAC,KAAKoQ,cAAL,CAAoBpQ,IAApB,CAAL,EAAgC;AAChC,UAAIA,IAAI,KAAK,QAAb,EAAuB;AACvB,UAAM2P,KAAK,GAAG,KAAK3P,IAAL,CAAd;;AAEA,UAAI2P,KAAK,YAAYU,KAArB,EAA4B;AAC1BsK,QAAAA,KAAK,CAAC3a,IAAD,CAAL,GAAc2P,KAAK,CAAC6E,GAAN,CAAU,UAAAhU,CAAC,EAAI;AAC3B,cAAI,OAAOA,CAAP,KAAa,QAAb,IAAyBA,CAAC,CAACka,MAA/B,EAAuC;AACrC,mBAAOla,CAAC,CAACka,MAAF,EAAP;AACD,WAFD,MAEO;AACL,mBAAOla,CAAP;AACD;AACF,SANa,CAAd;AAOD,OARD,MAQO,IAAI,OAAOmP,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,CAAC+K,MAAvC,EAA+C;AACpDC,QAAAA,KAAK,CAAC3a,IAAD,CAAL,GAAc2P,KAAK,CAAC+K,MAAN,EAAd;AACD,OAFM,MAEA;AACLC,QAAAA,KAAK,CAAC3a,IAAD,CAAL,GAAc2P,KAAd;AACD;AACF;;AAED,WAAOgL,KAAP;AACD;AAED;;;;;;;;;;;;;;;;;;;;SAkBA1D,MAAA,aAAIK,IAAJ,EAAUsD,WAAV,EAAuB;AACrB,QAAMha,GAAG,GAAG,IAAI4V,WAAJ,EAAZ;AACA,WAAO5V,GAAG,CAACqW,GAAJ,CAAQ,IAAR,EAAcK,IAAd,EAAoBsD,WAApB,CAAP;AACD;AAED;;;;;;;;;;SAQA/D,OAAA,gBAAO;AACL,QAAIhG,MAAM,GAAG,IAAb;;AACA,WAAOA,MAAM,CAACnI,MAAd;AAAsBmI,MAAAA,MAAM,GAAGA,MAAM,CAACnI,MAAhB;AAAtB;;AACA,WAAOmI,MAAP;AACD;;SAEDwJ,YAAA,mBAAUQ,WAAV,EAAuB;AACrB,WAAO,KAAK9D,IAAL,CAAUa,MAAjB;AACA,WAAO,KAAKb,IAAL,CAAUb,KAAjB;AACA,QAAI,CAAC2E,WAAL,EAAkB,OAAO,KAAK9D,IAAL,CAAUM,OAAjB;AACnB;;SAEDyD,iBAAA,wBAAezW,KAAf,EAAsB;AACpB,QAAMiJ,MAAM,GAAG,KAAK9L,QAAL,EAAf;AACA,QAAIyK,MAAM,GAAG,KAAKyI,MAAL,CAAYO,KAAZ,CAAkBhJ,MAA/B;AACA,QAAID,IAAI,GAAG,KAAK0I,MAAL,CAAYO,KAAZ,CAAkBjJ,IAA7B;;AAEA,SAAK,IAAIxL,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6D,KAApB,EAA2B7D,CAAC,EAA5B,EAAgC;AAC9B,UAAI8M,MAAM,CAAC9M,CAAD,CAAN,KAAc,IAAlB,EAAwB;AACtByL,QAAAA,MAAM,GAAG,CAAT;AACAD,QAAAA,IAAI,IAAI,CAAR;AACD,OAHD,MAGO;AACLC,QAAAA,MAAM,IAAI,CAAV;AACD;AACF;;AAED,WAAO;AAAED,MAAAA,IAAI,EAAJA,IAAF;AAAQC,MAAAA,MAAM,EAANA;AAAR,KAAP;AACD;;SAEDwN,aAAA,oBAAWD,IAAX,EAAiB;AACf,QAAI7F,GAAG,GAAG,KAAKe,MAAL,CAAYO,KAAtB;;AACA,QAAIuE,IAAI,CAACnV,KAAT,EAAgB;AACdsP,MAAAA,GAAG,GAAG,KAAKmH,cAAL,CAAoBtB,IAAI,CAACnV,KAAzB,CAAN;AACD,KAFD,MAEO,IAAImV,IAAI,CAACuB,IAAT,EAAe;AACpB,UAAM1W,KAAK,GAAG,KAAK7C,QAAL,GAAgB+L,OAAhB,CAAwBiM,IAAI,CAACuB,IAA7B,CAAd;AACA,UAAI1W,KAAK,KAAK,CAAC,CAAf,EAAkBsP,GAAG,GAAG,KAAKmH,cAAL,CAAoBzW,KAApB,CAAN;AACnB;;AACD,WAAOsP,GAAP;AACD;;SAEDqH,aAAA,sBAAa;AACX9J,IAAAA,QAAQ,CAAC,iDAAD,CAAR;AACA,WAAO,KAAKyI,MAAL,EAAP;AACD;;SAEDvZ,UAAA,iBAAQqH,KAAR,EAAe;AACbyJ,IAAAA,QAAQ,CAAC,kDAAD,CAAR;AACA,WAAO,KAAKgJ,WAAL,CAAiBzS,KAAjB,CAAP;AACD;;SAEDR,QAAA,eAAM4Q,GAAN,EAAWC,MAAX,EAAmB;AACjB5G,IAAAA,QAAQ,CAAC,4CAAD,CAAR;AACA,WAAO,KAAK+F,GAAL,CAASY,GAAT,EAAcC,MAAd,CAAP;AACD;;SAEDmD,cAAA,qBAAYJ,WAAZ,EAAyB;AACvB3J,IAAAA,QAAQ,CAAC,wDAAD,CAAR;AACA,WAAO,KAAKmJ,SAAL,CAAeQ,WAAf,CAAP;AACD;;;;wBAEY;AACX3J,MAAAA,QAAQ,CAAC,iDAAD,CAAR;AACA,aAAO,KAAK6F,IAAL,CAAUa,MAAjB;AACD;sBAEUsD,KAAK;AACdhK,MAAAA,QAAQ,CAAC,iDAAD,CAAR;AACA,WAAK6F,IAAL,CAAUa,MAAV,GAAmBsD,GAAnB;AACD;;;wBAEa;AACZhK,MAAAA,QAAQ,CAAC,mDAAD,CAAR;AACA,aAAO,KAAK6F,IAAL,CAAUM,OAAjB;AACD;sBAEW6D,KAAK;AACfhK,MAAAA,QAAQ,CAAC,mDAAD,CAAR;AACA,WAAK6F,IAAL,CAAUM,OAAV,GAAoB6D,GAApB;AACD;AAED;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACphBF;;;;;;;;;;;;IAWMC;;;AACJ,uBAAY5B,QAAZ,EAAsB;AAAA;;AACpB,6BAAMA,QAAN;AACA,UAAK3C,IAAL,GAAY,MAAZ;AAFoB;AAGrB;;;;wBAEY;AACX1F,MAAAA,QAAQ,CAAC,iDAAD,CAAR;AACA,aAAO,KAAK6F,IAAL,CAAUpH,KAAjB;AACD;sBAEUuL,KAAK;AACdhK,MAAAA,QAAQ,CAAC,iDAAD,CAAR;AACA,WAAK6F,IAAL,CAAUpH,KAAV,GAAkBuL,GAAlB;AACD;;;wBAEgB;AACfhK,MAAAA,QAAQ,CAAC,yDAAD,CAAR;AACA,aAAO,KAAK6F,IAAL,CAAUS,SAAjB;AACD;sBAEc0D,KAAK;AAClBhK,MAAAA,QAAQ,CAAC,yDAAD,CAAR;AACA,WAAK6F,IAAL,CAAUS,SAAV,GAAsB0D,GAAtB;AACD;AAED;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;;AAWA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAzDwB5B;;ACX1B;;;;;;;;;IAQM8B;;;AACJ,mBAAY7B,QAAZ,EAAsB;AAAA;;AACpB,6BAAMA,QAAN;AACA,UAAK3C,IAAL,GAAY,SAAZ;AAFoB;AAGrB;;;;wBAEU;AACT1F,MAAAA,QAAQ,CAAC,oDAAD,CAAR;AACA,aAAO,KAAK6F,IAAL,CAAUC,IAAjB;AACD;sBAEQkE,KAAK;AACZhK,MAAAA,QAAQ,CAAC,oDAAD,CAAR;AACA,WAAK6F,IAAL,CAAUC,IAAV,GAAiBkE,GAAjB;AACD;;;wBAEW;AACVhK,MAAAA,QAAQ,CAAC,sDAAD,CAAR;AACA,aAAO,KAAK6F,IAAL,CAAUG,KAAjB;AACD;sBAESgE,KAAK;AACbhK,MAAAA,QAAQ,CAAC,sDAAD,CAAR;AACA,WAAK6F,IAAL,CAAUG,KAAV,GAAkBgE,GAAlB;AACD;AAED;;;;;AAKA;;;;;;;;;;;;;;;;EA/BoB5B;;ACZtB;AACA;IAOqB+B;AACnB,kBAAY1I,KAAZ,EAAmB;AACjB,SAAKA,KAAL,GAAaA,KAAb;AAEA,SAAKgB,GAAL,GAAW,CAAX;AACA,SAAKkD,IAAL,GAAY,IAAIyE,IAAJ,EAAZ;AACA,SAAKC,OAAL,GAAe,KAAK1E,IAApB;AACA,SAAK2E,MAAL,GAAc,EAAd;AACA,SAAK7E,SAAL,GAAiB,KAAjB;AAEA,SAAKE,IAAL,CAAUnC,MAAV,GAAmB;AAAE/B,MAAAA,KAAK,EAALA,KAAF;AAASsC,MAAAA,KAAK,EAAE;AAAEjJ,QAAAA,IAAI,EAAE,CAAR;AAAWC,QAAAA,MAAM,EAAE;AAAnB;AAAhB,KAAnB;AACD;;;;SAEDyG,WAAA,sBAAW;AACT,SAAKE,MAAL,GAAc6I,QAAS,CAAC,KAAK9I,KAAN,CAAvB;AACD;;SAED+I,OAAA,gBAAO;AACL,QAAInH,KAAJ;;AACA,WAAO,KAAKZ,GAAL,GAAW,KAAKf,MAAL,CAAYnS,MAA9B,EAAsC;AACpC8T,MAAAA,KAAK,GAAG,KAAK3B,MAAL,CAAY,KAAKe,GAAjB,CAAR;;AAEA,cAAQY,KAAK,CAAC,CAAD,CAAb;AACE,aAAK,OAAL;AACA,aAAK,GAAL;AACE,eAAKiH,MAAL,IAAejH,KAAK,CAAC,CAAD,CAApB;AACA;;AAEF,aAAK,GAAL;AACE,eAAKY,GAAL,CAASZ,KAAT;AACA;;AAEF,aAAK,SAAL;AACE,eAAKJ,OAAL,CAAaI,KAAb;AACA;;AAEF,aAAK,SAAL;AACE,eAAKkD,MAAL,CAAYlD,KAAZ;AACA;;AAEF,aAAK,GAAL;AACE,eAAKoH,SAAL,CAAepH,KAAf;AACA;;AAEF;AACE,eAAKqH,KAAL;AACA;AAxBJ;;AA2BA,WAAKjI,GAAL,IAAY,CAAZ;AACD;;AACD,SAAKkI,OAAL;AACD;;SAED1H,UAAA,iBAAQI,KAAR,EAAe;AACb,QAAM3M,IAAI,GAAG,IAAIwT,OAAJ,EAAb;AACA,SAAKU,IAAL,CAAUlU,IAAV,EAAgB2M,KAAK,CAAC,CAAD,CAArB,EAA0BA,KAAK,CAAC,CAAD,CAA/B;AACA3M,IAAAA,IAAI,CAAC8M,MAAL,CAAYS,GAAZ,GAAkB;AAAEnJ,MAAAA,IAAI,EAAEuI,KAAK,CAAC,CAAD,CAAb;AAAkBtI,MAAAA,MAAM,EAAEsI,KAAK,CAAC,CAAD;AAA/B,KAAlB;AAEA,QAAM4C,IAAI,GAAG5C,KAAK,CAAC,CAAD,CAAL,CAASR,KAAT,CAAe,CAAf,EAAkB,CAAC,CAAnB,CAAb;;AACA,QAAI,QAAQ3S,IAAR,CAAa+V,IAAb,CAAJ,EAAwB;AACtBvP,MAAAA,IAAI,CAACuP,IAAL,GAAY,EAAZ;AACAvP,MAAAA,IAAI,CAACmP,IAAL,CAAUC,IAAV,GAAiBG,IAAjB;AACAvP,MAAAA,IAAI,CAACmP,IAAL,CAAUG,KAAV,GAAkB,EAAlB;AACD,KAJD,MAIO;AACL,UAAM5P,KAAK,GAAG6P,IAAI,CAAC7P,KAAL,CAAW,yBAAX,CAAd;AACAM,MAAAA,IAAI,CAACuP,IAAL,GAAY7P,KAAK,CAAC,CAAD,CAAjB;AACAM,MAAAA,IAAI,CAACmP,IAAL,CAAUC,IAAV,GAAiB1P,KAAK,CAAC,CAAD,CAAtB;AACAM,MAAAA,IAAI,CAACmP,IAAL,CAAUG,KAAV,GAAkB5P,KAAK,CAAC,CAAD,CAAvB;AACD;AACF;;SAEDqU,YAAA,mBAAUpH,KAAV,EAAiB;AACf,QAAM3M,IAAI,GAAG,IAAImU,IAAJ,EAAb;AACA,SAAKD,IAAL,CAAUlU,IAAV,EAAgB2M,KAAK,CAAC,CAAD,CAArB,EAA0BA,KAAK,CAAC,CAAD,CAA/B;AACA3M,IAAAA,IAAI,CAACjB,QAAL,GAAgB,EAAhB;AACAiB,IAAAA,IAAI,CAACmP,IAAL,CAAUM,OAAV,GAAoB,EAApB;AACA,SAAKkE,OAAL,GAAe3T,IAAf;AACD;;SAEDgU,QAAA,iBAAQ;AACN,QAAIrH,KAAJ;AACA,QAAIY,GAAG,GAAG,KAAV;AACA,QAAIyB,IAAI,GAAG,IAAX;AACA,QAAIjB,KAAK,GAAG,KAAZ;AACA,QAAIqG,OAAO,GAAG,IAAd;AACA,QAAM9H,QAAQ,GAAG,EAAjB;AAEA,QAAMe,KAAK,GAAG,KAAKtB,GAAnB;;AACA,WAAO,KAAKA,GAAL,GAAW,KAAKf,MAAL,CAAYnS,MAA9B,EAAsC;AACpC8T,MAAAA,KAAK,GAAG,KAAK3B,MAAL,CAAY,KAAKe,GAAjB,CAAR;AACAiD,MAAAA,IAAI,GAAGrC,KAAK,CAAC,CAAD,CAAZ;;AAEA,UAAIqC,IAAI,KAAK,GAAT,IAAgBA,IAAI,KAAK,GAA7B,EAAkC;AAChC,YAAI,CAACoF,OAAL,EAAcA,OAAO,GAAGzH,KAAV;AACdL,QAAAA,QAAQ,CAAC7Q,IAAT,CAAcuT,IAAI,KAAK,GAAT,GAAe,GAAf,GAAqB,GAAnC;AACD,OAHD,MAGO,IAAI1C,QAAQ,CAACzT,MAAT,KAAoB,CAAxB,EAA2B;AAChC,YAAImW,IAAI,KAAK,GAAb,EAAkB;AAChB,cAAIjB,KAAJ,EAAW;AACT,iBAAKyB,IAAL,CAAU,KAAKxE,MAAL,CAAYmB,KAAZ,CAAkBkB,KAAlB,EAAyB,KAAKtB,GAAL,GAAW,CAApC,CAAV;AACA;AACD,WAHD,MAGO;AACL;AACD;AACF,SAPD,MAOO,IAAIiD,IAAI,KAAK,GAAb,EAAkB;AACvB,eAAK/M,IAAL,CAAU,KAAK+I,MAAL,CAAYmB,KAAZ,CAAkBkB,KAAlB,EAAyB,KAAKtB,GAAL,GAAW,CAApC,CAAV;AACA;AACD,SAHM,MAGA,IAAIiD,IAAI,KAAK,GAAb,EAAkB;AACvB,eAAKjD,GAAL,IAAY,CAAZ;AACAwB,UAAAA,GAAG,GAAG,IAAN;AACA;AACD,SAJM,MAIA,IAAIyB,IAAI,KAAK,GAAb,EAAkB;AACvBjB,UAAAA,KAAK,GAAG,IAAR;AACD;AACF,OAlBM,MAkBA,IAAIiB,IAAI,KAAK1C,QAAQ,CAACA,QAAQ,CAACzT,MAAT,GAAkB,CAAnB,CAArB,EAA4C;AACjDyT,QAAAA,QAAQ,CAAC+H,GAAT;AACA,YAAI/H,QAAQ,CAACzT,MAAT,KAAoB,CAAxB,EAA2Bub,OAAO,GAAG,IAAV;AAC5B;;AAED,WAAKrI,GAAL,IAAY,CAAZ;AACD;;AACD,QAAI,KAAKA,GAAL,KAAa,KAAKf,MAAL,CAAYnS,MAA7B,EAAqC;AACnC,WAAKkT,GAAL,IAAY,CAAZ;AACAwB,MAAAA,GAAG,GAAG,IAAN;AACD;;AAED,QAAIjB,QAAQ,CAACzT,MAAT,GAAkB,CAAtB,EAAyB,KAAKyb,eAAL,CAAqBF,OAArB;;AAEzB,QAAI7G,GAAG,IAAIQ,KAAX,EAAkB;AAChB,aAAO,KAAKhC,GAAL,GAAWsB,KAAlB,EAAyB;AACvBV,QAAAA,KAAK,GAAG,KAAK3B,MAAL,CAAY,KAAKe,GAAjB,EAAsB,CAAtB,CAAR;AACA,YAAIY,KAAK,KAAK,OAAV,IAAqBA,KAAK,KAAK,SAAnC,EAA8C;AAC9C,aAAKZ,GAAL,IAAY,CAAZ;AACD;;AACD,WAAKyD,IAAL,CAAU,KAAKxE,MAAL,CAAYmB,KAAZ,CAAkBkB,KAAlB,EAAyB,KAAKtB,GAAL,GAAW,CAApC,CAAV;AACA;AACD;;AAED,SAAKwI,WAAL,CAAiBlH,KAAjB;AACD;;SAEDpL,OAAA,cAAK+I,MAAL,EAAa;AACXA,IAAAA,MAAM,CAACqJ,GAAP;AAEA,QAAMrU,IAAI,GAAG,IAAImU,IAAJ,EAAb;AACA,SAAKD,IAAL,CAAUlU,IAAV,EAAgBgL,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV,CAAhB,EAA8BA,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV,CAA9B;AAEAhL,IAAAA,IAAI,CAACmP,IAAL,CAAUM,OAAV,GAAoB,KAAK+E,aAAL,CAAmBxJ,MAAnB,CAApB;AACA,SAAKqE,GAAL,CAASrP,IAAT,EAAe,UAAf,EAA2BgL,MAA3B;AACA,SAAK2I,OAAL,GAAe3T,IAAf;AACD;;SAEDwP,OAAA,cAAKxE,MAAL,EAAa;AACX,QAAMhL,IAAI,GAAG,IAAIuT,WAAJ,EAAb;AACA,SAAKW,IAAL,CAAUlU,IAAV;AAEA,QAAMuL,IAAI,GAAGP,MAAM,CAACA,MAAM,CAACnS,MAAP,GAAgB,CAAjB,CAAnB;;AACA,QAAI0S,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAhB,EAAqB;AACnB,WAAKwD,SAAL,GAAiB,IAAjB;AACA/D,MAAAA,MAAM,CAACqJ,GAAP;AACD;;AACD,QAAI9I,IAAI,CAAC,CAAD,CAAR,EAAa;AACXvL,MAAAA,IAAI,CAAC8M,MAAL,CAAYS,GAAZ,GAAkB;AAAEnJ,QAAAA,IAAI,EAAEmH,IAAI,CAAC,CAAD,CAAZ;AAAiBlH,QAAAA,MAAM,EAAEkH,IAAI,CAAC,CAAD;AAA7B,OAAlB;AACD,KAFD,MAEO;AACLvL,MAAAA,IAAI,CAAC8M,MAAL,CAAYS,GAAZ,GAAkB;AAAEnJ,QAAAA,IAAI,EAAEmH,IAAI,CAAC,CAAD,CAAZ;AAAiBlH,QAAAA,MAAM,EAAEkH,IAAI,CAAC,CAAD;AAA7B,OAAlB;AACD;;AAED,WAAOP,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV,MAAiB,MAAxB,EAAgC;AAC9BhL,MAAAA,IAAI,CAACmP,IAAL,CAAUa,MAAV,IAAoBhF,MAAM,CAACyJ,KAAP,GAAe,CAAf,CAApB;AACD;;AACDzU,IAAAA,IAAI,CAAC8M,MAAL,CAAYO,KAAZ,GAAoB;AAAEjJ,MAAAA,IAAI,EAAE4G,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV,CAAR;AAAsB3G,MAAAA,MAAM,EAAE2G,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV;AAA9B,KAApB;AAEAhL,IAAAA,IAAI,CAAC0P,IAAL,GAAY,EAAZ;;AACA,WAAO1E,MAAM,CAACnS,MAAd,EAAsB;AACpB,UAAMmW,IAAI,GAAGhE,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV,CAAb;;AACA,UAAIgE,IAAI,KAAK,GAAT,IAAgBA,IAAI,KAAK,OAAzB,IAAoCA,IAAI,KAAK,SAAjD,EAA4D;AAC1D;AACD;;AACDhP,MAAAA,IAAI,CAAC0P,IAAL,IAAa1E,MAAM,CAACyJ,KAAP,GAAe,CAAf,CAAb;AACD;;AAEDzU,IAAAA,IAAI,CAACmP,IAAL,CAAUM,OAAV,GAAoB,EAApB;AAEA,QAAI9C,KAAJ;;AACA,WAAO3B,MAAM,CAACnS,MAAd,EAAsB;AACpB8T,MAAAA,KAAK,GAAG3B,MAAM,CAACyJ,KAAP,EAAR;;AAEA,UAAI9H,KAAK,CAAC,CAAD,CAAL,KAAa,GAAjB,EAAsB;AACpB3M,QAAAA,IAAI,CAACmP,IAAL,CAAUM,OAAV,IAAqB9C,KAAK,CAAC,CAAD,CAA1B;AACA;AACD,OAHD,MAGO;AACL3M,QAAAA,IAAI,CAACmP,IAAL,CAAUM,OAAV,IAAqB9C,KAAK,CAAC,CAAD,CAA1B;AACD;AACF;;AAED,QAAI3M,IAAI,CAAC0P,IAAL,CAAU,CAAV,MAAiB,GAAjB,IAAwB1P,IAAI,CAAC0P,IAAL,CAAU,CAAV,MAAiB,GAA7C,EAAkD;AAChD1P,MAAAA,IAAI,CAACmP,IAAL,CAAUa,MAAV,IAAoBhQ,IAAI,CAAC0P,IAAL,CAAU,CAAV,CAApB;AACA1P,MAAAA,IAAI,CAAC0P,IAAL,GAAY1P,IAAI,CAAC0P,IAAL,CAAUvD,KAAV,CAAgB,CAAhB,CAAZ;AACD;;AACDnM,IAAAA,IAAI,CAACmP,IAAL,CAAUM,OAAV,IAAqB,KAAKiF,eAAL,CAAqB1J,MAArB,CAArB;AACA,SAAK2J,uBAAL,CAA6B3J,MAA7B;;AAEA,SAAK,IAAIpS,CAAC,GAAGoS,MAAM,CAACnS,MAAP,GAAgB,CAA7B,EAAgCD,CAAC,GAAG,CAApC,EAAuCA,CAAC,EAAxC,EAA4C;AAC1C+T,MAAAA,KAAK,GAAG3B,MAAM,CAACpS,CAAD,CAAd;;AACA,UAAI+T,KAAK,CAAC,CAAD,CAAL,KAAa,YAAjB,EAA+B;AAC7B3M,QAAAA,IAAI,CAAC4P,SAAL,GAAiB,IAAjB;AACA,YAAIlK,MAAM,GAAG,KAAKkP,UAAL,CAAgB5J,MAAhB,EAAwBpS,CAAxB,CAAb;AACA8M,QAAAA,MAAM,GAAG,KAAK8O,aAAL,CAAmBxJ,MAAnB,IAA6BtF,MAAtC;AACA,YAAIA,MAAM,KAAK,aAAf,EAA8B1F,IAAI,CAACmP,IAAL,CAAUS,SAAV,GAAsBlK,MAAtB;AAC9B;AACD,OAND,MAMO,IAAIiH,KAAK,CAAC,CAAD,CAAL,KAAa,WAAjB,EAA8B;AACnC,YAAMkI,KAAK,GAAG7J,MAAM,CAACmB,KAAP,CAAa,CAAb,CAAd;AACA,YAAInT,GAAG,GAAG,EAAV;;AACA,aAAK,IAAIyY,CAAC,GAAG7Y,CAAb,EAAgB6Y,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B;AAC1B,cAAMzC,KAAI,GAAG6F,KAAK,CAACpD,CAAD,CAAL,CAAS,CAAT,CAAb;;AACA,cAAIzY,GAAG,CAACgD,IAAJ,GAAW2J,OAAX,CAAmB,GAAnB,MAA4B,CAA5B,IAAiCqJ,KAAI,KAAK,OAA9C,EAAuD;AACrD;AACD;;AACDhW,UAAAA,GAAG,GAAG6b,KAAK,CAACR,GAAN,GAAY,CAAZ,IAAiBrb,GAAvB;AACD;;AACD,YAAIA,GAAG,CAACgD,IAAJ,GAAW2J,OAAX,CAAmB,GAAnB,MAA4B,CAAhC,EAAmC;AACjC3F,UAAAA,IAAI,CAAC4P,SAAL,GAAiB,IAAjB;AACA5P,UAAAA,IAAI,CAACmP,IAAL,CAAUS,SAAV,GAAsB5W,GAAtB;AACAgS,UAAAA,MAAM,GAAG6J,KAAT;AACD;AACF;;AAED,UAAIlI,KAAK,CAAC,CAAD,CAAL,KAAa,OAAb,IAAwBA,KAAK,CAAC,CAAD,CAAL,KAAa,SAAzC,EAAoD;AAClD;AACD;AACF;;AAED,SAAK0C,GAAL,CAASrP,IAAT,EAAe,OAAf,EAAwBgL,MAAxB;AAEA,QAAIhL,IAAI,CAAC+H,KAAL,CAAWpC,OAAX,CAAmB,GAAnB,MAA4B,CAAC,CAAjC,EAAoC,KAAKmP,oBAAL,CAA0B9J,MAA1B;AACrC;;SAED6E,SAAA,gBAAOlD,KAAP,EAAc;AACZ,QAAM3M,IAAI,GAAG,IAAI+U,MAAJ,EAAb;AACA/U,IAAAA,IAAI,CAAC5H,IAAL,GAAYuU,KAAK,CAAC,CAAD,CAAL,CAASR,KAAT,CAAe,CAAf,CAAZ;;AACA,QAAInM,IAAI,CAAC5H,IAAL,KAAc,EAAlB,EAAsB;AACpB,WAAK4c,aAAL,CAAmBhV,IAAnB,EAAyB2M,KAAzB;AACD;;AACD,SAAKuH,IAAL,CAAUlU,IAAV,EAAgB2M,KAAK,CAAC,CAAD,CAArB,EAA0BA,KAAK,CAAC,CAAD,CAA/B;AAEA,QAAIpB,IAAI,GAAG,KAAX;AACA,QAAI0J,IAAI,GAAG,KAAX;AACA,QAAMnF,MAAM,GAAG,EAAf;AAEA,SAAK/D,GAAL,IAAY,CAAZ;;AACA,WAAO,KAAKA,GAAL,GAAW,KAAKf,MAAL,CAAYnS,MAA9B,EAAsC;AACpC8T,MAAAA,KAAK,GAAG,KAAK3B,MAAL,CAAY,KAAKe,GAAjB,CAAR;;AAEA,UAAIY,KAAK,CAAC,CAAD,CAAL,KAAa,GAAjB,EAAsB;AACpB3M,QAAAA,IAAI,CAAC8M,MAAL,CAAYS,GAAZ,GAAkB;AAAEnJ,UAAAA,IAAI,EAAEuI,KAAK,CAAC,CAAD,CAAb;AAAkBtI,UAAAA,MAAM,EAAEsI,KAAK,CAAC,CAAD;AAA/B,SAAlB;AACA,aAAKoC,SAAL,GAAiB,IAAjB;AACA;AACD,OAJD,MAIO,IAAIpC,KAAK,CAAC,CAAD,CAAL,KAAa,GAAjB,EAAsB;AAC3BsI,QAAAA,IAAI,GAAG,IAAP;AACA;AACD,OAHM,MAGA,IAAItI,KAAK,CAAC,CAAD,CAAL,KAAa,GAAjB,EAAsB;AAC3B,aAAKY,GAAL,CAASZ,KAAT;AACA;AACD,OAHM,MAGA;AACLmD,QAAAA,MAAM,CAACrU,IAAP,CAAYkR,KAAZ;AACD;;AAED,WAAKZ,GAAL,IAAY,CAAZ;AACD;;AACD,QAAI,KAAKA,GAAL,KAAa,KAAKf,MAAL,CAAYnS,MAA7B,EAAqC;AACnC0S,MAAAA,IAAI,GAAG,IAAP;AACD;;AAEDvL,IAAAA,IAAI,CAACmP,IAAL,CAAUM,OAAV,GAAoB,KAAK+E,aAAL,CAAmB1E,MAAnB,CAApB;;AACA,QAAIA,MAAM,CAACjX,MAAX,EAAmB;AACjBmH,MAAAA,IAAI,CAACmP,IAAL,CAAUY,SAAV,GAAsB,KAAK2E,eAAL,CAAqB5E,MAArB,CAAtB;AACA,WAAKT,GAAL,CAASrP,IAAT,EAAe,QAAf,EAAyB8P,MAAzB;;AACA,UAAIvE,IAAJ,EAAU;AACRoB,QAAAA,KAAK,GAAGmD,MAAM,CAACA,MAAM,CAACjX,MAAP,GAAgB,CAAjB,CAAd;AACAmH,QAAAA,IAAI,CAAC8M,MAAL,CAAYS,GAAZ,GAAkB;AAAEnJ,UAAAA,IAAI,EAAEuI,KAAK,CAAC,CAAD,CAAb;AAAkBtI,UAAAA,MAAM,EAAEsI,KAAK,CAAC,CAAD;AAA/B,SAAlB;AACA,aAAKiH,MAAL,GAAc5T,IAAI,CAACmP,IAAL,CAAUM,OAAxB;AACAzP,QAAAA,IAAI,CAACmP,IAAL,CAAUM,OAAV,GAAoB,EAApB;AACD;AACF,KATD,MASO;AACLzP,MAAAA,IAAI,CAACmP,IAAL,CAAUY,SAAV,GAAsB,EAAtB;AACA/P,MAAAA,IAAI,CAAC8P,MAAL,GAAc,EAAd;AACD;;AAED,QAAImF,IAAJ,EAAU;AACRjV,MAAAA,IAAI,CAACH,KAAL,GAAa,EAAb;AACA,WAAK8T,OAAL,GAAe3T,IAAf;AACD;AACF;;SAEDuN,MAAA,aAAIZ,KAAJ,EAAW;AACT,QAAI,KAAKgH,OAAL,CAAa9T,KAAb,IAAsB,KAAK8T,OAAL,CAAa9T,KAAb,CAAmBhH,MAA7C,EAAqD;AACnD,WAAK8a,OAAL,CAAaxE,IAAb,CAAkBJ,SAAlB,GAA8B,KAAKA,SAAnC;AACD;;AACD,SAAKA,SAAL,GAAiB,KAAjB;AAEA,SAAK4E,OAAL,CAAaxE,IAAb,CAAkBb,KAAlB,GAA0B,CAAC,KAAKqF,OAAL,CAAaxE,IAAb,CAAkBb,KAAlB,IAA2B,EAA5B,IAAkC,KAAKsF,MAAjE;AACA,SAAKA,MAAL,GAAc,EAAd;;AAEA,QAAI,KAAKD,OAAL,CAAa7S,MAAjB,EAAyB;AACvB,WAAK6S,OAAL,CAAa7G,MAAb,CAAoBS,GAApB,GAA0B;AAAEnJ,QAAAA,IAAI,EAAEuI,KAAK,CAAC,CAAD,CAAb;AAAkBtI,QAAAA,MAAM,EAAEsI,KAAK,CAAC,CAAD;AAA/B,OAA1B;AACA,WAAKgH,OAAL,GAAe,KAAKA,OAAL,CAAa7S,MAA5B;AACD,KAHD,MAGO;AACL,WAAKoU,eAAL,CAAqBvI,KAArB;AACD;AACF;;SAEDsH,UAAA,mBAAU;AACR,QAAI,KAAKN,OAAL,CAAa7S,MAAjB,EAAyB,KAAKqU,aAAL;;AACzB,QAAI,KAAKxB,OAAL,CAAa9T,KAAb,IAAsB,KAAK8T,OAAL,CAAa9T,KAAb,CAAmBhH,MAA7C,EAAqD;AACnD,WAAK8a,OAAL,CAAaxE,IAAb,CAAkBJ,SAAlB,GAA8B,KAAKA,SAAnC;AACD;;AACD,SAAK4E,OAAL,CAAaxE,IAAb,CAAkBb,KAAlB,GAA0B,CAAC,KAAKqF,OAAL,CAAaxE,IAAb,CAAkBb,KAAlB,IAA2B,EAA5B,IAAkC,KAAKsF,MAAjE;AACD;;;SAIDM,OAAA,cAAKlU,IAAL,EAAWoE,IAAX,EAAiBC,MAAjB,EAAyB;AACvB,SAAKsP,OAAL,CAAalY,IAAb,CAAkBuE,IAAlB;AAEAA,IAAAA,IAAI,CAAC8M,MAAL,GAAc;AAAEO,MAAAA,KAAK,EAAE;AAAEjJ,QAAAA,IAAI,EAAJA,IAAF;AAAQC,QAAAA,MAAM,EAANA;AAAR,OAAT;AAA2B0G,MAAAA,KAAK,EAAE,KAAKA;AAAvC,KAAd;AACA/K,IAAAA,IAAI,CAACmP,IAAL,CAAUa,MAAV,GAAmB,KAAK4D,MAAxB;AACA,SAAKA,MAAL,GAAc,EAAd;AACA,QAAI5T,IAAI,CAACgP,IAAL,KAAc,SAAlB,EAA6B,KAAKD,SAAL,GAAiB,KAAjB;AAC9B;;SAEDM,MAAA,aAAIrP,IAAJ,EAAU0P,IAAV,EAAgB1E,MAAhB,EAAwB;AACtB,QAAI2B,KAAJ,EAAWqC,IAAX;AACA,QAAMnW,MAAM,GAAGmS,MAAM,CAACnS,MAAtB;AACA,QAAIkP,KAAK,GAAG,EAAZ;AACA,QAAIqN,KAAK,GAAG,IAAZ;;AACA,SAAK,IAAIxc,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGC,MAApB,EAA4BD,CAAC,IAAI,CAAjC,EAAoC;AAClC+T,MAAAA,KAAK,GAAG3B,MAAM,CAACpS,CAAD,CAAd;AACAoW,MAAAA,IAAI,GAAGrC,KAAK,CAAC,CAAD,CAAZ;;AACA,UAAIqC,IAAI,KAAK,SAAT,IAAuBA,IAAI,KAAK,OAAT,IAAoBpW,CAAC,KAAKC,MAAM,GAAG,CAA9D,EAAkE;AAChEuc,QAAAA,KAAK,GAAG,KAAR;AACD,OAFD,MAEO;AACLrN,QAAAA,KAAK,IAAI4E,KAAK,CAAC,CAAD,CAAd;AACD;AACF;;AACD,QAAI,CAACyI,KAAL,EAAY;AACV,UAAM/F,GAAG,GAAGrE,MAAM,CAAC3E,MAAP,CAAc,UAACgP,GAAD,EAAMzc,CAAN;AAAA,eAAYyc,GAAG,GAAGzc,CAAC,CAAC,CAAD,CAAnB;AAAA,OAAd,EAAsC,EAAtC,CAAZ;AACAoH,MAAAA,IAAI,CAACmP,IAAL,CAAUO,IAAV,IAAkB;AAAE3H,QAAAA,KAAK,EAALA,KAAF;AAASsH,QAAAA,GAAG,EAAHA;AAAT,OAAlB;AACD;;AACDrP,IAAAA,IAAI,CAAC0P,IAAD,CAAJ,GAAa3H,KAAb;AACD;;SAEDyM,gBAAA,uBAAcxJ,MAAd,EAAsB;AACpB,QAAIsK,aAAJ;AACA,QAAI1B,MAAM,GAAG,EAAb;;AACA,WAAO5I,MAAM,CAACnS,MAAd,EAAsB;AACpByc,MAAAA,aAAa,GAAGtK,MAAM,CAACA,MAAM,CAACnS,MAAP,GAAgB,CAAjB,CAAN,CAA0B,CAA1B,CAAhB;AACA,UAAIyc,aAAa,KAAK,OAAlB,IAA6BA,aAAa,KAAK,SAAnD,EAA8D;AAC9D1B,MAAAA,MAAM,GAAG5I,MAAM,CAACqJ,GAAP,GAAa,CAAb,IAAkBT,MAA3B;AACD;;AACD,WAAOA,MAAP;AACD;;SAEDc,kBAAA,yBAAgB1J,MAAhB,EAAwB;AACtB,QAAII,IAAJ;AACA,QAAIwI,MAAM,GAAG,EAAb;;AACA,WAAO5I,MAAM,CAACnS,MAAd,EAAsB;AACpBuS,MAAAA,IAAI,GAAGJ,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV,CAAP;AACA,UAAII,IAAI,KAAK,OAAT,IAAoBA,IAAI,KAAK,SAAjC,EAA4C;AAC5CwI,MAAAA,MAAM,IAAI5I,MAAM,CAACyJ,KAAP,GAAe,CAAf,CAAV;AACD;;AACD,WAAOb,MAAP;AACD;;SAEDgB,aAAA,oBAAW5J,MAAX,EAAmBuK,IAAnB,EAAyB;AACvB,QAAItM,MAAM,GAAG,EAAb;;AACA,SAAK,IAAIrQ,CAAC,GAAG2c,IAAb,EAAmB3c,CAAC,GAAGoS,MAAM,CAACnS,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;AACzCqQ,MAAAA,MAAM,IAAI+B,MAAM,CAACpS,CAAD,CAAN,CAAU,CAAV,CAAV;AACD;;AACDoS,IAAAA,MAAM,CAACzI,MAAP,CAAcgT,IAAd,EAAoBvK,MAAM,CAACnS,MAAP,GAAgB0c,IAApC;AACA,WAAOtM,MAAP;AACD;;SAED8E,QAAA,eAAM/C,MAAN,EAAc;AACZ,QAAIsB,QAAQ,GAAG,CAAf;AACA,QAAIK,KAAJ,EAAWqC,IAAX,EAAiBnD,IAAjB;;AACA,SAAK,IAAIjT,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoS,MAAM,CAACnS,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;AACtC+T,MAAAA,KAAK,GAAG3B,MAAM,CAACpS,CAAD,CAAd;AACAoW,MAAAA,IAAI,GAAGrC,KAAK,CAAC,CAAD,CAAZ;;AAEA,UAAIqC,IAAI,KAAK,GAAb,EAAkB;AAChB1C,QAAAA,QAAQ,IAAI,CAAZ;AACD,OAFD,MAEO,IAAI0C,IAAI,KAAK,GAAb,EAAkB;AACvB1C,QAAAA,QAAQ,IAAI,CAAZ;AACD,OAFM,MAEA,IAAIA,QAAQ,KAAK,CAAb,IAAkB0C,IAAI,KAAK,GAA/B,EAAoC;AACzC,YAAI,CAACnD,IAAL,EAAW;AACT,eAAK2J,WAAL,CAAiB7I,KAAjB;AACD,SAFD,MAEO,IAAId,IAAI,CAAC,CAAD,CAAJ,KAAY,MAAZ,IAAsBA,IAAI,CAAC,CAAD,CAAJ,KAAY,QAAtC,EAAgD;AACrD;AACD,SAFM,MAEA;AACL,iBAAOjT,CAAP;AACD;AACF;;AAEDiT,MAAAA,IAAI,GAAGc,KAAP;AACD;;AACD,WAAO,KAAP;AACD;;;SAID2H,kBAAA,yBAAgBF,OAAhB,EAAyB;AACvB,UAAM,KAAKrJ,KAAL,CAAWmB,KAAX,CAAiB,kBAAjB,EAAqCkI,OAAO,CAAC,CAAD,CAA5C,EAAiDA,OAAO,CAAC,CAAD,CAAxD,CAAN;AACD;;SAEDG,cAAA,qBAAYlH,KAAZ,EAAmB;AACjB,QAAMV,KAAK,GAAG,KAAK3B,MAAL,CAAYqC,KAAZ,CAAd;AACA,UAAM,KAAKtC,KAAL,CAAWmB,KAAX,CAAiB,cAAjB,EAAiCS,KAAK,CAAC,CAAD,CAAtC,EAA2CA,KAAK,CAAC,CAAD,CAAhD,CAAN;AACD;;SAEDuI,kBAAA,yBAAgBvI,KAAhB,EAAuB;AACrB,UAAM,KAAK5B,KAAL,CAAWmB,KAAX,CAAiB,cAAjB,EAAiCS,KAAK,CAAC,CAAD,CAAtC,EAA2CA,KAAK,CAAC,CAAD,CAAhD,CAAN;AACD;;SAEDwI,gBAAA,yBAAgB;AACd,QAAMpJ,GAAG,GAAG,KAAK4H,OAAL,CAAa7G,MAAb,CAAoBO,KAAhC;AACA,UAAM,KAAKtC,KAAL,CAAWmB,KAAX,CAAiB,gBAAjB,EAAmCH,GAAG,CAAC3H,IAAvC,EAA6C2H,GAAG,CAAC1H,MAAjD,CAAN;AACD;;SAEDmR,cAAA,qBAAY7I,KAAZ,EAAmB;AACjB,UAAM,KAAK5B,KAAL,CAAWmB,KAAX,CAAiB,cAAjB,EAAiCS,KAAK,CAAC,CAAD,CAAtC,EAA2CA,KAAK,CAAC,CAAD,CAAhD,CAAN;AACD;;SAEDqI,gBAAA,uBAAchV,IAAd,EAAoB2M,KAApB,EAA2B;AACzB,UAAM,KAAK5B,KAAL,CAAWmB,KAAX,CAAiB,sBAAjB,EAAyCS,KAAK,CAAC,CAAD,CAA9C,EAAmDA,KAAK,CAAC,CAAD,CAAxD,CAAN;AACD;;SAEDgI,0BAAA,iCAAwB3J,MAAxB,EAAgC;AAC9B,AAED;;SAED8J,uBAAA,8BAAqB9J,MAArB,EAA6B;AAC3B,QAAM+C,KAAK,GAAG,KAAKA,KAAL,CAAW/C,MAAX,CAAd;AACA,QAAI+C,KAAK,KAAK,KAAd,EAAqB;AAErB,QAAI0H,OAAO,GAAG,CAAd;AACA,QAAI9I,KAAJ;;AACA,SAAK,IAAI8E,CAAC,GAAG1D,KAAK,GAAG,CAArB,EAAwB0D,CAAC,IAAI,CAA7B,EAAgCA,CAAC,EAAjC,EAAqC;AACnC9E,MAAAA,KAAK,GAAG3B,MAAM,CAACyG,CAAD,CAAd;;AACA,UAAI9E,KAAK,CAAC,CAAD,CAAL,KAAa,OAAjB,EAA0B;AACxB8I,QAAAA,OAAO,IAAI,CAAX;AACA,YAAIA,OAAO,KAAK,CAAhB,EAAmB;AACpB;AACF;;AACD,UAAM,KAAK1K,KAAL,CAAWmB,KAAX,CAAiB,kBAAjB,EAAqCS,KAAK,CAAC,CAAD,CAA1C,EAA+CA,KAAK,CAAC,CAAD,CAApD,CAAN;AACD;;;;;AC/cH;AACA,AAGe,SAAS+I,KAAT,CAAejY,GAAf,EAAoBmU,IAApB,EAA0B;AACvC,MAAIA,IAAI,IAAIA,IAAI,CAAC+D,IAAjB,EAAuB;AACrB,UAAM,IAAI7Z,KAAJ,CAAU,8BAA8B,4CAAxC,CAAN;AACD;;AAED,MAAMiP,KAAK,GAAG,IAAI2B,KAAJ,CAAUjP,GAAV,EAAemU,IAAf,CAAd;AAEA,MAAMgE,MAAM,GAAG,IAAInC,MAAJ,CAAW1I,KAAX,CAAf;;AACA,MAAI;AACF6K,IAAAA,MAAM,CAAC9K,QAAP;AACA8K,IAAAA,MAAM,CAAC9B,IAAP;AACD,GAHD,CAGE,OAAO/P,CAAP,EAAU;AACV,QAAIA,CAAC,CAAC3L,IAAF,KAAW,gBAAX,IAA+BwZ,IAA/B,IAAuCA,IAAI,CAAC2D,IAAhD,EAAsD;AACpD,UAAI,WAAW/b,IAAX,CAAgBoY,IAAI,CAAC2D,IAArB,CAAJ,EAAgC;AAC9BxR,QAAAA,CAAC,CAACwF,OAAF,IACE,oCACA,2BADA,GAEA,wCAHF;AAID,OALD,MAKO,IAAI,WAAW/P,IAAX,CAAgBoY,IAAI,CAAC2D,IAArB,CAAJ,EAAgC;AACrCxR,QAAAA,CAAC,CAACwF,OAAF,IACE,oCACA,2BADA,GAEA,wCAHF;AAID;AACF;;AACD,UAAMxF,CAAN;AACD;;AAED,SAAO6R,MAAM,CAAC3G,IAAd;AACD;;ACvBD,SAAS4G,WAAT,CAAqBhW,KAArB,EAA4B;AAC1B,SAAOA,KAAK,CAAC+M,GAAN,CAAU,UAAAhU,CAAC,EAAI;AACpB,QAAIA,CAAC,CAACiH,KAAN,EAAajH,CAAC,CAACiH,KAAF,GAAUgW,WAAW,CAACjd,CAAC,CAACiH,KAAH,CAArB;AACb,WAAOjH,CAAC,CAACkU,MAAT;AACA,WAAOlU,CAAP;AACD,GAJM,CAAP;AAKD;AAED;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;;;;IAUMkd;;;;;;;;;SACJra,OAAA,cAAKgF,KAAL,EAAY;AACVA,IAAAA,KAAK,CAACK,MAAN,GAAe,IAAf;AACA,SAAKjB,KAAL,CAAWpE,IAAX,CAAgBgF,KAAhB;AACA,WAAO,IAAP;AACD;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAiCAsV,OAAA,cAAKC,QAAL,EAAe;AACb,QAAI,CAAC,KAAKC,QAAV,EAAoB,KAAKA,QAAL,GAAgB,CAAhB;AACpB,QAAI,CAAC,KAAKC,OAAV,EAAmB,KAAKA,OAAL,GAAe,EAAf;AAEnB,SAAKD,QAAL,IAAiB,CAAjB;AACA,QAAMhY,EAAE,GAAG,KAAKgY,QAAhB;AACA,SAAKC,OAAL,CAAajY,EAAb,IAAmB,CAAnB;AAEA,QAAI,CAAC,KAAK4B,KAAV,EAAiB,OAAOjB,SAAP;AAEjB,QAAInC,KAAJ,EAAWwM,MAAX;;AACA,WAAO,KAAKiN,OAAL,CAAajY,EAAb,IAAmB,KAAK4B,KAAL,CAAWhH,MAArC,EAA6C;AAC3C4D,MAAAA,KAAK,GAAG,KAAKyZ,OAAL,CAAajY,EAAb,CAAR;AACAgL,MAAAA,MAAM,GAAG+M,QAAQ,CAAC,KAAKnW,KAAL,CAAWpD,KAAX,CAAD,EAAoBA,KAApB,CAAjB;AACA,UAAIwM,MAAM,KAAK,KAAf,EAAsB;AAEtB,WAAKiN,OAAL,CAAajY,EAAb,KAAoB,CAApB;AACD;;AAED,WAAO,KAAKiY,OAAL,CAAajY,EAAb,CAAP;AAEA,WAAOgL,MAAP;AACD;AAED;;;;;;;;;;;;;;;;;;;;;SAmBAsH,OAAA,cAAKyF,QAAL,EAAe;AACb,WAAO,KAAKD,IAAL,CAAU,UAACtV,KAAD,EAAQ7H,CAAR,EAAc;AAC7B,UAAIqQ,MAAM,GAAG+M,QAAQ,CAACvV,KAAD,EAAQ7H,CAAR,CAArB;;AACA,UAAIqQ,MAAM,KAAK,KAAX,IAAoBxI,KAAK,CAAC8P,IAA9B,EAAoC;AAClCtH,QAAAA,MAAM,GAAGxI,KAAK,CAAC8P,IAAN,CAAWyF,QAAX,CAAT;AACD;;AACD,aAAO/M,MAAP;AACD,KANM,CAAP;AAOD;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SA6BA8H,YAAA,mBAAUrB,IAAV,EAAgBsG,QAAhB,EAA0B;AACxB,QAAI,CAACA,QAAL,EAAe;AACbA,MAAAA,QAAQ,GAAGtG,IAAX;AACA,aAAO,KAAKa,IAAL,CAAU,UAAC9P,KAAD,EAAQ7H,CAAR,EAAc;AAC7B,YAAI6H,KAAK,CAACuO,IAAN,KAAe,MAAnB,EAA2B;AACzB,iBAAOgH,QAAQ,CAACvV,KAAD,EAAQ7H,CAAR,CAAf;AACD;AACF,OAJM,CAAP;AAKD,KAPD,MAOO,IAAI8W,IAAI,YAAYlR,MAApB,EAA4B;AACjC,aAAO,KAAK+R,IAAL,CAAU,UAAC9P,KAAD,EAAQ7H,CAAR,EAAc;AAC7B,YAAI6H,KAAK,CAACuO,IAAN,KAAe,MAAf,IAAyBU,IAAI,CAAClW,IAAL,CAAUiH,KAAK,CAACiP,IAAhB,CAA7B,EAAoD;AAClD,iBAAOsG,QAAQ,CAACvV,KAAD,EAAQ7H,CAAR,CAAf;AACD;AACF,OAJM,CAAP;AAKD,KANM,MAMA;AACL,aAAO,KAAK2X,IAAL,CAAU,UAAC9P,KAAD,EAAQ7H,CAAR,EAAc;AAC7B,YAAI6H,KAAK,CAACuO,IAAN,KAAe,MAAf,IAAyBvO,KAAK,CAACiP,IAAN,KAAeA,IAA5C,EAAkD;AAChD,iBAAOsG,QAAQ,CAACvV,KAAD,EAAQ7H,CAAR,CAAf;AACD;AACF,OAJM,CAAP;AAKD;AACF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;SAuBAud,YAAA,mBAAUpX,QAAV,EAAoBiX,QAApB,EAA8B;AAC5B,QAAI,CAACA,QAAL,EAAe;AACbA,MAAAA,QAAQ,GAAGjX,QAAX;AAEA,aAAO,KAAKwR,IAAL,CAAU,UAAC9P,KAAD,EAAQ7H,CAAR,EAAc;AAC7B,YAAI6H,KAAK,CAACuO,IAAN,KAAe,MAAnB,EAA2B;AACzB,iBAAOgH,QAAQ,CAACvV,KAAD,EAAQ7H,CAAR,CAAf;AACD;AACF,OAJM,CAAP;AAKD,KARD,MAQO,IAAImG,QAAQ,YAAYP,MAAxB,EAAgC;AACrC,aAAO,KAAK+R,IAAL,CAAU,UAAC9P,KAAD,EAAQ7H,CAAR,EAAc;AAC7B,YAAI6H,KAAK,CAACuO,IAAN,KAAe,MAAf,IAAyBjQ,QAAQ,CAACvF,IAAT,CAAciH,KAAK,CAAC1B,QAApB,CAA7B,EAA4D;AAC1D,iBAAOiX,QAAQ,CAACvV,KAAD,EAAQ7H,CAAR,CAAf;AACD;AACF,OAJM,CAAP;AAKD,KANM,MAMA;AACL,aAAO,KAAK2X,IAAL,CAAU,UAAC9P,KAAD,EAAQ7H,CAAR,EAAc;AAC7B,YAAI6H,KAAK,CAACuO,IAAN,KAAe,MAAf,IAAyBvO,KAAK,CAAC1B,QAAN,KAAmBA,QAAhD,EAA0D;AACxD,iBAAOiX,QAAQ,CAACvV,KAAD,EAAQ7H,CAAR,CAAf;AACD;AACF,OAJM,CAAP;AAKD;AACF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SA8BAwd,cAAA,qBAAYhe,IAAZ,EAAkB4d,QAAlB,EAA4B;AAC1B,QAAI,CAACA,QAAL,EAAe;AACbA,MAAAA,QAAQ,GAAG5d,IAAX;AACA,aAAO,KAAKmY,IAAL,CAAU,UAAC9P,KAAD,EAAQ7H,CAAR,EAAc;AAC7B,YAAI6H,KAAK,CAACuO,IAAN,KAAe,QAAnB,EAA6B;AAC3B,iBAAOgH,QAAQ,CAACvV,KAAD,EAAQ7H,CAAR,CAAf;AACD;AACF,OAJM,CAAP;AAKD,KAPD,MAOO,IAAIR,IAAI,YAAYoG,MAApB,EAA4B;AACjC,aAAO,KAAK+R,IAAL,CAAU,UAAC9P,KAAD,EAAQ7H,CAAR,EAAc;AAC7B,YAAI6H,KAAK,CAACuO,IAAN,KAAe,QAAf,IAA2B5W,IAAI,CAACoB,IAAL,CAAUiH,KAAK,CAACrI,IAAhB,CAA/B,EAAsD;AACpD,iBAAO4d,QAAQ,CAACvV,KAAD,EAAQ7H,CAAR,CAAf;AACD;AACF,OAJM,CAAP;AAKD,KANM,MAMA;AACL,aAAO,KAAK2X,IAAL,CAAU,UAAC9P,KAAD,EAAQ7H,CAAR,EAAc;AAC7B,YAAI6H,KAAK,CAACuO,IAAN,KAAe,QAAf,IAA2BvO,KAAK,CAACrI,IAAN,KAAeA,IAA9C,EAAoD;AAClD,iBAAO4d,QAAQ,CAACvV,KAAD,EAAQ7H,CAAR,CAAf;AACD;AACF,OAJM,CAAP;AAKD;AACF;AAED;;;;;;;;;;;;;;;;;;SAgBAiY,eAAA,sBAAamF,QAAb,EAAuB;AACrB,WAAO,KAAKzF,IAAL,CAAU,UAAC9P,KAAD,EAAQ7H,CAAR,EAAc;AAC7B,UAAI6H,KAAK,CAACuO,IAAN,KAAe,SAAnB,EAA8B;AAC5B,eAAOgH,QAAQ,CAACvV,KAAD,EAAQ7H,CAAR,CAAf;AACD;AACF,KAJM,CAAP;AAKD;AAED;;;;;;;;;;;;;;;;;;;;;;SAoBA8Z,SAAA,kBAAoB;AAAA;;AAAA,sCAAV2D,QAAU;AAAVA,MAAAA,QAAU;AAAA;;AAClBA,IAAAA,QAAQ,CAAC3a,OAAT,CAAiB,UAAA+E,KAAK,EAAI;AACxB,UAAMZ,KAAK,GAAG,KAAI,CAACyW,SAAL,CAAe7V,KAAf,EAAsB,KAAI,CAAC8K,IAA3B,CAAd;;AACA1L,MAAAA,KAAK,CAACnE,OAAN,CAAc,UAAAsE,IAAI;AAAA,eAAI,KAAI,CAACH,KAAL,CAAWpE,IAAX,CAAgBuE,IAAhB,CAAJ;AAAA,OAAlB;AACD,KAHD;AAIA,WAAO,IAAP;AACD;AAED;;;;;;;;;;;;;;;;;;;;;;SAoBAuW,UAAA,mBAAqB;AAAA;;AAAA,uCAAVF,QAAU;AAAVA,MAAAA,QAAU;AAAA;;AACnBA,IAAAA,QAAQ,GAAGA,QAAQ,CAACG,OAAT,EAAX;AACAH,IAAAA,QAAQ,CAAC3a,OAAT,CAAiB,UAAA+E,KAAK,EAAI;AACxB,UAAMZ,KAAK,GAAG,MAAI,CAACyW,SAAL,CAAe7V,KAAf,EAAsB,MAAI,CAAC0P,KAA3B,EAAkC,SAAlC,EAA6CqG,OAA7C,EAAd;;AACA3W,MAAAA,KAAK,CAACnE,OAAN,CAAc,UAAAsE,IAAI;AAAA,eAAI,MAAI,CAACH,KAAL,CAAW4W,OAAX,CAAmBzW,IAAnB,CAAJ;AAAA,OAAlB;;AACA,WAAK,IAAM/B,EAAX,IAAiB,MAAI,CAACiY,OAAtB,EAA+B;AAC7B,QAAA,MAAI,CAACA,OAAL,CAAajY,EAAb,IAAmB,MAAI,CAACiY,OAAL,CAAajY,EAAb,IAAmB4B,KAAK,CAAChH,MAA5C;AACD;AACF,KAND;AAOA,WAAO,IAAP;AACD;;SAED4Z,YAAA,mBAAUQ,WAAV,EAAuB;AACrB,oBAAMR,SAAN,YAAgBQ,WAAhB;;AACA,QAAI,KAAKpT,KAAT,EAAgB;AACd,WAAKA,KAAL,CAAWnE,OAAX,CAAmB,UAAAsE,IAAI;AAAA,eAAIA,IAAI,CAACyS,SAAL,CAAeQ,WAAf,CAAJ;AAAA,OAAvB;AACD;AACF;AAED;;;;;;;;;;;;;SAWA7R,eAAA,sBAAasV,KAAb,EAAoBpT,GAApB,EAAyB;AAAA;;AACvBoT,IAAAA,KAAK,GAAG,KAAKja,KAAL,CAAWia,KAAX,CAAR;AAEA,QAAM1H,IAAI,GAAG0H,KAAK,KAAK,CAAV,GAAc,SAAd,GAA0B,KAAvC;AACA,QAAM7W,KAAK,GAAG,KAAKyW,SAAL,CAAehT,GAAf,EAAoB,KAAKzD,KAAL,CAAW6W,KAAX,CAApB,EAAuC1H,IAAvC,EAA6CwH,OAA7C,EAAd;AACA3W,IAAAA,KAAK,CAACnE,OAAN,CAAc,UAAAsE,IAAI;AAAA,aAAI,MAAI,CAACH,KAAL,CAAW0C,MAAX,CAAkBmU,KAAlB,EAAyB,CAAzB,EAA4B1W,IAA5B,CAAJ;AAAA,KAAlB;AAEA,QAAIvD,KAAJ;;AACA,SAAK,IAAMwB,EAAX,IAAiB,KAAKiY,OAAtB,EAA+B;AAC7BzZ,MAAAA,KAAK,GAAG,KAAKyZ,OAAL,CAAajY,EAAb,CAAR;;AACA,UAAIyY,KAAK,IAAIja,KAAb,EAAoB;AAClB,aAAKyZ,OAAL,CAAajY,EAAb,IAAmBxB,KAAK,GAAGoD,KAAK,CAAChH,MAAjC;AACD;AACF;;AAED,WAAO,IAAP;AACD;AAED;;;;;;;;;;SAQAwZ,cAAA,qBAAYqE,KAAZ,EAAmBpT,GAAnB,EAAwB;AAAA;;AACtBoT,IAAAA,KAAK,GAAG,KAAKja,KAAL,CAAWia,KAAX,CAAR;AAEA,QAAM7W,KAAK,GAAG,KAAKyW,SAAL,CAAehT,GAAf,EAAoB,KAAKzD,KAAL,CAAW6W,KAAX,CAApB,EAAuCF,OAAvC,EAAd;AACA3W,IAAAA,KAAK,CAACnE,OAAN,CAAc,UAAAsE,IAAI;AAAA,aAAI,MAAI,CAACH,KAAL,CAAW0C,MAAX,CAAkBmU,KAAK,GAAG,CAA1B,EAA6B,CAA7B,EAAgC1W,IAAhC,CAAJ;AAAA,KAAlB;AAEA,QAAIvD,KAAJ;;AACA,SAAK,IAAMwB,EAAX,IAAiB,KAAKiY,OAAtB,EAA+B;AAC7BzZ,MAAAA,KAAK,GAAG,KAAKyZ,OAAL,CAAajY,EAAb,CAAR;;AACA,UAAIyY,KAAK,GAAGja,KAAZ,EAAmB;AACjB,aAAKyZ,OAAL,CAAajY,EAAb,IAAmBxB,KAAK,GAAGoD,KAAK,CAAChH,MAAjC;AACD;AACF;;AAED,WAAO,IAAP;AACD;;SAEDkZ,SAAA,gBAAOtR,KAAP,EAAc;AACZ,QAAI,OAAOA,KAAP,KAAiB,WAArB,EAAkC;AAChC6I,MAAAA,QAAQ,CAAC,qCAAqC,2BAAtC,CAAR;AACA,WAAKnJ,WAAL,CAAiBM,KAAjB;AACD,KAHD,MAGO;AACL,sBAAMsR,MAAN;AACD;;AACD,WAAO,IAAP;AACD;AAED;;;;;;;;;;;;;;;;SAcA5R,cAAA,qBAAYM,KAAZ,EAAmB;AACjBA,IAAAA,KAAK,GAAG,KAAKhE,KAAL,CAAWgE,KAAX,CAAR;AACA,SAAKZ,KAAL,CAAWY,KAAX,EAAkBK,MAAlB,GAA2BlC,SAA3B;AACA,SAAKiB,KAAL,CAAW0C,MAAX,CAAkB9B,KAAlB,EAAyB,CAAzB;AAEA,QAAIhE,KAAJ;;AACA,SAAK,IAAMwB,EAAX,IAAiB,KAAKiY,OAAtB,EAA+B;AAC7BzZ,MAAAA,KAAK,GAAG,KAAKyZ,OAAL,CAAajY,EAAb,CAAR;;AACA,UAAIxB,KAAK,IAAIgE,KAAb,EAAoB;AAClB,aAAKyV,OAAL,CAAajY,EAAb,IAAmBxB,KAAK,GAAG,CAA3B;AACD;AACF;;AAED,WAAO,IAAP;AACD;AAED;;;;;;;;;;;;SAUAka,YAAA,qBAAY;AACV,SAAK9W,KAAL,CAAWnE,OAAX,CAAmB,UAAAsE,IAAI;AAAA,aAAKA,IAAI,CAACc,MAAL,GAAclC,SAAnB;AAAA,KAAvB;AACA,SAAKiB,KAAL,GAAa,EAAb;AACA,WAAO,IAAP;AACD;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SA6BA+W,gBAAA,uBAAcC,OAAd,EAAuBjF,IAAvB,EAA6BoE,QAA7B,EAAuC;AACrC,QAAI,CAACA,QAAL,EAAe;AACbA,MAAAA,QAAQ,GAAGpE,IAAX;AACAA,MAAAA,IAAI,GAAG,EAAP;AACD;;AAED,SAAKb,SAAL,CAAe,UAAAvB,IAAI,EAAI;AACrB,UAAIoC,IAAI,CAACkF,KAAL,IAAclF,IAAI,CAACkF,KAAL,CAAWnR,OAAX,CAAmB6J,IAAI,CAACE,IAAxB,MAAkC,CAAC,CAArD,EAAwD;AACxD,UAAIkC,IAAI,CAACmF,IAAL,IAAavH,IAAI,CAACzH,KAAL,CAAWpC,OAAX,CAAmBiM,IAAI,CAACmF,IAAxB,MAAkC,CAAC,CAApD,EAAuD;AAEvDvH,MAAAA,IAAI,CAACzH,KAAL,GAAayH,IAAI,CAACzH,KAAL,CAAWvP,OAAX,CAAmBqe,OAAnB,EAA4Bb,QAA5B,CAAb;AACD,KALD;AAOA,WAAO,IAAP;AACD;AAED;;;;;;;;;;;;;SAWAgB,QAAA,eAAMC,SAAN,EAAiB;AACf,WAAO,KAAKpX,KAAL,CAAWmX,KAAX,CAAiBC,SAAjB,CAAP;AACD;AAED;;;;;;;;;;;;;SAWAC,OAAA,cAAKD,SAAL,EAAgB;AACd,WAAO,KAAKpX,KAAL,CAAWqX,IAAX,CAAgBD,SAAhB,CAAP;AACD;AAED;;;;;;;;;;;;SAUAxa,QAAA,eAAMgE,KAAN,EAAa;AACX,QAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;AAC7B,aAAOA,KAAP;AACD,KAFD,MAEO;AACL,aAAO,KAAKZ,KAAL,CAAW8F,OAAX,CAAmBlF,KAAnB,CAAP;AACD;AACF;AAED;;;;;;;;;;SA0BA6V,YAAA,mBAAUzW,KAAV,EAAiBsX,MAAjB,EAAyB;AAAA;;AACvB,QAAI,OAAOtX,KAAP,KAAiB,QAArB,EAA+B;AAC7BA,MAAAA,KAAK,GAAGgW,WAAW,CAACH,KAAK,CAAC7V,KAAD,CAAL,CAAaA,KAAd,CAAnB;AACD,KAFD,MAEO,IAAI,CAAC4I,KAAK,CAACC,OAAN,CAAc7I,KAAd,CAAL,EAA2B;AAChC,UAAIA,KAAK,CAACmP,IAAN,KAAe,MAAnB,EAA2B;AACzBnP,QAAAA,KAAK,GAAGA,KAAK,CAACA,KAAd;AACD,OAFD,MAEO,IAAIA,KAAK,CAACmP,IAAV,EAAgB;AACrBnP,QAAAA,KAAK,GAAG,CAACA,KAAD,CAAR;AACD,OAFM,MAEA,IAAIA,KAAK,CAAC6P,IAAV,EAAgB;AACrB,YAAI,OAAO7P,KAAK,CAACkI,KAAb,KAAuB,WAA3B,EAAwC;AACtC,gBAAM,IAAIjM,KAAJ,CAAU,wCAAV,CAAN;AACD,SAFD,MAEO,IAAI,OAAO+D,KAAK,CAACkI,KAAb,KAAuB,QAA3B,EAAqC;AAC1ClI,UAAAA,KAAK,CAACkI,KAAN,GAAc9P,MAAM,CAAC4H,KAAK,CAACkI,KAAP,CAApB;AACD;;AACDlI,QAAAA,KAAK,GAAG,CAAC,IAAI0T,WAAJ,CAAgB1T,KAAhB,CAAD,CAAR;AACD,OAPM,MAOA,IAAIA,KAAK,CAACd,QAAV,EAAoB;AACzBc,QAAAA,KAAK,GAAG,CAAC,IAAIsU,IAAJ,CAAStU,KAAT,CAAD,CAAR;AACD,OAFM,MAEA,IAAIA,KAAK,CAACzH,IAAV,EAAgB;AACrByH,QAAAA,KAAK,GAAG,CAAC,IAAIkV,MAAJ,CAAWlV,KAAX,CAAD,CAAR;AACD,OAFM,MAEA,IAAIA,KAAK,CAAC0P,IAAV,EAAgB;AACrB1P,QAAAA,KAAK,GAAG,CAAC,IAAI2T,OAAJ,CAAY3T,KAAZ,CAAD,CAAR;AACD,OAFM,MAEA;AACL,cAAM,IAAI/D,KAAJ,CAAU,oCAAV,CAAN;AACD;AACF;;AAED,QAAMsb,SAAS,GAAGvX,KAAK,CAAC+M,GAAN,CAAU,UAAAhU,CAAC,EAAI;AAC/B,UAAI,OAAOA,CAAC,CAACuW,IAAT,KAAkB,WAAtB,EAAmCvW,CAAC,GAAG,MAAI,CAACye,OAAL,CAAaze,CAAb,CAAJ;AAEnC,UAAIA,CAAC,CAACkI,MAAN,EAAclI,CAAC,GAAGA,CAAC,CAACqZ,KAAF,EAAJ;;AACd,UAAI,OAAOrZ,CAAC,CAACuW,IAAF,CAAOa,MAAd,KAAyB,WAA7B,EAA0C;AACxC,YAAImH,MAAM,IAAI,OAAOA,MAAM,CAAChI,IAAP,CAAYa,MAAnB,KAA8B,WAA5C,EAAyD;AACvDpX,UAAAA,CAAC,CAACuW,IAAF,CAAOa,MAAP,GAAgBmH,MAAM,CAAChI,IAAP,CAAYa,MAAZ,CAAmBxX,OAAnB,CAA2B,QAA3B,EAAqC,EAArC,CAAhB;AACD;AACF;;AACDI,MAAAA,CAAC,CAACkI,MAAF,GAAW,MAAX;AACA,aAAOlI,CAAP;AACD,KAXiB,CAAlB;AAaA,WAAOwe,SAAP;AACD;;SAEDC,UAAA,iBAAQrX,IAAR,EAAcc,MAAd,EAAsB;AAAA;;AACpB,QAAIwW,GAAJ;;AACA,QAAItX,IAAI,CAACgP,IAAL,KAAc,MAAlB,EAA0B;AACxBsI,MAAAA,GAAG,GAAG,IAAI5D,IAAJ,EAAN;AACD,KAFD,MAEO,IAAI1T,IAAI,CAACgP,IAAL,KAAc,QAAlB,EAA4B;AACjCsI,MAAAA,GAAG,GAAG,IAAIvC,MAAJ,EAAN;AACD,KAFM,MAEA,IAAI/U,IAAI,CAACgP,IAAL,KAAc,MAAlB,EAA0B;AAC/BsI,MAAAA,GAAG,GAAG,IAAInD,IAAJ,EAAN;AACD,KAFM,MAEA,IAAInU,IAAI,CAACgP,IAAL,KAAc,MAAlB,EAA0B;AAC/BsI,MAAAA,GAAG,GAAG,IAAI/D,WAAJ,EAAN;AACD,KAFM,MAEA,IAAIvT,IAAI,CAACgP,IAAL,KAAc,SAAlB,EAA6B;AAClCsI,MAAAA,GAAG,GAAG,IAAI9D,OAAJ,EAAN;AACD;;AAED,SAAK,IAAM5a,CAAX,IAAgBoH,IAAhB,EAAsB;AACpB,UAAIpH,CAAC,KAAK,OAAV,EAAmB;AACjB0e,QAAAA,GAAG,CAACzX,KAAJ,GAAYG,IAAI,CAACH,KAAL,CAAW+M,GAAX,CAAe,UAAA6E,CAAC;AAAA,iBAAI,MAAI,CAAC4F,OAAL,CAAa5F,CAAb,EAAgB6F,GAAhB,CAAJ;AAAA,SAAhB,CAAZ;AACD,OAFD,MAEO,IAAI1e,CAAC,KAAK,QAAN,IAAkBkI,MAAtB,EAA8B;AACnCwW,QAAAA,GAAG,CAACxW,MAAJ,GAAaA,MAAb;AACD,OAFM,MAEA,IAAId,IAAI,CAACwI,cAAL,CAAoB5P,CAApB,CAAJ,EAA4B;AACjC0e,QAAAA,GAAG,CAAC1e,CAAD,CAAH,GAASoH,IAAI,CAACpH,CAAD,CAAb;AACD;AACF;;AAED,WAAO0e,GAAP;AACD;;SAEDC,aAAA,oBAAWvB,QAAX,EAAqB;AACnB1M,IAAAA,QAAQ,CAAC,yCAAyC,6BAA1C,CAAR;AACA,WAAO,KAAKiH,IAAL,CAAUyF,QAAV,CAAP;AACD;;SAEDwB,WAAA,kBAAS9H,IAAT,EAAesG,QAAf,EAAyB;AACvB1M,IAAAA,QAAQ,CAAC,uCAAuC,kCAAxC,CAAR;AACA,WAAO,KAAKyH,SAAL,CAAerB,IAAf,EAAqBsG,QAArB,CAAP;AACD;;SAEDyB,WAAA,kBAAS1Y,QAAT,EAAmBiX,QAAnB,EAA6B;AAC3B1M,IAAAA,QAAQ,CAAC,uCAAuC,kCAAxC,CAAR;AACA,WAAO,KAAK6M,SAAL,CAAepX,QAAf,EAAyBiX,QAAzB,CAAP;AACD;;SAED0B,aAAA,oBAAWtf,IAAX,EAAiB4d,QAAjB,EAA2B;AACzB1M,IAAAA,QAAQ,CAAC,yCAAyC,oCAA1C,CAAR;AACA,WAAO,KAAK8M,WAAL,CAAiBhe,IAAjB,EAAuB4d,QAAvB,CAAP;AACD;;SAED2B,cAAA,qBAAY3B,QAAZ,EAAsB;AACpB1M,IAAAA,QAAQ,CAAC,0CAA0C,qCAA3C,CAAR;AACA,WAAO,KAAKuH,YAAL,CAAkBmF,QAAlB,CAAP;AACD;;;;wBA9GW;AACV,UAAI,CAAC,KAAKnW,KAAV,EAAiB,OAAOjB,SAAP;AACjB,aAAO,KAAKiB,KAAL,CAAW,CAAX,CAAP;AACD;AAED;;;;;;;;;;;wBAQW;AACT,UAAI,CAAC,KAAKA,KAAV,EAAiB,OAAOjB,SAAP;AACjB,aAAO,KAAKiB,KAAL,CAAW,KAAKA,KAAL,CAAWhH,MAAX,GAAoB,CAA/B,CAAP;AACD;;;wBAgGe;AACdyQ,MAAAA,QAAQ,CAAC,uDAAD,CAAR;AACA,aAAO,KAAK6F,IAAL,CAAUJ,SAAjB;AACD;sBAEauE,KAAK;AACjBhK,MAAAA,QAAQ,CAAC,uDAAD,CAAR;AACA,WAAK6F,IAAL,CAAUJ,SAAV,GAAsBuE,GAAtB;AACD;;;wBAEW;AACVhK,MAAAA,QAAQ,CAAC,+CAAD,CAAR;AACA,aAAO,KAAK6F,IAAL,CAAUb,KAAjB;AACD;sBAESgF,KAAK;AACbhK,MAAAA,QAAQ,CAAC,+CAAD,CAAR;AACA,WAAK6F,IAAL,CAAUb,KAAV,GAAkBgF,GAAlB;AACD;AAED;;;;;;;;;;;;;;EAvqBsB5B;;ACvCxB;;;;;;;;;;;;;;;;;;;IAkBMqD;;;AACJ,kBAAYpD,QAAZ,EAAsB;AAAA;;AACpB,kCAAMA,QAAN;AACA,UAAK3C,IAAL,GAAY,QAAZ;AAFoB;AAGrB;;;;SAED0D,SAAA,kBAAoB;AAAA;;AAClB,QAAI,CAAC,KAAK7S,KAAV,EAAiB,KAAKA,KAAL,GAAa,EAAb;;AADC,sCAAVwW,QAAU;AAAVA,MAAAA,QAAU;AAAA;;AAElB,yDAAa3D,MAAb,kDAAuB2D,QAAvB;AACD;;SAEDE,UAAA,mBAAqB;AAAA;;AACnB,QAAI,CAAC,KAAK1W,KAAV,EAAiB,KAAKA,KAAL,GAAa,EAAb;;AADE,uCAAVwW,QAAU;AAAVA,MAAAA,QAAU;AAAA;;AAEnB,0DAAaE,OAAb,mDAAwBF,QAAxB;AACD;;;;wBAEe;AACd/M,MAAAA,QAAQ,CAAC,4DAAD,CAAR;AACA,aAAO,KAAK6F,IAAL,CAAUY,SAAjB;AACD;sBAEauD,KAAK;AACjBhK,MAAAA,QAAQ,CAAC,4DAAD,CAAR;AACA,WAAK6F,IAAL,CAAUY,SAAV,GAAsBuD,GAAtB;AACD;;;wBAEa;AACZhK,MAAAA,QAAQ,CAAC,uDAAD,CAAR;AACA,aAAO,KAAK6F,IAAL,CAAUW,MAAjB;AACD;sBAEWwD,KAAK;AACfhK,MAAAA,QAAQ,CAAC,uDAAD,CAAR;AACA,WAAK6F,IAAL,CAAUW,MAAV,GAAmBwD,GAAnB;AACD;AAED;;;;;;;;;;AAUA;;;;;;;;;;;;AAYA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA1DmBwC;;ACtBrB;;AACA;;;;;;;;;AASA,IAAM8B,IAAI,GAAG;AACX1Y,EAAAA,KADW,iBACLwG,MADK,EACGmS,UADH,EACetM,IADf,EACqB;AAC9B,QAAMuM,KAAK,GAAG,EAAd;AACA,QAAInE,OAAO,GAAG,EAAd;AACA,QAAIzU,KAAK,GAAG,KAAZ;AAEA,QAAI6Y,IAAI,GAAG,CAAX;AACA,QAAI1M,KAAK,GAAG,KAAZ;AACA,QAAIG,MAAM,GAAG,KAAb;;AAEA,SAAK,IAAI5S,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8M,MAAM,CAAC7M,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;AACtC,UAAMof,MAAM,GAAGtS,MAAM,CAAC9M,CAAD,CAArB;;AAEA,UAAIyS,KAAJ,EAAW;AACT,YAAIG,MAAJ,EAAY;AACVA,UAAAA,MAAM,GAAG,KAAT;AACD,SAFD,MAEO,IAAIwM,MAAM,KAAK,IAAf,EAAqB;AAC1BxM,UAAAA,MAAM,GAAG,IAAT;AACD,SAFM,MAEA,IAAIwM,MAAM,KAAK3M,KAAf,EAAsB;AAC3BA,UAAAA,KAAK,GAAG,KAAR;AACD;AACF,OARD,MAQO,IAAI2M,MAAM,KAAK,GAAX,IAAkBA,MAAM,KAAK,GAAjC,EAAsC;AAC3C3M,QAAAA,KAAK,GAAG2M,MAAR;AACD,OAFM,MAEA,IAAIA,MAAM,KAAK,GAAf,EAAoB;AACzBD,QAAAA,IAAI,IAAI,CAAR;AACD,OAFM,MAEA,IAAIC,MAAM,KAAK,GAAf,EAAoB;AACzB,YAAID,IAAI,GAAG,CAAX,EAAcA,IAAI,IAAI,CAAR;AACf,OAFM,MAEA,IAAIA,IAAI,KAAK,CAAb,EAAgB;AACrB,YAAIF,UAAU,CAAClS,OAAX,CAAmBqS,MAAnB,MAA+B,CAAC,CAApC,EAAuC9Y,KAAK,GAAG,IAAR;AACxC;;AAED,UAAIA,KAAJ,EAAW;AACT,YAAIyU,OAAO,KAAK,EAAhB,EAAoBmE,KAAK,CAACrc,IAAN,CAAWkY,OAAO,CAAC3X,IAAR,EAAX;AACpB2X,QAAAA,OAAO,GAAG,EAAV;AACAzU,QAAAA,KAAK,GAAG,KAAR;AACD,OAJD,MAIO;AACLyU,QAAAA,OAAO,IAAIqE,MAAX;AACD;AACF;;AAED,QAAIzM,IAAI,IAAIoI,OAAO,KAAK,EAAxB,EAA4BmE,KAAK,CAACrc,IAAN,CAAWkY,OAAO,CAAC3X,IAAR,EAAX;AAC5B,WAAO8b,KAAP;AACD,GA1CU;;AA4CX;;;;;;;;;;;AAWAG,EAAAA,KAvDW,iBAuDLvS,MAvDK,EAuDG;AACZ,QAAMkO,MAAM,GAAG,CAAC,GAAD,EAAM,IAAN,EAAY,IAAZ,CAAf;AACA,WAAOgE,IAAI,CAAC1Y,KAAL,CAAWwG,MAAX,EAAmBkO,MAAnB,CAAP;AACD,GA1DU;;AA4DX;;;;;;;;;;;;AAYAsE,EAAAA,KAxEW,iBAwELxS,MAxEK,EAwEG;AACZ,QAAMwS,KAAK,GAAG,GAAd;AACA,WAAON,IAAI,CAAC1Y,KAAL,CAAWwG,MAAX,EAAmB,CAACwS,KAAD,CAAnB,EAA4B,IAA5B,CAAP;AACD;AA3EU,CAAb;;ACHA;;;;;;;;;;;;IAWM/D;;;AACJ,gBAAYxC,QAAZ,EAAsB;AAAA;;AACpB,kCAAMA,QAAN;AACA,UAAK3C,IAAL,GAAY,MAAZ;AACA,QAAI,CAAC,MAAKnP,KAAV,EAAiB,MAAKA,KAAL,GAAa,EAAb;AAHG;AAIrB;AAED;;;;;;;;;;;;;;;;;;;;wBAgBgB;AACd,aAAO+X,IAAI,CAACM,KAAL,CAAW,KAAKnZ,QAAhB,CAAP;AACD;sBAEaoZ,QAAQ;AACpB,UAAMzY,KAAK,GAAG,KAAKX,QAAL,GAAgB,KAAKA,QAAL,CAAcW,KAAd,CAAoB,MAApB,CAAhB,GAA8C,IAA5D;AACA,UAAM0Y,GAAG,GAAG1Y,KAAK,GAAGA,KAAK,CAAC,CAAD,CAAR,SAAkB,KAAK2P,GAAL,CAAS,SAAT,EAAoB,YAApB,CAAnC;AACA,WAAKtQ,QAAL,GAAgBoZ,MAAM,CAACpc,IAAP,CAAYqc,GAAZ,CAAhB;AACD;;;wBAEe;AACd9O,MAAAA,QAAQ,CAAC,sDAAD,CAAR;AACA,aAAO,KAAK6F,IAAL,CAAUpQ,QAAjB;AACD;sBAEauU,KAAK;AACjBhK,MAAAA,QAAQ,CAAC,sDAAD,CAAR;AACA,WAAK6F,IAAL,CAAUpQ,QAAV,GAAqBuU,GAArB;AACD;AAED;;;;;;;;;;;AAWA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAtDiBwC;;AClBnB;;AACA;;;;;;;;IAQMuC;AACJ;;;;;;;;;;;AAWA,mBAAY9I,IAAZ,EAAkBqC,IAAlB,EAA6B;AAAA,QAAXA,IAAW;AAAXA,MAAAA,IAAW,GAAJ,EAAI;AAAA;;AAC3B;;;;;;;;AAQA,SAAK5C,IAAL,GAAY,SAAZ;AACA;;;;;;;AAMA,SAAKO,IAAL,GAAYA,IAAZ;;AAEA,QAAIqC,IAAI,CAAC5R,IAAL,IAAa4R,IAAI,CAAC5R,IAAL,CAAU8M,MAA3B,EAAmC;AACjC,UAAMf,GAAG,GAAG6F,IAAI,CAAC5R,IAAL,CAAU6R,UAAV,CAAqBD,IAArB,CAAZ;AACA;;;;;;;;AAOA,WAAKxN,IAAL,GAAY2H,GAAG,CAAC3H,IAAhB;AACA;;;;;;;;AAOA,WAAKC,MAAL,GAAc0H,GAAG,CAAC1H,MAAlB;AACD;;AAED,SAAK,IAAMiU,GAAX,IAAkB1G,IAAlB;AAAwB,WAAK0G,GAAL,IAAY1G,IAAI,CAAC0G,GAAD,CAAhB;AAAxB;AACD;AAED;;;;;;;;;;;;SAQA1e,WAAA,oBAAW;AACT,QAAI,KAAKoG,IAAT,EAAe;AACb,aAAO,KAAKA,IAAL,CAAUkM,KAAV,CAAgB,KAAKqD,IAArB,EAA2B;AAChChJ,QAAAA,MAAM,EAAE,KAAKA,MADmB;AAEhC9J,QAAAA,KAAK,EAAE,KAAKA,KAFoB;AAGhC0W,QAAAA,IAAI,EAAE,KAAKA;AAHqB,OAA3B,EAIJ5J,OAJH;AAKD,KAND,MAMO,IAAI,KAAKhD,MAAT,EAAiB;AACtB,aAAU,KAAKA,MAAf,UAA0B,KAAKgJ,IAA/B;AACD,KAFM,MAEA;AACL,aAAO,KAAKA,IAAZ;AACD;AACF;AAED;;;;;;;;;;AAUA;;;;;;;;;;;;AC3FF;;;;;;AAMA;;;;;;;;;;;;;;;IAcMgJ;AACJ;;;;;;AAMA,kBAAYC,SAAZ,EAAuBvJ,IAAvB,EAA6B2C,IAA7B,EAAmC;AACjC;;;;;;;;;;;AAWA,SAAK4G,SAAL,GAAiBA,SAAjB;AACA;;;;;;;;;;;;;;;;;;;AAkBA,SAAKC,QAAL,GAAgB,EAAhB;AACA;;;;;;;AAMA,SAAKxJ,IAAL,GAAYA,IAAZ;AACA;;;;;;;;;AAQA,SAAK2C,IAAL,GAAYA,IAAZ;AACA;;;;;;;AAMA,SAAKnU,GAAL,GAAWmB,SAAX;AACA;;;;;;;;;;;;;;;AAcA,SAAKgO,GAAL,GAAWhO,SAAX;AACD;AAED;;;;;;;;;;;;SAQAhF,WAAA,oBAAW;AACT,WAAO,KAAK6D,GAAZ;AACD;AAED;;;;;;;;;;;;;;;;;;SAgBA2L,OAAA,cAAKmG,IAAL,EAAWqC,IAAX,EAAsB;AAAA,QAAXA,IAAW;AAAXA,MAAAA,IAAW,GAAJ,EAAI;AAAA;;AACpB,QAAI,CAACA,IAAI,CAACrL,MAAV,EAAkB;AAChB,UAAI,KAAKmS,UAAL,IAAmB,KAAKA,UAAL,CAAgBC,aAAvC,EAAsD;AACpD/G,QAAAA,IAAI,CAACrL,MAAL,GAAc,KAAKmS,UAAL,CAAgBC,aAA9B;AACD;AACF;;AAED,QAAMC,OAAO,GAAG,IAAIP,OAAJ,CAAY9I,IAAZ,EAAkBqC,IAAlB,CAAhB;AACA,SAAK6G,QAAL,CAAchd,IAAd,CAAmBmd,OAAnB;AAEA,WAAOA,OAAP;AACD;AAED;;;;;;;;;;;;;SAWAC,WAAA,oBAAW;AACT,WAAO,KAAKJ,QAAL,CAAcK,MAAd,CAAqB,UAAAlgB,CAAC;AAAA,aAAIA,CAAC,CAACoW,IAAF,KAAW,SAAf;AAAA,KAAtB,CAAP;AACD;AAED;;;;;;;;;;;;wBAQc;AACZ,aAAO,KAAKvR,GAAZ;AACD;;;;;;AClKH,SAASsb,SAAT,CAAmB1Q,GAAnB,EAAwB;AACtB,SAAO,OAAOA,GAAP,KAAe,QAAf,IAA2B,OAAOA,GAAG,CAAC2Q,IAAX,KAAoB,UAAtD;AACD;AAED;;;;;AAKA;;;;;AAKA;;;;;;;;;;IAQMC;AACJ,sBAAYT,SAAZ,EAAuB/a,GAAvB,EAA4BmU,IAA5B,EAAkC;AAChC,SAAKsH,WAAL,GAAmB,KAAnB;AACA,SAAK9B,SAAL,GAAiB,KAAjB;AAEA,QAAInI,IAAJ;;AACA,QAAI,OAAOxR,GAAP,KAAe,QAAf,IAA2BA,GAAG,CAACuR,IAAJ,KAAa,MAA5C,EAAoD;AAClDC,MAAAA,IAAI,GAAGxR,GAAP;AACD,KAFD,MAEO,IAAIA,GAAG,YAAYwb,UAAf,IAA6Bxb,GAAG,YAAY8a,MAAhD,EAAwD;AAC7DtJ,MAAAA,IAAI,GAAGxR,GAAG,CAACwR,IAAX;;AACA,UAAIxR,GAAG,CAACmP,GAAR,EAAa;AACX,YAAI,OAAOgF,IAAI,CAAChF,GAAZ,KAAoB,WAAxB,EAAqCgF,IAAI,CAAChF,GAAL,GAAW,EAAX;AACrC,YAAI,CAACgF,IAAI,CAAChF,GAAL,CAASuM,MAAd,EAAsBvH,IAAI,CAAChF,GAAL,CAASuM,MAAT,GAAkB,KAAlB;AACtBvH,QAAAA,IAAI,CAAChF,GAAL,CAASf,IAAT,GAAgBpO,GAAG,CAACmP,GAApB;AACD;AACF,KAPM,MAOA;AACL,UAAIgJ,MAAM,GAAGF,KAAb;AACA,UAAI9D,IAAI,CAACwH,MAAT,EAAiBxD,MAAM,GAAGhE,IAAI,CAACwH,MAAL,CAAY1D,KAArB;AACjB,UAAI9D,IAAI,CAACgE,MAAT,EAAiBA,MAAM,GAAGhE,IAAI,CAACgE,MAAd;AACjB,UAAIA,MAAM,CAACF,KAAX,EAAkBE,MAAM,GAAGA,MAAM,CAACF,KAAhB;;AAElB,UAAI;AACFzG,QAAAA,IAAI,GAAG2G,MAAM,CAACnY,GAAD,EAAMmU,IAAN,CAAb;AACD,OAFD,CAEE,OAAO1F,KAAP,EAAc;AACd,aAAKA,KAAL,GAAaA,KAAb;AACD;AACF;;AAED,SAAKjD,MAAL,GAAc,IAAIsP,MAAJ,CAAWC,SAAX,EAAsBvJ,IAAtB,EAA4B2C,IAA5B,CAAd;AACD;AAED;;;;;;;;;AAmGA;;;;;;SAMAiH,WAAA,oBAAW;AACT,WAAO,KAAKQ,IAAL,GAAYR,QAAZ,EAAP;AACD;AAED;;;;;;;;;;SAQAjf,WAAA,oBAAW;AACT,WAAO,KAAK6D,GAAZ;AACD;AAED;;;;;;;;;;;;;;;;;;;;SAkBAub,OAAA,cAAKM,WAAL,EAAkBC,UAAlB,EAA8B;AAC5B,WAAO,KAAKC,KAAL,GAAaR,IAAb,CAAkBM,WAAlB,EAA+BC,UAA/B,CAAP;AACD;AAED;;;;;;;;;;;;;;;;;;;oBAiBA,gBAAMA,UAAN,EAAkB;AAChB,WAAO,KAAKC,KAAL,YAAmBD,UAAnB,CAAP;AACD;;SAEDE,cAAA,qBAAYvN,KAAZ,EAAmB3F,MAAnB,EAA2B;AACzB,QAAI;AACF,WAAK2F,KAAL,GAAaA,KAAb;;AACA,UAAIA,KAAK,CAAC9T,IAAN,KAAe,gBAAf,IAAmC,CAAC8T,KAAK,CAAC3F,MAA9C,EAAsD;AACpD2F,QAAAA,KAAK,CAAC3F,MAAN,GAAeA,MAAM,CAACoS,aAAtB;AACAzM,QAAAA,KAAK,CAACe,UAAN;AACD,OAHD,MAGO,IAAI1G,MAAM,CAACmT,cAAX,EAA2B;AAChC,YAAMC,UAAU,GAAGpT,MAAM,CAACoS,aAA1B;AACA,YAAMiB,SAAS,GAAGrT,MAAM,CAACmT,cAAzB;AACA,YAAMG,UAAU,GAAG,KAAK5Q,MAAL,CAAYuP,SAAZ,CAAsBsB,OAAzC;AACA,YAAMze,CAAC,GAAGue,SAAS,CAAC1a,KAAV,CAAgB,GAAhB,CAAV;AACA,YAAM5D,CAAC,GAAGue,UAAU,CAAC3a,KAAX,CAAiB,GAAjB,CAAV;;AAEA,YAAI7D,CAAC,CAAC,CAAD,CAAD,KAASC,CAAC,CAAC,CAAD,CAAV,IAAiBqE,QAAQ,CAACtE,CAAC,CAAC,CAAD,CAAF,CAAR,GAAiBsE,QAAQ,CAACrE,CAAC,CAAC,CAAD,CAAF,CAA9C,EAAsD;AACpDgO,UAAAA,QAAQ,CACN,MAAG,kCAAkC,KAArC,IAA6CuQ,UAA7C,cAAgEF,UAAhE,oBACUC,SADV,2DADM,CAAR;AAKD;AACF;AACF,KApBD,CAoBE,OAAOG,GAAP,EAAY;AACZ,UAAI5Q,OAAO,IAAIA,OAAO,CAAC+C,KAAvB,EAA8B/C,OAAO,CAAC+C,KAAR,CAAc6N,GAAd;AAC/B;AACF;;SAEDC,YAAA,mBAAUC,OAAV,EAAmBC,MAAnB,EAA2B;AAAA;;AACzB,QAAI,KAAK3T,MAAL,IAAe,KAAKiS,SAAL,CAAe5T,OAAf,CAAuB/L,MAA1C,EAAkD;AAChD,WAAKue,SAAL,GAAiB,IAAjB;AACA,aAAO6C,OAAO,EAAd;AACD;;AAED,QAAI;AACF,UAAM1T,MAAM,GAAG,KAAKiS,SAAL,CAAe5T,OAAf,CAAuB,KAAK2B,MAA5B,CAAf;AACA,UAAM4T,OAAO,GAAG,KAAKC,GAAL,CAAS7T,MAAT,CAAhB;AACA,WAAKA,MAAL,IAAe,CAAf;;AAEA,UAAIwS,SAAS,CAACoB,OAAD,CAAb,EAAwB;AACtBA,QAAAA,OAAO,CACJnB,IADH,CACQ,YAAM;AACV,UAAA,KAAI,CAACgB,SAAL,CAAeC,OAAf,EAAwBC,MAAxB;AACD,SAHH,WAIS,UAAAhO,KAAK,EAAI;AACd,UAAA,KAAI,CAACuN,WAAL,CAAiBvN,KAAjB,EAAwB3F,MAAxB;;AACA,UAAA,KAAI,CAAC6Q,SAAL,GAAiB,IAAjB;AACA8C,UAAAA,MAAM,CAAChO,KAAD,CAAN;AACD,SARH;AASD,OAVD,MAUO;AACL,aAAK8N,SAAL,CAAeC,OAAf,EAAwBC,MAAxB;AACD;AACF,KAlBD,CAkBE,OAAOhO,KAAP,EAAc;AACd,WAAKkL,SAAL,GAAiB,IAAjB;AACA8C,MAAAA,MAAM,CAAChO,KAAD,CAAN;AACD;AACF;;SAEDsN,QAAA,iBAAQ;AAAA;;AACN,QAAI,KAAKpC,SAAT,EAAoB;AAClB,aAAO,IAAIiD,OAAJ,CAAY,UAACJ,OAAD,EAAUC,MAAV,EAAqB;AACtC,YAAI,MAAI,CAAChO,KAAT,EAAgB;AACdgO,UAAAA,MAAM,CAAC,MAAI,CAAChO,KAAN,CAAN;AACD,SAFD,MAEO;AACL+N,UAAAA,OAAO,CAAC,MAAI,CAACnL,SAAL,EAAD,CAAP;AACD;AACF,OANM,CAAP;AAOD;;AACD,QAAI,KAAKwL,UAAT,EAAqB;AACnB,aAAO,KAAKA,UAAZ;AACD;;AAED,SAAKA,UAAL,GAAkB,IAAID,OAAJ,CAAY,UAACJ,OAAD,EAAUC,MAAV,EAAqB;AACjD,UAAI,MAAI,CAAChO,KAAT,EAAgB,OAAOgO,MAAM,CAAC,MAAI,CAAChO,KAAN,CAAb;AAChB,MAAA,MAAI,CAAC3F,MAAL,GAAc,CAAd;;AACA,MAAA,MAAI,CAACyT,SAAL,CAAeC,OAAf,EAAwBC,MAAxB;AACD,KAJiB,EAIflB,IAJe,CAIV,YAAM;AACZ,MAAA,MAAI,CAAC5B,SAAL,GAAiB,IAAjB;AACA,aAAO,MAAI,CAACtI,SAAL,EAAP;AACD,KAPiB,CAAlB;AASA,WAAO,KAAKwL,UAAZ;AACD;;SAEDjB,OAAA,gBAAO;AAAA;;AACL,QAAI,KAAKjC,SAAT,EAAoB,OAAO,KAAKnO,MAAZ;AACpB,SAAKmO,SAAL,GAAiB,IAAjB;;AAEA,QAAI,KAAKkD,UAAT,EAAqB;AACnB,YAAM,IAAIxe,KAAJ,CAAU,sDAAV,CAAN;AACD;;AAED,QAAI,KAAKoQ,KAAT,EAAgB,MAAM,KAAKA,KAAX;AAEhB,SAAKjD,MAAL,CAAYuP,SAAZ,CAAsB5T,OAAtB,CAA8BlJ,OAA9B,CAAsC,UAAA6K,MAAM,EAAI;AAC9C,UAAM4T,OAAO,GAAG,MAAI,CAACC,GAAL,CAAS7T,MAAT,CAAhB;;AACA,UAAIwS,SAAS,CAACoB,OAAD,CAAb,EAAwB;AACtB,cAAM,IAAIre,KAAJ,CAAU,sDAAV,CAAN;AACD;AACF,KALD;AAOA,WAAO,KAAKmN,MAAZ;AACD;;SAEDmR,MAAA,aAAI7T,MAAJ,EAAY;AACV,SAAK0C,MAAL,CAAYyP,UAAZ,GAAyBnS,MAAzB;;AAEA,QAAI;AACF,aAAOA,MAAM,CAAC,KAAK0C,MAAL,CAAYgG,IAAb,EAAmB,KAAKhG,MAAxB,CAAb;AACD,KAFD,CAEE,OAAOiD,KAAP,EAAc;AACd,WAAKuN,WAAL,CAAiBvN,KAAjB,EAAwB3F,MAAxB;AACA,YAAM2F,KAAN;AACD;AACF;;SAED4C,YAAA,uBAAY;AACV,QAAI,KAAKoK,WAAT,EAAsB,OAAO,KAAKjQ,MAAZ;AACtB,SAAKiQ,WAAL,GAAmB,IAAnB;AAEA,SAAKG,IAAL;AAEA,QAAMzH,IAAI,GAAG,KAAK3I,MAAL,CAAY2I,IAAzB;AACA,QAAI5Y,GAAG,GAAG8V,SAAV;AACA,QAAI8C,IAAI,CAACwH,MAAT,EAAiBpgB,GAAG,GAAG4Y,IAAI,CAACwH,MAAL,CAAYtK,SAAlB;AACjB,QAAI8C,IAAI,CAACI,WAAT,EAAsBhZ,GAAG,GAAG4Y,IAAI,CAACI,WAAX;AACtB,QAAIhZ,GAAG,CAAC8V,SAAR,EAAmB9V,GAAG,GAAGA,GAAG,CAAC8V,SAAV;AAEnB,QAAI7F,MAAM,GAAG,EAAb;AACAjQ,IAAAA,GAAG,CAAC,KAAKiW,IAAN,EAAY,UAAArW,CAAC,EAAI;AAClBqQ,MAAAA,MAAM,IAAIrQ,CAAV;AACD,KAFE,CAAH;AAGA,SAAKqQ,MAAL,CAAYxL,GAAZ,GAAkBwL,MAAlB;AAEA,WAAO,KAAKA,MAAZ;AACD;;;;wBAnSe;AACd,aAAO,KAAKA,MAAL,CAAYuP,SAAnB;AACD;AAED;;;;;;;wBAIW;AACT,aAAO,KAAKvP,MAAL,CAAY2I,IAAnB;AACD;AAED;;;;;;;;;;;;;;;wBAYU;AACR,aAAO,KAAK9C,SAAL,GAAiBrR,GAAxB;AACD;AAED;;;;;;;;;;;;;;;wBAYc;AACZ,aAAO,KAAKqR,SAAL,GAAiB9P,OAAxB;AACD;AAED;;;;;;;;;;;;;;;wBAYU;AACR,aAAO,KAAK8P,SAAL,GAAiBlC,GAAxB;AACD;AAED;;;;;;;;;;;;;;;;wBAaW;AACT,aAAO,KAAKyM,IAAL,GAAYpK,IAAnB;AACD;AAED;;;;;;;;;;;;;;;;wBAae;AACb,aAAO,KAAKoK,IAAL,GAAYZ,QAAnB;AACD;;;;;;AC5JH;AACA,AAEA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;;;;;;;;;;IASM8B;AACJ;;;;AAIA,qBAAY3V,OAAZ,EAA0B;AAAA,QAAdA,OAAc;AAAdA,MAAAA,OAAc,GAAJ,EAAI;AAAA;;AACxB;;;;;;;;AAQA,SAAKkV,OAAL,GAAe,OAAf;AACA;;;;;;;;AAOA,SAAKlV,OAAL,GAAe,KAAK0R,SAAL,CAAe1R,OAAf,CAAf;AACD;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SA6BAmB,MAAA,aAAIQ,MAAJ,EAAY;AACV,SAAK3B,OAAL,GAAe,KAAKA,OAAL,CAAa4V,MAAb,CAAoB,KAAKlE,SAAL,CAAe,CAAC/P,MAAD,CAAf,CAApB,CAAf;AACA,WAAO,IAAP;AACD;AAED;;;;;;;;;;;;;;;;;;;;;;;;SAsBApN,UAAA,iBAAQsE,GAAR,EAAamU,IAAb,EAAwB;AAAA,QAAXA,IAAW;AAAXA,MAAAA,IAAW,GAAJ,EAAI;AAAA;;AACtB,WAAO,IAAIqH,UAAJ,CAAe,IAAf,EAAqBxb,GAArB,EAA0BmU,IAA1B,CAAP;AACD;;SAED0E,YAAA,mBAAU1R,OAAV,EAAmB;AACjB,QAAI6V,UAAU,GAAG,EAAjB;AACA7V,IAAAA,OAAO,CAAClJ,OAAR,CAAgB,UAAA9C,CAAC,EAAI;AACnB,UAAIA,CAAC,CAAC8hB,OAAN,EAAe9hB,CAAC,GAAGA,CAAC,CAAC8hB,OAAN;;AAEf,UAAI,OAAO9hB,CAAP,KAAa,QAAb,IAAyB6P,KAAK,CAACC,OAAN,CAAc9P,CAAC,CAACgM,OAAhB,CAA7B,EAAuD;AACrD6V,QAAAA,UAAU,GAAGA,UAAU,CAACD,MAAX,CAAkB5hB,CAAC,CAACgM,OAApB,CAAb;AACD,OAFD,MAEO,IAAI,OAAOhM,CAAP,KAAa,UAAjB,EAA6B;AAClC6hB,QAAAA,UAAU,CAAChf,IAAX,CAAgB7C,CAAhB;AACD,OAFM,MAEA;AACL,cAAM,IAAIkD,KAAJ,CAAalD,CAAb,8BAAN;AACD;AACF,KAVD;AAWA,WAAO6hB,UAAP;AACD;;;;;AC1LH;;;;;;;;;;;IAUM/G;;;AACJ,gBAAY/B,QAAZ,EAAsB;AAAA;;AACpB,kCAAMA,QAAN;AACA,UAAK3C,IAAL,GAAY,MAAZ;AACA,QAAI,CAAC,MAAKnP,KAAV,EAAiB,MAAKA,KAAL,GAAa,EAAb;AAHG;AAIrB;;;;SAEDM,cAAA,qBAAYM,KAAZ,EAAmB;AACjBA,IAAAA,KAAK,GAAG,KAAKhE,KAAL,CAAWgE,KAAX,CAAR;;AAEA,QAAIA,KAAK,KAAK,CAAV,IAAe,KAAKZ,KAAL,CAAWhH,MAAX,GAAoB,CAAvC,EAA0C;AACxC,WAAKgH,KAAL,CAAW,CAAX,EAAcsP,IAAd,CAAmBa,MAAnB,GAA4B,KAAKnQ,KAAL,CAAWY,KAAX,EAAkB0O,IAAlB,CAAuBa,MAAnD;AACD;;AAED,gCAAa7P,WAAb,YAAyBM,KAAzB;AACD;;SAED6V,YAAA,mBAAU7V,KAAV,EAAiB0W,MAAjB,EAAyBnI,IAAzB,EAA+B;AAC7B,QAAMnP,KAAK,wBAASyW,SAAT,YAAmB7V,KAAnB,CAAX;;AAEA,QAAI0W,MAAJ,EAAY;AACV,UAAInI,IAAI,KAAK,SAAb,EAAwB;AACtB,YAAI,KAAKnP,KAAL,CAAWhH,MAAX,GAAoB,CAAxB,EAA2B;AACzBse,UAAAA,MAAM,CAAChI,IAAP,CAAYa,MAAZ,GAAqB,KAAKnQ,KAAL,CAAW,CAAX,EAAcsP,IAAd,CAAmBa,MAAxC;AACD,SAFD,MAEO;AACL,iBAAOmH,MAAM,CAAChI,IAAP,CAAYa,MAAnB;AACD;AACF,OAND,MAMO,IAAI,KAAKG,KAAL,KAAegH,MAAnB,EAA2B;AAChCtX,QAAAA,KAAK,CAACnE,OAAN,CAAc,UAAAsE,IAAI,EAAI;AACpBA,UAAAA,IAAI,CAACmP,IAAL,CAAUa,MAAV,GAAmBmH,MAAM,CAAChI,IAAP,CAAYa,MAA/B;AACD,SAFD;AAGD;AACF;;AAED,WAAOnQ,KAAP;AACD;AAED;;;;;;;;;;;;;;;SAaA8a,WAAA,kBAAS/I,IAAT,EAAoB;AAAA,QAAXA,IAAW;AAAXA,MAAAA,IAAW,GAAJ,EAAI;AAAA;;AAClB,QAAMgJ,IAAI,GAAG,IAAI3B,UAAJ,CAAe,IAAIsB,SAAJ,EAAf,EAAgC,IAAhC,EAAsC3I,IAAtC,CAAb;AACA,WAAOgJ,IAAI,CAAC9L,SAAL,EAAP;AACD;;SAEDiD,SAAA,gBAAOtR,KAAP,EAAc;AACZ6I,IAAAA,QAAQ,CAAC,iDAAD,CAAR;AACA,SAAKnJ,WAAL,CAAiBM,KAAjB;AACD;;SAEDoa,UAAA,mBAAU;AACRvR,IAAAA,QAAQ,CAAC,uDAAD,CAAR;AACA,WAAO,KAAKwD,MAAL,CAAY/B,KAAZ,CAAkB6B,GAAzB;AACD;AAED;;;;;;;;;;;;;;;;;;EAjEiBkJ;;ACZnB,IAAIgF,QAAQ,GAAG,CAAf;AAEA;;;;;;;AAOA;;;;;;;;IAOMpO;AACJ;;;;AAIA,iBAAYjP,GAAZ,EAAiBmU,IAAjB,EAA4B;AAAA,QAAXA,IAAW;AAAXA,MAAAA,IAAW,GAAJ,EAAI;AAAA;;AAC1B;;;;;;;AAOA,SAAKnU,GAAL,GAAWA,GAAG,CAAC7D,QAAJ,EAAX;;AAEA,QAAI,KAAK6D,GAAL,CAAS,CAAT,MAAgB,QAAhB,IAA4B,KAAKA,GAAL,CAAS,CAAT,MAAgB,QAAhD,EAA0D;AACxD,WAAKA,GAAL,GAAW,KAAKA,GAAL,CAAS0O,KAAT,CAAe,CAAf,CAAX;AACD;;AAED,QAAIyF,IAAI,CAAC2D,IAAT,EAAe;AACb,UAAI,YAAY/b,IAAZ,CAAiBoY,IAAI,CAAC2D,IAAtB,CAAJ,EAAiC;AAC/B;;;;;;;;AAQA,aAAKxI,IAAL,GAAY6E,IAAI,CAAC2D,IAAjB;AACD,OAVD,MAUO;AACL,aAAKxI,IAAL,GAAYgO,IAAI,CAACd,OAAL,CAAarI,IAAI,CAAC2D,IAAlB,CAAZ;AACD;AACF;AAED;;;;;;;;;;;;;;;;;;AAiBA,QAAI,CAAC,KAAKxI,IAAV,EAAgB;AACd+N,MAAAA,QAAQ,IAAI,CAAZ;AACA;;;;;;;;;;;AAUA,WAAK7c,EAAL,mBAAwB6c,QAAxB;AACD;;AACD,QAAI,KAAKlO,GAAT,EAAc,KAAKA,GAAL,CAASG,IAAT,GAAgB,KAAKwI,IAArB;AACf;;;;SAEDrJ,QAAA,eAAM3C,OAAN,EAAenF,IAAf,EAAqBC,MAArB,EAA6BuN,IAA7B,EAAwC;AAAA,QAAXA,IAAW;AAAXA,MAAAA,IAAW,GAAJ,EAAI;AAAA;;AACtC,QAAI3I,MAAJ;AACA,QAAM+R,MAAM,GAAG,KAAKA,MAAL,CAAY5W,IAAZ,EAAkBC,MAAlB,CAAf;;AACA,QAAI2W,MAAJ,EAAY;AACV/R,MAAAA,MAAM,GAAG,IAAI4D,cAAJ,CACPtD,OADO,EAEPyR,MAAM,CAAC5W,IAFA,EAGP4W,MAAM,CAAC3W,MAHA,EAIP2W,MAAM,CAAClO,MAJA,EAKPkO,MAAM,CAACjO,IALA,EAMP6E,IAAI,CAACrL,MANE,CAAT;AAQD,KATD,MASO;AACL0C,MAAAA,MAAM,GAAG,IAAI4D,cAAJ,CAAmBtD,OAAnB,EAA4BnF,IAA5B,EAAkCC,MAAlC,EAA0C,KAAK5G,GAA/C,EAAoD,KAAKsP,IAAzD,EAA+D6E,IAAI,CAACrL,MAApE,CAAT;AACD;;AAED0C,IAAAA,MAAM,CAAC8B,KAAP,GAAe;AAAE3G,MAAAA,IAAI,EAAJA,IAAF;AAAQC,MAAAA,MAAM,EAANA,MAAR;AAAgByI,MAAAA,MAAM,EAAE,KAAKrP;AAA7B,KAAf;AACA,QAAI,KAAKsP,IAAT,EAAe9D,MAAM,CAAC8B,KAAP,CAAagC,IAAb,GAAoB,KAAKA,IAAzB;AAEf,WAAO9D,MAAP;AACD;AAED;;;;;;;;;;;;;;;SAaA+R,SAAA,gBAAO5W,IAAP,EAAaC,MAAb,EAAqB;AACnB,QAAI,CAAC,KAAKuI,GAAV,EAAe,OAAO,KAAP;AACf,QAAMqO,QAAQ,GAAG,KAAKrO,GAAL,CAASqO,QAAT,EAAjB;AAEA,QAAM1F,IAAI,GAAG0F,QAAQ,CAACC,mBAAT,CAA6B;AAAE9W,MAAAA,IAAI,EAAJA,IAAF;AAAQC,MAAAA,MAAM,EAANA;AAAR,KAA7B,CAAb;AACA,QAAI,CAACkR,IAAI,CAACzI,MAAV,EAAkB,OAAO,KAAP;AAElB,QAAM7D,MAAM,GAAG;AACb8D,MAAAA,IAAI,EAAE,KAAKoO,UAAL,CAAgB5F,IAAI,CAACzI,MAArB,CADO;AAEb1I,MAAAA,IAAI,EAAEmR,IAAI,CAACnR,IAFE;AAGbC,MAAAA,MAAM,EAAEkR,IAAI,CAAClR;AAHA,KAAf;AAMA,QAAMyI,MAAM,GAAGmO,QAAQ,CAACG,gBAAT,CAA0B7F,IAAI,CAACzI,MAA/B,CAAf;AACA,QAAIA,MAAJ,EAAY7D,MAAM,CAAC6D,MAAP,GAAgBA,MAAhB;AAEZ,WAAO7D,MAAP;AACD;;SAEDkS,aAAA,oBAAWpO,IAAX,EAAiB;AACf,QAAI,YAAYvT,IAAZ,CAAiBuT,IAAjB,CAAJ,EAA4B;AAC1B,aAAOA,IAAP;AACD,KAFD,MAEO;AACL,aAAOgO,IAAI,CAACd,OAAL,CAAa,KAAKrN,GAAL,CAASqO,QAAT,GAAoBI,UAApB,IAAkC,GAA/C,EAAoDtO,IAApD,CAAP;AACD;AACF;AAED;;;;;;;;;;;;;;;;wBAYW;AACT,aAAO,KAAKA,IAAL,IAAa,KAAK9O,EAAzB;AACD;;;;;;IClKkBqd;;;;;;;;;SACnBxQ,WAAA,sBAAW;AACT,SAAKE,MAAL,GAAcF,QAAQ,CAAC,KAAKC,KAAN,EAAa;AAAEI,MAAAA,YAAY,EAAE;AAAhB,KAAb,CAAtB;AACD;;SAEDoB,UAAA,iBAAQI,KAAR,EAAe;AACb,QAAM3M,IAAI,GAAG,IAAIwT,OAAJ,EAAb;AACA,SAAKU,IAAL,CAAUlU,IAAV,EAAgB2M,KAAK,CAAC,CAAD,CAArB,EAA0BA,KAAK,CAAC,CAAD,CAA/B;AACA3M,IAAAA,IAAI,CAAC8M,MAAL,CAAYS,GAAZ,GAAkB;AAAEnJ,MAAAA,IAAI,EAAEuI,KAAK,CAAC,CAAD,CAAb;AAAkBtI,MAAAA,MAAM,EAAEsI,KAAK,CAAC,CAAD;AAA/B,KAAlB;AAEA,QAAI4C,IAAI,GAAG5C,KAAK,CAAC,CAAD,CAAL,CAASR,KAAT,CAAe,CAAf,CAAX;AACA,QAAIoD,IAAI,CAACpD,KAAL,CAAW,CAAC,CAAZ,MAAmB,IAAvB,EAA6BoD,IAAI,GAAGA,IAAI,CAACpD,KAAL,CAAW,CAAX,EAAc,CAAC,CAAf,CAAP;;AAE7B,QAAI,QAAQ3S,IAAR,CAAa+V,IAAb,CAAJ,EAAwB;AACtBvP,MAAAA,IAAI,CAACuP,IAAL,GAAY,EAAZ;AACAvP,MAAAA,IAAI,CAACmP,IAAL,CAAUC,IAAV,GAAiBG,IAAjB;AACAvP,MAAAA,IAAI,CAACmP,IAAL,CAAUG,KAAV,GAAkB,EAAlB;AACD,KAJD,MAIO;AACL,UAAM5P,KAAK,GAAG6P,IAAI,CAAC7P,KAAL,CAAW,yBAAX,CAAd;AACAM,MAAAA,IAAI,CAACuP,IAAL,GAAY7P,KAAK,CAAC,CAAD,CAAjB;AACAM,MAAAA,IAAI,CAACmP,IAAL,CAAUC,IAAV,GAAiB1P,KAAK,CAAC,CAAD,CAAtB;AACAM,MAAAA,IAAI,CAACmP,IAAL,CAAUG,KAAV,GAAkB5P,KAAK,CAAC,CAAD,CAAvB;AACD;AACF;;SAED4U,kBAAA,2BAAkB;;SAElBC,cAAA,qBAAYlH,KAAZ,EAAmB;AACjB,QAAMkO,MAAM,GAAG,KAAKvQ,MAAL,CAAYmB,KAAZ,CAAkBkB,KAAlB,EAAyB,KAAKtB,GAAL,GAAW,CAApC,CAAf;AACA,SAAK6H,MAAL,IAAe2H,MAAM,CAAC3O,GAAP,CAAW,UAAAhU,CAAC;AAAA,aAAIA,CAAC,CAAC,CAAD,CAAL;AAAA,KAAZ,EAAsBmD,IAAtB,CAA2B,EAA3B,CAAf;AACD;;SAEDmZ,kBAAA,2BAAkB;AAChB,SAAKvB,OAAL,CAAaxE,IAAb,CAAkBb,KAAlB,IAA2B,GAA3B;AACD;;SAEDkH,cAAA,uBAAc;;SAEdR,gBAAA,uBAAchV,IAAd,EAAoB;AAClBA,IAAAA,IAAI,CAAC5H,IAAL,GAAY,EAAZ;AACD;;SAEDuc,0BAAA,iCAAwB3J,MAAxB,EAAgC;AAC9B,QAAM+C,KAAK,GAAG,KAAKA,KAAL,CAAW/C,MAAX,CAAd;AACA,QAAI+C,KAAK,KAAK,KAAd,EAAqB;AAErB,QAAI7O,KAAJ;;AACA,SAAKA,KAAK,GAAG6O,KAAK,GAAG,CAArB,EAAwB7O,KAAK,IAAI,CAAjC,EAAoCA,KAAK,EAAzC,EAA6C;AAC3C,UAAI8L,MAAM,CAAC9L,KAAD,CAAN,CAAc,CAAd,MAAqB,MAAzB,EAAiC;AAClC;;AACD,SAAKA,KAAK,IAAI,CAAd,EAAiBA,KAAK,IAAI,CAA1B,EAA6BA,KAAK,EAAlC,EAAsC;AACpC,UAAI8L,MAAM,CAAC9L,KAAD,CAAN,CAAc,CAAd,MAAqB,OAAzB,EAAkC;AAChCA,QAAAA,KAAK,IAAI,CAAT;AACA;AACD;AACF;;AACD,QAAM8U,KAAK,GAAGhJ,MAAM,CAACzI,MAAP,CAAcrD,KAAd,EAAqB8L,MAAM,CAACnS,MAAP,GAAgBqG,KAArC,CAAd;AACA,SAAKsQ,IAAL,CAAUwE,KAAV;AACD;;SAEDc,uBAAA,gCAAuB;;SAEvBb,UAAA,mBAAU;AACR,QAAI,KAAKN,OAAL,CAAa9T,KAAb,IAAsB,KAAK8T,OAAL,CAAa9T,KAAb,CAAmBhH,MAA7C,EAAqD;AACnD,WAAK8a,OAAL,CAAaxE,IAAb,CAAkBJ,SAAlB,GAA8B,KAAKA,SAAnC;AACD;;AACD,SAAK4E,OAAL,CAAaxE,IAAb,CAAkBb,KAAlB,GAA0B,CAAC,KAAKqF,OAAL,CAAaxE,IAAb,CAAkBb,KAAlB,IAA2B,EAA5B,IAAkC,KAAKsF,MAAjE;;AAEA,WAAO,KAAKD,OAAL,CAAa7S,MAApB,EAA4B;AAC1B,WAAK6S,OAAL,GAAe,KAAKA,OAAL,CAAa7S,MAA5B;AACA,WAAK6S,OAAL,CAAaxE,IAAb,CAAkBb,KAAlB,GAA0B,EAA1B;AACD;AACF;;;EAxEqCmF;;ACLxC;AACA,AAIe,SAAS+H,SAAT,CAAmB/d,GAAnB,EAAwBmU,IAAxB,EAA8B;AAC3C,MAAM7G,KAAK,GAAG,IAAI2B,KAAJ,CAAUjP,GAAV,EAAemU,IAAf,CAAd;AAEA,MAAMgE,MAAM,GAAG,IAAI0F,UAAJ,CAAevQ,KAAf,CAAf;AACA6K,EAAAA,MAAM,CAAC9K,QAAP;AACA8K,EAAAA,MAAM,CAAC9B,IAAP;AAEA,SAAO8B,MAAM,CAAC3G,IAAd;AACD;;ACbD;AAUA,IAAIwM,SAAS,GAAG,EAAhB;AAEA,AAIA;;;;AAGA,oBAAe,UAACtU,UAAD,EAA4B;AAAA,MACnCuU,WADmC;AAIvC,yBAAY/e,KAAZ,EAA4B;AAC1B,WAAKA,KAAL,GAAaA,KAAb;AACD;;AANsC;;AAAA,WAQvCgf,mBARuC,GAQvC,6BAAoB5S,gBAApB,EAA8C;AAC5C,UAAM5C,OAAO,GAAG2C,OAAO,CAAC,KAAKnM,KAAN,EAAaoM,gBAAb,CAAP,CAAsChN,IAAtC,CAA2C,EAA3C,CAAhB;AAEA,UAAMhD,IAAI,GAAG6iB,mBAAmB,CAACzV,OAAD,CAAhC;;AACA,UAAI,CAACsV,SAAS,CAAC1iB,IAAD,CAAd,EAAsB;AACpB,YAAMkW,IAAI,GAAGyG,SAAK,CAACvP,OAAD,CAAlB;AACA,YAAM0V,SAAS,GAAG,EAAlB;AACA5M,QAAAA,IAAI,CAAC8G,IAAL,CAAU,UAAA/V,IAAI,EAAI;AAChB,cAAIA,IAAI,CAACgP,IAAL,KAAc,MAAlB,EAA0B;AACxB6M,YAAAA,SAAS,CAACpgB,IAAV,CAAe,CAACuE,IAAI,CAAC0P,IAAN,EAAY1P,IAAI,CAAC+H,KAAjB,CAAf;AACD,WAFD,MAEO,IAAI5O,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAAzB,IAAyC2G,IAAI,CAACgP,IAAL,KAAc,SAA3D,EAAsE;AAC3E;AACA7F,YAAAA,OAAO,CAACC,IAAR,mBAA6BpJ,IAAI,CAACgP,IAAlC;AACD;AACF,SAPD,EAHoB;AAYpB;AACA;AACA;;AACA,YAAM8M,WAAW,GAAGC,kBAAkB,CAACF,SAAD,EAAY,CAChD,cADgD,EAEhD,aAFgD,EAGhD,aAHgD,EAIhD,aAJgD,CAAZ,CAAtC;AAMA,YAAMG,MAAM,GAAG7U,UAAU,CAAC8U,MAAX,CAAkB;AAC/BR,UAAAA,SAAS,EAAEK;AADoB,SAAlB,CAAf;AAGAL,QAAAA,SAAS,CAAC1iB,IAAD,CAAT,GAAkBijB,MAAM,CAACP,SAAzB;AACD;;AACD,aAAOA,SAAS,CAAC1iB,IAAD,CAAhB;AACD,KAvCsC;;AAAA;AAAA;;AA0CzC,SAAO2iB,WAAP;AACD,CA3CD;;ACnBA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,IAAMQ,QAAQ,GAAG,SAAXA,QAAW,CAAA5I,GAAG,EAAI;AACtB,SACE,OAAOA,GAAP,KAAe,UAAf,IAA8B,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,KAAK,IAAnC,IAA2C,CAAC7K,KAAK,CAACC,OAAN,CAAc4K,GAAd,CAD5E;AAGD,CAJD;;AAMA,IAAM6I,UAAU,GAAG,SAAbA,UAAa,CAAA5T,GAAG,EAAI;AACxB,SAAOA,GAAG,KAAK,WAAR,IAAuBA,GAAG,KAAK,aAA/B,IAAgDA,GAAG,KAAK,WAA/D;AACD,CAFD;;AAIA,SAAS6T,KAAT,CAAeljB,MAAf,EAAuBoa,GAAvB,EAA4B/K,GAA5B,EAAiC;AAC/B,MAAMF,GAAG,GAAGnP,MAAM,CAACqP,GAAD,CAAlB;;AACA,MAAI2T,QAAQ,CAAC5I,GAAD,CAAR,IAAiB4I,QAAQ,CAAC7T,GAAD,CAA7B,EAAoC;AAClCgU,IAAAA,SAAS,CAAChU,GAAD,EAAMiL,GAAN,CAAT;AACD,GAFD,MAEO;AACLpa,IAAAA,MAAM,CAACqP,GAAD,CAAN,GAAc+K,GAAd;AACD;AACF;;AAED,AAAe,SAAS+I,SAAT,CAAmBnjB,MAAnB,EAAoC;AAAA,oCAANojB,IAAM;AAANA,IAAAA,IAAM;AAAA;;AACjD,2BAAkBA,IAAlB,2BAAwB;AAAnB,QAAMjU,GAAG,YAAT;;AACH,QAAI6T,QAAQ,CAAC7T,GAAD,CAAZ,EAAmB;AACjB,WAAK,IAAME,GAAX,IAAkBF,GAAlB,EAAuB;AACrB,YAAI8T,UAAU,CAAC5T,GAAD,CAAd,EAAqB;AACnB6T,UAAAA,KAAK,CAACljB,MAAD,EAASmP,GAAG,CAACE,GAAD,CAAZ,EAAmBA,GAAnB,CAAL;AACD;AACF;AACF;AACF;;AAED,SAAOrP,MAAP;AACD;;AC3DD;AACA,AAMA,sBAAe,UAAC4d,KAAD,EAAeyF,aAAf,EAAmCC,YAAnC,EAAwE;AAAA,MAArCA,YAAqC;AAArCA,IAAAA,YAAqC,GAAjBvhB,YAAiB;AAAA;;AACrF,SAAQ6b,KAAK,CAAC2F,KAAN,KAAgBD,YAAY,CAACC,KAA7B,IAAsC3F,KAAK,CAAC2F,KAA7C,IAAuDF,aAAvD,IAAwEC,YAAY,CAACC,KAA5F;AACD,CAFD;;ACPA;AAGA,AAAe,SAASC,KAAT,CAAexjB,MAAf,EAAmF;AAChG,SACE,OAAOA,MAAP,KAAkB,QAAlB,KACCC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAAzB,GACGH,MAAM,CAACyjB,MAAP,CAAc,CAAd,MAAqBzjB,MAAM,CAACyjB,MAAP,CAAc,CAAd,EAAiB/U,WAAjB,EADxB,GAEG,IAHJ,CADF;AAMD;;ACVD;AAEA,AAGe,SAASgV,mBAAT,CACb1jB,MADa,EAEL;AACR,SAAOwjB,KAAK,CAACxjB,MAAD,CAAL,eAA0BA,MAA1B,eAA+CD,gBAAgB,CAACC,MAAD,CAA/D,MAAP;AACD;;ICKY2jB,YAAY,GAA0BpW,KAAK,CAACC,aAAN,EAA5C;AAEP,IAAaoW,aAAa,GAAGD,YAAY,CAACjW,QAAnC;;AAEP,SAASmW,UAAT,CAAoBN,KAApB,EAA0CO,UAA1C,EAAqE;AACnE,MAAI,CAACP,KAAL,EAAY;AACV,WAAO1f,0BAAgB,CAAC,EAAD,CAAvB;AACD;;AAED,MAAIxD,UAAU,CAACkjB,KAAD,CAAd,EAAuB;AACrB,QAAMQ,WAAW,GAAGR,KAAK,CAACO,UAAD,CAAzB;;AAEA,QACE7jB,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAAzB,KACC4jB,WAAW,KAAK,IAAhB,IAAwBxU,KAAK,CAACC,OAAN,CAAcuU,WAAd,CAAxB,IAAsD,OAAOA,WAAP,KAAuB,QAD9E,CADF,EAGE;AACA,aAAOlgB,0BAAgB,CAAC,CAAD,CAAvB;AACD;;AAED,WAAOkgB,WAAP;AACD;;AAED,MAAIxU,KAAK,CAACC,OAAN,CAAc+T,KAAd,KAAwB,OAAOA,KAAP,KAAiB,QAA7C,EAAuD;AACrD,WAAO1f,0BAAgB,CAAC,CAAD,CAAvB;AACD;;AAED,SAAOigB,UAAU,gBAAQA,UAAR,MAAuBP,KAAvB,IAAiCA,KAAlD;AACD;AAED;;;;;AAGA,AAAe,SAASS,aAAT,CAAuBpG,KAAvB,EAAqC;AAClD,MAAMkG,UAAU,GAAGG,UAAU,CAACN,YAAD,CAA7B;AACA,MAAMO,YAAY,GAAGC,OAAO,CAAC;AAAA,WAAMN,UAAU,CAACjG,KAAK,CAAC2F,KAAP,EAAcO,UAAd,CAAhB;AAAA,GAAD,EAA4C,CACtElG,KAAK,CAAC2F,KADgE,EAEtEO,UAFsE,CAA5C,CAA5B;;AAKA,MAAI,CAAClG,KAAK,CAACT,QAAX,EAAqB;AACnB,WAAO,IAAP;AACD;;AAED,sBAAO,oBAAC,YAAD,CAAc,QAAd;AAAuB,IAAA,KAAK,EAAE+G;AAA9B,KAA6CtG,KAAK,CAACT,QAAnD,CAAP;AACD;;AC1CD;AAEA;;AACA,IAAMiH,SAAS,GAAG,SAAZA,SAAY;AAAA,SAAM,IAAN;AAAA,CAAlB;;IAEMC;;;;;;;;;;;UAGJC,QAAQ;;;;;;SAERC,SAAA,kBAAS;AAAA;;AACP,wBACE,oBAAC,aAAD,QACG,UAAChB,KAAD,EAAmB;AAAA,yBAUd,MAAI,CAAC3F,KAVS;AAAA,UAEX4G,eAFW,gBAEhBC,GAFgB;AAAA,UAGZC,QAHY,gBAGhBC,EAHgB;AAAA,UAIhBC,kBAJgB,gBAIhBA,kBAJgB;AAAA,UAKhBC,WALgB,gBAKhBA,WALgB;AAAA,UAMhBC,YANgB,gBAMhBA,YANgB;AAAA,UAOhBC,MAPgB,gBAOhBA,MAPgB;AAAA,4CAQhB5e,KARgB;AAAA,UAQhBA,KARgB,mCAQR,EARQ;AAAA,UASbyX,KATa;;AAAA,UAYV0F,YAZU,GAYkCsB,kBAZlC,CAYVtB,YAZU;AAAA,UAYItjB,MAZJ,GAYkC4kB,kBAZlC,CAYI5kB,MAZJ;AAAA,UAYYglB,iBAZZ,GAYkCJ,kBAZlC,CAYYI,iBAZZ;AAalB,UAAMC,mBAAmB,GACvB,MAAI,CAACX,KAAL,CAAWG,GAAX,IAAkB,MAAI,CAACH,KAAL,CAAWK,EAA7B,IAAmCH,eAAnC,IAAsDE,QAAtD,IAAkE1kB,MADpE;;AAGA,UAAMklB,eAAe,GAAG,MAAI,CAACC,uBAAL,CACtBC,cAAc,CAAC,MAAI,CAACxH,KAAN,EAAa2F,KAAb,EAAoBD,YAApB,CAAd,IAAmDvhB,YAD7B,EAEtB,MAAI,CAAC6b,KAFiB,CAAxB;;AAKA,UAAMyH,WAAW,GAAG7B,KAAK,CAACyB,mBAAD,CAAzB;AACA,UAAMK,aAAa,GAAG,MAAI,CAAChB,KAAL,KAAe1G,KAAf,gBAA4BA,KAA5B,MAAsC,MAAI,CAAC0G,KAA3C,IAAqD1G,KAA3E;AACA,UAAM2H,YAAY,GAAGP,iBAAiB,IAAKK,WAAW,IAAIjB,SAA1D;AACA,UAAMoB,eAAe,GAAG,EAAxB;AACA,UAAInW,GAAJ;;AAEA,WAAKA,GAAL,IAAYiW,aAAZ,EAA2B;AACzB,YAAIjW,GAAG,CAAC,CAAD,CAAH,KAAW,GAAX,IAAkBA,GAAG,KAAK,IAA9B,EAAoC,SAApC,KACK,IAAIA,GAAG,KAAK,aAAZ,EAA2B;AAC9BmW,UAAAA,eAAe,CAACb,EAAhB,GAAqB/G,KAAK,CAACvO,GAAD,CAA1B;AACD,SAFI,MAEE,IAAI,CAACkW,YAAD,IAAiBA,YAAY,CAAClW,GAAD,EAAM+U,SAAN,CAAjC,EAAmD;AACxD;AACAoB,UAAAA,eAAe,CAACnW,GAAD,CAAf,GAAuBiW,aAAa,CAACjW,GAAD,CAApC;AACD;AACF;;AAEDmW,MAAAA,eAAe,CAACrf,KAAhB,GAAwB,OAAOA,KAAP,KAAiB,UAAjB,GACxB,UAACsf,KAAD,EAAW;AACT,eAAO,CAACP,eAAD,EAAkB5D,MAAlB,CAAyBnb,KAAK,CAACsf,KAAD,CAA9B,CAAP;AACD,OAHuB,GAItB,CAACP,eAAD,EAAkB5D,MAAlB,CAAyBnb,KAAzB,CAJF;AAKAqf,MAAAA,eAAe,CAACT,MAAhB,GAAyBA,MAAM,IAAIS,eAAe,CAACT,MAAnD;AAEA,UAAID,YAAJ,EAAkBU,eAAe,CAACE,GAAhB,GAAsBZ,YAAtB;AAClB,UAAID,WAAJ,EAAiBW,eAAe,CAACb,EAAhB,GAAqBE,WAArB;AAEjB,aAAOhd,aAAa,CAACod,mBAAD,EAAsBO,eAAtB,CAApB;AACD,KAjDH,CADF;AAqDD;;SAEDG,wBAAA,+BAAsBpC,KAAtB,EAAsC3F,KAAtC,EAAqD0G,KAArD,EAAmE;AAAA;;AACjE,QAAMvZ,OAAO,gBAAQ6S,KAAR;AAAe2F,MAAAA,KAAK,EAALA;AAAf,MAAb;;AAEA,QAAI,CAACe,KAAK,CAAC3kB,MAAX,EAAmB,OAAOoL,OAAP;AAEnB,SAAKuZ,KAAL,GAAa,EAAb;AAEAA,IAAAA,KAAK,CAAC9hB,OAAN,CAAc,UAAAojB,OAAO,EAAI;AACvB,UAAIC,eAAe,GAAGD,OAAtB;AACA,UAAIE,IAAJ;AACA,UAAIzW,GAAJ;;AAEA,UAAIhP,UAAU,CAACwlB,eAAD,CAAd,EAAiC;AAC/BA,QAAAA,eAAe,GAAGA,eAAe,CAAC9a,OAAD,CAAjC;AACD;AAED;;;AACA,WAAKsE,GAAL,IAAYwW,eAAZ,EAA6B;AAC3BC,QAAAA,IAAI,GAAGD,eAAe,CAACxW,GAAD,CAAtB;AACA,QAAA,MAAI,CAACiV,KAAL,CAAWjV,GAAX,IAAkByW,IAAlB;AACA/a,QAAAA,OAAO,CAACsE,GAAD,CAAP,GAAeyW,IAAf;AACD;AACD;;AACD,KAhBD;AAkBA,WAAO/a,OAAP;AACD;;SAEDoa,0BAAA,iCAAwB5B,KAAxB,EAAoC3F,KAApC,EAAgD;AAAA,QACtCmI,WADsC,GACtBnI,KAAK,CAACgH,kBADgB,CACtCmB,WADsC;AAG9C,QAAMlW,gBAAgB,GAAG,KAAK8V,qBAAL,CACvBpC,KADuB,EAEvB3F,KAFuB,EAGvBA,KAAK,CAACgH,kBAAN,CAAyBN,KAHF,CAAzB;AAMA,WAAOyB,WAAW,CAACtD,mBAAZ,CAAgC5S,gBAAhC,CAAP;AACD;;SAEDmW,iBAAA,wBAAeC,WAAf,EAAoC;AAClC,QAAI,KAAKlQ,IAAL,KAAcrQ,SAAlB,EAA6B;AAC3B;AACA,WAAKqQ,IAAL,CAAUiQ,cAAV,CAAyBC,WAAzB;AACD,KAHD,MAGO,IAAIhmB,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AAChD;AACA8P,MAAAA,OAAO,CAACC,IAAR,CACE,4FADF;AAGD;AACF;;;EA/GiCgW;;AAkHpC,8BAAe,UAAC1D,WAAD,EAA2B;AACxC,MAAM2D,2BAA2B,GAAG,SAA9BA,2BAA8B,CAACnmB,MAAD,EAAiB0J,OAAjB,EAAkCjG,KAAlC,EAAqD;AAAA,yBAKnFiG,OALmF,CAErF4a,KAFqF;AAAA,QAErFA,KAFqF,+BAE7EziB,WAF6E;AAAA,+BAKnF6H,OALmF,CAGrFtJ,WAHqF;AAAA,QAGrFA,WAHqF,qCAGvEsjB,mBAAmB,CAAC1jB,MAAD,CAHoD;AAAA,gCAKnF0J,OALmF,CAIrF0c,eAJqF;AAAA,QAIrFA,eAJqF,sCAInE/B,qBAJmE;AAOvF,QAAMgC,OAAO,GAAG,CAAC7C,KAAK,CAACxjB,MAAD,CAAtB;AACA,QAAMsmB,kBAAkB,GAAGxlB,iBAAiB,CAACd,MAAD,CAA5C,CARuF;;AAWvF,QAAMumB,4BAA4B,GAAGhZ,KAAK,CAACiZ,UAAN,CAAiB,UAAC5I,KAAD,EAAQ8H,GAAR;AAAA,0BACpD,oBAAC,eAAD,eACM9H,KADN;AAEE,QAAA,kBAAkB,EAAE2I,4BAFtB;AAGE,QAAA,YAAY,EAAEb;AAHhB,SADoD;AAAA,KAAjB,CAArC;AAQA,QAAMe,UAAU;AAEdH,IAAAA,kBAAkB,IAAItmB,MAAM,CAACskB,KAA7B,GACI/U,KAAK,CAAC/O,SAAN,CAAgB8gB,MAAhB,CAAuBthB,MAAM,CAACskB,KAA9B,EAAqCA,KAArC,EAA4C1E,MAA5C,CAAmDle,OAAnD,CADJ,GAEI4iB,KAJN,CAnBuF;;AA0BvF,QAAIU,iBAAiB,GAAGtb,OAAO,CAACsb,iBAAhC,CA1BuF;;AA6BvF,QAAIsB,kBAAkB,IAAItmB,MAAM,CAACglB,iBAAjC,EAAoD;AAClD,UAAIA,iBAAJ,EAAuB;AACrB;AACAA,QAAAA,iBAAiB,GAAG,2BAACxO,IAAD,EAAOkQ,QAAP,EAAiBC,kBAAjB;AAAA;AAElB3mB,YAAAA,MAAM,CAACglB,iBAAP,CAAyBxO,IAAzB,EAA+BkQ,QAA/B,EAAyCC,kBAAzC,KACAjd,OAAO,CAACsb,iBAAR,CAA0BxO,IAA1B,EAAgCkQ,QAAhC,EAA0CC,kBAA1C;AAHkB;AAAA,SAApB;AAID,OAND,MAMO;AACL;AACA3B,QAAAA,iBAAiB,GAAGhlB,MAAM,CAACglB,iBAA3B;AACD;AACF;AAED;;;;AAKA;;;AACAuB,IAAAA,4BAA4B,CAACjC,KAA7B,GAAqCmC,UAArC;AAEAF,IAAAA,4BAA4B,CAACnmB,WAA7B,GAA2CA,WAA3C,CAlDuF;;AAqDvFmmB,IAAAA,4BAA4B,CAACvB,iBAA7B,GAAiDA,iBAAjD,CArDuF;;AAwDvFuB,IAAAA,4BAA4B,CAACR,WAA7B,GAA2C,IAAIvD,WAAJ;AAEzC8D,IAAAA,kBAAkB,GAAGtmB,MAAM,CAAC+lB,WAAP,CAAmBtiB,KAAnB,CAAyB6d,MAAzB,CAAgC7d,KAAhC,CAAH,GAA4CA,KAFrB,CAA3C,CAxDuF;;AA8DvF8iB,IAAAA,4BAA4B,CAACxlB,iBAA7B,GAAiD,uBAAjD,CA9DuF;;AAgEvFwlB,IAAAA,4BAA4B,CAACvmB,MAA7B,GAAsCsmB,kBAAkB;AAEpDtmB,IAAAA,MAAM,CAACA,MAF6C,GAGpDA,MAHJ,CAhEuF;;AAqEvFumB,IAAAA,4BAA4B,CAACK,aAA7B,GAA6C,SAASA,aAAT,CAAuB5jB,GAAvB,EAAoC;AAAA,UAC1D2J,CAD0D,GACjBjD,OADiB,CACvEtJ,WADuE;AAAA,UAC1CymB,EAD0C,GACjBnd,OADiB,CACvDsD,WADuD;AAAA,UACnC8Z,aADmC,iCACjBpd,OADiB;;AAE/E,UAAMqd,UAAU,gBACXD,aADW;AAEdxC,QAAAA,KAAK,EAAEmC,UAFO;AAGdL,QAAAA,eAAe,EAAfA;AAHc,QAAhB;;AAMA,aAAOD,2BAA2B,CAACnjB,GAAD,EAAM+jB,UAAN,EAAkBtjB,KAAlB,CAAlC;AACD,KATD,CArEuF;;;AAiFvF9C,IAAAA,MAAM,CAACqmB,cAAP,CAAsBT,4BAAtB,EAAoD,cAApD,EAAoE;AAClEthB,MAAAA,GADkE,iBAC5D;AACJ,eAAO,KAAKgiB,mBAAZ;AACD,OAHiE;AAKlEnjB,MAAAA,GALkE,eAK9DqL,GAL8D,EAKzD;AACP;AACA,aAAK8X,mBAAL,GAA2BX,kBAAkB,GAAGY,SAAK,CAAC,EAAD,EAAKlnB,MAAM,CAACsjB,YAAZ,EAA0BnU,GAA1B,CAAR,GAAyCA,GAAtF;AACD;AARiE,KAApE;;AAWA,QAAIkX,OAAJ,EAAa;AACXc,MAAAA,KAAK,CAACZ,4BAAD,EAAgCvmB,MAAhC,EAA8C;AACjD;AACAskB,QAAAA,KAAK,EAAE,IAF0C;AAGjDlkB,QAAAA,WAAW,EAAE,IAHoC;AAIjD4kB,QAAAA,iBAAiB,EAAE,IAJ8B;AAKjDe,QAAAA,WAAW,EAAE,IALoC;AAMjDhlB,QAAAA,iBAAiB,EAAE,IAN8B;AAOjDf,QAAAA,MAAM,EAAE,IAPyC;AAQjD4mB,QAAAA,aAAa,EAAE;AARkC,OAA9C,CAAL;AAUD;;AAED,WAAOL,4BAAP;AACD,GA1GD;;AA4GA,SAAOJ,2BAAP;AACD,CA9GD;;ACvIA;AAGA,kBAAe,UACbiB,OADa,EAEbzkB,cAFa,EAGY;AACzB,MAAMoN,MAAM,GAAG,CAACqX,OAAO,CAAC,CAAD,CAAR,CAAf;;AAEA,OAAK,IAAI1nB,CAAC,GAAG,CAAR,EAAW4C,GAAG,GAAGK,cAAc,CAAChD,MAArC,EAA6CD,CAAC,GAAG4C,GAAjD,EAAsD5C,CAAC,IAAI,CAA3D,EAA8D;AAC5DqQ,IAAAA,MAAM,CAACxN,IAAP,CAAYI,cAAc,CAACjD,CAAD,CAA1B,EAA+B0nB,OAAO,CAAC1nB,CAAC,GAAG,CAAL,CAAtC;AACD;;AAED,SAAOqQ,MAAP;AACD,CAXD;;ACHA;AACA,AAOA;;;;;AAIA,IAAMsX,MAAM,GAAG,SAATA,MAAS,CAAAC,GAAG,EAAI;AACpB,MAAI/X,KAAK,CAACC,OAAN,CAAc8X,GAAd,CAAJ,EAAwB;AACtB;AACAA,IAAAA,GAAG,CAAC7X,KAAJ,GAAY,IAAZ;AACD;;AACD,SAAO6X,GAAP;AACD,CAND;;AAQA,AAAe,SAAS/iB,GAAT,CAAaue,MAAb,EAA+E;AAAA,oCAA/CngB,cAA+C;AAA/CA,IAAAA,cAA+C;AAAA;;AAC5F,MAAItC,UAAU,CAACyiB,MAAD,CAAV,IAAsBnT,aAAa,CAACmT,MAAD,CAAvC,EAAiD;AAC/C;AACA,WAAOuE,MAAM,CAACzX,OAAO,CAAC2X,UAAU,CAAC1lB,WAAD,GAAeihB,MAAf,SAA0BngB,cAA1B,EAAX,CAAR,CAAb;AACD;;AAED,MAAIA,cAAc,CAAChD,MAAf,KAA0B,CAA1B,IAA+BmjB,MAAM,CAACnjB,MAAP,KAAkB,CAAjD,IAAsD,OAAOmjB,MAAM,CAAC,CAAD,CAAb,KAAqB,QAA/E,EAAyF;AACvF;AACA,WAAOA,MAAP;AACD,GAT2F;;;AAY5F,SAAOuE,MAAM,CAACzX,OAAO,CAAC2X,UAAU,CAACzE,MAAD,EAASngB,cAAT,CAAX,CAAR,CAAb;AACD;;ACzBc,SAAS6kB,oBAAT,CACbC,oBADa,EAEbzkB,GAFa,EAGb0G,OAHa,EAIb;AAAA,MADAA,OACA;AADAA,IAAAA,OACA,GADkB3H,YAClB;AAAA;;AACA,MAAI,CAAC2lB,kBAAkB,CAAC1kB,GAAD,CAAvB,EAA8B;AAC5B,WAAOa,0BAAgB,CAAC,CAAD,EAAI9E,MAAM,CAACiE,GAAD,CAAV,CAAvB;AACD;AAED;AACA;;;AACA,MAAM2kB,gBAAgB,GAAG,SAAnBA,gBAAmB;AAAA,WAAaF,oBAAoB,CAACzkB,GAAD,EAAM0G,OAAN,EAAenF,GAAG,MAAH,mBAAf,CAAjC;AAAA,GAAzB;AAEA;;;AACAojB,EAAAA,gBAAgB,CAACC,UAAjB,GAA8B,UAAAC,MAAM;AAAA,WAClCL,oBAAoB,CAACC,oBAAD,EAAuBzkB,GAAvB,eAAiC0G,OAAjC,MAA6Cme,MAA7C,EADc;AAAA,GAApC;AAGA;;;AACAF,EAAAA,gBAAgB,CAACrD,KAAjB,GAAyB,UAAAA,KAAK;AAAA,WAC5BkD,oBAAoB,CAACC,oBAAD,EAAuBzkB,GAAvB,eACf0G,OADe;AAElB4a,MAAAA,KAAK,EAAE/U,KAAK,CAAC/O,SAAN,CAAgB8gB,MAAhB,CAAuB5X,OAAO,CAAC4a,KAA/B,EAAsCA,KAAtC,EAA6C1E,MAA7C,CAAoDle,OAApD;AAFW,OADQ;AAAA,GAA9B;;AAMA,SAAOimB,gBAAP;AACD;;ACzBD;AACA;AACA;AACA;AACA;;AAEA,iBAAe,UAACzB,SAAD,EAAwC;AACrD;AACA,MAAM4B,SAAS,GAAGva,KAAK,CAACiZ,UAAN,CAAiB,UAAC5I,KAAD,EAAQ8H,GAAR,EAAgB;AACjD,QAAMnC,KAAK,GAAGU,UAAU,CAACN,YAAD,CAAxB,CADiD;;AAAA,QAGzCL,YAHyC,GAGxB4C,SAHwB,CAGzC5C,YAHyC;AAIjD,QAAMyE,SAAS,GAAG3C,cAAc,CAACxH,KAAD,EAAQ2F,KAAR,EAAeD,YAAf,CAAhC;;AAEA,QAAIrjB,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAAzB,IAAyC4nB,SAAS,KAAKriB,SAA3D,EAAsE;AACpE;AACAuK,MAAAA,OAAO,CAACC,IAAR,6HAC2HnQ,gBAAgB,CACvImmB,SADuI,CAD3I;AAKD;;AAED,wBAAO,oBAAC,SAAD,eAAetI,KAAf;AAAsB,MAAA,KAAK,EAAEmK,SAA7B;AAAwC,MAAA,GAAG,EAAErC;AAA7C,OAAP;AACD,GAhBiB,CAAlB;AAkBAsC,EAAAA,KAAY,CAACF,SAAD,EAAY5B,SAAZ,CAAZ;AAEA4B,EAAAA,SAAS,CAAC1nB,WAAV,kBAAqCL,gBAAgB,CAACmmB,SAAD,CAArD;AAEA,SAAO4B,SAAP;AACD,CAzBD;;ACdA;AACA;AAGA,IAAMG,QAAQ,GAAG,SAAXA,QAAW;AAAA,SAAMhE,UAAU,CAACN,YAAD,CAAhB;AAAA,CAAjB;;ACJA;;AAeA,IAAMuE,WAAW,GAAGC,OAAO,CAAC,cAAD,CAA3B;;AAEA,IAAM3F,WAAW,GAAG4F,YAAY,CAACF,WAAW,CAAC1e,UAAb,CAAhC;;AACA,IAAM6a,uBAAqB,GAAGgE,sBAAsB,CAAC7F,WAAD,CAApD;;AACA,IAAM8F,MAAM,GAAG,SAATA,MAAS,CAACtlB,GAAD;AAAA,SAAiBwkB,oBAAoB,CAACnD,uBAAD,EAAwBrhB,GAAxB,CAArC;AAAA,CAAf;AAEA;;;;AAEA,IAAMulB,OAAO,mpBAAb;AAQA;;;AAEAA,OAAO,CAACviB,KAAR,CAAc,MAAd,EAAsBxD,OAAtB,CAA8B,UAAAgmB,KAAK;AAAA,SACjC7nB,MAAM,CAACqmB,cAAP,CAAsBsB,MAAtB,EAA8BE,KAA9B,EAAqC;AACnCC,IAAAA,UAAU,EAAE,IADuB;AAEnCC,IAAAA,YAAY,EAAE,KAFqB;AAGnCzjB,IAAAA,GAHmC,iBAG7B;AACJ,aAAOqjB,MAAM,CAACJ,WAAW,CAACM,KAAD,CAAZ,CAAb;AACD;AALkC,GAArC,CADiC;AAAA,CAAnC;;;;;"}