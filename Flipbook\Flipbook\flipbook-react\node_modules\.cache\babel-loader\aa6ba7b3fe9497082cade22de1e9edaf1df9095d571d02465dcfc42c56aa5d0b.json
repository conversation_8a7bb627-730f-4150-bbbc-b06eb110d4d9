{"ast": null, "code": "import React,{useState}from'react';import TextEditor from'../TextEditor/TextEditor';import ImageLibrary from'../ImageLibrary/ImageLibrary';import'./FrontPage.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FrontPage=_ref=>{let{title,subtitle,backgroundImage,onTitleChange,onSubtitleChange,onBackgroundImageChange,onNext}=_ref;const[activeEditor,setActiveEditor]=useState(null);const[showImageLibrary,setShowImageLibrary]=useState(false);const handleImageUploadClick=()=>{setShowImageLibrary(true);};const handleImageSelect=imageUrl=>{onBackgroundImageChange(imageUrl);setShowImageLibrary(false);};const handleEditorFocus=editor=>{setActiveEditor(editor);};const handleEditorBlur=()=>{setActiveEditor(null);};return/*#__PURE__*/_jsxs(\"div\",{className:\"front-page\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"page-header\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"pages-title\",children:\"PAGES\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flipbook-title\",children:/*#__PURE__*/_jsx(TextEditor,{value:title,onChange:onTitleChange,placeholder:\"Hanash\",isActive:activeEditor==='title',onFocus:()=>handleEditorFocus('title'),onBlur:handleEditorBlur,className:\"title-editor\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"main-content\",onClick:handleImageUploadClick,style:{backgroundImage:backgroundImage?\"url(\".concat(backgroundImage,\")\"):'none'},children:[/*#__PURE__*/_jsx(\"div\",{className:\"decorative-border-frame\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"center-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"name-section\",children:/*#__PURE__*/_jsx(TextEditor,{value:subtitle||\"ZARA IRUM\",onChange:onSubtitleChange,placeholder:\"ZARA IRUM\",isActive:activeEditor==='subtitle',onFocus:()=>handleEditorFocus('subtitle'),onBlur:handleEditorBlur,className:\"main-name-editor\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"profession-title\",children:\"ARCHITECT\"})]}),!backgroundImage&&/*#__PURE__*/_jsx(\"div\",{className:\"upload-hint\",children:/*#__PURE__*/_jsx(\"span\",{children:\"Tip: A single click on an image will open your Image editing tools. A double click will open your Image toolbar and Rollover\"})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"navigation-controls\",children:/*#__PURE__*/_jsx(\"button\",{className:\"next-page-btn\",onClick:onNext,children:\"\\u2192\"})}),/*#__PURE__*/_jsx(ImageLibrary,{isOpen:showImageLibrary,onClose:()=>setShowImageLibrary(false),onSelectImage:handleImageSelect})]});};export default FrontPage;", "map": {"version": 3, "names": ["React", "useState", "TextEditor", "ImageLibrary", "jsx", "_jsx", "jsxs", "_jsxs", "FrontPage", "_ref", "title", "subtitle", "backgroundImage", "onTitleChange", "onSubtitleChange", "onBackgroundImageChange", "onNext", "activeEditor", "setActiveEditor", "showImageLibrary", "setShowImageLibrary", "handleImageUploadClick", "handleImageSelect", "imageUrl", "handleEditorFocus", "editor", "handleEditorBlur", "className", "children", "value", "onChange", "placeholder", "isActive", "onFocus", "onBlur", "onClick", "style", "concat", "isOpen", "onClose", "onSelectImage"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/FrontPage/FrontPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport TextEditor from '../TextEditor/TextEditor';\nimport ImageLibrary from '../ImageLibrary/ImageLibrary';\nimport './FrontPage.css';\n\ninterface FrontPageProps {\n  title: string;\n  subtitle: string;\n  backgroundImage: string;\n  onTitleChange: (title: string) => void;\n  onSubtitleChange: (subtitle: string) => void;\n  onBackgroundImageChange: (imageUrl: string) => void;\n  onNext: () => void;\n}\n\nconst FrontPage: React.FC<FrontPageProps> = ({\n  title,\n  subtitle,\n  backgroundImage,\n  onTitleChange,\n  onSubtitleChange,\n  onBackgroundImageChange,\n  onNext\n}) => {\n  const [activeEditor, setActiveEditor] = useState<'title' | 'subtitle' | 'name' | null>(null);\n  const [showImageLibrary, setShowImageLibrary] = useState(false);\n\n  const handleImageUploadClick = () => {\n    setShowImageLibrary(true);\n  };\n\n  const handleImageSelect = (imageUrl: string) => {\n    onBackgroundImageChange(imageUrl);\n    setShowImageLibrary(false);\n  };\n\n  const handleEditorFocus = (editor: 'title' | 'subtitle' | 'name') => {\n    setActiveEditor(editor);\n  };\n\n  const handleEditorBlur = () => {\n    setActiveEditor(null);\n  };\n\n  return (\n    <div className=\"front-page\">\n      {/* Top Header */}\n      <div className=\"page-header\">\n        <h1 className=\"pages-title\">PAGES</h1>\n        <div className=\"flipbook-title\">\n          <TextEditor\n            value={title}\n            onChange={onTitleChange}\n            placeholder=\"Hanash\"\n            isActive={activeEditor === 'title'}\n            onFocus={() => handleEditorFocus('title')}\n            onBlur={handleEditorBlur}\n            className=\"title-editor\"\n          />\n        </div>\n      </div>\n\n      {/* Main Content Area */}\n      <div\n        className=\"main-content\"\n        onClick={handleImageUploadClick}\n        style={{\n          backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'none'\n        }}\n      >\n        {/* Decorative Border Frame */}\n        <div className=\"decorative-border-frame\"></div>\n\n        {/* Center Content */}\n        <div className=\"center-content\">\n          <div className=\"name-section\">\n            <TextEditor\n              value={subtitle || \"ZARA IRUM\"}\n              onChange={onSubtitleChange}\n              placeholder=\"ZARA IRUM\"\n              isActive={activeEditor === 'subtitle'}\n              onFocus={() => handleEditorFocus('subtitle')}\n              onBlur={handleEditorBlur}\n              className=\"main-name-editor\"\n            />\n          </div>\n          <div className=\"profession-title\">\n            ARCHITECT\n          </div>\n        </div>\n\n        {/* Upload Hint */}\n        {!backgroundImage && (\n          <div className=\"upload-hint\">\n            <span>Tip: A single click on an image will open your Image editing tools. A double click will open your Image toolbar and Rollover</span>\n          </div>\n        )}\n      </div>\n\n      {/* Navigation Arrow */}\n      <div className=\"navigation-controls\">\n        <button className=\"next-page-btn\" onClick={onNext}>\n          →\n        </button>\n      </div>\n      \n      {/* Image Library Modal */}\n      <ImageLibrary\n        isOpen={showImageLibrary}\n        onClose={() => setShowImageLibrary(false)}\n        onSelectImage={handleImageSelect}\n      />\n    </div>\n  );\n};\n\nexport default FrontPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,UAAU,KAAM,0BAA0B,CACjD,MAAO,CAAAC,YAAY,KAAM,8BAA8B,CACvD,MAAO,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAYzB,KAAM,CAAAC,SAAmC,CAAGC,IAAA,EAQtC,IARuC,CAC3CC,KAAK,CACLC,QAAQ,CACRC,eAAe,CACfC,aAAa,CACbC,gBAAgB,CAChBC,uBAAuB,CACvBC,MACF,CAAC,CAAAP,IAAA,CACC,KAAM,CAACQ,YAAY,CAAEC,eAAe,CAAC,CAAGjB,QAAQ,CAAuC,IAAI,CAAC,CAC5F,KAAM,CAACkB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CAE/D,KAAM,CAAAoB,sBAAsB,CAAGA,CAAA,GAAM,CACnCD,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAIC,QAAgB,EAAK,CAC9CR,uBAAuB,CAACQ,QAAQ,CAAC,CACjCH,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAI,iBAAiB,CAAIC,MAAqC,EAAK,CACnEP,eAAe,CAACO,MAAM,CAAC,CACzB,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7BR,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED,mBACEX,KAAA,QAAKoB,SAAS,CAAC,YAAY,CAAAC,QAAA,eAEzBrB,KAAA,QAAKoB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BvB,IAAA,OAAIsB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,OAAK,CAAI,CAAC,cACtCvB,IAAA,QAAKsB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BvB,IAAA,CAACH,UAAU,EACT2B,KAAK,CAAEnB,KAAM,CACboB,QAAQ,CAAEjB,aAAc,CACxBkB,WAAW,CAAC,QAAQ,CACpBC,QAAQ,CAAEf,YAAY,GAAK,OAAQ,CACnCgB,OAAO,CAAEA,CAAA,GAAMT,iBAAiB,CAAC,OAAO,CAAE,CAC1CU,MAAM,CAAER,gBAAiB,CACzBC,SAAS,CAAC,cAAc,CACzB,CAAC,CACC,CAAC,EACH,CAAC,cAGNpB,KAAA,QACEoB,SAAS,CAAC,cAAc,CACxBQ,OAAO,CAAEd,sBAAuB,CAChCe,KAAK,CAAE,CACLxB,eAAe,CAAEA,eAAe,QAAAyB,MAAA,CAAUzB,eAAe,MAAM,MACjE,CAAE,CAAAgB,QAAA,eAGFvB,IAAA,QAAKsB,SAAS,CAAC,yBAAyB,CAAM,CAAC,cAG/CpB,KAAA,QAAKoB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BvB,IAAA,QAAKsB,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BvB,IAAA,CAACH,UAAU,EACT2B,KAAK,CAAElB,QAAQ,EAAI,WAAY,CAC/BmB,QAAQ,CAAEhB,gBAAiB,CAC3BiB,WAAW,CAAC,WAAW,CACvBC,QAAQ,CAAEf,YAAY,GAAK,UAAW,CACtCgB,OAAO,CAAEA,CAAA,GAAMT,iBAAiB,CAAC,UAAU,CAAE,CAC7CU,MAAM,CAAER,gBAAiB,CACzBC,SAAS,CAAC,kBAAkB,CAC7B,CAAC,CACC,CAAC,cACNtB,IAAA,QAAKsB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,WAElC,CAAK,CAAC,EACH,CAAC,CAGL,CAAChB,eAAe,eACfP,IAAA,QAAKsB,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BvB,IAAA,SAAAuB,QAAA,CAAM,8HAA4H,CAAM,CAAC,CACtI,CACN,EACE,CAAC,cAGNvB,IAAA,QAAKsB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAClCvB,IAAA,WAAQsB,SAAS,CAAC,eAAe,CAACQ,OAAO,CAAEnB,MAAO,CAAAY,QAAA,CAAC,QAEnD,CAAQ,CAAC,CACN,CAAC,cAGNvB,IAAA,CAACF,YAAY,EACXmC,MAAM,CAAEnB,gBAAiB,CACzBoB,OAAO,CAAEA,CAAA,GAAMnB,mBAAmB,CAAC,KAAK,CAAE,CAC1CoB,aAAa,CAAElB,iBAAkB,CAClC,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAd,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}