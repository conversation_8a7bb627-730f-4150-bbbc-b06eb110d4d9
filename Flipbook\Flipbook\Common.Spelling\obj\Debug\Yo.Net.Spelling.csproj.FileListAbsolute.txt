C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\Hunspellx64.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\Hunspellx86.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\Yo.Net.Spelling.dll.config
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\Yo.Net.Spelling.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\Yo.Net.Spelling.pdb
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\csc.exe
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\csc.exe.config
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\csc.rsp
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\csi.exe
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\csi.rsp
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\Microsoft.CodeAnalysis.Scripting.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\Microsoft.CSharp.Core.targets
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\Microsoft.DiaSymReader.Native.x86.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\Microsoft.VisualBasic.Core.targets
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\System.AppContext.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\System.Collections.Immutable.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\System.IO.FileSystem.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\System.Reflection.Metadata.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\vbc.exe
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\vbc.exe.config
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\vbc.rsp
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\VBCSCompiler.exe
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\roslyn\VBCSCompiler.exe.config
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\NHunspell.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Debug\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\obj\Debug\Yo.Net.Spelling.csprojAssemblyReference.cache
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\obj\Debug\Yo.Net.Spelling.Common.Spell.Resources.resources
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\obj\Debug\Yo.Net.Spelling.csproj.GenerateResource.cache
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\obj\Debug\Yo.Net.Spelling.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\obj\Debug\Yo.Net.Spelling.csproj.CopyComplete
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\obj\Debug\Yo.Net.Spelling.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\obj\Debug\Yo.Net.Spelling.pdb
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\Hunspellx64.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\Hunspellx86.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\Yo.Net.Spelling.dll.config
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\Yo.Net.Spelling.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\Yo.Net.Spelling.pdb
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\csc.exe
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\csc.exe.config
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\csc.rsp
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\csi.exe
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\csi.rsp
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\Microsoft.CodeAnalysis.Scripting.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\Microsoft.CSharp.Core.targets
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\Microsoft.DiaSymReader.Native.x86.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\Microsoft.VisualBasic.Core.targets
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\System.AppContext.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\System.Collections.Immutable.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\System.IO.FileSystem.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\System.Reflection.Metadata.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\vbc.exe
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\vbc.exe.config
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\vbc.rsp
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\VBCSCompiler.exe
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\roslyn\VBCSCompiler.exe.config
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\NHunspell.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Debug\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\obj\Debug\Yo.Net.Spelling.csprojAssemblyReference.cache
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\obj\Debug\Yo.Net.Spelling.Common.Spell.Resources.resources
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\obj\Debug\Yo.Net.Spelling.csproj.GenerateResource.cache
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\obj\Debug\Yo.Net.Spelling.csproj.CopyComplete
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\obj\Debug\Yo.Net.Spelling.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\obj\Debug\Yo.Net.Spelling.pdb
