{"ast": null, "code": "import _objectSpread from\"D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";// Custom hooks for flipbook data management\nimport{useState,useCallback}from'react';import{apiService}from'../services/api.service';export const useFlipbooks=()=>{const[userFlipbooks,setUserFlipbooks]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const loadFlipbooks=useCallback(async()=>{try{setLoading(true);setError(null);// Load only user flipbooks (inspiration flipbooks are loaded in initial data)\nconst userResponse=await apiService.getMyFlipbooks();if(userResponse.success){setUserFlipbooks(userResponse.data);}else{console.error('Failed to load user flipbooks:',userResponse.error);setError('Failed to load your flipbooks. Please try again.');}}catch(err){setError('An unexpected error occurred while loading your flipbooks.');console.error('Error loading user flipbooks:',err);}finally{setLoading(false);}},[]);const refreshFlipbooks=useCallback(async()=>{await loadFlipbooks();},[loadFlipbooks]);const createFlipbook=useCallback(async request=>{try{const response=await apiService.createNewFlipbook(request);if(response.success){// Refresh the flipbooks list to include the new one\nawait refreshFlipbooks();return true;}else{setError(response.error||'Failed to create flipbook');return false;}}catch(err){setError('An error occurred while creating the flipbook.');console.error('Error creating flipbook:',err);return false;}},[refreshFlipbooks]);const copyInspirationFlipbook=useCallback(async request=>{try{const response=await apiService.copyInspirationFlipbook(request);if(response.success){// Refresh the flipbooks list to include the copied one\nawait refreshFlipbooks();return true;}else{setError(response.error||'Failed to copy inspiration flipbook');return false;}}catch(err){setError('An error occurred while copying the inspiration flipbook.');console.error('Error copying inspiration flipbook:',err);return false;}},[refreshFlipbooks]);const updateFlipbook=useCallback(async request=>{try{const response=await apiService.updateFlipbook(request);if(response.success){// Update the local state\nsetUserFlipbooks(prev=>prev.map(fb=>fb.PortfolioID===request.portfolioId?_objectSpread(_objectSpread({},fb),{},{PortfolioTitle:request.title}):fb));return true;}else{setError(response.error||'Failed to update flipbook');return false;}}catch(err){setError('An error occurred while updating the flipbook.');console.error('Error updating flipbook:',err);return false;}},[]);const deleteFlipbook=useCallback(async portfolioId=>{try{const response=await apiService.deleteFlipbook(portfolioId);if(response.success){// Remove from local state\nsetUserFlipbooks(prev=>prev.filter(fb=>fb.PortfolioID!==portfolioId));return true;}else{setError(response.error||'Failed to delete flipbook');return false;}}catch(err){setError('An error occurred while deleting the flipbook.');console.error('Error deleting flipbook:',err);return false;}},[]);const setFlipbookPreferred=useCallback(async(portfolioId,isPreferred)=>{try{const response=await apiService.setFlipbookPreferred(portfolioId,isPreferred);if(response.success){// Update the local state\nsetUserFlipbooks(prev=>prev.map(fb=>fb.PortfolioID===portfolioId?_objectSpread(_objectSpread({},fb),{},{IsPreferred:isPreferred}):fb));return true;}else{setError(response.error||'Failed to update flipbook preference');return false;}}catch(err){setError('An error occurred while updating flipbook preference.');console.error('Error updating flipbook preference:',err);return false;}},[]);// Load flipbooks only when explicitly called (by authenticated users)\n// useEffect(() => {\n//   loadFlipbooks();\n// }, [loadFlipbooks]);\nreturn{userFlipbooks,loading,error,refreshFlipbooks,createFlipbook,copyInspirationFlipbook,updateFlipbook,deleteFlipbook,setFlipbookPreferred};};", "map": {"version": 3, "names": ["useState", "useCallback", "apiService", "useFlipbooks", "userFlipbooks", "setUserFlipbooks", "loading", "setLoading", "error", "setError", "loadFlipbooks", "userResponse", "getMyFlipbooks", "success", "data", "console", "err", "refreshFlipbooks", "createFlipbook", "request", "response", "createNewFlipbook", "copyInspirationFlipbook", "updateFlipbook", "prev", "map", "fb", "PortfolioID", "portfolioId", "_objectSpread", "PortfolioTitle", "title", "deleteFlipbook", "filter", "setFlipbookPreferred", "isPreferred", "IsPreferred"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/hooks/useFlipbooks.ts"], "sourcesContent": ["// Custom hooks for flipbook data management\n\nimport { useState, useCallback } from 'react';\nimport { apiService } from '../services/api.service';\nimport {\n  Portfolio,\n  CreateFlipbookRequest,\n  CopyInspirationRequest,\n  UpdateFlipbookRequest\n} from '../types/flipbook.types';\n\nexport interface UseFlipbooksReturn {\n  userFlipbooks: Portfolio[];\n  loading: boolean;\n  error: string | null;\n  refreshFlipbooks: () => Promise<void>;\n  createFlipbook: (request: CreateFlipbookRequest) => Promise<boolean>;\n  copyInspirationFlipbook: (request: CopyInspirationRequest) => Promise<boolean>;\n  updateFlipbook: (request: UpdateFlipbookRequest) => Promise<boolean>;\n  deleteFlipbook: (portfolioId: number) => Promise<boolean>;\n  setFlipbookPreferred: (portfolioId: number, isPreferred: boolean) => Promise<boolean>;\n}\n\nexport const useFlipbooks = (): UseFlipbooksReturn => {\n  const [userFlipbooks, setUserFlipbooks] = useState<Portfolio[]>([]);\n  const [loading, setLoading] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const loadFlipbooks = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Load only user flipbooks (inspiration flipbooks are loaded in initial data)\n      const userResponse = await apiService.getMyFlipbooks();\n\n      if (userResponse.success) {\n        setUserFlipbooks(userResponse.data);\n      } else {\n        console.error('Failed to load user flipbooks:', userResponse.error);\n        setError('Failed to load your flipbooks. Please try again.');\n      }\n    } catch (err) {\n      setError('An unexpected error occurred while loading your flipbooks.');\n      console.error('Error loading user flipbooks:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const refreshFlipbooks = useCallback(async () => {\n    await loadFlipbooks();\n  }, [loadFlipbooks]);\n\n  const createFlipbook = useCallback(async (request: CreateFlipbookRequest): Promise<boolean> => {\n    try {\n      const response = await apiService.createNewFlipbook(request);\n      if (response.success) {\n        // Refresh the flipbooks list to include the new one\n        await refreshFlipbooks();\n        return true;\n      } else {\n        setError(response.error || 'Failed to create flipbook');\n        return false;\n      }\n    } catch (err) {\n      setError('An error occurred while creating the flipbook.');\n      console.error('Error creating flipbook:', err);\n      return false;\n    }\n  }, [refreshFlipbooks]);\n\n  const copyInspirationFlipbook = useCallback(async (request: CopyInspirationRequest): Promise<boolean> => {\n    try {\n      const response = await apiService.copyInspirationFlipbook(request);\n      if (response.success) {\n        // Refresh the flipbooks list to include the copied one\n        await refreshFlipbooks();\n        return true;\n      } else {\n        setError(response.error || 'Failed to copy inspiration flipbook');\n        return false;\n      }\n    } catch (err) {\n      setError('An error occurred while copying the inspiration flipbook.');\n      console.error('Error copying inspiration flipbook:', err);\n      return false;\n    }\n  }, [refreshFlipbooks]);\n\n  const updateFlipbook = useCallback(async (request: UpdateFlipbookRequest): Promise<boolean> => {\n    try {\n      const response = await apiService.updateFlipbook(request);\n      if (response.success) {\n        // Update the local state\n        setUserFlipbooks(prev => \n          prev.map(fb => \n            fb.PortfolioID === request.portfolioId \n              ? { ...fb, PortfolioTitle: request.title }\n              : fb\n          )\n        );\n        return true;\n      } else {\n        setError(response.error || 'Failed to update flipbook');\n        return false;\n      }\n    } catch (err) {\n      setError('An error occurred while updating the flipbook.');\n      console.error('Error updating flipbook:', err);\n      return false;\n    }\n  }, []);\n\n  const deleteFlipbook = useCallback(async (portfolioId: number): Promise<boolean> => {\n    try {\n      const response = await apiService.deleteFlipbook(portfolioId);\n      if (response.success) {\n        // Remove from local state\n        setUserFlipbooks(prev => prev.filter(fb => fb.PortfolioID !== portfolioId));\n        return true;\n      } else {\n        setError(response.error || 'Failed to delete flipbook');\n        return false;\n      }\n    } catch (err) {\n      setError('An error occurred while deleting the flipbook.');\n      console.error('Error deleting flipbook:', err);\n      return false;\n    }\n  }, []);\n\n  const setFlipbookPreferred = useCallback(async (portfolioId: number, isPreferred: boolean): Promise<boolean> => {\n    try {\n      const response = await apiService.setFlipbookPreferred(portfolioId, isPreferred);\n      if (response.success) {\n        // Update the local state\n        setUserFlipbooks(prev => \n          prev.map(fb => \n            fb.PortfolioID === portfolioId \n              ? { ...fb, IsPreferred: isPreferred }\n              : fb\n          )\n        );\n        return true;\n      } else {\n        setError(response.error || 'Failed to update flipbook preference');\n        return false;\n      }\n    } catch (err) {\n      setError('An error occurred while updating flipbook preference.');\n      console.error('Error updating flipbook preference:', err);\n      return false;\n    }\n  }, []);\n\n  // Load flipbooks only when explicitly called (by authenticated users)\n  // useEffect(() => {\n  //   loadFlipbooks();\n  // }, [loadFlipbooks]);\n\n  return {\n    userFlipbooks,\n    loading,\n    error,\n    refreshFlipbooks,\n    createFlipbook,\n    copyInspirationFlipbook,\n    updateFlipbook,\n    deleteFlipbook,\n    setFlipbookPreferred\n  };\n};\n"], "mappings": "4IAAA;AAEA,OAASA,QAAQ,CAAEC,WAAW,KAAQ,OAAO,CAC7C,OAASC,UAAU,KAAQ,yBAAyB,CAoBpD,MAAO,MAAM,CAAAC,YAAY,CAAGA,CAAA,GAA0B,CACpD,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGL,QAAQ,CAAc,EAAE,CAAC,CACnE,KAAM,CAACM,OAAO,CAAEC,UAAU,CAAC,CAAGP,QAAQ,CAAU,KAAK,CAAC,CACtD,KAAM,CAACQ,KAAK,CAAEC,QAAQ,CAAC,CAAGT,QAAQ,CAAgB,IAAI,CAAC,CAEvD,KAAM,CAAAU,aAAa,CAAGT,WAAW,CAAC,SAAY,CAC5C,GAAI,CACFM,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd;AACA,KAAM,CAAAE,YAAY,CAAG,KAAM,CAAAT,UAAU,CAACU,cAAc,CAAC,CAAC,CAEtD,GAAID,YAAY,CAACE,OAAO,CAAE,CACxBR,gBAAgB,CAACM,YAAY,CAACG,IAAI,CAAC,CACrC,CAAC,IAAM,CACLC,OAAO,CAACP,KAAK,CAAC,gCAAgC,CAAEG,YAAY,CAACH,KAAK,CAAC,CACnEC,QAAQ,CAAC,kDAAkD,CAAC,CAC9D,CACF,CAAE,MAAOO,GAAG,CAAE,CACZP,QAAQ,CAAC,4DAA4D,CAAC,CACtEM,OAAO,CAACP,KAAK,CAAC,+BAA+B,CAAEQ,GAAG,CAAC,CACrD,CAAC,OAAS,CACRT,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAU,gBAAgB,CAAGhB,WAAW,CAAC,SAAY,CAC/C,KAAM,CAAAS,aAAa,CAAC,CAAC,CACvB,CAAC,CAAE,CAACA,aAAa,CAAC,CAAC,CAEnB,KAAM,CAAAQ,cAAc,CAAGjB,WAAW,CAAC,KAAO,CAAAkB,OAA8B,EAAuB,CAC7F,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAlB,UAAU,CAACmB,iBAAiB,CAACF,OAAO,CAAC,CAC5D,GAAIC,QAAQ,CAACP,OAAO,CAAE,CACpB;AACA,KAAM,CAAAI,gBAAgB,CAAC,CAAC,CACxB,MAAO,KAAI,CACb,CAAC,IAAM,CACLR,QAAQ,CAACW,QAAQ,CAACZ,KAAK,EAAI,2BAA2B,CAAC,CACvD,MAAO,MAAK,CACd,CACF,CAAE,MAAOQ,GAAG,CAAE,CACZP,QAAQ,CAAC,gDAAgD,CAAC,CAC1DM,OAAO,CAACP,KAAK,CAAC,0BAA0B,CAAEQ,GAAG,CAAC,CAC9C,MAAO,MAAK,CACd,CACF,CAAC,CAAE,CAACC,gBAAgB,CAAC,CAAC,CAEtB,KAAM,CAAAK,uBAAuB,CAAGrB,WAAW,CAAC,KAAO,CAAAkB,OAA+B,EAAuB,CACvG,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAlB,UAAU,CAACoB,uBAAuB,CAACH,OAAO,CAAC,CAClE,GAAIC,QAAQ,CAACP,OAAO,CAAE,CACpB;AACA,KAAM,CAAAI,gBAAgB,CAAC,CAAC,CACxB,MAAO,KAAI,CACb,CAAC,IAAM,CACLR,QAAQ,CAACW,QAAQ,CAACZ,KAAK,EAAI,qCAAqC,CAAC,CACjE,MAAO,MAAK,CACd,CACF,CAAE,MAAOQ,GAAG,CAAE,CACZP,QAAQ,CAAC,2DAA2D,CAAC,CACrEM,OAAO,CAACP,KAAK,CAAC,qCAAqC,CAAEQ,GAAG,CAAC,CACzD,MAAO,MAAK,CACd,CACF,CAAC,CAAE,CAACC,gBAAgB,CAAC,CAAC,CAEtB,KAAM,CAAAM,cAAc,CAAGtB,WAAW,CAAC,KAAO,CAAAkB,OAA8B,EAAuB,CAC7F,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAlB,UAAU,CAACqB,cAAc,CAACJ,OAAO,CAAC,CACzD,GAAIC,QAAQ,CAACP,OAAO,CAAE,CACpB;AACAR,gBAAgB,CAACmB,IAAI,EACnBA,IAAI,CAACC,GAAG,CAACC,EAAE,EACTA,EAAE,CAACC,WAAW,GAAKR,OAAO,CAACS,WAAW,CAAAC,aAAA,CAAAA,aAAA,IAC7BH,EAAE,MAAEI,cAAc,CAAEX,OAAO,CAACY,KAAK,GACtCL,EACN,CACF,CAAC,CACD,MAAO,KAAI,CACb,CAAC,IAAM,CACLjB,QAAQ,CAACW,QAAQ,CAACZ,KAAK,EAAI,2BAA2B,CAAC,CACvD,MAAO,MAAK,CACd,CACF,CAAE,MAAOQ,GAAG,CAAE,CACZP,QAAQ,CAAC,gDAAgD,CAAC,CAC1DM,OAAO,CAACP,KAAK,CAAC,0BAA0B,CAAEQ,GAAG,CAAC,CAC9C,MAAO,MAAK,CACd,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAgB,cAAc,CAAG/B,WAAW,CAAC,KAAO,CAAA2B,WAAmB,EAAuB,CAClF,GAAI,CACF,KAAM,CAAAR,QAAQ,CAAG,KAAM,CAAAlB,UAAU,CAAC8B,cAAc,CAACJ,WAAW,CAAC,CAC7D,GAAIR,QAAQ,CAACP,OAAO,CAAE,CACpB;AACAR,gBAAgB,CAACmB,IAAI,EAAIA,IAAI,CAACS,MAAM,CAACP,EAAE,EAAIA,EAAE,CAACC,WAAW,GAAKC,WAAW,CAAC,CAAC,CAC3E,MAAO,KAAI,CACb,CAAC,IAAM,CACLnB,QAAQ,CAACW,QAAQ,CAACZ,KAAK,EAAI,2BAA2B,CAAC,CACvD,MAAO,MAAK,CACd,CACF,CAAE,MAAOQ,GAAG,CAAE,CACZP,QAAQ,CAAC,gDAAgD,CAAC,CAC1DM,OAAO,CAACP,KAAK,CAAC,0BAA0B,CAAEQ,GAAG,CAAC,CAC9C,MAAO,MAAK,CACd,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAkB,oBAAoB,CAAGjC,WAAW,CAAC,MAAO2B,WAAmB,CAAEO,WAAoB,GAAuB,CAC9G,GAAI,CACF,KAAM,CAAAf,QAAQ,CAAG,KAAM,CAAAlB,UAAU,CAACgC,oBAAoB,CAACN,WAAW,CAAEO,WAAW,CAAC,CAChF,GAAIf,QAAQ,CAACP,OAAO,CAAE,CACpB;AACAR,gBAAgB,CAACmB,IAAI,EACnBA,IAAI,CAACC,GAAG,CAACC,EAAE,EACTA,EAAE,CAACC,WAAW,GAAKC,WAAW,CAAAC,aAAA,CAAAA,aAAA,IACrBH,EAAE,MAAEU,WAAW,CAAED,WAAW,GACjCT,EACN,CACF,CAAC,CACD,MAAO,KAAI,CACb,CAAC,IAAM,CACLjB,QAAQ,CAACW,QAAQ,CAACZ,KAAK,EAAI,sCAAsC,CAAC,CAClE,MAAO,MAAK,CACd,CACF,CAAE,MAAOQ,GAAG,CAAE,CACZP,QAAQ,CAAC,uDAAuD,CAAC,CACjEM,OAAO,CAACP,KAAK,CAAC,qCAAqC,CAAEQ,GAAG,CAAC,CACzD,MAAO,MAAK,CACd,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA;AACA;AACA;AAEA,MAAO,CACLZ,aAAa,CACbE,OAAO,CACPE,KAAK,CACLS,gBAAgB,CAChBC,cAAc,CACdI,uBAAuB,CACvBC,cAAc,CACdS,cAAc,CACdE,oBACF,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}