<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flipbook Editor - Design Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f8f9fa;
            height: 100vh;
            display: flex;
        }

        .sidebar {
            width: 160px;
            background: #2c3e50;
            color: white;
            display: flex;
            flex-direction: column;
        }

        .sidebar-section {
            border-bottom: 1px solid #34495e;
        }

        .toolbar-icons {
            padding: 15px 8px;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .icon-btn {
            width: 40px;
            height: 40px;
            background: #34495e;
            color: white;
            border: none;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
        }

        .click-drag-section {
            background: #34495e;
            color: white;
            text-align: center;
            padding: 12px 8px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            line-height: 1.2;
        }

        .pages-section {
            flex: 1;
            padding: 15px 8px;
        }

        .page-thumbnail {
            background: white;
            margin-bottom: 8px;
            border-radius: 4px;
            aspect-ratio: 8.5 / 11;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .page-thumbnail.active {
            border: 2px solid #8B5DBA;
        }

        .add-page-section {
            padding: 12px 8px;
            text-align: center;
        }

        .add-page-btn {
            width: 100%;
            background: #27ae60;
            color: white;
            border: none;
            padding: 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            text-transform: uppercase;
        }

        .main-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .page-container {
            width: 100%;
            max-width: 700px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #e1e5e9;
        }

        .page-header {
            text-align: center;
            padding: 20px 0;
            background: #ffffff;
            border-bottom: 1px solid #e0e0e0;
        }

        .pages-title {
            margin: 0 0 15px 0;
            font-size: 14px;
            font-weight: 400;
            color: #999999;
            letter-spacing: 3px;
            text-transform: uppercase;
        }

        .flipbook-title {
            font-size: 24px;
            font-weight: 600;
            color: #333333;
        }

        .page-content {
            aspect-ratio: 8.5 / 11;
            position: relative;
            background: white;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .decorative-border-frame {
            position: absolute;
            top: 30px;
            left: 30px;
            right: 30px;
            bottom: 30px;
            border: 3px solid transparent;
            background:
                linear-gradient(white, white) padding-box,
                repeating-linear-gradient(
                    0deg,
                    #d4a574 0px,
                    #d4a574 2px,
                    #8b4513 2px,
                    #8b4513 4px,
                    #cd853f 4px,
                    #cd853f 6px,
                    #daa520 6px,
                    #daa520 8px,
                    #b8860b 8px,
                    #b8860b 10px,
                    #d2691e 10px,
                    #d2691e 12px
                ) border-box;
            border-radius: 8px;
            pointer-events: none;
            z-index: 1;
        }

        .decorative-border-frame::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: repeating-linear-gradient(
                90deg,
                #d4a574 0px,
                #d4a574 2px,
                #8b4513 2px,
                #8b4513 4px,
                #cd853f 4px,
                #cd853f 6px,
                #daa520 6px,
                #daa520 8px,
                #b8860b 8px,
                #b8860b 10px,
                #d2691e 10px,
                #d2691e 12px
            );
            border-radius: 8px;
            z-index: -1;
        }

        .center-content {
            text-align: center;
            z-index: 5;
            position: relative;
        }

        .main-text {
            font-size: 48px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 4px;
        }

        .profession-title {
            font-size: 18px;
            font-weight: 400;
            color: #2c3e50;
            letter-spacing: 6px;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="toolbar-icons sidebar-section">
            <button class="icon-btn">📌</button>
            <button class="icon-btn">A</button>
            <button class="icon-btn">🖼️</button>
            <button class="icon-btn">⬛</button>
            <button class="icon-btn">📏</button>
            <button class="icon-btn">✂️</button>
        </div>
        
        <div class="click-drag-section">
            CLICK & DRAG<br/>TO REORDER PAGES
        </div>
        
        <div class="pages-section">
            <div class="page-thumbnail active">Front Cover</div>
            <div class="page-thumbnail">Page 2</div>
            <div class="page-thumbnail">Back Cover</div>
        </div>
        
        <div class="add-page-section">
            <button class="add-page-btn">+ ADD NEW PAGE</button>
        </div>
    </div>

    <div class="main-content">
        <div class="page-container">
            <div class="page-header">
                <h1 class="pages-title">PAGES</h1>
                <div class="flipbook-title">Bhanu</div>
            </div>
            
            <div class="page-content">
                <div class="decorative-border-frame"></div>

                <div class="center-content">
                    <div class="main-text">
                        ZARA IRUM
                    </div>
                    <div class="profession-title">
                        ARCHITECT
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
