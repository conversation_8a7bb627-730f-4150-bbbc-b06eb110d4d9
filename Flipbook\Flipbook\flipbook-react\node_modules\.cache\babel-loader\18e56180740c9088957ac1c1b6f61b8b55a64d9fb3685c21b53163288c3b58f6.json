{"ast": null, "code": "import React,{useState}from'react';import TextEditor from'../TextEditor/TextEditor';import ImageLibrary from'../ImageLibrary/ImageLibrary';import'./ResumePage.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ResumePage=_ref=>{let{sections,profileImage,onSectionChange,onSectionDelete,onSectionReorder,onProfileImageChange,onTextEditorFocus,onTextEditorBlur,pendingFormatting,onFormattingApplied,onPrevious,onNext}=_ref;const[activeEditor,setActiveEditor]=useState(null);const[showImageLibrary,setShowImageLibrary]=useState(false);const[draggedSection,setDraggedSection]=useState(null);const[dragOverColumn,setDragOverColumn]=useState(null);const[dragOverIndex,setDragOverIndex]=useState(null);const handleEditorFocus=sectionId=>{setActiveEditor(sectionId);};const handleEditorBlur=()=>{setActiveEditor(null);};const handleImageUploadClick=()=>{setShowImageLibrary(true);};const handleImageSelect=imageUrl=>{onProfileImageChange(imageUrl);setShowImageLibrary(false);};// Default sections if none provided\nconst defaultSections=[{id:'objective',title:'Objective',content:'To find a position as a junior designer in a residential architectural firm, preferably located in New York City.'},{id:'professional',title:'Professional Interests',content:'As a 2016 summer intern at SOM, I have focused on office layouts, boost strength and color desk optimization.'},{id:'personal',title:'Personal Statement',content:'Previously, I was a summer intern with Cesar Pelli, where we focused on environmental design.'},{id:'education',title:'Education',content:'Bachelor Degree - London, August 2013 to Present\\nInstitute, Graphics and Visualisation Environment Administrative\\nAssembly prepared project consultation document, Review shop drawings and project materials for performance compliance.'},{id:'experience',title:'Experience',content:'The Architecture Foundation - London, February 2008 - January 2009\\nInterns, Graphics and Visualisation Recreations Administrative office administration'},{id:'skills',title:'Skill Proficiencies',content:'CAD maintenance\\nPublic event and exhibition setup\\nTeam management and scheduling'}];const currentSections=sections.length>0?sections:defaultSections;// Split sections into left and right columns\nconst leftSections=currentSections.slice(0,3);const rightSections=currentSections.slice(3);// Drag and Drop Handlers\nconst handleDragStart=(e,sectionId)=>{setDraggedSection(sectionId);e.dataTransfer.effectAllowed='move';e.dataTransfer.setData('text/plain',sectionId);};const handleDragEnd=()=>{setDraggedSection(null);setDragOverColumn(null);setDragOverIndex(null);};const handleDragOver=(e,column,index)=>{e.preventDefault();e.dataTransfer.dropEffect='move';setDragOverColumn(column);if(index!==undefined){setDragOverIndex(index);}};const handleDragLeave=e=>{const rect=e.currentTarget.getBoundingClientRect();const x=e.clientX;const y=e.clientY;// Check if mouse is outside the drop zone\nif(x<rect.left||x>rect.right||y<rect.top||y>rect.bottom){setDragOverColumn(null);setDragOverIndex(null);}};const handleDrop=(e,targetColumn,targetIndex)=>{e.preventDefault();if(!draggedSection)return;const draggedSectionData=currentSections.find(s=>s.id===draggedSection);if(!draggedSectionData)return;const newSections=[...currentSections];const draggedIndex=newSections.findIndex(s=>s.id===draggedSection);// Remove dragged section from current position\nnewSections.splice(draggedIndex,1);// Calculate new index based on target column and position\nlet newIndex;if(targetColumn==='left'){newIndex=targetIndex!==undefined?targetIndex:0;newIndex=Math.min(newIndex,2);// Max 3 items in left column\n}else{newIndex=targetIndex!==undefined?targetIndex+3:3;newIndex=Math.max(newIndex,3);// Start from index 3 for right column\n}// Insert section at new position\nnewSections.splice(newIndex,0,draggedSectionData);onSectionReorder(newSections);setDraggedSection(null);setDragOverColumn(null);setDragOverIndex(null);};return/*#__PURE__*/_jsxs(\"div\",{className:\"resume-page\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"resume-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"left-column\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"name-section\",children:/*#__PURE__*/_jsx(\"h1\",{children:\"Zara Irum\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"image-section\",children:/*#__PURE__*/_jsx(\"div\",{className:\"image-container\",onClick:handleImageUploadClick,children:profileImage?/*#__PURE__*/_jsxs(\"div\",{className:\"profile-image\",children:[/*#__PURE__*/_jsx(\"img\",{src:profileImage,alt:\"Profile\"}),/*#__PURE__*/_jsx(\"div\",{className:\"image-overlay\",children:/*#__PURE__*/_jsx(\"span\",{children:\"Change Image\"})})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"image-placeholder\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"placeholder-icon\",children:\"\\uD83D\\uDDBC\\uFE0F\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Click to add image\"})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"left-sections \".concat(dragOverColumn==='left'?'drag-over':''),onDragOver:e=>handleDragOver(e,'left'),onDragLeave:handleDragLeave,onDrop:e=>handleDrop(e,'left'),children:[leftSections.map((section,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"resume-section \".concat(draggedSection===section.id?'dragging':'',\" \").concat(dragOverColumn==='left'&&dragOverIndex===index?'drag-over-item':''),\"data-section-id\":section.id,draggable:true,onDragStart:e=>handleDragStart(e,section.id),onDragEnd:handleDragEnd,onDragOver:e=>handleDragOver(e,'left',index),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"section-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"drag-handle\",title:\"Drag to reorder\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\u22EE\\u22EE\"})}),/*#__PURE__*/_jsx(\"h3\",{children:section.title}),/*#__PURE__*/_jsx(\"button\",{className:\"delete-section-btn\",onClick:()=>onSectionDelete(section.id),title:\"Delete Section\",children:\"\\uD83D\\uDDD1\\uFE0F\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"section-content\",children:/*#__PURE__*/_jsx(TextEditor,{value:section.content,onChange:content=>onSectionChange(section.id,content),placeholder:\"Enter \".concat(section.title.toLowerCase(),\" details...\"),isActive:activeEditor===section.id,onFocus:()=>{handleEditorFocus(section.id);// Notify parent about text editor focus for common toolbar\nif(onTextEditorFocus){const editorElement=document.querySelector(\"[data-section-id=\\\"\".concat(section.id,\"\\\"] .text-editor\"));if(editorElement){onTextEditorFocus(editorElement,{fontFamily:'Arial',fontSize:'14',bold:false,italic:false,underline:false,textAlign:'left',color:'#000000',backgroundColor:'transparent',lineHeight:'1.4',letterSpacing:'0'});}}},onBlur:event=>{handleEditorBlur();onTextEditorBlur===null||onTextEditorBlur===void 0?void 0:onTextEditorBlur(event);},pendingFormatting:pendingFormatting,onFormattingApplied:onFormattingApplied,className:\"resume-text-editor\"})})]},section.id)),dragOverColumn==='left'&&/*#__PURE__*/_jsx(\"div\",{className:\"drop-zone-indicator\",children:\"Drop section here\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"right-column\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"right-header\",children:/*#__PURE__*/_jsx(\"h2\",{children:\"Resume Highlights\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"right-sections \".concat(dragOverColumn==='right'?'drag-over':''),onDragOver:e=>handleDragOver(e,'right'),onDragLeave:handleDragLeave,onDrop:e=>handleDrop(e,'right'),children:[rightSections.map((section,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"resume-section \".concat(draggedSection===section.id?'dragging':'',\" \").concat(dragOverColumn==='right'&&dragOverIndex===index?'drag-over-item':''),\"data-section-id\":section.id,draggable:true,onDragStart:e=>handleDragStart(e,section.id),onDragEnd:handleDragEnd,onDragOver:e=>handleDragOver(e,'right',index),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"section-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"drag-handle\",title:\"Drag to reorder\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\u22EE\\u22EE\"})}),/*#__PURE__*/_jsx(\"h3\",{children:section.title}),/*#__PURE__*/_jsx(\"button\",{className:\"delete-section-btn\",onClick:()=>onSectionDelete(section.id),title:\"Delete Section\",children:\"\\uD83D\\uDDD1\\uFE0F\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"section-content\",children:/*#__PURE__*/_jsx(TextEditor,{value:section.content,onChange:content=>onSectionChange(section.id,content),placeholder:\"Enter \".concat(section.title.toLowerCase(),\" information...\"),isActive:activeEditor===section.id,onFocus:()=>{handleEditorFocus(section.id);// Notify parent about text editor focus for common toolbar\nif(onTextEditorFocus){const editorElement=document.querySelector(\"[data-section-id=\\\"\".concat(section.id,\"\\\"] .text-editor\"));if(editorElement){onTextEditorFocus(editorElement,{fontFamily:'Arial',fontSize:'14',bold:false,italic:false,underline:false,textAlign:'left',color:'#000000',backgroundColor:'transparent',lineHeight:'1.4',letterSpacing:'0'});}}},onBlur:event=>{handleEditorBlur();onTextEditorBlur===null||onTextEditorBlur===void 0?void 0:onTextEditorBlur(event);},pendingFormatting:pendingFormatting,onFormattingApplied:onFormattingApplied,className:\"resume-text-editor\"})})]},section.id)),dragOverColumn==='right'&&/*#__PURE__*/_jsx(\"div\",{className:\"drop-zone-indicator\",children:\"Drop section here\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"navigation-controls\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"nav-btn prev-btn\",onClick:onPrevious,title:\"Previous Page\",children:/*#__PURE__*/_jsx(\"span\",{className:\"arrow-icon\",children:\"\\u2190\"})}),/*#__PURE__*/_jsx(\"button\",{className:\"nav-btn next-btn\",onClick:onNext,title:\"Next Page\",children:/*#__PURE__*/_jsx(\"span\",{className:\"arrow-icon\",children:\"\\u2192\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"add-section-controls\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"add-left-section-btn\",title:\"Add Left Section\",children:\"+ ADD LEFT SECTION\"}),/*#__PURE__*/_jsx(\"button\",{className:\"add-right-section-btn\",title:\"Add Right Section\",children:\"+ ADD RIGHT SECTION\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"background-pattern\"}),/*#__PURE__*/_jsx(ImageLibrary,{isOpen:showImageLibrary,onClose:()=>setShowImageLibrary(false),onSelectImage:handleImageSelect})]});};export default ResumePage;", "map": {"version": 3, "names": ["React", "useState", "TextEditor", "ImageLibrary", "jsx", "_jsx", "jsxs", "_jsxs", "ResumePage", "_ref", "sections", "profileImage", "onSectionChange", "onSectionDelete", "onSectionReorder", "onProfileImageChange", "onTextEditorFocus", "onTextEditorBlur", "pendingFormatting", "onFormattingApplied", "onPrevious", "onNext", "activeEditor", "setActiveEditor", "showImageLibrary", "setShowImageLibrary", "draggedSection", "setDraggedSection", "dragOverColumn", "setDragOverColumn", "dragOverIndex", "setDragOverIndex", "handleEditorFocus", "sectionId", "handleEditorBlur", "handleImageUploadClick", "handleImageSelect", "imageUrl", "defaultSections", "id", "title", "content", "currentSections", "length", "leftSections", "slice", "rightSections", "handleDragStart", "e", "dataTransfer", "effectAllowed", "setData", "handleDragEnd", "handleDragOver", "column", "index", "preventDefault", "dropEffect", "undefined", "handleDragLeave", "rect", "currentTarget", "getBoundingClientRect", "x", "clientX", "y", "clientY", "left", "right", "top", "bottom", "handleDrop", "targetColumn", "targetIndex", "draggedSectionData", "find", "s", "newSections", "draggedIndex", "findIndex", "splice", "newIndex", "Math", "min", "max", "className", "children", "onClick", "src", "alt", "concat", "onDragOver", "onDragLeave", "onDrop", "map", "section", "draggable", "onDragStart", "onDragEnd", "value", "onChange", "placeholder", "toLowerCase", "isActive", "onFocus", "editor<PERSON><PERSON>", "document", "querySelector", "fontFamily", "fontSize", "bold", "italic", "underline", "textAlign", "color", "backgroundColor", "lineHeight", "letterSpacing", "onBlur", "event", "isOpen", "onClose", "onSelectImage"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/ResumePage/ResumePage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport TextEditor from '../TextEditor/TextEditor';\r\nimport ImageLibrary from '../ImageLibrary/ImageLibrary';\r\nimport './ResumePage.css';\r\n\r\ninterface ResumeSection {\r\n  id: string;\r\n  title: string;\r\n  content: string;\r\n}\r\n\r\ninterface ResumePageProps {\r\n  sections: ResumeSection[];\r\n  profileImage: string;\r\n  onSectionChange: (sectionId: string, content: string) => void;\r\n  onSectionDelete: (sectionId: string) => void;\r\n  onSectionReorder: (sections: ResumeSection[]) => void;\r\n  onProfileImageChange: (imageUrl: string) => void;\r\n  onTextEditorFocus?: (editorRef: HTMLDivElement, formatting: any) => void;\r\n  onTextEditorBlur?: (event?: React.FocusEvent) => void;\r\n  pendingFormatting?: any;\r\n  onFormattingApplied?: () => void;\r\n  onPrevious: () => void;\r\n  onNext: () => void;\r\n}\r\n\r\nconst ResumePage: React.FC<ResumePageProps> = ({\r\n  sections,\r\n  profileImage,\r\n  onSectionChange,\r\n  onSectionDelete,\r\n  onSectionReorder,\r\n  onProfileImageChange,\r\n  onTextEditorFocus,\r\n  onTextEditorBlur,\r\n  pendingFormatting,\r\n  onFormattingApplied,\r\n  onPrevious,\r\n  onNext\r\n}) => {\r\n  const [activeEditor, setActiveEditor] = useState<string | null>(null);\r\n  const [showImageLibrary, setShowImageLibrary] = useState(false);\r\n  const [draggedSection, setDraggedSection] = useState<string | null>(null);\r\n  const [dragOverColumn, setDragOverColumn] = useState<'left' | 'right' | null>(null);\r\n  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);\r\n\r\n  const handleEditorFocus = (sectionId: string) => {\r\n    setActiveEditor(sectionId);\r\n  };\r\n\r\n  const handleEditorBlur = () => {\r\n    setActiveEditor(null);\r\n  };\r\n\r\n  const handleImageUploadClick = () => {\r\n    setShowImageLibrary(true);\r\n  };\r\n\r\n  const handleImageSelect = (imageUrl: string) => {\r\n    onProfileImageChange(imageUrl);\r\n    setShowImageLibrary(false);\r\n  };\r\n\r\n  // Default sections if none provided\r\n  const defaultSections: ResumeSection[] = [\r\n    { id: 'objective', title: 'Objective', content: 'To find a position as a junior designer in a residential architectural firm, preferably located in New York City.' },\r\n    { id: 'professional', title: 'Professional Interests', content: 'As a 2016 summer intern at SOM, I have focused on office layouts, boost strength and color desk optimization.' },\r\n    { id: 'personal', title: 'Personal Statement', content: 'Previously, I was a summer intern with Cesar Pelli, where we focused on environmental design.' },\r\n    { id: 'education', title: 'Education', content: 'Bachelor Degree - London, August 2013 to Present\\nInstitute, Graphics and Visualisation Environment Administrative\\nAssembly prepared project consultation document, Review shop drawings and project materials for performance compliance.' },\r\n    { id: 'experience', title: 'Experience', content: 'The Architecture Foundation - London, February 2008 - January 2009\\nInterns, Graphics and Visualisation Recreations Administrative office administration' },\r\n    { id: 'skills', title: 'Skill Proficiencies', content: 'CAD maintenance\\nPublic event and exhibition setup\\nTeam management and scheduling' }\r\n  ];\r\n\r\n  const currentSections = sections.length > 0 ? sections : defaultSections;\r\n\r\n  // Split sections into left and right columns\r\n  const leftSections = currentSections.slice(0, 3);\r\n  const rightSections = currentSections.slice(3);\r\n\r\n  // Drag and Drop Handlers\r\n  const handleDragStart = (e: React.DragEvent, sectionId: string) => {\r\n    setDraggedSection(sectionId);\r\n    e.dataTransfer.effectAllowed = 'move';\r\n    e.dataTransfer.setData('text/plain', sectionId);\r\n  };\r\n\r\n  const handleDragEnd = () => {\r\n    setDraggedSection(null);\r\n    setDragOverColumn(null);\r\n    setDragOverIndex(null);\r\n  };\r\n\r\n  const handleDragOver = (e: React.DragEvent, column: 'left' | 'right', index?: number) => {\r\n    e.preventDefault();\r\n    e.dataTransfer.dropEffect = 'move';\r\n    setDragOverColumn(column);\r\n    if (index !== undefined) {\r\n      setDragOverIndex(index);\r\n    }\r\n  };\r\n\r\n  const handleDragLeave = (e: React.DragEvent) => {\r\n    const rect = e.currentTarget.getBoundingClientRect();\r\n    const x = e.clientX;\r\n    const y = e.clientY;\r\n    \r\n    // Check if mouse is outside the drop zone\r\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\r\n      setDragOverColumn(null);\r\n      setDragOverIndex(null);\r\n    }\r\n  };\r\n\r\n  const handleDrop = (e: React.DragEvent, targetColumn: 'left' | 'right', targetIndex?: number) => {\r\n    e.preventDefault();\r\n    \r\n    if (!draggedSection) return;\r\n    \r\n    const draggedSectionData = currentSections.find(s => s.id === draggedSection);\r\n    if (!draggedSectionData) return;\r\n\r\n    const newSections = [...currentSections];\r\n    const draggedIndex = newSections.findIndex(s => s.id === draggedSection);\r\n    \r\n    // Remove dragged section from current position\r\n    newSections.splice(draggedIndex, 1);\r\n    \r\n    // Calculate new index based on target column and position\r\n    let newIndex: number;\r\n    if (targetColumn === 'left') {\r\n      newIndex = targetIndex !== undefined ? targetIndex : 0;\r\n      newIndex = Math.min(newIndex, 2); // Max 3 items in left column\r\n    } else {\r\n      newIndex = targetIndex !== undefined ? targetIndex + 3 : 3;\r\n      newIndex = Math.max(newIndex, 3); // Start from index 3 for right column\r\n    }\r\n    \r\n    // Insert section at new position\r\n    newSections.splice(newIndex, 0, draggedSectionData);\r\n    \r\n    onSectionReorder(newSections);\r\n    setDraggedSection(null);\r\n    setDragOverColumn(null);\r\n    setDragOverIndex(null);\r\n  };\r\n\r\n  return (\r\n    <div className=\"resume-page\">\r\n      <div className=\"resume-content\">\r\n        {/* Left Column */}\r\n        <div className=\"left-column\">\r\n          <div className=\"name-section\">\r\n            <h1>Zara Irum</h1>\r\n          </div>\r\n\r\n          {/* Profile Image */}\r\n          <div className=\"image-section\">\r\n            <div className=\"image-container\" onClick={handleImageUploadClick}>\r\n              {profileImage ? (\r\n                <div className=\"profile-image\">\r\n                  <img src={profileImage} alt=\"Profile\" />\r\n                  <div className=\"image-overlay\">\r\n                    <span>Change Image</span>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"image-placeholder\">\r\n                  <div className=\"placeholder-icon\">🖼️</div>\r\n                  <p>Click to add image</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Left Column Sections */}\r\n          <div \r\n            className={`left-sections ${\r\n              dragOverColumn === 'left' ? 'drag-over' : ''\r\n            }`}\r\n            onDragOver={(e) => handleDragOver(e, 'left')}\r\n            onDragLeave={handleDragLeave}\r\n            onDrop={(e) => handleDrop(e, 'left')}\r\n          >\r\n            {leftSections.map((section, index) => (\r\n              <div \r\n                key={section.id} \r\n                className={`resume-section ${\r\n                  draggedSection === section.id ? 'dragging' : ''\r\n                } ${\r\n                  dragOverColumn === 'left' && dragOverIndex === index ? 'drag-over-item' : ''\r\n                }`}\r\n                data-section-id={section.id}\r\n                draggable\r\n                onDragStart={(e) => handleDragStart(e, section.id)}\r\n                onDragEnd={handleDragEnd}\r\n                onDragOver={(e) => handleDragOver(e, 'left', index)}\r\n              >\r\n                <div className=\"section-header\">\r\n                  <div className=\"drag-handle\" title=\"Drag to reorder\">\r\n                    <span>⋮⋮</span>\r\n                  </div>\r\n                  <h3>{section.title}</h3>\r\n                  <button\r\n                    className=\"delete-section-btn\"\r\n                    onClick={() => onSectionDelete(section.id)}\r\n                    title=\"Delete Section\"\r\n                  >\r\n                    🗑️\r\n                  </button>\r\n                </div>\r\n                <div className=\"section-content\">\r\n                  <TextEditor\r\n                    value={section.content}\r\n                    onChange={(content) => onSectionChange(section.id, content)}\r\n                    placeholder={`Enter ${section.title.toLowerCase()} details...`}\r\n                    isActive={activeEditor === section.id}\r\n                    onFocus={() => {\r\n                      handleEditorFocus(section.id);\r\n                      // Notify parent about text editor focus for common toolbar\r\n                      if (onTextEditorFocus) {\r\n                        const editorElement = document.querySelector(`[data-section-id=\"${section.id}\"] .text-editor`) as HTMLDivElement;\r\n                        if (editorElement) {\r\n                          onTextEditorFocus(editorElement, {\r\n                            fontFamily: 'Arial',\r\n                            fontSize: '14',\r\n                            bold: false,\r\n                            italic: false,\r\n                            underline: false,\r\n                            textAlign: 'left',\r\n                            color: '#000000',\r\n                            backgroundColor: 'transparent',\r\n                            lineHeight: '1.4',\r\n                            letterSpacing: '0'\r\n                          });\r\n                        }\r\n                      }\r\n                    }}\r\n                    onBlur={(event?: React.FocusEvent) => {\r\n                      handleEditorBlur();\r\n                      onTextEditorBlur?.(event);\r\n                    }}\r\n                    pendingFormatting={pendingFormatting}\r\n                    onFormattingApplied={onFormattingApplied}\r\n                    className=\"resume-text-editor\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            ))}\r\n            {/* Drop zone indicator for left column */}\r\n            {dragOverColumn === 'left' && (\r\n              <div className=\"drop-zone-indicator\">\r\n                Drop section here\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Right Column */}\r\n        <div className=\"right-column\">\r\n          <div className=\"right-header\">\r\n            <h2>Resume Highlights</h2>\r\n          </div>\r\n\r\n          {/* Right Column Sections */}\r\n          <div \r\n            className={`right-sections ${\r\n              dragOverColumn === 'right' ? 'drag-over' : ''\r\n            }`}\r\n            onDragOver={(e) => handleDragOver(e, 'right')}\r\n            onDragLeave={handleDragLeave}\r\n            onDrop={(e) => handleDrop(e, 'right')}\r\n          >\r\n            {rightSections.map((section, index) => (\r\n              <div \r\n                key={section.id} \r\n                className={`resume-section ${\r\n                  draggedSection === section.id ? 'dragging' : ''\r\n                } ${\r\n                  dragOverColumn === 'right' && dragOverIndex === index ? 'drag-over-item' : ''\r\n                }`}\r\n                data-section-id={section.id}\r\n                draggable\r\n                onDragStart={(e) => handleDragStart(e, section.id)}\r\n                onDragEnd={handleDragEnd}\r\n                onDragOver={(e) => handleDragOver(e, 'right', index)}\r\n              >\r\n                <div className=\"section-header\">\r\n                  <div className=\"drag-handle\" title=\"Drag to reorder\">\r\n                    <span>⋮⋮</span>\r\n                  </div>\r\n                  <h3>{section.title}</h3>\r\n                  <button\r\n                    className=\"delete-section-btn\"\r\n                    onClick={() => onSectionDelete(section.id)}\r\n                    title=\"Delete Section\"\r\n                  >\r\n                    🗑️\r\n                  </button>\r\n                </div>\r\n                <div className=\"section-content\">\r\n                  <TextEditor\r\n                    value={section.content}\r\n                    onChange={(content) => onSectionChange(section.id, content)}\r\n                    placeholder={`Enter ${section.title.toLowerCase()} information...`}\r\n                    isActive={activeEditor === section.id}\r\n                    onFocus={() => {\r\n                      handleEditorFocus(section.id);\r\n                      // Notify parent about text editor focus for common toolbar\r\n                      if (onTextEditorFocus) {\r\n                        const editorElement = document.querySelector(`[data-section-id=\"${section.id}\"] .text-editor`) as HTMLDivElement;\r\n                        if (editorElement) {\r\n                          onTextEditorFocus(editorElement, {\r\n                            fontFamily: 'Arial',\r\n                            fontSize: '14',\r\n                            bold: false,\r\n                            italic: false,\r\n                            underline: false,\r\n                            textAlign: 'left',\r\n                            color: '#000000',\r\n                            backgroundColor: 'transparent',\r\n                            lineHeight: '1.4',\r\n                            letterSpacing: '0'\r\n                          });\r\n                        }\r\n                      }\r\n                    }\r\n                  }\r\n                    onBlur={(event?: React.FocusEvent) => {\r\n                      handleEditorBlur();\r\n                      onTextEditorBlur?.(event);\r\n                    }}\r\n                    pendingFormatting={pendingFormatting}\r\n                    onFormattingApplied={onFormattingApplied}\r\n                    className=\"resume-text-editor\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            ))}\r\n            {/* Drop zone indicator for right column */}\r\n            {dragOverColumn === 'right' && (\r\n              <div className=\"drop-zone-indicator\">\r\n                Drop section here\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Navigation Controls */}\r\n        <div className=\"navigation-controls\">\r\n          <button className=\"nav-btn prev-btn\" onClick={onPrevious} title=\"Previous Page\">\r\n            <span className=\"arrow-icon\">←</span>\r\n          </button>\r\n          <button className=\"nav-btn next-btn\" onClick={onNext} title=\"Next Page\">\r\n            <span className=\"arrow-icon\">→</span>\r\n          </button>\r\n        </div>\r\n\r\n        {/* Add Section Button */}\r\n        <div className=\"add-section-controls\">\r\n          <button className=\"add-left-section-btn\" title=\"Add Left Section\">\r\n            + ADD LEFT SECTION\r\n          </button>\r\n          <button className=\"add-right-section-btn\" title=\"Add Right Section\">\r\n            + ADD RIGHT SECTION\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Background Pattern */}\r\n      <div className=\"background-pattern\"></div>\r\n\r\n      {/* Image Library Modal */}\r\n      <ImageLibrary\r\n        isOpen={showImageLibrary}\r\n        onClose={() => setShowImageLibrary(false)}\r\n        onSelectImage={handleImageSelect}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ResumePage;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,UAAU,KAAM,0BAA0B,CACjD,MAAO,CAAAC,YAAY,KAAM,8BAA8B,CACvD,MAAO,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAuB1B,KAAM,CAAAC,UAAqC,CAAGC,IAAA,EAaxC,IAbyC,CAC7CC,QAAQ,CACRC,YAAY,CACZC,eAAe,CACfC,eAAe,CACfC,gBAAgB,CAChBC,oBAAoB,CACpBC,iBAAiB,CACjBC,gBAAgB,CAChBC,iBAAiB,CACjBC,mBAAmB,CACnBC,UAAU,CACVC,MACF,CAAC,CAAAZ,IAAA,CACC,KAAM,CAACa,YAAY,CAAEC,eAAe,CAAC,CAAGtB,QAAQ,CAAgB,IAAI,CAAC,CACrE,KAAM,CAACuB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACyB,cAAc,CAAEC,iBAAiB,CAAC,CAAG1B,QAAQ,CAAgB,IAAI,CAAC,CACzE,KAAM,CAAC2B,cAAc,CAAEC,iBAAiB,CAAC,CAAG5B,QAAQ,CAA0B,IAAI,CAAC,CACnF,KAAM,CAAC6B,aAAa,CAAEC,gBAAgB,CAAC,CAAG9B,QAAQ,CAAgB,IAAI,CAAC,CAEvE,KAAM,CAAA+B,iBAAiB,CAAIC,SAAiB,EAAK,CAC/CV,eAAe,CAACU,SAAS,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7BX,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED,KAAM,CAAAY,sBAAsB,CAAGA,CAAA,GAAM,CACnCV,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAW,iBAAiB,CAAIC,QAAgB,EAAK,CAC9CtB,oBAAoB,CAACsB,QAAQ,CAAC,CAC9BZ,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAa,eAAgC,CAAG,CACvC,CAAEC,EAAE,CAAE,WAAW,CAAEC,KAAK,CAAE,WAAW,CAAEC,OAAO,CAAE,mHAAoH,CAAC,CACrK,CAAEF,EAAE,CAAE,cAAc,CAAEC,KAAK,CAAE,wBAAwB,CAAEC,OAAO,CAAE,+GAAgH,CAAC,CACjL,CAAEF,EAAE,CAAE,UAAU,CAAEC,KAAK,CAAE,oBAAoB,CAAEC,OAAO,CAAE,+FAAgG,CAAC,CACzJ,CAAEF,EAAE,CAAE,WAAW,CAAEC,KAAK,CAAE,WAAW,CAAEC,OAAO,CAAE,6OAA8O,CAAC,CAC/R,CAAEF,EAAE,CAAE,YAAY,CAAEC,KAAK,CAAE,YAAY,CAAEC,OAAO,CAAE,0JAA2J,CAAC,CAC9M,CAAEF,EAAE,CAAE,QAAQ,CAAEC,KAAK,CAAE,qBAAqB,CAAEC,OAAO,CAAE,oFAAqF,CAAC,CAC9I,CAED,KAAM,CAAAC,eAAe,CAAGhC,QAAQ,CAACiC,MAAM,CAAG,CAAC,CAAGjC,QAAQ,CAAG4B,eAAe,CAExE;AACA,KAAM,CAAAM,YAAY,CAAGF,eAAe,CAACG,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAChD,KAAM,CAAAC,aAAa,CAAGJ,eAAe,CAACG,KAAK,CAAC,CAAC,CAAC,CAE9C;AACA,KAAM,CAAAE,eAAe,CAAGA,CAACC,CAAkB,CAAEf,SAAiB,GAAK,CACjEN,iBAAiB,CAACM,SAAS,CAAC,CAC5Be,CAAC,CAACC,YAAY,CAACC,aAAa,CAAG,MAAM,CACrCF,CAAC,CAACC,YAAY,CAACE,OAAO,CAAC,YAAY,CAAElB,SAAS,CAAC,CACjD,CAAC,CAED,KAAM,CAAAmB,aAAa,CAAGA,CAAA,GAAM,CAC1BzB,iBAAiB,CAAC,IAAI,CAAC,CACvBE,iBAAiB,CAAC,IAAI,CAAC,CACvBE,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED,KAAM,CAAAsB,cAAc,CAAGA,CAACL,CAAkB,CAAEM,MAAwB,CAAEC,KAAc,GAAK,CACvFP,CAAC,CAACQ,cAAc,CAAC,CAAC,CAClBR,CAAC,CAACC,YAAY,CAACQ,UAAU,CAAG,MAAM,CAClC5B,iBAAiB,CAACyB,MAAM,CAAC,CACzB,GAAIC,KAAK,GAAKG,SAAS,CAAE,CACvB3B,gBAAgB,CAACwB,KAAK,CAAC,CACzB,CACF,CAAC,CAED,KAAM,CAAAI,eAAe,CAAIX,CAAkB,EAAK,CAC9C,KAAM,CAAAY,IAAI,CAAGZ,CAAC,CAACa,aAAa,CAACC,qBAAqB,CAAC,CAAC,CACpD,KAAM,CAAAC,CAAC,CAAGf,CAAC,CAACgB,OAAO,CACnB,KAAM,CAAAC,CAAC,CAAGjB,CAAC,CAACkB,OAAO,CAEnB;AACA,GAAIH,CAAC,CAAGH,IAAI,CAACO,IAAI,EAAIJ,CAAC,CAAGH,IAAI,CAACQ,KAAK,EAAIH,CAAC,CAAGL,IAAI,CAACS,GAAG,EAAIJ,CAAC,CAAGL,IAAI,CAACU,MAAM,CAAE,CACtEzC,iBAAiB,CAAC,IAAI,CAAC,CACvBE,gBAAgB,CAAC,IAAI,CAAC,CACxB,CACF,CAAC,CAED,KAAM,CAAAwC,UAAU,CAAGA,CAACvB,CAAkB,CAAEwB,YAA8B,CAAEC,WAAoB,GAAK,CAC/FzB,CAAC,CAACQ,cAAc,CAAC,CAAC,CAElB,GAAI,CAAC9B,cAAc,CAAE,OAErB,KAAM,CAAAgD,kBAAkB,CAAGhC,eAAe,CAACiC,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACrC,EAAE,GAAKb,cAAc,CAAC,CAC7E,GAAI,CAACgD,kBAAkB,CAAE,OAEzB,KAAM,CAAAG,WAAW,CAAG,CAAC,GAAGnC,eAAe,CAAC,CACxC,KAAM,CAAAoC,YAAY,CAAGD,WAAW,CAACE,SAAS,CAACH,CAAC,EAAIA,CAAC,CAACrC,EAAE,GAAKb,cAAc,CAAC,CAExE;AACAmD,WAAW,CAACG,MAAM,CAACF,YAAY,CAAE,CAAC,CAAC,CAEnC;AACA,GAAI,CAAAG,QAAgB,CACpB,GAAIT,YAAY,GAAK,MAAM,CAAE,CAC3BS,QAAQ,CAAGR,WAAW,GAAKf,SAAS,CAAGe,WAAW,CAAG,CAAC,CACtDQ,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAACF,QAAQ,CAAE,CAAC,CAAC,CAAE;AACpC,CAAC,IAAM,CACLA,QAAQ,CAAGR,WAAW,GAAKf,SAAS,CAAGe,WAAW,CAAG,CAAC,CAAG,CAAC,CAC1DQ,QAAQ,CAAGC,IAAI,CAACE,GAAG,CAACH,QAAQ,CAAE,CAAC,CAAC,CAAE;AACpC,CAEA;AACAJ,WAAW,CAACG,MAAM,CAACC,QAAQ,CAAE,CAAC,CAAEP,kBAAkB,CAAC,CAEnD5D,gBAAgB,CAAC+D,WAAW,CAAC,CAC7BlD,iBAAiB,CAAC,IAAI,CAAC,CACvBE,iBAAiB,CAAC,IAAI,CAAC,CACvBE,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED,mBACExB,KAAA,QAAK8E,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B/E,KAAA,QAAK8E,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAE7B/E,KAAA,QAAK8E,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjF,IAAA,QAAKgF,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BjF,IAAA,OAAAiF,QAAA,CAAI,WAAS,CAAI,CAAC,CACf,CAAC,cAGNjF,IAAA,QAAKgF,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BjF,IAAA,QAAKgF,SAAS,CAAC,iBAAiB,CAACE,OAAO,CAAEpD,sBAAuB,CAAAmD,QAAA,CAC9D3E,YAAY,cACXJ,KAAA,QAAK8E,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjF,IAAA,QAAKmF,GAAG,CAAE7E,YAAa,CAAC8E,GAAG,CAAC,SAAS,CAAE,CAAC,cACxCpF,IAAA,QAAKgF,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BjF,IAAA,SAAAiF,QAAA,CAAM,cAAY,CAAM,CAAC,CACtB,CAAC,EACH,CAAC,cAEN/E,KAAA,QAAK8E,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCjF,IAAA,QAAKgF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,oBAAG,CAAK,CAAC,cAC3CjF,IAAA,MAAAiF,QAAA,CAAG,oBAAkB,CAAG,CAAC,EACtB,CACN,CACE,CAAC,CACH,CAAC,cAGN/E,KAAA,QACE8E,SAAS,kBAAAK,MAAA,CACP9D,cAAc,GAAK,MAAM,CAAG,WAAW,CAAG,EAAE,CAC3C,CACH+D,UAAU,CAAG3C,CAAC,EAAKK,cAAc,CAACL,CAAC,CAAE,MAAM,CAAE,CAC7C4C,WAAW,CAAEjC,eAAgB,CAC7BkC,MAAM,CAAG7C,CAAC,EAAKuB,UAAU,CAACvB,CAAC,CAAE,MAAM,CAAE,CAAAsC,QAAA,EAEpC1C,YAAY,CAACkD,GAAG,CAAC,CAACC,OAAO,CAAExC,KAAK,gBAC/BhD,KAAA,QAEE8E,SAAS,mBAAAK,MAAA,CACPhE,cAAc,GAAKqE,OAAO,CAACxD,EAAE,CAAG,UAAU,CAAG,EAAE,MAAAmD,MAAA,CAE/C9D,cAAc,GAAK,MAAM,EAAIE,aAAa,GAAKyB,KAAK,CAAG,gBAAgB,CAAG,EAAE,CAC3E,CACH,kBAAiBwC,OAAO,CAACxD,EAAG,CAC5ByD,SAAS,MACTC,WAAW,CAAGjD,CAAC,EAAKD,eAAe,CAACC,CAAC,CAAE+C,OAAO,CAACxD,EAAE,CAAE,CACnD2D,SAAS,CAAE9C,aAAc,CACzBuC,UAAU,CAAG3C,CAAC,EAAKK,cAAc,CAACL,CAAC,CAAE,MAAM,CAAEO,KAAK,CAAE,CAAA+B,QAAA,eAEpD/E,KAAA,QAAK8E,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BjF,IAAA,QAAKgF,SAAS,CAAC,aAAa,CAAC7C,KAAK,CAAC,iBAAiB,CAAA8C,QAAA,cAClDjF,IAAA,SAAAiF,QAAA,CAAM,cAAE,CAAM,CAAC,CACZ,CAAC,cACNjF,IAAA,OAAAiF,QAAA,CAAKS,OAAO,CAACvD,KAAK,CAAK,CAAC,cACxBnC,IAAA,WACEgF,SAAS,CAAC,oBAAoB,CAC9BE,OAAO,CAAEA,CAAA,GAAM1E,eAAe,CAACkF,OAAO,CAACxD,EAAE,CAAE,CAC3CC,KAAK,CAAC,gBAAgB,CAAA8C,QAAA,CACvB,oBAED,CAAQ,CAAC,EACN,CAAC,cACNjF,IAAA,QAAKgF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BjF,IAAA,CAACH,UAAU,EACTiG,KAAK,CAAEJ,OAAO,CAACtD,OAAQ,CACvB2D,QAAQ,CAAG3D,OAAO,EAAK7B,eAAe,CAACmF,OAAO,CAACxD,EAAE,CAAEE,OAAO,CAAE,CAC5D4D,WAAW,UAAAX,MAAA,CAAWK,OAAO,CAACvD,KAAK,CAAC8D,WAAW,CAAC,CAAC,eAAc,CAC/DC,QAAQ,CAAEjF,YAAY,GAAKyE,OAAO,CAACxD,EAAG,CACtCiE,OAAO,CAAEA,CAAA,GAAM,CACbxE,iBAAiB,CAAC+D,OAAO,CAACxD,EAAE,CAAC,CAC7B;AACA,GAAIvB,iBAAiB,CAAE,CACrB,KAAM,CAAAyF,aAAa,CAAGC,QAAQ,CAACC,aAAa,uBAAAjB,MAAA,CAAsBK,OAAO,CAACxD,EAAE,oBAAiB,CAAmB,CAChH,GAAIkE,aAAa,CAAE,CACjBzF,iBAAiB,CAACyF,aAAa,CAAE,CAC/BG,UAAU,CAAE,OAAO,CACnBC,QAAQ,CAAE,IAAI,CACdC,IAAI,CAAE,KAAK,CACXC,MAAM,CAAE,KAAK,CACbC,SAAS,CAAE,KAAK,CAChBC,SAAS,CAAE,MAAM,CACjBC,KAAK,CAAE,SAAS,CAChBC,eAAe,CAAE,aAAa,CAC9BC,UAAU,CAAE,KAAK,CACjBC,aAAa,CAAE,GACjB,CAAC,CAAC,CACJ,CACF,CACF,CAAE,CACFC,MAAM,CAAGC,KAAwB,EAAK,CACpCrF,gBAAgB,CAAC,CAAC,CAClBjB,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAGsG,KAAK,CAAC,CAC3B,CAAE,CACFrG,iBAAiB,CAAEA,iBAAkB,CACrCC,mBAAmB,CAAEA,mBAAoB,CACzCkE,SAAS,CAAC,oBAAoB,CAC/B,CAAC,CACC,CAAC,GA5DDU,OAAO,CAACxD,EA6DV,CACN,CAAC,CAEDX,cAAc,GAAK,MAAM,eACxBvB,IAAA,QAAKgF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,mBAErC,CAAK,CACN,EACE,CAAC,EACH,CAAC,cAGN/E,KAAA,QAAK8E,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BjF,IAAA,QAAKgF,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BjF,IAAA,OAAAiF,QAAA,CAAI,mBAAiB,CAAI,CAAC,CACvB,CAAC,cAGN/E,KAAA,QACE8E,SAAS,mBAAAK,MAAA,CACP9D,cAAc,GAAK,OAAO,CAAG,WAAW,CAAG,EAAE,CAC5C,CACH+D,UAAU,CAAG3C,CAAC,EAAKK,cAAc,CAACL,CAAC,CAAE,OAAO,CAAE,CAC9C4C,WAAW,CAAEjC,eAAgB,CAC7BkC,MAAM,CAAG7C,CAAC,EAAKuB,UAAU,CAACvB,CAAC,CAAE,OAAO,CAAE,CAAAsC,QAAA,EAErCxC,aAAa,CAACgD,GAAG,CAAC,CAACC,OAAO,CAAExC,KAAK,gBAChChD,KAAA,QAEE8E,SAAS,mBAAAK,MAAA,CACPhE,cAAc,GAAKqE,OAAO,CAACxD,EAAE,CAAG,UAAU,CAAG,EAAE,MAAAmD,MAAA,CAE/C9D,cAAc,GAAK,OAAO,EAAIE,aAAa,GAAKyB,KAAK,CAAG,gBAAgB,CAAG,EAAE,CAC5E,CACH,kBAAiBwC,OAAO,CAACxD,EAAG,CAC5ByD,SAAS,MACTC,WAAW,CAAGjD,CAAC,EAAKD,eAAe,CAACC,CAAC,CAAE+C,OAAO,CAACxD,EAAE,CAAE,CACnD2D,SAAS,CAAE9C,aAAc,CACzBuC,UAAU,CAAG3C,CAAC,EAAKK,cAAc,CAACL,CAAC,CAAE,OAAO,CAAEO,KAAK,CAAE,CAAA+B,QAAA,eAErD/E,KAAA,QAAK8E,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BjF,IAAA,QAAKgF,SAAS,CAAC,aAAa,CAAC7C,KAAK,CAAC,iBAAiB,CAAA8C,QAAA,cAClDjF,IAAA,SAAAiF,QAAA,CAAM,cAAE,CAAM,CAAC,CACZ,CAAC,cACNjF,IAAA,OAAAiF,QAAA,CAAKS,OAAO,CAACvD,KAAK,CAAK,CAAC,cACxBnC,IAAA,WACEgF,SAAS,CAAC,oBAAoB,CAC9BE,OAAO,CAAEA,CAAA,GAAM1E,eAAe,CAACkF,OAAO,CAACxD,EAAE,CAAE,CAC3CC,KAAK,CAAC,gBAAgB,CAAA8C,QAAA,CACvB,oBAED,CAAQ,CAAC,EACN,CAAC,cACNjF,IAAA,QAAKgF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BjF,IAAA,CAACH,UAAU,EACTiG,KAAK,CAAEJ,OAAO,CAACtD,OAAQ,CACvB2D,QAAQ,CAAG3D,OAAO,EAAK7B,eAAe,CAACmF,OAAO,CAACxD,EAAE,CAAEE,OAAO,CAAE,CAC5D4D,WAAW,UAAAX,MAAA,CAAWK,OAAO,CAACvD,KAAK,CAAC8D,WAAW,CAAC,CAAC,mBAAkB,CACnEC,QAAQ,CAAEjF,YAAY,GAAKyE,OAAO,CAACxD,EAAG,CACtCiE,OAAO,CAAEA,CAAA,GAAM,CACbxE,iBAAiB,CAAC+D,OAAO,CAACxD,EAAE,CAAC,CAC7B;AACA,GAAIvB,iBAAiB,CAAE,CACrB,KAAM,CAAAyF,aAAa,CAAGC,QAAQ,CAACC,aAAa,uBAAAjB,MAAA,CAAsBK,OAAO,CAACxD,EAAE,oBAAiB,CAAmB,CAChH,GAAIkE,aAAa,CAAE,CACjBzF,iBAAiB,CAACyF,aAAa,CAAE,CAC/BG,UAAU,CAAE,OAAO,CACnBC,QAAQ,CAAE,IAAI,CACdC,IAAI,CAAE,KAAK,CACXC,MAAM,CAAE,KAAK,CACbC,SAAS,CAAE,KAAK,CAChBC,SAAS,CAAE,MAAM,CACjBC,KAAK,CAAE,SAAS,CAChBC,eAAe,CAAE,aAAa,CAC9BC,UAAU,CAAE,KAAK,CACjBC,aAAa,CAAE,GACjB,CAAC,CAAC,CACJ,CACF,CACF,CACD,CACCC,MAAM,CAAGC,KAAwB,EAAK,CACpCrF,gBAAgB,CAAC,CAAC,CAClBjB,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAGsG,KAAK,CAAC,CAC3B,CAAE,CACFrG,iBAAiB,CAAEA,iBAAkB,CACrCC,mBAAmB,CAAEA,mBAAoB,CACzCkE,SAAS,CAAC,oBAAoB,CAC/B,CAAC,CACC,CAAC,GA7DDU,OAAO,CAACxD,EA8DV,CACN,CAAC,CAEDX,cAAc,GAAK,OAAO,eACzBvB,IAAA,QAAKgF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,mBAErC,CAAK,CACN,EACE,CAAC,EACH,CAAC,cAGN/E,KAAA,QAAK8E,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCjF,IAAA,WAAQgF,SAAS,CAAC,kBAAkB,CAACE,OAAO,CAAEnE,UAAW,CAACoB,KAAK,CAAC,eAAe,CAAA8C,QAAA,cAC7EjF,IAAA,SAAMgF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAC,CAAM,CAAC,CAC/B,CAAC,cACTjF,IAAA,WAAQgF,SAAS,CAAC,kBAAkB,CAACE,OAAO,CAAElE,MAAO,CAACmB,KAAK,CAAC,WAAW,CAAA8C,QAAA,cACrEjF,IAAA,SAAMgF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAC,CAAM,CAAC,CAC/B,CAAC,EACN,CAAC,cAGN/E,KAAA,QAAK8E,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCjF,IAAA,WAAQgF,SAAS,CAAC,sBAAsB,CAAC7C,KAAK,CAAC,kBAAkB,CAAA8C,QAAA,CAAC,oBAElE,CAAQ,CAAC,cACTjF,IAAA,WAAQgF,SAAS,CAAC,uBAAuB,CAAC7C,KAAK,CAAC,mBAAmB,CAAA8C,QAAA,CAAC,qBAEpE,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAGNjF,IAAA,QAAKgF,SAAS,CAAC,oBAAoB,CAAM,CAAC,cAG1ChF,IAAA,CAACF,YAAY,EACXqH,MAAM,CAAEhG,gBAAiB,CACzBiG,OAAO,CAAEA,CAAA,GAAMhG,mBAAmB,CAAC,KAAK,CAAE,CAC1CiG,aAAa,CAAEtF,iBAAkB,CAClC,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}