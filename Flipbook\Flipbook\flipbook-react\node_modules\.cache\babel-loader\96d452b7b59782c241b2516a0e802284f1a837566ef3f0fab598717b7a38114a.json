{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4;import React,{useState}from'react';import styled from'styled-components';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SidebarContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  width: 60px;\\n  background-color: #2c3e50;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 20px 0;\\n  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);\\n\"])));const SidebarItem=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  width: 40px;\\n  height: 40px;\\n  margin-bottom: 15px;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  background-color: \",\";\\n  color: \",\";\\n  position: relative;\\n\\n  &:hover {\\n    background-color: \",\";\\n    color: white;\\n    transform: scale(1.1);\\n  }\\n\"])),props=>props.active?'#3498db':'transparent',props=>props.active?'white':'#bdc3c7',props=>props.active?'#3498db':'#34495e');const IconWrapper=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  font-size: 18px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n\"])));const Tooltip=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  position: absolute;\\n  left: 60px;\\n  background-color: #2c3e50;\\n  color: white;\\n  padding: 8px 12px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  white-space: nowrap;\\n  opacity: \",\";\\n  visibility: \",\";\\n  transition: all 0.3s ease;\\n  z-index: 1000;\\n  \\n  &::before {\\n    content: '';\\n    position: absolute;\\n    left: -5px;\\n    top: 50%;\\n    transform: translateY(-50%);\\n    border: 5px solid transparent;\\n    border-right-color: #2c3e50;\\n  }\\n\"])),props=>props.show?1:0,props=>props.show?'visible':'hidden');const Sidebar=()=>{const[hoveredItem,setHoveredItem]=useState(null);const[activeItem,setActiveItem]=useState('flipbooks');const sidebarItems=[{id:'flipbooks',icon:'📖',label:'My Flipbooks',active:true},{id:'images',icon:'🖼️',label:'Images'},{id:'templates',icon:'📄',label:'Templates'},{id:'layouts',icon:'📐',label:'Layouts'},{id:'colors',icon:'🎨',label:'Colors'},{id:'text',icon:'📝',label:'Text Tools'},{id:'effects',icon:'✨',label:'Effects'},{id:'settings',icon:'⚙️',label:'Settings'}];const handleItemClick=itemId=>{setActiveItem(itemId);};const handleMouseEnter=itemId=>{setHoveredItem(itemId);};const handleMouseLeave=()=>{setHoveredItem(null);};return/*#__PURE__*/_jsx(SidebarContainer,{children:sidebarItems.map(item=>/*#__PURE__*/_jsxs(SidebarItem,{active:activeItem===item.id,onClick:()=>handleItemClick(item.id),onMouseEnter:()=>handleMouseEnter(item.id),onMouseLeave:handleMouseLeave,children:[/*#__PURE__*/_jsx(IconWrapper,{children:item.icon}),/*#__PURE__*/_jsx(Tooltip,{show:hoveredItem===item.id,children:item.label})]},item.id))});};export default Sidebar;", "map": {"version": 3, "names": ["React", "useState", "styled", "jsx", "_jsx", "jsxs", "_jsxs", "SidebarContainer", "div", "_templateObject", "_taggedTemplateLiteral", "SidebarItem", "_templateObject2", "props", "active", "IconWrapper", "_templateObject3", "<PERSON><PERSON><PERSON>", "_templateObject4", "show", "Sidebar", "hoveredItem", "setHoveredItem", "activeItem", "setActiveItem", "sidebarItems", "id", "icon", "label", "handleItemClick", "itemId", "handleMouseEnter", "handleMouseLeave", "children", "map", "item", "onClick", "onMouseEnter", "onMouseLeave"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/LandingPage/Sidebar.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\n\nconst SidebarContainer = styled.div`\n  width: 60px;\n  background-color: #2c3e50;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20px 0;\n  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);\n`;\n\nconst SidebarItem = styled.div<{ active?: boolean }>`\n  width: 40px;\n  height: 40px;\n  margin-bottom: 15px;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background-color: ${props => props.active ? '#3498db' : 'transparent'};\n  color: ${props => props.active ? 'white' : '#bdc3c7'};\n  position: relative;\n\n  &:hover {\n    background-color: ${props => props.active ? '#3498db' : '#34495e'};\n    color: white;\n    transform: scale(1.1);\n  }\n`;\n\nconst IconWrapper = styled.div`\n  font-size: 18px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\n\nconst Tooltip = styled.div<{ show: boolean }>`\n  position: absolute;\n  left: 60px;\n  background-color: #2c3e50;\n  color: white;\n  padding: 8px 12px;\n  border-radius: 4px;\n  font-size: 12px;\n  white-space: nowrap;\n  opacity: ${props => props.show ? 1 : 0};\n  visibility: ${props => props.show ? 'visible' : 'hidden'};\n  transition: all 0.3s ease;\n  z-index: 1000;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    left: -5px;\n    top: 50%;\n    transform: translateY(-50%);\n    border: 5px solid transparent;\n    border-right-color: #2c3e50;\n  }\n`;\n\ninterface SidebarItemData {\n  id: string;\n  icon: string;\n  label: string;\n  active?: boolean;\n}\n\nconst Sidebar: React.FC = () => {\n  const [hoveredItem, setHoveredItem] = useState<string | null>(null);\n  const [activeItem, setActiveItem] = useState<string>('flipbooks');\n\n  const sidebarItems: SidebarItemData[] = [\n    { id: 'flipbooks', icon: '📖', label: 'My Flipbooks', active: true },\n    { id: 'images', icon: '🖼️', label: 'Images' },\n    { id: 'templates', icon: '📄', label: 'Templates' },\n    { id: 'layouts', icon: '📐', label: 'Layouts' },\n    { id: 'colors', icon: '🎨', label: 'Colors' },\n    { id: 'text', icon: '📝', label: 'Text Tools' },\n    { id: 'effects', icon: '✨', label: 'Effects' },\n    { id: 'settings', icon: '⚙️', label: 'Settings' },\n  ];\n\n  const handleItemClick = (itemId: string) => {\n    setActiveItem(itemId);\n  };\n\n  const handleMouseEnter = (itemId: string) => {\n    setHoveredItem(itemId);\n  };\n\n  const handleMouseLeave = () => {\n    setHoveredItem(null);\n  };\n\n  return (\n    <SidebarContainer>\n      {sidebarItems.map((item) => (\n        <SidebarItem\n          key={item.id}\n          active={activeItem === item.id}\n          onClick={() => handleItemClick(item.id)}\n          onMouseEnter={() => handleMouseEnter(item.id)}\n          onMouseLeave={handleMouseLeave}\n        >\n          <IconWrapper>{item.icon}</IconWrapper>\n          <Tooltip show={hoveredItem === item.id}>\n            {item.label}\n          </Tooltip>\n        </SidebarItem>\n      ))}\n    </SidebarContainer>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": "oOAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvC,KAAM,CAAAC,gBAAgB,CAAGL,MAAM,CAACM,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,8LAQlC,CAED,KAAM,CAAAC,WAAW,CAAGT,MAAM,CAACM,GAAG,CAAAI,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,iXAURG,KAAK,EAAIA,KAAK,CAACC,MAAM,CAAG,SAAS,CAAG,aAAa,CAC5DD,KAAK,EAAIA,KAAK,CAACC,MAAM,CAAG,OAAO,CAAG,SAAS,CAI9BD,KAAK,EAAIA,KAAK,CAACC,MAAM,CAAG,SAAS,CAAG,SAAS,CAIpE,CAED,KAAM,CAAAC,WAAW,CAAGb,MAAM,CAACM,GAAG,CAAAQ,gBAAA,GAAAA,gBAAA,CAAAN,sBAAA,oGAK7B,CAED,KAAM,CAAAO,OAAO,CAAGf,MAAM,CAACM,GAAG,CAAAU,gBAAA,GAAAA,gBAAA,CAAAR,sBAAA,wdASbG,KAAK,EAAIA,KAAK,CAACM,IAAI,CAAG,CAAC,CAAG,CAAC,CACxBN,KAAK,EAAIA,KAAK,CAACM,IAAI,CAAG,SAAS,CAAG,QAAQ,CAazD,CASD,KAAM,CAAAC,OAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGrB,QAAQ,CAAgB,IAAI,CAAC,CACnE,KAAM,CAACsB,UAAU,CAAEC,aAAa,CAAC,CAAGvB,QAAQ,CAAS,WAAW,CAAC,CAEjE,KAAM,CAAAwB,YAA+B,CAAG,CACtC,CAAEC,EAAE,CAAE,WAAW,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,cAAc,CAAEd,MAAM,CAAE,IAAK,CAAC,CACpE,CAAEY,EAAE,CAAE,QAAQ,CAAEC,IAAI,CAAE,KAAK,CAAEC,KAAK,CAAE,QAAS,CAAC,CAC9C,CAAEF,EAAE,CAAE,WAAW,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,WAAY,CAAC,CACnD,CAAEF,EAAE,CAAE,SAAS,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC/C,CAAEF,EAAE,CAAE,QAAQ,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,QAAS,CAAC,CAC7C,CAAEF,EAAE,CAAE,MAAM,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,YAAa,CAAC,CAC/C,CAAEF,EAAE,CAAE,SAAS,CAAEC,IAAI,CAAE,GAAG,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC9C,CAAEF,EAAE,CAAE,UAAU,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,UAAW,CAAC,CAClD,CAED,KAAM,CAAAC,eAAe,CAAIC,MAAc,EAAK,CAC1CN,aAAa,CAACM,MAAM,CAAC,CACvB,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAID,MAAc,EAAK,CAC3CR,cAAc,CAACQ,MAAM,CAAC,CACxB,CAAC,CAED,KAAM,CAAAE,gBAAgB,CAAGA,CAAA,GAAM,CAC7BV,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAED,mBACElB,IAAA,CAACG,gBAAgB,EAAA0B,QAAA,CACdR,YAAY,CAACS,GAAG,CAAEC,IAAI,eACrB7B,KAAA,CAACK,WAAW,EAEVG,MAAM,CAAES,UAAU,GAAKY,IAAI,CAACT,EAAG,CAC/BU,OAAO,CAAEA,CAAA,GAAMP,eAAe,CAACM,IAAI,CAACT,EAAE,CAAE,CACxCW,YAAY,CAAEA,CAAA,GAAMN,gBAAgB,CAACI,IAAI,CAACT,EAAE,CAAE,CAC9CY,YAAY,CAAEN,gBAAiB,CAAAC,QAAA,eAE/B7B,IAAA,CAACW,WAAW,EAAAkB,QAAA,CAAEE,IAAI,CAACR,IAAI,CAAc,CAAC,cACtCvB,IAAA,CAACa,OAAO,EAACE,IAAI,CAAEE,WAAW,GAAKc,IAAI,CAACT,EAAG,CAAAO,QAAA,CACpCE,IAAI,CAACP,KAAK,CACJ,CAAC,GATLO,IAAI,CAACT,EAUC,CACd,CAAC,CACc,CAAC,CAEvB,CAAC,CAED,cAAe,CAAAN,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}