{"ast": null, "code": "var _jsxFileName = \"D:\\\\TradeWorks\\\\Flipbook\\\\Flipbook\\\\Flipbook\\\\flipbook-react\\\\src\\\\components\\\\LandingPage\\\\Header.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../hooks/useAuth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HeaderContainer = styled.header`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: relative;\n  z-index: 100;\n`;\n_c = HeaderContainer;\nconst LeftSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 30px;\n`;\n_c2 = LeftSection;\nconst Logo = styled.div`\n  font-family: 'Brush Script MT', cursive;\n  font-size: 28px;\n  font-weight: bold;\n  color: white;\n  text-decoration: none;\n  cursor: pointer;\n  \n  &:hover {\n    opacity: 0.9;\n  }\n`;\n_c3 = Logo;\nconst Navigation = styled.nav`\n  display: flex;\n  gap: 25px;\n`;\n_c4 = Navigation;\nconst NavItem = styled.a`\n  color: white;\n  text-decoration: none;\n  font-size: 14px;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  padding: 8px 12px;\n  border-radius: 4px;\n  transition: background-color 0.3s ease;\n  cursor: pointer;\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n_c5 = NavItem;\nconst RightSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 15px;\n`;\n_c6 = RightSection;\nconst IconButton = styled.button`\n  background: none;\n  border: none;\n  color: white;\n  font-size: 18px;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 4px;\n  transition: background-color 0.3s ease;\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n_c7 = IconButton;\nconst UserSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 10px;\n`;\n_c8 = UserSection;\nconst UserAvatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 14px;\n  color: white;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n\n  &:hover {\n    transform: scale(1.1);\n  }\n`;\n_c9 = UserAvatar;\nconst UserInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n`;\n_c0 = UserInfo;\nconst SignInText = styled.span`\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n`;\n_c1 = SignInText;\nconst JoinUsText = styled.span`\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.7);\n`;\n_c10 = JoinUsText;\nconst Header = () => {\n  _s();\n  var _user$firstName, _user$lastName;\n  const {\n    user,\n    isAuthenticated,\n    signOut\n  } = useAuth();\n  const [showSignInModal, setShowSignInModal] = useState(false);\n  const handleSignInClick = () => {\n    if (isAuthenticated) {\n      signOut();\n    } else {\n      setShowSignInModal(true);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(HeaderContainer, {\n    children: [/*#__PURE__*/_jsxDEV(LeftSection, {\n      children: [/*#__PURE__*/_jsxDEV(Logo, {\n        children: \"Flipbook\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Navigation, {\n        children: [/*#__PURE__*/_jsxDEV(NavItem, {\n          href: \"#file\",\n          children: \"FILE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(NavItem, {\n          href: \"#edit\",\n          children: \"EDIT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(NavItem, {\n          href: \"#share\",\n          children: \"SHARE!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(NavItem, {\n          href: \"#help\",\n          children: \"HELP\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RightSection, {\n      children: [/*#__PURE__*/_jsxDEV(IconButton, {\n        title: \"Tools\",\n        children: \"\\uD83D\\uDD27\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(UserSection, {\n        children: [/*#__PURE__*/_jsxDEV(UserAvatar, {\n          children: isAuthenticated && user ? (((_user$firstName = user.firstName) === null || _user$firstName === void 0 ? void 0 : _user$firstName.charAt(0)) || 'U') + (((_user$lastName = user.lastName) === null || _user$lastName === void 0 ? void 0 : _user$lastName.charAt(0)) || 'S') : 'FR'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(UserInfo, {\n          children: [/*#__PURE__*/_jsxDEV(SignInText, {\n            onClick: handleSignInClick,\n            style: {\n              cursor: 'pointer'\n            },\n            children: isAuthenticated ? 'Sign Out' : 'Sign In'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(JoinUsText, {\n            children: \"Join Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 34\n          }, this), isAuthenticated && user && /*#__PURE__*/_jsxDEV(JoinUsText, {\n            children: [user.firstName, \" \", user.lastName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"9Msb/ATf8hlzNrmvo/5ocmjsBY0=\", false, function () {\n  return [useAuth];\n});\n_c11 = Header;\nexport default Header;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11;\n$RefreshReg$(_c, \"HeaderContainer\");\n$RefreshReg$(_c2, \"LeftSection\");\n$RefreshReg$(_c3, \"Logo\");\n$RefreshReg$(_c4, \"Navigation\");\n$RefreshReg$(_c5, \"NavItem\");\n$RefreshReg$(_c6, \"RightSection\");\n$RefreshReg$(_c7, \"IconButton\");\n$RefreshReg$(_c8, \"UserSection\");\n$RefreshReg$(_c9, \"UserAvatar\");\n$RefreshReg$(_c0, \"UserInfo\");\n$RefreshReg$(_c1, \"SignInText\");\n$RefreshReg$(_c10, \"JoinUsText\");\n$RefreshReg$(_c11, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "styled", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header", "_c", "LeftSection", "div", "_c2", "Logo", "_c3", "Navigation", "nav", "_c4", "NavItem", "a", "_c5", "RightSection", "_c6", "IconButton", "button", "_c7", "UserSection", "_c8", "UserAvatar", "_c9", "UserInfo", "_c0", "SignInText", "span", "_c1", "JoinUsText", "_c10", "Header", "_s", "_user$firstName", "_user$lastName", "user", "isAuthenticated", "signOut", "showSignInModal", "setShowSignInModal", "handleSignInClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "title", "firstName", "char<PERSON>t", "lastName", "onClick", "style", "cursor", "_c11", "$RefreshReg$"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/LandingPage/Header.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../hooks/useAuth';\n\nconst HeaderContainer = styled.header`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: relative;\n  z-index: 100;\n`;\n\nconst LeftSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 30px;\n`;\n\nconst Logo = styled.div`\n  font-family: 'Brush Script MT', cursive;\n  font-size: 28px;\n  font-weight: bold;\n  color: white;\n  text-decoration: none;\n  cursor: pointer;\n  \n  &:hover {\n    opacity: 0.9;\n  }\n`;\n\nconst Navigation = styled.nav`\n  display: flex;\n  gap: 25px;\n`;\n\nconst NavItem = styled.a`\n  color: white;\n  text-decoration: none;\n  font-size: 14px;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  padding: 8px 12px;\n  border-radius: 4px;\n  transition: background-color 0.3s ease;\n  cursor: pointer;\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n\nconst RightSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 15px;\n`;\n\nconst IconButton = styled.button`\n  background: none;\n  border: none;\n  color: white;\n  font-size: 18px;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 4px;\n  transition: background-color 0.3s ease;\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n\nconst UserSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 10px;\n`;\n\nconst UserAvatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 14px;\n  color: white;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n\n  &:hover {\n    transform: scale(1.1);\n  }\n`;\n\nconst UserInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n`;\n\nconst SignInText = styled.span`\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n`;\n\nconst JoinUsText = styled.span`\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.7);\n`;\n\nconst Header: React.FC = () => {\n  const { user, isAuthenticated, signOut } = useAuth();\n  const [showSignInModal, setShowSignInModal] = useState(false);\n\n  const handleSignInClick = () => {\n    if (isAuthenticated) {\n      signOut();\n    } else {\n      setShowSignInModal(true);\n    }\n  };\n\n  return (\n    <HeaderContainer>\n      <LeftSection>\n        <Logo>Flipbook</Logo>\n        <Navigation>\n          <NavItem href=\"#file\">FILE</NavItem>\n          <NavItem href=\"#edit\">EDIT</NavItem>\n          <NavItem href=\"#share\">SHARE!</NavItem>\n          <NavItem href=\"#help\">HELP</NavItem>\n        </Navigation>\n      </LeftSection>\n\n      <RightSection>\n        <IconButton title=\"Tools\">\n          🔧\n        </IconButton>\n\n        <UserSection>\n          <UserAvatar>\n            {isAuthenticated && user ?\n              (user.firstName?.charAt(0) || 'U') + (user.lastName?.charAt(0) || 'S') :\n              'FR'\n            }\n          </UserAvatar>\n          <UserInfo>\n            <SignInText onClick={handleSignInClick} style={{ cursor: 'pointer' }}>\n              {isAuthenticated ? 'Sign Out' : 'Sign In'}\n            </SignInText>\n            {!isAuthenticated && <JoinUsText>Join Us</JoinUsText>}\n            {isAuthenticated && user && (\n              <JoinUsText>{user.firstName} {user.lastName}</JoinUsText>\n            )}\n          </UserInfo>\n        </UserSection>\n      </RightSection>\n    </HeaderContainer>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,eAAe,GAAGJ,MAAM,CAACK,MAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAVIF,eAAe;AAYrB,MAAMG,WAAW,GAAGP,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,WAAW;AAMjB,MAAMG,IAAI,GAAGV,MAAM,CAACQ,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAXID,IAAI;AAaV,MAAME,UAAU,GAAGZ,MAAM,CAACa,GAAG;AAC7B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,UAAU;AAKhB,MAAMG,OAAO,GAAGf,MAAM,CAACgB,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,OAAO;AAiBb,MAAMG,YAAY,GAAGlB,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAJID,YAAY;AAMlB,MAAME,UAAU,GAAGpB,MAAM,CAACqB,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAbIF,UAAU;AAehB,MAAMG,WAAW,GAAGvB,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GAJID,WAAW;AAMjB,MAAME,UAAU,GAAGzB,MAAM,CAACQ,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GAjBID,UAAU;AAmBhB,MAAME,QAAQ,GAAG3B,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GAJID,QAAQ;AAMd,MAAME,UAAU,GAAG7B,MAAM,CAAC8B,IAAI;AAC9B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,UAAU;AAMhB,MAAMG,UAAU,GAAGhC,MAAM,CAAC8B,IAAI;AAC9B;AACA;AACA,CAAC;AAACG,IAAA,GAHID,UAAU;AAKhB,MAAME,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,cAAA;EAC7B,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGvC,OAAO,CAAC,CAAC;EACpD,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAM4C,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIJ,eAAe,EAAE;MACnBC,OAAO,CAAC,CAAC;IACX,CAAC,MAAM;MACLE,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC;EAED,oBACEvC,OAAA,CAACC,eAAe;IAAAwC,QAAA,gBACdzC,OAAA,CAACI,WAAW;MAAAqC,QAAA,gBACVzC,OAAA,CAACO,IAAI;QAAAkC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrB7C,OAAA,CAACS,UAAU;QAAAgC,QAAA,gBACTzC,OAAA,CAACY,OAAO;UAACkC,IAAI,EAAC,OAAO;UAAAL,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eACpC7C,OAAA,CAACY,OAAO;UAACkC,IAAI,EAAC,OAAO;UAAAL,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eACpC7C,OAAA,CAACY,OAAO;UAACkC,IAAI,EAAC,QAAQ;UAAAL,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eACvC7C,OAAA,CAACY,OAAO;UAACkC,IAAI,EAAC,OAAO;UAAAL,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEd7C,OAAA,CAACe,YAAY;MAAA0B,QAAA,gBACXzC,OAAA,CAACiB,UAAU;QAAC8B,KAAK,EAAC,OAAO;QAAAN,QAAA,EAAC;MAE1B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb7C,OAAA,CAACoB,WAAW;QAAAqB,QAAA,gBACVzC,OAAA,CAACsB,UAAU;UAAAmB,QAAA,EACRL,eAAe,IAAID,IAAI,GACtB,CAAC,EAAAF,eAAA,GAAAE,IAAI,CAACa,SAAS,cAAAf,eAAA,uBAAdA,eAAA,CAAgBgB,MAAM,CAAC,CAAC,CAAC,KAAI,GAAG,KAAK,EAAAf,cAAA,GAAAC,IAAI,CAACe,QAAQ,cAAAhB,cAAA,uBAAbA,cAAA,CAAee,MAAM,CAAC,CAAC,CAAC,KAAI,GAAG,CAAC,GACtE;QAAI;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEI,CAAC,eACb7C,OAAA,CAACwB,QAAQ;UAAAiB,QAAA,gBACPzC,OAAA,CAAC0B,UAAU;YAACyB,OAAO,EAAEX,iBAAkB;YAACY,KAAK,EAAE;cAAEC,MAAM,EAAE;YAAU,CAAE;YAAAZ,QAAA,EAClEL,eAAe,GAAG,UAAU,GAAG;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACZ,CAACT,eAAe,iBAAIpC,OAAA,CAAC6B,UAAU;YAAAY,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACpDT,eAAe,IAAID,IAAI,iBACtBnC,OAAA,CAAC6B,UAAU;YAAAY,QAAA,GAAEN,IAAI,CAACa,SAAS,EAAC,GAAC,EAACb,IAAI,CAACe,QAAQ;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CACzD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEtB,CAAC;AAACb,EAAA,CAjDID,MAAgB;EAAA,QACuBjC,OAAO;AAAA;AAAAwD,IAAA,GAD9CvB,MAAgB;AAmDtB,eAAeA,MAAM;AAAC,IAAA5B,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,IAAA,EAAAwB,IAAA;AAAAC,YAAA,CAAApD,EAAA;AAAAoD,YAAA,CAAAjD,GAAA;AAAAiD,YAAA,CAAA/C,GAAA;AAAA+C,YAAA,CAAA5C,GAAA;AAAA4C,YAAA,CAAAzC,GAAA;AAAAyC,YAAA,CAAAvC,GAAA;AAAAuC,YAAA,CAAApC,GAAA;AAAAoC,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAA9B,GAAA;AAAA8B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAzB,IAAA;AAAAyB,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}