﻿using System.Reflection;
using System.Web;
using System.Web.Optimization;

namespace Flipbook
{

    public class BundleConfig
    {
        //    For Responsive:
        //    For Full Screen user : responsive.css
        //    For Non Full Screen Use :responsive-nonfull.css
        //    Currently Useing responsive-nonfull.css

        // For more information on bundling, visit http://go.microsoft.com/fwlink/?LinkId=301862
        public static void RegisterBundles(BundleCollection bundles)
        {

            //ST-1542

            Assembly web = Assembly.GetExecutingAssembly();
            AssemblyName webName = web.GetName();

            string TWVersion = webName.Version.ToString();

            Styles.DefaultTagFormat = string.Format("<link href='{{0}}?v={0}' rel='stylesheet'/>", TWVersion);
            Scripts.DefaultTagFormat = string.Format("<script src='{{0}}?v={0}'></script>", TWVersion);

            //bundles.Clear();
            //bundles.ResetAll();
            //BundleTable.EnableOptimizations = true;

            #region Flip Book


            bundles.Add(new ScriptBundle("~/bundles/jquery").Include(
                         //"~/Scripts/jquery-{version}.js",
                         "~/Scripts/jquery.min.js",
                         "~/Scripts/bootstrap.min.js",
                         "~/Scripts/enscroll-0.js",
                         "~/Scripts/owl.carousel.js",
                         "~/Scripts/custom.js",
                         "~/Scripts/bootstrap-slider.js",
                         "~/Scripts/jquery.jscrollpane.min.js",
                         "~/Scripts/jquery.mousewheel.js", //IM, 2/8/17, ST217,
                          "~/Scripts/redirecttomobile.js"
                        ));

            bundles.Add(new ScriptBundle("~/bundles/jqueryval").Include(
                        "~/Scripts/jquery.validate*"));

            bundles.Add(new StyleBundle("~/Content/customcss").Include(
                   "~/Content/jquery.dataTables.css"
               ));

            bundles.Add(new ScriptBundle("~/bundles/customjs").Include(
                 "~/Scripts/Datatable/jquery.dataTables.js"
            ));

            bundles.Add(new ScriptBundle("~/bundles/tradeworksjs").Include(
               "~/Scripts/tradeworks/Giantlist.js",
               "~/Scripts/tradeworks/ManageClasses.js"
          ));

            // Use the development version of Modernizr to develop with and learn from. Then, when you're
            // ready for production, use the build tool at http://modernizr.com to pick only the tests you need.
            bundles.Add(new ScriptBundle("~/bundles/modernizr").Include(
                        "~/Scripts/modernizr-*"));

            bundles.Add(new ScriptBundle("~/bundles/bootstrap").Include(
                      "~/Scripts/bootstrap.js",
                      "~/Scripts/respond.js"));

            bundles.Add(new StyleBundle("~/Content/css").Include(
                       //"~/Content/bootstrap.css",
                       //"~/Content/site.css"
                       "~/Content/owl.carousel.css",
                      "~/Content/bootstrap.min.css",
                      "~/Content/result-light.css",
                      "~/Content/style.css",
                      "~/Content/responsive.css",
                      "~/Content/font-awesome.css",
                      "~/Content/bootstrap-slider.css",
                      "~/Content/jquery.jscrollpane.css",
                      "~/Content/mystyle.css"
                      ));

            bundles.Add(new ScriptBundle("~/bundles/jqueryui").Include(
                    "~/Scripts/jquery-ui.js"));




            bundles.Add(new StyleBundle("~/Content/FlipbookPg").Include(
                "~/Content/font-awesome.css",
                "~/Content/toolset.css",
                "~/Content/FB/TextEditor.css",
                "~/Content/FB/newflip.css"
                ));

            bundles.Add(new ScriptBundle("~/Scripts/FlipbookPg").Include(
                    "~/Scripts/jquery.min.js",
                    "~/Scripts/modernizr.custom.js",
                    "~/Scripts/jquerypp.custom.js",
                    "~/Scripts/jquery.bookblock.js",
                    "~/Scripts/jquery.fullscreen.min.js"
                ));

            bundles.Add(new ScriptBundle("~/Scripts/PageManager").Include(
              "~/Scripts/jquery-ui.min.js",
              "~/Scripts/scrollBar.js",
               "~/Scripts/tinter-shader.js",
              "~/Scripts/glisse.js",
              "~/Scripts/slick.min.js",
              "~/Scripts/bootstrap-slider.js",
              "~/Scripts/jquery.maskedinput/jquery.maskedinput.1.4.1.js",
              "~/Scripts/filtrr2/filtrr2.js",
              "~/Scripts/filtrr2/layers.js",
              "~/Scripts/filtrr2/effects.js",
              "~/Scripts/filtrr2/events.js",
              "~/Scripts/filtrr2/util.js",
              "~/Scripts/ColorPickerCanvas.js",
              "~/Scripts/colorPicker.js",
              "~/Scripts/bootstrap-slider/bootstrap-slider.js",
              "~/Scripts/Flipbook/pagemanager.js",
              "~/Scripts/Flipbook/Border.js",
              "~/Scripts/Flipbook/Publish.js"

              ));

            bundles.Add(new StyleBundle("~/Content/PageManager").Include(
                 "~/Content/jquery-ui.css",
                 "~/Content/bootstrap-slider.css",
                 "~/Content/scrollBar.css",
                 "~/Content/slick.css",
                 "~/Content/color_picker.css",
                 "~/Content/colordrop.css"                 
             ));


            bundles.Add(new ScriptBundle("~/Scripts/ImagePopUp").Include(
                "~/Scripts/jquery.waitforimages.min.js",
             "~/Scripts/ImagePopUp.js"));
            //"~/Scripts/ColorPickerCanvas.js"));//ST-1860

            bundles.Add(new StyleBundle("~/Content/ImagePopUp").Include(
                 "~/Content/ImagePopUp.css"
             ));

            bundles.Add(new StyleBundle("~/Content/PagePopUp").Include(
                 "~/Content/FB/flipbook.css"
             ));

            bundles.Add(new ScriptBundle("~/Scripts/ColorPickerCanvas").Include(
              "~/Scripts/ColorPickerCanvas.js"));



            bundles.Add(new StyleBundle("~/Content/Flipbook_Workspace").Include(
                    "~/Content/font-awesome.css",
                    "~/Content/bootstrap.min.css",
                    "~/Content/FB/newflip.css",
                    "~/Content/FB/FBMenu.css",
                    "~/Content/bootstrap-multiselect.css",
                     "~/Content/FB/responsive.css"
             ));


            bundles.Add(new ScriptBundle("~/Scripts/Flipbook_Workspace").Include(
            "~/Scripts/jquery.min.js",
            "~/Scripts/bootstrap.min.js",
            "~/Scripts/bootstrap-multiselect.min.js",
            "~/Scripts/jquery.maskedinput/jquery.maskedinput.1.4.1.js",
            //"~/Scripts/Flipbook/flipbooklayout.js",
            "~/Scripts/FlipBook/flipbook_workspace.js",
            "~/Scripts/FlipBook/fbcommon.js",
            "~/Scripts/Common.js"));


            bundles.Add(new StyleBundle("~/Content/FlipBookLayout").Include(
                 "~/Content/bootstrap.min.css",
                 "~/Content/font-awesome.css",
                 "~/Content/toolset.css",
                 "~/Content/FB/TextEditor.css",
                 "~/Content/FB/newflip.css",
                 "~/Content/FB/FBMenu.css",
                 "~/ckeditor/samples/css/samples.css",                 
                 "~/Scripts/Crop/cropper.css",
                 "~/Content/jquery.spellchecker.css",
                 "~/Content/FB/flipbooklayout.css"
             , "~/Content/FB/responsive.css"
             ));


            bundles.Add(new ScriptBundle("~/Scripts/FlipBookLayout").Include(

            "~/Scripts/jquery.min.js",
            "~/Scripts/jquery.scrollbar.js",
            "~/Scripts/bootstrap.min.js",
            //"~/Scripts/scrollBar.js",
            //"~/ckeditor/ckeditor.js",
            "~/Scripts/Common.js",


            "~/Scripts/html2canvas.js",
            "~/Scripts/canvas-toBlob.js",
            "~/Scripts/Spell/jquery.spellchecker.js",
            "~/Scripts/caman.full.min.js",
            "~/Scripts/Flipbook/flipbooklayout.js",
            "~/Scripts/modernizr.custom.js",
            "~/Scripts/jquerypp.custom.js",
            "~/scripts/flipbook/flipbookimages.js",
            "~/scripts/flipbook/EndPapers.js",
            "~/Scripts/jquery.bookblock.js",
            "~/Scripts/FlipBook/flipBook.js",            
            "~/Scripts/Crop/cropper.js",            
            "~/Scripts/Crop/main.js",
            "~/Scripts/FlipBook/fbcommon.js"
            ));

            var Cbundle = new ScriptBundle("~/Scripts/Ckeditor").Include(
            "~/ckeditor/ckeditor.js"
            );
            bundles.Add(Cbundle);

            bundles.Add(new ScriptBundle("~/Scripts/PublishedFB").Include(
               "~/Scripts/jquery.min.js",
               "~/Flipbooks/PreviewAssets/extras/modernizr.2.5.3.min.js",
               "~/Flipbooks/PreviewAssets/lib/hash.js",
               "~/Scripts/libs/fontawesome-iconpicker.min.js"
              ));
            
            bundles.Add(new StyleBundle("~/Content/Preview").Include(
                "~/Content/FB/TextEditor.css"
                , "~/Content/FB/flipbooklayout.css"
                , "~/Content/FB/flipbook.css"
                , "~/Content/FB/newflip.css"
                , "~/Content/FB/Publish.css"
                , "~/Content/FB/flippage.css"
                , "~/Content/bootstrap.min.css"
                , "~/Content/ImagePopUp.css"
                ));


            bundles.Add(new ScriptBundle("~/Scripts/Preview").Include(
                "~/Scripts/jquery-1.10.2.min.js"
                ,"~/Scripts/jquery-ui.min.js"
                , "~/Scripts/flip/jquery.mousewheel.min.js"
                , "~/Scripts/flip/modernizr.2.5.3.min.js"
                , "~/Scripts/flip/hash.js"
                , "~/Scripts/bootstrap.min.js"
                , "~/Scripts/FlipBook/fbcommon.js"
                , "~/Scripts/Common.js"
                ));

            #endregion


            #region Resume Wizard

           bundles.Add(new StyleBundle("~/Content/ResumeWizardCSS").Include(
                       "~/Content/bootstrap.min.css",
                      "~/Content/rwizard.css",
                      "~/Content/jquery.spellchecker.css",
                      "~/Content/scrollBar.css"
                      ));

            //-ST-1859
            bundles.Add(new StyleBundle("~/Content/GlobalFormating").Include(                       
                      "~/Content/font-awesome.css",
                      "~/Scripts/colorpick/spectrum.css"));

            bundles.Add(new StyleBundle("~/Content/ResumeWizard/FilterTemplate").Include(
                "~/Content/glisseZoom.css",
                "~/Content/slick.css",
              "~/Content/jquery-ui.css"));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/CommunityService").Include(
                   "~/Scripts/ResumeWizard/RWPreviewCommon.js",
                  "~/Scripts/ResumeWizard/CommunityService.js",
                  "~/Scripts/ResumeWizard/AffiliationAndAccreditation.js"
                  ));

            //-ST-1859
            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizardJS").Include(
                  "~/Scripts/jquery.min.js",
                  "~/Scripts/bootstrap.min.js",
                  "~/Scripts/slick.min.js",
                  "~/Scripts/rwizard.js",
                  "~/Scripts/Common.js",
                  "~/Scripts/ResumeWizard/flip.js",
                  "~/Scripts/ResumeWizard/ResumeWizardMenu.js",
                  "~/Scripts/ResumeWizard/PublishResumeWizard.js",
                  "~/Scripts/ResumeWizard/publishresumecommon.js",
                  //"~/Scripts/FlipBook/fbcommon.js",
                  "~/Scripts/Spell/jquery.spellchecker.js",
                  "~/Scripts/scrollBar.js"));
            //ST-1859
            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/YourResumes").Include(
                 //"~/Scripts/jquery.min.js",
                    "~/Scripts/ResumeWizard/YourResumes.js",
                   "~/Scripts/ResumeWizard/RWPreviewCommon.js"                   
                   ));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/WorkExperience").Include(
                   "~/Scripts/ResumeWizard/WorkExperience.js",
                   "~/Scripts/ResumeWizard/RWPreviewCommon.js",
                   "~/Scripts/select/bootstrap-select.js",
                   "~/Scripts/jquery-ui.js"));

            bundles.Add(new StyleBundle("~/Content/ResumeWizard/WorkExperience").Include(
                  "~/Content/jquery-ui.css"
              ));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/WorkAuthorizations").Include(
                  "~/Scripts/ResumeWizard/WorkAuthorizations.js",
                  "~/Scripts/ResumeWizard/RWPreviewCommon.js",
                  "~/Scripts/ResumeWizard/RWPreviewHTMLGenerater.js",
                  "~/Scripts/select/bootstrap-select.js",
                  "~/Scripts/scroller/jquery.mCustomScrollbar.js"));

            bundles.Add(new StyleBundle("~/Content/ResumeWizard/WorkAuthorizations").Include(
                  "~/Scripts/select/bootstrap-select.css"
              ));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/SkillSet").Include(
                  "~/Scripts/ResumeWizard/RWPreviewCommon.js",
                  "~/Scripts/ResumeWizard/SkillSet.js",
                  "~/Scripts/scroller/jquery.mCustomScrollbar.js",
                  "~/Scripts/cicleprogressbar/jQuery-plugin-progressbar.js",
                  "~/Scripts/select/bootstrap-select.js"));

            bundles.Add(new StyleBundle("~/Content/ResumeWizard/SkillSet").Include(
                  "~/Scripts/scroller/jquery.mCustomScrollbar.css",
                  "~/Content/RW/chart-circle.css",
                  "~/Content/displayformat.css",
                  "~/Scripts/cicleprogressbar/jQuery-plugin-progressbar.css",
                  "~/Scripts/select/bootstrap-select.css"
              ));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/Research").Include(
                  "~/Scripts/ResumeWizard/Research.js",
                  "~/Scripts/ResumeWizard/RWPreviewCommon.js",
                  "~/Scripts/ResumeWizard/RWPreviewHTMLGenerater.js",
                   "~/Scripts/select/bootstrap-select.js"));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/Publications").Include(
                 "~/Scripts/ResumeWizard/Publications.js",
                 "~/Scripts/ResumeWizard/RWPreviewCommon.js",
                 "~/Scripts/ResumeWizard/RWPreviewHTMLGenerater.js",
                   "~/Scripts/select/bootstrap-select.js"));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/ProfessionalAwards").Include(
                 "~/Scripts/ResumeWizard/ProfessionalAwards.js",
                 "~/Scripts/ResumeWizard/RWPreviewCommon.js",
                 "~/Scripts/ResumeWizard/RWPreviewHTMLGenerater.js",
                   "~/Scripts/select/bootstrap-select.js"));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/PersonalityTraits").Include(
                 "~/Scripts/ResumeWizard/PersonalityTraits.js",
                 "~/Scripts/ResumeWizard/RWPreviewCommon.js",
                 "~/Scripts/ResumeWizard/RWPreviewHTMLGenerater.js",
                 "~/Scripts/scroller/jquery.mCustomScrollbar.js",
                   "~/Scripts/select/bootstrap-select.js"));

            bundles.Add(new StyleBundle("~/Content/ResumeWizard/PersonalityTraits").Include(
                 "~/Scripts/scroller/jquery.mCustomScrollbar.css"
             ));

            bundles.Add(new StyleBundle("~/Content/PersonalInfoStyle").Include(
                 "~/Scripts/countrydropdown/cdd.css"
                 , "~/Content/RW/autocomplete.css"
             ));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/PersonalInfo").Include(
                 "~/Scripts/ResumeWizard/PersonalInfo.js",
                 "~/Scripts/ResumeWizard/RWPreviewCommon.js"));//,
                                                               //"~/Scripts/select/bootstrap-select.js"

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/Patents").Include(
                "~/Scripts/ResumeWizard/Patents.js",
                "~/Scripts/ResumeWizard/RWPreviewCommon.js",
                "~/Scripts/ResumeWizard/RWPreviewHTMLGenerater.js",
                   "~/Scripts/select/bootstrap-select.js"));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/OrderOfAchievements").Include(
                "~/Scripts/ResumeWizard/OrderOfAchievements.js",
                "~/Scripts/ResumeWizard/RWPreviewCommon.js"));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/ObjectivesAndSkillSummary").Include(
                "~/Scripts/ResumeWizard/ObjectivesAndSkillSummary.js",
                "~/Scripts/ResumeWizard/RWPreviewCommon.js",
                   "~/Scripts/select/bootstrap-select.js"));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/MilitaryService").Include(
                "~/Scripts/ResumeWizard/MilitaryService.js",
                "~/Scripts/ResumeWizard/RWPreviewCommon.js",
                "~/Scripts/ResumeWizard/RWPreviewHTMLGenerater.js",
                   "~/Scripts/select/bootstrap-select.js"));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/Education").Include(
               "~/Scripts/ResumeWizard/Education.js",
               "~/Scripts/scroller/jquery.mCustomScrollbar.js",
               "~/Scripts/select/bootstrap-select.js",
               "~/Scripts/ResumeWizard/RWPreviewCommon.js",
               "~/Scripts/jquery-ui.js"));

            bundles.Add(new StyleBundle("~/Content/ResumeWizard/Education").Include(
                  "~/Content/jquery-ui.css"
              ));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/AddImages").Include(
               "~/Scripts/ResumeWizard/AddImages.js",
               "~/Scripts/ResumeWizard/RWPreviewCommon.js",
               "~/Scripts/caman.full.min.js"
               ));

            bundles.Add(new StyleBundle("~/Content/AddImages").Include(
               "~/Content/slick.css",
               "~/Content/seiyria-bootstrap-slider.css",
               //"~/Content/croppie.css",
               "~/Content/jquery-ui.min.css"));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/ChooseTemplate").Include(
                "~/Scripts/ResumeWizard/ChooseTemplate.js"));

            bundles.Add(new StyleBundle("~/Content/ResumeWizard/ChooseTemplate").Include(
                 "~/Content/slick.css"
             ));

            bundles.Add(new StyleBundle("~/Content/SelectPicker").Include(
                 "~/Scripts/select/bootstrap-select.css"
             ));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/Athletics").Include(
                "~/Scripts/ResumeWizard/Athletics.js",
                "~/Scripts/ResumeWizard/RWPreviewCommon.js",
                "~/Scripts/ResumeWizard/RWPreviewHTMLGenerater.js",
                   "~/Scripts/select/bootstrap-select.js"));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/AffiliationAndAccreditation").Include(
                "~/Scripts/ResumeWizard/AffiliationAndAccreditation.js",
                "~/Scripts/ResumeWizard/RWPreviewCommon.js",
                   "~/Scripts/select/bootstrap-select.js",
                   "~/Scripts/jquery-ui.js"));

            bundles.Add(new StyleBundle("~/Content/AffiliationAndAccreditation").Include(
                 "~/Scripts/select/bootstrap-select.css",
                 "~/Content/jquery-ui.css"
             ));
            //ST-1859
            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/ChooseTemplate").Include(
               //"~/Scripts/jquery.min.js",
               "~/Scripts/ResumeWizard/ChooseTemplate.js",
               "~/Scripts/html2canvas.js"));

            bundles.Add(new ScriptBundle("~/Scripts/Swap/SwapTemplate").Include(
              "~/Scripts/TWPlugin.js",
              "~/Scripts/puffEffect.js",
               "~/Scripts/wingfly.js",
                "~/Scripts/scroller/jquery.mCustomScrollbar.js",
              "~/Scripts/Swap/SwapTemplate.js"));

            bundles.Add(new StyleBundle("~/Content/Swap/SwapTemplate").Include(
                 "~/Content/bootstrap.min.css",
                 "~/Content/rwizard.css",
                 "~/Content/jquery.spellchecker.css",
                 "~/Scripts/scroller/jquery.mCustomScrollbar.css"
             ));
            bundles.Add(new ScriptBundle("~/Scripts/AutoFill/AutoFill").Include(
               "~/Scripts/scroller/jquery.mCustomScrollbar.js",
             "~/Scripts/Swap/AutoFill.js",
                "~/Scripts/ResumeWizard/RWPreviewCommon.js"));

            bundles.Add(new StyleBundle("~/Content/AutoFill/AutoFill").Include(
                 "~/Content/bootstrap.min.css",
                 "~/Content/rwizard.css",
                 "~/Content/jquery.spellchecker.css",
                 "~/Scripts/scroller/jquery.mCustomScrollbar.css"
             ));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/FilterTemplate").Include(
                "~/scripts/resumewizard/FilterTemplate.js",
              "~/Scripts/glisse.js"));
            
            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/UserAddedCategory").Include(
                "~/Scripts/ResumeWizard/UserAddedCategory.js",
                "~/Scripts/ResumeWizard/RWPreviewCommon.js",
                   "~/Scripts/select/bootstrap-select.js"));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/GlobalFormating").Include(
                 "~/Scripts/ResumeWizard/GlobalFormating.js",
                 "~/Scripts/ResumeWizard/RWPreviewCommon.js",
                 "~/Scripts/select/bootstrap-select.js",
                 "~/Scripts/colorpick/spectrum.js"));//ST-1859

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/SpellCheck").Include(
               "~/Scripts/ResumeWizard/SpellCheck.js",
               "~/Scripts/ResumeWizard/RWPreviewCommon.js"));

            bundles.Add(new ScriptBundle("~/Scripts/ResumeWizard/Publish").Include(
               "~/Scripts/ResumeWizard/Publish.js",
               "~/Scripts/ResumeWizard/RWPreviewCommon.js"));

            #endregion

            #region '  Admin   '

            bundles.Add(new ScriptBundle("~/Scripts/FlipBookAdminEP").Include(
               "~/Scripts/jquery.min.js"
               ,"~/Scripts/Datatable/jquery.dataTables.js"
               , "~/Scripts/bootstrap.min.js"
               , "~/Scripts/FlipBook/ManageEndPapers.js"));

            bundles.Add(new StyleBundle("~/Content/FlipBookAdminEP").Include(
               "~/Content/bootstrap.min.css"
               ,"~/Content/jquery.dataTables.css"
               , "~/Content/fb/manageendpaper.css"));
            

            #endregion

        }
    }
}
