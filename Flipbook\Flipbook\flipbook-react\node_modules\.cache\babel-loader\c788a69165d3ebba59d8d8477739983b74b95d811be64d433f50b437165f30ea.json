{"ast": null, "code": "var _jsxFileName = \"D:\\\\TradeWorks\\\\Flipbook\\\\Flipbook\\\\Flipbook\\\\flipbook-react\\\\src\\\\components\\\\LandingPage\\\\Header.tsx\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HeaderContainer = styled.header`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: relative;\n  z-index: 100;\n`;\n_c = HeaderContainer;\nconst LeftSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 30px;\n`;\n_c2 = LeftSection;\nconst Logo = styled.div`\n  font-family: 'Brush Script MT', cursive;\n  font-size: 28px;\n  font-weight: bold;\n  color: white;\n  text-decoration: none;\n  cursor: pointer;\n  \n  &:hover {\n    opacity: 0.9;\n  }\n`;\n_c3 = Logo;\nconst Navigation = styled.nav`\n  display: flex;\n  gap: 25px;\n`;\n_c4 = Navigation;\nconst NavItem = styled.a`\n  color: white;\n  text-decoration: none;\n  font-size: 14px;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  padding: 8px 12px;\n  border-radius: 4px;\n  transition: background-color 0.3s ease;\n  cursor: pointer;\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n_c5 = NavItem;\nconst RightSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 15px;\n`;\n_c6 = RightSection;\nconst IconButton = styled.button`\n  background: none;\n  border: none;\n  color: white;\n  font-size: 18px;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 4px;\n  transition: background-color 0.3s ease;\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n_c7 = IconButton;\nconst UserSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 10px;\n`;\n_c8 = UserSection;\nconst UserAvatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 14px;\n  color: white;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n\n  &:hover {\n    transform: scale(1.1);\n  }\n`;\n_c9 = UserAvatar;\nconst UserInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n`;\n_c0 = UserInfo;\nconst SignInText = styled.span`\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n`;\n_c1 = SignInText;\nconst JoinUsText = styled.span`\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.7);\n`;\n_c10 = JoinUsText;\nconst Header = () => {\n  return /*#__PURE__*/_jsxDEV(HeaderContainer, {\n    children: [/*#__PURE__*/_jsxDEV(LeftSection, {\n      children: [/*#__PURE__*/_jsxDEV(Logo, {\n        children: \"Flipbook\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Navigation, {\n        children: [/*#__PURE__*/_jsxDEV(NavItem, {\n          href: \"#file\",\n          children: \"FILE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(NavItem, {\n          href: \"#edit\",\n          children: \"EDIT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(NavItem, {\n          href: \"#share\",\n          children: \"SHARE!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(NavItem, {\n          href: \"#help\",\n          children: \"HELP\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RightSection, {\n      children: [/*#__PURE__*/_jsxDEV(IconButton, {\n        title: \"Tools\",\n        children: \"\\uD83D\\uDD27\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(UserSection, {\n        children: [/*#__PURE__*/_jsxDEV(UserAvatar, {\n          children: \"FR\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(UserInfo, {\n          children: [/*#__PURE__*/_jsxDEV(SignInText, {\n            children: \"Sign In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(JoinUsText, {\n            children: \"Join Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 5\n  }, this);\n};\n_c11 = Header;\nexport default Header;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11;\n$RefreshReg$(_c, \"HeaderContainer\");\n$RefreshReg$(_c2, \"LeftSection\");\n$RefreshReg$(_c3, \"Logo\");\n$RefreshReg$(_c4, \"Navigation\");\n$RefreshReg$(_c5, \"NavItem\");\n$RefreshReg$(_c6, \"RightSection\");\n$RefreshReg$(_c7, \"IconButton\");\n$RefreshReg$(_c8, \"UserSection\");\n$RefreshReg$(_c9, \"UserAvatar\");\n$RefreshReg$(_c0, \"UserInfo\");\n$RefreshReg$(_c1, \"SignInText\");\n$RefreshReg$(_c10, \"JoinUsText\");\n$RefreshReg$(_c11, \"Header\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header", "_c", "LeftSection", "div", "_c2", "Logo", "_c3", "Navigation", "nav", "_c4", "NavItem", "a", "_c5", "RightSection", "_c6", "IconButton", "button", "_c7", "UserSection", "_c8", "UserAvatar", "_c9", "UserInfo", "_c0", "SignInText", "span", "_c1", "JoinUsText", "_c10", "Header", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "title", "_c11", "$RefreshReg$"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/LandingPage/Header.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../hooks/useAuth';\n\nconst HeaderContainer = styled.header`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: relative;\n  z-index: 100;\n`;\n\nconst LeftSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 30px;\n`;\n\nconst Logo = styled.div`\n  font-family: 'Brush Script MT', cursive;\n  font-size: 28px;\n  font-weight: bold;\n  color: white;\n  text-decoration: none;\n  cursor: pointer;\n  \n  &:hover {\n    opacity: 0.9;\n  }\n`;\n\nconst Navigation = styled.nav`\n  display: flex;\n  gap: 25px;\n`;\n\nconst NavItem = styled.a`\n  color: white;\n  text-decoration: none;\n  font-size: 14px;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  padding: 8px 12px;\n  border-radius: 4px;\n  transition: background-color 0.3s ease;\n  cursor: pointer;\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n\nconst RightSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 15px;\n`;\n\nconst IconButton = styled.button`\n  background: none;\n  border: none;\n  color: white;\n  font-size: 18px;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 4px;\n  transition: background-color 0.3s ease;\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n\nconst UserSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 10px;\n`;\n\nconst UserAvatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 14px;\n  color: white;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n\n  &:hover {\n    transform: scale(1.1);\n  }\n`;\n\nconst UserInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n`;\n\nconst SignInText = styled.span`\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n`;\n\nconst JoinUsText = styled.span`\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.7);\n`;\n\nconst Header: React.FC = () => {\n  return (\n    <HeaderContainer>\n      <LeftSection>\n        <Logo>Flipbook</Logo>\n        <Navigation>\n          <NavItem href=\"#file\">FILE</NavItem>\n          <NavItem href=\"#edit\">EDIT</NavItem>\n          <NavItem href=\"#share\">SHARE!</NavItem>\n          <NavItem href=\"#help\">HELP</NavItem>\n        </Navigation>\n      </LeftSection>\n      \n      <RightSection>\n        <IconButton title=\"Tools\">\n          🔧\n        </IconButton>\n        \n        <UserSection>\n          <UserAvatar>\n            FR\n          </UserAvatar>\n          <UserInfo>\n            <SignInText>Sign In</SignInText>\n            <JoinUsText>Join Us</JoinUsText>\n          </UserInfo>\n        </UserSection>\n      </RightSection>\n    </HeaderContainer>\n  );\n};\n\nexport default Header;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAoB,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGvC,MAAMC,eAAe,GAAGH,MAAM,CAACI,MAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAVIF,eAAe;AAYrB,MAAMG,WAAW,GAAGN,MAAM,CAACO,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,WAAW;AAMjB,MAAMG,IAAI,GAAGT,MAAM,CAACO,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAXID,IAAI;AAaV,MAAME,UAAU,GAAGX,MAAM,CAACY,GAAG;AAC7B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,UAAU;AAKhB,MAAMG,OAAO,GAAGd,MAAM,CAACe,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,OAAO;AAiBb,MAAMG,YAAY,GAAGjB,MAAM,CAACO,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAJID,YAAY;AAMlB,MAAME,UAAU,GAAGnB,MAAM,CAACoB,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAbIF,UAAU;AAehB,MAAMG,WAAW,GAAGtB,MAAM,CAACO,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GAJID,WAAW;AAMjB,MAAME,UAAU,GAAGxB,MAAM,CAACO,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GAjBID,UAAU;AAmBhB,MAAME,QAAQ,GAAG1B,MAAM,CAACO,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GAJID,QAAQ;AAMd,MAAME,UAAU,GAAG5B,MAAM,CAAC6B,IAAI;AAC9B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,UAAU;AAMhB,MAAMG,UAAU,GAAG/B,MAAM,CAAC6B,IAAI;AAC9B;AACA;AACA,CAAC;AAACG,IAAA,GAHID,UAAU;AAKhB,MAAME,MAAgB,GAAGA,CAAA,KAAM;EAC7B,oBACE/B,OAAA,CAACC,eAAe;IAAA+B,QAAA,gBACdhC,OAAA,CAACI,WAAW;MAAA4B,QAAA,gBACVhC,OAAA,CAACO,IAAI;QAAAyB,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrBpC,OAAA,CAACS,UAAU;QAAAuB,QAAA,gBACThC,OAAA,CAACY,OAAO;UAACyB,IAAI,EAAC,OAAO;UAAAL,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eACpCpC,OAAA,CAACY,OAAO;UAACyB,IAAI,EAAC,OAAO;UAAAL,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eACpCpC,OAAA,CAACY,OAAO;UAACyB,IAAI,EAAC,QAAQ;UAAAL,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eACvCpC,OAAA,CAACY,OAAO;UAACyB,IAAI,EAAC,OAAO;UAAAL,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEdpC,OAAA,CAACe,YAAY;MAAAiB,QAAA,gBACXhC,OAAA,CAACiB,UAAU;QAACqB,KAAK,EAAC,OAAO;QAAAN,QAAA,EAAC;MAE1B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbpC,OAAA,CAACoB,WAAW;QAAAY,QAAA,gBACVhC,OAAA,CAACsB,UAAU;UAAAU,QAAA,EAAC;QAEZ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpC,OAAA,CAACwB,QAAQ;UAAAQ,QAAA,gBACPhC,OAAA,CAAC0B,UAAU;YAAAM,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChCpC,OAAA,CAAC6B,UAAU;YAAAG,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEtB,CAAC;AAACG,IAAA,GA9BIR,MAAgB;AAgCtB,eAAeA,MAAM;AAAC,IAAA5B,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,IAAA,EAAAS,IAAA;AAAAC,YAAA,CAAArC,EAAA;AAAAqC,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAxB,GAAA;AAAAwB,YAAA,CAAArB,GAAA;AAAAqB,YAAA,CAAAnB,GAAA;AAAAmB,YAAA,CAAAjB,GAAA;AAAAiB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAV,IAAA;AAAAU,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}