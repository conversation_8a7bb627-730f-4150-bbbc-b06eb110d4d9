{"ast": null, "code": "import _objectSpread from\"D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useRef,useEffect}from'react';import'./TextEditor.css';import{jsx as _jsx}from\"react/jsx-runtime\";const TextEditor=_ref=>{let{value,onChange,placeholder='Enter text...',isActive,onFocus,onBlur,className='',style={},onSelectionChange,onCommandReceived,pendingFormatting,onFormattingApplied}=_ref;const[currentFormatting,setCurrentFormatting]=useState({fontFamily:'Arial',fontSize:'14',bold:false,italic:false,underline:false,textAlign:'left',color:'#000000',backgroundColor:'transparent',lineHeight:'1.4',letterSpacing:'0'});const editorRef=useRef(null);const[lastInputLength,setLastInputLength]=useState(0);useEffect(()=>{if(isActive&&editorRef.current){editorRef.current.focus();}},[isActive]);// Listen for external formatting commands\nuseEffect(()=>{if(isActive&&onCommandReceived){// This component will receive commands from the global toolbar\n// Commands are applied via execCommand in the parent component\n}},[isActive,onCommandReceived]);// Expose applyCommand method to parent\nconst applyCommand=(command,value)=>{if(!editorRef.current)return;editorRef.current.focus();switch(command){case'bold':case'italic':case'underline':document.execCommand(command,false);break;case'fontSize':// Apply font size via CSS\nconst selection=window.getSelection();if(selection&&selection.rangeCount>0){const range=selection.getRangeAt(0);if(!range.collapsed){const span=document.createElement('span');span.style.fontSize=value+'pt';try{range.surroundContents(span);}catch(e){// Fallback to execCommand if surroundContents fails\ndocument.execCommand('fontSize',false,'7');// Then apply CSS size\nconst selectedElements=editorRef.current.querySelectorAll('font[size=\"7\"]');selectedElements.forEach(el=>{el.style.fontSize=value+'pt';});}}}break;case'fontName':case'foreColor':case'backColor':case'justifyLeft':case'justifyCenter':case'justifyRight':case'justifyFull':case'insertUnorderedList':case'insertOrderedList':case'createLink':document.execCommand(command,false,value);break;}// Update current formatting state\nsetTimeout(()=>updateFormattingState(),10);};useEffect(()=>{if(isActive&&editorRef.current){editorRef.current.applyCommand=applyCommand;}},[isActive]);const handleTextChange=e=>{const content=e.currentTarget.innerHTML;// Use innerHTML to preserve formatting\nconst textContent=e.currentTarget.innerText;const currentLength=textContent.length;// Check if user is typing new text (not deleting/cutting)\nif(pendingFormatting&&currentLength>lastInputLength){applyPendingFormattingToNewText();}setLastInputLength(currentLength);onChange(textContent);// Still use innerText for the actual content\n};const handleFocus=()=>{onFocus===null||onFocus===void 0?void 0:onFocus();updateFormattingState();};const handleBlur=e=>{onBlur===null||onBlur===void 0?void 0:onBlur(e);};const applyPendingFormattingToNewText=()=>{if(!pendingFormatting||!editorRef.current)return;const selection=window.getSelection();if(!selection||selection.rangeCount===0)return;const range=selection.getRangeAt(0);if(range.collapsed)return;// No text selected\n// Apply pending formatting to the newly typed text\nconst span=document.createElement('span');// Apply formatting styles\nif(pendingFormatting.bold)span.style.fontWeight='bold';if(pendingFormatting.italic)span.style.fontStyle='italic';if(pendingFormatting.underline)span.style.textDecoration='underline';if(pendingFormatting.fontSize)span.style.fontSize=pendingFormatting.fontSize+'pt';if(pendingFormatting.fontFamily)span.style.fontFamily=pendingFormatting.fontFamily;if(pendingFormatting.color)span.style.color=pendingFormatting.color;if(pendingFormatting.backgroundColor&&pendingFormatting.backgroundColor!=='transparent'){span.style.backgroundColor=pendingFormatting.backgroundColor;}try{range.surroundContents(span);onFormattingApplied===null||onFormattingApplied===void 0?void 0:onFormattingApplied();// Clear pending formatting\n}catch(e){console.warn('Could not apply formatting to new text:',e);}};const updateFormattingState=()=>{if(!editorRef.current)return;const selection=window.getSelection();if(!selection||selection.rangeCount===0)return;// Only update formatting state if we don't have pending formatting from toolbar\nif(!pendingFormatting){const formatting={fontFamily:document.queryCommandValue('fontName')||'Arial',fontSize:document.queryCommandValue('fontSize')||'14',bold:document.queryCommandState('bold'),italic:document.queryCommandState('italic'),underline:document.queryCommandState('underline'),textAlign:document.queryCommandState('justifyCenter')?'center':document.queryCommandState('justifyRight')?'right':document.queryCommandState('justifyFull')?'justify':'left',color:document.queryCommandValue('foreColor')||'#000000',backgroundColor:document.queryCommandValue('backColor')||'transparent',lineHeight:'1.4',letterSpacing:'0'};setCurrentFormatting(formatting);onSelectionChange===null||onSelectionChange===void 0?void 0:onSelectionChange(formatting);}};const handleSelectionChange=()=>{if(isActive&&!pendingFormatting){updateFormattingState();}};useEffect(()=>{document.addEventListener('selectionchange',handleSelectionChange);return()=>{document.removeEventListener('selectionchange',handleSelectionChange);};},[isActive,pendingFormatting]);// Update current length when value changes\nuseEffect(()=>{setLastInputLength(value.length);},[value]);const editorStyle=_objectSpread(_objectSpread({},style),{},{lineHeight:currentFormatting.lineHeight,letterSpacing:currentFormatting.letterSpacing});// Update editor content when value changes\nuseEffect(()=>{if(editorRef.current&&editorRef.current.innerHTML!==value){const currentSelection=window.getSelection();const currentRange=currentSelection&&currentSelection.rangeCount>0?currentSelection.getRangeAt(0):null;editorRef.current.innerHTML=value||'';// Restore selection if it existed\nif(currentRange&&currentSelection){try{currentSelection.removeAllRanges();currentSelection.addRange(currentRange);}catch(e){// Selection restore failed, place cursor at end\nconst range=document.createRange();range.selectNodeContents(editorRef.current);range.collapse(false);currentSelection.removeAllRanges();currentSelection.addRange(range);}}}},[value]);return/*#__PURE__*/_jsx(\"div\",{className:\"text-editor-container \".concat(className),children:/*#__PURE__*/_jsx(\"div\",{ref:editorRef,className:\"text-editor \".concat(isActive?'active':''),contentEditable:true,suppressContentEditableWarning:true,onInput:handleTextChange,onFocus:handleFocus,onBlur:handleBlur,style:editorStyle,\"data-placeholder\":placeholder})});};export default TextEditor;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsx", "_jsx", "TextEditor", "_ref", "value", "onChange", "placeholder", "isActive", "onFocus", "onBlur", "className", "style", "onSelectionChange", "onCommandReceived", "pendingFormatting", "onFormattingApplied", "currentFormatting", "setCurrentFormatting", "fontFamily", "fontSize", "bold", "italic", "underline", "textAlign", "color", "backgroundColor", "lineHeight", "letterSpacing", "editor<PERSON><PERSON>", "lastInputLength", "setLastInputLength", "current", "focus", "applyCommand", "command", "document", "execCommand", "selection", "window", "getSelection", "rangeCount", "range", "getRangeAt", "collapsed", "span", "createElement", "surroundContents", "e", "selectedElements", "querySelectorAll", "for<PERSON>ach", "el", "setTimeout", "updateFormattingState", "handleTextChange", "content", "currentTarget", "innerHTML", "textContent", "innerText", "<PERSON><PERSON><PERSON><PERSON>", "length", "applyPendingFormattingToNewText", "handleFocus", "handleBlur", "fontWeight", "fontStyle", "textDecoration", "console", "warn", "formatting", "queryCommandValue", "queryCommandState", "handleSelectionChange", "addEventListener", "removeEventListener", "editor<PERSON><PERSON><PERSON>", "_objectSpread", "currentSelection", "currentRange", "removeAllRanges", "addRange", "createRange", "selectNodeContents", "collapse", "concat", "children", "ref", "contentEditable", "suppressContentEditableWarning", "onInput"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/TextEditor/TextEditor.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './TextEditor.css';\n\ninterface TextEditorProps {\n  value: string;\n  onChange: (value: string) => void;\n  placeholder?: string;\n  isActive: boolean;\n  onFocus?: () => void;\n  onBlur?: (event?: React.FocusEvent) => void;\n  className?: string;\n  style?: React.CSSProperties;\n  onSelectionChange?: (formatting: any) => void;\n  onCommandReceived?: (command: string, value?: string) => void;\n  pendingFormatting?: any;\n  onFormattingApplied?: () => void;\n}\n\ninterface TextFormat {\n  bold: boolean;\n  italic: boolean;\n  underline: boolean;\n  fontSize: number;\n  fontFamily: string;\n  color: string;\n  align: 'left' | 'center' | 'right' | 'justify';\n}\n\nconst TextEditor: React.FC<TextEditorProps> = ({\n  value,\n  onChange,\n  placeholder = 'Enter text...',\n  isActive,\n  onFocus,\n  onBlur,\n  className = '',\n  style = {},\n  onSelectionChange,\n  onCommandReceived,\n  pendingFormatting,\n  onFormattingApplied\n}) => {\n  const [currentFormatting, setCurrentFormatting] = useState({\n    fontFamily: 'Arial',\n    fontSize: '14',\n    bold: false,\n    italic: false,\n    underline: false,\n    textAlign: 'left' as 'left' | 'center' | 'right' | 'justify',\n    color: '#000000',\n    backgroundColor: 'transparent',\n    lineHeight: '1.4',\n    letterSpacing: '0'\n  });\n  \n  const editorRef = useRef<HTMLDivElement>(null);\n  const [lastInputLength, setLastInputLength] = useState(0);\n\n  useEffect(() => {\n    if (isActive && editorRef.current) {\n      editorRef.current.focus();\n    }\n  }, [isActive]);\n\n  // Listen for external formatting commands\n  useEffect(() => {\n    if (isActive && onCommandReceived) {\n      // This component will receive commands from the global toolbar\n      // Commands are applied via execCommand in the parent component\n    }\n  }, [isActive, onCommandReceived]);\n\n  // Expose applyCommand method to parent\n  const applyCommand = (command: string, value?: string) => {\n    if (!editorRef.current) return;\n    \n    editorRef.current.focus();\n    \n    switch (command) {\n      case 'bold':\n      case 'italic':\n      case 'underline':\n        document.execCommand(command, false);\n        break;\n      case 'fontSize':\n        // Apply font size via CSS\n        const selection = window.getSelection();\n        if (selection && selection.rangeCount > 0) {\n          const range = selection.getRangeAt(0);\n          if (!range.collapsed) {\n            const span = document.createElement('span');\n            span.style.fontSize = value + 'pt';\n            try {\n              range.surroundContents(span);\n            } catch (e) {\n              // Fallback to execCommand if surroundContents fails\n              document.execCommand('fontSize', false, '7');\n              // Then apply CSS size\n              const selectedElements = editorRef.current.querySelectorAll('font[size=\"7\"]');\n              selectedElements.forEach(el => {\n                (el as HTMLElement).style.fontSize = value + 'pt';\n              });\n            }\n          }\n        }\n        break;\n      case 'fontName':\n      case 'foreColor':\n      case 'backColor':\n      case 'justifyLeft':\n      case 'justifyCenter':\n      case 'justifyRight':\n      case 'justifyFull':\n      case 'insertUnorderedList':\n      case 'insertOrderedList':\n      case 'createLink':\n        document.execCommand(command, false, value);\n        break;\n    }\n    \n    // Update current formatting state\n    setTimeout(() => updateFormattingState(), 10);\n  };\n\n  useEffect(() => {\n    if (isActive && editorRef.current) {\n      (editorRef.current as any).applyCommand = applyCommand;\n    }\n  }, [isActive]);\n\n  const handleTextChange = (e: React.FormEvent<HTMLDivElement>) => {\n    const content = e.currentTarget.innerHTML; // Use innerHTML to preserve formatting\n    const textContent = e.currentTarget.innerText;\n    const currentLength = textContent.length;\n    \n    // Check if user is typing new text (not deleting/cutting)\n    if (pendingFormatting && currentLength > lastInputLength) {\n      applyPendingFormattingToNewText();\n    }\n    \n    setLastInputLength(currentLength);\n    onChange(textContent); // Still use innerText for the actual content\n  };\n\n  const handleFocus = () => {\n    onFocus?.();\n    updateFormattingState();\n  };\n\n  const handleBlur = (e: React.FocusEvent) => {\n    onBlur?.(e);\n  };\n\n  const applyPendingFormattingToNewText = () => {\n    if (!pendingFormatting || !editorRef.current) return;\n    \n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    \n    const range = selection.getRangeAt(0);\n    if (range.collapsed) return; // No text selected\n    \n    // Apply pending formatting to the newly typed text\n    const span = document.createElement('span');\n    \n    // Apply formatting styles\n    if (pendingFormatting.bold) span.style.fontWeight = 'bold';\n    if (pendingFormatting.italic) span.style.fontStyle = 'italic';\n    if (pendingFormatting.underline) span.style.textDecoration = 'underline';\n    if (pendingFormatting.fontSize) span.style.fontSize = pendingFormatting.fontSize + 'pt';\n    if (pendingFormatting.fontFamily) span.style.fontFamily = pendingFormatting.fontFamily;\n    if (pendingFormatting.color) span.style.color = pendingFormatting.color;\n    if (pendingFormatting.backgroundColor && pendingFormatting.backgroundColor !== 'transparent') {\n      span.style.backgroundColor = pendingFormatting.backgroundColor;\n    }\n    \n    try {\n      range.surroundContents(span);\n      onFormattingApplied?.(); // Clear pending formatting\n    } catch (e) {\n      console.warn('Could not apply formatting to new text:', e);\n    }\n  };\n  \n  const updateFormattingState = () => {\n    if (!editorRef.current) return;\n    \n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    \n    // Only update formatting state if we don't have pending formatting from toolbar\n    if (!pendingFormatting) {\n      const formatting = {\n        fontFamily: document.queryCommandValue('fontName') || 'Arial',\n        fontSize: document.queryCommandValue('fontSize') || '14',\n        bold: document.queryCommandState('bold'),\n        italic: document.queryCommandState('italic'),\n        underline: document.queryCommandState('underline'),\n        textAlign: (\n          document.queryCommandState('justifyCenter') ? 'center' :\n          document.queryCommandState('justifyRight') ? 'right' :\n          document.queryCommandState('justifyFull') ? 'justify' : 'left'\n        ) as 'left' | 'center' | 'right' | 'justify',\n        color: document.queryCommandValue('foreColor') || '#000000',\n        backgroundColor: document.queryCommandValue('backColor') || 'transparent',\n        lineHeight: '1.4',\n        letterSpacing: '0'\n      };\n      \n      setCurrentFormatting(formatting);\n      onSelectionChange?.(formatting);\n    }\n  };\n\n  const handleSelectionChange = () => {\n    if (isActive && !pendingFormatting) {\n      updateFormattingState();\n    }\n  };\n\n  useEffect(() => {\n    document.addEventListener('selectionchange', handleSelectionChange);\n    return () => {\n      document.removeEventListener('selectionchange', handleSelectionChange);\n    };\n  }, [isActive, pendingFormatting]);\n  \n  // Update current length when value changes\n  useEffect(() => {\n    setLastInputLength(value.length);\n  }, [value]);\n\n  const editorStyle = {\n    ...style,\n    lineHeight: currentFormatting.lineHeight,\n    letterSpacing: currentFormatting.letterSpacing\n  };\n\n  // Update editor content when value changes\n  useEffect(() => {\n    if (editorRef.current && editorRef.current.innerHTML !== value) {\n      const currentSelection = window.getSelection();\n      const currentRange = currentSelection && currentSelection.rangeCount > 0 ? currentSelection.getRangeAt(0) : null;\n      \n      editorRef.current.innerHTML = value || '';\n      \n      // Restore selection if it existed\n      if (currentRange && currentSelection) {\n        try {\n          currentSelection.removeAllRanges();\n          currentSelection.addRange(currentRange);\n        } catch (e) {\n          // Selection restore failed, place cursor at end\n          const range = document.createRange();\n          range.selectNodeContents(editorRef.current);\n          range.collapse(false);\n          currentSelection.removeAllRanges();\n          currentSelection.addRange(range);\n        }\n      }\n    }\n  }, [value]);\n\n  return (\n    <div className={`text-editor-container ${className}`}>\n      <div\n        ref={editorRef}\n        className={`text-editor ${isActive ? 'active' : ''}`}\n        contentEditable\n        suppressContentEditableWarning={true}\n        onInput={handleTextChange}\n        onFocus={handleFocus}\n        onBlur={handleBlur}\n        style={editorStyle}\n        data-placeholder={placeholder}\n      />\n    </div>\n  );\n};\n\nexport default TextEditor;\n"], "mappings": "4IAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,MAAO,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBA2B1B,KAAM,CAAAC,UAAqC,CAAGC,IAAA,EAaxC,IAbyC,CAC7CC,KAAK,CACLC,QAAQ,CACRC,WAAW,CAAG,eAAe,CAC7BC,QAAQ,CACRC,OAAO,CACPC,MAAM,CACNC,SAAS,CAAG,EAAE,CACdC,KAAK,CAAG,CAAC,CAAC,CACVC,iBAAiB,CACjBC,iBAAiB,CACjBC,iBAAiB,CACjBC,mBACF,CAAC,CAAAZ,IAAA,CACC,KAAM,CAACa,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGpB,QAAQ,CAAC,CACzDqB,UAAU,CAAE,OAAO,CACnBC,QAAQ,CAAE,IAAI,CACdC,IAAI,CAAE,KAAK,CACXC,MAAM,CAAE,KAAK,CACbC,SAAS,CAAE,KAAK,CAChBC,SAAS,CAAE,MAAiD,CAC5DC,KAAK,CAAE,SAAS,CAChBC,eAAe,CAAE,aAAa,CAC9BC,UAAU,CAAE,KAAK,CACjBC,aAAa,CAAE,GACjB,CAAC,CAAC,CAEF,KAAM,CAAAC,SAAS,CAAG9B,MAAM,CAAiB,IAAI,CAAC,CAC9C,KAAM,CAAC+B,eAAe,CAAEC,kBAAkB,CAAC,CAAGjC,QAAQ,CAAC,CAAC,CAAC,CAEzDE,SAAS,CAAC,IAAM,CACd,GAAIQ,QAAQ,EAAIqB,SAAS,CAACG,OAAO,CAAE,CACjCH,SAAS,CAACG,OAAO,CAACC,KAAK,CAAC,CAAC,CAC3B,CACF,CAAC,CAAE,CAACzB,QAAQ,CAAC,CAAC,CAEd;AACAR,SAAS,CAAC,IAAM,CACd,GAAIQ,QAAQ,EAAIM,iBAAiB,CAAE,CACjC;AACA;AAAA,CAEJ,CAAC,CAAE,CAACN,QAAQ,CAAEM,iBAAiB,CAAC,CAAC,CAEjC;AACA,KAAM,CAAAoB,YAAY,CAAGA,CAACC,OAAe,CAAE9B,KAAc,GAAK,CACxD,GAAI,CAACwB,SAAS,CAACG,OAAO,CAAE,OAExBH,SAAS,CAACG,OAAO,CAACC,KAAK,CAAC,CAAC,CAEzB,OAAQE,OAAO,EACb,IAAK,MAAM,CACX,IAAK,QAAQ,CACb,IAAK,WAAW,CACdC,QAAQ,CAACC,WAAW,CAACF,OAAO,CAAE,KAAK,CAAC,CACpC,MACF,IAAK,UAAU,CACb;AACA,KAAM,CAAAG,SAAS,CAAGC,MAAM,CAACC,YAAY,CAAC,CAAC,CACvC,GAAIF,SAAS,EAAIA,SAAS,CAACG,UAAU,CAAG,CAAC,CAAE,CACzC,KAAM,CAAAC,KAAK,CAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC,CACrC,GAAI,CAACD,KAAK,CAACE,SAAS,CAAE,CACpB,KAAM,CAAAC,IAAI,CAAGT,QAAQ,CAACU,aAAa,CAAC,MAAM,CAAC,CAC3CD,IAAI,CAACjC,KAAK,CAACQ,QAAQ,CAAGf,KAAK,CAAG,IAAI,CAClC,GAAI,CACFqC,KAAK,CAACK,gBAAgB,CAACF,IAAI,CAAC,CAC9B,CAAE,MAAOG,CAAC,CAAE,CACV;AACAZ,QAAQ,CAACC,WAAW,CAAC,UAAU,CAAE,KAAK,CAAE,GAAG,CAAC,CAC5C;AACA,KAAM,CAAAY,gBAAgB,CAAGpB,SAAS,CAACG,OAAO,CAACkB,gBAAgB,CAAC,gBAAgB,CAAC,CAC7ED,gBAAgB,CAACE,OAAO,CAACC,EAAE,EAAI,CAC5BA,EAAE,CAAiBxC,KAAK,CAACQ,QAAQ,CAAGf,KAAK,CAAG,IAAI,CACnD,CAAC,CAAC,CACJ,CACF,CACF,CACA,MACF,IAAK,UAAU,CACf,IAAK,WAAW,CAChB,IAAK,WAAW,CAChB,IAAK,aAAa,CAClB,IAAK,eAAe,CACpB,IAAK,cAAc,CACnB,IAAK,aAAa,CAClB,IAAK,qBAAqB,CAC1B,IAAK,mBAAmB,CACxB,IAAK,YAAY,CACf+B,QAAQ,CAACC,WAAW,CAACF,OAAO,CAAE,KAAK,CAAE9B,KAAK,CAAC,CAC3C,MACJ,CAEA;AACAgD,UAAU,CAAC,IAAMC,qBAAqB,CAAC,CAAC,CAAE,EAAE,CAAC,CAC/C,CAAC,CAEDtD,SAAS,CAAC,IAAM,CACd,GAAIQ,QAAQ,EAAIqB,SAAS,CAACG,OAAO,CAAE,CAChCH,SAAS,CAACG,OAAO,CAASE,YAAY,CAAGA,YAAY,CACxD,CACF,CAAC,CAAE,CAAC1B,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAA+C,gBAAgB,CAAIP,CAAkC,EAAK,CAC/D,KAAM,CAAAQ,OAAO,CAAGR,CAAC,CAACS,aAAa,CAACC,SAAS,CAAE;AAC3C,KAAM,CAAAC,WAAW,CAAGX,CAAC,CAACS,aAAa,CAACG,SAAS,CAC7C,KAAM,CAAAC,aAAa,CAAGF,WAAW,CAACG,MAAM,CAExC;AACA,GAAI/C,iBAAiB,EAAI8C,aAAa,CAAG/B,eAAe,CAAE,CACxDiC,+BAA+B,CAAC,CAAC,CACnC,CAEAhC,kBAAkB,CAAC8B,aAAa,CAAC,CACjCvD,QAAQ,CAACqD,WAAW,CAAC,CAAE;AACzB,CAAC,CAED,KAAM,CAAAK,WAAW,CAAGA,CAAA,GAAM,CACxBvD,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAG,CAAC,CACX6C,qBAAqB,CAAC,CAAC,CACzB,CAAC,CAED,KAAM,CAAAW,UAAU,CAAIjB,CAAmB,EAAK,CAC1CtC,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAGsC,CAAC,CAAC,CACb,CAAC,CAED,KAAM,CAAAe,+BAA+B,CAAGA,CAAA,GAAM,CAC5C,GAAI,CAAChD,iBAAiB,EAAI,CAACc,SAAS,CAACG,OAAO,CAAE,OAE9C,KAAM,CAAAM,SAAS,CAAGC,MAAM,CAACC,YAAY,CAAC,CAAC,CACvC,GAAI,CAACF,SAAS,EAAIA,SAAS,CAACG,UAAU,GAAK,CAAC,CAAE,OAE9C,KAAM,CAAAC,KAAK,CAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC,CACrC,GAAID,KAAK,CAACE,SAAS,CAAE,OAAQ;AAE7B;AACA,KAAM,CAAAC,IAAI,CAAGT,QAAQ,CAACU,aAAa,CAAC,MAAM,CAAC,CAE3C;AACA,GAAI/B,iBAAiB,CAACM,IAAI,CAAEwB,IAAI,CAACjC,KAAK,CAACsD,UAAU,CAAG,MAAM,CAC1D,GAAInD,iBAAiB,CAACO,MAAM,CAAEuB,IAAI,CAACjC,KAAK,CAACuD,SAAS,CAAG,QAAQ,CAC7D,GAAIpD,iBAAiB,CAACQ,SAAS,CAAEsB,IAAI,CAACjC,KAAK,CAACwD,cAAc,CAAG,WAAW,CACxE,GAAIrD,iBAAiB,CAACK,QAAQ,CAAEyB,IAAI,CAACjC,KAAK,CAACQ,QAAQ,CAAGL,iBAAiB,CAACK,QAAQ,CAAG,IAAI,CACvF,GAAIL,iBAAiB,CAACI,UAAU,CAAE0B,IAAI,CAACjC,KAAK,CAACO,UAAU,CAAGJ,iBAAiB,CAACI,UAAU,CACtF,GAAIJ,iBAAiB,CAACU,KAAK,CAAEoB,IAAI,CAACjC,KAAK,CAACa,KAAK,CAAGV,iBAAiB,CAACU,KAAK,CACvE,GAAIV,iBAAiB,CAACW,eAAe,EAAIX,iBAAiB,CAACW,eAAe,GAAK,aAAa,CAAE,CAC5FmB,IAAI,CAACjC,KAAK,CAACc,eAAe,CAAGX,iBAAiB,CAACW,eAAe,CAChE,CAEA,GAAI,CACFgB,KAAK,CAACK,gBAAgB,CAACF,IAAI,CAAC,CAC5B7B,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAG,CAAC,CAAE;AAC3B,CAAE,MAAOgC,CAAC,CAAE,CACVqB,OAAO,CAACC,IAAI,CAAC,yCAAyC,CAAEtB,CAAC,CAAC,CAC5D,CACF,CAAC,CAED,KAAM,CAAAM,qBAAqB,CAAGA,CAAA,GAAM,CAClC,GAAI,CAACzB,SAAS,CAACG,OAAO,CAAE,OAExB,KAAM,CAAAM,SAAS,CAAGC,MAAM,CAACC,YAAY,CAAC,CAAC,CACvC,GAAI,CAACF,SAAS,EAAIA,SAAS,CAACG,UAAU,GAAK,CAAC,CAAE,OAE9C;AACA,GAAI,CAAC1B,iBAAiB,CAAE,CACtB,KAAM,CAAAwD,UAAU,CAAG,CACjBpD,UAAU,CAAEiB,QAAQ,CAACoC,iBAAiB,CAAC,UAAU,CAAC,EAAI,OAAO,CAC7DpD,QAAQ,CAAEgB,QAAQ,CAACoC,iBAAiB,CAAC,UAAU,CAAC,EAAI,IAAI,CACxDnD,IAAI,CAAEe,QAAQ,CAACqC,iBAAiB,CAAC,MAAM,CAAC,CACxCnD,MAAM,CAAEc,QAAQ,CAACqC,iBAAiB,CAAC,QAAQ,CAAC,CAC5ClD,SAAS,CAAEa,QAAQ,CAACqC,iBAAiB,CAAC,WAAW,CAAC,CAClDjD,SAAS,CACPY,QAAQ,CAACqC,iBAAiB,CAAC,eAAe,CAAC,CAAG,QAAQ,CACtDrC,QAAQ,CAACqC,iBAAiB,CAAC,cAAc,CAAC,CAAG,OAAO,CACpDrC,QAAQ,CAACqC,iBAAiB,CAAC,aAAa,CAAC,CAAG,SAAS,CAAG,MACd,CAC5ChD,KAAK,CAAEW,QAAQ,CAACoC,iBAAiB,CAAC,WAAW,CAAC,EAAI,SAAS,CAC3D9C,eAAe,CAAEU,QAAQ,CAACoC,iBAAiB,CAAC,WAAW,CAAC,EAAI,aAAa,CACzE7C,UAAU,CAAE,KAAK,CACjBC,aAAa,CAAE,GACjB,CAAC,CAEDV,oBAAoB,CAACqD,UAAU,CAAC,CAChC1D,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAG0D,UAAU,CAAC,CACjC,CACF,CAAC,CAED,KAAM,CAAAG,qBAAqB,CAAGA,CAAA,GAAM,CAClC,GAAIlE,QAAQ,EAAI,CAACO,iBAAiB,CAAE,CAClCuC,qBAAqB,CAAC,CAAC,CACzB,CACF,CAAC,CAEDtD,SAAS,CAAC,IAAM,CACdoC,QAAQ,CAACuC,gBAAgB,CAAC,iBAAiB,CAAED,qBAAqB,CAAC,CACnE,MAAO,IAAM,CACXtC,QAAQ,CAACwC,mBAAmB,CAAC,iBAAiB,CAAEF,qBAAqB,CAAC,CACxE,CAAC,CACH,CAAC,CAAE,CAAClE,QAAQ,CAAEO,iBAAiB,CAAC,CAAC,CAEjC;AACAf,SAAS,CAAC,IAAM,CACd+B,kBAAkB,CAAC1B,KAAK,CAACyD,MAAM,CAAC,CAClC,CAAC,CAAE,CAACzD,KAAK,CAAC,CAAC,CAEX,KAAM,CAAAwE,WAAW,CAAAC,aAAA,CAAAA,aAAA,IACZlE,KAAK,MACRe,UAAU,CAAEV,iBAAiB,CAACU,UAAU,CACxCC,aAAa,CAAEX,iBAAiB,CAACW,aAAa,EAC/C,CAED;AACA5B,SAAS,CAAC,IAAM,CACd,GAAI6B,SAAS,CAACG,OAAO,EAAIH,SAAS,CAACG,OAAO,CAAC0B,SAAS,GAAKrD,KAAK,CAAE,CAC9D,KAAM,CAAA0E,gBAAgB,CAAGxC,MAAM,CAACC,YAAY,CAAC,CAAC,CAC9C,KAAM,CAAAwC,YAAY,CAAGD,gBAAgB,EAAIA,gBAAgB,CAACtC,UAAU,CAAG,CAAC,CAAGsC,gBAAgB,CAACpC,UAAU,CAAC,CAAC,CAAC,CAAG,IAAI,CAEhHd,SAAS,CAACG,OAAO,CAAC0B,SAAS,CAAGrD,KAAK,EAAI,EAAE,CAEzC;AACA,GAAI2E,YAAY,EAAID,gBAAgB,CAAE,CACpC,GAAI,CACFA,gBAAgB,CAACE,eAAe,CAAC,CAAC,CAClCF,gBAAgB,CAACG,QAAQ,CAACF,YAAY,CAAC,CACzC,CAAE,MAAOhC,CAAC,CAAE,CACV;AACA,KAAM,CAAAN,KAAK,CAAGN,QAAQ,CAAC+C,WAAW,CAAC,CAAC,CACpCzC,KAAK,CAAC0C,kBAAkB,CAACvD,SAAS,CAACG,OAAO,CAAC,CAC3CU,KAAK,CAAC2C,QAAQ,CAAC,KAAK,CAAC,CACrBN,gBAAgB,CAACE,eAAe,CAAC,CAAC,CAClCF,gBAAgB,CAACG,QAAQ,CAACxC,KAAK,CAAC,CAClC,CACF,CACF,CACF,CAAC,CAAE,CAACrC,KAAK,CAAC,CAAC,CAEX,mBACEH,IAAA,QAAKS,SAAS,0BAAA2E,MAAA,CAA2B3E,SAAS,CAAG,CAAA4E,QAAA,cACnDrF,IAAA,QACEsF,GAAG,CAAE3D,SAAU,CACflB,SAAS,gBAAA2E,MAAA,CAAiB9E,QAAQ,CAAG,QAAQ,CAAG,EAAE,CAAG,CACrDiF,eAAe,MACfC,8BAA8B,CAAE,IAAK,CACrCC,OAAO,CAAEpC,gBAAiB,CAC1B9C,OAAO,CAAEuD,WAAY,CACrBtD,MAAM,CAAEuD,UAAW,CACnBrD,KAAK,CAAEiE,WAAY,CACnB,mBAAkBtE,WAAY,CAC/B,CAAC,CACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}