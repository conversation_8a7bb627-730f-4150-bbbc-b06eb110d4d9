﻿<?xml version="1.0"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=301880
  -->
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
    <section name="clientDependency" type="ClientDependency.Core.Config.ClientDependencySection, ClientDependency.Core" requirePermission="false"/>
  </configSections>
  <connectionStrings>
    <!--<add name="AppSecurity" connectionString="Data Source=*************;Initial Catalog=AppSecurity;Persist Security Info=True;User ID=franklin;Password=***************" providerName="System.Data.SqlClient"/>
    <add name="TradeWorksTESTEntities" connectionString="metadata=res://*/Models.TradeworksTest.csdl|res://*/Models.TradeworksTest.ssdl|res://*/Models.TradeworksTest.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=************;initial catalog=FranklinReport;persist security info=True;user id=franklin;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient"/>
    <add name="AppSecurityEntities" connectionString="metadata=res://*/Models.AppSecurity.csdl|res://*/Models.AppSecurity.ssdl|res://*/Models.AppSecurity.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=*************;initial catalog=AppSecurity;persist security info=True;user id=franklin;password=***************;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient"/>-->
	<add name="AppSecurity" connectionString="Data Source=fra-sql-use-prd.database.windows.net;Initial Catalog=fra-sdb-use-prod;Persist Security Info=True;User ID=tradeworks_un;Password=*************" providerName="System.Data.SqlClient" />
	<add name="TradeWorksTESTEntities" connectionString="metadata=res://*/Models.TradeWorksTEST.csdl|res://*/Models.TradeWorksTEST.ssdl|res://*/Models.TradeWorksTEST.msl;provider=System.Data.SqlClient;provider connection string=&quot;Data Source=fra-sql-use-prd.database.windows.net;Initial Catalog=fra-sdb-use-prod;Persist Security Info=True;User ID=tradeworks_un;Password=*************;MultipleActiveResultSets=True&quot;" providerName="System.Data.EntityClient" />
	<add name="AppSecurityEntities" connectionString="metadata=res://*/Models.AppSecurity.csdl|res://*/Models.AppSecurity.ssdl|res://*/Models.AppSecurity.msl;provider=System.Data.SqlClient;provider connection string=&quot;Data Source=fra-sql-use-prd.database.windows.net;Initial Catalog=fra-sdb-use-prod;Persist Security Info=True;User ID=tradeworks_un;Password=*************;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  </connectionStrings>
  <appSettings>
    <add key="webpages:Version" value="*******"/>
    <add key="webpages:Enabled" value="false"/>
    <add key="ClientValidationEnabled" value="true"/>
    <add key="UnobtrusiveJavaScriptEnabled" value="true"/>
    <add key="BetaTesting" value="true"/>
  </appSettings>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.5" />
      </system.Web>
  -->
  <system.web>
    <authentication mode="None"/>
    <compilation debug="true" targetFramework="4.8">
      <assemblies>
        <add assembly="System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
      </assemblies>
    </compilation>
    <httpRuntime targetFramework="4.5" executionTimeout="100000" maxRequestLength="214748364" fcnMode="Single"/>
    <customErrors mode="Off"/>
    <httpModules>
      <add name="ImageProcessorModule" type="ImageProcessor.Web.HttpModules.ImageProcessingModule, ImageProcessor.Web"/>
      <add name="ClientDependencyModule" type="ClientDependency.Core.Module.ClientDependencyModule, ClientDependency.Core"/>
    </httpModules>
    <pages>
      <namespaces>
        <add namespace="ClientDependency.Core"/>
        <add namespace="ClientDependency.Core.Mvc"/>
      </namespaces>
    </pages>
    <httpHandlers>
      <add verb="GET" path="DependencyHandler.axd" type="ClientDependency.Core.CompositeFiles.CompositeDependencyHandler, ClientDependency.Core "/>
    </httpHandlers>
  </system.web>
  <system.webServer>
    <!--<modules>
      <remove name="FormsAuthentication" />
    </modules>-->
    <modules runAllManagedModulesForAllRequests="true">
      <add name="ImageProcessorModule" type="ImageProcessor.Web.HttpModules.ImageProcessingModule, ImageProcessor.Web"/>
      <remove name="ClientDependencyModule"/>
      <add name="ClientDependencyModule" type="ClientDependency.Core.Module.ClientDependencyModule, ClientDependency.Core"/>
    </modules>
    <staticContent>
      <remove fileExtension=".woff"/>
      <mimeMap fileExtension=".woff" mimeType="application/octet-stream"/>
    </staticContent>
    <validation validateIntegratedModeConfiguration="false"/>
    <handlers>
      <remove name="DependencyHandler"/>
      <add name="DependencyHandler" preCondition="integratedMode" verb="GET" path="DependencyHandler.axd" type="ClientDependency.Core.CompositeFiles.CompositeDependencyHandler, ClientDependency.Core "/>
    </handlers>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.Cookies" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed"/>
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb"/>
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer"/>
    </providers>
  </entityFramework>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:6 /nowarn:1659;1699;1701"/>
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:14 /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+"/>
    </compilers>
  </system.codedom>
  <clientDependency version="1">
    <!-- Full config documentation is here: https://github.com/Shazwazza/ClientDependency/wiki/Configuration -->
  </clientDependency>
</configuration>