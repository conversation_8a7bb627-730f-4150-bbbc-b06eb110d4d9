function getEditors(){

		// Turn off automatic editor creation first.
		CKEDITOR.disableAutoInline = true;

		CKEDITOR.inline( 'inline1', {
            extraPlugins: 'tokens,lineheight,letterspacing,sharedspace,textToolbar,lineheight',
			removePlugins: 'floatingspace,resize',
			sharedSpaces: {
				top: 'top',
				bottom: 'bottom'
			}
		});

		CKEDITOR.inline( 'inline2', {
		    extraPlugins: 'tokens,lineheight,letterspacing,sharedspace,textToolbar,lineheight',
			removePlugins: 'floatingspace,resize',
			sharedSpaces: {
				top: 'top',
				bottom: 'bottom'
			}
		});

		 

}