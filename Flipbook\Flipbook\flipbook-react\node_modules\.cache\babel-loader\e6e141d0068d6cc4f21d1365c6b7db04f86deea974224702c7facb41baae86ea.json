{"ast": null, "code": "import _objectSpread from\"D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{useParams,useNavigate}from'react-router-dom';import FrontPage from'../FrontPage/FrontPage';import ResumePage from'../ResumePage/ResumePage';import ContactDetails from'../ContactDetails/ContactDetails';import NameFlipbook from'../NameFlipbook/NameFlipbook';import AddPageModal from'../AddPageModal/AddPageModal';import TextToolbar from'../TextToolbar/TextToolbar';import{useAuth}from'../../hooks/useAuth';import{useFlipbooks}from'../../hooks/useFlipbooks';import{apiService}from'../../services/api.service';import'./FlipbookEditor.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FlipbookEditor=()=>{const{portfolioId}=useParams();const navigate=useNavigate();const{user}=useAuth();const{userFlipbooks,updateFlipbook,refreshUserFlipbooks}=useFlipbooks();const[flipbookTitle,setFlipbookTitle]=useState('');const[currentPage,setCurrentPage]=useState(1);const[totalPages,setTotalPages]=useState(3);// Front Cover + 1 Content Page + Back Cover\nconst[isNewFlipbook,setIsNewFlipbook]=useState(false);const[isSaving,setIsSaving]=useState(false);const[lastSaved,setLastSaved]=useState(null);const[showContactModal,setShowContactModal]=useState(false);const[showNameModal,setShowNameModal]=useState(false);const[hasCreatedFlipbook,setHasCreatedFlipbook]=useState(false);const[showAddPageModal,setShowAddPageModal]=useState(false);const[draggedPageIndex,setDraggedPageIndex]=useState(null);const[dragOverPageIndex,setDragOverPageIndex]=useState(null);const[pageOrder,setPageOrder]=useState([]);// Text formatting state\nconst[activeTextEditor,setActiveTextEditor]=useState(null);const[currentTextFormatting,setCurrentTextFormatting]=useState({fontFamily:'Arial',fontSize:'14',bold:false,italic:false,underline:false,textAlign:'left',color:'#000000',backgroundColor:'transparent',lineHeight:'1.4',letterSpacing:'0'});const[showTextToolbar,setShowTextToolbar]=useState(false);const[isTyping,setIsTyping]=useState(false);const[pendingFormatting,setPendingFormatting]=useState(null);// Flipbook content data\nconst[flipbookData,setFlipbookData]=useState({title:'Welcome to My Portfolio',subtitle:'Professional Excellence in Every Detail',frontPageBackground:'',resumeSections:[{id:'objective',title:'Objective',content:'Enter your career objective here...'},{id:'education',title:'Education',content:'Enter your educational background...'},{id:'experience',title:'Experience',content:'Enter your work experience...'},{id:'skills',title:'Skill Proficiencies',content:'List your key skills...'},{id:'personal',title:'Personal Statement',content:'Write your personal statement...'},{id:'interests',title:'Professional Interests',content:'Describe your professional interests...'}],profileImage:'',contactInfo:{name:'Zara Irum',mobile:'************',email:'<EMAIL>',flipbookUrl:'https://flipbook.franklinreport.com/2137 2236 <EMAIL>'}});useEffect(()=>{// Check if we're creating a new flipbook (no portfolioId in URL)\nif(!portfolioId&&!hasCreatedFlipbook){setIsNewFlipbook(true);setShowNameModal(true);}else{setIsNewFlipbook(false);// Load existing flipbook data here\nconst flipbook=userFlipbooks.find(fb=>fb.PortfolioID.toString()===portfolioId);if(flipbook){setFlipbookTitle(flipbook.PortfolioTitle);// Ensure minimum of 3 pages (front cover, content, back cover)\nconst pageCount=Math.max(flipbook.PageCount||3,3);console.log('Loading flipbook with page count:',pageCount,'from stored:',flipbook.PageCount);setTotalPages(pageCount);}else{// If flipbook not found in userFlipbooks, ensure we still have 3 pages minimum\nconsole.log('Flipbook not found, setting default 3 pages');setTotalPages(3);}}},[portfolioId,userFlipbooks,hasCreatedFlipbook]);// Initialize page order when total pages changes\nuseEffect(()=>{console.log('Total pages changed to:',totalPages);setPageOrder(Array.from({length:totalPages},(_,i)=>i));},[totalPages]);const handleSave=async()=>{if(!portfolioId){// Handle case where no portfolio ID exists\nreturn;}setIsSaving(true);try{const success=await updateFlipbook({portfolioId:parseInt(portfolioId),title:flipbookTitle});if(success){setLastSaved(new Date());// Refresh flipbooks to get latest data\nawait refreshUserFlipbooks();}}catch(error){console.error('Error saving flipbook:',error);}finally{setIsSaving(false);}};const handleTitleEdit=()=>{const newTitle=prompt('Enter new flipbook title:',flipbookTitle);if(newTitle&&newTitle.trim()){setFlipbookTitle(newTitle.trim());}};const handlePreview=()=>{if(portfolioId){navigate(\"/viewer/\".concat(portfolioId));}};const handleClose=()=>{navigate('/');};const handleAddPage=()=>{setShowAddPageModal(true);};const handleSelectPageTemplate=template=>{// Add the new page with the selected template\nsetTotalPages(prev=>prev+1);// Navigate to the new page\nsetCurrentPage(totalPages+1);console.log('Selected template:',template);};// Page drag and drop handlers\nconst handlePageDragStart=(e,pageIndex)=>{// Don't allow dragging front cover (0) or back cover (last page)\nif(pageIndex===0||pageIndex===totalPages-1){e.preventDefault();return;}setDraggedPageIndex(pageIndex);e.dataTransfer.effectAllowed='move';e.dataTransfer.setData('text/plain',pageIndex.toString());};const handlePageDragEnd=()=>{setDraggedPageIndex(null);setDragOverPageIndex(null);};const handlePageDragOver=(e,targetIndex)=>{e.preventDefault();// Don't allow dropping on front cover (0) or back cover (last page)\nif(targetIndex===0||targetIndex===totalPages-1){return;}e.dataTransfer.dropEffect='move';setDragOverPageIndex(targetIndex);};const handlePageDragLeave=()=>{setDragOverPageIndex(null);};const handlePageDrop=(e,targetIndex)=>{e.preventDefault();if(draggedPageIndex===null||draggedPageIndex===targetIndex)return;// Don't allow dropping on front cover (0) or back cover (last page)\nif(targetIndex===0||targetIndex===totalPages-1){return;}const newPageOrder=[...pageOrder];const draggedPage=newPageOrder[draggedPageIndex];// Remove dragged page\nnewPageOrder.splice(draggedPageIndex,1);// Insert at new position\nconst insertIndex=draggedPageIndex<targetIndex?targetIndex-1:targetIndex;newPageOrder.splice(insertIndex,0,draggedPage);setPageOrder(newPageOrder);setDraggedPageIndex(null);setDragOverPageIndex(null);// Update current page if necessary\nif(currentPage===draggedPageIndex+1){setCurrentPage(insertIndex+1);}};// Get page label based on original index\nconst getPageLabel=originalIndex=>{if(originalIndex===0)return'Front Cover';if(originalIndex===totalPages-1)return'Back Cover';return\"Page \".concat(originalIndex+1);};// Check if a page is draggable\nconst isPageDraggable=index=>{return index!==0&&index!==totalPages-1;};// Text formatting handlers\nconst handleTextEditorFocus=(editorRef,formatting)=>{setActiveTextEditor(editorRef);setShowTextToolbar(true);// Don't override formatting if we have pending formatting from toolbar\nif(!pendingFormatting&&formatting){setCurrentTextFormatting(formatting);}};const handleTextEditorBlur=event=>{// Don't hide toolbar if clicking on the toolbar itself\nif(event!==null&&event!==void 0&&event.relatedTarget){const clickedElement=event.relatedTarget;const toolbar=document.querySelector('.text-toolbar');if(toolbar&&toolbar.contains(clickedElement)){return;// Don't hide toolbar if clicking on it\n}}// Don't hide toolbar immediately - let it stay visible for a moment\nsetTimeout(()=>{// Double-check if we should still hide the toolbar\nconst activeElement=document.activeElement;const toolbar=document.querySelector('.text-toolbar');// Don't hide if focus is on toolbar or a text editor\nif(toolbar&&toolbar.contains(activeElement)){return;}if(activeElement&&activeElement.classList.contains('text-editor')){return;}setShowTextToolbar(false);setActiveTextEditor(null);},150);};const handleTextFormattingChange=formatting=>{const newFormatting=_objectSpread(_objectSpread({},currentTextFormatting),formatting);setCurrentTextFormatting(newFormatting);setPendingFormatting(newFormatting);setIsTyping(false);};const handleApplyFormatting=(command,value)=>{if(!activeTextEditor)return;// Use the TextEditor's applyCommand method if available\nif(activeTextEditor.applyCommand){activeTextEditor.applyCommand(command,value);}else{// Fallback to direct document.execCommand\nactiveTextEditor.focus();switch(command){case'bold':case'italic':case'underline':document.execCommand(command,false);break;case'fontSize':// Apply font size via CSS for better control\nconst selection=window.getSelection();if(selection&&selection.rangeCount>0){const range=selection.getRangeAt(0);if(!range.collapsed){const span=document.createElement('span');span.style.fontSize=value+'pt';try{range.surroundContents(span);}catch(e){// Fallback to execCommand if surroundContents fails\ndocument.execCommand('fontSize',false,'7');// Then apply CSS size\nconst selectedElements=activeTextEditor.querySelectorAll('font[size=\"7\"]');selectedElements.forEach(el=>{el.style.fontSize=value+'pt';});}}}break;case'fontName':case'foreColor':case'backColor':case'justifyLeft':case'justifyCenter':case'justifyRight':case'justifyFull':case'insertUnorderedList':case'insertOrderedList':case'createLink':document.execCommand(command,false,value);break;}}// Set pending formatting for when user starts typing\nsetPendingFormatting(currentTextFormatting);};const handleFormattingApplied=()=>{// Clear pending formatting after it has been applied\nsetPendingFormatting(null);};const handleNameSubmit=async name=>{try{// Create the flipbook on the server with 3 pages (front cover, content, back cover)\nconst response=await apiService.createNewFlipbook({title:name,pageCount:3});if(response.success&&response.data){const newPortfolioId=response.data;setFlipbookTitle(name);setShowNameModal(false);setHasCreatedFlipbook(true);// Refresh flipbooks to get latest data\nawait refreshUserFlipbooks();// Update URL to include the new portfolio ID\nnavigate(\"/editor/\".concat(newPortfolioId),{replace:true});}else{console.error('Failed to create flipbook:',response.error);alert('Failed to create flipbook. Please try again.');}}catch(error){console.error('Error creating flipbook:',error);alert('An error occurred while creating the flipbook.');}};const handleNameModalClose=()=>{// If user closes modal without naming, redirect to dashboard\nnavigate('/');};return/*#__PURE__*/_jsxs(\"div\",{className:\"flipbook-editor\",children:[/*#__PURE__*/_jsx(TextToolbar,{isVisible:showTextToolbar,currentFormatting:currentTextFormatting,onFormatChange:handleTextFormattingChange,onApplyFormatting:handleApplyFormatting}),/*#__PURE__*/_jsxs(\"div\",{className:\"editor-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"editor-sidebar\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"toolbar-icons sidebar-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"icon-grid\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"icon-btn\",title:\"Select\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDCCC\"})}),/*#__PURE__*/_jsx(\"button\",{className:\"icon-btn\",title:\"Text\",children:/*#__PURE__*/_jsx(\"span\",{children:\"A\"})}),/*#__PURE__*/_jsx(\"button\",{className:\"icon-btn\",title:\"Image\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDDBC\\uFE0F\"})}),/*#__PURE__*/_jsx(\"button\",{className:\"icon-btn\",title:\"Shape\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\u2B1B\"})}),/*#__PURE__*/_jsx(\"button\",{className:\"icon-btn\",title:\"Line\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDCCF\"})}),/*#__PURE__*/_jsx(\"button\",{className:\"icon-btn\",title:\"Crop\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\u2702\\uFE0F\"})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"click-drag-section\",children:[\"CLICK & DRAG\",/*#__PURE__*/_jsx(\"br\",{}),\"TO REORDER PAGES\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"pages-section sidebar-section\",children:pageOrder.map((originalIndex,displayIndex)=>{const isDraggable=isPageDraggable(originalIndex);const isDragging=draggedPageIndex===displayIndex;const isDragOver=dragOverPageIndex===displayIndex;return/*#__PURE__*/_jsxs(\"div\",{className:\"page-thumbnail \".concat(currentPage===displayIndex+1?'active':'',\" \").concat(isDragging?'dragging':'',\" \").concat(isDragOver&&isDraggable?'drag-over':'',\" \").concat(!isDraggable?'not-draggable':'draggable'),draggable:isDraggable,onClick:()=>setCurrentPage(displayIndex+1),onDragStart:e=>handlePageDragStart(e,displayIndex),onDragEnd:handlePageDragEnd,onDragOver:e=>handlePageDragOver(e,displayIndex),onDragLeave:handlePageDragLeave,onDrop:e=>handlePageDrop(e,displayIndex),title:isDraggable?\"Drag to reorder - \".concat(getPageLabel(originalIndex)):getPageLabel(originalIndex),children:[isDraggable&&/*#__PURE__*/_jsx(\"div\",{className:\"page-drag-handle\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\u22EE\\u22EE\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"page-label\",children:getPageLabel(originalIndex)}),!isDraggable&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed-page-indicator\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDCCC\"})})]},\"page-\".concat(originalIndex));})}),/*#__PURE__*/_jsx(\"div\",{className:\"add-page-section\",children:/*#__PURE__*/_jsx(\"button\",{className:\"add-page-btn\",onClick:handleAddPage,children:\"+ Add New Page\"})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"editor-canvas\",children:/*#__PURE__*/_jsx(\"div\",{className:\"canvas-container\",children:!flipbookTitle?/*#__PURE__*/_jsx(\"div\",{className:\"waiting-for-name\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"placeholder-content\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Creating New Flipbook...\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Please name your flipbook to continue\"})]})}):/*#__PURE__*/_jsxs(\"div\",{className:\"page-canvas\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"pages-header\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"PAGES\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"flipbook-title\",children:flipbookTitle})]}),currentPage===1&&totalPages>=1&&/*#__PURE__*/_jsx(FrontPage,{title:flipbookTitle||flipbookData.title,subtitle:flipbookData.subtitle,backgroundImage:flipbookData.frontPageBackground,onTitleChange:title=>{setFlipbookData(prev=>_objectSpread(_objectSpread({},prev),{},{title}));},onSubtitleChange:subtitle=>setFlipbookData(prev=>_objectSpread(_objectSpread({},prev),{},{subtitle})),onBackgroundImageChange:imageUrl=>setFlipbookData(prev=>_objectSpread(_objectSpread({},prev),{},{frontPageBackground:imageUrl})),onNext:()=>setCurrentPage(2)}),currentPage===2&&totalPages>=2&&/*#__PURE__*/_jsx(ResumePage,{sections:flipbookData.resumeSections,profileImage:flipbookData.profileImage,onSectionChange:(sectionId,content)=>{setFlipbookData(prev=>_objectSpread(_objectSpread({},prev),{},{resumeSections:prev.resumeSections.map(section=>section.id===sectionId?_objectSpread(_objectSpread({},section),{},{content}):section)}));},onSectionDelete:sectionId=>{setFlipbookData(prev=>_objectSpread(_objectSpread({},prev),{},{resumeSections:prev.resumeSections.filter(section=>section.id!==sectionId)}));},onSectionReorder:newSections=>{setFlipbookData(prev=>_objectSpread(_objectSpread({},prev),{},{resumeSections:newSections}));},onProfileImageChange:imageUrl=>setFlipbookData(prev=>_objectSpread(_objectSpread({},prev),{},{profileImage:imageUrl})),onTextEditorFocus:handleTextEditorFocus,onTextEditorBlur:handleTextEditorBlur,pendingFormatting:pendingFormatting,onFormattingApplied:handleFormattingApplied,onPrevious:()=>setCurrentPage(1),onNext:()=>setCurrentPage(3)}),currentPage===3&&totalPages>=3&&/*#__PURE__*/_jsx(\"div\",{className:\"back-page\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"back-page-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"contact-info-section\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Contact Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact-card\",onClick:()=>setShowContactModal(true),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"contact-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"label\",children:\"Name:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"value\",children:flipbookData.contactInfo.name})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"label\",children:\"Mobile:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"value\",children:flipbookData.contactInfo.mobile})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"label\",children:\"Email:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"value\",children:flipbookData.contactInfo.email})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"label\",children:\"Portfolio URL:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"value\",children:flipbookData.contactInfo.flipbookUrl})]}),/*#__PURE__*/_jsx(\"div\",{className:\"edit-hint\",children:\"Click to edit contact details\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flipbook-branding\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"brand-logo\",children:\"\\uD83D\\uDCDA Flipbook\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Created with Flipbook\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"navigation-controls\",children:/*#__PURE__*/_jsx(\"button\",{className:\"nav-btn prev-btn\",onClick:()=>setCurrentPage(2),title:\"Previous Page\",children:/*#__PURE__*/_jsx(\"span\",{className:\"arrow-icon\",children:\"\\u2190\"})})})]})}),currentPage>3&&/*#__PURE__*/_jsxs(\"div\",{className:\"page-content\",children:[/*#__PURE__*/_jsxs(\"h1\",{children:[\"Page \",currentPage]}),/*#__PURE__*/_jsx(\"p\",{children:\"Additional page content can be added here.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"navigation-controls\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"nav-btn prev-btn\",onClick:()=>setCurrentPage(Math.max(1,currentPage-1)),title:\"Previous Page\",children:/*#__PURE__*/_jsx(\"span\",{className:\"arrow-icon\",children:\"\\u2190\"})}),currentPage<totalPages&&/*#__PURE__*/_jsx(\"button\",{className:\"nav-btn next-btn\",onClick:()=>setCurrentPage(Math.min(totalPages,currentPage+1)),title:\"Next Page\",children:/*#__PURE__*/_jsx(\"span\",{className:\"arrow-icon\",children:\"\\u2192\"})})]})]})]})})})]}),/*#__PURE__*/_jsx(ContactDetails,{isOpen:showContactModal,onClose:()=>setShowContactModal(false),contactInfo:flipbookData.contactInfo,onSave:contactInfo=>{setFlipbookData(prev=>_objectSpread(_objectSpread({},prev),{},{contactInfo}));setShowContactModal(false);}}),/*#__PURE__*/_jsx(NameFlipbook,{isOpen:showNameModal,onClose:handleNameModalClose,onSubmit:handleNameSubmit}),/*#__PURE__*/_jsx(AddPageModal,{isOpen:showAddPageModal,onClose:()=>setShowAddPageModal(false),onSelectTemplate:handleSelectPageTemplate})]});};export default FlipbookEditor;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "FrontPage", "ResumePage", "ContactDetails", "NameFlipbook", "AddPageModal", "TextToolbar", "useAuth", "useFlipbooks", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "FlipbookEditor", "portfolioId", "navigate", "user", "userFlipbooks", "updateFlipbook", "refreshUserFlipbooks", "flipbookTitle", "setFlipbookTitle", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "isNewFlipbook", "setIsNewFlipbook", "isSaving", "setIsSaving", "lastSaved", "setLastSaved", "showContactModal", "setShowContactModal", "showNameModal", "setShowNameModal", "hasCreatedFlipbook", "setHasCreatedFlipbook", "showAddPageModal", "setShowAddPageModal", "draggedPageIndex", "setDraggedPageIndex", "dragOverPageIndex", "setDragOverPageIndex", "pageOrder", "setPageOrder", "activeTextEditor", "setActiveTextEditor", "currentTextFormatting", "setCurrentTextFormatting", "fontFamily", "fontSize", "bold", "italic", "underline", "textAlign", "color", "backgroundColor", "lineHeight", "letterSpacing", "showTextToolbar", "setShowTextToolbar", "isTyping", "setIsTyping", "pendingFormatting", "setPendingFormatting", "flipbookData", "setFlipbookData", "title", "subtitle", "frontPageBackground", "resumeSections", "id", "content", "profileImage", "contactInfo", "name", "mobile", "email", "flipbookUrl", "flipbook", "find", "fb", "PortfolioID", "toString", "PortfolioTitle", "pageCount", "Math", "max", "PageCount", "console", "log", "Array", "from", "length", "_", "i", "handleSave", "success", "parseInt", "Date", "error", "handleTitleEdit", "newTitle", "prompt", "trim", "handlePreview", "concat", "handleClose", "handleAddPage", "handleSelectPageTemplate", "template", "prev", "handlePageDragStart", "e", "pageIndex", "preventDefault", "dataTransfer", "effectAllowed", "setData", "handlePageDragEnd", "handlePageDragOver", "targetIndex", "dropEffect", "handlePageDragLeave", "handlePageDrop", "newPageOrder", "draggedPage", "splice", "insertIndex", "getPageLabel", "originalIndex", "isPageDraggable", "index", "handleTextEditorFocus", "editor<PERSON><PERSON>", "formatting", "handleTextEditorBlur", "event", "relatedTarget", "clickedElement", "toolbar", "document", "querySelector", "contains", "setTimeout", "activeElement", "classList", "handleTextFormattingChange", "newFormatting", "_objectSpread", "handleApplyFormatting", "command", "value", "applyCommand", "focus", "execCommand", "selection", "window", "getSelection", "rangeCount", "range", "getRangeAt", "collapsed", "span", "createElement", "style", "surroundContents", "selectedElements", "querySelectorAll", "for<PERSON>ach", "el", "handleFormattingApplied", "handleNameSubmit", "response", "createNewFlipbook", "data", "newPortfolioId", "replace", "alert", "handleNameModalClose", "className", "children", "isVisible", "currentFormatting", "onFormatChange", "onApplyFormatting", "map", "displayIndex", "isDraggable", "isDragging", "isDragOver", "draggable", "onClick", "onDragStart", "onDragEnd", "onDragOver", "onDragLeave", "onDrop", "backgroundImage", "onTitleChange", "onSubtitleChange", "onBackgroundImageChange", "imageUrl", "onNext", "sections", "onSectionChange", "sectionId", "section", "onSectionDelete", "filter", "onSectionReorder", "newSections", "onProfileImageChange", "onTextEditorFocus", "onTextEditorBlur", "onFormattingApplied", "onPrevious", "min", "isOpen", "onClose", "onSave", "onSubmit", "onSelectTemplate"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/FlipbookEditor/FlipbookEditor.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Header from '../Header/Header';\nimport FrontPage from '../FrontPage/FrontPage';\nimport ResumePage from '../ResumePage/ResumePage';\nimport ContactDetails from '../ContactDetails/ContactDetails';\nimport NameFlipbook from '../NameFlipbook/NameFlipbook';\nimport AddPageModal from '../AddPageModal/AddPageModal';\nimport TextToolbar from '../TextToolbar/TextToolbar';\nimport { useAuth } from '../../hooks/useAuth';\nimport { useFlipbooks } from '../../hooks/useFlipbooks';\nimport { apiService } from '../../services/api.service';\nimport './FlipbookEditor.css';\n\ninterface FlipbookEditorProps {}\n\ninterface ResumeSection {\n  id: string;\n  title: string;\n  content: string;\n}\n\ninterface ContactInfo {\n  name: string;\n  mobile: string;\n  email: string;\n  flipbookUrl: string;\n}\n\ninterface FlipbookData {\n  title: string;\n  subtitle: string;\n  frontPageBackground: string;\n  resumeSections: ResumeSection[];\n  profileImage: string;\n  contactInfo: ContactInfo;\n}\n\nconst FlipbookEditor: React.FC<FlipbookEditorProps> = () => {\n  const { portfolioId } = useParams<{ portfolioId: string }>();\n  const navigate = useNavigate();\n  const { user } = useAuth();\n  const { userFlipbooks, updateFlipbook, refreshUserFlipbooks } = useFlipbooks();\n  \n  const [flipbookTitle, setFlipbookTitle] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(3); // Front Cover + 1 Content Page + Back Cover\n  const [isNewFlipbook, setIsNewFlipbook] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [lastSaved, setLastSaved] = useState<Date | null>(null);\n  const [showContactModal, setShowContactModal] = useState(false);\n  const [showNameModal, setShowNameModal] = useState(false);\n  const [hasCreatedFlipbook, setHasCreatedFlipbook] = useState(false);\n  const [showAddPageModal, setShowAddPageModal] = useState(false);\n  const [draggedPageIndex, setDraggedPageIndex] = useState<number | null>(null);\n  const [dragOverPageIndex, setDragOverPageIndex] = useState<number | null>(null);\n  const [pageOrder, setPageOrder] = useState<number[]>([]);\n  \n  // Text formatting state\n  const [activeTextEditor, setActiveTextEditor] = useState<HTMLDivElement | null>(null);\n  const [currentTextFormatting, setCurrentTextFormatting] = useState({\n    fontFamily: 'Arial',\n    fontSize: '14',\n    bold: false,\n    italic: false,\n    underline: false,\n    textAlign: 'left' as 'left' | 'center' | 'right' | 'justify',\n    color: '#000000',\n    backgroundColor: 'transparent',\n    lineHeight: '1.4',\n    letterSpacing: '0'\n  });\n  const [showTextToolbar, setShowTextToolbar] = useState(false);\n  const [isTyping, setIsTyping] = useState(false);\n  const [pendingFormatting, setPendingFormatting] = useState<any>(null);\n  \n  // Flipbook content data\n  const [flipbookData, setFlipbookData] = useState<FlipbookData>({\n    title: 'Welcome to My Portfolio',\n    subtitle: 'Professional Excellence in Every Detail',\n    frontPageBackground: '',\n    resumeSections: [\n      { id: 'objective', title: 'Objective', content: 'Enter your career objective here...' },\n      { id: 'education', title: 'Education', content: 'Enter your educational background...' },\n      { id: 'experience', title: 'Experience', content: 'Enter your work experience...' },\n      { id: 'skills', title: 'Skill Proficiencies', content: 'List your key skills...' },\n      { id: 'personal', title: 'Personal Statement', content: 'Write your personal statement...' },\n      { id: 'interests', title: 'Professional Interests', content: 'Describe your professional interests...' }\n    ],\n    profileImage: '',\n    contactInfo: {\n      name: 'Zara Irum',\n      mobile: '************',\n      email: '<EMAIL>',\n      flipbookUrl: 'https://flipbook.franklinreport.com/2137 2236 <EMAIL>'\n    }\n  });\n\n  useEffect(() => {\n    // Check if we're creating a new flipbook (no portfolioId in URL)\n    if (!portfolioId && !hasCreatedFlipbook) {\n      setIsNewFlipbook(true);\n      setShowNameModal(true);\n    } else {\n      setIsNewFlipbook(false);\n      // Load existing flipbook data here\n      const flipbook = userFlipbooks.find(fb => fb.PortfolioID.toString() === portfolioId);\n      if (flipbook) {\n        setFlipbookTitle(flipbook.PortfolioTitle);\n        // Ensure minimum of 3 pages (front cover, content, back cover)\n        const pageCount = Math.max(flipbook.PageCount || 3, 3);\n        console.log('Loading flipbook with page count:', pageCount, 'from stored:', flipbook.PageCount);\n        setTotalPages(pageCount);\n      } else {\n        // If flipbook not found in userFlipbooks, ensure we still have 3 pages minimum\n        console.log('Flipbook not found, setting default 3 pages');\n        setTotalPages(3);\n      }\n    }\n  }, [portfolioId, userFlipbooks, hasCreatedFlipbook]);\n\n  // Initialize page order when total pages changes\n  useEffect(() => {\n    console.log('Total pages changed to:', totalPages);\n    setPageOrder(Array.from({ length: totalPages }, (_, i) => i));\n  }, [totalPages]);\n\n  const handleSave = async () => {\n    if (!portfolioId) {\n      // Handle case where no portfolio ID exists\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      const success = await updateFlipbook({\n        portfolioId: parseInt(portfolioId),\n        title: flipbookTitle\n      });\n      \n      if (success) {\n        setLastSaved(new Date());\n        // Refresh flipbooks to get latest data\n        await refreshUserFlipbooks();\n      }\n    } catch (error) {\n      console.error('Error saving flipbook:', error);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleTitleEdit = () => {\n    const newTitle = prompt('Enter new flipbook title:', flipbookTitle);\n    if (newTitle && newTitle.trim()) {\n      setFlipbookTitle(newTitle.trim());\n    }\n  };\n\n  const handlePreview = () => {\n    if (portfolioId) {\n      navigate(`/viewer/${portfolioId}`);\n    }\n  };\n\n  const handleClose = () => {\n    navigate('/');\n  };\n\n  const handleAddPage = () => {\n    setShowAddPageModal(true);\n  };\n\n  const handleSelectPageTemplate = (template: any) => {\n    // Add the new page with the selected template\n    setTotalPages(prev => prev + 1);\n    // Navigate to the new page\n    setCurrentPage(totalPages + 1);\n    console.log('Selected template:', template);\n  };\n\n  // Page drag and drop handlers\n  const handlePageDragStart = (e: React.DragEvent, pageIndex: number) => {\n    // Don't allow dragging front cover (0) or back cover (last page)\n    if (pageIndex === 0 || pageIndex === totalPages - 1) {\n      e.preventDefault();\n      return;\n    }\n    setDraggedPageIndex(pageIndex);\n    e.dataTransfer.effectAllowed = 'move';\n    e.dataTransfer.setData('text/plain', pageIndex.toString());\n  };\n\n  const handlePageDragEnd = () => {\n    setDraggedPageIndex(null);\n    setDragOverPageIndex(null);\n  };\n\n  const handlePageDragOver = (e: React.DragEvent, targetIndex: number) => {\n    e.preventDefault();\n    // Don't allow dropping on front cover (0) or back cover (last page)\n    if (targetIndex === 0 || targetIndex === totalPages - 1) {\n      return;\n    }\n    e.dataTransfer.dropEffect = 'move';\n    setDragOverPageIndex(targetIndex);\n  };\n\n  const handlePageDragLeave = () => {\n    setDragOverPageIndex(null);\n  };\n\n  const handlePageDrop = (e: React.DragEvent, targetIndex: number) => {\n    e.preventDefault();\n    \n    if (draggedPageIndex === null || draggedPageIndex === targetIndex) return;\n    \n    // Don't allow dropping on front cover (0) or back cover (last page)\n    if (targetIndex === 0 || targetIndex === totalPages - 1) {\n      return;\n    }\n\n    const newPageOrder = [...pageOrder];\n    const draggedPage = newPageOrder[draggedPageIndex];\n    \n    // Remove dragged page\n    newPageOrder.splice(draggedPageIndex, 1);\n    \n    // Insert at new position\n    const insertIndex = draggedPageIndex < targetIndex ? targetIndex - 1 : targetIndex;\n    newPageOrder.splice(insertIndex, 0, draggedPage);\n    \n    setPageOrder(newPageOrder);\n    setDraggedPageIndex(null);\n    setDragOverPageIndex(null);\n    \n    // Update current page if necessary\n    if (currentPage === draggedPageIndex + 1) {\n      setCurrentPage(insertIndex + 1);\n    }\n  };\n\n  // Get page label based on original index\n  const getPageLabel = (originalIndex: number) => {\n    if (originalIndex === 0) return 'Front Cover';\n    if (originalIndex === totalPages - 1) return 'Back Cover';\n    return `Page ${originalIndex + 1}`;\n  };\n\n  // Check if a page is draggable\n  const isPageDraggable = (index: number) => {\n    return index !== 0 && index !== totalPages - 1;\n  };\n\n  // Text formatting handlers\n  const handleTextEditorFocus = (editorRef: HTMLDivElement, formatting: any) => {\n    setActiveTextEditor(editorRef);\n    setShowTextToolbar(true);\n    \n    // Don't override formatting if we have pending formatting from toolbar\n    if (!pendingFormatting && formatting) {\n      setCurrentTextFormatting(formatting);\n    }\n  };\n\n  const handleTextEditorBlur = (event?: React.FocusEvent) => {\n    // Don't hide toolbar if clicking on the toolbar itself\n    if (event?.relatedTarget) {\n      const clickedElement = event.relatedTarget as HTMLElement;\n      const toolbar = document.querySelector('.text-toolbar');\n      if (toolbar && toolbar.contains(clickedElement)) {\n        return; // Don't hide toolbar if clicking on it\n      }\n    }\n    \n    // Don't hide toolbar immediately - let it stay visible for a moment\n    setTimeout(() => {\n      // Double-check if we should still hide the toolbar\n      const activeElement = document.activeElement as HTMLElement;\n      const toolbar = document.querySelector('.text-toolbar');\n      \n      // Don't hide if focus is on toolbar or a text editor\n      if (toolbar && toolbar.contains(activeElement)) {\n        return;\n      }\n      \n      if (activeElement && activeElement.classList.contains('text-editor')) {\n        return;\n      }\n      \n      setShowTextToolbar(false);\n      setActiveTextEditor(null);\n    }, 150);\n  };\n\n  const handleTextFormattingChange = (formatting: any) => {\n    const newFormatting = { ...currentTextFormatting, ...formatting };\n    setCurrentTextFormatting(newFormatting);\n    setPendingFormatting(newFormatting);\n    setIsTyping(false);\n  };\n\n  const handleApplyFormatting = (command: string, value?: string) => {\n    if (!activeTextEditor) return;\n    \n    // Use the TextEditor's applyCommand method if available\n    if ((activeTextEditor as any).applyCommand) {\n      (activeTextEditor as any).applyCommand(command, value);\n    } else {\n      // Fallback to direct document.execCommand\n      activeTextEditor.focus();\n      \n      switch (command) {\n        case 'bold':\n        case 'italic':\n        case 'underline':\n          document.execCommand(command, false);\n          break;\n        case 'fontSize':\n          // Apply font size via CSS for better control\n          const selection = window.getSelection();\n          if (selection && selection.rangeCount > 0) {\n            const range = selection.getRangeAt(0);\n            if (!range.collapsed) {\n              const span = document.createElement('span');\n              span.style.fontSize = value + 'pt';\n              try {\n                range.surroundContents(span);\n              } catch (e) {\n                // Fallback to execCommand if surroundContents fails\n                document.execCommand('fontSize', false, '7');\n                // Then apply CSS size\n                const selectedElements = activeTextEditor.querySelectorAll('font[size=\"7\"]');\n                selectedElements.forEach(el => {\n                  (el as HTMLElement).style.fontSize = value + 'pt';\n                });\n              }\n            }\n          }\n          break;\n        case 'fontName':\n        case 'foreColor':\n        case 'backColor':\n        case 'justifyLeft':\n        case 'justifyCenter':\n        case 'justifyRight':\n        case 'justifyFull':\n        case 'insertUnorderedList':\n        case 'insertOrderedList':\n        case 'createLink':\n          document.execCommand(command, false, value);\n          break;\n      }\n    }\n    \n    // Set pending formatting for when user starts typing\n    setPendingFormatting(currentTextFormatting);\n  };\n  \n  const handleFormattingApplied = () => {\n    // Clear pending formatting after it has been applied\n    setPendingFormatting(null);\n  };\n\n  const handleNameSubmit = async (name: string) => {\n    try {\n      // Create the flipbook on the server with 3 pages (front cover, content, back cover)\n      const response = await apiService.createNewFlipbook({ \n        title: name,\n        pageCount: 3 \n      });\n      if (response.success && response.data) {\n        const newPortfolioId = response.data;\n        setFlipbookTitle(name);\n        setShowNameModal(false);\n        setHasCreatedFlipbook(true);\n        \n        // Refresh flipbooks to get latest data\n        await refreshUserFlipbooks();\n        \n        // Update URL to include the new portfolio ID\n        navigate(`/editor/${newPortfolioId}`, { replace: true });\n      } else {\n        console.error('Failed to create flipbook:', response.error);\n        alert('Failed to create flipbook. Please try again.');\n      }\n    } catch (error) {\n      console.error('Error creating flipbook:', error);\n      alert('An error occurred while creating the flipbook.');\n    }\n  };\n\n  const handleNameModalClose = () => {\n    // If user closes modal without naming, redirect to dashboard\n    navigate('/');\n  };\n\n  return (\n    <div className=\"flipbook-editor\">\n      {/* Common Text Toolbar - Fixed at top */}\n      <TextToolbar\n        isVisible={showTextToolbar}\n        currentFormatting={currentTextFormatting}\n        onFormatChange={handleTextFormattingChange}\n        onApplyFormatting={handleApplyFormatting}\n      />\n      \n      <div className=\"editor-content\">\n        <div className=\"editor-sidebar\">\n          {/* Toolbar Icons */}\n          <div className=\"toolbar-icons sidebar-section\">\n            <div className=\"icon-grid\">\n              <button className=\"icon-btn\" title=\"Select\">\n                <span>📌</span>\n              </button>\n              <button className=\"icon-btn\" title=\"Text\">\n                <span>A</span>\n              </button>\n              <button className=\"icon-btn\" title=\"Image\">\n                <span>🖼️</span>\n              </button>\n              <button className=\"icon-btn\" title=\"Shape\">\n                <span>⬛</span>\n              </button>\n              <button className=\"icon-btn\" title=\"Line\">\n                <span>📏</span>\n              </button>\n              <button className=\"icon-btn\" title=\"Crop\">\n                <span>✂️</span>\n              </button>\n            </div>\n          </div>\n          \n          {/* Click & Drag Section */}\n          <div className=\"click-drag-section\">\n            CLICK & DRAG<br/>TO REORDER PAGES\n          </div>\n          \n          {/* Pages Section */}\n          <div className=\"pages-section sidebar-section\">\n            {pageOrder.map((originalIndex, displayIndex) => {\n              const isDraggable = isPageDraggable(originalIndex);\n              const isDragging = draggedPageIndex === displayIndex;\n              const isDragOver = dragOverPageIndex === displayIndex;\n              \n              return (\n                <div \n                  key={`page-${originalIndex}`}\n                  className={`page-thumbnail ${\n                    currentPage === displayIndex + 1 ? 'active' : ''\n                  } ${\n                    isDragging ? 'dragging' : ''\n                  } ${\n                    isDragOver && isDraggable ? 'drag-over' : ''\n                  } ${\n                    !isDraggable ? 'not-draggable' : 'draggable'\n                  }`}\n                  draggable={isDraggable}\n                  onClick={() => setCurrentPage(displayIndex + 1)}\n                  onDragStart={(e) => handlePageDragStart(e, displayIndex)}\n                  onDragEnd={handlePageDragEnd}\n                  onDragOver={(e) => handlePageDragOver(e, displayIndex)}\n                  onDragLeave={handlePageDragLeave}\n                  onDrop={(e) => handlePageDrop(e, displayIndex)}\n                  title={isDraggable ? `Drag to reorder - ${getPageLabel(originalIndex)}` : getPageLabel(originalIndex)}\n                >\n                  {isDraggable && (\n                    <div className=\"page-drag-handle\">\n                      <span>⋮⋮</span>\n                    </div>\n                  )}\n                  <span className=\"page-label\">{getPageLabel(originalIndex)}</span>\n                  {!isDraggable && (\n                    <div className=\"fixed-page-indicator\">\n                      <span>📌</span>\n                    </div>\n                  )}\n                </div>\n              );\n            })}\n          </div>\n          \n          {/* Add Page Section */}\n          <div className=\"add-page-section\">\n            <button className=\"add-page-btn\" onClick={handleAddPage}>\n              + Add New Page\n            </button>\n          </div>\n        </div>\n\n        <div className=\"editor-canvas\">\n          <div className=\"canvas-container\">\n            {!flipbookTitle ? (\n              <div className=\"waiting-for-name\">\n                <div className=\"placeholder-content\">\n                  <h2>Creating New Flipbook...</h2>\n                  <p>Please name your flipbook to continue</p>\n                </div>\n              </div>\n            ) : (\n              <div className=\"page-canvas\">\n                {/* Pages Header */}\n                <div className=\"pages-header\">\n                  <h1>PAGES</h1>\n                  <h2 className=\"flipbook-title\">{flipbookTitle}</h2>\n                </div>\n                \n                {currentPage === 1 && totalPages >= 1 && (\n                <FrontPage\n                  title={flipbookTitle || flipbookData.title}\n                  subtitle={flipbookData.subtitle}\n                  backgroundImage={flipbookData.frontPageBackground}\n                  onTitleChange={(title) => {\n                    setFlipbookData(prev => ({ ...prev, title }));\n                  }}\n                  onSubtitleChange={(subtitle) => setFlipbookData(prev => ({ ...prev, subtitle }))}\n                  onBackgroundImageChange={(imageUrl) => setFlipbookData(prev => ({ ...prev, frontPageBackground: imageUrl }))}\n                  onNext={() => setCurrentPage(2)}\n                />\n              )}\n              \n              {currentPage === 2 && totalPages >= 2 && (\n                <ResumePage\n                  sections={flipbookData.resumeSections}\n                  profileImage={flipbookData.profileImage}\n                  onSectionChange={(sectionId, content) => {\n                    setFlipbookData(prev => ({\n                      ...prev,\n                      resumeSections: prev.resumeSections.map(section =>\n                        section.id === sectionId ? { ...section, content } : section\n                      )\n                    }));\n                  }}\n                  onSectionDelete={(sectionId) => {\n                    setFlipbookData(prev => ({\n                      ...prev,\n                      resumeSections: prev.resumeSections.filter(section => section.id !== sectionId)\n                    }));\n                  }}\n                  onSectionReorder={(newSections) => {\n                    setFlipbookData(prev => ({\n                      ...prev,\n                      resumeSections: newSections\n                    }));\n                  }}\n                  onProfileImageChange={(imageUrl) => setFlipbookData(prev => ({ ...prev, profileImage: imageUrl }))}\n                  onTextEditorFocus={handleTextEditorFocus}\n                  onTextEditorBlur={handleTextEditorBlur}\n                  pendingFormatting={pendingFormatting}\n                  onFormattingApplied={handleFormattingApplied}\n                  onPrevious={() => setCurrentPage(1)}\n                  onNext={() => setCurrentPage(3)}\n                />\n              )}\n              \n              {currentPage === 3 && totalPages >= 3 && (\n                <div className=\"back-page\">\n                  <div className=\"back-page-content\">\n                    <div className=\"contact-info-section\">\n                      <h2>Contact Information</h2>\n                      <div className=\"contact-card\" onClick={() => setShowContactModal(true)}>\n                        <div className=\"contact-item\">\n                          <span className=\"label\">Name:</span>\n                          <span className=\"value\">{flipbookData.contactInfo.name}</span>\n                        </div>\n                        <div className=\"contact-item\">\n                          <span className=\"label\">Mobile:</span>\n                          <span className=\"value\">{flipbookData.contactInfo.mobile}</span>\n                        </div>\n                        <div className=\"contact-item\">\n                          <span className=\"label\">Email:</span>\n                          <span className=\"value\">{flipbookData.contactInfo.email}</span>\n                        </div>\n                        <div className=\"contact-item\">\n                          <span className=\"label\">Portfolio URL:</span>\n                          <span className=\"value\">{flipbookData.contactInfo.flipbookUrl}</span>\n                        </div>\n                        <div className=\"edit-hint\">\n                          Click to edit contact details\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flipbook-branding\">\n                      <div className=\"brand-logo\">📚 Flipbook</div>\n                      <p>Created with Flipbook</p>\n                    </div>\n                    \n                    <div className=\"navigation-controls\">\n                      <button className=\"nav-btn prev-btn\" onClick={() => setCurrentPage(2)} title=\"Previous Page\">\n                        <span className=\"arrow-icon\">←</span>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              )}\n              \n              {currentPage > 3 && (\n                <div className=\"page-content\">\n                  <h1>Page {currentPage}</h1>\n                  <p>Additional page content can be added here.</p>\n                  <div className=\"navigation-controls\">\n                    <button className=\"nav-btn prev-btn\" onClick={() => setCurrentPage(Math.max(1, currentPage - 1))} title=\"Previous Page\">\n                      <span className=\"arrow-icon\">←</span>\n                    </button>\n                    {currentPage < totalPages && (\n                      <button className=\"nav-btn next-btn\" onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))} title=\"Next Page\">\n                        <span className=\"arrow-icon\">→</span>\n                      </button>\n                    )}\n                  </div>\n                </div>\n              )}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Properties panel can be added later if needed */}\n      </div>\n      \n      {/* Contact Details Modal */}\n      <ContactDetails\n        isOpen={showContactModal}\n        onClose={() => setShowContactModal(false)}\n        contactInfo={flipbookData.contactInfo}\n        onSave={(contactInfo) => {\n          setFlipbookData(prev => ({ ...prev, contactInfo }));\n          setShowContactModal(false);\n        }}\n      />\n      \n      {/* Name Flipbook Modal */}\n      <NameFlipbook\n        isOpen={showNameModal}\n        onClose={handleNameModalClose}\n        onSubmit={handleNameSubmit}\n      />\n      \n      {/* Add Page Modal */}\n      <AddPageModal\n        isOpen={showAddPageModal}\n        onClose={() => setShowAddPageModal(false)}\n        onSelectTemplate={handleSelectPageTemplate}\n      />\n    </div>\n  );\n};\n\nexport default FlipbookEditor;\n"], "mappings": "4IAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CAEzD,MAAO,CAAAC,SAAS,KAAM,wBAAwB,CAC9C,MAAO,CAAAC,UAAU,KAAM,0BAA0B,CACjD,MAAO,CAAAC,cAAc,KAAM,kCAAkC,CAC7D,MAAO,CAAAC,YAAY,KAAM,8BAA8B,CACvD,MAAO,CAAAC,YAAY,KAAM,8BAA8B,CACvD,MAAO,CAAAC,WAAW,KAAM,4BAA4B,CACpD,OAASC,OAAO,KAAQ,qBAAqB,CAC7C,OAASC,YAAY,KAAQ,0BAA0B,CACvD,OAASC,UAAU,KAAQ,4BAA4B,CACvD,MAAO,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBA0B9B,KAAM,CAAAC,cAA6C,CAAGA,CAAA,GAAM,CAC1D,KAAM,CAAEC,WAAY,CAAC,CAAGhB,SAAS,CAA0B,CAAC,CAC5D,KAAM,CAAAiB,QAAQ,CAAGhB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEiB,IAAK,CAAC,CAAGV,OAAO,CAAC,CAAC,CAC1B,KAAM,CAAEW,aAAa,CAAEC,cAAc,CAAEC,oBAAqB,CAAC,CAAGZ,YAAY,CAAC,CAAC,CAE9E,KAAM,CAACa,aAAa,CAAEC,gBAAgB,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC0B,WAAW,CAAEC,cAAc,CAAC,CAAG3B,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAAC4B,UAAU,CAAEC,aAAa,CAAC,CAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAE;AACjD,KAAM,CAAC8B,aAAa,CAAEC,gBAAgB,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACgC,QAAQ,CAAEC,WAAW,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAACkC,SAAS,CAAEC,YAAY,CAAC,CAAGnC,QAAQ,CAAc,IAAI,CAAC,CAC7D,KAAM,CAACoC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACsC,aAAa,CAAEC,gBAAgB,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACwC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGzC,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAAC0C,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAC4C,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7C,QAAQ,CAAgB,IAAI,CAAC,CAC7E,KAAM,CAAC8C,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG/C,QAAQ,CAAgB,IAAI,CAAC,CAC/E,KAAM,CAACgD,SAAS,CAAEC,YAAY,CAAC,CAAGjD,QAAQ,CAAW,EAAE,CAAC,CAExD;AACA,KAAM,CAACkD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGnD,QAAQ,CAAwB,IAAI,CAAC,CACrF,KAAM,CAACoD,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGrD,QAAQ,CAAC,CACjEsD,UAAU,CAAE,OAAO,CACnBC,QAAQ,CAAE,IAAI,CACdC,IAAI,CAAE,KAAK,CACXC,MAAM,CAAE,KAAK,CACbC,SAAS,CAAE,KAAK,CAChBC,SAAS,CAAE,MAAiD,CAC5DC,KAAK,CAAE,SAAS,CAChBC,eAAe,CAAE,aAAa,CAC9BC,UAAU,CAAE,KAAK,CACjBC,aAAa,CAAE,GACjB,CAAC,CAAC,CACF,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGjE,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACkE,QAAQ,CAAEC,WAAW,CAAC,CAAGnE,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAACoE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGrE,QAAQ,CAAM,IAAI,CAAC,CAErE;AACA,KAAM,CAACsE,YAAY,CAAEC,eAAe,CAAC,CAAGvE,QAAQ,CAAe,CAC7DwE,KAAK,CAAE,yBAAyB,CAChCC,QAAQ,CAAE,yCAAyC,CACnDC,mBAAmB,CAAE,EAAE,CACvBC,cAAc,CAAE,CACd,CAAEC,EAAE,CAAE,WAAW,CAAEJ,KAAK,CAAE,WAAW,CAAEK,OAAO,CAAE,qCAAsC,CAAC,CACvF,CAAED,EAAE,CAAE,WAAW,CAAEJ,KAAK,CAAE,WAAW,CAAEK,OAAO,CAAE,sCAAuC,CAAC,CACxF,CAAED,EAAE,CAAE,YAAY,CAAEJ,KAAK,CAAE,YAAY,CAAEK,OAAO,CAAE,+BAAgC,CAAC,CACnF,CAAED,EAAE,CAAE,QAAQ,CAAEJ,KAAK,CAAE,qBAAqB,CAAEK,OAAO,CAAE,yBAA0B,CAAC,CAClF,CAAED,EAAE,CAAE,UAAU,CAAEJ,KAAK,CAAE,oBAAoB,CAAEK,OAAO,CAAE,kCAAmC,CAAC,CAC5F,CAAED,EAAE,CAAE,WAAW,CAAEJ,KAAK,CAAE,wBAAwB,CAAEK,OAAO,CAAE,yCAA0C,CAAC,CACzG,CACDC,YAAY,CAAE,EAAE,CAChBC,WAAW,CAAE,CACXC,IAAI,CAAE,WAAW,CACjBC,MAAM,CAAE,cAAc,CACtBC,KAAK,CAAE,gBAAgB,CACvBC,WAAW,CAAE,8DACf,CACF,CAAC,CAAC,CAEFlF,SAAS,CAAC,IAAM,CACd;AACA,GAAI,CAACiB,WAAW,EAAI,CAACsB,kBAAkB,CAAE,CACvCT,gBAAgB,CAAC,IAAI,CAAC,CACtBQ,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,IAAM,CACLR,gBAAgB,CAAC,KAAK,CAAC,CACvB;AACA,KAAM,CAAAqD,QAAQ,CAAG/D,aAAa,CAACgE,IAAI,CAACC,EAAE,EAAIA,EAAE,CAACC,WAAW,CAACC,QAAQ,CAAC,CAAC,GAAKtE,WAAW,CAAC,CACpF,GAAIkE,QAAQ,CAAE,CACZ3D,gBAAgB,CAAC2D,QAAQ,CAACK,cAAc,CAAC,CACzC;AACA,KAAM,CAAAC,SAAS,CAAGC,IAAI,CAACC,GAAG,CAACR,QAAQ,CAACS,SAAS,EAAI,CAAC,CAAE,CAAC,CAAC,CACtDC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAEL,SAAS,CAAE,cAAc,CAAEN,QAAQ,CAACS,SAAS,CAAC,CAC/FhE,aAAa,CAAC6D,SAAS,CAAC,CAC1B,CAAC,IAAM,CACL;AACAI,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC,CAC1DlE,aAAa,CAAC,CAAC,CAAC,CAClB,CACF,CACF,CAAC,CAAE,CAACX,WAAW,CAAEG,aAAa,CAAEmB,kBAAkB,CAAC,CAAC,CAEpD;AACAvC,SAAS,CAAC,IAAM,CACd6F,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAEnE,UAAU,CAAC,CAClDqB,YAAY,CAAC+C,KAAK,CAACC,IAAI,CAAC,CAAEC,MAAM,CAAEtE,UAAW,CAAC,CAAE,CAACuE,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAAC,CAAC,CAC/D,CAAC,CAAE,CAACxE,UAAU,CAAC,CAAC,CAEhB,KAAM,CAAAyE,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CAACnF,WAAW,CAAE,CAChB;AACA,OACF,CAEAe,WAAW,CAAC,IAAI,CAAC,CACjB,GAAI,CACF,KAAM,CAAAqE,OAAO,CAAG,KAAM,CAAAhF,cAAc,CAAC,CACnCJ,WAAW,CAAEqF,QAAQ,CAACrF,WAAW,CAAC,CAClCsD,KAAK,CAAEhD,aACT,CAAC,CAAC,CAEF,GAAI8E,OAAO,CAAE,CACXnE,YAAY,CAAC,GAAI,CAAAqE,IAAI,CAAC,CAAC,CAAC,CACxB;AACA,KAAM,CAAAjF,oBAAoB,CAAC,CAAC,CAC9B,CACF,CAAE,MAAOkF,KAAK,CAAE,CACdX,OAAO,CAACW,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CAAC,OAAS,CACRxE,WAAW,CAAC,KAAK,CAAC,CACpB,CACF,CAAC,CAED,KAAM,CAAAyE,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,QAAQ,CAAGC,MAAM,CAAC,2BAA2B,CAAEpF,aAAa,CAAC,CACnE,GAAImF,QAAQ,EAAIA,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAE,CAC/BpF,gBAAgB,CAACkF,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CACnC,CACF,CAAC,CAED,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAI5F,WAAW,CAAE,CACfC,QAAQ,YAAA4F,MAAA,CAAY7F,WAAW,CAAE,CAAC,CACpC,CACF,CAAC,CAED,KAAM,CAAA8F,WAAW,CAAGA,CAAA,GAAM,CACxB7F,QAAQ,CAAC,GAAG,CAAC,CACf,CAAC,CAED,KAAM,CAAA8F,aAAa,CAAGA,CAAA,GAAM,CAC1BtE,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAuE,wBAAwB,CAAIC,QAAa,EAAK,CAClD;AACAtF,aAAa,CAACuF,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CAC/B;AACAzF,cAAc,CAACC,UAAU,CAAG,CAAC,CAAC,CAC9BkE,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAEoB,QAAQ,CAAC,CAC7C,CAAC,CAED;AACA,KAAM,CAAAE,mBAAmB,CAAGA,CAACC,CAAkB,CAAEC,SAAiB,GAAK,CACrE;AACA,GAAIA,SAAS,GAAK,CAAC,EAAIA,SAAS,GAAK3F,UAAU,CAAG,CAAC,CAAE,CACnD0F,CAAC,CAACE,cAAc,CAAC,CAAC,CAClB,OACF,CACA3E,mBAAmB,CAAC0E,SAAS,CAAC,CAC9BD,CAAC,CAACG,YAAY,CAACC,aAAa,CAAG,MAAM,CACrCJ,CAAC,CAACG,YAAY,CAACE,OAAO,CAAC,YAAY,CAAEJ,SAAS,CAAC/B,QAAQ,CAAC,CAAC,CAAC,CAC5D,CAAC,CAED,KAAM,CAAAoC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B/E,mBAAmB,CAAC,IAAI,CAAC,CACzBE,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED,KAAM,CAAA8E,kBAAkB,CAAGA,CAACP,CAAkB,CAAEQ,WAAmB,GAAK,CACtER,CAAC,CAACE,cAAc,CAAC,CAAC,CAClB;AACA,GAAIM,WAAW,GAAK,CAAC,EAAIA,WAAW,GAAKlG,UAAU,CAAG,CAAC,CAAE,CACvD,OACF,CACA0F,CAAC,CAACG,YAAY,CAACM,UAAU,CAAG,MAAM,CAClChF,oBAAoB,CAAC+E,WAAW,CAAC,CACnC,CAAC,CAED,KAAM,CAAAE,mBAAmB,CAAGA,CAAA,GAAM,CAChCjF,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAkF,cAAc,CAAGA,CAACX,CAAkB,CAAEQ,WAAmB,GAAK,CAClER,CAAC,CAACE,cAAc,CAAC,CAAC,CAElB,GAAI5E,gBAAgB,GAAK,IAAI,EAAIA,gBAAgB,GAAKkF,WAAW,CAAE,OAEnE;AACA,GAAIA,WAAW,GAAK,CAAC,EAAIA,WAAW,GAAKlG,UAAU,CAAG,CAAC,CAAE,CACvD,OACF,CAEA,KAAM,CAAAsG,YAAY,CAAG,CAAC,GAAGlF,SAAS,CAAC,CACnC,KAAM,CAAAmF,WAAW,CAAGD,YAAY,CAACtF,gBAAgB,CAAC,CAElD;AACAsF,YAAY,CAACE,MAAM,CAACxF,gBAAgB,CAAE,CAAC,CAAC,CAExC;AACA,KAAM,CAAAyF,WAAW,CAAGzF,gBAAgB,CAAGkF,WAAW,CAAGA,WAAW,CAAG,CAAC,CAAGA,WAAW,CAClFI,YAAY,CAACE,MAAM,CAACC,WAAW,CAAE,CAAC,CAAEF,WAAW,CAAC,CAEhDlF,YAAY,CAACiF,YAAY,CAAC,CAC1BrF,mBAAmB,CAAC,IAAI,CAAC,CACzBE,oBAAoB,CAAC,IAAI,CAAC,CAE1B;AACA,GAAIrB,WAAW,GAAKkB,gBAAgB,CAAG,CAAC,CAAE,CACxCjB,cAAc,CAAC0G,WAAW,CAAG,CAAC,CAAC,CACjC,CACF,CAAC,CAED;AACA,KAAM,CAAAC,YAAY,CAAIC,aAAqB,EAAK,CAC9C,GAAIA,aAAa,GAAK,CAAC,CAAE,MAAO,aAAa,CAC7C,GAAIA,aAAa,GAAK3G,UAAU,CAAG,CAAC,CAAE,MAAO,YAAY,CACzD,cAAAmF,MAAA,CAAewB,aAAa,CAAG,CAAC,EAClC,CAAC,CAED;AACA,KAAM,CAAAC,eAAe,CAAIC,KAAa,EAAK,CACzC,MAAO,CAAAA,KAAK,GAAK,CAAC,EAAIA,KAAK,GAAK7G,UAAU,CAAG,CAAC,CAChD,CAAC,CAED;AACA,KAAM,CAAA8G,qBAAqB,CAAGA,CAACC,SAAyB,CAAEC,UAAe,GAAK,CAC5EzF,mBAAmB,CAACwF,SAAS,CAAC,CAC9B1E,kBAAkB,CAAC,IAAI,CAAC,CAExB;AACA,GAAI,CAACG,iBAAiB,EAAIwE,UAAU,CAAE,CACpCvF,wBAAwB,CAACuF,UAAU,CAAC,CACtC,CACF,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAIC,KAAwB,EAAK,CACzD;AACA,GAAIA,KAAK,SAALA,KAAK,WAALA,KAAK,CAAEC,aAAa,CAAE,CACxB,KAAM,CAAAC,cAAc,CAAGF,KAAK,CAACC,aAA4B,CACzD,KAAM,CAAAE,OAAO,CAAGC,QAAQ,CAACC,aAAa,CAAC,eAAe,CAAC,CACvD,GAAIF,OAAO,EAAIA,OAAO,CAACG,QAAQ,CAACJ,cAAc,CAAC,CAAE,CAC/C,OAAQ;AACV,CACF,CAEA;AACAK,UAAU,CAAC,IAAM,CACf;AACA,KAAM,CAAAC,aAAa,CAAGJ,QAAQ,CAACI,aAA4B,CAC3D,KAAM,CAAAL,OAAO,CAAGC,QAAQ,CAACC,aAAa,CAAC,eAAe,CAAC,CAEvD;AACA,GAAIF,OAAO,EAAIA,OAAO,CAACG,QAAQ,CAACE,aAAa,CAAC,CAAE,CAC9C,OACF,CAEA,GAAIA,aAAa,EAAIA,aAAa,CAACC,SAAS,CAACH,QAAQ,CAAC,aAAa,CAAC,CAAE,CACpE,OACF,CAEAnF,kBAAkB,CAAC,KAAK,CAAC,CACzBd,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,CAED,KAAM,CAAAqG,0BAA0B,CAAIZ,UAAe,EAAK,CACtD,KAAM,CAAAa,aAAa,CAAAC,aAAA,CAAAA,aAAA,IAAQtG,qBAAqB,EAAKwF,UAAU,CAAE,CACjEvF,wBAAwB,CAACoG,aAAa,CAAC,CACvCpF,oBAAoB,CAACoF,aAAa,CAAC,CACnCtF,WAAW,CAAC,KAAK,CAAC,CACpB,CAAC,CAED,KAAM,CAAAwF,qBAAqB,CAAGA,CAACC,OAAe,CAAEC,KAAc,GAAK,CACjE,GAAI,CAAC3G,gBAAgB,CAAE,OAEvB;AACA,GAAKA,gBAAgB,CAAS4G,YAAY,CAAE,CACzC5G,gBAAgB,CAAS4G,YAAY,CAACF,OAAO,CAAEC,KAAK,CAAC,CACxD,CAAC,IAAM,CACL;AACA3G,gBAAgB,CAAC6G,KAAK,CAAC,CAAC,CAExB,OAAQH,OAAO,EACb,IAAK,MAAM,CACX,IAAK,QAAQ,CACb,IAAK,WAAW,CACdV,QAAQ,CAACc,WAAW,CAACJ,OAAO,CAAE,KAAK,CAAC,CACpC,MACF,IAAK,UAAU,CACb;AACA,KAAM,CAAAK,SAAS,CAAGC,MAAM,CAACC,YAAY,CAAC,CAAC,CACvC,GAAIF,SAAS,EAAIA,SAAS,CAACG,UAAU,CAAG,CAAC,CAAE,CACzC,KAAM,CAAAC,KAAK,CAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC,CACrC,GAAI,CAACD,KAAK,CAACE,SAAS,CAAE,CACpB,KAAM,CAAAC,IAAI,CAAGtB,QAAQ,CAACuB,aAAa,CAAC,MAAM,CAAC,CAC3CD,IAAI,CAACE,KAAK,CAACnH,QAAQ,CAAGsG,KAAK,CAAG,IAAI,CAClC,GAAI,CACFQ,KAAK,CAACM,gBAAgB,CAACH,IAAI,CAAC,CAC9B,CAAE,MAAOlD,CAAC,CAAE,CACV;AACA4B,QAAQ,CAACc,WAAW,CAAC,UAAU,CAAE,KAAK,CAAE,GAAG,CAAC,CAC5C;AACA,KAAM,CAAAY,gBAAgB,CAAG1H,gBAAgB,CAAC2H,gBAAgB,CAAC,gBAAgB,CAAC,CAC5ED,gBAAgB,CAACE,OAAO,CAACC,EAAE,EAAI,CAC5BA,EAAE,CAAiBL,KAAK,CAACnH,QAAQ,CAAGsG,KAAK,CAAG,IAAI,CACnD,CAAC,CAAC,CACJ,CACF,CACF,CACA,MACF,IAAK,UAAU,CACf,IAAK,WAAW,CAChB,IAAK,WAAW,CAChB,IAAK,aAAa,CAClB,IAAK,eAAe,CACpB,IAAK,cAAc,CACnB,IAAK,aAAa,CAClB,IAAK,qBAAqB,CAC1B,IAAK,mBAAmB,CACxB,IAAK,YAAY,CACfX,QAAQ,CAACc,WAAW,CAACJ,OAAO,CAAE,KAAK,CAAEC,KAAK,CAAC,CAC3C,MACJ,CACF,CAEA;AACAxF,oBAAoB,CAACjB,qBAAqB,CAAC,CAC7C,CAAC,CAED,KAAM,CAAA4H,uBAAuB,CAAGA,CAAA,GAAM,CACpC;AACA3G,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED,KAAM,CAAA4G,gBAAgB,CAAG,KAAO,CAAAjG,IAAY,EAAK,CAC/C,GAAI,CACF;AACA,KAAM,CAAAkG,QAAQ,CAAG,KAAM,CAAAtK,UAAU,CAACuK,iBAAiB,CAAC,CAClD3G,KAAK,CAAEQ,IAAI,CACXU,SAAS,CAAE,CACb,CAAC,CAAC,CACF,GAAIwF,QAAQ,CAAC5E,OAAO,EAAI4E,QAAQ,CAACE,IAAI,CAAE,CACrC,KAAM,CAAAC,cAAc,CAAGH,QAAQ,CAACE,IAAI,CACpC3J,gBAAgB,CAACuD,IAAI,CAAC,CACtBzC,gBAAgB,CAAC,KAAK,CAAC,CACvBE,qBAAqB,CAAC,IAAI,CAAC,CAE3B;AACA,KAAM,CAAAlB,oBAAoB,CAAC,CAAC,CAE5B;AACAJ,QAAQ,YAAA4F,MAAA,CAAYsE,cAAc,EAAI,CAAEC,OAAO,CAAE,IAAK,CAAC,CAAC,CAC1D,CAAC,IAAM,CACLxF,OAAO,CAACW,KAAK,CAAC,4BAA4B,CAAEyE,QAAQ,CAACzE,KAAK,CAAC,CAC3D8E,KAAK,CAAC,8CAA8C,CAAC,CACvD,CACF,CAAE,MAAO9E,KAAK,CAAE,CACdX,OAAO,CAACW,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD8E,KAAK,CAAC,gDAAgD,CAAC,CACzD,CACF,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAGA,CAAA,GAAM,CACjC;AACArK,QAAQ,CAAC,GAAG,CAAC,CACf,CAAC,CAED,mBACEH,KAAA,QAAKyK,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAE9B5K,IAAA,CAACL,WAAW,EACVkL,SAAS,CAAE3H,eAAgB,CAC3B4H,iBAAiB,CAAExI,qBAAsB,CACzCyI,cAAc,CAAErC,0BAA2B,CAC3CsC,iBAAiB,CAAEnC,qBAAsB,CAC1C,CAAC,cAEF3I,KAAA,QAAKyK,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B1K,KAAA,QAAKyK,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAE7B5K,IAAA,QAAK2K,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5C1K,KAAA,QAAKyK,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB5K,IAAA,WAAQ2K,SAAS,CAAC,UAAU,CAACjH,KAAK,CAAC,QAAQ,CAAAkH,QAAA,cACzC5K,IAAA,SAAA4K,QAAA,CAAM,cAAE,CAAM,CAAC,CACT,CAAC,cACT5K,IAAA,WAAQ2K,SAAS,CAAC,UAAU,CAACjH,KAAK,CAAC,MAAM,CAAAkH,QAAA,cACvC5K,IAAA,SAAA4K,QAAA,CAAM,GAAC,CAAM,CAAC,CACR,CAAC,cACT5K,IAAA,WAAQ2K,SAAS,CAAC,UAAU,CAACjH,KAAK,CAAC,OAAO,CAAAkH,QAAA,cACxC5K,IAAA,SAAA4K,QAAA,CAAM,oBAAG,CAAM,CAAC,CACV,CAAC,cACT5K,IAAA,WAAQ2K,SAAS,CAAC,UAAU,CAACjH,KAAK,CAAC,OAAO,CAAAkH,QAAA,cACxC5K,IAAA,SAAA4K,QAAA,CAAM,QAAC,CAAM,CAAC,CACR,CAAC,cACT5K,IAAA,WAAQ2K,SAAS,CAAC,UAAU,CAACjH,KAAK,CAAC,MAAM,CAAAkH,QAAA,cACvC5K,IAAA,SAAA4K,QAAA,CAAM,cAAE,CAAM,CAAC,CACT,CAAC,cACT5K,IAAA,WAAQ2K,SAAS,CAAC,UAAU,CAACjH,KAAK,CAAC,MAAM,CAAAkH,QAAA,cACvC5K,IAAA,SAAA4K,QAAA,CAAM,cAAE,CAAM,CAAC,CACT,CAAC,EACN,CAAC,CACH,CAAC,cAGN1K,KAAA,QAAKyK,SAAS,CAAC,oBAAoB,CAAAC,QAAA,EAAC,cACtB,cAAA5K,IAAA,QAAI,CAAC,mBACnB,EAAK,CAAC,cAGNA,IAAA,QAAK2K,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAC3C1I,SAAS,CAAC+I,GAAG,CAAC,CAACxD,aAAa,CAAEyD,YAAY,GAAK,CAC9C,KAAM,CAAAC,WAAW,CAAGzD,eAAe,CAACD,aAAa,CAAC,CAClD,KAAM,CAAA2D,UAAU,CAAGtJ,gBAAgB,GAAKoJ,YAAY,CACpD,KAAM,CAAAG,UAAU,CAAGrJ,iBAAiB,GAAKkJ,YAAY,CAErD,mBACEhL,KAAA,QAEEyK,SAAS,mBAAA1E,MAAA,CACPrF,WAAW,GAAKsK,YAAY,CAAG,CAAC,CAAG,QAAQ,CAAG,EAAE,MAAAjF,MAAA,CAEhDmF,UAAU,CAAG,UAAU,CAAG,EAAE,MAAAnF,MAAA,CAE5BoF,UAAU,EAAIF,WAAW,CAAG,WAAW,CAAG,EAAE,MAAAlF,MAAA,CAE5C,CAACkF,WAAW,CAAG,eAAe,CAAG,WAAW,CAC3C,CACHG,SAAS,CAAEH,WAAY,CACvBI,OAAO,CAAEA,CAAA,GAAM1K,cAAc,CAACqK,YAAY,CAAG,CAAC,CAAE,CAChDM,WAAW,CAAGhF,CAAC,EAAKD,mBAAmB,CAACC,CAAC,CAAE0E,YAAY,CAAE,CACzDO,SAAS,CAAE3E,iBAAkB,CAC7B4E,UAAU,CAAGlF,CAAC,EAAKO,kBAAkB,CAACP,CAAC,CAAE0E,YAAY,CAAE,CACvDS,WAAW,CAAEzE,mBAAoB,CACjC0E,MAAM,CAAGpF,CAAC,EAAKW,cAAc,CAACX,CAAC,CAAE0E,YAAY,CAAE,CAC/CxH,KAAK,CAAEyH,WAAW,sBAAAlF,MAAA,CAAwBuB,YAAY,CAACC,aAAa,CAAC,EAAKD,YAAY,CAACC,aAAa,CAAE,CAAAmD,QAAA,EAErGO,WAAW,eACVnL,IAAA,QAAK2K,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/B5K,IAAA,SAAA4K,QAAA,CAAM,cAAE,CAAM,CAAC,CACZ,CACN,cACD5K,IAAA,SAAM2K,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEpD,YAAY,CAACC,aAAa,CAAC,CAAO,CAAC,CAChE,CAAC0D,WAAW,eACXnL,IAAA,QAAK2K,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnC5K,IAAA,SAAA4K,QAAA,CAAM,cAAE,CAAM,CAAC,CACZ,CACN,WAAA3E,MAAA,CA7BYwB,aAAa,CA8BvB,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,cAGNzH,IAAA,QAAK2K,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/B5K,IAAA,WAAQ2K,SAAS,CAAC,cAAc,CAACY,OAAO,CAAEpF,aAAc,CAAAyE,QAAA,CAAC,gBAEzD,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,cAEN5K,IAAA,QAAK2K,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B5K,IAAA,QAAK2K,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC9B,CAAClK,aAAa,cACbV,IAAA,QAAK2K,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/B1K,KAAA,QAAKyK,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC5K,IAAA,OAAA4K,QAAA,CAAI,0BAAwB,CAAI,CAAC,cACjC5K,IAAA,MAAA4K,QAAA,CAAG,uCAAqC,CAAG,CAAC,EACzC,CAAC,CACH,CAAC,cAEN1K,KAAA,QAAKyK,SAAS,CAAC,aAAa,CAAAC,QAAA,eAE1B1K,KAAA,QAAKyK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B5K,IAAA,OAAA4K,QAAA,CAAI,OAAK,CAAI,CAAC,cACd5K,IAAA,OAAI2K,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAElK,aAAa,CAAK,CAAC,EAChD,CAAC,CAELE,WAAW,GAAK,CAAC,EAAIE,UAAU,EAAI,CAAC,eACrCd,IAAA,CAACV,SAAS,EACRoE,KAAK,CAAEhD,aAAa,EAAI8C,YAAY,CAACE,KAAM,CAC3CC,QAAQ,CAAEH,YAAY,CAACG,QAAS,CAChCkI,eAAe,CAAErI,YAAY,CAACI,mBAAoB,CAClDkI,aAAa,CAAGpI,KAAK,EAAK,CACxBD,eAAe,CAAC6C,IAAI,EAAAsC,aAAA,CAAAA,aAAA,IAAUtC,IAAI,MAAE5C,KAAK,EAAG,CAAC,CAC/C,CAAE,CACFqI,gBAAgB,CAAGpI,QAAQ,EAAKF,eAAe,CAAC6C,IAAI,EAAAsC,aAAA,CAAAA,aAAA,IAAUtC,IAAI,MAAE3C,QAAQ,EAAG,CAAE,CACjFqI,uBAAuB,CAAGC,QAAQ,EAAKxI,eAAe,CAAC6C,IAAI,EAAAsC,aAAA,CAAAA,aAAA,IAAUtC,IAAI,MAAE1C,mBAAmB,CAAEqI,QAAQ,EAAG,CAAE,CAC7GC,MAAM,CAAEA,CAAA,GAAMrL,cAAc,CAAC,CAAC,CAAE,CACjC,CACF,CAEAD,WAAW,GAAK,CAAC,EAAIE,UAAU,EAAI,CAAC,eACnCd,IAAA,CAACT,UAAU,EACT4M,QAAQ,CAAE3I,YAAY,CAACK,cAAe,CACtCG,YAAY,CAAER,YAAY,CAACQ,YAAa,CACxCoI,eAAe,CAAEA,CAACC,SAAS,CAAEtI,OAAO,GAAK,CACvCN,eAAe,CAAC6C,IAAI,EAAAsC,aAAA,CAAAA,aAAA,IACftC,IAAI,MACPzC,cAAc,CAAEyC,IAAI,CAACzC,cAAc,CAACoH,GAAG,CAACqB,OAAO,EAC7CA,OAAO,CAACxI,EAAE,GAAKuI,SAAS,CAAAzD,aAAA,CAAAA,aAAA,IAAQ0D,OAAO,MAAEvI,OAAO,GAAKuI,OACvD,CAAC,EACD,CAAC,CACL,CAAE,CACFC,eAAe,CAAGF,SAAS,EAAK,CAC9B5I,eAAe,CAAC6C,IAAI,EAAAsC,aAAA,CAAAA,aAAA,IACftC,IAAI,MACPzC,cAAc,CAAEyC,IAAI,CAACzC,cAAc,CAAC2I,MAAM,CAACF,OAAO,EAAIA,OAAO,CAACxI,EAAE,GAAKuI,SAAS,CAAC,EAC/E,CAAC,CACL,CAAE,CACFI,gBAAgB,CAAGC,WAAW,EAAK,CACjCjJ,eAAe,CAAC6C,IAAI,EAAAsC,aAAA,CAAAA,aAAA,IACftC,IAAI,MACPzC,cAAc,CAAE6I,WAAW,EAC3B,CAAC,CACL,CAAE,CACFC,oBAAoB,CAAGV,QAAQ,EAAKxI,eAAe,CAAC6C,IAAI,EAAAsC,aAAA,CAAAA,aAAA,IAAUtC,IAAI,MAAEtC,YAAY,CAAEiI,QAAQ,EAAG,CAAE,CACnGW,iBAAiB,CAAEhF,qBAAsB,CACzCiF,gBAAgB,CAAE9E,oBAAqB,CACvCzE,iBAAiB,CAAEA,iBAAkB,CACrCwJ,mBAAmB,CAAE5C,uBAAwB,CAC7C6C,UAAU,CAAEA,CAAA,GAAMlM,cAAc,CAAC,CAAC,CAAE,CACpCqL,MAAM,CAAEA,CAAA,GAAMrL,cAAc,CAAC,CAAC,CAAE,CACjC,CACF,CAEAD,WAAW,GAAK,CAAC,EAAIE,UAAU,EAAI,CAAC,eACnCd,IAAA,QAAK2K,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB1K,KAAA,QAAKyK,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC1K,KAAA,QAAKyK,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC5K,IAAA,OAAA4K,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5B1K,KAAA,QAAKyK,SAAS,CAAC,cAAc,CAACY,OAAO,CAAEA,CAAA,GAAMhK,mBAAmB,CAAC,IAAI,CAAE,CAAAqJ,QAAA,eACrE1K,KAAA,QAAKyK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B5K,IAAA,SAAM2K,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,OAAK,CAAM,CAAC,cACpC5K,IAAA,SAAM2K,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAEpH,YAAY,CAACS,WAAW,CAACC,IAAI,CAAO,CAAC,EAC3D,CAAC,cACNhE,KAAA,QAAKyK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B5K,IAAA,SAAM2K,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,cACtC5K,IAAA,SAAM2K,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAEpH,YAAY,CAACS,WAAW,CAACE,MAAM,CAAO,CAAC,EAC7D,CAAC,cACNjE,KAAA,QAAKyK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B5K,IAAA,SAAM2K,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cACrC5K,IAAA,SAAM2K,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAEpH,YAAY,CAACS,WAAW,CAACG,KAAK,CAAO,CAAC,EAC5D,CAAC,cACNlE,KAAA,QAAKyK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B5K,IAAA,SAAM2K,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,gBAAc,CAAM,CAAC,cAC7C5K,IAAA,SAAM2K,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAEpH,YAAY,CAACS,WAAW,CAACI,WAAW,CAAO,CAAC,EAClE,CAAC,cACNrE,IAAA,QAAK2K,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,+BAE3B,CAAK,CAAC,EACH,CAAC,EACH,CAAC,cAEN1K,KAAA,QAAKyK,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC5K,IAAA,QAAK2K,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,uBAAW,CAAK,CAAC,cAC7C5K,IAAA,MAAA4K,QAAA,CAAG,uBAAqB,CAAG,CAAC,EACzB,CAAC,cAEN5K,IAAA,QAAK2K,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAClC5K,IAAA,WAAQ2K,SAAS,CAAC,kBAAkB,CAACY,OAAO,CAAEA,CAAA,GAAM1K,cAAc,CAAC,CAAC,CAAE,CAAC6C,KAAK,CAAC,eAAe,CAAAkH,QAAA,cAC1F5K,IAAA,SAAM2K,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAC,CAAM,CAAC,CAC/B,CAAC,CACN,CAAC,EACH,CAAC,CACH,CACN,CAEAhK,WAAW,CAAG,CAAC,eACdV,KAAA,QAAKyK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B1K,KAAA,OAAA0K,QAAA,EAAI,OAAK,CAAChK,WAAW,EAAK,CAAC,cAC3BZ,IAAA,MAAA4K,QAAA,CAAG,4CAA0C,CAAG,CAAC,cACjD1K,KAAA,QAAKyK,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC5K,IAAA,WAAQ2K,SAAS,CAAC,kBAAkB,CAACY,OAAO,CAAEA,CAAA,GAAM1K,cAAc,CAACgE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAElE,WAAW,CAAG,CAAC,CAAC,CAAE,CAAC8C,KAAK,CAAC,eAAe,CAAAkH,QAAA,cACrH5K,IAAA,SAAM2K,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAC,CAAM,CAAC,CAC/B,CAAC,CACRhK,WAAW,CAAGE,UAAU,eACvBd,IAAA,WAAQ2K,SAAS,CAAC,kBAAkB,CAACY,OAAO,CAAEA,CAAA,GAAM1K,cAAc,CAACgE,IAAI,CAACmI,GAAG,CAAClM,UAAU,CAAEF,WAAW,CAAG,CAAC,CAAC,CAAE,CAAC8C,KAAK,CAAC,WAAW,CAAAkH,QAAA,cAC1H5K,IAAA,SAAM2K,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAC,CAAM,CAAC,CAC/B,CACT,EACE,CAAC,EACH,CACN,EACI,CACN,CACE,CAAC,CACH,CAAC,EAGH,CAAC,cAGN5K,IAAA,CAACR,cAAc,EACbyN,MAAM,CAAE3L,gBAAiB,CACzB4L,OAAO,CAAEA,CAAA,GAAM3L,mBAAmB,CAAC,KAAK,CAAE,CAC1C0C,WAAW,CAAET,YAAY,CAACS,WAAY,CACtCkJ,MAAM,CAAGlJ,WAAW,EAAK,CACvBR,eAAe,CAAC6C,IAAI,EAAAsC,aAAA,CAAAA,aAAA,IAAUtC,IAAI,MAAErC,WAAW,EAAG,CAAC,CACnD1C,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CAAE,CACH,CAAC,cAGFvB,IAAA,CAACP,YAAY,EACXwN,MAAM,CAAEzL,aAAc,CACtB0L,OAAO,CAAExC,oBAAqB,CAC9B0C,QAAQ,CAAEjD,gBAAiB,CAC5B,CAAC,cAGFnK,IAAA,CAACN,YAAY,EACXuN,MAAM,CAAErL,gBAAiB,CACzBsL,OAAO,CAAEA,CAAA,GAAMrL,mBAAmB,CAAC,KAAK,CAAE,CAC1CwL,gBAAgB,CAAEjH,wBAAyB,CAC5C,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAjG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}