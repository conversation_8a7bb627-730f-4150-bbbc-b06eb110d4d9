{"name": "es-get-iterator", "version": "1.1.3", "description": "Get an iterator for any JS language value. Works robustly across all environments, all versions.", "main": "./index.js", "browser": "./index.js", "exports": {".": [{"browser": "./index.js", "import": "./node.mjs", "default": "./node.js"}, "./node.js"], "./package": "./package.json", "./package.json": "./package.json"}, "type": "commonjs", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc node test/${TEST_VARIANT-}", "tests-esm": "TEST_VARIANT=node.mjs npm run tests-only", "tests-preload-es6-shim": "TEST_VARIANT=es6-shim PRELOAD_GET_ITERATOR=true npm run tests-only", "test": "npm run tests-only && TEST_VARIANT=node npm run tests-only && npm run tests-esm && npm run tests-preload-es6-shim", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-get-iterator.git"}, "keywords": ["iterator", "Symbol.iterator", "iterable", "collection", "next", "iteration"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-get-iterator/issues"}, "homepage": "https://github.com/ljharb/es-get-iterator#readme", "devDependencies": {"@ljharb/eslint-config": "^21.0.1", "aud": "^2.0.2", "auto-changelog": "^2.4.0", "core-js": "^2.6.10 || ^3.4.2", "es5-shim": "^4.6.7", "es6-shim": "^0.35.7", "eslint": "=8.8.0", "for-each": "^0.3.3", "has-bigints": "^1.0.2", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object-inspect": "^1.12.3", "safe-publish-latest": "^2.0.0", "tape": "^5.6.1"}, "testling": {"files": "./test/index.js"}, "greenkeeper": {"ignore": ["nyc"]}, "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "has-symbols": "^1.0.3", "is-arguments": "^1.1.1", "is-map": "^2.0.2", "is-set": "^2.0.2", "is-string": "^1.0.7", "isarray": "^2.0.5", "stop-iteration-iterator": "^1.0.0"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "publishConfig": {"ignore": [".github/workflows"]}}