{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10;import React,{useState}from'react';import styled from'styled-components';import{useAuth}from'../../hooks/useAuth';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const HeaderContainer=styled.header(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 10px 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  position: relative;\\n  z-index: 100;\\n\"])));const LeftSection=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  gap: 30px;\\n\"])));const Logo=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  font-family: 'Brush Script MT', cursive;\\n  font-size: 28px;\\n  font-weight: bold;\\n  color: white;\\n  text-decoration: none;\\n  cursor: pointer;\\n  \\n  &:hover {\\n    opacity: 0.9;\\n  }\\n\"])));const Navigation=styled.nav(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 25px;\\n\"])));const NavItem=styled.a(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  color: white;\\n  text-decoration: none;\\n  font-size: 14px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  padding: 8px 12px;\\n  border-radius: 4px;\\n  transition: background-color 0.3s ease;\\n  cursor: pointer;\\n\\n  &:hover {\\n    background-color: rgba(255, 255, 255, 0.1);\\n  }\\n\"])));const RightSection=styled.div(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n\"])));const IconButton=styled.button(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  background: none;\\n  border: none;\\n  color: white;\\n  font-size: 18px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 4px;\\n  transition: background-color 0.3s ease;\\n\\n  &:hover {\\n    background-color: rgba(255, 255, 255, 0.1);\\n  }\\n\"])));const UserSection=styled.div(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n\"])));const UserAvatar=styled.div(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: bold;\\n  font-size: 14px;\\n  color: white;\\n  cursor: pointer;\\n  transition: transform 0.3s ease;\\n\\n  &:hover {\\n    transform: scale(1.1);\\n  }\\n\"])));const UserInfo=styled.div(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n\"])));const SignInText=styled.span(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.9);\\n  margin-bottom: 2px;\\n\"])));const JoinUsText=styled.span(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  font-size: 11px;\\n  color: rgba(255, 255, 255, 0.7);\\n\"])));const Header=()=>{var _user$firstName,_user$lastName;const{user,isAuthenticated,signOut}=useAuth();const[showSignInModal,setShowSignInModal]=useState(false);const handleSignInClick=()=>{if(isAuthenticated){signOut();}else{setShowSignInModal(true);}};return/*#__PURE__*/_jsxs(HeaderContainer,{children:[/*#__PURE__*/_jsxs(LeftSection,{children:[/*#__PURE__*/_jsx(Logo,{children:\"Flipbook\"}),/*#__PURE__*/_jsxs(Navigation,{children:[/*#__PURE__*/_jsx(NavItem,{href:\"#file\",children:\"FILE\"}),/*#__PURE__*/_jsx(NavItem,{href:\"#edit\",children:\"EDIT\"}),/*#__PURE__*/_jsx(NavItem,{href:\"#share\",children:\"SHARE!\"}),/*#__PURE__*/_jsx(NavItem,{href:\"#help\",children:\"HELP\"})]})]}),/*#__PURE__*/_jsxs(RightSection,{children:[/*#__PURE__*/_jsx(IconButton,{title:\"Tools\",children:\"\\uD83D\\uDD27\"}),/*#__PURE__*/_jsxs(UserSection,{children:[/*#__PURE__*/_jsx(UserAvatar,{children:isAuthenticated&&user?(((_user$firstName=user.firstName)===null||_user$firstName===void 0?void 0:_user$firstName.charAt(0))||'U')+(((_user$lastName=user.lastName)===null||_user$lastName===void 0?void 0:_user$lastName.charAt(0))||'S'):'FR'}),/*#__PURE__*/_jsxs(UserInfo,{children:[/*#__PURE__*/_jsx(SignInText,{onClick:handleSignInClick,style:{cursor:'pointer'},children:isAuthenticated?'Sign Out':'Sign In'}),!isAuthenticated&&/*#__PURE__*/_jsx(JoinUsText,{children:\"Join Us\"}),isAuthenticated&&user&&/*#__PURE__*/_jsxs(JoinUsText,{children:[user.firstName,\" \",user.lastName]})]})]})]})]});};export default Header;", "map": {"version": 3, "names": ["React", "useState", "styled", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header", "_templateObject", "_taggedTemplateLiteral", "LeftSection", "div", "_templateObject2", "Logo", "_templateObject3", "Navigation", "nav", "_templateObject4", "NavItem", "a", "_templateObject5", "RightSection", "_templateObject6", "IconButton", "button", "_templateObject7", "UserSection", "_templateObject8", "UserAvatar", "_templateObject9", "UserInfo", "_templateObject0", "SignInText", "span", "_templateObject1", "JoinUsText", "_templateObject10", "Header", "_user$firstName", "_user$lastName", "user", "isAuthenticated", "signOut", "showSignInModal", "setShowSignInModal", "handleSignInClick", "children", "href", "title", "firstName", "char<PERSON>t", "lastName", "onClick", "style", "cursor"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/LandingPage/Header.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../hooks/useAuth';\n\nconst HeaderContainer = styled.header`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: relative;\n  z-index: 100;\n`;\n\nconst LeftSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 30px;\n`;\n\nconst Logo = styled.div`\n  font-family: 'Brush Script MT', cursive;\n  font-size: 28px;\n  font-weight: bold;\n  color: white;\n  text-decoration: none;\n  cursor: pointer;\n  \n  &:hover {\n    opacity: 0.9;\n  }\n`;\n\nconst Navigation = styled.nav`\n  display: flex;\n  gap: 25px;\n`;\n\nconst NavItem = styled.a`\n  color: white;\n  text-decoration: none;\n  font-size: 14px;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  padding: 8px 12px;\n  border-radius: 4px;\n  transition: background-color 0.3s ease;\n  cursor: pointer;\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n\nconst RightSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 15px;\n`;\n\nconst IconButton = styled.button`\n  background: none;\n  border: none;\n  color: white;\n  font-size: 18px;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 4px;\n  transition: background-color 0.3s ease;\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n\nconst UserSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 10px;\n`;\n\nconst UserAvatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 14px;\n  color: white;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n\n  &:hover {\n    transform: scale(1.1);\n  }\n`;\n\nconst UserInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n`;\n\nconst SignInText = styled.span`\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n`;\n\nconst JoinUsText = styled.span`\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.7);\n`;\n\nconst Header: React.FC = () => {\n  const { user, isAuthenticated, signOut } = useAuth();\n  const [showSignInModal, setShowSignInModal] = useState(false);\n\n  const handleSignInClick = () => {\n    if (isAuthenticated) {\n      signOut();\n    } else {\n      setShowSignInModal(true);\n    }\n  };\n\n  return (\n    <HeaderContainer>\n      <LeftSection>\n        <Logo>Flipbook</Logo>\n        <Navigation>\n          <NavItem href=\"#file\">FILE</NavItem>\n          <NavItem href=\"#edit\">EDIT</NavItem>\n          <NavItem href=\"#share\">SHARE!</NavItem>\n          <NavItem href=\"#help\">HELP</NavItem>\n        </Navigation>\n      </LeftSection>\n\n      <RightSection>\n        <IconButton title=\"Tools\">\n          🔧\n        </IconButton>\n\n        <UserSection>\n          <UserAvatar>\n            {isAuthenticated && user ?\n              (user.firstName?.charAt(0) || 'U') + (user.lastName?.charAt(0) || 'S') :\n              'FR'\n            }\n          </UserAvatar>\n          <UserInfo>\n            <SignInText onClick={handleSignInClick} style={{ cursor: 'pointer' }}>\n              {isAuthenticated ? 'Sign Out' : 'Sign In'}\n            </SignInText>\n            {!isAuthenticated && <JoinUsText>Join Us</JoinUsText>}\n            {isAuthenticated && user && (\n              <JoinUsText>{user.firstName} {user.lastName}</JoinUsText>\n            )}\n          </UserInfo>\n        </UserSection>\n      </RightSection>\n    </HeaderContainer>\n  );\n};\n\nexport default Header;\n"], "mappings": "6WAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,OAAO,KAAQ,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9C,KAAM,CAAAC,eAAe,CAAGN,MAAM,CAACO,MAAM,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,sRAUpC,CAED,KAAM,CAAAC,WAAW,CAAGV,MAAM,CAACW,GAAG,CAAAC,gBAAA,GAAAA,gBAAA,CAAAH,sBAAA,kEAI7B,CAED,KAAM,CAAAI,IAAI,CAAGb,MAAM,CAACW,GAAG,CAAAG,gBAAA,GAAAA,gBAAA,CAAAL,sBAAA,wMAWtB,CAED,KAAM,CAAAM,UAAU,CAAGf,MAAM,CAACgB,GAAG,CAAAC,gBAAA,GAAAA,gBAAA,CAAAR,sBAAA,0CAG5B,CAED,KAAM,CAAAS,OAAO,CAAGlB,MAAM,CAACmB,CAAC,CAAAC,gBAAA,GAAAA,gBAAA,CAAAX,sBAAA,uUAevB,CAED,KAAM,CAAAY,YAAY,CAAGrB,MAAM,CAACW,GAAG,CAAAW,gBAAA,GAAAA,gBAAA,CAAAb,sBAAA,kEAI9B,CAED,KAAM,CAAAc,UAAU,CAAGvB,MAAM,CAACwB,MAAM,CAAAC,gBAAA,GAAAA,gBAAA,CAAAhB,sBAAA,iQAa/B,CAED,KAAM,CAAAiB,WAAW,CAAG1B,MAAM,CAACW,GAAG,CAAAgB,gBAAA,GAAAA,gBAAA,CAAAlB,sBAAA,kEAI7B,CAED,KAAM,CAAAmB,UAAU,CAAG5B,MAAM,CAACW,GAAG,CAAAkB,gBAAA,GAAAA,gBAAA,CAAApB,sBAAA,qWAiB5B,CAED,KAAM,CAAAqB,QAAQ,CAAG9B,MAAM,CAACW,GAAG,CAAAoB,gBAAA,GAAAA,gBAAA,CAAAtB,sBAAA,mFAI1B,CAED,KAAM,CAAAuB,UAAU,CAAGhC,MAAM,CAACiC,IAAI,CAAAC,gBAAA,GAAAA,gBAAA,CAAAzB,sBAAA,yFAI7B,CAED,KAAM,CAAA0B,UAAU,CAAGnC,MAAM,CAACiC,IAAI,CAAAG,iBAAA,GAAAA,iBAAA,CAAA3B,sBAAA,kEAG7B,CAED,KAAM,CAAA4B,MAAgB,CAAGA,CAAA,GAAM,KAAAC,eAAA,CAAAC,cAAA,CAC7B,KAAM,CAAEC,IAAI,CAAEC,eAAe,CAAEC,OAAQ,CAAC,CAAGzC,OAAO,CAAC,CAAC,CACpD,KAAM,CAAC0C,eAAe,CAAEC,kBAAkB,CAAC,CAAG7C,QAAQ,CAAC,KAAK,CAAC,CAE7D,KAAM,CAAA8C,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,GAAIJ,eAAe,CAAE,CACnBC,OAAO,CAAC,CAAC,CACX,CAAC,IAAM,CACLE,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CACF,CAAC,CAED,mBACEvC,KAAA,CAACC,eAAe,EAAAwC,QAAA,eACdzC,KAAA,CAACK,WAAW,EAAAoC,QAAA,eACV3C,IAAA,CAACU,IAAI,EAAAiC,QAAA,CAAC,UAAQ,CAAM,CAAC,cACrBzC,KAAA,CAACU,UAAU,EAAA+B,QAAA,eACT3C,IAAA,CAACe,OAAO,EAAC6B,IAAI,CAAC,OAAO,CAAAD,QAAA,CAAC,MAAI,CAAS,CAAC,cACpC3C,IAAA,CAACe,OAAO,EAAC6B,IAAI,CAAC,OAAO,CAAAD,QAAA,CAAC,MAAI,CAAS,CAAC,cACpC3C,IAAA,CAACe,OAAO,EAAC6B,IAAI,CAAC,QAAQ,CAAAD,QAAA,CAAC,QAAM,CAAS,CAAC,cACvC3C,IAAA,CAACe,OAAO,EAAC6B,IAAI,CAAC,OAAO,CAAAD,QAAA,CAAC,MAAI,CAAS,CAAC,EAC1B,CAAC,EACF,CAAC,cAEdzC,KAAA,CAACgB,YAAY,EAAAyB,QAAA,eACX3C,IAAA,CAACoB,UAAU,EAACyB,KAAK,CAAC,OAAO,CAAAF,QAAA,CAAC,cAE1B,CAAY,CAAC,cAEbzC,KAAA,CAACqB,WAAW,EAAAoB,QAAA,eACV3C,IAAA,CAACyB,UAAU,EAAAkB,QAAA,CACRL,eAAe,EAAID,IAAI,CACtB,CAAC,EAAAF,eAAA,CAAAE,IAAI,CAACS,SAAS,UAAAX,eAAA,iBAAdA,eAAA,CAAgBY,MAAM,CAAC,CAAC,CAAC,GAAI,GAAG,GAAK,EAAAX,cAAA,CAAAC,IAAI,CAACW,QAAQ,UAAAZ,cAAA,iBAAbA,cAAA,CAAeW,MAAM,CAAC,CAAC,CAAC,GAAI,GAAG,CAAC,CACtE,IAAI,CAEI,CAAC,cACb7C,KAAA,CAACyB,QAAQ,EAAAgB,QAAA,eACP3C,IAAA,CAAC6B,UAAU,EAACoB,OAAO,CAAEP,iBAAkB,CAACQ,KAAK,CAAE,CAAEC,MAAM,CAAE,SAAU,CAAE,CAAAR,QAAA,CAClEL,eAAe,CAAG,UAAU,CAAG,SAAS,CAC/B,CAAC,CACZ,CAACA,eAAe,eAAItC,IAAA,CAACgC,UAAU,EAAAW,QAAA,CAAC,SAAO,CAAY,CAAC,CACpDL,eAAe,EAAID,IAAI,eACtBnC,KAAA,CAAC8B,UAAU,EAAAW,QAAA,EAAEN,IAAI,CAACS,SAAS,CAAC,GAAC,CAACT,IAAI,CAACW,QAAQ,EAAa,CACzD,EACO,CAAC,EACA,CAAC,EACF,CAAC,EACA,CAAC,CAEtB,CAAC,CAED,cAAe,CAAAd,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}