﻿CKEDITOR.plugins.add('cp2',
    {
        init: function (editor) {
            editor.ui.addButton('cp2',
                {
                    label: 'cp2',
                    command: 'cmdAddButtonDialog', /*this command invoke function when you click button */
                    icon: this.path + 'cp2.png', /* path of your icon image*/
                    className:'cp2-cke-Btn'
                });  
            editor.addCommand('cmdAddButtonDialog', new CKEDITOR.dialogCommand('placeholderDialog'));  
        }
    })
CKEDITOR.dialog.add('cke23', function (editor) {
    return {
        title: 'My Dialog',
        minWidth: 400,
        minHeight: 200,
        contents: [
            {
                id: 'tab1',
                label: 'First Tab',
                title: 'First Tab',
                elements:
                    [
                        {
                            id: 'input1',
                            type: 'text',
                            label: 'Input 1'
                        }
                    ]
            }
        ],
        buttons: [
            CKEDITOR.dialog.okButton,

            CKEDITOR.dialog.cancelButton,

            {
                id: 'unique name',
                label: 'Custom Button',
                title: 'Button description',
                accessKey: 'C',
                disabled: false,
                onClick: function () {
                    // code on click
                }
            }
        ]

    };
});


CKEDITOR.plugins.add('cke22',
    {
        init: function (editor) {
            var config = editor.config,
                lang = editor.lang.format;
            var trackings = [];
            var CkeLetterspacingVal;
            config.allowedContent = 'span'; //There may be a better way to do this.
            var pluginName = 'newplugin';
            //editor.ui.addButton('Newplugin',
            //editor.ui.addRichCombo('Newplugin',
            //    {
            //        label: 'My New Plugin',
            //        command: 'OpenWindow',
            //        //icon: "asd",
            //        //contents: [{
            //        //    id: 'page1',  /* not CSS ID attribute! */
            //        //    label: 'Page1',
            //        //    accessKey: 'P',
            //        //    elements: [{
            //        //        type: 'html',
            //        //        html: '<div>Cell1</div>',
            //        //    }]
            //        //}]
            //    });

                editor.ui.addRichCombo('cke2', {
                    label: 'Custom Color',// 'Text Spacing'
                    title: 'Change letter-spacing',
                    toolbar: 'custgrp,',
                    voiceLabel: 'Change letter-spacing',
                    className: 'cke_format ckeKerningClass',
                    multiSelect: false,

                    panel: {
                        css: [config.contentsCss, CKEDITOR.getUrl(CKEDITOR.skin.getPath('editor') + 'editor.css'), CKEDITOR.getUrl(CKEDITOR.skin.getPath('colorpicker') + 'colorpicker.css')]
                    },

                    init: function () {
                        this.startGroup('Kerning');
                        
                        
                        this.add("", `<div class="innderbox divcp2StandardColr">
            <div class="standardcolor"> 
                <div data-content="Our curated palette" class="titlecolor popupoverright" data-original-title="" title="">Standard Colors <span class="spanStandColName" style="color: rgb(0, 0, 0);">Black</span></div>             

                <div class="clear"></div>
            <span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #000   ;background:#000   " data-color="#000" data-colorname="Black"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #646464;background:#646464" data-color="#646464" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #c01f2a;background:#c01f2a" data-color="#c01f2a" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #234f1e;background:#234f1e" data-color="#234f1e" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #00339a;background:#00339a" data-color="#00339a" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #410166;background:#410166" data-color="#410166" data-colorname="Purple"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #573725;background:#573725" data-color="#573725" data-colorname="Brown"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #fb6b01;background:#fb6b01" data-color="#fb6b01" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #feeb75;background:#feeb75" data-color="#feeb75" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #979797;background:#ffffff" data-color="#ffffff" data-colorname="White"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #afafaf;background:#afafaf" data-color="#afafaf" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #fc46aa;background:#fc46aa" data-color="#fc46aa" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #c1d0be;background:#c1d0be" data-color="#c1d0be" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #ddeffa;background:#ddeffa" data-color="#ddeffa" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #c9c4e7;background:#c9c4e7" data-color="#c9c4e7" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #d2c4b1;background:#d2c4b1" data-color="#d2c4b1" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #ff9685;background:#ff9685" data-color="#ff9685" data-colorname="null"></span><span id="colNewSelectedColor" data-color="#ffffff" data-colorname="clear" onmouseover="displayColorName(this);" style="cursor:pointer; position:absolute; right:7px; display: flex;margin-top: 20px;"><img src="/images/transparent.png" style="width:18px"></span></div>
                    <div style="clear:both"></div>
                    <div class="mypalette">
                        <div class="tooltiptext" style="display:none"> Delete colors in  Itten Star</div>
                        <div class="titlecolor popupoverright" data-content="Delete colors in  Itten Star" data-original-title="" title="">My Palette</div>
                        <div class="carousel slide media-carousel" data-interval="false" id="divCp2ColorBoxSlider">
                            <a data-slide="prev" id="aCp2ToolPrev" href="#divCp2ColorBoxSlider" class="left carousel-control aFrameColorNavig" style="display: none;"></a>
                            <a data-slide="next" id="aCp2ToolNext" href="#divCp2ColorBoxSlider" class="right carousel-control aFrameColorNavig" style="display: none;"></a>
                            <div>
                                <div class="carousel-inner ParentCp2CustomColorBoxItems"><div class="item active divCp2CustomColorBoxItems">  <span class="color spanCustColor" id="spanCp2ColorBox1" data-color="fac4af" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(250, 196, 175);"></span><span class="color spanCustColor" id="spanCp2ColorBox2" data-color="f59070" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(245, 144, 112);"></span><span class="color spanCustColor" id="spanCp2ColorBox3" data-color="fdec59" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(253, 236, 89);"></span><span class="color spanCustColor" id="spanCp2ColorBox4" data-color="204498" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(32, 68, 152);"></span><span class="color spanCustColor" id="spanCp2ColorBox5" data-color="f16624" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(241, 102, 36);"></span><span class="color spanCustColor" id="spanCp2ColorBox6" data-color="c1842b" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(193, 132, 43);"></span><span class="color  empty" id="spanCp2ColorBox7"></span><span class="color  empty" id="spanCp2ColorBox8"></span><span class="color  empty" id="spanCp2ColorBox9"></span><span class="color  empty" id="spanCp2ColorBox10"></span><span class="color  empty" id="spanCp2ColorBox11"></span><span class="color  empty" id="spanCp2ColorBox12"></span><span class="color  empty" id="spanCp2ColorBox13"></span><span class="color  empty" id="spanCp2ColorBox14"></span></div></div>
                            </div>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="morecolor">
                        <div class="tooltiptext" style="display:none"> More color pickers tools</div>
                        <div class="titlecolor popupoverright" data-content="More color pickers tools" data-original-title="" title="">More Colors</div>
                        <ul class="ulCp2MoreColors vrhide">
                            <li><span class="cp2OpenCpPoupop" onclick="OnClick_cp2OpenCpPopUp(this);" data-toggle="modal" data-target="#myModal" data-backdrop="static" data-keyboard="false" data-cptab="IttanStar"><img src="/images/ImageEditor/itten-art-small.png"></span></li>
                            <li><span class="cp2OpenCpPoupop" onclick="OnClick_cp2OpenCpPopUp(this);" data-toggle="modal" data-target="#myModal" data-backdrop="static" data-keyboard="false" data-cptab="CIRCLE"><img src="/images/ImageEditor/ColorWheel-small.png"></span></li>
                            <li><span class="cp2OpenCpPoupop" onclick="CKEDITOR.tools.callFunction(myvar);" data-toggle="modal" data-target="#myModal" data-backdrop="static" data-keyboard="false"  data-cptab="PASTELBOX"><img src="/images/ImageEditor/turquoise-small.png"></span></li>
                            <li><span class="cp2OpenCpPoupop" onclick="OnClick_cp2OpenCpPopUp(this);" data-toggle="modal" data-target="#myModal" data-backdrop="static" data-keyboard="false"  data-cptab="ARTWORK"><img src="/images/ImageEditor/square-small.png"></span></li>
                        </ul>
                    </div>
                </div>`, "");
                        ////ST-1668,BV
                        //for (var this_letting in trackings) {

                        //    var ckeLetterVal = $(trackings[this_letting])
                        //    if (ckeLetterVal[1] != undefined) {
                        //        this.add(ckeLetterVal[1], ckeLetterVal[0], ckeLetterVal[1]);
                        //    }
                        //    else {
                        //        this.add(ckeLetterVal[0], ckeLetterVal[0], ckeLetterVal[0]);
                        //    }

                        //}

                    },

                    onClick: function (value) {
                        //editor.focus();
                        //editor.fire('saveSnapshot');
                        //var ep = editor.elementPath();
                        //var style = new CKEDITOR.style({ styles: { 'color': "#ffffff" } });
                        //editor[style.checkActive(ep) ? 'removeStyle' : 'applyStyle'](style);
                        //editor.fire('saveSnapshot');
                        editor.openDialog('placeholderDialog');

                        //CkeLetterspacingVal = value;//ST-1354
                        //editor.focus();
                        //editor.fire('saveSnapshot');
                        //var ep = editor.elementPath();
                        //setTimeout(function () { this.label = (value + 'Kerning '); }, 200) //ST-1354
                        //var style = new CKEDITOR.style({ styles: { 'letter-spacing': value } });
                        //editor[style.checkActive(ep) ? 'removeStyle' : 'applyStyle'](style);
                        ////ST-1354
                        //SetCkeKerningValueDefault(CkeLetterspacingVal);
                        //editor.fire('saveSnapshot');

                    },


                })

            //editor.ui.addButton(
            //    'helloworld.btn',
            //    {
            //        label: "My Plug-in",
            //        command: 'helloworld.cmd',
            //        //icon: mypath + 'images/helloworld.gif'
            //    }
            //);
           
        //    CKEDITOR.dialog.addIframe(
        //        'helloworld.dlg',
        //        'Hello Title',
        //        `<div class="innderbox divcp2StandardColr">
        //<div class="standardcolor"> 
        //    <div data-content="Our curated palette" class="titlecolor popupoverright" data-original-title="" title="">Standard Colors <span class="spanStandColName" style="color: rgb(0, 0, 0);">Black</span></div>             

        //    <div class="clear"></div>
        //<span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #000   ;background:#000   " data-color="#000" data-colorname="Black"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #646464;background:#646464" data-color="#646464" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #c01f2a;background:#c01f2a" data-color="#c01f2a" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #234f1e;background:#234f1e" data-color="#234f1e" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #00339a;background:#00339a" data-color="#00339a" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #410166;background:#410166" data-color="#410166" data-colorname="Purple"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #573725;background:#573725" data-color="#573725" data-colorname="Brown"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #fb6b01;background:#fb6b01" data-color="#fb6b01" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #feeb75;background:#feeb75" data-color="#feeb75" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #979797;background:#ffffff" data-color="#ffffff" data-colorname="White"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #afafaf;background:#afafaf" data-color="#afafaf" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #fc46aa;background:#fc46aa" data-color="#fc46aa" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #c1d0be;background:#c1d0be" data-color="#c1d0be" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #ddeffa;background:#ddeffa" data-color="#ddeffa" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #c9c4e7;background:#c9c4e7" data-color="#c9c4e7" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #d2c4b1;background:#d2c4b1" data-color="#d2c4b1" data-colorname="null"></span><span class="color" onmouseover="displayColorName(this);" onclick="cp2SetCurrentColorToObj(this)" ;="" style="border: solid 1px #ff9685;background:#ff9685" data-color="#ff9685" data-colorname="null"></span><span id="colNewSelectedColor" data-color="#ffffff" data-colorname="clear" onmouseover="displayColorName(this);" style="cursor:pointer; position:absolute; right:7px; display: flex;margin-top: 20px;"><img src="/images/transparent.png" style="width:18px"></span></div>
        //        <div style="clear:both"></div>
        //        <div class="mypalette">
        //            <div class="tooltiptext" style="display:none"> Delete colors in  Itten Star</div>
        //            <div class="titlecolor popupoverright" data-content="Delete colors in  Itten Star" data-original-title="" title="">My Palette</div>
        //            <div class="carousel slide media-carousel" data-interval="false" id="divCp2ColorBoxSlider">
        //                <a data-slide="prev" id="aCp2ToolPrev" href="#divCp2ColorBoxSlider" class="left carousel-control aFrameColorNavig" style="display: none;"></a>
        //                <a data-slide="next" id="aCp2ToolNext" href="#divCp2ColorBoxSlider" class="right carousel-control aFrameColorNavig" style="display: none;"></a>
        //                <div>
        //                    <div class="carousel-inner ParentCp2CustomColorBoxItems"><div class="item active divCp2CustomColorBoxItems">  <span class="color spanCustColor" id="spanCp2ColorBox1" data-color="fac4af" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(250, 196, 175);"></span><span class="color spanCustColor" id="spanCp2ColorBox2" data-color="f59070" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(245, 144, 112);"></span><span class="color spanCustColor" id="spanCp2ColorBox3" data-color="fdec59" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(253, 236, 89);"></span><span class="color spanCustColor" id="spanCp2ColorBox4" data-color="204498" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(32, 68, 152);"></span><span class="color spanCustColor" id="spanCp2ColorBox5" data-color="f16624" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(241, 102, 36);"></span><span class="color spanCustColor" id="spanCp2ColorBox6" data-color="c1842b" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(193, 132, 43);"></span><span class="color  empty" id="spanCp2ColorBox7"></span><span class="color  empty" id="spanCp2ColorBox8"></span><span class="color  empty" id="spanCp2ColorBox9"></span><span class="color  empty" id="spanCp2ColorBox10"></span><span class="color  empty" id="spanCp2ColorBox11"></span><span class="color  empty" id="spanCp2ColorBox12"></span><span class="color  empty" id="spanCp2ColorBox13"></span><span class="color  empty" id="spanCp2ColorBox14"></span></div></div>
        //                </div>
        //            </div>
        //            <div class="clearfix"></div>
        //        </div>
        //        <div class="morecolor">
        //            <div class="tooltiptext" style="display:none"> More color pickers tools</div>
        //            <div class="titlecolor popupoverright" data-content="More color pickers tools" data-original-title="" title="">More Colors</div>
        //            <ul class="ulCp2MoreColors vrhide">
        //                <li><spn class="cp2OpenCpPoupop" onclick="OnClick_cp2OpenCpPopUp(this);" data-toggle="modal" data-target="#myModal" data-cptab="IttanStar"><img src="/images/ImageEditor/itten-art-small.png"></spn></li>
        //                <li><spn class="cp2OpenCpPoupop" onclick="OnClick_cp2OpenCpPopUp(this);" data-toggle="modal" data-target="#myModal" data-cptab="CIRCLE"><img src="/images/ImageEditor/ColorWheel-small.png"></spn></li>
        //                <li><span class="cp2OpenCpPoupop" onclick="OnClick_cp2OpenCpPopUp(this);" data-toggle="modal" data-target="#myModal" data-cptab="PASTELBOX"><img src="/images/ImageEditor/turquoise-small.png"></span></li>
        //                <li><span class="cp2OpenCpPoupop" onclick="OnClick_cp2OpenCpPopUp(this);" data-toggle="modal" data-target="#myModal" data-cptab="ARTWORK"><img src="/images/ImageEditor/square-small.png"></span></li>
        //            </ul>
        //        </div>
        //    </div>`,
        //        400,
        //        300,
        //        function () {
        //        }
        //    );

            var cmd = editor.addCommand('OpenWindow', { exec: showMyDialog });
        }

        //init: function (editor) {
        //    var pluginName = 'footnote';
        //    CKEDITOR.dialog.add(pluginName, this.path + 'dialogs/footnote.js');
        //    editor.addCommand(pluginName, new CKEDITOR.dialogCommand(pluginName));
        //    editor.ui.addButton('Footnote',
        //        {
        //            label: 'Footnote or Citation',
        //            command: pluginName
        //        });
        //}
    });
CKEDITOR.on("instanceReady", function (event) {
    CKEDITOR.dialog.add('placeholderDialog', function (editor) {
        
        var colorstr = getColors(editor.name);
        AddCustomClass(editor.name);
        return {
            title: '',
            minWidth: 192, minHeight: 230,
            className: 'myClassName cp2-cke-Body2',
            panel: {
                css: [editor.config.contentsCss, CKEDITOR.getUrl(CKEDITOR.skin.getPath('editor') + 'editor.css'), CKEDITOR.getUrl(CKEDITOR.skin.getPath('colorpicker') + 'colorpicker.css')]
                ,className: "cp2-cke-panel"
            },
            contents:
                [
                    {
                        id: 'general', label: 'Settings', className: "testclass1 cp2-cke-Body3",
                        elements:
                            [
                                {
                                    type: 'html', html: `<div class="innderbox divcp2StandardColr cp2-cke-Body">
                                                            <div class="standardcolor"> 
                                                                <div data-content="Our curated palette" class="titlecolor popupoverright" data-original-title="" title="">Standard Colors <span class="spanStandColName" style="color: rgb(0, 0, 0);">Black</span></div>
                                                                <div class="clear"></div>`+ colorstr +`
                                                            
<span class="cp2colortran"><img src="/images/transparent.png" style="width:18px"></span></div>
                                                                    <div style="clear:both"></div>

                                                                </div>`
                                }
                            ]
                    }
                ]
        };
        
    });
    //<div class="mypalette">
    //    <div class="tooltiptext" style="display:none"> Delete colors in  Itten Star</div>
    //    <div class="titlecolor popupoverright" data-content="Delete colors in  Itten Star" data-original-title="" title="">My Palette</div>
    //    <div class="carousel slide media-carousel" data-interval="false" id="divCp2ColorBoxSlider">
    //        <div>
    //            <div class="carousel-inner ParentCp2CustomColorBoxItems"><div class="item active divCp2CustomColorBoxItems">  <span class="color spanCustColor" id="spanCp2ColorBox1" data-color="fac4af" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(250, 196, 175);"></span><span class="color spanCustColor" id="spanCp2ColorBox2" data-color="f59070" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(245, 144, 112);"></span><span class="color spanCustColor" id="spanCp2ColorBox3" data-color="fdec59" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(253, 236, 89);"></span><span class="color spanCustColor" id="spanCp2ColorBox4" data-color="204498" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(32, 68, 152);"></span><span class="color spanCustColor" id="spanCp2ColorBox5" data-color="f16624" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(241, 102, 36);"></span><span class="color spanCustColor" id="spanCp2ColorBox6" data-color="c1842b" onclick="cp2SetCurrentColorToObj(this)" style="background: rgb(193, 132, 43);"></span><span class="color  empty" id="spanCp2ColorBox7"></span><span class="color  empty" id="spanCp2ColorBox8"></span><span class="color  empty" id="spanCp2ColorBox9"></span><span class="color  empty" id="spanCp2ColorBox10"></span><span class="color  empty" id="spanCp2ColorBox11"></span><span class="color  empty" id="spanCp2ColorBox12"></span><span class="color  empty" id="spanCp2ColorBox13"></span><span class="color  empty" id="spanCp2ColorBox14"></span></div></div>
    //        </div>
    //    </div>
    //    <div class="clearfix"></div>
    //</div>
    //    <div class="morecolor">
    //        <div class="tooltiptext" style="display:none"> More color pickers tools</div>
    //        <div class="titlecolor popupoverright" data-content="More color pickers tools" data-original-title="" title="">More Colors</div>
    //        <ul class="ulCp2MoreColors vrhide">
    //            <li><spn class="cp2OpenCpPoupop" onclick="OnClick_cp2OpenCpPopUp(this);" data-toggle="modal" data-target="#myModal" data-cptab="IttanStar"><img src="/images/ImageEditor/itten-art-small.png"></spn></li>
    //                <li><spn class="cp2OpenCpPoupop" onclick="OnClick_cp2OpenCpPopUp(this);" data-toggle="modal" data-target="#myModal" data-cptab="CIRCLE"><img src="/images/ImageEditor/ColorWheel-small.png"></spn></li>
    //                    <li><span class="cp2OpenCpPoupop" onclick="CKEDITOR.tools.callFunction( " ref" ,this);" data-toggle="modal" data-target="#myModal" data-cptab="PASTELBOX"><img src="/images/ImageEditor/turquoise-small.png"></span></li>
    //                    <li><span class="cp2OpenCpPoupop" onclick="OnClick_cp2OpenCpPopUp(this);" data-toggle="modal" data-target="#myModal" data-cptab="ARTWORK"><img src="/images/ImageEditor/square-small.png"></span></li>
    //                                                                    </ul>
    //                                                                </div>
    //put your code here

});


function getColors(editorname) {
    var str = "";
    for (var i = 0; i < ColorLst.length; i++) {
        if (ColorLst[i].ColorCode == '#ffffff') {
            str += '<span class="color" onmouseout="displayColorName(this,true);" onmouseover="displayColorName(this);" onclick="CKEDITOR.plugins.ck2Function.hello(this,' + editorname + ')"; style="border: solid 1px #979797;background:' + ColorLst[i].ColorCode + '"  data-color=' + ColorLst[i].ColorCode + ' data-colorName =' + ColorLst[i].ColorName + ' ></span>';
        }else{
            str += '<span class="color" onmouseout="displayColorName(this,true);" onmouseover="displayColorName(this);" onclick="CKEDITOR.plugins.ck2Function.hello(this,' + editorname + ')"; style="border: solid 1px ' + ColorLst[i].ColorCode + ';background:' + ColorLst[i].ColorCode + '"  data-color=' + ColorLst[i].ColorCode + ' data-colorName =' + ColorLst[i].ColorName + ' ></span>';
        }
    }
    return str;
}

var className = '';
/*ST-1478,VR,BV*/
function AddCustomClass(editorName)
{
    className = '.cke_editor_' + editorName + '_dialog'
    setTimeout(function () {
        $(className).addClass('cp2-cke-Parent');
    },0);
    
}

var myvar = CKEDITOR.tools.addFunction("OnClick_cp2OpenCpPopUp", function (Obj) {
    var CallFrom = $(Obj).parents('.divCp2Main').attr('data-callfrom');
    CallOnColorPopUpOpen(CallFrom);
});
CKEDITOR.plugins.add('ck2Function', {
    init: function (editor) {
        //alert(1);
        //var CallFrom = $(Obj).parents('.divCp2Main').attr('data-callfrom');
        //CallOnColorPopUpOpen(CallFrom);
        }
});
CKEDITOR.plugins.ck2Function = {
    hello: function (Obj, editor) {
        debugger
        var editorId = "";
        editorId = $(editor).attr("id");
        var color = $(Obj).attr("data-color");
        var editor1 = "";
        editor1 = CKEDITOR.instances[editorId];
        var styles = new CKEDITOR.style({ attributes: { 'style': 'color:' + color } });
        editor1.applyStyle(styles);
        if (editor1._.elementsPath.list != undefined && editor1._.elementsPath.list[0] != undefined) {
            var color = $(editor1._.elementsPath.list[0].$).css("color");
            var btns = $("a.cp2-cke-Btn");
            for (var i = 0; i < btns.length; i++) {
                $(btns[i]).css('cssText', "background-color:" + color + '!important');
            }
        }
        FontSelector = false;
        CKEDITOR.dialog.getCurrent().hide();
        }
    };
function OnClick_cp2OpenCpPopUp(Obj) {

    var CallFrom = $(Obj).parents('.divCp2Main').attr('data-callfrom');
    CallOnColorPopUpOpen(CallFrom);

}
CKEDITOR.on("OnClick_cp2OpenCpPopUp", function (event, Obj) {
    
    var CallFrom = $(Obj).parents('.divCp2Main').attr('data-callfrom');
    CallOnColorPopUpOpen(CallFrom);
})
function showMyDialog(e) {
    
    $("#myModal").modal("show");
    
    //window.open('/Views/Shared/_ColorPickerToolBar.cshtml', 'MyWindow', 'width=800,height=700,scrollbars=no,scrolling=no,location=no,toolbar=no');
}



//CKEDITOR.plugins.add('customColorToolbar',
//    {
//        //init: function (editor) {
//        //    editor.ui.addButton(
//        //        'helloworld.btn',
//        //        {
//        //            label: "My Plug-in",
//        //            command: 'helloworld.cmd',
//        //            //icon: mypath + 'images/helloworld.gif'
//        //        },
//        //        onClick: function (value) {

//        //        }
//        //    );
//        //},
//        requires: ['dialog'],
//        onLoad: function () {
//            /**
//     * An iframe base dialog.
//     * @param {String} name Name of the dialog
//     * @param {String} title Title of the dialog
//     * @param {Number} minWidth Minimum width of the dialog
//     * @param {Number} minHeight Minimum height of the dialog
//     * @param {Function} [onContentLoad] Function called when the iframe has been loaded.
//     * If it isn't specified, the inner frame is notified of the dialog events ('load',
//     * 'resize', 'ok' and 'cancel') on a function called 'onDialogEvent'
//     * @param {Object} [userDefinition] Additional properties for the dialog definition
//     * @example
//     */
//            CKEDITOR.dialog.addIframe = function (name, title, src, minWidth, minHeight, onContentLoad, userDefinition) {
//                var element =
//                {
//                    type: 'iframe',
//                    src: src,
//                    width: '100%',
//                    height: '100%'

//                };

//                if (typeof (onContentLoad) == 'function')
//                    element.onContentLoad = onContentLoad;
//                else
//                    element.onContentLoad = function () {
//                        var element = this.getElement(),
//                            childWindow = element.$.contentWindow;

//                        // If the inner frame has defined a "onDialogEvent" function, setup listeners
//                        if (childWindow.onDialogEvent) {
//                            var dialog = this.getDialog(),
//                                notifyEvent = function (e) {
//                                    return childWindow.onDialogEvent(e);

//                                };

//                            dialog.on('ok', notifyEvent);
//                            dialog.on('cancel', notifyEvent);
//                            dialog.on('resize', notifyEvent);

//                            // Clear listeners
//                            dialog.on('hide', function (e) {
//                                dialog.removeListener('ok', notifyEvent);
//                                dialog.removeListener('cancel', notifyEvent);
//                                dialog.removeListener('resize', notifyEvent);

//                                e.removeListener();
//                            });

//                            // Notify child iframe of load:
//                            childWindow.onDialogEvent({
//                                name: 'load',
//                                sender: this,
//                                editor: dialog._.editor
//                            });

//                        }



//                        var definition =
//                        {
//                            title: title,
//                            minWidth: minWidth,
//                            minHeight: minHeight,
//                            contents:
//                                [
//                                    {
//                                        id: 'iframe',
//                                        label: title,
//                                        expand: true,
//                                        elements: [element]
//                                    }
//                                ]
//                        };

//                        for (var i in userDefinition)
//                            definition[i] = userDefinition[i];

//                        this.add(name, function () { return definition; });
//                    };

//                (function () {
//                    /**
//                                    * An iframe element.
//                                    * @extends CKEDITOR.ui.dialog.uiElement
//                                    * @example
//                                    * @constructor
//                                    * @param {CKEDITOR.dialog} dialog
//                                    * Parent dialog object.
//                                    * @param {CKEDITOR.dialog.definition.uiElement} elementDefinition
//                                    * The element definition. Accepted fields:
//                                    * <ul>
//                                    * 	<li><strong>src</strong> (Required) The src field of the iframe. </li>
//                                    * 	<li><strong>width</strong> (Required) The iframe's width.</li>
//                                    * 	<li><strong>height</strong> (Required) The iframe's height.</li>
//                                    * 	<li><strong>onContentLoad</strong> (Optional) A function to be executed
//                                    * 	after the iframe's contents has finished loading.</li>
//                                    * </ul>
//                                    * @param {Array} htmlList
//                                    * List of HTML code to output to.
//                                    */
//                    var iframeElement = function (dialog, elementDefinition, htmlList) {
//                        if (arguments.length < 3)
//                            return;

//                        var _ = (this._ || (this._ = {})),
//                            contentLoad = elementDefinition.onContentLoad && CKEDITOR.tools.bind(elementDefinition.onContentLoad, this),
//                            cssWidth = CKEDITOR.tools.cssLength(elementDefinition.width),
//                            cssHeight = CKEDITOR.tools.cssLength(elementDefinition.height);
//                        _.frameId = CKEDITOR.tools.getNextId() + '_iframe';

//                        // IE BUG: Parent container does not resize to contain the iframe automatically.
//                        dialog.on('load', function () {
//                            var iframe = CKEDITOR.document.getById(_.frameId),
//                                parentContainer = iframe.getParent();

//                            parentContainer.setStyles(
//                                {
//                                    width: cssWidth,
//                                    height: cssHeight

//                                });
//                        });

//                        var attributes =
//                        {
//                            src: '%2',
//                            id: _.frameId,
//                            frameborder: 0,
//                            allowtransparency: true

//                        };
//                        var myHtml = [];

//                        if (typeof (elementDefinition.onContentLoad) == 'function')
//                            attributes.onload = 'CKEDITOR.tools.callFunction(%1);';

//                        CKEDITOR.ui.dialog.uiElement.call(this, dialog, elementDefinition, myHtml, 'iframe',
//                            {
//                                width: cssWidth,
//                                height: cssHeight

//                            }, attributes, '');

//                        // Put a placeholder for the first time.
//                        htmlList.push('<div style="width:' + cssWidth + ';height:' + cssHeight + ';" id="' + this.domId + '"></div>');

//                        // Iframe elements should be refreshed whenever it is shown.
//                        myHtml = myHtml.join('');
//                        dialog.on('show', function () {
//                            var iframe = CKEDITOR.document.getById(_.frameId),
//                                parentContainer = iframe.getParent(),
//                                callIndex = CKEDITOR.tools.addFunction(contentLoad),
//                                html = myHtml.replace('%1', callIndex).replace('%2', CKEDITOR.tools.htmlEncode(elementDefinition.src));
//                            parentContainer.setHtml(html);
//                        });
//                    };

//                    iframeElement.prototype = new CKEDITOR.ui.dialog.uiElement;

//                    CKEDITOR.dialog.addUIElement('iframe',
//                        {
//                            build: function (dialog, elementDefinition, output) {
//                                return new iframeElement(dialog, elementDefinition, output);
//                            }
//                        });
//                })();
//            }
//        }
//    } );

