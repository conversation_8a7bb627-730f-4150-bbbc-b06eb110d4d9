( function() {
    function addCombo(editor, comboName, styleType, lang, entries, defaultLabel, styleDefinition, order) {
        
		var config = editor.config,style = new CKEDITOR.style( styleDefinition );		
		var names = entries.split( ';' ),values = [];		
		var styles = {};
		for ( var i = 0; i < names.length; i++ ) {
			var parts = names[ i ];
			if ( parts ) {
				parts = parts.split( '/' );
				var vars = {},name = names[ i ] = parts[ 0 ];
				vars[ styleType ] = values[ i ] = parts[ 1 ] || name;
				styles[ name ] = new CKEDITOR.style( styleDefinition, vars );
				styles[ name ]._.definition.name = name;
			} else
				names.splice( i--, 1 );
		}
		editor.ui.addRichCombo( comboName, {
			label: editor.lang.lineheight.title,
            title: editor.lang.lineheight.title,
			toolbar: 'custgrp,' + order,
			allowedContent: style,
			requiredContent: style,
            className: 'ckeLineheightclass',/*ST-1354*/
            
			panel: {
                // PK 10/10/2017 ST-520 Line Height Adjust to Normal Instead of Having Line Height Same as Dropdown val.
                //ST-1480
                css: [CKEDITOR.skin.getPath('editor')].concat(config.contentsCss).concat('.cke_panel_listItem a span { line-height: normal !important; } .cke_panel_listItem a{font-size:13px;padding: 2px 8px;} .cke_panel_grouptitle{font-size:12px;}'),
				multiSelect: false,
				attributes: { 'aria-label': editor.lang.lineheight.title }
			},
			init: function() {
                this.startGroup(editor.lang.lineheight.title);
                debugger;
				for ( var i = 0; i < names.length; i++ ) {
                    var name = names[i];					
                    //ST-1480
                    var DisplayText = styles[name].buildPreview();
                    if (styles[name]._.definition.name == 1) {
                        DisplayText = (styles[name].buildPreview()) + " Standard" 
                    }
                    this.add(name, DisplayText , (name) );
				}
			},
			onClick: function( value ) {
				editor.focus();
				editor.fire('saveSnapshot'); 
				
				var style = styles[ value ];
				editor[this.getValue() == value ? 'removeStyle' : 'applyStyle'](style);
				SetCkeLeadingValueDefault(value); 
				editor.fire( 'saveSnapshot' );
			},
			onRender: function() {
				editor.on( 'selectionChange', function( ev ) {
					var currentValue = this.getValue();
					var elementPath = ev.data.path,elements = elementPath.elements;
					for ( var i = 0, element; i < elements.length; i++ ) {
						element = elements[ i ];
                        for (var value in styles) {
							if ( styles[ value ].checkElementMatch( element, true, editor ) ) {
							    if (value != currentValue)							        
                                    this.setValue(value); //ST-1480
                                SetCkeLeadingValueDefault(value); /*ST-1354*/
								return;
							}
						}
					}
//					this.setValue( '', defaultLabel ); //ST-1480
					this.setValue(1);
		            SetCkeLeadingValueDefault(1);
				}, this );
			},
			refresh: function() {
				if ( !editor.activeFilter.check( style ) )
					this.setState( CKEDITOR.TRISTATE_DISABLED );
			},
			mark: function (value) {
                        this.multiSelect || this.unmarkAll();
                        a = this._.items[value];
                        var b = this.element.getDocument().getById(a);
                        b.addClass("cke_selected");
                        this.element.getDocument().getById(a + "_option").setAttribute("aria-selected", !0);
                        this.onMark && this.onMark(b)
                    },
			unmark: function (value) {
                        var b = this.element.getDocument();
                        a = this._.items[value];
                        var c = b.getById(a);
                        c.removeClass("cke_selected");
                        b.getById(a + "_option").removeAttribute("aria-selected");
                        this.onUnmark && this.onUnmark(c)
                    },
                    unmarkAll: function() {
                        var a = this._.items,
                            b = this.element.getDocument(),
                            c;
                        for (c in a) {
                            var d = a[c];
                            b.getById(d).removeClass("cke_selected");
                            b.getById(d + "_option").removeAttribute("aria-selected")
                        }
                        this.onUnmark &&
                            this.onUnmark()
                    },
                    isMarked: function (value) {
                        return this.element.getDocument().getById(this._.items[value]).hasClass("cke_selected")
                    }
		} );
	}
	CKEDITOR.plugins.add( 'lineheight', {
		requires: 'richcombo',
		lang: 'ar,de,en,es,fr,ko,pt',
		init: function( editor ) {
            var config = editor.config;
//            addCombo(editor, 'lineheight', 'size', editor.lang.lineheight.title, config.line_height, '0 ' +editor.lang.lineheight.title, config.lineHeight_style, 0 );   //ST-1480
     addCombo(editor, 'lineheight', 'size', editor.lang.lineheight.title, config.line_height, '1 ' +editor.lang.lineheight.title, config.lineHeight_style, 0 );
		}
	} );
})();
// PK ST-912 , Max Value For Line Height will be 5.
//ST-1668,BV,2019 03 13
//CKEDITOR.config.line_height = '0;0.75;1;1.25;1.5;1.75;2;2.5;3;3.5;4;4.5;5;';//CD 9/4/2018 912.5 '1;2;3;4;5;';
/// CKEDITOR.config.line_height = '0.75;1.0;1.25;1.50;1.75;2.0;2.5;3.0;';//ST-1480,BV 2019 09 17 ST-1480
CKEDITOR.config.line_height = '0.75;1;1.25;1.50;1.75;2.0;2.5;3.0;';//ST-1480,BV 2019 09 17
CKEDITOR.config.lineHeight_style = {
	element: 'span',
	styles: { 'line-height': '#(size)' },
	overrides: [ {
		element: 'line-height', attributes: { 'size': null }
	} ]
};
