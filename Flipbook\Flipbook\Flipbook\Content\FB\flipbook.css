@font-face {font-family: 'arrows'; src:url('/fonts/arrows/arrows.eot'); src:url('/fonts/arrows/arrows.eot?#iefix') format('embedded-opentype'), url('/fonts/arrows/arrows.woff') format('woff'), url('/fonts/arrows/arrows.ttf') format('truetype'), url('/fonts/arrows/arrows.svg#arrows') format('svg'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'ProximaNova-Bold'; src: url('/fonts/Flipbook/ProximaNova-Bold.eot'); src: url('/fonts/Flipbook/ProximaNova-Bold.woff2') format('woff2'), url('/fonts/Flipbook/ProximaNova-Bold.woff')format('woff'), url('/fonts/Flipbook/ProximaNova-Bold.ttf') format('truetype'), url('/fonts/Flipbook/ProximaNova-Bold.svg#ProximaNova-Bold') format('svg'), url('/fonts/Flipbook/ProximaNova-Bold.eot?#iefix') format('embedded-opentype'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'ProximaNova-Light'; src: url('/fonts/Flipbook/ProximaNova-Light.eot'); src: url('/fonts/Flipbook/ProximaNova-Light.woff2') format('woff2'), url('/fonts/Flipbook/ProximaNova-Light.woff') format('woff'), url('/fonts/Flipbook/ProximaNova-Light.ttf') format('truetype'), url('/fonts/Flipbook/ProximaNova-Light.svg#ProximaNova-Light') format('svg'), url('/fonts/Flipbook/ProximaNova-Light.eot?#iefix') format('embedded-opentype'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'ProximaNova-Regular'; src: url('/fonts/Flipbook/ProximaNova-Regular.eot'); src: url('/fonts/Flipbook/ProximaNova-Regular.woff2') format('woff2'), url('/fonts/Flipbook/ProximaNova-Regular.woff') format('woff'), url('/fonts/Flipbook/ProximaNova-Regular.ttf') format('truetype'), url('/fonts/Flipbook/ProximaNova-Regular.svg#ProximaNova-Regular') format('svg'),
       url('/fonts/Flipbook/ProximaNova-Regular.eot?#iefix') format('embedded-opentype'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'ProximaNova-Semibold'; src: url('/fonts/Flipbook/ProximaNova-Semibold.eot'); src: url('/fonts/Flipbook/ProximaNova-Semibold.woff2') format('woff2'), url('/fonts/Flipbook/ProximaNova-Semibold.woff') format('woff'), url('/fonts/Flipbook/ProximaNova-Semibold.ttf') format('truetype'), url('/fonts/Flipbook/ProximaNova-Semibold.svg#ProximaNova-Semibold') format(' svg'), url('/fonts/Flipbook/ProximaNova-Semibold.eot?#iefix') format('embedded-opentype'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'Sylfaen'; src: url('/fonts/Flipbook/Sylfaen.woff') format('woff'), url('/fonts/Flipbook/Sylfaen.ttf') format('truetype'), url('/fonts/Flipbook/Sylfaen.svg#Sylfaen') format('svg'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'sylfaen'; src: url('/fonts/Flipbook/sylfaen.eot'); src: url('/fonts/Flipbook/sylfaen.woff2') format('woff2'), url('/fonts/Flipbook/sylfaen.eot?#iefix') format('embedded-opentype'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'Optima'; src: url('/fonts/Flipbook/Optima.woff') format('woff'), url('/fonts/Flipbook/Optima.ttf') format('truetype'), url('/fonts/Flipbook/Optima.svg#Optima') format('svg'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'unicode.optima'; src: url('/fonts/Flipbook/unicode.optima.eot'); src: url('/fonts/Flipbook/unicode.optima.woff2') format('woff2'), url('/fonts/Flipbook/unicode.optima.eot?#iefix') format('embedded-opentype'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'ACaslonPro-Bold'; src: url('/fonts/Flipbook/ACaslonPro-Bold.eot'); src: url('/fonts/Flipbook/ACaslonPro-Bold.woff2') format('woff2'), url('/fonts/Flipbook/ACaslonPro-Bold.woff') format('woff'), url('/fonts/Flipbook/ACaslonPro-Bold.ttf') format('truetype'), url('/fonts/Flipbook/ACaslonPro-Bold.svg#ACaslonPro-Bold') format('svg'), url('/fonts/Flipbook/ACaslonPro-Bold.eot?#iefix') format('/fonts/Flipbook/embedded-opentype'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'ACaslonPro-Regular'; src: url('/fonts/Flipbook/ACaslonPro-Regular.eot'); src: url('/fonts/Flipbook/ACaslonPro-Regular.woff2') format('woff2'), url('/fonts/Flipbook/ACaslonPro-Regular.woff') format('woff'), url('/fonts/Flipbook/ACaslonPro-Regular.ttf') format('truetype'), url('/fonts/Flipbook/ACaslonPro-Regular.svg#ACaslonPro-Regular') format('svg'), 
       url('/fonts/Flipbook/ACaslonPro-Regular.eot?#iefix') format('embedded-opentype'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'GillSansCE-Roman'; src: url('/fonts/Flipbook/GillSansCE-Roman.eot'); src: url('/fonts/Flipbook/GillSansCE-Roman.woff2') format('woff2'), url('/fonts/Flipbook/GillSansCE-Roman.woff') format('woff'), url('/fonts/Flipbook/GillSansCE-Roman.ttf') format('truetype'), url('/fonts/Flipbook/GillSansCE-Roman.svg#GillSansCE-Roman') format('svg'), url('/fonts/Flipbook/GillSansCE-Roman.eot?#iefix') format('embedded-opentype'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'GillSans-Light'; src: url('/fonts/Flipbook/GillSans-Light.eot'); src: url('/fonts/Flipbook/GillSans-Light.woff2') format('woff2'), url('/fonts/Flipbook/GillSans-Light.woff') format('woff'), url('/fonts/Flipbook/GillSans-Light.ttf') format('truetype'), url('/fonts/Flipbook/GillSans-Light.svg#GillSans-Light') format('svg'), url('/fonts/Flipbook/GillSans-Light.eot?#iefix') format('/fonts/Flipbook/embedded-opentype'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'MyriadPro-Regular'; src: url('/fonts/Flipbook/MyriadPro-Regular.eot'); src: url('/fonts/Flipbook/MyriadPro-Regular.woff2') format('woff2'), url('/fonts/Flipbook/MyriadPro-Regular.woff') format('woff'), url('/fonts/Flipbook/MyriadPro-Regular.ttf') format('truetype'), url('/fonts/Flipbook/MyriadPro-Regular.svg#MyriadPro-Regular') format('svg'), url('/fonts/Flipbook/MyriadPro-Regular.eot?#iefix') format('embedded-opentype'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'Optima CE Roman'; src: url('/fonts/Flipbook/Optima CE Roman.eot'); src: url('/fonts/Flipbook/Optima CE Roman.woff2') format('woff2'), url('/fonts/Flipbook/Optima CE Roman.eot?#iefix') format('embedded-opentype'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'OptimaCE-Roman'; src: url('/fonts/Flipbook/OptimaCE-Roman.woff') format('woff'), url('/fonts/Flipbook/OptimaCE-Roman.ttf') format('truetype'), url('/fonts/Flipbook/OptimaCE-Roman.svg#OptimaCE-Roman') format('svg'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'OptimaNovaLT-Black'; src: url('/fonts/Flipbook/OptimaNovaLT-Black.eot'); src: url('/fonts/Flipbook/OptimaNovaLT-Black.woff2') format('woff2'), url('/fonts/Flipbook/OptimaNovaLT-Black.woff') format('woff'), url('/fonts/Flipbook/OptimaNovaLT-Black.ttf') format('truetype'), url('/fonts/Flipbook/OptimaNovaLT-Black.svg#OptimaNovaLT-Black') format('svg'),
       url('/fonts/Flipbook/OptimaNovaLT-Black.eot?#iefix') format('embedded-opentype'); font-weight: normal; font-style: normal;}
@font-face {font-family: '/fonts/Flipbook/PalatinoLinotype-Italic'; src: url('/fonts/Flipbook/PalatinoLinotype-Italic.woff') format('woff'), url('/fonts/Flipbook/PalatinoLinotype-Italic.ttf') format('truetype'),
       url('/fonts/Flipbook/PalatinoLinotype-Italic.svg#PalatinoLinotype-Italic') format('svg'); font-weight: normal; font-style: normal;}
@font-face {font-family: 'palai'; src: url('/fonts/Flipbook/palai.eot'); src: url('/fonts/Flipbook/palai.woff2') format('woff2'), url('/fonts/Flipbook/palai.eot?#iefix') format('embedded-opentype'); font-weight: normal; font-style: normal;}
@font-face{font-family:'GillSansMT-Bold';src:url('/fonts/Flipbook/GillSansMT-Bold.eot');src:url('/fonts/Flipbook/GillSansMT-Bold.eot?#iefix') format('embedded-opentype'), url('/fonts/Flipbook/GillSansMT-Bold.woff') format('woff'), url('/fonts/Flipbook/GillSansMT-Bold.ttf') format('truetype');font-weight:bold;font-style:normal;}


@font-face{font-family:'Gill Sans MT';src:url('/fonts/Flipbook/GillSansMT.eot');src:url('/fonts/Flipbook/GillSansMT.eot?#iefix') format('embedded-opentype'), url('/fonts/Flipbook/GillSansMT.woff') format('woff'), url('/fonts/Flipbook/GillSansMT.ttf') format('truetype');font-weight:normal;font-style:normal;}
@font-face {font-family: 'PoorRichard-Regular'; src: url('/fonts/Flipbook/PoorRichard-Regular.eot?#iefix') format('embedded-opentype'),  url('/fonts/Flipbook/PoorRichard-Regular.woff') format('woff'), url('/fonts/Flipbook/PoorRichard-Regular.ttf')  format('truetype'), url('/fonts/Flipbook/PoorRichard-Regular.svg#PoorRichard-Regular') format('svg'); font-weight: normal; font-style: normal;}
@font-face{font-family:'Avenir-Medium';src:url('/fonts/Avenir 65 Medium.eot');src:url('/fonts/Avenir 65 Medium.eot?#iefix') format('embedded-opentype'), url('/fonts/Avenir 65 Medium.woff') format('woff'), url('/fonts/Avenir 65 Medium.ttf') format('truetype'), url('/fonts/Avenir 65 Medium.svg#PoorRichard-Regular') format('svg');font-weight:bold;font-style:normal;} /*ST-1603*/

@font-face {font-family: "PalatinoLTStd";src: url("/fonts/editorfonts/Palatino LT Std Light.eot");src: url("/fonts/editorfonts/Palatino LT Std Light.eot?#iefix") format("embedded-opentype"), url("/fonts/editorfonts/Palatino LT Std Light.woff2") format("woff2"), url("/fonts/editorfonts/Palatino LT Std Light.woff") format("woff"), url("/fonts/editorfonts/Palatino LT Std Light.ttf") format("truetype"), url("/fonts/editorfonts/Palatino LT Std Light.svg#Palatino LT Std Light") format("svg");}
@font-face {font-family: "PalatinoLTStd-Light";src: url("/fonts/editorfonts/Palatino LT Std Light.eot");src: url("/fonts/editorfonts/Palatino LT Std Light.eot?#iefix") format("embedded-opentype"), url("/fonts/editorfonts/Palatino LT Std Light.woff2") format("woff2"), url("/fonts/editorfonts/Palatino LT Std Light.woff") format("woff"), url("/fonts/editorfonts/Palatino LT Std Light.ttf") format("truetype"), url("/fonts/editorfonts/Palatino LT Std Light.svg#Palatino LT Std Light") format("svg");}
@font-face { font-family: 'Rollerscript-Smooth'; src: url('/fonts/Rollerscript-Smooth.eot'); src: url('/fonts/Rollerscript-Smooth.woff2') format('woff2'), url('/fonts/Rollerscript-Smooth.woff') format('woff'), url('/fonts/Rollerscript-Smooth.ttf') format('truetype'), url('/fonts/Rollerscript-Smooth.svg#Rollerscript-Smooth') format('svg'), url('/fonts/Rollerscript-Smooth.eot?#iefix') format('embedded-opentype'); font-weight: normal; font-style: normal; }



















*, *:after, *:before { -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; }
body, html { font-size: 100%; padding: 0; margin: 0;/* height: 100%;*/}
.clearfix:before, .clearfix:after { content: " "; display: table; }
.clearfix:after { clear: both; }
button, img{outline: none;}
body {font-family:'Gill Sans MT'; /*overflow: hidden;*/}
a {color: #555; text-decoration: none; outline: none;}
a:hover, a:active {color: #777;}
a img {border: none;} 
/*.container {height: 100%;}*/

.bb-custom-wrapper {width: 1200px; /*height: 579px;*/ height: 576px; position: relative; margin: 25px auto 0; outline: 1px solid #808080; -webkit-margin-before: 35px;} /*ST-1556,ST-1671*/
.bb-custom-wrapper .bb-bookblock {width: 100%; height: 100%; -webkit-perspective: 2000px; perspective: 2000px; -moz-perspective: 2000px; -ms-perspective: 2000px; -o-perspective: 2000px;}
/* PK : 12/1/2017 Update Width to 100% For IE Bug Resolve */
.bb-custom-side {width: 575px; float: left; height: 100%; overflow: hidden; background-color: #fff;  /*-webkit-border-image: url("/images/stitches.png") 30 round; -o-border-image: url("/images/stitches.png") 30 round; border-image: url("/images/stitches.png") 30 round;*/ border-width: 0px 1px 0px 0px;}
.bb-item .custom_side_wrapper{width: 100%; height: 100%; position: relative;}
.bb-item .custom_side_wrapper .stitches_wrapper{position: absolute; top: 0; left: 49.7%; bottom: 0; height: 100%;}/*RP ST-1731*/
/*.bb-item .custom_side_wrapper .stitches{background: url("/images/stitches.png") top center repeat-y; height: 100%; width: 10px; margin: 0 auto 10px;}*/
.bb-item .custom_side_wrapper .stitches_black{background: url("/images/stitches_black.png") top center repeat-y; width: 1px;}
.bb-custom-firstpage{text-align: center; width: 50%; float: left; height: 100%; position: relative; border-right: 1px solid #000000; background-color: #fff;}
/*.bb-custom-side p {padding: 8%; font-size: 1.8em; font-weight: 300;}*/
.bb-custom-wrapper h3 {font-size: 1.4em; font-weight: 300; margin: 0.4em 0 1em;}

.bb-custom-wrapper nav a#bb-nav-next{right: -45px; background-image : url(/images/right.png);}
.bb-custom-wrapper nav a#bb-nav-prev{left: -45px; background-image : url(/images/left.png);}
.bb-custom-wrapper nav a#bb-nav-next, .bb-custom-wrapper nav a#bb-nav-prev{-webkit-background-size: 44px 88px; -moz-background-size: 44px 88px; background-size: 44px 88px; background-position: 50% 50%; background-repeat: no-repeat; -webkit-transition: -webkit-transform 300ms; -moz-transition: -moz-transform 300ms; -o-transition: -o-transform 300ms; -ms-transition: -ms-transform 300ms; transition: transform 300ms; z-index: 1; position:absolute; width:44px; height:100%; top: 0;}
.bb-custom-wrapper nav a#bb-nav-next:active, .bb-custom-wrapper nav a#bb-nav-prev:active{background-color: rgba(0, 0, 0, 0.05);}
.bb-bookblock {width: 400px; height: 300px; margin: 0 auto; position: relative; z-index: 100; -webkit-perspective: 1300px; perspective: 1300px; -moz-perspective: 1300px; -ms-perspective: 1300px; -o-perspective: 1300px; -webkit-backface-visibility: hidden; backface-visibility: hidden; -ms-backface-visibility: hidden; -moz-backface-visibility: hidden; -o-backface-visibility: hidden; transform-style: preserve-3d; -webkit-transform-style: preserve-3d; -moz-transform-style: preserve-3d; -ms-transform-style: preserve-3d;}
.bb-page {position: absolute; -webkit-transform-style: preserve-3d; /*transform-style: preserve-3d;*/ -moz-transform-style: preserve-3d; /*-ms-transform-style: preserve-3d;*/ -o-transform-style: preserve-3d; -webkit-transition-property: -webkit-transform; transition-property: transform; -moz-transition-property: transform; -ms-transition-property: transform; -o-transition-property: transform;}
.bb-vertical .bb-page {width: 50%; height: 100%; left: 50%; -webkit-transform-origin: left center; transform-origin: left center; -moz-transform-origin: left center; -ms-transform-origin: left center; -o-transform-origin: left center;}
.bb-horizontal .bb-page {width: 100%; height: 50%; top: 50%; -webkit-transform-origin: center top; transform-origin: center top; -moz-transform-origin: center top; -ms-transform-origin: center top; -o-transform-origin: center top;}
.bb-page > div, .bb-outer, .bb-content, .bb-inner {position: absolute; height: 100%; width: 100%; top: 0; left: 0; -webkit-backface-visibility: hidden; /*backface-visibility: hidden;*/ -moz-backface-visibility: hidden; /*-ms-backface-visibility: hidden;*/ -o-backface-visibility: hidden;}
.bb-vertical .bb-content {width: 200%;}
.bb-horizontal .bb-content {height: 200%;}
.bb-page > div {width: 100%; -webkit-transform-style: preserve-3d; /*transform-style: preserve-3d;*/ -moz-transform-style: preserve-3d; /*-ms-transform-style: preserve-3d;*/ -o-transform-style: preserve-3d;}
.bb-vertical .bb-back {-webkit-transform: rotateY(-180deg); transform: rotateY(-180deg); -moz-transform: rotateY(-180deg); -ms-transform: rotateY(-180deg); -o-transform: rotateY(-180deg); }
.bb-horizontal .bb-back {-webkit-transform: rotateX(-180deg); transform: rotateX(-180deg); -moz-transform: rotateX(-180deg); -ms-transform: rotateX(-180deg); -o-transform: rotateX(-180deg);}
.bb-outer {width: 100%; overflow: hidden; z-index: 999;}
.bb-overlay, .bb-flipoverlay {background-color: rgba(0, 0, 0, 0.7); position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; opacity: 0;}
.bb-flipoverlay {background-color: rgba(0, 0, 0, 0.2);}
.bb-bookblock.bb-vertical > div.bb-page:first-child, .bb-bookblock.bb-vertical > div.bb-page:first-child .bb-back {-webkit-transform: rotateY(180deg); transform: rotateY(180deg); -moz-transform: rotateY(180deg); -ms-transform: rotateY(180deg); -o-transform: rotateY(180deg);}
.bb-bookblock.bb-horizontal > div.bb-page:first-child, .bb-bookblock.bb-horizontal > div.bb-page:first-child .bb-back {-webkit-transform: rotateX(180deg); transform: rotateX(180deg); -moz-transform: rotateX(180deg); -ms-transform: rotateX(180deg); -o-transform: rotateX(180deg);}
/* Content display */
.bb-content {background: #fff;}
.bb-vertical .bb-front .bb-content {left: -100%;}
.bb-horizontal .bb-front .bb-content {top: -100%;}
/* Flipping classes */
.bg_end_paper{background-size: 100% 100%; height: 576px; width: 100%; padding:24px;}/*ST-1398 PK 10/24/2018, Case1-step1 , ST-1398.1 PK 11/13/2018 */
.bb-vertical .bb-flip-next, .bb-vertical .bb-flip-initial {-webkit-transform: rotateY(-180deg); transform: rotateY(-180deg); -ms-transform: rotateY(-180deg); -moz-transform: rotateY(-180deg); -o-transform: rotateY(-180deg);}
.bb-vertical .bb-flip-prev {-webkit-transform: rotateY(0deg); transform: rotateY(0deg); -ms-transform: rotateY(0deg); -moz-transform: rotateY(0deg); -o-transform: rotateY(0deg);}
.bb-horizontal .bb-flip-next, .bb-horizontal .bb-flip-initial {-webkit-transform: rotateX(180deg); transform: rotateX(180deg); -moz-transform: rotateX(180deg); -ms-transform: rotateX(180deg); -o-transform: rotateX(180deg);}
.bb-horizontal .bb-flip-prev {-webkit-transform: rotateX(0deg); transform: rotateX(0deg); -moz-transform: rotateX(0deg); -ms-transform: rotateX(0deg); -o-transform: rotateX(0deg);}
.bb-vertical .bb-flip-next-end {-webkit-transform: rotateY(-15deg); transform: rotateY(-15deg); -moz-transform: rotateY(-15deg); -ms-transform: rotateY(-15deg); -o-transform: rotateY(-15deg);}
.bb-vertical .bb-flip-prev-end {-webkit-transform: rotateY(-165deg); transform: rotateY(-165deg); -moz-transform: rotateY(-165deg); -ms-transform: rotateY(-165deg); -o-transform: rotateY(-165deg);}
.bb-horizontal .bb-flip-next-end {-webkit-transform: rotateX(15deg); transform: rotateX(15deg); -moz-transform: rotateX(15deg); -ms-transform: rotateX(15deg); -o-transform: rotateX(15deg);}
.bb-horizontal .bb-flip-prev-end {-webkit-transform: rotateX(165deg); transform: rotateX(165deg); -moz-transform: rotateX(165deg); -ms-transform: rotateX(165deg); -o-transform: rotateX(165deg);}

.no-js .bb-bookblock, .no-js ul.bb-custom-grid li {width: auto; height: auto;}
.no-js .bb-item {display: block; position: relative;}
.no-js .bb-custom-wrapper {height: auto;}
.no-js .bb-custom-content {height: 470px;}
/*.bb-custom-firstpage .fred_nicolaus{position: absolute; top: 50%; left: 50%; 
                                       /*transform: translate(-50%, -50%); -webkit-transform: translate(-50%, -50%);
                                       -ms-transform: translate(-50%, -50%); -moz-transform: translate(-50%, -50%); -o-transform: translate(-50%, -50%); border: 1px solid #000000; padding: 6px; min-width: 250px;max-width:400px;overflow:hidden;width:auto;}*/
.bb-front {-webkit-backface-visibility: hidden; -moz-backface-visibility: hidden; -o-backface-visibility: hidden; -ms-backface-visibility: hidden; backface-visibility: hidden;}  /*KG, 4/12/17, #472*/
.bb-custom-firstpage .fred_nicolaus h1{font-size: 24px; color: rgba(35, 31, 32, 0.7); margin: 0; font-weight: normal;}
.bb-custom-firstpage .fred_nicolaus .fred_number{padding: 0 15px 0 0;}
.bb-custom-firstpage .fred_nicolaus span{font-size: 13px; color: #231F20; display: inline-block; width: 49%;}
.bb-custom-firstpage .fred_nicolaus a{font-size: 10px; text-decoration: none; color: #231F20; display: inline-block; margin: 6px 0 0; font-weight: normal;}
.bb-custom-firstpage .fred_nicolaus a:hover{text-decoration: none;}
.bb-custom-firstpage .flip_bottom_book{position: absolute; left: 0; right: 0; bottom: 25px;}
.bb-custom-firstpage .flip_bottom_book img{width: 180px;}
.flip_book_logo{width: 100%; height: 100%; position: relative;}
.flip_book_logo .flip_logo{position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); -webkit-transform: translate(-50%, -50%); -ms-transform: translate(-50%, -50%); -moz-transform: translate(-50%, -50%); width: 100%; text-align: center;}
.flip_book_logo .flip_logo span{display: inline-block; vertical-align: middle;}
.flip_book_logo .flip_logo span.logo_text{position: relative;}
.flip_book_logo .flip_logo span.logo_text b{font-weight: normal; position: absolute; bottom: 50px; right: 25%; font-size: 14px; color: #000; font-family: 'Optima';}
.fred_img{position: relative;} /*ST-1582*/
.fred_img img{width: 100%; border: 1px solid #000000; height: 100%;}

.fred_text h2{color: #000000; margin: 0; padding: 0; font-size: 40px; font-weight: normal;}
.fred_text h2 b{font-family: 'OptimaNovaLT-Black'; font-size: 39px; letter-spacing: 3px;}
.fred_text span.line{height: 1px; width: 100%; background-color: #000000; margin: 3px 0; display: block;}
/*ST-1556 top-aligned, drop down 3 px*/
.frederich_img{padding: 26px 30px 25px 30px; position: relative; border-top: 1px solid #000; border-bottom: 1px solid #000; border-left: 1px solid #000;} /* PK ST-1398 11/13/2018 Update Padding */
.frederich_img1{padding: 26px 30px 25px 30px; border-right: 1px solid #000; border-left: 0 none;}/* PK ST-1398 11/13/2018 Update Padding */
/*.frederich_img img{float: right; width: 150px;}*/
.frederich_img img{height:100%; width: 100%;}
.cb{clear: both;}
.frederich_img h2{margin: 0 0 20px; font-size: 24px; color: #231f20; font-family:'GillSansMT-Bold';}
/*.frederich_img h3{margin: 0 0 5px; font-size: 22px; color: #231f20; padding: 0 0 2px; border-bottom: 1px solid #000000; width: 280px; font-weight: 500; font-family: 'GillSansCE-Roman';}*/
/*.frederich_img p{color: #231f20; margin: 0 0 15px; font-size: 12px; font-family: 'ACaslonPro-Regular'; line-height: 15px;}*/
.frederich_img .london_archi{margin: 20px 0 0;}
.frederich_img .junior_designer{width: 300px;}


.frederich_img ul{margin: 10px 0 0; padding: 0 0 0 65px;}
.frederich_img ul li{margin: 0; padding: 0; list-style-type: disc; color: #231f20; font-size: 12px; font-family: 'ACaslonPro-Regular'; line-height: 15px;}
.frederich_img .bottom_text{position: absolute; bottom: 44px; left: 0; right: 0; text-align: center;}
.frederich_img .bottom_text a{text-decoration: none; font-size: 12px; color: #A7A9AB; font-family: 'ProximaNova-Regular';}
.frederich_img .bottom_text a:hover{text-decoration: none;}
.shingle{padding: 30px 20px 25px;}
.shingle .shingle_style_beach{width: 100%; height: 475px; padding: 0 30px 0 50px;}
.shingle .shingle_style_beach img{width: 100%; height: 100%;}
.shingle .shingle_style_beach .shingle_line{width: 100%; height: 1px; background-color: #000000; margin: 14px 0 8px;}
.shingle .shingle_style_beach h4{margin: 0; color: #231f20; font-size: 18px; font-weight: bold; text-align: center; font-family: 'ProximaNova-Bold';}
.shingle .shingle_style_beach p{/*color: #231f20;*/ margin: 0; font-size: 12px; text-align: center; font-weight: normal; font-family:'Gill Sans MT';} /*BV,2018 12 31,ST-1544,ST-1571*/



.midcentury_left{width: 738px}
.midcentury_left img{width: 100%;}
.midcentury_right{width: 414px;padding: 42px 27px 27px 45px;}
.bb_full_img{width: 100%; overflow: hidden; position: relative; z-index: 100;}
.bb_full_img img{width: 100%;}
.keller_environment{padding: 10px 20px 5px 20px; height: auto; overflow: visible;}
.keller_environment .keller_text{width: 55%; float: left; padding-right: 35px;}
.keller_environment .keller_text h4{color: #000000; padding: 0 0 10px; margin: 10px 0 10px; font-size: 20px; font-weight: bold; border-bottom: 1px solid #000000;     font-family: 'ProximaNova-Bold';}
.keller_environment .keller_text span{display: block; color: #000000; font-size: 10px; margin: 0 0 20px; font-family:'Gill Sans MT';}
.keller_environment .keller_text p{color: #000000; margin: 0; font-size: 12px; line-height: 17px; font-family: 'PoorRichard-Regular';}
.keller_environment .keller_img{width: 44%; float: left;}
.keller_environment .keller_img img{width: 100%;height:100%}
.keller_environment .keller_img span{display: block; color: #000000; font-size: 10px; margin: 6px 0 0; font-family:'Gill Sans MT';}
.keller_environment .keller_img1{width: 48%; float: left; padding-right: 20px; position: relative;}
.keller_environment .keller_img1 img{width: 100%;height:100%}
.keller_environment .keller_img2{width: 50%; float: left; position: relative;}
.keller_environment .keller_img2 img{width: 100%;height:100%}
/*.keller_environment .keller_img1 span, .keller_environment .keller_img2 span{display: block; color: #000000; font-size: 10px; position: absolute; left: 0; margin: 6px 0 0; font-family:'Gill Sans MT';}*/
.salesforce_tower{width: 572px; height: 576px}
.salesforce_tower img{width: 100%;height: 100%;}
/*.drake{padding: 20px 20px 20px 20px;}/* ST-1441 PK , 17/10/2018 , BV,ST-1582 */
.drake_tower{width: 100%; height: 100%; padding: 16px 19px 16px 16px;}/* ST-1441 PK , 17/10/2018 ,ST-1582,BV,2019 02 20*/
.drake_tower h3{color: #422164; padding: 0 0 15px; width: 100%; border-bottom: 1px solid #422164; font-size: 36px; margin: 5px 0 10px; font-weight: 500; font-family: 'Optima'; font-weight: normal;}

.content_text p {font-family: 'PalatinoLTStd-Light';line-height:16px ;}
/*ST-1441 PK 10/19/2018 */
.drake_tower .sub_heading .sub_heading p {font-family: 'MillerText-Roman' !important;font-size:12px;}
/*.drake_tower .sub_heading p {font-family: 'MillerText-Roman' !important;font-size: 12px;},BV,ST-1571*/
.drake_tow_text img{border: 1px solid #000; float: right; margin-left: 20px;}

.historic_quincy_img{width: 64%;}
.historic_quincy_img img{width: 100%; height: 100%;}
/*BV, ST-1443, 2019 01 07
    .historic_quincy_txt{width: 36%;  border-top: 1px solid #000; border-right: 1px solid #000;}
.historic_quincy_txt h3{width: 100%; color: #000; font-size: 26px; padding: 0 0 5px; border-bottom: 1px solid #000; margin: 0 0 15px; font-family: 'ACaslonPro-Bold'; line-height: 30px;}*/
/*.historic_quincy_txt p{color: #000; font-size: 12px; line-height: 15px; text-indent: 20px; font-family: 'PoorRichard-Regular';}
.historic_quincy_txt .historic-line{width: 70%; height: 2px; background-color: #000;}*/
.sketches_img, .sketches_img_right{padding: 20px; position: relative; height: 577px}
.sketches_img h3{color: #000; font-size: 34px; font-weight: bold; border-bottom: 1px solid #000; padding: 0; margin: 10px 0 20px; width: 150px; float: left; font-family: 'ACaslonPro-Bold'; line-height: 35px;}
.sketches_img .sketches_left{width: 40%; position: absolute; left: 20px; top: 75px;}
.sketches_img .sketches_left .sketches_up{width: 100%; height: 50%; margin: 0 0 20px;}
.sketches_img .sketches_left .sketches_up img{width: 85%;}
.sketches_img .sketches_left .sketches_down{width: 100%; height: 50%;}
.sketches_img .sketches_left .sketches_down img{width: 75%;}
.sketches_img .sketches_right{width: 60%; position: absolute; right: 20px; top: 20px;}
.sketches_img .sketches_right .sketches_up{width: 100%; height: 50%; margin: 0 0 20px; text-align: right;}
.sketches_img .sketches_right .sketches_up img{width: 85%;}
.sketches_img .sketches_right .sketches_down{width: 100%; height: 50%; text-align: right;}
.sketches_img .sketches_right .sketches_down img{width: 95%;}
.sketches_img_right .sketches_left{width: 55%; position: absolute; left: 0; top: 20px;}
.sketches_img_right .sketches_left .sketches_up{width: 100%; height: 100%; margin: 0;}
.sketches_img_right .sketches_left .sketches_up img{width: 95%;}
.sketches_img_right .sketches_right{width: 45%; position: absolute; right: 20px; top: 20px;}
.sketches_img_right .sketches_right .sketches_up{width: 100%; height: 50%; margin: 0 0 20px; text-align: right;}
.sketches_img_right .sketches_right .sketches_up img{width: 93%;}
.sketches_img_right .sketches_right .sketches_down{width: 100%; height: 50%; text-align: right;}
.sketches_img_right .sketches_right .sketches_down img{width: 100%;}

.last_slide_Left, .last_slide_right{position: relative; height: 577px;}
.zoom_wrapper{position: fixed; bottom: 5px; right: 100px; background-color: #000000; padding: 5px 7px; border-radius: 20px; z-index: 100}
#screen_full img{border-right: 1px solid #fff; padding-right: 5px;}
.zoom_wrapper a{color: #fff; text-decoration: none; vertical-align: middle; display: inline-block;}
select#zoom{background-color: transparent; border: none; color: #fff; -webkit-appearance: none; outline: none; pointer-events: none; display: inline-block; vertical-align: middle; -moz-appearance: none; -ms-appearance: none;}
select#zoom option{color: #000;}
select::-ms-expand{display: none; pointer-events: none;}

.ui-dialog-content .container{ width: 100%;}
.ui-dialog-content .main_heading h1{ margin-top: 0;}
.ui-widget-header{ background: none !important; border: none !important}
.ui-button .ui-icon{background-image:url("/images/btn-close.png") !important;}
.ui-icon-closethick{ background-position: left top !important}
.ui-dialog .ui-dialog-content{overflow: inherit !important; padding: 0 2em 0 0 !important;}

/* Start From Venkat image popup ST-1317*/
#PageZoomPopUp {z-index: 1001 !important}
.divddlZoomPopUp .control-div {width: 85%;margin: 0 auto}
.divddlZoomPopUp .control-div .flipbook-left-arrow {background: url(/images/flipbook-left-arrow.png) no-repeat;height: 45px;width: 17px}
.divddlZoomPopUp .control-div .flipbook-right-arrow {background: url(/images/flipbook-right-arrow.png) no-repeat;height: 45px;width: 17px}
/*.divddlZoomPopUp .control-div .zoomcontrol {background: #E8E8E8;padding: 8px 5px;margin: 10px auto 10px;width: 76px} ST-1310*/
.divddlZoomPopUp .control-div .zoomcontrol {background: #E8E8E8;padding: 8px 5px;margin: 0px auto 10px;width: 76px}
.divddlZoomPopUp .control-div .zoomcontrol a {display: inline-block}
.divddlZoomPopUp .control-div .zoomcontrol .zoomin {background: url(/images/flipbook-zoom-in.png) no-repeat;height: 23px;width: 24px;}
.divddlZoomPopUp .control-div .zoomcontrol .zoomout {background: url(/images/flipbook-zomm-out.png) no-repeat;height: 23px;width: 24px;}
.divddlZoomPopUp .control-div .selectoption {border: 1px solid #fff;color: #333;font-size: 14px;line-height: 25px;display: block;position: relative;background: #FFF url(/images/arrow-down.png) no-repeat right;height: 25px !important;-webkit-appearance: none;}
/* End From Venkat */

.ui-widget-overlay {
    background: #ffffff;
    opacity: 0.8;    
}
/*ST-1442 AC */
div#divInnerPrimaryUpload13 {
    max-width: 472px;
    height: 314px;
    margin: 0px 0 50px;
}

.shingle .shingle_style_beach .bottom_text_wrap .bottom_text p {
    font-family: MillerText-Roman ;
    font-size: 16px ;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #000000;
    padding: 6px 10px 3px;
    display: inline-block;
    border-bottom: 1px solid #000;
}
.shingle .shingle_style_beach .bottom_text{
	    text-align: center;
        width: 100%;
}
.shingle .shingle_style_beach .bottom_subtitle_text p{
	font-family: MillerText-Roman;
  font-size: 12px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  /*color: #000000; BV,2018 12 31,ST-1544,ST-1571*/
     
}

.bottom_text_wrap{
	    margin-bottom: 4px;
}

.cottage .georgica_pond{
height: 532px;
}
.cottage {
    padding: 22px 24px;
}




.editableBorderByToolBar .top_heading .heading_in {
    /*border-bottom: 1px solid #000;*/
    width: 100%;
	    padding: 5px 0;
}
/*ST-1582, BV, 2019 01 28*/
.editableBorderByToolBar .top_heading {width: 100%;}
.bottom_subtitle_text p {
    margin: 0;
    font-family: MillerText-Roman;
    font-size: 12px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: right;
    /*color: #000000; BV,2018 12 31,ST-1544,ST-1571*/
    padding: 7px 0;
}
div#div_subtitle15 {
    width: 100%;
}
.sub_heading .sub_heading_in p {
    font-family: MillerText-Roman;
    font-size: 19px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.16;
    letter-spacing: normal;
    text-align: center;
	    
    overflow: hidden;
    color: #000000;
}
.sub_heading .sub_heading_in {
    padding:0 10px;
}
/*BV, removed color: #979797 ST-1544-ST-1571, 2018 12 28*/
/*.cottage .georgica_pond .cottage_txt p {
    font-family: PalatinoLTSt;
    font-size: 12px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.33;
    letter-spacing: normal;
}*/

/*.sub_heading>div{
	display:table;
	width:100%;
}ST-1571, BV*/

.sub_heading>div .sub_heading_in{
	display:table-cell;
	width:100%;
	height: 68px;
	vertical-align:middle;
}
.bottom_subtitle_text .bottom_text {
    width: 100%;
}
.bottom_subtitle_text .bottom_text{
    margin-bottom: 0;
}


.cottage .georgica_pond .cottage_txt .right_content .right_content_in {
    height: 174px;
    vertical-align: middle;
    display: table-cell;
}

.cottage .georgica_pond ul{
	margin: 15px 0 0;
   
}
.cottage .georgica_pond ul li {
    width: 33.33%;
    margin-right: 0;
    display: block;
    float: left;
	padding-right: 10px;
}

.cottage .georgica_pond ul li:last-child {
    padding-right: 0;
}

.cottage .georgica_pond .cottage_img{
	    width: 33.33%;
}
.cottage .georgica_pond .cottage_txt {
    width: 66.33%;
}		



/*.flipbook_rightbox div p {
    margin:0px !important;
}*/

div#divInnerPrimaryUpload13 {
    width: 472px;
    height: 314px;
    margin: 0px 0 50px;
}

.flipbook_leftbox .shingle .shingle_style_beach .bottom_text_wrap .bottom_text p {
    font-family: MillerText-Roman ;
    font-size: 16px ;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #000000;
    padding: 6px 10px !important;
    display: inline-block;
    border-bottom: 1px solid #000;

}
.shingle .shingle_style_beach .bottom_text{
	    text-align: center;
}
.shingle .shingle_style_beach .bottom_subtitle_text p{
	font-family: MillerText-Roman;
  font-size: 12px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center !important;
  /*color: #000000;/*BV,2018 12 31,ST-1544,ST-1571*/
} 

.bottom_text_wrap{
	    margin-bottom: 4px;
}

.cottage .georgica_pond{
height: 532px;
}
.cottage {
    padding: 22px 24px;
}



.editableBorderByToolBar.flipbook_rightbox .top_heading .heading_in, .flipbook_rightbox .top_heading .heading_in {
    border-bottom: 1px solid #000;
    width: 100% !important;
    padding: 0 !important;
    line-height: normal !IMPORTANT;
    height: auto !important;
        margin-bottom: 0px;
        
}
.flipbook_rightbox  .cottage .georgica_pond {
    padding: 23px 29px;
}
.editableBorderByToolBar.flipbook_rightbox .top_heading {
    width: 100%;
    margin-bottom:0px !important;
}
/*.flipbook_rightbox .bottom_subtitle_text p {
    margin: 0 !important;
    font-family: MillerText-Roman !important;
    font-size: 12px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: right !important;
    color: #000000;
    margin:0px !important;
    padding: 0px 0;
}*/
div#div_subtitle15 {
    width: 100%;
}
.flipbook_rightbox .sub_heading .sub_heading_in p {
    font-family: MillerText-Roman;
    font-size: 19px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.16;
    letter-spacing: normal;
    text-align: center;
    overflow: hidden;   
    color: #000000;
}
.flipbook_rightbox .sub_heading .sub_heading_in {
    padding:0 10px;
}

 /*.cottage .flipbook_rightbox.georgica_pond .cottage_txt p {
    font-family: PalatinoLTStd !important;
    font-size: 12px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.33 !important;
    color: #979797;
    letter-spacing: normal;
}*/

.flipbook_rightbox  .sub_heading>div{
	display:table;
	width:100%;
}

.flipbook_rightbox  .sub_heading>div .sub_heading_in{
	display:table-cell;
	width:100%;
	height: 68px;
	vertical-align:middle;
}

.flipbook_rightbox  .bottom_subtitle_text .bottom_text{
    margin-bottom: 0;
    width: 100%;
    padding:0px !important;
}

.flipbook_rightbox  .cottage .georgica_pond .cottage_txt .right_content {
    display: table;
    width: 100%;
}
body .cottage .georgica_pond.flipbook_rightbox .cottage_txt .right_content .right_content_in {
      height: 174px !important;
    vertical-align: middle !important;
    display: table-cell !important;
    max-height: 174px;
    width: 100% !important;
    margin: 0px;

}
 /*.cottage .georgica_pond.flipbook_rightbox  ul{margin: 9px 0 0;} ------ */ 
 .cottage .georgica_pond.flipbook_rightbox  ul{margin: 12px 0 0;}

 .cottage .georgica_pond.flipbook_rightbox ul li {
    width: 150px;
    margin-right: 0;
    display: block;
    float: left;
        padding-right: 0;
	margin-right: 9px;
}

.cottage .georgica_pond.flipbook_rightbox  ul li:last-child {
    margin-right: 0;
}

.flipbook_rightbox .cottage .georgica_pond .cottage_img{
	    width: 33.33%;
}
.flipbook_rightbox .cottage .georgica_pond .cottage_txt {
    width: 66.33%;
}
body .cottage .georgica_pond.flipbook_rightbox .cottage_txt .right_content .right_content_in div {
    height: 174px;
}


body .cottage .georgica_pond.flipbook_rightbox .cottage_txt .right_content .right_content_in div div.ApplyHoverEffect p {
    display: table-cell;
    vertical-align: middle;
    font-family: PalatinoLTStd-Light !important;
    font-size: 12px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.33 !important;
    letter-spacing: normal;
    /*color: #979797 !important;*/
    word-break: break-all;
}

body .cottage .georgica_pond.flipbook_rightbox .cottage_txt .right_content .right_content_in div div.ApplyHoverEffect {
    display: table;
    width: 100%;
    vertical-align: middle;
    min-height: 174px;
}

.shingle.flipbook_lefttbox .bottom_subtitle_text .bottom_text {
    padding:0px;
}
.shingle.flipbook_lefttbox .shingle_style_beach .bottom_subtitle_text p {
 padding:0px;
     margin: 0 !important;

}
.shingle .shingle_style_beach .bottom_text {
    margin-bottom: 0;
}

.cottage .georgica_pond ul {
    margin: 11px 0 0;
}

.shingle.flipbook_lefttbox .shingle_style_beach {
    min-height: 414px !important;
    height:auto !important;
    padding: 0px 32px 0 30px;
}





.cottage{padding: 0px !important; }
    .cottage .georgica_pond {
        width: 100%;
        /*border: 2px solid #422164;*/
        /*padding: 20px 0 15px 15px;*/
        position: relative;
        z-index: 100;
    }
.cottage .georgica_pond .low_opacity_img{position: absolute; right: 15px; top: 15px; background: url("/images/low_opacity.png") top center no-repeat; width: 40%; height: 190px; background-size: 100%; z-index: 1; opacity: 0.19}
/*.cottage .georgica_pond h3{margin: 0 0 15px; padding: 0 0 5px; border-bottom: 1px solid #000000; font-size: 24px; color: #231f20; font-weight: 700; width: 355px; font-family:'GillSansMT-Bold';}
.cottage .georgica_pond h4{margin: 0 0 25px; font-size: 20px; color: #231f20; text-align: center; font-weight: 400; font-family: 'Sylfaen';}*/
.cottage .georgica_pond .cottage_img{ width: 30%; float: left;}
.cottage .georgica_pond .cottage_txt{ float: left; padding-left: 15px;} /*ST-1452 PK 02/04/2019 , Removed Width*/
.cottage .georgica_pond ul{margin: 20px 0 0; padding: 0;}
.cottage .georgica_pond ul li{list-style-type: none; width: 232px; margin-right: 10px; display: inline-block;vertical-align:top;}
.cottage .georgica_pond ul li:last-child{margin: 0;}

.bb-custom-side.flipbook_rightbox .heading_in .cke_editable {min-height:auto;}
.bb-custom-side.historic_quincy_txt.flipbook_rightbox {width: 35.9%;border:none;}
div.flipbook_rightbox .content_in p {font-family: PalatinoLTStd !important;font-size: 12px !important;font-weight: 300;font-style: normal;font-stretch: normal;line-height: 1.33 !important;letter-spacing: normal;} 
div.flipbook_rightbox .cell_div .content_wrap {padding: 12px;}
.flipbook_rightbox .sub_heading {width: 100%;box-sizing: border-box;}
.cottage .georgica_pond.flipbook_rightbox ul li img, .cottage .georgica_pond.flipbook_rightbox .cottage_img img {border:none ;width: 100%;}/*ST-1575*/
div#divInnerPrimaryUpload13 img{border:none;}

/*ST-1398 , For Radiate feature , PK, 10/28/2018 */
#inline189 {position: relative;width: 100%;border: 0;}
/*
#inline189:before,#inline189:after {position: absolute;bottom: -1px;content: '';}
#inline189:before {position: absolute;left: 50%;height: 2px;margin-top:10px; width: 0;background: #A574C1;border: 0;transition-duration: 0.2s;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);transform: translateX(-50%);z-index: 2;}
#inline189:after {position: absolute;left: 50%;height: 2px;margin-top:10px; width: 0;background: #A574C1;border: 0;transition-duration: 0.2s;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);transform: translateX(-50%);z-index: 2;}
#inline189:focus {outline: 0;}
#inline189:focus:after {width: 100%;}
*/
#inline189.mobile_cke_focus:after {width: 100%;}
#inline189.mobile_cke_focus {outline: 0;}
/*.PhoneNumberRow.ApplyHoverUnderLine2:after {position: absolute;left: 50%;height: 1px;margin-top:10px; width: 0;background: #A574C1;border: 0;transition-duration: 0.2s;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);transform: translateX(-50%);z-index: 2;}
.PhoneNumberRow.ApplyHoverUnderLine2.border_animation:after {width: 100%;}
.PhoneNumberRow.ApplyHoverUnderLine2:after {position: absolute;bottom: -1px;content: '';}*/
.dvPleaseTryAgain {font-family: 'GillSans-SemiBold';font-size: 20px;font-weight: 600;font-style: normal;font-stretch: normal;line-height: normal;letter-spacing: normal;text-align: center;color: var(--rouge);}

/*.cke_editable p{font-family:'Gill Sans'}, BV,- PLI has it's own style*/

/*ST-1398 Bug fix*/

div#inline189 p, div#inline192 p, div#inline190 p {
    font-family: GillSans !important;
}
div#div2:hover, div#div190:hover {
    border: none !important;
}
/*ST-1398 design bug fix 11/19/2018*/
div#inline2, div#inline189, div#inline190, div#inline192, div#businessManagerPopupClickDiv {
    cursor: pointer !important;
}

div#inline2 p, div#inline189 p, div#inline190 p, div#inline192 p, div#businessManagerPopupClickDiv {
    color: #a1a1a1 !important;
}
 
div#inline192.fullBorderForRW, div#inline2.fullBorderForRW, div#inline189.fullBorderForRW, div#inline190.fullBorderForRW {
    border: none !important
}
.customURLRow p {
    color: #a1a1a1 !important;
}
/*ST-1426*/ 
.cottage .georgica_pond.flipbook_rightbox ul .imgClassForBlankImg,
.cottage .georgica_pond.flipbook_rightbox .cottage_img .imgClassForBlankImg,
.drag-Img-slider .imgClassForBlankImg,
.imgClassForBlankImg{border:0px dashed rgba(0, 0, 255, 0.2) !important;}


/*ST-1526 40 tp 28 ,GillSans-Light , line-hel 35-23 to adj height*/
/*#inline5, #inline5 p {font-size: 28px !important;color: #000000 !important;font-family: Avenir-Book;font-weight: 300;line-height: 29px !important;text-align:center;}*/ 
/*#inline183, #inline183 p {font-size: 38px !important; color: #000000 !important;font-weight: bold;font-family: 'OptimaBold';line-height: 32px !important;text-align:center;}*/

/*ST-1663*/
.fred_text{position: absolute; top: 48%; background-color: rgba(255, 255, 255, 0.6); padding: 2px 0 2px 20px; left: 370px; }
.divClExtraWidth {min-width:130px; background-color:#FFFFFF;}/*ST-1663*/
/*PK 1308 09/17/2018 top 42 to 48% to adjust center*/
.fred_text_layout2 {/*min-width: 240px;*/z-index: 10 !important;padding-left: 0px !important;background-color: #FFFFFF;}
/*ST-1526 width was 350, make it 308 ,2018 12 15, change min-height */
/*ST-1663,BV, 1308*/
.divFCBorder {height: 6px;border-top: 1px solid #000;cursor: pointer;width: 103px;margin: auto;}
.DragImgForTextArea {cursor: url("/images/Cursor Cross Arrow.png"),auto !important;color: #000;height: 13px !important;width: 16px !important;position: absolute;bottom: 8px;border: none !important;cursor: pointer;opacity: 1 !important;display: none;z-index: 80;}/*ST-1526,ST-1506,BV-updated drag icon*/
/*ST-1474,ST-1526 updated*/

.imgClassForBlankImg{height:100%;width:100%}
.sublayout17 .keller_environment .RightSection {width: 235px;float: right;}
/*ST-1632, VR, 2019 04 05*/
.sublayout17 .keller_environment .LeftSection {width: 230px;float: left;} 
.sublayout17{ margin: 37px 47px 44px 47px !important;width: 482px !important;height: 495px;}

.cottage3PartLeft {margin-top: 25px;width: 160px;float: left;}/*ST-1656, BV 166 -160 -text boxes spilling over.*/
.cottage3PartRight {margin-top: 25px;width: 148px;float: left;}
.OverflowRightBottom {position: absolute;top: 307px;}
.clIsPlaceHolderParent {background-color:#ffffff;}
/*ST-1577,BV ,2019 01 21 , ST-1603*/

 /*ST-1595,BV,2019 02 06*/
.popover{border:1px solid #596da7;}
.popover.top>.arrow{border-top-color:#8693BC;border-top-color:rgba(134,148,189,1);}
.popover.right>.arrow{border-right-color:#596da7!important;border-right-color:rgba(134,148,189,1);left:-11px;}/*RP ST-1877*/
/*.popover.left>.arrow{border-left-color:#596da7!important;border-left-color:rgba(134,148,189,1)}*//*RP ST-1877*/
.popover-content{ color:#000000 !important; padding:3px 10px; font-family:'Avenir-Book'; font-size:12px; line-height: 12px; min-width:90px; text-align:center}
  
/*.popover.bottom>.arrow:after{top:1px!important;margin-left:-10px!important;content:" "!important;border-top-width:0!important;border-bottom-color:#fff !important}*/
/*.popover.bottom>.arrow{left:50% !important;width:17px; height:13px; border:0px !important;top: -10px}
.popover.bottom>.arrow:after{ display:none !important}*/
.popover.bottom > .arrow {left: 50% !important;width: 12px;height: 13px;background-size: cover;background-position: 0 -50px;-o-background-size: 100% 100%;margin-left: -13px;top: -16px;border-bottom-color: rgba(255,255,255,1);border-width: 9px;}

@media(max-width:823px) and (min-width: 720px ) {
    .popover.bottom > .arrow {
        top: -17px!important;
    }
}
@media(max-width:480px) and (min-width: 360px ) {
    .popover.right > .arrow {
        top: -16.5px!important;
    }
}
@media(max-width:1900px) and (min-width: 1440px ) {
    .popover.right > .arrow {
        left: -10px!important;
    }
}
@media(max-width:1152px) and (min-width: 960px ) {
    .popover.right > .arrow {
        left: -11px !important;
    }
}
@media(max-width:1800px) and (min-width: 1600px ) {
    .popover.left > .arrow {
        right: -11.5px !important;
    }
}
.popover.bottom>.arrow:after {
    display: block !important;background-image: url(/images/tooptip_arrow1.svg);width: 24px;height: 15px;border: 0px !important;-o-background-size: 100% 100%;-webkit-background-size: 100% 100%;margin-left: -12px !important;top: -1px!important;margin-top: -3px;
}
#myNavbar .popover > .arrow {
    width: 14.5px !important;
}
/*.popover.left > .arrow {width: 12px;height: 13px;background-size: cover;background-position: 0 -50px;-o-background-size: 100% 100%;margin-left: -13px;top: -16px;border-left-color: rgba(255,255,255,1);border-width: 9px;}*/
.popover.left>.arrow:after {display: block !important;background-image: url(/images/right-arrow.svg);width: 18px;height: 20px;border: 0px !important;-o-background-size: 100% 100%;-webkit-background-size: 100% 100%;margin-left: -10px !important;top: -3px!important;margin-top: -7px;right: -2px;}
.popover.left > .arrow {top: 50%;right: -11.5px;margin-top: -11px;border-right-width: 0;border-left-color: #999;border-left-color: white!important;}    /* ST 1602 */
.popover>.arrow { width:15.5px!important;}
.Title-Plate-Toolbar {height: 23px;font-family: GillSans;font-size: 20px;font-weight: 300;font-style: normal;font-stretch: normal;line-height: normal;letter-spacing: 0px;color: #3a3b39;height: 43.9px;background-color: #f2f2f2;padding-top: 6px;}
.Title-Plate-Toolbar .Color {height: 30px; margin-right:8px; font-family: GillSans;font-size: 13px;letter-spacing: -0.2px;color: #1a1919; float:left; line-height:30px;}
.color-picker {float:left; height: 30px; width:54px;}
.color-picker .toptextColorPicker {width:50px;}
.color-picker form {margin-top:0;}
.color-picker .dropdown {margin-top: -28px;}
.color-picker .dropdown .dropdown-menu {display:none;}
.color-picker .dropdown.open .dropdown-menu {display:block;}
.Fill-35 {width: 10.3px;height: 11.6px;background-color: #0f100e;}
.Fill-29 {width: 18.3px;height: 18.5px;/*background-color: #161615;*/}
.Title-Plate-Toolbar .opacityText {margin-left: 33px;position: absolute;line-height: 36px;height: 16px;font-family: GillSans;font-size: 14px;font-weight: normal;font-style: normal;font-stretch: normal;letter-spacing: normal;color: #1b1a19;}
.ddlSelect {/*margin-left: 20px;*/margin-left: 86px;width: 70.6px;height: 24.3px;margin-top: 5px;}
.Drag-drop-for-plac {width: 239px;font-family: GillSans;font-size: 16px;font-weight: 300;color:#787b75;}
.ckeOpacityClass .ddlSelect{appearance: none; -webkit-appearance: none;-moz-appearance: none;background: #fff url(/images/color_dropdown.jpg) no-repeat right; background-size: 24px 24px;border-radius: 0px 5px 5px 0px;height: 24px;line-height: 24px; padding-left:5px}
.Title-Plate-Toolbar .toptextcolorbox .toptextcolorbtn{height: 28px;}

.ckeOpacityClass .ddlSelect{appearance: none; -webkit-appearance: none;-moz-appearance: none;background: #fff url(/images/color_dropdown.jpg) no-repeat right; background-size: 24px 24px;border-radius: 0px 5px 5px 0px;height: 24px;line-height: 24px; padding-left:5px;width:75px !important}
.Title-Plate-Toolbar .color-picker{width:50px !important; height:26px!important;}
.Title-Plate-Toolbar .toptextcolorbox{ border:solid 1px #4d4e4c !important; margin-top:3px !important}
.Title-Plate-Toolbar .color-picker .toptextColorPicker{ width:48px !important; height:24px !important;}
.Title-Plate-Toolbar .color-picker .dropdown{margin-top: -25px !important;width: 50px;}
.Title-Plate-Toolbar .toptextcolorbox .dropdown-toggle{ height:22px !important; border-top:solid 0px #4d4e4c !important; border-left:solid 0px #4d4e4c !important;}
.Title-Plate-Toolbar .toptextcolorbox .toptextcolorbtn{background-size: 27px 26px !important;width: 26px;height: 25px !important; top:0px !important;border-radius: 0px 7px 7px 0px !important;border-right: 1px solid #58585b; margin-right: -1px;}
.Title-Plate-Toolbar .color-picker form{ margin-top:0px !important}

/*ST-1577,BV,2019 01 21,22 ,ST-1635 ,BV 2019 02 06*/
.divEraserForAll { position: static;float: right;top: 2px;margin-right:-4px;}
.divEraserForAll .imgEraserForAll {display: none;height: 17px !important;width: 15px !important;float: right;cursor:pointer;position:absolute;}

/*ST-1593,BV, 2019 01 29*/
.Click-on-text-image{height: 23px;font-size: 20px;color: #000000;margin:auto;font-family: 'GillSans-Light';display:block;margin-top:10px;}

/*ST-1634,*/ 
.drake_layout_border .cke-Rule4-Parent.fullBorderForRW{ border:0px !important}
.drake_layout_border .cke-Rule4-Parent:hover .cke-Rule4, .drake_layout_border .cke-Rule4-Parent:hover .cke-Rule4 .fullBorderForRW, .drake_layout_border .cke-Rule4-Parent .cke-Rule4 .fullBorderForRW {outline: 0px solid #c6deff !important;}
.drake_layout_border .cke-Rule4-Parent:hover .fullBorderForRW {border: 0px !important;outline: 0px solid #c6deff !important;}
.drake_layout_border .cke-Rule4-Parent:hover .left_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 3px solid #c6deff !important;border-right: 0px solid #c6deff !important;border-bottom: 3px solid #c6deff !important;border-top: 3px solid #c6deff !important;width: 248px;height: 433px;margin-left: -5px;}
.drake_layout_border .cke-Rule4-Parent:hover .left_content:before {content: "";position: absolute;width: 248px;height: 186px;border-right: 0px solid #c6deff !important;bottom: -5px!important;margin-left: -5px;}/*RP ST-1605*/
.drake_layout_border .cke-Rule4-Parent:hover .right_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 0px solid #c6deff !important;border-right: 3px solid #c6deff !important;border-bottom: 3px solid #c6deff !important;border-top: 3px solid #c6deff !important;width: 255px;height: 250px;margin-left: -10px;}
/*RP ST-1868 Start*/
/*Left Bleed*/
.drake_layout_border .cke-Rule4-Parent.fullBorderForRW .left_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 3px solid #5e94e0 !important;border-right: 0px solid #5e94e0 !important;border-bottom: 3px solid #5e94e0 !important;border-top: 3px solid #5e94e0 !important;width: 248px;height: 433px;margin-left: -5px;}
.drake_layout_border .cke-Rule4-Parent.fullBorderForRW .left_content:before {content: "";position: absolute;width: 248px;height: 186px;border-right: 0px solid #5e94e0 !important;bottom: -5px!important;margin-left: -5px;}/*RP ST-1605*/
.drake_layout_border .cke-Rule4-Parent.fullBorderForRW .right_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 0px solid #5e94e0 !important;border-right: 3px solid #5e94e0 !important;border-bottom: 3px solid #5e94e0 !important;border-top: 3px solid #5e94e0 !important;width: 255px;height: 250px;margin-left: -10px;}
.drake_layout_border .cke-Rule4-Parent .ApplyHoverEffect, .drake_layout_border .cke-Rule4-Parent .ApplyHoverEffect:hover, .cke-Rule4-Parent .ApplyBackGroundEffect, .drake_layout_border .cke-Rule4-Parent .ApplyBackGroundEffect:hover{outline:0; border:0; background:none !important}
/*Panoramica*/
.drake_layout_border.box-layout { position:relative;}
.drake_layout_border.box-layout .cke-Rule4-Parent:hover .left_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 3px solid #c6deff !important;border-right: 0px solid #c6deff !important;border-bottom: 3px solid #c6deff !important;border-top: 3px solid #c6deff !important;width: 250px;height: 140px;margin-left: -5px;}
.drake_layout_border.box-layout .cke-Rule4-Parent:hover .left_content:before {display:none!important;}
.drake_layout_border.box-layout .cke-Rule4-Parent:hover .right_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 0px solid #c6deff !important;border-right: 3px solid #c6deff !important;border-bottom: 3px solid #c6deff !important;border-top: 3px solid #c6deff !important;width: 255px;height: 140px;margin-left: -11px;}
.drake_layout_border.box-layout .cke-Rule4-Parent.fullBorderForRW .left_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #5e94e0;border-left: 3px solid #5e94e0 !important;border-right: 0px solid #5e94e0 !important;border-bottom: 3px solid #5e94e0 !important;border-top: 3px solid #5e94e0 !important;width: 250px;height: 140px;margin-left: -5px;}
.drake_layout_border.box-layout .cke-Rule4-Parent.fullBorderForRW .left_content:before {display:none!important;}
.drake_layout_border.box-layout .cke-Rule4-Parent.fullBorderForRW .right_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #5e94e0;border-left: 0px solid #5e94e0 !important;border-right: 3px solid #5e94e0 !important;border-bottom: 3px solid #5e94e0 !important;border-top: 3px solid #5e94e0 !important;width: 255px;height: 140px;margin-left: -11px;}

/*Single Tile*/
.drake_layout_border.sublayout16-box .cke-Rule4-Parent:hover .left_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 3px solid #c6deff !important;border-right: 0px solid #c6deff !important;border-bottom: 3px solid #c6deff !important;border-top: 3px solid #c6deff !important;width: 240px;height: 425px;margin-left: -5px;}
.drake_layout_border.sublayout16-box .cke-Rule4-Parent:hover .left_content:before {content: "";position: absolute;width: 241px;height: 250px;border-right: 3px solid #c6deff !important;bottom: 38px!important;margin-left: -5px;}
.drake_layout_border.sublayout16-box .cke-Rule4-Parent:hover .right_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 0px solid #c6deff !important;border-right: 3px solid #c6deff !important;border-bottom: 3px solid #c6deff !important;border-top: 3px solid #c6deff !important;width: 247px;height: 175px;margin-left: -14px;}
.drake_layout_border.sublayout16-box .cke-Rule4-Parent.fullBorderForRW .left_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #5e94e0;border-left: 3px solid #5e94e0 !important;border-right: 0px solid #5e94e0 !important;border-bottom: 3px solid #5e94e0 !important;border-top: 3px solid #5e94e0 !important;width: 240px;height: 425px;margin-left: -5px;}
.drake_layout_border.sublayout16-box .cke-Rule4-Parent.fullBorderForRW .left_content:before {content: "";position: absolute;width: 241px;height: 250px;border-right: 3px solid #5e94e0 !important;bottom: 38px!important;margin-left: -5px;}
.drake_layout_border.sublayout16-box .cke-Rule4-Parent.fullBorderForRW .right_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #5e94e0;border-left: 0px solid #5e94e0 !important;border-right: 3px solid #5e94e0 !important;border-bottom: 3px solid #5e94e0 !important;border-top: 3px solid #5e94e0 !important;width: 247px;height: 175px;margin-left: -14px;}

/*Border Singlet*/
.drake_layout_border.sublayout22-box .cke-Rule4-Parent:hover .left_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 3px solid #c6deff !important;border-right: 0px solid #c6deff !important;border-bottom: 3px solid #c6deff !important;border-top: 3px solid #c6deff !important;width: 232px;height: 407px;margin-left: -5px;}/*RP ST-1864*/
.drake_layout_border.sublayout22-box .cke-Rule4-Parent:hover .left_content:before {content: "";position: absolute;width: 232px;height: 185px;border-right: 3px solid #c6deff !important;bottom: -6px !important;margin-left: -5px;}/*RP ST-1864*/
.drake_layout_border.sublayout22-box .cke-Rule4-Parent:hover .right_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 0px solid #c6deff !important;border-right: 3px solid #c6deff !important;border-bottom: 3px solid #c6deff !important;border-top: 3px solid #c6deff !important;width: 246px;height: 225px;margin-left: -13px;}/*RP ST-1864*/
.drake_layout_border.sublayout22-box .cke-Rule4-Parent.fullBorderForRW  .left_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #5e94e0;border-left: 3px solid #5e94e0 !important;border-right: 0px solid #5e94e0 !important;border-bottom: 3px solid #5e94e0 !important;border-top: 3px solid #5e94e0 !important;width: 232px;height: 407px;margin-left: -5px;}/*RP ST-1864*/
.drake_layout_border.sublayout22-box .cke-Rule4-Parent.fullBorderForRW  .left_content:before {content: "";position: absolute;width: 232px;height: 185px;border-right: 3px solid #5e94e0 !important;bottom: -6px !important;margin-left: -5px;}/*RP ST-1864*/
.drake_layout_border.sublayout22-box .cke-Rule4-Parent.fullBorderForRW  .right_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #5e94e0;border-left: 0px solid #5e94e0 !important;border-right: 3px solid #5e94e0 !important;border-bottom: 3px solid #5e94e0 !important;border-top: 3px solid #5e94e0 !important;width: 246px;height: 225px;margin-left: -13px;}/*RP ST-1864*/

/*Text & 2 Big Tiles*/
.drake_layout_border.sublayout14-box .cke-Rule4-Parent:hover .left_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 3px solid #c6deff !important;border-right: 0px solid #c6deff !important;border-bottom: 3px solid #c6deff !important;border-top: 3px solid #c6deff !important;width: 250px;height: 130px;margin-left: -2px;}
.drake_layout_border.sublayout14-box .cke-Rule4-Parent:hover .left_content:before {display:none!important;}
.drake_layout_border.sublayout14-box .cke-Rule4-Parent:hover .right_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 0px solid #c6deff !important;border-right: 3px solid #c6deff !important;border-bottom: 3px solid #c6deff !important;border-top: 3px solid #c6deff !important;width: 243px;height: 130px;margin-left: -10px;}
.drake_layout_border.sublayout14-box .cke-Rule4-Parent.fullBorderForRW .left_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #5e94e0;border-left: 3px solid #5e94e0 !important;border-right: 0px solid #5e94e0 !important;border-bottom: 3px solid #5e94e0 !important;border-top: 3px solid #5e94e0 !important;width: 250px;height: 130px;margin-left: -2px;}
.drake_layout_border.sublayout14-box .cke-Rule4-Parent.fullBorderForRW .left_content:before {display:none!important;}
.drake_layout_border.sublayout14-box .cke-Rule4-Parent.fullBorderForRW .right_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #5e94e0;border-left: 0px solid #5e94e0 !important;border-right: 3px solid #5e94e0 !important;border-bottom: 3px solid #5e94e0 !important;border-top: 3px solid #5e94e0 !important;width: 243px;height: 130px;margin-left: -10px;}

/*Verbiage/Clear*/
.drake_layout_border.sublayout18-box .cke-Rule4-Parent:hover .left_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 3px solid #c6deff !important;border-right: 0px solid #c6deff !important;border-bottom: 3px solid #c6deff !important;border-top: 3px solid #c6deff !important;width: 250px;height: 415px;margin-left: -5px;}
.drake_layout_border.sublayout18-box .cke-Rule4-Parent:hover .left_content:before {display:none!important;}
.drake_layout_border.sublayout18-box .cke-Rule4-Parent:hover .right_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 0px solid #c6deff !important;border-right: 3px solid #c6deff !important;border-bottom: 3px solid #c6deff !important;border-top: 3px solid #c6deff !important;width: 255px;height: 415px;margin-left: -10px;}
.drake_layout_border.sublayout18-box .cke-Rule4-Parent.fullBorderForRW .left_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #5e94e0;border-left: 3px solid #5e94e0 !important;border-right: 0px solid #5e94e0 !important;border-bottom: 3px solid #5e94e0 !important;border-top: 3px solid #5e94e0 !important;width: 250px;height: 415px;margin-left: -5px;}
.drake_layout_border.sublayout18-box .cke-Rule4-Parent.fullBorderForRW .left_content:before {display:none!important;}
.drake_layout_border.sublayout18-box .cke-Rule4-Parent.fullBorderForRW .right_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #5e94e0;border-left: 0px solid #5e94e0 !important;border-right: 3px solid #5e94e0 !important;border-bottom: 3px solid #5e94e0 !important;border-top: 3px solid #5e94e0 !important;width: 255px;height: 415px;margin-left: -10px;}

/*Text & 3 Tiles*/
.drake_layout_border.sublayout12-box .cke-Rule4-Parent:hover .left_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 3px solid #c6deff !important;border-right: 0px solid #c6deff !important;border-bottom: 3px solid #c6deff !important;border-top: 3px solid #c6deff !important;width: 250px;height: 190px;margin-left: -5px;}
.drake_layout_border.sublayout12-box .cke-Rule4-Parent:hover .left_content:before {display:none!important;}
.drake_layout_border.sublayout12-box .cke-Rule4-Parent:hover .right_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 0px solid #c6deff !important;border-right: 3px solid #c6deff !important;border-bottom: 3px solid #c6deff !important;border-top: 3px solid #c6deff !important;width: 255px;height: 190px;margin-left: -10px;}
.drake_layout_border.sublayout12-box .cke-Rule4-Parent.fullBorderForRW .left_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #5e94e0;border-left: 3px solid #5e94e0 !important;border-right: 0px solid #5e94e0 !important;border-bottom: 3px solid #5e94e0 !important;border-top: 3px solid #5e94e0 !important;width: 250px;height: 190px;margin-left: -5px;}
.drake_layout_border.sublayout12-box .cke-Rule4-Parent.fullBorderForRW .left_content:before {display:none!important;}
.drake_layout_border.sublayout12-box .cke-Rule4-Parent.fullBorderForRW .right_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #5e94e0;border-left: 0px solid #5e94e0 !important;border-right: 3px solid #5e94e0 !important;border-bottom: 3px solid #5e94e0 !important;border-top: 3px solid #5e94e0 !important;width: 255px;height: 190px;margin-left: -10px;}

/*Text & 2 Tiles*/
.drake_layout_border.sublayout11-box .cke-Rule4-Parent:hover .left_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 3px solid #c6deff !important;border-right: 0px solid #c6deff !important;border-bottom: 3px solid #c6deff !important;border-top: 3px solid #c6deff !important;width: 250px;height: 353px;margin-left: -5px;}
.drake_layout_border.sublayout11-box .cke-Rule4-Parent:hover .left_content:before {display:none!important;}
.drake_layout_border.sublayout11-box .cke-Rule4-Parent:hover .right_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #c6deff;border-left: 0px solid #c6deff !important;border-right: 3px solid #c6deff !important;border-bottom: 3px solid #c6deff !important;border-top: 3px solid #c6deff !important;width: 162px;height: 353px;margin-left: -10px;}
.drake_layout_border.sublayout11-box .cke-Rule4-Parent.fullBorderForRW .left_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #5e94e0;border-left: 3px solid #5e94e0 !important;border-right: 0px solid #5e94e0 !important;border-bottom: 3px solid #5e94e0 !important;border-top: 3px solid #5e94e0 !important;width: 250px;height: 353px;margin-left: -5px;}
.drake_layout_border.sublayout11-box .cke-Rule4-Parent.fullBorderForRW .left_content:before {display:none!important;}
.drake_layout_border.sublayout11-box .cke-Rule4-Parent.fullBorderForRW .right_content .cke-Rule4:before {content: "";position: absolute;outline: 0px solid #5e94e0;border-left: 0px solid #5e94e0 !important;border-right: 3px solid #5e94e0 !important;border-bottom: 3px solid #5e94e0 !important;border-top: 3px solid #5e94e0 !important;width: 162px;height: 353px;margin-left: -10px;}
/*RP ST-1868 End*/
/*ST-1652*/
.divLayoutHeight {height:578px;
}
.stitches_wrapper{position: absolute; top: 0; left: 49.6%; bottom: 0; height: 100%;}
.stitches_wrapper .stitches{background: url("/images/stitches.png") top center repeat-y; height: 100%; width: 4px; margin: 0 auto 10px;}
.stitches_wrapper .stitches_dash{background: url("/images/stitches1.png") top center repeat-y; height: 100%; width: 4px; margin: 0 auto 10px;}
.stitches_wrapper .stitches_solid{border-right: 1px solid #CCC; height: 100%; width: 4px; margin: 0 auto 10px ;}
.stitches_wrapper .stitches_none{background: none;}


.divLayout1 .Click-on-text-image{width:576px;}
.divLayoutBC .Click-on-text-image{width:576px;}
.Layout-8-RightSec {padding-top: 96px;margin-left: 49px;width: 319px;}


/****651 Styles* PK ST-649 ***/
.flpbook_achievemnet { width: 100% !important; margin-top: 4px; border: solid 3px transparent; } /*ST-1817, removed !important form margin-top*/
.frederich_img1 .flpbook_achievemnet { margin-top: 16px; } /*ST-1817,BV, Removed !important;*/
.frederich_img1 .flpbook_achievemnet:first-child {margin-top: 0px;}/*ST-1817*/
.ip_left .divLayout2SecParent {padding-top: 2pt;}/*ST-1371 ,16px confirmed with MB,ST-1712*/ 
.ip_left .divLayout2SecParent.Ishidden {padding-top: 0pt;}

/*ST-1582, BV 2019 01 28 */

/*ST-1371, ST-1369.1 , ST-1510 */
.divLayout2SecParent {padding-right:15px; width:511px}/*For hove effect*/
.frederich_img1 .divLayout2SecParent {width:510px}
.divLayout2SecParent.divLayout2SecSmall {padding-top:12pt; width:323px} /*ST-1817,BV, Px to Pt*/
.divLayout2SecParent.divLayout2SecSmall .flpbook_achievemnet  {min-height:108px;}
.divLayout2SecParent.divLayout2SecSmall .divCkEdiMain .cke_editable {min-height:65px;}
.divLayout2SecParent.divLayout2SecSmall .Hamburger {margin-top: -35px;} 
.divLayout2SecParent.divLayout2SecSmall:hover .trashcan,
.divLayout2SecParent.divLayout2SecSmall:hover .Hamburger,
.divLayout2SecParent:hover .trashcan, .divLayout2SecParent:hover .Hamburger{display:block !important;}


/*ST-1371 ,BV 2018 14 12*/
.divLayout2 .divTopHeadingLeft {min-height: 32px;}
.divLayout2 .Hamburger,
.divLayout2 .trashcan {position:relative; float: right ;width: 13px ;height: 15px ;margin-right: -18px;}

.divLayout2 .Hamburger {top: -26px;}
.divLayout2 .trashcan {top: 8px;margin-top: -13px;}

/*ST-1510, BV, 2019 04 08*/
.divLayout2 .div10trashcan,
.divLayout2 .div10Hamburger {/*margin-right: -25px;*/}
.divLayout2 .divLayout2SecSmall .Hamburger {margin-right: 19px;top:-11pt;}/*-8px , ST-1817*/
.divLayout2 .divLayout2SecSmall .trashcan {top: -25pt;margin-right: 2px; margin-top:-17px;}/*-27px , ST-1817*/
  
.divLayout2 .eraserForAllDivimg , 
.eraserForAllDivimg {width: 16px;height: 16px;cursor: pointer;border: none}
.eraserForAllDiv {float: right;cursor: pointer;margin: 3px;width: 6%;}
.divLayout2 .eraserForAllDiv {cursor: pointer;margin: 1px;width: 32px; position:absolute; }/*top:25px !important; right:-15px !important*/
.divLayout2 .divLayout2SecSub .eraserForAllDivimg{ background-color: #ffffff73;} /*ST-1800  margin-left:11px; margin: -1px; right: auto !important;margin-top: -21px;*/
.divLayout2 .divLayout2SecSmall .eraserForAllDivimg {}/*margin-top: -22px;margin-right: -1px;margin-left:15px;*/
.divLayout2 .divLayout2SecSmall .divLayout2SecSub .eraserForAllDivimg {}/* margin-top: -20px; margin-right: -1px; margin-left: -5px !important; background: #ffffff !important;*/
.divLayout2 .eraserForAllDivimg{border: none; background-color: #ffffff73;}/*ST-1800- bg color  top: -11px;margin-top: -22px;margin-right: -6px;margin-left: 14px;*/
.divLayout2 .bordebottomblack .eraserForAllDiv {
    /*right: 0px !important; top:20px !important*/
	right: 24px !important;
    top: 20px !important;
} /*ST-1510.1,VR*/
.divLayout2 .bordebottomblack .eraserForAllDivimg{
	margin-top:-20px !important;
}
.divLayout2SecSub{ position:relative;}
.divLayout2SecSub .eraserForAllDiv {position:absolute;right: -16px;bottom: 0px; }/*right: -15px;top: -3px;*/
.divLayout2SecSub .cke_editable{min-height: 35px;}/*ST-1782, adding padding so font do not cut at bottom,ST-2050 remove padding*/
.IntroFixDiv {height: 40px;}
.IntroDragPlaceholder {height: 60px;background-color: #ecf3ff;}
/*ST-1549- BV,20181220*/
.aPickSolidColor {cursor:pointer;}

/*.cottage .georgica_pond fond in other layout too..*/
.divLayout4 .image_wrap {position: relative;height: 100%;width: 100%;}
.divLayout4 .show_item {display: block;}
.divLayout4 .shingle .new_singhle_layout {height: 415px;}
.divLayout4 .bottom_text_wrap {text-align: left;height:32px;/*ST-914*/}
.divLayout4 .upload_wrap {position: relative;height:auto; max-height:150px;width: 150px;margin: 0;}
.divLayout4 .cottage .georgica_pond ul li .upload_wrap a img {width: 40%;border: none;}
.divLayout4 .upload_wrap a span {font-size: 10px;display: block;}
.divLayout4 .cottage .georgica_pond.flipbook_rightbox ul li img{height:auto !important;}
.divLayout4 .bb-custom-side {width: 577px;}

.divLayout4 .cottage .georgica_pond{height: 533px;} /*ST-1582, BV 2019 02 20*/
.divLayout4 .cottage{padding: 22px 22px 22px 26px !important;width: 577px;}
/*.divLayout4 .bb-custom-side.shingle.flipbook_lefttbox {padding: 108px 20px 26px; min-height:578px}*/
.divLayout4 .bb-custom-side.shingle.flipbook_lefttbox {padding: 108px 20px 26px; min-height:576px}
.divLayout4 .cottage .georgica_pond {padding: 14px 35px 23px 24px;}
.Layout4-Right-Top {margin-bottom:18px !important}
.divLayout4 .bb-custom-wrapper{ height:578px}

.divLayout4 .editableBorderParent:hover .editableBorderByToolBar {box-shadow: inset 0 0 0 4px #c6deff, 0 0 0 4px #c6deff;}
.divLayout4 .editableBorderByToolBar { padding:14px 0px 23px 0px !important}

/*.editableBorderParent:hover .editableBorderByToolBar {box-shadow: inset 0 0 0 4px #c6deff, 0 0 0 4px #c6deff;}
.editableBorderByToolBar { padding:14px 0px 23px 0px !important}*/
.divLayout4 .editableBorderByToolBar .editableBorderInner{ width:472px; margin:0 auto; }

/*ST-1655 */
.drake_tower.sublayout22{ width:576px; padding:22px !important; margin:0px !important}
.sublayout22 .content_text{width: 100%;padding-top: 14px;margin-top: 39px;}
.sublayout22 .left_content {width: 232px;float: left;padding-right: 16px;}
.sublayout22 .right_content {width: 232px;float: right;}
.sublayout22 .clear{ clear:both}
.sublayout22 .subheading {width: 100%;text-align: right !important;height: 17px;}
.sublayout22 .sublayout22main{ padding:30px 30px;height: 532px; overflow: hidden; position:relative}
/*.sublayout22 .divHoverInner:before{ border: solid 0px #c6deff !important; }
.sublayout22 .divHoverInner:hover .sublayout22main{border: solid 3px #c6deff!important;}
.sublayout22 .divBorderActive .sublayout22main{border: solid 3px #5E94E0!important;}*/
.sublayout22 .sublayout22main.divHoverInner:before, .sublayout22 .sublayout22main.divHoverInner::before {position: absolute;content: '';border: solid 3px #c6deff;width: 532px;height: 532px; top: 0px;left: 0px;}
.sublayout22 .sublayout22main.divBorderActive:before, .sublayout22 .sublayout22main.divBorderActive::before {position: absolute;content: '';border: solid 3px #5E94E0;width: 532px;height: 532px; top: 0px;left: 0px;}

/*ST-1867*/
.sharefbm-profile {
    width: 762px;
    margin: 0 auto;
    background: #FAFAFA;
    padding: 15px 0px 5px
}

    .sharefbm-profile .profilebox {
        padding-left: 130px
    }

        .sharefbm-profile .profilebox .profileimg {
            float: left;
            width: 160px;
        }

            .sharefbm-profile .profilebox .profileimg img {
                width: 132px;
                height: 132px;
                border-radius: 132px;
                border: 5px solid #000
            }

        .sharefbm-profile .profilebox .profileheading {
            width: 380px;
            margin-top: 40px;
            float: left;
            font-size: 20px;
            color: #000000;
            line-height: 26px;
            font-family: 'Avenir-Heavy';
            font-weight: 600;
            text-transform: uppercase
        }

    .sharefbm-profile .clear {
        clear: both
    }

    .sharefbm-profile p {
        width: 95%;
        margin: 28px auto 20px;
        font-family: 'Rollerscript-Smooth';
        color: #550b90;
        font-size: 25px;
        line-height: 31px;
        text-align: center
    }

.sharefbm-footer {
    padding: 10px 0px;
    font-family: 'Avenir-Medium';
    font-size: 20px;
    color: #000000;
    line-height: 26px;
    text-align: center;
    background: #FAFAFA; /*width:463px;*/
    margin: 0 auto;
    display: inline-block;
}

    .sharefbm-footer a {
        color: #410166;
        text-decoration: none
    }

.abtn-sharefbm {
    display: inline-block;
    border: 1px solid #57307a;
    color: #550b90;
    background-color: #FFFFFF;
    font-size: 20px;
    font-family: 'Avenir-Heavy';
    border-radius: 20px;
    padding: 0px 40px 0px 40px;
    text-decoration: none;
    transition: all ease-out 0.5s;
    -webkit-transition: all ease-out 0.5s;
    -moz-transition: all ease-out 0.5s;
    text-transform: uppercase;
    height: 43px;
    font-weight: 900;
    font-style: normal;
    font-stretch: normal;
    letter-spacing: normal;
    text-align: center;
    line-height: 41px
}
/*ST-1746 */
.divsinglpg.body-gv-shrink,
.fbshrinksingle {
    /*-webkit-transform: scale(0.357);
    -moz-transform: scale(0.357);
    -ms-transform: scale(0.357);
    transform: scale(0.357);*/
    -webkit-transform: scale(0.240);
    -moz-transform: scale(0.240);
    -ms-transform: scale(0.240);
    transform: scale(0.240);
    transform-origin: top left;
    height: 150px;
}

/*.fbshrinkflip*/
.body-gv-shrink {
    /*-webkit-transform: scale(0.263);
    -moz-transform: scale(0.263);
    -ms-transform: scale(0.263);
    transform: scale(0.263);
    -webkit-transform: scale(0.242);
    -moz-transform: scale(0.242);
    -ms-transform: scale(0.242);
    transform: scale(0.242);*/
    -webkit-transform: scale(0.2492);
    -moz-transform: scale(0.2492);
    -ms-transform: scale(0.2492);
    transform: scale(0.2492);
    transform-origin: top left;
    height: 150px;
}



/*Common Template Rules*/
/*BV, ST-1544 2018 12 28,2019 01 03 white-space: nowrap; PLI 15,2019 01 04, for fix height */
.Img-Type-WaterMark img.clIsPlaceHolder {width:100%;}/*ST-1582, BV, 2019 01 25*/

.cke-TypeA p {text-align: center !important;overflow: hidden !important;letter-spacing: normal;}
.cke-TypeA {display: table-cell; vertical-align: middle; margin: 0px 0px 5px 0px; box-sizing: border-box; line-height: 16px;margin: 0px 0px 5px 0px;}
.cke-TypeC {vertical-align:middle;display: table-cell;width: 100%;margin: 0px;overflow: hidden;text-align: left;float: left;}
.cke-TypeC .cke_editable {display: table-cell;width: 100%;vertical-align: middle;min-height: 174px;height: 174px;max-height: 135px;overflow: hidden;}
.cke-TypeD {margin-bottom: 0;text-align: center;width: 100%;padding: 3px;float:left;}
.cke-TypeD .cke_editable{max-height: 42px;overflow: hidden;}
.cke-TypeD p { text-align:left !important;overflow: hidden !important;letter-spacing: normal; padding: 6px 10px 3px;display: inline-block; white-space: nowrap; overflow:hidden;}/*MB, 12/27/18 Changed from center to left*/
.cke-TypeF {line-height:12px; width: 100%;float: left;line-height: normal;letter-spacing: normal;padding: 0px 0;}
.cke-TypeF .cke_editable_inline {height:20px; overflow:hidden;}
.cke-TypeF p {width: 100%; font-family: MillerText-Roman;font-size: 12px; margin: 0px; padding:0px 0; text-align:right;overflow: hidden;letter-spacing: normal; white-space: nowrap;display: inline-block; }
.cke-TypeG {width: 100%;padding: 0px;line-height: normal;height: auto;text-align: left;float: left;}
.cke-TypeG p {text-align:left;overflow: hidden;letter-spacing: normal; padding: 0px;line-height: 1; /*display: inline-block;ST-1843, having diff line height */} 
.cke-TypeG .cke_editable {overflow:hidden;}
.cke-TypeH {width: 100%;padding: 0px;height: auto;float: left;overflow: hidden;white-space: nowrap;} /*BV,2019, 01 07*/
.cke-TypeH p {width: 100%; text-align:left;overflow: hidden;letter-spacing: normal; padding: 0px;line-height: normal; display: inline-block;margin: 0 0 3px 0;white-space: nowrap;} 
.cke-TypeH .cke_editable {overflow:hidden;}
#div32 .cke_editable  {padding-top:2px;}/*RP ST-1864*/
.cke-TypeJ {
} /*ST-1595,BV,2019 01 30*/
.cke-TypeJ-Upper {text-transform:uppercase;}
.cke-Rule1 p {
        /*font-family: Avenir-Book;font-size:28px;*/
              color:#000000; line-height:30px;}
.cke-Rule2 {}/*ST-1544 BV,2018 01 02*/
.cke-Rule3A p{padding-right: 10px;padding-left: 10px; display: inline-block; border-bottom: 1px solid #000;}

.cke-Rule3B {padding-right: 0px; display: inline-block; border-bottom: 1px solid #000;}/*RP ST-1864*/
.cke-Rule4 {}
.cke-Rule4-Parent .divEraserForAll { margin-right: -1px;margin-top: -14px;}/*ST-1635,BV, 2019 02 06*/
.cke-Rule4-Parent .cke-Rule4 .fullBorderForRW{outline: 3px solid #5e94e0 !important; border:none !important;}
.cke-Rule4-Parent:hover .cke-Rule4 {outline: 3px solid #c6deff; border: 0px solid #c6deff!important;}/*ST-1476, BV,2019 01 23 */
.cke-Rule4-Parent:hover .cke-Rule4 .fullBorderForRW{outline: 3px solid #5e94e0 !important; border:none !important;}/*ST-1476, BV,2019 01 23*/
.cke-Rule4-Parent:hover .cke-Rule4.fullBorderForRW{outline: 3px solid #5e94e0 !important; border:none !important;}/*ST-1476, BV,2019 01 23*/
.cke-Rule4-Parent:hover .cke-Rule4 .cke_editable.ApplyHoverEffect:hover {border:none !important;}

.cke-Rule6 {line-height:normal;border-bottom: 1px solid #000 ;width: 100%;padding: 0;height: 38px ;margin-bottom: 0px;}/*RP ST-1864*/
.cke-Rule7 p ,.cke-Rule7 p span{color:#000000;}
.cke-Rule-Notext {border-bottom: 0px solid;} /*ST-1581,BV, 2019 01 09*/
.cke-Rule-Notext p{border-bottom: 0px solid;}
 

/* Common Border Rules & Border class, == START*/
/*ST-1582,2019 02 04*/
.PLI-CustomBorder {}
/*---- .border-Type2:hover {box-shadow:0px 0px 0px 3px #c6deff;border: 5px solid blue; }*/
/*Image getting selected due to following*/
/*.border-Type2:hover::after{  content: "";position: absolute;border: solid 3px #c6deff;width: 100%;height: 100%;top: 1px;left: 1px;}*/
/*.border-Type1:hover {background-color: rgba(198, 222, 255, 0.74);}
.border-Type1:hover {box-shadow:0px 0px 0px 3px #c6deff; }*/
.border-Type1-PH:hover{padding:1px;}
/* --- .divLayout1 .border-Arrow1 img{ height:20px !important;margin-top: -12px !important;} */
.border-Type1.border-Type1-PHNon:hover{padding:2px;}
.border-Type2.divHoverInner .fre_bg_img_border{  border: solid 3px #57307a;}
.PLI-Border-210 .bg_end_paperinner{ width:100%; height:100%; position:relative;background-color: #ffffff;}
/*.divLayoutBC .PLI-CustomBorder:hover{box-shadow: 0px 0px 0px 3px #c6deff;}
.divLayoutBC .PLI-CustomBorder:hover::before {content: "";position: absolute;width: 100%;height: 100%;top: 0px;left: 0px;border: solid 3px #c6deff;}*/
.divLayoutBC .divHoverInner::before {content: "";position: absolute;width: 100%;height: 100%;top: 0px;left: 0px;border: solid 3px #57307a;}
.divHoverInnerActive-210::before{  content: "";position: absolute;border: solid 3px #57307a !important;width: 100%;height: 100%;top: 0px;left: 0px;}
.drake{padding:22px !important;margin-left: -2px !important;}
.darkmain{ position:absolute; width: 538px;}
.divHoverInner::before{  content: "";position: absolute;border: solid 4px #57307a;width: 100%;height: 100%;top: 0px;left: 0px;}
.divHoverInnerActive-209::before{  content: "";position: absolute;border: solid 3px #57307a;width: 100%;height: 100%;top: 0px;left: 0px;}
.divHoverInnerActive-208::before{  content: "";position: absolute;border: solid 3px #57307a;width: 100%;height: 100%;top: 0px;left: 0px;}
.divHoverInnerActive-210::before{  content: "";position: absolute;border: solid 3px #57307a;width: 100%;height: 100%;top: 0px;left: 0px;}
.divFCBorder .divHoverInner::before{  content: "";position: initial;border: solid 3px #57307a;width: 100%;height: 100%;top: 0px;left: 0px;}

#FBBorderImage{width:23px; height:15px; margin-left: -30px; border: 0px; margin-top: -08px;position:absolute;z-index: 9;}
#FBOuterBorderImage {width:46px; height:40px;  position:fixed; top: 35px; left: 139.2px ; border: 0px;z-index: 10000;}
/*ST-1707*/
.divLayout2 .PLI-CustomBorder {background: #fff;border: solid 20px #fff;height: 528px; box-shadow: #57307a 0px 0px 0px 0px}
.divLayout2 .custom_side_wrapper{width: 1052px;height: 100%;margin: auto auto;}/*RP ST-1731*/
.divLayout2 .bb-custom-side.frederich_img.frederich_img_Left, .divLayout2 .bb-custom-side.frederich_img.frederich_img1{width: 522px !important; padding:0px;}
.divLayout2 .bb-custom-side.frederich_img.frederich_img_Left, .divLayout2 .bb-custom-side.frederich_img.frederich_img1{/*height: 478px !important;*/padding-top: 28px;}
.divLayout2 .addLRSection_flip{ margin-top:70px}
.divLayout2 .PLI-CustomBorder.divHoverInner {box-shadow: #57307a 0px 0px 0px 3px !important} /*rgb(198, 222, 255)*/
.divLayout2 .PLI-CustomBorder.divHoverInner .divlayout2inner{ border:solid 3px #57307a; height: 100%;}
.divLayout2 .PLI-CustomBorder::before{ border: solid 0px #c6deff; display:block}
.divLayout2 .PLI-CustomBorder .divlayout2inner{height:100%;background:#ffffff;}/*RP ST-1731, ST-1833,BV*/
.divLayout2 .bb-custom-side.frederich_img1 .footer_url{top: 50px !important;}
.divLayout2 .antiBullPen{ position:absolute; width:100%; /*bottom:-100px*/bottom:-93px}
.divLayout2 .PLI-CustomBorder.divBorderActive {box-shadow: rgb(94, 148, 255) 0px 0px 0px 3px !important}
.divLayout2 .PLI-CustomBorder.divBorderActive .divlayout2inner{ border:solid 3px rgb(94, 148, 255); height: 100%;}
.divLayout2 .img-PliMain {object-fit: cover;}/*ST-2044*/
/*.divLayout2 .bb-custom-side.frederich_img1 .content_wrap_in{height: 395px;}*/
.padding-top80 {padding-top:120px !important;}/*ST-1712*/
/* Border class, == END*/


/*ST-1800*/
.divLayout2 .bb-custom-side.frederich_img.frederich_img1{width: 533px !important; padding-right:0px !important;margin-right: -8px;}
.divLayout2 .bb-custom-side.frederich_img.frederich_img1 .content_wrap{width: 533px !important;}
.bb-custom-side.frederich_img.frederich_img1 .content_wrap .in_inner{width: 515px !important;}
.divLayout2 .Hamburger, .divLayout2 .trashcan{margin-right: -16px;}
.frederich_img1 .divLayout2SecParent {width:528px}
.divLayout2 .bb-custom-side.frederich_img.frederich_img1{ height: 486px !important;}
.divLayout2 .bb-custom-side.frederich_img.frederich_img_Left, .divLayout2 .bb-custom-side.frederich_img.frederich_img1{ height:99.5% !important}

/*ST-1955*/
.divLayout2 .custom_side_wrapper{ width:100%}
.divLayout2 .bb-custom-side.frederich_img.frederich_img_Left, .divLayout2 .bb-custom-side.frederich_img.frederich_img1{ width:50% !important}
.bb-custom-side.frederich_img.frederich_img_Left .content_wrap, .bb-custom-side.frederich_img.frederich_img1 .content_wrap{width: 100%;}
/*.bb-custom-side.frederich_img.frederich_img_Left .content_wrap .in_inner{width: 100%; padding-left:2%}*/
.bb-custom-side.frederich_img.frederich_img_Left .content_wrap .in_inner{width: 100%; padding-left:14px}
.divLayout2SecParent.divLayout2SecSmall{width: calc(100% - 200px);}
.divLayout2SecParent.divLayout2SecSmall{width: calc(100% - 200px);}
.divLayout2SecParent{ padding-right:0px;width: calc(100% - 17px);}
.bb-custom-side.frederich_img.frederich_img1 .content_wrap{ padding-left:0px;}
.divLayout2 .bb-custom-side.frederich_img.frederich_img1 .content_wrap{width: 100% !important;}
/*.bb-custom-side.frederich_img.frederich_img1 .content_wrap .in_inner{width: 100% !important; padding-left:2% !important}*/
.bb-custom-side.frederich_img.frederich_img1 .content_wrap .in_inner{width: 100% !important; padding-left:14px !important}
.frederich_img1 .divLayout2SecParent{padding-right:0px;width: calc(100% - 14px);}
.divTopHeadingRight { width:98%;}
.frederich_img1 .flpbook_achievemnet{ width:100%;}

/* General Class For Managing Area*/
/*ST-1441,BV 2019 01 05 */
 
/*Layout Specif Styles = START*/
/*BV,ST-1443, BV, 2019 01 07*/

.Sublayout-12-ul li,.Sublayout-15-ul li {width: 154px !important;} /*ST-1452 PK 02/04/2019 , Added Width*/
.sublayout21 .linebutton .linetext.dingtext {padding-left: 15px} /*ST-1623 PK 02/04/2019 , For Position of Dingbats*/

.cke_editable a:hover {color:#326891;}/*ST-1479,BV ,2019 03 2019,ST-1740*/
.cke_editable a {color:#326891}/*ST-1740, BV, 2019 04 24*/
/*Layout Specif Styles = END*/

/*ST-1973*/
.abussCardYourName, .abussCardYEmail, .abussCardYourURL {position:absolute;right:-15px;}/*ST-2084*/
.abussCardPhoneNo {position:absolute;right:54px; top:1px;} 
.abussCardYourName img, 
.abussCardPhoneNo img,
.abussCardYEmail img,
.abussCardYourURL img {height:12px;}

/*PLI-Specific Styles*/
.lbl-PLIMain {font-weight: normal;}
.lbl-PLI-2 {
    margin: 0;width: 100%;height: 28px;font-family: 'GillSans-Light';
    font-size: 25px;font-weight: 300;font-style: normal;font-stretch: normal;
    line-height: normal;letter-spacing: normal;text-align: center;color: #888888;
    white-space: nowrap;padding: 0;}
/*ST-1467,ST-1549,BV2018 12 21, added padding,ST-1813*/
.cke-PLI5 .cke_editable {border:3px solid transparent;padding-top :12px;padding-right :20px;padding-left:20px;padding-bottom: 4px;max-height: 230px;overflow: hidden;/*min-width:260px*/}/*RP ST-1864*/ /*RP ST-1620*/
.cke-PLI5 p {text-align:center;}
.cke-PLI9 {width:98%;}
/*ST-1556*/ 
.cke-PLI7 {margin-top: 3px;}
/*.cke-PLI7 cke_editable p{line-height:126px;} ST-1843*/
.cke-PLI14 {margin: 0 auto 5px;}
.cke-PLI14 p{white-space: nowrap;}
/*ST-1843*/
.cke-PLI15{ float: none; height: 72px; word-wrap: break-word;}
.cke-PLI15 .cke_editable {min-height: 72px;height: 72px;vertical-align: bottom; display: table-cell;width: 472px;}/*RP ST-1868*/
.cke-PLI15 p{padding: 0px 0 0px 0;white-space: initial;}/*BV,2019 01 08*//*RP ST-1868*/
.cke-PLI16 p {font-weight: normal;font-style: normal;font-stretch: normal;line-height: normal !important;}/*BV,2019 01 08*/
.cke-PLI16 {padding: 0 !important;}
.cke-PLI16 .cke_editable_inline{ height:53px;overflow:hidden}
.cke-PLI16 .cke_editable{ height:49px;overflow:hidden;width:465px;}/*BV,2019 01 08*/
.img-PLI18 {height: 174px;width: 150px;}
.cke-PLI17 {height: 174px;max-height: 174px;min-height: 135px; width:289px;padding-left:0px; margin-left:14px}/*ST-1843*/
.cke-PLI17 .cke_editable{width:289px;}
.cke-PLI17 p{font-weight: 300;line-height: 1;}/*ST-1843 : line-height */
.cottage .georgica_pond .cottage_img.img-PLI-18-Parent{ width:150px; height:174px}
/*ST-1445, PK,2019 01 08 New added starts*/

.img-PLI22 {height: 576px;width: 734px;position:relative;}/*RP ST-1627*/
.cke-PLI24 {margin-top:17.5px;margin-bottom:45px;}
.cke-PLI24 p{width: 329px;font-weight: 300;line-height:1;}/*ST-1843, added line-height to 1*/
.cke-PLI24 .cke_editable{height: 145px;} 
.img-PLI25 {height: 92px;width: 332px;}
.img-PLI25 img{height: 92px !important;width: 332px !important;}
.cke-PLI26,.cke-PLI28 {padding-top:2px;padding-bottom:10px;width: 332px;overflow:hidden}
.cke-PLI26 .cke_editable,.cke-PLI28 .cke_editable{height: 19px;min-height:19px}
.img-PLI27 {height: 92px;width: 332px;position:relative}/*RP ST-1627*/
.img-PLI27 img{height: 92px;width: 332px;}

.cke-PLI32 {max-height: 76px !important;}
.cke-PLI32 .cke_editable{border: 3px solid transparent;}/*RP ST-1804*/
.cke-PLI32 p {margin-bottom: 7px;margin: 0px;font-weight: normal;font-style: normal;font-stretch: normal;}
.cke-PLI32 {height:76px!important;max-height: 76px !important;width: 484px;overflow:hidden;}/*RP ST-1445*/
.cke-PLI32 p {margin-bottom: 7px;margin: 0px;font-weight: normal;font-style: normal;font-stretch: normal;}
.cke-PLI33 {margin-top: 3px !important;margin-bottom:8px !important;width: 483px;}/*RP ST-1445*/
.cke-PLI33 .cke_editable{height: 23px;}
.cke-PLI33 p {font-weight: normal;font-style: normal;font-stretch: normal;text-align: right;margin: 0 !important}
.cke-PLI34 p {width: 240px;font-weight: 300/*;line-height: 16px;*/} /*ST-1843 - line-height to 1*/
.cke-PLI34 .cke_editable_inline {overflow: hidden;height: 130px;min-height:130px; padding-top:5px}
/*ST-1445, PK,2019 01 08 New added Ends*/
.cke-PLI43 .cke_editable {min-height:42px;height:42px;}/*RP ST-1864*/
.cke-PLI43 p {margin-top: 3px;}
.cke-PLI44 .cke_editable_inline{height:428px; overflow:hidden;}
.cke-PLI45 .cke_editable_inline{height:242px; overflow:hidden;}
/*.cke-PLI45 p, .cke-PLI44 p{line-height: 1.5; } ST-1571, BV,2019 01 04 set font by Ref 1578.1
    BV, Change Request ST-1582 3/14/2019-Body text font incorrect; should be like Border Singlet. 
*/
.cke-PLI45 {margin-bottom:12px;}
.cke-PLI49 .cke_editable  {min-height:42px; height:42px;}/*RP ST-1864*/
.cke-PLI49 p {margin-top: 0px;}/*RP ST-1864*/
.cke-PLI50 .cke_editable {height:34px; min-height:370px;}
/* ST-1446 , PK 01/08/2019 Album Page Starts */
.cke-PLI51 {margin-top: 36px;margin-left: 49px;width:auto;max-width:480px;overflow:hidden;min-width:150px;}/*RP ST-1620*/
.cke-PLI51 .cke_editable {min-height:37px; height:37px;}/*RP ST-1864*/
.img-PLI52 {position: absolute;float: left;margin-top: 106px;width: 165px;height: 206px;margin-left: 49px;}
.img-PLI52 img{width:100%;height:100%}
.img-PLI53 {position: absolute;float: left;margin-top: 327px;width: 179.1px;height: 204px;margin-left: 49px;}
.img-PLI53 img{width:100%;height:100%}
.img-PLI54 {position: absolute;margin-top: 106px;width: 302px;height: 159.1px;float: left;margin-left: 230px;}
.img-PLI54 img{width:100%;height:100%}
.img-PLI55 {position: absolute;margin-top: 282px;width: 285px;height: 250px;float: left;margin-left: 244px;}
.img-PLI55 img{width:100%;height:100%}
.img-PLI56 {position: absolute;margin-top: 37px;width: 233px;height: 495px;float: left;margin-left: 47px;}
.img-PLI56 img{width:100%;height:100%}
.img-PLI57 {position: absolute;margin-top: 37px;width: 234px;height: 177.1px;float: left;margin-left: 296px;}
.img-PLI57 img{width:100%;height:100%}
.img-PLI58 {position: absolute;margin-top: 230px;width: 235px;height: 302px;float: right;margin-left: 296px;}
.img-PLI58 img{width:100%;height:100%}
/* ST-1446 , PK 01/08/2019 Album Page Ends*/
.cke-PLI67 {margin-bottom: 20px}
/*.cke-PLI67 p {line-height:38px} ST-1843 - line-height to 1*/
.cke-PLI67 .cke_editable {min-height: 38px;height: 38px;}
.cke-PLI68 {width: 438px;padding-right:35px;padding-left:35px;/*line-height: 21px;*/} /*ST-1843 - line-height to 1*/
/*.cke-PLI68 p{line-height: 21px;} ST-1843 - line-height to 1*/
.cke-PLI68 .cke_editable {min-height: 66px;height: 66px;overflow:hidden;/*line-height: 21px;*/}/*ST-1843 - line-height to 1*/
.cke-imagePLI69{width: 150px;height: 172px !important;margin-bottom:7px;position: relative;}/*RP ST-1627*/
.cke-imagePLI69 img , .cke-imagePLI71 img{width: 150px;height: 172px !important}
.cke-imagePLI69 .close_icon_PlaceHolder img , .cke-imagePLI71 .close_icon_PlaceHolder img{width: auto!important;height: auto!important;}/*RP ST-1627*/
.cke-PLI70{width: 149px;float:left;}
/*.cke-PLI70 p{line-height: 15px !important;} ST-1843 - line-height to 1*/
.cke-PLI70 .cke_editable_inline{height: 348px;overflow:hidden;}
.cke-imagePLI71{width: 150px;height: 173px !important;position:relative;}/*RP ST-1627*/
.cke-PLI72{width: 149px;height: 351px;float:right;}
/*.cke-PLI72 p{line-height: 15px !important;} ST-1843 - line-height to 1*/
.cke-PLI72 .cke_editable_inline{height: 348px;overflow:hidden;}
.cke-PLI73 .cke_editable {min-height:38px; height:38px;}
.cke-PLI74 {width: 480px;text-align: center;margin: 0 auto;height:48px;max-height:78px;min-height: 78px;padding-top: 24px;}
.cke-PLI74 p{margin-bottom: 0px !important;width: 480px;/*line-height: 1.16*/} /*ST-1843 - line-height to 1*/
.cke-PLI74 .cke_editable_inline{ height:48px;overflow:hidden}
.cke-PLI78 {margin-right: 10px;width: 239px;}
/*.cke-PLI78 p {line-height: 16px !important;} ST-1843 - line-height to 1*/
.cke-PLI78 .cke_editable_inline{height:179px; overflow:hidden;}
/*ST-1450 , PK,2019 01 08 New added starts*/
.cke-PLI80 .cke_editable {min-height: 38px;height: 38px;}
.cke-PLI81 {margin-bottom: 0px !important;width: 580px;padding-top: 24px;min-height: 96px;max-height: 96px;text-align: center;margin: 0 auto;height: 66px;padding-left:1%}
/*.cke-PLI81 p {width: 352px;line-height: 1.10;} ST-1843 - line-height to 1*/
/*.cke-PLI81 .cke_editable {min-height:66px; height:66px;}*/
.cke-PLI81 .cke_editable_inline{ height:66px;overflow:hidden}
.cke-PLI82 {width: 228px;}
/*.cke-PLI82 p {line-height: 16px !important;} ST-1843 - line-height to 1*/
.cke-PLI82 .cke_editable_inline{height:113px; overflow:hidden;}
.cke-PLI183 .cke_editable {border:3px solid transparent; max-height: 230px;overflow: hidden;padding-top :5px;padding-right :20px;padding-left:20px;padding-bottom:8px;}/*ST-1813*/
.cke-PLI183 p {text-align:center;}
/*.cke-PLI183 .cke_editable {max-height: 230px;overflow: hidden;padding-top :2px;padding-right :20px;padding-left:20px;padding-bottom:14px;}
.cke-PLI183 .cke_editable, .cke-PLI183 .cke_editable p {font-size: 38px; color: #000000;font-weight: bold;font-family: 'OptimaBold';line-height: 32px;text-align:center;}*/
/*PK,ST-1452,2019 01 08 New Added starts*/
.cke-PLI85 {width: 472px;text-align: left;margin-bottom: 7px;}
.cke-PLI85 .cke_editable {min-height:38px; height:38px;}
.cke-PLI86 {width: 481px;text-align: center;padding-top: 36px;}
.cke-PLI86 .cke_editable{overflow: hidden;height: 66px;}
.cke-PLI86 p{font-weight:400;/*line-height:1.11*/;margin-left:48px;margin-right:33px}/*ST-1843 - line-height to 1*/
.img-PLI87 {height: 178.79px;width: 152px;position:relative;}/*RP ST-1627*/
.cke-PLI88 {width: 305px;max-height: 172px;}
.cke-PLI88 p{width: 295px;overflow:hidden;}
.cke-PLI88 .cke_editable_inline{font-weight:300;font-style:normal;font-stretch:normal;/*line-height:1.33;*/overflow:hidden !important;height: 172px;}/*ST-1843 - line-height to 1*/
.img-PLI89 {height: 153.95px;width: 152px;position:relative;}/*RP ST-1627*/
.img-PLI90 {height: 153.45px;width: 152px;position:relative;}/*RP ST-1627*/
.img-PLI91 {height: 152.7px;width: 152px;position:relative;}/*RP ST-1627*/
/*ST-1449 , PK,2019 01 09 New added Starts*/
.cke-PLI92 {width:481px;max-width:480px;overflow:hidden;min-width:50px;}
/*.cke-PLI92 p {line-height:38px;} ST-1843 - line-height to 1*/
.cke-PLI92 .cke_editable {min-height:38px; height:38px;}
.cke-PLI93{width:232px; margin-top:39px;}
.cke-PLI93 .cke_editable {min-height:419px; height:419px;}
.cke-PLI93 p{/*line-height : 16px;*/letter-spacing: normal;}/*ST-1843 - line-height to 1*/
.cke-PLI94 {width :233px;margin-left:16px;margin-top:39px;}
.cke-PLI94 .cke_editable {min-height:169px; height:169px;}
.cke-PLI94 p{/*line-height: 16px;*/letter-spacing: normal;}/*ST-1843 - line-height to 1*/
/*ST-1689,BV,2019 01 03*/
.img-PLI95 img.img-PliMain {height: 100%; width:100%}
.img-PLI95 {position:relative; width:233px !important;height: 231.5px !important;margin-left: 248px;margin-top: 20px;}
/*ST-1449 , PK,2019 01 09 New added Ends*/
.img-PLI96 {width: 482px;height: 255.7px;}
.img-PLI96 img {width: 482px;height: 255.7px;}
.cke-PLI97 {margin-top: 19.7px;}/*RP ST-1864*/
.cke-PLI97 .cke_editable_inline { height: 40px; overflow : hidden !important;}/*RP ST-1864*/
.cke-PLI97 p {margin : 0 ;/*line-height:35px !important;*/}/*ST-1843 - line-height to 1*/
.cke-PLI98 {margin-top: 18px;text-align: center;width: 230px !important;} /*ST-1632 VR */
.cke-PLI98 p {text-align: left;width: 230px;/*line-height: 16px;*/}/*ST-1843 - line-height to 1*/
.cke-PLI98 .cke_editable_inline { height: 148px; overflow :hidden !important;}/*ST-1632 VR */

.cke-PLI98 p {text-align: left;width: 230px;/*line-height: 16px;*/}/*ST-1843 - line-height to 1*/

.img-PLI99{position:relative;}/*RP ST-1627*/
.img-PLI99 img {width: 234px !important;height: 227.3px !important;}
.img-PLI99 .close_icon_PlaceHolder img{width:auto!important;height:auto!important;}/*RP ST-1627*/

/*ST-1451, PK,2019 01 09 New added Starts*/
/*.cke-PLI100 p {line-height: 38px;}*//*ST-1843 - line-height to 1*/
.cke-PLI100 .cke_editable {min-height:38px; height:38px;overflow: hidden;}
.cke-PLI101{padding-top: 5px;padding-right: 5px;margin-bottom:14px}
.cke-PLI101 .cke_editable {min-height:22px; height:22px;overflow:hidden;}
.cke-PLI102 {width: 231px;float:left;}
/*.cke-PLI102 p {line-height: 16px;}*//*ST-1843 - line-height to 1*/
.cke-PLI102 .cke_editable_inline {height: 404px;overflow: hidden;}

/*ST-1451, PK,2019 01 09 New added ENDS*/
/*PK,ST-1452,2019 01 08 New Added Ends*/
/*Starts ST-1455, PK,2019 01 07 added new class for PLI103*/
.cke-PLI103 {margin: 35px 0px 5px 26px;text-align: left;padding-left: 0px;padding-top: 0px;padding-bottom: 0px;width: auto !important;max-width: 235px;overflow: hidden;min-width:175px}/*RP ST-1620*/
.cke-PLI103 p {padding: 0px;margin: 0px;text-align: left;color: #000;/*line-height: 40px !important*/}/*ST-1843 - line-height to 1*/
.cke-PLI103 .cke_editable {min-height:38px; height:38px;}
.cke-PLI114 {margin-top: 3px;}
.cke-PLI114 cke_editable {max-height:40px;overflow:hidden;}
/*.cke-PLI114 cke_editable p{line-height:26px;}*//*ST-1843 - line-height to 1*/
/*.cke-PLI114 cke_editable p:hover {border-bottom: 3px solid #C6DEFF;}*/
.cke-PLI114 p:hover {border-bottom:3px solid #C6DEFF !important;}
.cke-PLI114 p {border-bottom:3px solid transparent;}
/*Ends ST-1455, PK,2019 01 07 added new class for PLI103*/

.cke-PLI185 {margin-top: -9px;}
.cke-PLI185 p{white-space: nowrap;}
/*ST-1448,1445, PK,2019 01 07, 01 10 2019*/
.cke-PLI186 {margin:4px 0 8px 0; max-height:20px;} /*BV,ST-1544,2019 01 07,ST-1843*/
.cke-PLI188 {margin:2px 0 19px 0; max-height:20px;} /*BV,ST-1544,2019 01 07*/
.cke-PLI191 {margin-top:5px;}
.cke-PLI199 p {width: 240px;font-weight: 300;/*line-height: 16px;*/}/*ST-1843 - line-height to 1*/
.cke-PLI199 .cke_editable_inline {overflow: hidden;height: 130px;min-height:130px;padding-top:5px}
.cke-PLI200 {width: 232px;min-height:179px}
.cke-PLI200 .cke_editable_inline{height:179px; overflow:hidden;}
/*.cke-PLI200 p {line-height: 16px !important;}*//*ST-1843 - line-height to 1*/
/*ST-1450 , PK,2019 01 08 New added Ends*/

.cke-PLI201 {width: 228px !important;margin: 0px 5px 5px 0px;text-align: left;float: right;}
/*.cke-PLI201 p {line-height: 16px !important;}*//*ST-1843 - line-height to 1*/
.cke-PLI201 .cke_editable_inline{height:113px; overflow:hidden;}
/*ST-1448, PK,2019 01 07 New added Ends*/
.cke-PLI202 {width: 231px;float: right;}
.cke-PLI202 .cke_editable_inline {height: 404px;overflow: hidden;}
/*.cke-PLI202 p {line-height: 16px;}*//*ST-1843 - line-height to 1*/
/*.cke-PLI203 p{line-height:29px;}*//*ST-1843 - line-height to 1*/

.img-PLI-204 {position: absolute;width: 201px;height: 93px; right:20px;top:90px;}/*ST-2027,BV top 90 ,ST-1870, ST-1582, BV, 2019 01 25*//*ST-1778 23-4-2019*/
.img-PLI-204 .img-PliMain{width: 100%;height: 100%; opacity:0.1;}/*ST-1582, BV, 2019 01 25*/

.cke-PLI205 {width: 40%;padding: 0px;height: auto;overflow: hidden;white-space: nowrap;margin-left: 146px;/*line-height: 3px;*/}/*ST-1843 - line-height to 1*/
.cke-PLI205 p {overflow: hidden;letter-spacing: normal; display: inline-block;margin: 2px 0 0 0;white-space: nowrap;/*line-height: 30px;*/} /*ST-1843 - line-height to 1*/
.cke-PLI205 .cke_editable {overflow:hidden;border: 3px solid transparent;min-height:38px;}/*RP ST-1728*/
.cke-PLI205 .divEraserForAll{display:none!important;}/*RP ST-1728*/
/*ST-1656*/
.cke-PLI211 .cke_editable {min-height:37px;height:37px;}/*RP ST-1864*/
.cke-PLI212 .cke_editable {min-height: 22px;height: 24px;}
.cke-PLI213 .cke_editable_inline{height: 428px; overflow: hidden;}
.cke-PLI213 {height:401px; overflow:hidden}
.cke-PLI214 .cke_editable_inline{height: 242px; overflow: hidden;}
.cke-PLI214 {margin-bottom: 12px; height:218px; overflow:hidden;}
/*.cke-PLI214 p, .cke-PLI213 p{padding-bottom: 8px;}*/
.img-PLI216{ margin-top:6px;position:absolute; bottom:0px; right:0; width:232px; height:164px}


/*PLI-Specific Styles -- Ends */

/*PK,01/22/2019 ST-1502*/
.drag-Img-slider:hover {outline: 3px solid #c6deff;/*opacity: 0.6*/;background: rgba(255, 255, 255, 0.6);background-image: none;}
.img-PLI4.drag-Img-slider:hover,.img-PLI31.drag-Img-slider:hover,.img-PLI22.drag-Img-slider:hover,.img-PLI48.drag-Img-slider:hover,.img-PLI42.drag-Img-slider:hover {
    border: 3px solid #c6deff;/*opacity: 0.6;*/background: rgba(255, 255, 255, 0.6);background-image: none;outline:none;
}
.Image-Active.drag-Img-slider:hover {outline: 3px solid #5e94e0;opacity: 1;}
.Image-Active.drag-Img-slider {outline: 3px solid #5e94e0;}
.Img-Type-WaterMark.drag-Img-slider:hover {outline: 3px solid #FF00FF;opacity: 1;} /*ST-1582,BV 2019 01 24*/
.Img-Type-WaterMark.Image-Active.drag-Img-slider {outline: 3px solid #FF00FF;}/*ST-1582,BV 2019 01 24*/
.img-PLI4.Image-Active.drag-Img-slider:hover, .img-PLI31.Image-Active.drag-Img-slider:hover, .img-PLI22.Image-Active.drag-Img-slider:hover ,.img-PLI48.Image-Active.drag-Img-slider:hover,.img-PLI42.Image-Active.drag-Img-slider:hover 
{
    border: 3px solid #5e94e0;opacity: 1;outline:none;
}
.img-PLI4.Image-Active.drag-Img-slider, .img-PLI31.Image-Active.drag-Img-slider, .img-PLI22.Image-Active.drag-Img-slider, .img-PLI48.Image-Active.drag-Img-slider, .img-PLI42.Image-Active.drag-Img-slider {
    border: 3px solid #5e94e0;outline: none;
}
/*PK,01/22/2019 ST-1502 ENDS*/


/*PK,01/22/2019 ST-1543 Starts*/
.notelayout {width: 576px;height:576px; /*min-height:691px;*/background: fff; /*position:relative;*/float: left;}
.notebox { width:576px;height:576px; /*min-height:579px;*/ background:fff; /*position:relative;*/ /*border:solid 1px #979797;*/ padding:20px 65px;}
.notetitle {font-family: 'AvenirLTStd-Book';font-size:28px; color:#000; text-align:center; position:relative;line-height: 30px;}
.notetitle .leftnoteicon{ background:url(/images/noteicon-left.png) no-repeat center; position:absolute; width:30px; height:43px; content:" ";/*margin-left: -89px;*/  left:122px;  margin-top:-6px;}/*RP ST-1751*//*RP ST-1620*/
.notetitle .rightnoteicon{ background:url(/images/noteicon-right.png) no-repeat center; position:absolute; width:30px; height:43px; content:" ";/*margin-left: 84px;*/  right:120px;   margin-top: -46px;}/*RP ST-1751*/
.notestext {padding: 0px 23px 0px 23px;font-family: 'AvenirLTStd-Book';margin-left:135px}/*RP ST-1620*/
.linebutton .switch {position: relative;display: inline-block;width: 53px;height: 24px;vertical-align: middle; background:transparent; float:none; }
.linebutton .switch input {display: none;}
.linebutton .switch .sliderround {position: absolute;cursor: pointer;top: 0;left: 0;right: 0;bottom: 0;background-color: #fff;-webkit-transition: .4s;transition: .4s;border: solid 1px #410166;}
.linebutton .switch .sliderround:before {position: absolute;content: "";height: 18px;width: 18px;left: 3px;bottom: 2px;background-color: #410166;-webkit-transition: .4s;transition: .4s; z-index:99}
.linebutton .switch .sliderround:after{}
.linebutton .switch input:checked + .sliderround {background-color: #fff;}
.linebutton .switch input:focus + .sliderround {box-shadow: 0 0 1px #2196F3;}
.linebutton .switch input:checked + .sliderround:before {-webkit-transform: translateX(26px);-ms-transform: translateX(26px);transform: translateX(26px);}
.linebutton .switch .sliderround.round {border-radius: 11px;}
.linebutton .switch .sliderround.round:before {border-radius: 50%;}
.linebutton .switch .dotleft {position: absolute;z-index: 1;left: 6px;width: 6px;height: 6px;background: #4a4a4a;border-radius: 6px;top: 40%;}
.linebutton .switch .dotright {position: absolute;z-index: 1;right: 6px;width: 6px;height: 6px;background: #4a4a4a;border-radius: 6px;top: 40%;}
.linebutton{ margin:30px 0px; text-align:right;}
.linebutton .linetext{ color:#410166; font-size:16px;font-family: 'Avenir-Book';cursor:pointer;}/*RP ST-1650*/
.linebutton .offtext{ color:#000000; font-size:16px;font-family: 'Avenir-Book';}/*RP ST-1628*/
.notetext{ width:460px; padding:5px; max-height:494px;min-height:494px} /*ST-1623 Remove Last Line , PK , 08-Feb-2019*/
.notetext.lineborder {background-image:-webkit-linear-gradient(white, white 30px, #ccc 30px, #ccc 31px, white 31px);background-image: -moz-linear-gradient(white, white 30px, #ccc 30px, #ccc 31px, white 31px);background-image:-ms-linear-gradient(white, white 30px, #ccc 30px, #ccc 31px, white 31px);background-image: -o-linear-gradient(white, white 30px, #ccc 30px, #ccc 31px, white 31px);background-image: linear-gradient(white, white 30px, #ccc 30px, #ccc 31px, white 31px);background-size: 100% 31px;border: 0px solid #ccc;border-radius: 0px;box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0.1);line-height: 29px;font-family: AvenirLTStd-Book;padding: 8px;}
.noteclose{ background:url(/images/note-delete.png) no-repeat; width:9px; height:9px; position:absolute;display:none}
.rightnoteclose{ background:url(/images/note-delete.png) no-repeat; width:9px; height:9px; position:absolute;display:none; margin-left:25px}
.notecontent .ApplyHoverEffect:hover,.notecontent .ApplyHoverEffect:active{width: 469px !important;min-height: 490px !important;}
.cke-PLI203 .cke_editable {height:490px;overflow:hidden;margin-left: -9px !important;margin-right:-21px;}
.notecontent .divEraserForAll{margin-right:-16px !important;}
.sublayout21 .notecontent{ width:auto !important; height:auto !important}
/*.notes_wrapper p{ min-height:auto !important; max-height:auto !important; overflow: inherit;}*/
/*PK,01/22/2019 ST-1543 ENDs*/
.leftnoteicon:hover .noteclose{ display:block}
.rightnoteicon:hover .rightnoteclose {display: block} 
/*ST-1613,pk,2019 01 31*/
.bc-urltext p {width:185px;}


/*RP ST-1761*/
.SubLayouts, .divLayout2, .divLayout4, .common_margin_layout{ position:relative}
.SubLayouts .beta_testing, .divLayout2 .beta_testing, .divLayout4 .beta_testing, .common_margin_layout .beta_testing{right: -1px !important; bottom: -94px !important;}
.divLayout1 .beta_testing, .divLayoutBC .beta_testing{right: -78px !important; bottom: -76px !important;}
.grid_page_manage .gridpagebetatesting{ top:39px; bottom:auto}
.grid_page_manage{width: 81.5% !important;}
@media only screen and (max-width: 1368px) {
    .grid_page_manage{width: 80% !important;}
}