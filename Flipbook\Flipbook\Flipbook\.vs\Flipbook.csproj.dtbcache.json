{"RootPath": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook", "ProjectFileName": "Flipbook.csproj", "Configuration": "Release|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "App_Start\\BundleConfig.cs"}, {"SourceFile": "App_Start\\FilterConfig.cs"}, {"SourceFile": "App_Start\\IdentityConfig.cs"}, {"SourceFile": "App_Start\\RouteConfig.cs"}, {"SourceFile": "App_Start\\Startup.Auth.cs"}, {"SourceFile": "Controllers\\AccountController.cs"}, {"SourceFile": "Controllers\\AdminController.cs"}, {"SourceFile": "Controllers\\BaseController.cs"}, {"SourceFile": "Controllers\\CompanyController.cs"}, {"SourceFile": "Controllers\\fbController.cs"}, {"SourceFile": "Controllers\\FlipbookController.cs"}, {"SourceFile": "Controllers\\FlipbookLayoutsController.cs"}, {"SourceFile": "Controllers\\HomeController.cs"}, {"SourceFile": "Controllers\\ManageController.cs"}, {"SourceFile": "Controllers\\PublishController.cs"}, {"SourceFile": "Controllers\\ResumeWizardController.cs"}, {"SourceFile": "Controllers\\SwapController.cs"}, {"SourceFile": "Controllers\\TricksOfTradeController.cs"}, {"SourceFile": "Global.asax.cs"}, {"SourceFile": "Models\\AccountViewModels.cs"}, {"SourceFile": "Models\\AddPortfolios_Pagemanager_Result.cs"}, {"SourceFile": "Models\\Address.cs"}, {"SourceFile": "Models\\AddUpdatePortfolioItems_Pagemanager_Result.cs"}, {"SourceFile": "Models\\AppSecurity.Context.cs"}, {"SourceFile": "Models\\AppSecurity.cs"}, {"SourceFile": "Models\\AppSecurity.Designer.cs"}, {"SourceFile": "Models\\AspNetRole.cs"}, {"SourceFile": "Models\\AspNetUser.cs"}, {"SourceFile": "Models\\AspNetUserClaim.cs"}, {"SourceFile": "Models\\AspNetUserLogin.cs"}, {"SourceFile": "Models\\AspNetUserRole.cs"}, {"SourceFile": "Models\\BindJobTemplates_PostJobs_Result.cs"}, {"SourceFile": "Models\\Class.cs"}, {"SourceFile": "Models\\Color.cs"}, {"SourceFile": "Models\\Common.cs"}, {"SourceFile": "Models\\Companies_Addresses.cs"}, {"SourceFile": "Models\\Company.cs"}, {"SourceFile": "Models\\DDL_Categories_Result.cs"}, {"SourceFile": "Models\\DDL_Colleges_Result.cs"}, {"SourceFile": "Models\\DDL_Companies_Result.cs"}, {"SourceFile": "Models\\DDL_Country_Result.cs"}, {"SourceFile": "Models\\DDL_DisplayFormatsByCat_Result.cs"}, {"SourceFile": "Models\\DDL_GiantList_Result.cs"}, {"SourceFile": "Models\\DDL_Months_Result.cs"}, {"SourceFile": "Models\\DDL_TricksOfTheTrade_Result.cs"}, {"SourceFile": "Models\\ErrorLog.cs"}, {"SourceFile": "Models\\Feedback.cs"}, {"SourceFile": "Models\\FlipBook\\ViewModelManageEndPaper.cs"}, {"SourceFile": "Models\\FlipBook\\ViewModelPageGridView.cs"}, {"SourceFile": "Models\\FlipBook\\ViewModelPublish.cs"}, {"SourceFile": "Models\\FlipBook\\ViewModelSaveFeedBack.cs"}, {"SourceFile": "Models\\FlipBook\\ViewModelWorkspace.cs"}, {"SourceFile": "Models\\GetAddLink_Result.cs"}, {"SourceFile": "Models\\GetAllResponseValues_ManageClasses_Result.cs"}, {"SourceFile": "Models\\GETAllVenues_SIC2_Result.cs"}, {"SourceFile": "Models\\GetAutoFillOption_Result.cs"}, {"SourceFile": "Models\\GetClasses_All_GiantList_Result.cs"}, {"SourceFile": "Models\\GetClasses_ByTrade_GiantList_Result.cs"}, {"SourceFile": "Models\\GetClasses_Filtered_GiantList_Result.cs"}, {"SourceFile": "Models\\GetClasses_General_GiantList_Result.cs"}, {"SourceFile": "Models\\GetClasses_General_Inactive_GiantList_Result.cs"}, {"SourceFile": "Models\\GetClasses_InActive_GiantList_Result.cs"}, {"SourceFile": "Models\\GetClasses_NonGeneral_GiantList_Result.cs"}, {"SourceFile": "Models\\GetCommonData_Result.cs"}, {"SourceFile": "Models\\GetCompaniesData_Result.cs"}, {"SourceFile": "Models\\GetCustomDoubleSpreads_AddPage_Result.cs"}, {"SourceFile": "Models\\GetDataForOptionalParameters_PostJobs_Result.cs"}, {"SourceFile": "Models\\GetData_Step2_PostJobs_Result.cs"}, {"SourceFile": "Models\\GetEndPaperByPortfolioID_Result.cs"}, {"SourceFile": "Models\\GetEndPaperByPortPage_Result.cs"}, {"SourceFile": "Models\\GetEndpaperColorsByID_Result.cs"}, {"SourceFile": "Models\\GetEndPaperColors_Result.cs"}, {"SourceFile": "Models\\GetEndPaperListByUsage_Result.cs"}, {"SourceFile": "Models\\GetEndPaper_Result.cs"}, {"SourceFile": "Models\\GetEPFilterColors_Result.cs"}, {"SourceFile": "Models\\GetErrorList_Result.cs"}, {"SourceFile": "Models\\GetFieldsByCatID_Result.cs"}, {"SourceFile": "Models\\GetFilterTemplate_Result.cs"}, {"SourceFile": "Models\\GetGroups_All_ManageNavBarLayouts_Result.cs"}, {"SourceFile": "Models\\GetImagesInUsedByProtfolioID_Result.cs"}, {"SourceFile": "Models\\GetImagesUploadedByUser_Result.cs"}, {"SourceFile": "Models\\GetJobData_Result.cs"}, {"SourceFile": "Models\\GetJobWizardPreviewData_ManageNavBarLayouts_Result.cs"}, {"SourceFile": "Models\\GetLanguages_Result.cs"}, {"SourceFile": "Models\\GetLocationDDL_Result.cs"}, {"SourceFile": "Models\\GetParametersByClassAndTrade_GiantList_Result.cs"}, {"SourceFile": "Models\\GetParametersFromClass_GiantList_Result.cs"}, {"SourceFile": "Models\\GetParametersFromClass_Inactive_GiantList_Result.cs"}, {"SourceFile": "Models\\GetParameters_All_GiantList_Result.cs"}, {"SourceFile": "Models\\GetPortfolioByCustomURL_Result.cs"}, {"SourceFile": "Models\\GetPortfolioItems_Pagemanager_Result.cs"}, {"SourceFile": "Models\\GetPortfolioLayouts_Pagemanager_F111_Result.cs"}, {"SourceFile": "Models\\GetPortfolioLayouts_Pagemanager_F112_Result.cs"}, {"SourceFile": "Models\\GetPortfolioLayouts_Pagemanager_F11_Result.cs"}, {"SourceFile": "Models\\GetPortfolioLayouts_Pagemanager_Result.cs"}, {"SourceFile": "Models\\GetPortFolioPagesForBinding_Result.cs"}, {"SourceFile": "Models\\GetPortFolioPagesForPDF_PageManager_Result.cs"}, {"SourceFile": "Models\\GetPortfoliosDetails_Pagemanager_Result.cs"}, {"SourceFile": "Models\\GetPortPageID_Result.cs"}, {"SourceFile": "Models\\GetProfilePhotoAndDurationByIDForResume_Result.cs"}, {"SourceFile": "Models\\GetProfilePhotoAndDurationByID_Result.cs"}, {"SourceFile": "Models\\GetProtfolioItemsWithImages_Result.cs"}, {"SourceFile": "Models\\GetRepresentativeImagesForEmail_Result.cs"}, {"SourceFile": "Models\\GetResponseValues_ManageClasses_Result.cs"}, {"SourceFile": "Models\\GetResumeAutoFillData_PageManager_Result.cs"}, {"SourceFile": "Models\\GetResumesByUserID_Publish_Result.cs"}, {"SourceFile": "Models\\GetSkillGroupItemByGrpID_Result.cs"}, {"SourceFile": "Models\\GetSkillGroupsByCategory_Result.cs"}, {"SourceFile": "Models\\GetSkillSet_Result.cs"}, {"SourceFile": "Models\\GetStandardColors_Result.cs"}, {"SourceFile": "Models\\GetSubLayout1_Result.cs"}, {"SourceFile": "Models\\GetSubLayout_Result.cs"}, {"SourceFile": "Models\\GetSysEndPaperByID_Admin_Result.cs"}, {"SourceFile": "Models\\GetSysEndPapers_Admin_Result.cs"}, {"SourceFile": "Models\\GetSystemEndPapers_Result.cs"}, {"SourceFile": "Models\\GetSystemImages_Result.cs"}, {"SourceFile": "Models\\GetTables_GiantList_Result.cs"}, {"SourceFile": "Models\\GetTitlePlateByPortPageID_Result.cs"}, {"SourceFile": "Models\\GetTradeDDL_GiantList_Result.cs"}, {"SourceFile": "Models\\GetTradeParameters_JobListingPreview_Result.cs"}, {"SourceFile": "Models\\GetTradesClassesResponseValues_ManageClasses_Result.cs"}, {"SourceFile": "Models\\GetTradesClasses_Active_GiantList_Result.cs"}, {"SourceFile": "Models\\GetTradesClasses_ByName_GiantList_Result.cs"}, {"SourceFile": "Models\\GetTradesClasses_New_Result.cs"}, {"SourceFile": "Models\\GetTradesGroupsByStatus_ManageNavBarLayouts_Result.cs"}, {"SourceFile": "Models\\GetTradesGroupsClassesByGroupID_ManageNavBarLayouts_Result.cs"}, {"SourceFile": "Models\\GetTradesGroupsClasses_ManageNavBarLayouts_Result.cs"}, {"SourceFile": "Models\\GetTradesGroups_Active_ManageNavBarLayouts_Result.cs"}, {"SourceFile": "Models\\GetTradesGroups_InActive_ManageNavBarLayouts_Result.cs"}, {"SourceFile": "Models\\GetTradesGroups_ManageNavBarLayouts_Result.cs"}, {"SourceFile": "Models\\GetTradesSynonyms_GiantList_Result.cs"}, {"SourceFile": "Models\\GetTrades_GiantList_Result.cs"}, {"SourceFile": "Models\\GetUIMessages_GiantList_Result.cs"}, {"SourceFile": "Models\\GetUserColorList_Result.cs"}, {"SourceFile": "Models\\GetUserDetalsByPortfolioID_Result.cs"}, {"SourceFile": "Models\\GetUserImageInUseData_Result.cs"}, {"SourceFile": "Models\\Get_PortfolioItems_Pagemanager_F11_Result.cs"}, {"SourceFile": "Models\\Group.cs"}, {"SourceFile": "Models\\IdentityExtensions.cs"}, {"SourceFile": "Models\\IdentityModels.cs"}, {"SourceFile": "Models\\ItemType.cs"}, {"SourceFile": "Models\\Job.cs"}, {"SourceFile": "Models\\JobCriteriaGroup.cs"}, {"SourceFile": "Models\\Jobs_Parameters.cs"}, {"SourceFile": "Models\\Jobs_Templates.cs"}, {"SourceFile": "Models\\ManageViewModels.cs"}, {"SourceFile": "Models\\Month.cs"}, {"SourceFile": "Models\\Parameter.cs"}, {"SourceFile": "Models\\Portfolio.cs"}, {"SourceFile": "Models\\PortfolioItem.cs"}, {"SourceFile": "Models\\PortfolioLayout.cs"}, {"SourceFile": "Models\\PortfolioLayoutItem.cs"}, {"SourceFile": "Models\\PortfolioPage.cs"}, {"SourceFile": "Models\\PortfolioPages_SubLayouts.cs"}, {"SourceFile": "Models\\ResponseGroup.cs"}, {"SourceFile": "Models\\ResponseGroups_Values.cs"}, {"SourceFile": "Models\\ResponseValue.cs"}, {"SourceFile": "Models\\ResumeWizard\\GeneralModel.cs"}, {"SourceFile": "Models\\ResumeWizard\\GetResumeData.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModeGenerateTN.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelAddImages.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelAffAndAcc.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelAthletics.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelCategoryList.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelChooseTemplate.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelCommunityService.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelEducation.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelFilterTemplate.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelGlobalFormating.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelMilitaryService.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelInterest.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelOrderOfAchievements.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelPatents.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelPersonalInfo.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelPersonalityTraits.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelProfessionalAwards.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelPublications.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelObjSkillAndSummary.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelPublish.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelResearch.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelSkillSet.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelSpellCheckMaster.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelUserAddedCategory.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelWorkAuthorizations.cs"}, {"SourceFile": "Models\\ResumeWizard\\ViewModelWorkExperience.cs"}, {"SourceFile": "Models\\RW_Categories.cs"}, {"SourceFile": "Models\\RW_Categories_ItemTypes.cs"}, {"SourceFile": "Models\\RW_Data.cs"}, {"SourceFile": "Models\\RW_GetActiveTrade_Result.cs"}, {"SourceFile": "Models\\RW_GetInstitutionList_Result.cs"}, {"SourceFile": "Models\\RW_GetParameterNameByCatID_Result.cs"}, {"SourceFile": "Models\\RW_GetResumDetailListByUser_Result.cs"}, {"SourceFile": "Models\\RW_GetResumeDetail_Result.cs"}, {"SourceFile": "Models\\RW_GetResumeDetal_Result.cs"}, {"SourceFile": "Models\\RW_GetTemplateByTradID_Result.cs"}, {"SourceFile": "Models\\RW_GetTemplateDetailByID_Result.cs"}, {"SourceFile": "Models\\RW_GetUserTradeWorkProfile_Result.cs"}, {"SourceFile": "Models\\RW_PredictiveSearch_Result.cs"}, {"SourceFile": "Models\\RW_Resumes.cs"}, {"SourceFile": "Models\\RW_Resumes_FlipBooks.cs"}, {"SourceFile": "Models\\RW_Schema.cs"}, {"SourceFile": "Models\\RW_Schema_Classes.cs"}, {"SourceFile": "Models\\RW_TemplateByTrade.cs"}, {"SourceFile": "Models\\RW_Templates.cs"}, {"SourceFile": "Models\\SIC1.cs"}, {"SourceFile": "Models\\storedata.cs"}, {"SourceFile": "Models\\SwapTemplate\\ViewModelAutoFill.cs"}, {"SourceFile": "Models\\SwapTemplate\\ViewModelSwapStatement.cs"}, {"SourceFile": "Models\\SwapTemplate\\ViewModelSwapTemplate.cs"}, {"SourceFile": "Models\\SysEndPaperByColor.cs"}, {"SourceFile": "Models\\Trade.cs"}, {"SourceFile": "Models\\TradesClass.cs"}, {"SourceFile": "Models\\TradesClassesResponseGroup.cs"}, {"SourceFile": "Models\\TradesGroups.cs"}, {"SourceFile": "Models\\TradesGroupsClass.cs"}, {"SourceFile": "Models\\TradesParameter.cs"}, {"SourceFile": "Models\\TradesSynonym.cs"}, {"SourceFile": "Models\\TradeWorksTEST.Context.cs"}, {"SourceFile": "Models\\TradeWorksTEST.cs"}, {"SourceFile": "Models\\TradeWorksTEST.Designer.cs"}, {"SourceFile": "Models\\UIMessage.cs"}, {"SourceFile": "Models\\UpdateOrderInPortPages_Result.cs"}, {"SourceFile": "Models\\UserSelectedColor.cs"}, {"SourceFile": "Models\\Users_Companies.cs"}, {"SourceFile": "Models\\UserUploadedImageInUsed.cs"}, {"SourceFile": "Models\\User_Dictionary.cs"}, {"SourceFile": "Models\\ViewModelCommonDetails.cs"}, {"SourceFile": "Models\\ViewModelEmailSend.cs"}, {"SourceFile": "Models\\ViewModelImages.cs"}, {"SourceFile": "Models\\ViewModelSpellCheck.cs"}, {"SourceFile": "Models\\Year.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Startup.cs"}, {"SourceFile": "Utilities\\Helper.cs"}, {"SourceFile": "Utilities\\IO.cs"}, {"SourceFile": "Utilities\\RwCommon.cs"}, {"SourceFile": "Utilities\\SessionInfo.cs"}, {"SourceFile": "obj\\Release\\.NETFramework,Version=v4.5.2.AssemblyAttributes.cs"}], "References": [{"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Antlr3.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\ClientDependency.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\ClientDependency.Core.Mvc.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\DocumentFormat.OpenXml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\EntityFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\EntityFramework.SqlServer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\FlipBook.Helper\\bin\\Release\\FlipBook.Helper.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\FlipBook.Helper\\bin\\Release\\FlipBook.Helper.dll"}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\HtmlAgilityPack.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\ImageProcessor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\ImageProcessor.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\MariGold.HtmlParser.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\MariGold.OpenXHTML.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Microsoft.AspNet.Identity.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Microsoft.AspNet.Identity.EntityFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Microsoft.AspNet.Identity.Owin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\Microsoft.CSharp\\v4.0_4.0.0.0__b03f5f7f11d50a3a\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Microsoft.IO.RecyclableMemoryStream.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Microsoft.Owin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Microsoft.Owin.Host.SystemWeb.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Microsoft.Owin.Security.Cookies.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Microsoft.Owin.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Microsoft.Owin.Security.Facebook.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Microsoft.Owin.Security.Google.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Microsoft.Owin.Security.MicrosoftAccount.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Microsoft.Owin.Security.OAuth.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Microsoft.Owin.Security.Twitter.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Microsoft.Web.Infrastructure.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.NET\\Framework\\v4.0.30319\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Owin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\packages\\Select.HtmlToPdf.17.1.0\\lib\\net40\\Select.HtmlToPdf.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\SSO\\bin\\Release\\SSO.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\SSO\\bin\\Release\\SSO.dll"}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.ComponentModel.DataAnnotations\\v4.0_4.0.0.0__31bf3856ad364e35\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Configuration\\v4.0_4.0.0.0__b03f5f7f11d50a3a\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Core\\v4.0_4.0.0.0__b77a5c561934e089\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Data.DataSetExtensions\\v4.0_4.0.0.0__b77a5c561934e089\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_64\\System.Data\\v4.0_4.0.0.0__b77a5c561934e089\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Data.Entity\\v4.0_4.0.0.0__b77a5c561934e089\\System.Data.Entity.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System\\v4.0_4.0.0.0__b77a5c561934e089\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Drawing\\v4.0_4.0.0.0__b03f5f7f11d50a3a\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_64\\System.EnterpriseServices\\v4.0_4.0.0.0__b03f5f7f11d50a3a\\System.EnterpriseServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Net.Http\\v4.0_4.0.0.0__b03f5f7f11d50a3a\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\System.Net.Http.Formatting.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Net.Http.WebRequest\\v4.0_4.0.0.0__b03f5f7f11d50a3a\\System.Net.Http.WebRequest.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Runtime.Serialization\\v4.0_4.0.0.0__b77a5c561934e089\\System.Runtime.Serialization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Security\\v4.0_4.0.0.0__b03f5f7f11d50a3a\\System.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Web.Abstractions\\v4.0_4.0.0.0__31bf3856ad364e35\\System.Web.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Web.ApplicationServices\\v4.0_4.0.0.0__31bf3856ad364e35\\System.Web.ApplicationServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_64\\System.Web\\v4.0_4.0.0.0__b03f5f7f11d50a3a\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Web.DynamicData\\v4.0_4.0.0.0__31bf3856ad364e35\\System.Web.DynamicData.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Web.Entity\\v4.0_4.0.0.0__b77a5c561934e089\\System.Web.Entity.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Web.Extensions\\v4.0_4.0.0.0__31bf3856ad364e35\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\System.Web.Helpers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\System.Web.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\System.Web.Mvc.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\System.Web.Optimization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\System.Web.Razor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Web.Routing\\v4.0_4.0.0.0__31bf3856ad364e35\\System.Web.Routing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Web.Services\\v4.0_4.0.0.0__b03f5f7f11d50a3a\\System.Web.Services.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\System.Web.WebPages.Deployment.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\System.Web.WebPages.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\System.Web.WebPages.Razor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Xml\\v4.0_4.0.0.0__b77a5c561934e089\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Xml.Linq\\v4.0_4.0.0.0__b77a5c561934e089\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\WebGrease.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\WINDOWS\\Microsoft.Net\\assembly\\GAC_MSIL\\WindowsBase\\v4.0_4.0.0.0__31bf3856ad364e35\\WindowsBase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Common.Spelling\\bin\\Release\\Yo.Net.Spelling.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Common.Spelling\\bin\\Release\\Yo.Net.Spelling.dll"}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\projects\\tradeworks\\franklinreports\\Flipbook\\Flipbook\\Flipbook\\Flipbook\\bin\\Flipbook.dll", "OutputItemRelativePath": "Flipbook.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}