﻿CKEDITOR.plugins.add('ckecp2', {

    icons: 'ckecp2',
    init: function (editor) {



        var config = editor.config;

        editor.addCommand('ckecp2', new CKEDITOR.dialogCommand('colorpickerDialog'));

        CKEDITOR.plugins.addExternal('bootstrap', '/Scripts/bootstrap.min.js', '');

        //CKEDITOR.replace('editor1', {
        //    contentsCss: [CKEDITOR.basePath + 'contents.css', '/path/to/custom.css'],
        //    allowedContent: true,
        //});


        //CKEDITOR.dialog.add('colorpickerDialog', this.path + 'dialogs/ckecp2.js');
        CKEDITOR.dialog.add('colorpickerDialog', function (editor) {
            return {
                title: 'Color Picker cpt',
                minWidth: 700,
                minHeight: 300,
                contents: [{
                    id: 'tab1',
                    label: 'label1',
                    elements:
                    [{
                        type: 'html',
                        html: '<textarea id="codeEditor"></textarea>',
                        id: 'codeEditor',
                        label: 'CodeEditor',
                    }]
                }]
            };
        });


        addButton('ckeCp2BGColor', 'back', "CkeCp2 bgColorTitle", 20);

        function addButton(name, type, title, order) {

            var style = new CKEDITOR.style(config['colorButton_' + type + 'Style']),
				panelBlock;

            editor.ui.add(name, CKEDITOR.UI_PANELBUTTON, {
                label: title,
                title: title,
                modes: { wysiwyg: 1 },
                editorFocus: 0,
                toolbar: 'colors,' + order,
                allowedContent: style,
                requiredContent: style,
                onChange: function () {
                    // this = CKEDITOR.ui.dialog.select
                    //alert( 'Current value: ');
                },
                panel: {
                    css: [editor.config.contentsCss
                        , CKEDITOR.getUrl(CKEDITOR.skin.getPath('editor') + 'editor.css')
                        , CKEDITOR.getUrl('/Content/bootstrap.min.css')
                        //, CKEDITOR.getUrl('/Scripts/bootstrap.min.js')
                        , CKEDITOR.getUrl(CKEDITOR.skin.getPath('CkeCp2') + 'CkeCp2.css')
                        //,CKEDITOR.plugins.addExternal('bootstrap', '/Scripts/bootstrap.min.js', '')
                    ]
                    //,addCommand: function () { alert('command'); }
                    //, addFunction: function () { alert('funaeion');}
                    , className: "cp2-cke-panel"
                    , attributes: { role: 'listbox', 'aria-label': '' }
                },

                onBlock: function (panel, block) {


                    panelBlock = block;

                    block.autoSize = true;
                    block.element.addClass('cke_colorblock');
                    block.element.setHtml(renderColors(panel, "back"));

                    //GeneratColorBoxInCkeCp2ToolBar();

                    

                    CkeCp2_BindOnLoad();

                    block.element.getDocument().getBody().setStyle('overflow', 'hidden');

                    CKEDITOR.ui.fire('ready', this);
                },



                refresh: function () {                    
                    if (!editor.activeFilter.check(style))
                        this.setState(CKEDITOR.TRISTATE_DISABLED);
                }

            });

        }

        function renderColors(panel, type) {

            var output = [];
            var clickFn = CKEDITOR.tools.addFunction(function applyColorStyle(color, type) {

                editor.focus();
                editor.fire('saveSnapshot');

                if (color == '?') {
                    editor.getColorFromDialog(function (color) {
                        if (color) {
                            return setColor(color);
                        }
                    });
                } else {
                    return setColor(color);
                }

                function setColor(color) {
                    setCkecp2Color(color);
                    type = 'back';
                    var colorStyle = config['colorButton_foreStyle'];
                    editor.removeStyle(new CKEDITOR.style(colorStyle, { color: 'inherit' }));
                    editor.focus();
                    if (color) {
                        editor.applyStyle(new CKEDITOR.style(colorStyle, { color: color }));
                    }
                    editor.fire('saveSnapshot');
                }

            });

            output.push('<div class="innderbox divcp2StandardColr cp2-cke-Body" data-clickFnId="' + clickFn + '">');
            var HTMLstring = ' <div class="standardcolor">'; // Div 11 S
            HTMLstring += ' <div data-content="Our curated palette" class="titlecolor popupoverright" >Standard Colors <span class="spanStandColName" style="color: rgb(0, 0, 0);">Black</span></div><div class="clear"></div>';

            var colorstr = "";
            for (var i = 0; i < ColorLst.length; i++) {
                var colorName = ColorLst[i].ColorName;

                if (ColorLst[i].ColorCode == '#ffffff') {
                    colorstr += '<span class="color" onmouseout="parent.displayColorName(this,true);" onmouseover="parent.displayColorName(this);" onclick="CKEDITOR.tools.callFunction(' + clickFn + ',\'' + ColorLst[i].ColorCode + '\'); return false;" style="border: solid 1px #979797;background:' + ColorLst[i].ColorCode + '"  data-color=' + ColorLst[i].ColorCode + ' data-colorName =' + ColorLst[i].ColorName + ' ></span>';
                } else {
                    colorstr += '<span class="color" onmouseout="parent.displayColorName(this,true);" onmouseover="parent.displayColorName(this);" onclick="CKEDITOR.tools.callFunction(' + clickFn + ',\'' + ColorLst[i].ColorCode + '\'); return false;" style="border: solid 1px ' + ColorLst[i].ColorCode + ';background:' + ColorLst[i].ColorCode + '"  data-color=' + ColorLst[i].ColorCode + ' data-colorName =' + ColorLst[i].ColorName + ' ></span>';
                }
            }

            output.push(colorstr);
            output.push('<span class="cp2colortran"><img src="/images/transparent.png" style="width:16px"></span>');
            //ST-1860,BV 
            HTMLstring += ' </div>';// Div 11 E

            HTMLstring += ' <div style="clear:both"></div>';
            HTMLstring += ' <div class="mypalette">'; // Div 12 S

            HTMLstring += ' <div class="titlecolor popupoverright" data-content="Delete colors in  Itten Star">My Palette</div>'; //Div 12.1 S E

            //ST - 1876
            var Cp2HTMLmColor = GeneratColorBoxInCp2ToolBar_cke(clickFn);
            HTMLstring += Cp2HTMLmColor;

            
            


            HTMLstring += ' </div>';// Div 12 E
            //Div 13 S
            HTMLstring += ' <div class="morecolor"> <div class="tooltiptext" style="display:none"> More color pickers tools</div><div class="titlecolor popupoverright" data-content="More color pickers tools">More Colors</div>';
            HTMLstring += ' <ul class="ulCp2MoreColors" data-ckecp2id="' + clickFn + '"> ';
            HTMLstring += ' <li><spn class="cp2OpenCpPoupop"  onclick="parent.onColorPickerCkeCp2Click(this,\'' + editor.name + '\');" data-toggle="modal" data-target="#myModal" data-backdrop="static" data-keyboard="false" data-cpTab="ITTEN"><img src="/images/ImageEditor/itten-art-small.png"></spn></li>';
            HTMLstring += ' <li><spn class="cp2OpenCpPoupop"  onclick="parent.onColorPickerCkeCp2Click(this,\'' + editor.name + '\');" data-toggle="modal" data-target="#myModal" data-backdrop="static" data-keyboard="false" data-cpTab="CIRCLE"><img src="/images/ImageEditor/ColorWheel-small.png"></spn></li>';
            HTMLstring += ' <li><span class="cp2OpenCpPoupop" onclick="parent.onColorPickerCkeCp2Click(this,\'' + editor.name + '\');" data-toggle="modal" data-target="#myModal" data-backdrop="static" data-keyboard="false" data-cpTab="PASTELBOX"><img src="/images/ImageEditor/turquoise-small.png"></span></li>';
            HTMLstring += ' <li><span class="cp2OpenCpPoupop" onclick="parent.onColorPickerCkeCp2Click(this,\'' + editor.name + '\');" data-toggle="modal" data-target="#myModal" data-backdrop="static" data-keyboard="false" data-cpTab="ARTWORK"><img src="/images/ImageEditor/square-small.png"></span></li>';
            HTMLstring += '</ul> </div>'; //Div 13 E
            output.push(HTMLstring);

            //setTimeout(function () {
            //    GeneratColorBoxInCkeCp2ToolBar();
            //},100);


            return output.join('');
        }

        //ST-1876      
        editor.on("panelShow", function () {
            ReGenerateCkEditorColrs();
            CkeCp2_BindOnPanelShow(editor);
            
            //console.log('Panel Open. After 222')
        }); 
    } 
});


//CKEDITOR.plugins.ckecp2 = {
//    CKECP2_BindSlider: function () {

//        //$('#divCp2ColorBoxSlider').carousel({
//        //    pause: true,
//        //    interval: 400000,
//        //});

//        //setTimeout(function () {
//        $('.popupoverright').popover({
//            trigger: "hover",
//            placement: 'left'
//        });
//        //},2000);
//    }
//}
