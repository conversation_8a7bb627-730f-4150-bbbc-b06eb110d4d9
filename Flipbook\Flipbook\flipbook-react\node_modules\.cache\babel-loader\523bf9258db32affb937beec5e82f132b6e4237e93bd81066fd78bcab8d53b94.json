{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6;import React,{useState}from'react';import styled from'styled-components';import Header from'./Header';import Sidebar from'./Sidebar';import FlipbookGrid from'./FlipbookGrid';import CreateFlipbookModal from'./CreateFlipbookModal';import{useFlipbooks}from'../../hooks/useFlipbooks';import{useAuth}from'../../hooks/useAuth';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LandingPageContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  background-color: #f5f5f5;\\n\"])));const MainContent=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex: 1;\\n  overflow: hidden;\\n\"])));const ContentArea=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  flex: 1;\\n  padding: 20px;\\n  overflow-y: auto;\\n  background-color: #ffffff;\\n\"])));const Section=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  margin-bottom: 40px;\\n\"])));const SectionTitle=styled.h2(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  font-size: 18px;\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 20px;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n\"])));const CertificationBadge=styled.div(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  position: fixed;\\n  top: 80px;\\n  right: 20px;\\n  width: 100px;\\n  height: 100px;\\n  background-image: url('/images/certification-badge.png');\\n  background-size: contain;\\n  background-repeat: no-repeat;\\n  z-index: 10;\\n\"])));const LandingPage=()=>{const[isCreateModalOpen,setIsCreateModalOpen]=useState(false);// Use real hooks that match .NET functionality\nconst{user,isAuthenticated}=useAuth();const{userFlipbooks,inspirationFlipbooks,loading,error,createFlipbook,copyInspirationFlipbook}=useFlipbooks();// Convert data to match component interface\nconst myFlipbooks=userFlipbooks.map(fb=>({id:fb.PortfolioID,title:fb.PortfolioTitle||'Untitled',thumbnail:fb.ThumbnailPath||'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop',isInspiration:false}));const inspirationFlipbooksFormatted=inspirationFlipbooks.map(fb=>({id:fb.PortfolioID,title:fb.FBTitle,thumbnail:fb.TnImageSrc||'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop',isInspiration:true}));const handleCreateNew=()=>{setIsCreateModalOpen(true);};const handleCloseModal=()=>{setIsCreateModalOpen(false);};const handleCreateFlipbook=async title=>{try{const portfolioId=await createFlipbook({title});if(portfolioId){console.log('Flipbook created successfully with ID:',portfolioId);setIsCreateModalOpen(false);// Navigate to flipbook editor if needed\n// window.location.href = `/Flipbook/Pagemanager?PortFolioID=${portfolioId}`;\n}}catch(err){console.error('Failed to create flipbook:',err);}};const handleCopyInspiration=async flipbook=>{try{const portfolioId=await copyInspirationFlipbook({portfolioId:flipbook.id,title:\"Copy of \".concat(flipbook.title)});if(portfolioId){console.log('Inspiration flipbook copied successfully with ID:',portfolioId);// Navigate to flipbook editor if needed\n// window.location.href = `/Flipbook/Pagemanager?PortFolioID=${portfolioId}`;\n}}catch(err){console.error('Failed to copy inspiration flipbook:',err);}};return/*#__PURE__*/_jsxs(LandingPageContainer,{children:[/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsxs(MainContent,{children:[/*#__PURE__*/_jsx(Sidebar,{}),/*#__PURE__*/_jsxs(ContentArea,{children:[/*#__PURE__*/_jsxs(Section,{children:[/*#__PURE__*/_jsx(SectionTitle,{children:\"My Flipbooks\"}),/*#__PURE__*/_jsx(FlipbookGrid,{flipbooks:myFlipbooks,showCreateNew:true,onCreateNew:handleCreateNew,onFlipbookClick:flipbook=>console.log('Open flipbook:',flipbook),onFlipbookEdit:flipbook=>console.log('Edit flipbook:',flipbook)})]}),/*#__PURE__*/_jsxs(Section,{children:[/*#__PURE__*/_jsx(SectionTitle,{children:\"Inspiration\"}),/*#__PURE__*/_jsx(FlipbookGrid,{flipbooks:inspirationFlipbooksFormatted,onFlipbookClick:flipbook=>console.log('Preview flipbook:',flipbook),onFlipbookCopy:handleCopyInspiration})]})]})]}),/*#__PURE__*/_jsx(CertificationBadge,{}),isCreateModalOpen&&/*#__PURE__*/_jsx(CreateFlipbookModal,{onClose:handleCloseModal,onCreate:handleCreateFlipbook})]});};export default LandingPage;", "map": {"version": 3, "names": ["React", "useState", "styled", "Header", "Sidebar", "FlipbookGrid", "CreateFlipbookModal", "useFlipbooks", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "LandingPageContainer", "div", "_templateObject", "_taggedTemplateLiteral", "MainContent", "_templateObject2", "ContentArea", "_templateObject3", "Section", "_templateObject4", "SectionTitle", "h2", "_templateObject5", "CertificationBadge", "_templateObject6", "LandingPage", "isCreateModalOpen", "setIsCreateModalOpen", "user", "isAuthenticated", "userFlipbooks", "inspirationFlipbooks", "loading", "error", "createFlipbook", "copyInspirationFlipbook", "myFlipbooks", "map", "fb", "id", "PortfolioID", "title", "PortfolioTitle", "thumbnail", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isInspiration", "inspirationFlipbooksFormatted", "FBTitle", "TnImageSrc", "handleCreateNew", "handleCloseModal", "handleCreateFlipbook", "portfolioId", "console", "log", "err", "handleCopyInspiration", "flipbook", "concat", "children", "flipbooks", "showCreateNew", "onCreateNew", "onFlipbookClick", "onFlipbookEdit", "onFlipbookCopy", "onClose", "onCreate"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/LandingPage/LandingPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport Header from './Header';\nimport Sidebar from './Sidebar';\nimport FlipbookGrid from './FlipbookGrid';\nimport CreateFlipbookModal from './CreateFlipbookModal';\nimport { useFlipbooks } from '../../hooks/useFlipbooks';\nimport { useAuth } from '../../hooks/useAuth';\n\nconst LandingPageContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #f5f5f5;\n`;\n\nconst MainContent = styled.div`\n  display: flex;\n  flex: 1;\n  overflow: hidden;\n`;\n\nconst ContentArea = styled.div`\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n  background-color: #ffffff;\n`;\n\nconst Section = styled.div`\n  margin-bottom: 40px;\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: 18px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 20px;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n`;\n\n\n\nconst CertificationBadge = styled.div`\n  position: fixed;\n  top: 80px;\n  right: 20px;\n  width: 100px;\n  height: 100px;\n  background-image: url('/images/certification-badge.png');\n  background-size: contain;\n  background-repeat: no-repeat;\n  z-index: 10;\n`;\n\ninterface Flipbook {\n  id: number;\n  title: string;\n  thumbnail?: string;\n  isInspiration?: boolean;\n}\n\nconst LandingPage: React.FC = () => {\n  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);\n\n  // Use real hooks that match .NET functionality\n  const { user, isAuthenticated } = useAuth();\n  const {\n    userFlipbooks,\n    inspirationFlipbooks,\n    loading,\n    error,\n    createFlipbook,\n    copyInspirationFlipbook\n  } = useFlipbooks();\n\n  // Convert data to match component interface\n  const myFlipbooks: Flipbook[] = userFlipbooks.map(fb => ({\n    id: fb.PortfolioID,\n    title: fb.PortfolioTitle || 'Untitled',\n    thumbnail: fb.ThumbnailPath || 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop',\n    isInspiration: false\n  }));\n\n  const inspirationFlipbooksFormatted: Flipbook[] = inspirationFlipbooks.map(fb => ({\n    id: fb.PortfolioID,\n    title: fb.FBTitle,\n    thumbnail: fb.TnImageSrc || 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop',\n    isInspiration: true\n  }));\n\n  const handleCreateNew = () => {\n    setIsCreateModalOpen(true);\n  };\n\n  const handleCloseModal = () => {\n    setIsCreateModalOpen(false);\n  };\n\n  const handleCreateFlipbook = async (title: string) => {\n    try {\n      const portfolioId = await createFlipbook({ title });\n      if (portfolioId) {\n        console.log('Flipbook created successfully with ID:', portfolioId);\n        setIsCreateModalOpen(false);\n        // Navigate to flipbook editor if needed\n        // window.location.href = `/Flipbook/Pagemanager?PortFolioID=${portfolioId}`;\n      }\n    } catch (err) {\n      console.error('Failed to create flipbook:', err);\n    }\n  };\n\n  const handleCopyInspiration = async (flipbook: Flipbook) => {\n    try {\n      const portfolioId = await copyInspirationFlipbook({\n        portfolioId: flipbook.id,\n        title: `Copy of ${flipbook.title}`\n      });\n      if (portfolioId) {\n        console.log('Inspiration flipbook copied successfully with ID:', portfolioId);\n        // Navigate to flipbook editor if needed\n        // window.location.href = `/Flipbook/Pagemanager?PortFolioID=${portfolioId}`;\n      }\n    } catch (err) {\n      console.error('Failed to copy inspiration flipbook:', err);\n    }\n  };\n\n  return (\n    <LandingPageContainer>\n      <Header />\n      <MainContent>\n        <Sidebar />\n        <ContentArea>\n          <Section>\n            <SectionTitle>My Flipbooks</SectionTitle>\n            <FlipbookGrid\n              flipbooks={myFlipbooks}\n              showCreateNew={true}\n              onCreateNew={handleCreateNew}\n              onFlipbookClick={(flipbook) => console.log('Open flipbook:', flipbook)}\n              onFlipbookEdit={(flipbook) => console.log('Edit flipbook:', flipbook)}\n            />\n          </Section>\n\n          <Section>\n            <SectionTitle>Inspiration</SectionTitle>\n            <FlipbookGrid\n              flipbooks={inspirationFlipbooksFormatted}\n              onFlipbookClick={(flipbook) => console.log('Preview flipbook:', flipbook)}\n              onFlipbookCopy={handleCopyInspiration}\n            />\n          </Section>\n        </ContentArea>\n      </MainContent>\n      \n      <CertificationBadge />\n      \n      {isCreateModalOpen && (\n        <CreateFlipbookModal\n          onClose={handleCloseModal}\n          onCreate={handleCreateFlipbook}\n        />\n      )}\n    </LandingPageContainer>\n  );\n};\n\nexport default LandingPage;\n"], "mappings": "sQAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,MAAM,KAAM,UAAU,CAC7B,MAAO,CAAAC,OAAO,KAAM,WAAW,CAC/B,MAAO,CAAAC,YAAY,KAAM,gBAAgB,CACzC,MAAO,CAAAC,mBAAmB,KAAM,uBAAuB,CACvD,OAASC,YAAY,KAAQ,0BAA0B,CACvD,OAASC,OAAO,KAAQ,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9C,KAAM,CAAAC,oBAAoB,CAAGX,MAAM,CAACY,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,uGAKtC,CAED,KAAM,CAAAC,WAAW,CAAGf,MAAM,CAACY,GAAG,CAAAI,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,6DAI7B,CAED,KAAM,CAAAG,WAAW,CAAGjB,MAAM,CAACY,GAAG,CAAAM,gBAAA,GAAAA,gBAAA,CAAAJ,sBAAA,2FAK7B,CAED,KAAM,CAAAK,OAAO,CAAGnB,MAAM,CAACY,GAAG,CAAAQ,gBAAA,GAAAA,gBAAA,CAAAN,sBAAA,kCAEzB,CAED,KAAM,CAAAO,YAAY,CAAGrB,MAAM,CAACsB,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAT,sBAAA,iJAO7B,CAID,KAAM,CAAAU,kBAAkB,CAAGxB,MAAM,CAACY,GAAG,CAAAa,gBAAA,GAAAA,gBAAA,CAAAX,sBAAA,0OAUpC,CASD,KAAM,CAAAY,WAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG7B,QAAQ,CAAC,KAAK,CAAC,CAEjE;AACA,KAAM,CAAE8B,IAAI,CAAEC,eAAgB,CAAC,CAAGxB,OAAO,CAAC,CAAC,CAC3C,KAAM,CACJyB,aAAa,CACbC,oBAAoB,CACpBC,OAAO,CACPC,KAAK,CACLC,cAAc,CACdC,uBACF,CAAC,CAAG/B,YAAY,CAAC,CAAC,CAElB;AACA,KAAM,CAAAgC,WAAuB,CAAGN,aAAa,CAACO,GAAG,CAACC,EAAE,GAAK,CACvDC,EAAE,CAAED,EAAE,CAACE,WAAW,CAClBC,KAAK,CAAEH,EAAE,CAACI,cAAc,EAAI,UAAU,CACtCC,SAAS,CAAEL,EAAE,CAACM,aAAa,EAAI,mFAAmF,CAClHC,aAAa,CAAE,KACjB,CAAC,CAAC,CAAC,CAEH,KAAM,CAAAC,6BAAyC,CAAGf,oBAAoB,CAACM,GAAG,CAACC,EAAE,GAAK,CAChFC,EAAE,CAAED,EAAE,CAACE,WAAW,CAClBC,KAAK,CAAEH,EAAE,CAACS,OAAO,CACjBJ,SAAS,CAAEL,EAAE,CAACU,UAAU,EAAI,mFAAmF,CAC/GH,aAAa,CAAE,IACjB,CAAC,CAAC,CAAC,CAEH,KAAM,CAAAI,eAAe,CAAGA,CAAA,GAAM,CAC5BtB,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAuB,gBAAgB,CAAGA,CAAA,GAAM,CAC7BvB,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAwB,oBAAoB,CAAG,KAAO,CAAAV,KAAa,EAAK,CACpD,GAAI,CACF,KAAM,CAAAW,WAAW,CAAG,KAAM,CAAAlB,cAAc,CAAC,CAAEO,KAAM,CAAC,CAAC,CACnD,GAAIW,WAAW,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAEF,WAAW,CAAC,CAClEzB,oBAAoB,CAAC,KAAK,CAAC,CAC3B;AACA;AACF,CACF,CAAE,MAAO4B,GAAG,CAAE,CACZF,OAAO,CAACpB,KAAK,CAAC,4BAA4B,CAAEsB,GAAG,CAAC,CAClD,CACF,CAAC,CAED,KAAM,CAAAC,qBAAqB,CAAG,KAAO,CAAAC,QAAkB,EAAK,CAC1D,GAAI,CACF,KAAM,CAAAL,WAAW,CAAG,KAAM,CAAAjB,uBAAuB,CAAC,CAChDiB,WAAW,CAAEK,QAAQ,CAAClB,EAAE,CACxBE,KAAK,YAAAiB,MAAA,CAAaD,QAAQ,CAAChB,KAAK,CAClC,CAAC,CAAC,CACF,GAAIW,WAAW,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAEF,WAAW,CAAC,CAC7E;AACA;AACF,CACF,CAAE,MAAOG,GAAG,CAAE,CACZF,OAAO,CAACpB,KAAK,CAAC,sCAAsC,CAAEsB,GAAG,CAAC,CAC5D,CACF,CAAC,CAED,mBACE9C,KAAA,CAACC,oBAAoB,EAAAiD,QAAA,eACnBpD,IAAA,CAACP,MAAM,GAAE,CAAC,cACVS,KAAA,CAACK,WAAW,EAAA6C,QAAA,eACVpD,IAAA,CAACN,OAAO,GAAE,CAAC,cACXQ,KAAA,CAACO,WAAW,EAAA2C,QAAA,eACVlD,KAAA,CAACS,OAAO,EAAAyC,QAAA,eACNpD,IAAA,CAACa,YAAY,EAAAuC,QAAA,CAAC,cAAY,CAAc,CAAC,cACzCpD,IAAA,CAACL,YAAY,EACX0D,SAAS,CAAExB,WAAY,CACvByB,aAAa,CAAE,IAAK,CACpBC,WAAW,CAAEb,eAAgB,CAC7Bc,eAAe,CAAGN,QAAQ,EAAKJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAEG,QAAQ,CAAE,CACvEO,cAAc,CAAGP,QAAQ,EAAKJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAEG,QAAQ,CAAE,CACvE,CAAC,EACK,CAAC,cAEVhD,KAAA,CAACS,OAAO,EAAAyC,QAAA,eACNpD,IAAA,CAACa,YAAY,EAAAuC,QAAA,CAAC,aAAW,CAAc,CAAC,cACxCpD,IAAA,CAACL,YAAY,EACX0D,SAAS,CAAEd,6BAA8B,CACzCiB,eAAe,CAAGN,QAAQ,EAAKJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAEG,QAAQ,CAAE,CAC1EQ,cAAc,CAAET,qBAAsB,CACvC,CAAC,EACK,CAAC,EACC,CAAC,EACH,CAAC,cAEdjD,IAAA,CAACgB,kBAAkB,GAAE,CAAC,CAErBG,iBAAiB,eAChBnB,IAAA,CAACJ,mBAAmB,EAClB+D,OAAO,CAAEhB,gBAAiB,CAC1BiB,QAAQ,CAAEhB,oBAAqB,CAChC,CACF,EACmB,CAAC,CAE3B,CAAC,CAED,cAAe,CAAA1B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}