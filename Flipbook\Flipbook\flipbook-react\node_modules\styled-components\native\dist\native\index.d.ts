import React from 'react';
import { Styled } from '../constructors/constructWithOptions';
import css from '../constructors/css';
import withTheme from '../hoc/withTheme';
import ThemeProvider, { ThemeConsumer, ThemeContext, useTheme } from '../models/ThemeProvider';
import { NativeTarget } from '../types';
import isStyledComponent from '../utils/isStyledComponent';
declare const styled: (<Target extends NativeTarget>(tag: Target) => Styled<"native", Target, Target extends import("../types").KnownTarget ? React.ComponentPropsWithRef<Target> : object, object, object>) & {
    ActivityIndicator: Styled<"native", typeof import("react-native").ActivityIndicator, import("react-native").ActivityIndicatorProps, object, object>;
    Button: Styled<"native", typeof import("react-native").Button, import("react-native").ButtonProps, object, object>;
    DatePickerIOS: Styled<"native", typeof import("react-native").DatePickerIOS, import("react-native").DatePickerIOSProps, object, object>;
    DrawerLayoutAndroid: Styled<"native", typeof import("react-native").DrawerLayoutAndroid, import("react-native").DrawerLayoutAndroidProps, object, object>;
    FlatList: Styled<"native", typeof import("react-native").FlatList, import("react-native").FlatListProps<unknown>, object, object>;
    Image: Styled<"native", typeof import("react-native").Image, import("react-native").ImageProps, object, object>;
    ImageBackground: Styled<"native", typeof import("react-native").ImageBackground, import("react-native").ImageBackgroundProps, object, object>;
    KeyboardAvoidingView: Styled<"native", typeof import("react-native").KeyboardAvoidingView, import("react-native").KeyboardAvoidingViewProps, object, object>;
    Modal: Styled<"native", typeof import("react-native").Modal, import("react-native").ModalBaseProps & import("react-native").ModalPropsIOS & import("react-native").ModalPropsAndroid & import("react-native").ViewProps, object, object>;
    Pressable: Styled<"native", import("react").ForwardRefExoticComponent<import("react-native").PressableProps & import("react").RefAttributes<import("react-native").View>>, import("react-native").PressableProps & import("react").RefAttributes<import("react-native").View>, object, object>;
    ProgressBarAndroid: Styled<"native", typeof import("react-native").ProgressBarAndroid, import("react-native").ProgressBarAndroidProps, object, object>;
    ProgressViewIOS: Styled<"native", typeof import("react-native").ProgressViewIOS, import("react-native").ProgressViewIOSProps, object, object>;
    RefreshControl: Styled<"native", typeof import("react-native").RefreshControl, import("react-native").RefreshControlProps, object, object>;
    SafeAreaView: Styled<"native", typeof import("react-native").SafeAreaView, import("react-native").ViewProps, object, object>;
    ScrollView: Styled<"native", typeof import("react-native").ScrollView, import("react-native").ScrollViewProps, object, object>;
    SectionList: Styled<"native", typeof import("react-native").SectionList, import("react-native").SectionListProps<unknown, unknown>, object, object>;
    Slider: Styled<"native", typeof import("react-native").Slider, import("react-native").SliderProps, object, object>;
    Switch: Styled<"native", typeof import("react-native").Switch, import("react-native").SwitchProps, object, object>;
    Text: Styled<"native", typeof import("react-native").Text, import("react-native").TextProps, object, object>;
    TextInput: Styled<"native", typeof import("react-native").TextInput, import("react-native").TextInputProps, object, object>;
    TouchableHighlight: Styled<"native", typeof import("react-native").TouchableHighlight, import("react-native").TouchableHighlightProps, object, object>;
    TouchableOpacity: Styled<"native", typeof import("react-native").TouchableOpacity, import("react-native").TouchableOpacityProps, object, object>;
    View: Styled<"native", typeof import("react-native").View, import("react-native").ViewProps, object, object>;
    VirtualizedList: Styled<"native", typeof import("react-native").VirtualizedList, import("react-native").VirtualizedListProps<unknown>, object, object>;
};
export { DefaultTheme, ExecutionContext, ExecutionProps, IStyledComponent, IStyledComponentFactory, IStyledStatics, NativeTarget, PolymorphicComponent, PolymorphicComponentProps, Runtime, StyledObject, StyledOptions, } from '../types';
export { css, isStyledComponent, ThemeProvider, ThemeConsumer, ThemeContext, withTheme, useTheme };
export { styled, styled as default };
