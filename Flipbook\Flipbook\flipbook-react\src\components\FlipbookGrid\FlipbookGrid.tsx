import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import FlipbookPreview from '../FlipbookPreview/FlipbookPreview';
import { useAuth } from '../../hooks/useAuth';
import { useFlipbooks } from '../../hooks/useFlipbooks';
import { Inspiration } from '../../types/flipbook.types';
import { ORIGINAL_FLIPBOOK } from '../../constants/flipbook.constants';
import { apiService } from '../../services/api.service';
import './FlipbookGrid.css';

interface FlipbookGridProps {
  initialInspirationFlipbooks: Inspiration[];
}

const FlipbookGrid: React.FC<FlipbookGridProps> = ({ initialInspirationFlipbooks }) => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const {
    userFlipbooks,
    loading: flipbooksLoading,
    error: flipbooksError,
    createFlipbook,
    copyInspirationFlipbook,
    deleteFlipbook,
    refreshUserFlipbooks
  } = useFlipbooks();

  // Use initial inspiration flipbooks (loaded without auth)
  // Ensure we always have at least the Original Flipbook
  const inspirationFlipbooks = initialInspirationFlipbooks.length > 0 
    ? initialInspirationFlipbooks 
    : [ORIGINAL_FLIPBOOK];
    
  // Debug logging (removed for production)

  // Load user flipbooks when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      refreshUserFlipbooks();
    }
  }, [isAuthenticated, refreshUserFlipbooks]);
  
  const [previewFlipbook, setPreviewFlipbook] = useState<{
    portfolioId: number;
    pageCount: number;
    isInspiration: boolean;
  } | null>(null);


  const handleCreateNewFlipbook = () => {
    // Navigate directly to editor without portfolio ID
    // This will trigger the NameFlipbook modal in the editor
    navigate('/editor');
  };


  const handleOpenFlipbook = (portfolio: typeof userFlipbooks[0]) => {
    console.log('Opening flipbook:', portfolio.PortfolioTitle);
    // Navigate to the editor for user flipbooks
    navigate(`/editor/${portfolio.PortfolioID}`);
  };

  const handleOpenInspirationFlipbook = (inspiration: Inspiration) => {
    console.log('Opening inspiration flipbook:', inspiration.FBTitle);
    // For inspiration flipbooks, navigate to viewer
    navigate(`/viewer/${inspiration.PortfolioID}`);
  };

  const handleClosePreview = () => {
    setPreviewFlipbook(null);
  };

  const handleCopyToAccount = async (portfolioId: string, title: string) => {
    console.log(`Copying flipbook ${portfolioId} with title: ${title}`);
    const success = await copyInspirationFlipbook({ 
      portfolioId: parseInt(portfolioId), 
      title 
    });
    
    if (success) {
      setPreviewFlipbook(null);
      // Show success message
      alert(`Flipbook "${title}" has been copied to your account!`);
    }
  };

  // Show loading only for authenticated user's flipbooks
  if (isAuthenticated && flipbooksLoading) {
    return (
      <div className="flipbook-grid-container">
        <div className="flipbook-grid">
          <div className="loading-state">
            <div className="spinner"></div>
            <p>Loading your flipbooks...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error only for critical failures
  if (flipbooksError && isAuthenticated) {
    return (
      <div className="flipbook-grid-container">
        <div className="flipbook-grid">
          <div className="error-state">
            <p>Error: {flipbooksError}</p>
            <button onClick={() => window.location.reload()}>Try Again</button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flipbook-grid-container">
      <div className="flipbook-grid">
        {/* My Flipbooks Section */}
        <section className="flipbooks-section">
          <h1 className="section-title">MY FLIPBOOKS</h1>
          
          <div className="flipbooks-grid">
            {/* Create New Flipbook Card */}
            <div className="flipbook-card create-new" onClick={handleCreateNewFlipbook}>
              <div className="create-new-content">
                <div className="plus-icon">
                  <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              </div>
              <p className="flipbook-title">Create New Flipbook</p>
            </div>

            {/* User's Flipbooks */}
            {userFlipbooks.map((portfolio) => (
              <div 
                key={portfolio.PortfolioID}
                className="flipbook-card"
                onClick={() => handleOpenFlipbook(portfolio)}
              >
                <div className="flipbook-thumbnail">
                  <img 
                    src={user ? apiService.getThumbnailUrl(portfolio.PortfolioID, user.userFolderID, false) : '/images/flipbooks/placeholders/thumbnail-fallback.jpg'} 
                    alt={portfolio.PortfolioTitle}
                  />
                  <div className="flipbook-overlay">
                    <button className="open-btn">Open</button>
                  </div>
                </div>
                <p className="flipbook-title">{portfolio.PortfolioTitle}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Inspiration Section */}
        <section className="flipbooks-section inspiration-section">
          <h1 className="section-title">INSPIRATION</h1>
          
          <div className="flipbooks-grid">
            {inspirationFlipbooks.map((inspiration) => (
              <div 
                key={inspiration.PortfolioID}
                className="flipbook-card inspiration-card"
                onClick={() => handleOpenInspirationFlipbook(inspiration)}
              >
                <div className="flipbook-thumbnail">
                  <img 
                    src={inspiration.TnImageSrc} 
                    alt={inspiration.FBTitle}
                  />
                  <div className="flipbook-overlay">
                    <button className="open-btn">Open</button>
                  </div>
                </div>
                <p className="flipbook-title">{inspiration.FBTitle}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Beta Testing Badge */}
        <div className="beta-badge">
          <img src="/images/flipbooks/placeholders/default-fallback.jpg" alt="Beta Testing" />
        </div>
      </div>


      {/* FlipbookPreview Modal */}
      {previewFlipbook && (
        <FlipbookPreview
          portfolioId={previewFlipbook.portfolioId.toString()}
          pageCount={previewFlipbook.pageCount}
          isInspiration={previewFlipbook.isInspiration}
          onClose={handleClosePreview}
          onCopyToAccount={handleCopyToAccount}
        />
      )}
    </div>
  );
};

export default FlipbookGrid;
