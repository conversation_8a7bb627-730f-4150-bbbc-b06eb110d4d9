{"ast": null, "code": "var _jsxFileName = \"D:\\\\TradeWorks\\\\Flipbook\\\\Flipbook\\\\Flipbook\\\\flipbook-react\\\\src\\\\components\\\\LandingPage\\\\FlipbookGrid.tsx\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst GridContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 20px;\n  padding: 20px 0;\n`;\n_c = GridContainer;\nconst FlipbookCard = styled.div`\n  width: 200px;\n  height: 250px;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background-color: white;\n  position: relative;\n\n  &:hover {\n    transform: translateY(-8px);\n    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n  }\n`;\n_c2 = FlipbookCard;\nconst CreateNewCard = styled(FlipbookCard)`\n  border: 2px dashed #d1d5db;\n  background-color: #f9fafb;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    border-color: #3b82f6;\n    background-color: #eff6ff;\n  }\n`;\n_c3 = CreateNewCard;\nconst PlusIcon = styled.div`\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  background-color: #e5e7eb;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 32px;\n  color: #6b7280;\n  margin-bottom: 16px;\n  transition: all 0.3s ease;\n\n  ${CreateNewCard}:hover & {\n    background-color: #3b82f6;\n    color: white;\n  }\n`;\n_c4 = PlusIcon;\nconst CreateText = styled.span`\n  font-size: 14px;\n  color: #6b7280;\n  text-align: center;\n  font-weight: 500;\n  line-height: 1.4;\n\n  ${CreateNewCard}:hover & {\n    color: #3b82f6;\n  }\n`;\n_c5 = CreateText;\nconst FlipbookThumbnail = styled.div`\n  width: 100%;\n  height: 180px;\n  background-image: ${props => props.backgroundImage ? `url(${props.backgroundImage})` : 'none'};\n  background-size: cover;\n  background-position: center;\n  background-color: #f3f4f6;\n  position: relative;\n\n  ${props => !props.backgroundImage && `\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: #9ca3af;\n    font-size: 48px;\n  `}\n`;\n_c6 = FlipbookThumbnail;\nconst FlipbookInfo = styled.div`\n  padding: 16px;\n  height: 70px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n`;\n_c7 = FlipbookInfo;\nconst FlipbookTitle = styled.h3`\n  font-size: 14px;\n  font-weight: 600;\n  color: #1f2937;\n  margin: 0;\n  text-align: center;\n  line-height: 1.4;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n`;\n_c8 = FlipbookTitle;\nconst ActionOverlay = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  \n  ${FlipbookCard}:hover & {\n    opacity: 1;\n  }\n`;\n_c9 = ActionOverlay;\nconst ActionButton = styled.button`\n  background-color: #3b82f6;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  margin: 0 5px;\n  transition: background-color 0.3s ease;\n\n  &:hover {\n    background-color: #2563eb;\n  }\n`;\n_c0 = ActionButton;\nconst FlipbookGrid = ({\n  flipbooks,\n  showCreateNew = false,\n  onCreateNew,\n  onFlipbookClick,\n  onFlipbookEdit,\n  onFlipbookCopy\n}) => {\n  return /*#__PURE__*/_jsxDEV(GridContainer, {\n    children: [showCreateNew && /*#__PURE__*/_jsxDEV(CreateNewCard, {\n      onClick: onCreateNew,\n      children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n        children: \"+\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(CreateText, {\n        children: \"Create New Flipbook\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 9\n    }, this), flipbooks.map(flipbook => /*#__PURE__*/_jsxDEV(FlipbookCard, {\n      onClick: () => onFlipbookClick === null || onFlipbookClick === void 0 ? void 0 : onFlipbookClick(flipbook),\n      children: [/*#__PURE__*/_jsxDEV(FlipbookThumbnail, {\n        backgroundImage: flipbook.thumbnail,\n        children: !flipbook.thumbnail && '📖'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(FlipbookInfo, {\n        children: /*#__PURE__*/_jsxDEV(FlipbookTitle, {\n          children: flipbook.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ActionOverlay, {\n        children: flipbook.isInspiration ? /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: e => {\n            e.stopPropagation();\n            onFlipbookCopy === null || onFlipbookCopy === void 0 ? void 0 : onFlipbookCopy(flipbook);\n          },\n          children: \"Copy to My Flipbooks\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n            onClick: e => {\n              e.stopPropagation();\n              onFlipbookClick === null || onFlipbookClick === void 0 ? void 0 : onFlipbookClick(flipbook);\n            },\n            children: \"Open\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n            onClick: e => {\n              e.stopPropagation();\n              onFlipbookEdit === null || onFlipbookEdit === void 0 ? void 0 : onFlipbookEdit(flipbook);\n            },\n            children: \"Edit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 11\n      }, this)]\n    }, flipbook.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 9\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 173,\n    columnNumber: 5\n  }, this);\n};\n_c1 = FlipbookGrid;\nexport default FlipbookGrid;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"GridContainer\");\n$RefreshReg$(_c2, \"FlipbookCard\");\n$RefreshReg$(_c3, \"CreateNewCard\");\n$RefreshReg$(_c4, \"PlusIcon\");\n$RefreshReg$(_c5, \"CreateText\");\n$RefreshReg$(_c6, \"FlipbookThumbnail\");\n$RefreshReg$(_c7, \"FlipbookInfo\");\n$RefreshReg$(_c8, \"FlipbookTitle\");\n$RefreshReg$(_c9, \"ActionOverlay\");\n$RefreshReg$(_c0, \"ActionButton\");\n$RefreshReg$(_c1, \"FlipbookGrid\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "FlipbookCard", "_c2", "CreateNewCard", "_c3", "PlusIcon", "_c4", "CreateText", "span", "_c5", "FlipbookThumbnail", "props", "backgroundImage", "_c6", "FlipbookInfo", "_c7", "FlipbookTitle", "h3", "_c8", "ActionOverlay", "_c9", "ActionButton", "button", "_c0", "FlipbookGrid", "flipbooks", "showCreateNew", "onCreateNew", "onFlipbookClick", "onFlipbookEdit", "onFlipbookCopy", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "flipbook", "thumbnail", "title", "isInspiration", "e", "stopPropagation", "id", "_c1", "$RefreshReg$"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/LandingPage/FlipbookGrid.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\n\nconst GridContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 20px;\n  padding: 20px 0;\n`;\n\nconst FlipbookCard = styled.div`\n  width: 200px;\n  height: 250px;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background-color: white;\n  position: relative;\n\n  &:hover {\n    transform: translateY(-8px);\n    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n  }\n`;\n\nconst CreateNewCard = styled(FlipbookCard)`\n  border: 2px dashed #d1d5db;\n  background-color: #f9fafb;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    border-color: #3b82f6;\n    background-color: #eff6ff;\n  }\n`;\n\nconst PlusIcon = styled.div`\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  background-color: #e5e7eb;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 32px;\n  color: #6b7280;\n  margin-bottom: 16px;\n  transition: all 0.3s ease;\n\n  ${CreateNewCard}:hover & {\n    background-color: #3b82f6;\n    color: white;\n  }\n`;\n\nconst CreateText = styled.span`\n  font-size: 14px;\n  color: #6b7280;\n  text-align: center;\n  font-weight: 500;\n  line-height: 1.4;\n\n  ${CreateNewCard}:hover & {\n    color: #3b82f6;\n  }\n`;\n\nconst FlipbookThumbnail = styled.div<{ backgroundImage?: string }>`\n  width: 100%;\n  height: 180px;\n  background-image: ${props => props.backgroundImage ? `url(${props.backgroundImage})` : 'none'};\n  background-size: cover;\n  background-position: center;\n  background-color: #f3f4f6;\n  position: relative;\n\n  ${props => !props.backgroundImage && `\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: #9ca3af;\n    font-size: 48px;\n  `}\n`;\n\nconst FlipbookInfo = styled.div`\n  padding: 16px;\n  height: 70px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n`;\n\nconst FlipbookTitle = styled.h3`\n  font-size: 14px;\n  font-weight: 600;\n  color: #1f2937;\n  margin: 0;\n  text-align: center;\n  line-height: 1.4;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n`;\n\nconst ActionOverlay = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  \n  ${FlipbookCard}:hover & {\n    opacity: 1;\n  }\n`;\n\nconst ActionButton = styled.button`\n  background-color: #3b82f6;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  margin: 0 5px;\n  transition: background-color 0.3s ease;\n\n  &:hover {\n    background-color: #2563eb;\n  }\n`;\n\ninterface Flipbook {\n  id: number;\n  title: string;\n  thumbnail?: string;\n  isInspiration?: boolean;\n}\n\ninterface FlipbookGridProps {\n  flipbooks: Flipbook[];\n  showCreateNew?: boolean;\n  onCreateNew?: () => void;\n  onFlipbookClick?: (flipbook: Flipbook) => void;\n  onFlipbookEdit?: (flipbook: Flipbook) => void;\n  onFlipbookCopy?: (flipbook: Flipbook) => void | Promise<void>;\n}\n\nconst FlipbookGrid: React.FC<FlipbookGridProps> = ({\n  flipbooks,\n  showCreateNew = false,\n  onCreateNew,\n  onFlipbookClick,\n  onFlipbookEdit,\n  onFlipbookCopy\n}) => {\n  return (\n    <GridContainer>\n      {showCreateNew && (\n        <CreateNewCard onClick={onCreateNew}>\n          <PlusIcon>+</PlusIcon>\n          <CreateText>Create New Flipbook</CreateText>\n        </CreateNewCard>\n      )}\n      \n      {flipbooks.map((flipbook) => (\n        <FlipbookCard key={flipbook.id} onClick={() => onFlipbookClick?.(flipbook)}>\n          <FlipbookThumbnail backgroundImage={flipbook.thumbnail}>\n            {!flipbook.thumbnail && '📖'}\n          </FlipbookThumbnail>\n          <FlipbookInfo>\n            <FlipbookTitle>{flipbook.title}</FlipbookTitle>\n          </FlipbookInfo>\n          \n          <ActionOverlay>\n            {flipbook.isInspiration ? (\n              <ActionButton onClick={(e) => {\n                e.stopPropagation();\n                onFlipbookCopy?.(flipbook);\n              }}>\n                Copy to My Flipbooks\n              </ActionButton>\n            ) : (\n              <>\n                <ActionButton onClick={(e) => {\n                  e.stopPropagation();\n                  onFlipbookClick?.(flipbook);\n                }}>\n                  Open\n                </ActionButton>\n                <ActionButton onClick={(e) => {\n                  e.stopPropagation();\n                  onFlipbookEdit?.(flipbook);\n                }}>\n                  Edit\n                </ActionButton>\n              </>\n            )}\n          </ActionOverlay>\n        </FlipbookCard>\n      ))}\n    </GridContainer>\n  );\n};\n\nexport default FlipbookGrid;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,aAAa,GAAGL,MAAM,CAACM,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,aAAa;AAOnB,MAAMG,YAAY,GAAGR,MAAM,CAACM,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAfID,YAAY;AAiBlB,MAAME,aAAa,GAAGV,MAAM,CAACQ,YAAY,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAZID,aAAa;AAcnB,MAAME,QAAQ,GAAGZ,MAAM,CAACM,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAII,aAAa;AACjB;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAjBID,QAAQ;AAmBd,MAAME,UAAU,GAAGd,MAAM,CAACe,IAAI;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,IAAIL,aAAa;AACjB;AACA;AACA,CAAC;AAACM,GAAA,GAVIF,UAAU;AAYhB,MAAMG,iBAAiB,GAAGjB,MAAM,CAACM,GAAiC;AAClE;AACA;AACA,sBAAsBY,KAAK,IAAIA,KAAK,CAACC,eAAe,GAAG,OAAOD,KAAK,CAACC,eAAe,GAAG,GAAG,MAAM;AAC/F;AACA;AACA;AACA;AACA;AACA,IAAID,KAAK,IAAI,CAACA,KAAK,CAACC,eAAe,IAAI;AACvC;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AAACC,GAAA,GAhBIH,iBAAiB;AAkBvB,MAAMI,YAAY,GAAGrB,MAAM,CAACM,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GANID,YAAY;AAQlB,MAAME,aAAa,GAAGvB,MAAM,CAACwB,EAAE;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAZIF,aAAa;AAcnB,MAAMG,aAAa,GAAG1B,MAAM,CAACM,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,YAAY;AAChB;AACA;AACA,CAAC;AAACmB,GAAA,GAhBID,aAAa;AAkBnB,MAAME,YAAY,GAAG5B,MAAM,CAAC6B,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,YAAY;AAiClB,MAAMG,YAAyC,GAAGA,CAAC;EACjDC,SAAS;EACTC,aAAa,GAAG,KAAK;EACrBC,WAAW;EACXC,eAAe;EACfC,cAAc;EACdC;AACF,CAAC,KAAK;EACJ,oBACEnC,OAAA,CAACG,aAAa;IAAAiC,QAAA,GACXL,aAAa,iBACZ/B,OAAA,CAACQ,aAAa;MAAC6B,OAAO,EAAEL,WAAY;MAAAI,QAAA,gBAClCpC,OAAA,CAACU,QAAQ;QAAA0B,QAAA,EAAC;MAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACtBzC,OAAA,CAACY,UAAU;QAAAwB,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAChB,EAEAX,SAAS,CAACY,GAAG,CAAEC,QAAQ,iBACtB3C,OAAA,CAACM,YAAY;MAAmB+B,OAAO,EAAEA,CAAA,KAAMJ,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGU,QAAQ,CAAE;MAAAP,QAAA,gBACzEpC,OAAA,CAACe,iBAAiB;QAACE,eAAe,EAAE0B,QAAQ,CAACC,SAAU;QAAAR,QAAA,EACpD,CAACO,QAAQ,CAACC,SAAS,IAAI;MAAI;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACpBzC,OAAA,CAACmB,YAAY;QAAAiB,QAAA,eACXpC,OAAA,CAACqB,aAAa;UAAAe,QAAA,EAAEO,QAAQ,CAACE;QAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eAEfzC,OAAA,CAACwB,aAAa;QAAAY,QAAA,EACXO,QAAQ,CAACG,aAAa,gBACrB9C,OAAA,CAAC0B,YAAY;UAACW,OAAO,EAAGU,CAAC,IAAK;YAC5BA,CAAC,CAACC,eAAe,CAAC,CAAC;YACnBb,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAGQ,QAAQ,CAAC;UAC5B,CAAE;UAAAP,QAAA,EAAC;QAEH;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,gBAEfzC,OAAA,CAAAE,SAAA;UAAAkC,QAAA,gBACEpC,OAAA,CAAC0B,YAAY;YAACW,OAAO,EAAGU,CAAC,IAAK;cAC5BA,CAAC,CAACC,eAAe,CAAC,CAAC;cACnBf,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGU,QAAQ,CAAC;YAC7B,CAAE;YAAAP,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACfzC,OAAA,CAAC0B,YAAY;YAACW,OAAO,EAAGU,CAAC,IAAK;cAC5BA,CAAC,CAACC,eAAe,CAAC,CAAC;cACnBd,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAGS,QAAQ,CAAC;YAC5B,CAAE;YAAAP,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA,eACf;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA,GAhCCE,QAAQ,CAACM,EAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAiChB,CACf,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAEpB,CAAC;AAACS,GAAA,GAvDIrB,YAAyC;AAyD/C,eAAeA,YAAY;AAAC,IAAAxB,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAsB,GAAA;AAAAC,YAAA,CAAA9C,EAAA;AAAA8C,YAAA,CAAA5C,GAAA;AAAA4C,YAAA,CAAA1C,GAAA;AAAA0C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAArC,GAAA;AAAAqC,YAAA,CAAAjC,GAAA;AAAAiC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAvB,GAAA;AAAAuB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}