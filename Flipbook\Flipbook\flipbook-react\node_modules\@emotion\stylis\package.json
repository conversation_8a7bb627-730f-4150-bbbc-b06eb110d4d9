{"name": "@emotion/stylis", "version": "0.8.5", "description": "A custom build of <PERSON><PERSON><PERSON>", "main": "dist/stylis.cjs.js", "module": "dist/stylis.esm.js", "types": "types/index.d.ts", "license": "MIT", "scripts": {"test:typescript": "dtslint types"}, "repository": "https://github.com/emotion-js/emotion/tree/master/packages/stylis", "publishConfig": {"access": "public"}, "files": ["src", "dist", "types"], "devDependencies": {"dtslint": "^0.3.0", "jscodeshift": "^0.5.0", "request": "^2.85.0", "request-promise-native": "^1.0.5", "stylis": "3.5.4"}, "browser": {"./dist/stylis.cjs.js": "./dist/stylis.browser.cjs.js", "./dist/stylis.esm.js": "./dist/stylis.browser.esm.js"}}