{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route,Navigate}from'react-router-dom';import LandingPage from'../LandingPage/LandingPage';import FlipbookEditor from'../FlipbookEditor/FlipbookEditor';import FlipbookViewer from'../FlipbookViewer/FlipbookViewer';import NotFound from'../NotFound/NotFound';// Placeholder component for User Profile\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const UserProfile=()=>{return/*#__PURE__*/_jsxs(\"div\",{style:{padding:'40px',textAlign:'center'},children:[/*#__PURE__*/_jsx(\"h1\",{children:\"User Profile\"}),/*#__PURE__*/_jsx(\"p\",{children:\"User profile functionality will be implemented here.\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>window.history.back(),children:\"Go Back\"})]});};const AppRouter=()=>{return/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(LandingPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard\",element:/*#__PURE__*/_jsx(LandingPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/editor\",element:/*#__PURE__*/_jsx(FlipbookEditor,{})}),/*#__PURE__*/_jsx(Route,{path:\"/editor/:portfolioId\",element:/*#__PURE__*/_jsx(FlipbookEditor,{})}),/*#__PURE__*/_jsx(Route,{path:\"/viewer/:portfolioId\",element:/*#__PURE__*/_jsx(FlipbookViewer,{})}),/*#__PURE__*/_jsx(Route,{path:\"/preview/:portfolioId\",element:/*#__PURE__*/_jsx(FlipbookViewer,{})}),/*#__PURE__*/_jsx(Route,{path:\"/profile\",element:/*#__PURE__*/_jsx(UserProfile,{})}),/*#__PURE__*/_jsx(Route,{path:\"/settings\",element:/*#__PURE__*/_jsx(UserProfile,{})}),/*#__PURE__*/_jsx(Route,{path:\"/share/:portfolioId\",element:/*#__PURE__*/_jsx(FlipbookViewer,{})}),/*#__PURE__*/_jsx(Route,{path:\"/public/:portfolioId\",element:/*#__PURE__*/_jsx(FlipbookViewer,{})}),/*#__PURE__*/_jsx(Route,{path:\"/Flipbook/UserPreview/:portfolioId\",element:/*#__PURE__*/_jsx(FlipbookViewer,{})}),/*#__PURE__*/_jsx(Route,{path:\"/Users/<USER>/Flipbooks/:portfolioId\",element:/*#__PURE__*/_jsx(FlipbookViewer,{})}),/*#__PURE__*/_jsx(Route,{path:\"/flipbook\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"/home\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(NotFound,{})})]})});};export default AppRouter;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "LandingPage", "FlipbookEditor", "FlipbookViewer", "NotFound", "jsx", "_jsx", "jsxs", "_jsxs", "UserProfile", "style", "padding", "textAlign", "children", "onClick", "window", "history", "back", "AppRouter", "path", "element", "to", "replace"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/Router/AppRouter.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport LandingPage from '../LandingPage/LandingPage';\nimport Dashboard from '../Dashboard/Dashboard';\nimport FlipbookEditor from '../FlipbookEditor/FlipbookEditor';\nimport FlipbookViewer from '../FlipbookViewer/FlipbookViewer';\nimport NotFound from '../NotFound/NotFound';\n\n// Placeholder component for User Profile\nconst UserProfile: React.FC = () => {\n  return (\n    <div style={{ padding: '40px', textAlign: 'center' }}>\n      <h1>User Profile</h1>\n      <p>User profile functionality will be implemented here.</p>\n      <button onClick={() => window.history.back()}>Go Back</button>\n    </div>\n  );\n};\n\nconst AppRouter: React.FC = () => {\n  return (\n    <Router>\n      <Routes>\n        {/* Main landing page route */}\n        <Route path=\"/\" element={<LandingPage />} />\n\n        {/* Dashboard alias */}\n        <Route path=\"/dashboard\" element={<LandingPage />} />\n        \n        {/* Flipbook editor routes */}\n        <Route path=\"/editor\" element={<FlipbookEditor />} />\n        <Route path=\"/editor/:portfolioId\" element={<FlipbookEditor />} />\n        \n        {/* Flipbook viewer routes */}\n        <Route path=\"/viewer/:portfolioId\" element={<FlipbookViewer />} />\n        <Route path=\"/preview/:portfolioId\" element={<FlipbookViewer />} />\n        \n        {/* User profile and settings */}\n        <Route path=\"/profile\" element={<UserProfile />} />\n        <Route path=\"/settings\" element={<UserProfile />} />\n        \n        {/* Public flipbook sharing routes */}\n        <Route path=\"/share/:portfolioId\" element={<FlipbookViewer />} />\n        <Route path=\"/public/:portfolioId\" element={<FlipbookViewer />} />\n        \n        {/* Legacy .NET application routes for compatibility */}\n        <Route path=\"/Flipbook/UserPreview/:portfolioId\" element={<FlipbookViewer />} />\n        <Route path=\"/Users/<USER>/Flipbooks/:portfolioId\" element={<FlipbookViewer />} />\n        \n        {/* Redirect old routes */}\n        <Route path=\"/flipbook\" element={<Navigate to=\"/dashboard\" replace />} />\n        <Route path=\"/home\" element={<Navigate to=\"/dashboard\" replace />} />\n        \n        {/* 404 Not Found */}\n        <Route path=\"*\" element={<NotFound />} />\n      </Routes>\n    </Router>\n  );\n};\n\nexport default AppRouter;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,MAAO,CAAAC,WAAW,KAAM,4BAA4B,CAEpD,MAAO,CAAAC,cAAc,KAAM,kCAAkC,CAC7D,MAAO,CAAAC,cAAc,KAAM,kCAAkC,CAC7D,MAAO,CAAAC,QAAQ,KAAM,sBAAsB,CAE3C;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,WAAqB,CAAGA,CAAA,GAAM,CAClC,mBACED,KAAA,QAAKE,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAC,QAAA,eACnDP,IAAA,OAAAO,QAAA,CAAI,cAAY,CAAI,CAAC,cACrBP,IAAA,MAAAO,QAAA,CAAG,sDAAoD,CAAG,CAAC,cAC3DP,IAAA,WAAQQ,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE,CAAAJ,QAAA,CAAC,SAAO,CAAQ,CAAC,EAC3D,CAAC,CAEV,CAAC,CAED,KAAM,CAAAK,SAAmB,CAAGA,CAAA,GAAM,CAChC,mBACEZ,IAAA,CAACT,MAAM,EAAAgB,QAAA,cACLL,KAAA,CAACV,MAAM,EAAAe,QAAA,eAELP,IAAA,CAACP,KAAK,EAACoB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEd,IAAA,CAACL,WAAW,GAAE,CAAE,CAAE,CAAC,cAG5CK,IAAA,CAACP,KAAK,EAACoB,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEd,IAAA,CAACL,WAAW,GAAE,CAAE,CAAE,CAAC,cAGrDK,IAAA,CAACP,KAAK,EAACoB,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEd,IAAA,CAACJ,cAAc,GAAE,CAAE,CAAE,CAAC,cACrDI,IAAA,CAACP,KAAK,EAACoB,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAEd,IAAA,CAACJ,cAAc,GAAE,CAAE,CAAE,CAAC,cAGlEI,IAAA,CAACP,KAAK,EAACoB,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAEd,IAAA,CAACH,cAAc,GAAE,CAAE,CAAE,CAAC,cAClEG,IAAA,CAACP,KAAK,EAACoB,IAAI,CAAC,uBAAuB,CAACC,OAAO,cAAEd,IAAA,CAACH,cAAc,GAAE,CAAE,CAAE,CAAC,cAGnEG,IAAA,CAACP,KAAK,EAACoB,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEd,IAAA,CAACG,WAAW,GAAE,CAAE,CAAE,CAAC,cACnDH,IAAA,CAACP,KAAK,EAACoB,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEd,IAAA,CAACG,WAAW,GAAE,CAAE,CAAE,CAAC,cAGpDH,IAAA,CAACP,KAAK,EAACoB,IAAI,CAAC,qBAAqB,CAACC,OAAO,cAAEd,IAAA,CAACH,cAAc,GAAE,CAAE,CAAE,CAAC,cACjEG,IAAA,CAACP,KAAK,EAACoB,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAEd,IAAA,CAACH,cAAc,GAAE,CAAE,CAAE,CAAC,cAGlEG,IAAA,CAACP,KAAK,EAACoB,IAAI,CAAC,oCAAoC,CAACC,OAAO,cAAEd,IAAA,CAACH,cAAc,GAAE,CAAE,CAAE,CAAC,cAChFG,IAAA,CAACP,KAAK,EAACoB,IAAI,CAAC,uCAAuC,CAACC,OAAO,cAAEd,IAAA,CAACH,cAAc,GAAE,CAAE,CAAE,CAAC,cAGnFG,IAAA,CAACP,KAAK,EAACoB,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEd,IAAA,CAACN,QAAQ,EAACqB,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cACzEhB,IAAA,CAACP,KAAK,EAACoB,IAAI,CAAC,OAAO,CAACC,OAAO,cAAEd,IAAA,CAACN,QAAQ,EAACqB,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cAGrEhB,IAAA,CAACP,KAAK,EAACoB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEd,IAAA,CAACF,QAAQ,GAAE,CAAE,CAAE,CAAC,EACnC,CAAC,CACH,CAAC,CAEb,CAAC,CAED,cAAe,CAAAc,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}