{"ast": null, "code": "var _jsxFileName = \"D:\\\\TradeWorks\\\\Flipbook\\\\Flipbook\\\\Flipbook\\\\flipbook-react\\\\src\\\\components\\\\LandingPage\\\\LandingPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport Header from './Header';\nimport Sidebar from './Sidebar';\nimport FlipbookGrid from './FlipbookGrid';\nimport CreateFlipbookModal from './CreateFlipbookModal';\nimport { useFlipbooks } from '../../hooks/useFlipbooks';\nimport { useAuth } from '../../hooks/useAuth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LandingPageContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #f5f5f5;\n`;\n_c = LandingPageContainer;\nconst MainContent = styled.div`\n  display: flex;\n  flex: 1;\n  overflow: hidden;\n`;\n_c2 = MainContent;\nconst ContentArea = styled.div`\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n  background-color: #ffffff;\n`;\n_c3 = ContentArea;\nconst Section = styled.div`\n  margin-bottom: 40px;\n`;\n_c4 = Section;\nconst SectionTitle = styled.h2`\n  font-size: 18px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 20px;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n`;\n_c5 = SectionTitle;\nconst CertificationBadge = styled.div`\n  position: fixed;\n  top: 80px;\n  right: 20px;\n  width: 100px;\n  height: 100px;\n  background-image: url('/images/certification-badge.png');\n  background-size: contain;\n  background-repeat: no-repeat;\n  z-index: 10;\n`;\n_c6 = CertificationBadge;\nconst LandingPage = () => {\n  _s();\n  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);\n\n  // Use real hooks that match .NET functionality\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const {\n    userFlipbooks,\n    inspirationFlipbooks,\n    loading,\n    error,\n    createFlipbook,\n    copyInspirationFlipbook\n  } = useFlipbooks();\n\n  // Convert data to match component interface\n  const myFlipbooks = userFlipbooks.map(fb => ({\n    id: fb.PortfolioID,\n    title: fb.PortfolioTitle || 'Untitled',\n    thumbnail: fb.ThumbnailPath || 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop',\n    isInspiration: false\n  }));\n  const inspirationFlipbooksFormatted = inspirationFlipbooks.map(fb => ({\n    id: fb.PortfolioID,\n    title: fb.FBTitle,\n    thumbnail: fb.TnImageSrc || 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop',\n    isInspiration: true\n  }));\n  const handleCreateNew = () => {\n    setIsCreateModalOpen(true);\n  };\n  const handleCloseModal = () => {\n    setIsCreateModalOpen(false);\n  };\n  const handleCreateFlipbook = async title => {\n    try {\n      const portfolioId = await createFlipbook({\n        title\n      });\n      if (portfolioId) {\n        console.log('Flipbook created successfully with ID:', portfolioId);\n        setIsCreateModalOpen(false);\n        // Navigate to flipbook editor if needed\n        // window.location.href = `/Flipbook/Pagemanager?PortFolioID=${portfolioId}`;\n      }\n    } catch (err) {\n      console.error('Failed to create flipbook:', err);\n    }\n  };\n  const handleCopyInspiration = async flipbook => {\n    try {\n      const portfolioId = await copyInspirationFlipbook({\n        portfolioId: flipbook.id,\n        title: `Copy of ${flipbook.title}`\n      });\n      if (portfolioId) {\n        console.log('Inspiration flipbook copied successfully with ID:', portfolioId);\n        // Navigate to flipbook editor if needed\n        // window.location.href = `/Flipbook/Pagemanager?PortFolioID=${portfolioId}`;\n      }\n    } catch (err) {\n      console.error('Failed to copy inspiration flipbook:', err);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(LandingPageContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ContentArea, {\n        children: [/*#__PURE__*/_jsxDEV(Section, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: \"My Flipbooks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FlipbookGrid, {\n            flipbooks: myFlipbooks,\n            showCreateNew: true,\n            onCreateNew: handleCreateNew,\n            onFlipbookClick: flipbook => console.log('Open flipbook:', flipbook),\n            onFlipbookEdit: flipbook => console.log('Edit flipbook:', flipbook)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Section, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: \"Inspiration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FlipbookGrid, {\n            flipbooks: inspirationFlipbooksFormatted,\n            onFlipbookClick: flipbook => console.log('Preview flipbook:', flipbook),\n            onFlipbookCopy: handleCopyInspiration\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CertificationBadge, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), isCreateModalOpen && /*#__PURE__*/_jsxDEV(CreateFlipbookModal, {\n      onClose: handleCloseModal,\n      onCreate: handleCreateFlipbook\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n};\n_s(LandingPage, \"jfW3z5B+Ld3YB310Jk34B8wu5WM=\", false, function () {\n  return [useAuth, useFlipbooks];\n});\n_c7 = LandingPage;\nexport default LandingPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"LandingPageContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"ContentArea\");\n$RefreshReg$(_c4, \"Section\");\n$RefreshReg$(_c5, \"SectionTitle\");\n$RefreshReg$(_c6, \"CertificationBadge\");\n$RefreshReg$(_c7, \"LandingPage\");", "map": {"version": 3, "names": ["React", "useState", "styled", "Header", "Sidebar", "FlipbookGrid", "CreateFlipbookModal", "useFlipbooks", "useAuth", "jsxDEV", "_jsxDEV", "LandingPageContainer", "div", "_c", "MainContent", "_c2", "ContentArea", "_c3", "Section", "_c4", "SectionTitle", "h2", "_c5", "CertificationBadge", "_c6", "LandingPage", "_s", "isCreateModalOpen", "setIsCreateModalOpen", "user", "isAuthenticated", "userFlipbooks", "inspirationFlipbooks", "loading", "error", "createFlipbook", "copyInspirationFlipbook", "myFlipbooks", "map", "fb", "id", "PortfolioID", "title", "PortfolioTitle", "thumbnail", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isInspiration", "inspirationFlipbooksFormatted", "FBTitle", "TnImageSrc", "handleCreateNew", "handleCloseModal", "handleCreateFlipbook", "portfolioId", "console", "log", "err", "handleCopyInspiration", "flipbook", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flipbooks", "showCreateNew", "onCreateNew", "onFlipbookClick", "onFlipbookEdit", "onFlipbookCopy", "onClose", "onCreate", "_c7", "$RefreshReg$"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/LandingPage/LandingPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport Header from './Header';\nimport Sidebar from './Sidebar';\nimport FlipbookGrid from './FlipbookGrid';\nimport CreateFlipbookModal from './CreateFlipbookModal';\nimport { useFlipbooks } from '../../hooks/useFlipbooks';\nimport { useAuth } from '../../hooks/useAuth';\n\nconst LandingPageContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #f5f5f5;\n`;\n\nconst MainContent = styled.div`\n  display: flex;\n  flex: 1;\n  overflow: hidden;\n`;\n\nconst ContentArea = styled.div`\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n  background-color: #ffffff;\n`;\n\nconst Section = styled.div`\n  margin-bottom: 40px;\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: 18px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 20px;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n`;\n\n\n\nconst CertificationBadge = styled.div`\n  position: fixed;\n  top: 80px;\n  right: 20px;\n  width: 100px;\n  height: 100px;\n  background-image: url('/images/certification-badge.png');\n  background-size: contain;\n  background-repeat: no-repeat;\n  z-index: 10;\n`;\n\ninterface Flipbook {\n  id: number;\n  title: string;\n  thumbnail?: string;\n  isInspiration?: boolean;\n}\n\nconst LandingPage: React.FC = () => {\n  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);\n\n  // Use real hooks that match .NET functionality\n  const { user, isAuthenticated } = useAuth();\n  const {\n    userFlipbooks,\n    inspirationFlipbooks,\n    loading,\n    error,\n    createFlipbook,\n    copyInspirationFlipbook\n  } = useFlipbooks();\n\n  // Convert data to match component interface\n  const myFlipbooks: Flipbook[] = userFlipbooks.map(fb => ({\n    id: fb.PortfolioID,\n    title: fb.PortfolioTitle || 'Untitled',\n    thumbnail: fb.ThumbnailPath || 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop',\n    isInspiration: false\n  }));\n\n  const inspirationFlipbooksFormatted: Flipbook[] = inspirationFlipbooks.map(fb => ({\n    id: fb.PortfolioID,\n    title: fb.FBTitle,\n    thumbnail: fb.TnImageSrc || 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop',\n    isInspiration: true\n  }));\n\n  const handleCreateNew = () => {\n    setIsCreateModalOpen(true);\n  };\n\n  const handleCloseModal = () => {\n    setIsCreateModalOpen(false);\n  };\n\n  const handleCreateFlipbook = async (title: string) => {\n    try {\n      const portfolioId = await createFlipbook({ title });\n      if (portfolioId) {\n        console.log('Flipbook created successfully with ID:', portfolioId);\n        setIsCreateModalOpen(false);\n        // Navigate to flipbook editor if needed\n        // window.location.href = `/Flipbook/Pagemanager?PortFolioID=${portfolioId}`;\n      }\n    } catch (err) {\n      console.error('Failed to create flipbook:', err);\n    }\n  };\n\n  const handleCopyInspiration = async (flipbook: Flipbook) => {\n    try {\n      const portfolioId = await copyInspirationFlipbook({\n        portfolioId: flipbook.id,\n        title: `Copy of ${flipbook.title}`\n      });\n      if (portfolioId) {\n        console.log('Inspiration flipbook copied successfully with ID:', portfolioId);\n        // Navigate to flipbook editor if needed\n        // window.location.href = `/Flipbook/Pagemanager?PortFolioID=${portfolioId}`;\n      }\n    } catch (err) {\n      console.error('Failed to copy inspiration flipbook:', err);\n    }\n  };\n\n  return (\n    <LandingPageContainer>\n      <Header />\n      <MainContent>\n        <Sidebar />\n        <ContentArea>\n          <Section>\n            <SectionTitle>My Flipbooks</SectionTitle>\n            <FlipbookGrid\n              flipbooks={myFlipbooks}\n              showCreateNew={true}\n              onCreateNew={handleCreateNew}\n              onFlipbookClick={(flipbook) => console.log('Open flipbook:', flipbook)}\n              onFlipbookEdit={(flipbook) => console.log('Edit flipbook:', flipbook)}\n            />\n          </Section>\n\n          <Section>\n            <SectionTitle>Inspiration</SectionTitle>\n            <FlipbookGrid\n              flipbooks={inspirationFlipbooksFormatted}\n              onFlipbookClick={(flipbook) => console.log('Preview flipbook:', flipbook)}\n              onFlipbookCopy={handleCopyInspiration}\n            />\n          </Section>\n        </ContentArea>\n      </MainContent>\n      \n      <CertificationBadge />\n      \n      {isCreateModalOpen && (\n        <CreateFlipbookModal\n          onClose={handleCloseModal}\n          onCreate={handleCreateFlipbook}\n        />\n      )}\n    </LandingPageContainer>\n  );\n};\n\nexport default LandingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,OAAO,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,oBAAoB,GAAGT,MAAM,CAACU,GAAG;AACvC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,oBAAoB;AAO1B,MAAMG,WAAW,GAAGZ,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAJID,WAAW;AAMjB,MAAME,WAAW,GAAGd,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GALID,WAAW;AAOjB,MAAME,OAAO,GAAGhB,MAAM,CAACU,GAAG;AAC1B;AACA,CAAC;AAACO,GAAA,GAFID,OAAO;AAIb,MAAME,YAAY,GAAGlB,MAAM,CAACmB,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,YAAY;AAWlB,MAAMG,kBAAkB,GAAGrB,MAAM,CAACU,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GAVID,kBAAkB;AAmBxB,MAAME,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM;IAAE4B,IAAI;IAAEC;EAAgB,CAAC,GAAGtB,OAAO,CAAC,CAAC;EAC3C,MAAM;IACJuB,aAAa;IACbC,oBAAoB;IACpBC,OAAO;IACPC,KAAK;IACLC,cAAc;IACdC;EACF,CAAC,GAAG7B,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM8B,WAAuB,GAAGN,aAAa,CAACO,GAAG,CAACC,EAAE,KAAK;IACvDC,EAAE,EAAED,EAAE,CAACE,WAAW;IAClBC,KAAK,EAAEH,EAAE,CAACI,cAAc,IAAI,UAAU;IACtCC,SAAS,EAAEL,EAAE,CAACM,aAAa,IAAI,mFAAmF;IAClHC,aAAa,EAAE;EACjB,CAAC,CAAC,CAAC;EAEH,MAAMC,6BAAyC,GAAGf,oBAAoB,CAACM,GAAG,CAACC,EAAE,KAAK;IAChFC,EAAE,EAAED,EAAE,CAACE,WAAW;IAClBC,KAAK,EAAEH,EAAE,CAACS,OAAO;IACjBJ,SAAS,EAAEL,EAAE,CAACU,UAAU,IAAI,mFAAmF;IAC/GH,aAAa,EAAE;EACjB,CAAC,CAAC,CAAC;EAEH,MAAMI,eAAe,GAAGA,CAAA,KAAM;IAC5BtB,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMuB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BvB,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMwB,oBAAoB,GAAG,MAAOV,KAAa,IAAK;IACpD,IAAI;MACF,MAAMW,WAAW,GAAG,MAAMlB,cAAc,CAAC;QAAEO;MAAM,CAAC,CAAC;MACnD,IAAIW,WAAW,EAAE;QACfC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEF,WAAW,CAAC;QAClEzB,oBAAoB,CAAC,KAAK,CAAC;QAC3B;QACA;MACF;IACF,CAAC,CAAC,OAAO4B,GAAG,EAAE;MACZF,OAAO,CAACpB,KAAK,CAAC,4BAA4B,EAAEsB,GAAG,CAAC;IAClD;EACF,CAAC;EAED,MAAMC,qBAAqB,GAAG,MAAOC,QAAkB,IAAK;IAC1D,IAAI;MACF,MAAML,WAAW,GAAG,MAAMjB,uBAAuB,CAAC;QAChDiB,WAAW,EAAEK,QAAQ,CAAClB,EAAE;QACxBE,KAAK,EAAE,WAAWgB,QAAQ,CAAChB,KAAK;MAClC,CAAC,CAAC;MACF,IAAIW,WAAW,EAAE;QACfC,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEF,WAAW,CAAC;QAC7E;QACA;MACF;IACF,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZF,OAAO,CAACpB,KAAK,CAAC,sCAAsC,EAAEsB,GAAG,CAAC;IAC5D;EACF,CAAC;EAED,oBACE9C,OAAA,CAACC,oBAAoB;IAAAgD,QAAA,gBACnBjD,OAAA,CAACP,MAAM;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVrD,OAAA,CAACI,WAAW;MAAA6C,QAAA,gBACVjD,OAAA,CAACN,OAAO;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXrD,OAAA,CAACM,WAAW;QAAA2C,QAAA,gBACVjD,OAAA,CAACQ,OAAO;UAAAyC,QAAA,gBACNjD,OAAA,CAACU,YAAY;YAAAuC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACzCrD,OAAA,CAACL,YAAY;YACX2D,SAAS,EAAE3B,WAAY;YACvB4B,aAAa,EAAE,IAAK;YACpBC,WAAW,EAAEhB,eAAgB;YAC7BiB,eAAe,EAAGT,QAAQ,IAAKJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEG,QAAQ,CAAE;YACvEU,cAAc,EAAGV,QAAQ,IAAKJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEG,QAAQ;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAEVrD,OAAA,CAACQ,OAAO;UAAAyC,QAAA,gBACNjD,OAAA,CAACU,YAAY;YAAAuC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACxCrD,OAAA,CAACL,YAAY;YACX2D,SAAS,EAAEjB,6BAA8B;YACzCoB,eAAe,EAAGT,QAAQ,IAAKJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEG,QAAQ,CAAE;YAC1EW,cAAc,EAAEZ;UAAsB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEdrD,OAAA,CAACa,kBAAkB;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAErBpC,iBAAiB,iBAChBjB,OAAA,CAACJ,mBAAmB;MAClBgE,OAAO,EAAEnB,gBAAiB;MAC1BoB,QAAQ,EAAEnB;IAAqB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACmB,CAAC;AAE3B,CAAC;AAACrC,EAAA,CAzGID,WAAqB;EAAA,QAISjB,OAAO,EAQrCD,YAAY;AAAA;AAAAiE,GAAA,GAZZ/C,WAAqB;AA2G3B,eAAeA,WAAW;AAAC,IAAAZ,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAgD,GAAA;AAAAC,YAAA,CAAA5D,EAAA;AAAA4D,YAAA,CAAA1D,GAAA;AAAA0D,YAAA,CAAAxD,GAAA;AAAAwD,YAAA,CAAAtD,GAAA;AAAAsD,YAAA,CAAAnD,GAAA;AAAAmD,YAAA,CAAAjD,GAAA;AAAAiD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}