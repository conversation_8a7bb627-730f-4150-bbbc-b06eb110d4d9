/* Front Page - Exact Match to Screenshot 2 */

.front-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  position: relative;
}

/* Page Header - Top Section */
.page-header {
  text-align: center;
  padding: 20px 0;
  background: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  z-index: 10;
}

.pages-title {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 400;
  color: #999999;
  letter-spacing: 3px;
  text-transform: uppercase;
}

.flipbook-title {
  margin-bottom: 0;
}

.title-editor .text-editor {
  font-size: 24px !important;
  font-weight: 600 !important;
  text-align: center;
  border: none;
  background: transparent;
  color: #333333;
  min-height: auto;
  padding: 5px 20px;
}

/* Main Content Area - Fills Rest of Page */
.main-content {
  flex: 1;
  position: relative;
  cursor: pointer;
  overflow: hidden;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
}


/* Decorative Border Pattern - Matching Screenshot */
.decorative-border-pattern {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  background-image:
    /* Corner decorations */
    radial-gradient(circle at 0% 0%, rgba(139, 93, 186, 0.8) 0%, transparent 15%),
    radial-gradient(circle at 100% 0%, rgba(139, 93, 186, 0.8) 0%, transparent 15%),
    radial-gradient(circle at 0% 100%, rgba(139, 93, 186, 0.8) 0%, transparent 15%),
    radial-gradient(circle at 100% 100%, rgba(139, 93, 186, 0.8) 0%, transparent 15%),
    /* Border pattern */
    linear-gradient(0deg, transparent 48%, rgba(139, 93, 186, 0.3) 49%, rgba(139, 93, 186, 0.3) 51%, transparent 52%),
    linear-gradient(90deg, transparent 48%, rgba(139, 93, 186, 0.3) 49%, rgba(139, 93, 186, 0.3) 51%, transparent 52%);
  background-size:
    50px 50px, 50px 50px, 50px 50px, 50px 50px,
    100% 100%, 100% 100%;
  background-position:
    top left, top right, bottom left, bottom right,
    center, center;
  border: 2px solid rgba(139, 93, 186, 0.6);
  border-radius: 8px;
  pointer-events: none;
  z-index: 1;
}

/* Center Content - Name and Title */
.center-content {
  text-align: center;
  z-index: 5;
  position: relative;
}

.name-section {
  margin-bottom: 20px;
}

.main-name-editor .text-editor {
  font-size: 48px !important;
  font-weight: bold !important;
  text-align: center;
  color: #2c3e50;
  border: none;
  background: transparent;
  min-height: auto;
  padding: 10px 20px;
  text-transform: uppercase;
  letter-spacing: 4px;
}

.profession-title {
  font-size: 18px;
  font-weight: 400;
  color: #2c3e50;
  letter-spacing: 6px;
  text-transform: uppercase;
  margin-top: 20px;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.upload-placeholder p {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
}

.upload-placeholder small {
  font-size: 14px;
  color: #888;
}

/* Upload Hint */
.upload-hint {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.95);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 11px;
  color: #666;
  border: 1px solid #ddd;
  z-index: 6;
  max-width: 80%;
  text-align: center;
  line-height: 1.3;
}

.main-content:hover .upload-hint {
  background: rgba(248, 244, 255, 0.95);
  border-color: #8B5DBA;
  color: #8B5DBA;
}

/* Navigation Controls */
.navigation-controls {
  position: absolute;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.next-page-btn {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #8B5DBA 0%, #7A4BA8 100%);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(139, 93, 186, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.next-page-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(139, 93, 186, 0.6);
}

/* Background Image Styling */
.main-content[style*="background-image"] .decorative-border-pattern {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(2px);
}

.main-content[style*="background-image"] .center-content {
  background: rgba(255, 255, 255, 0.95);
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-name-editor .text-editor {
    font-size: 32px !important;
    letter-spacing: 2px;
  }
  
  .profession-title {
    font-size: 18px;
    letter-spacing: 4px;
  }
  
  .decorative-border-pattern {
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
  }
  
  .navigation-controls {
    right: 20px;
  }
  
  .next-page-btn {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
}

.front-page-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: 600px;
  margin: 0 auto;
}

.title-section,
.subtitle-section {
  width: 100%;
  margin-bottom: 24px;
}

.title-editor .text-editor {
  min-height: 60px;
  font-size: 32px !important;
  font-weight: bold;
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  border: 2px solid rgba(139, 93, 186, 0.3);
}

.subtitle-editor .text-editor {
  min-height: 50px;
  font-size: 18px !important;
  text-align: center;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(3px);
  border: 2px solid rgba(139, 93, 186, 0.2);
}

.navigation-controls {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}

.next-btn {
  width: 60px;
  height: 60px;
  background: #8B5DBA;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(139, 93, 186, 0.3);
}

.next-btn:hover {
  background: #7A4BA8;
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 6px 16px rgba(139, 93, 186, 0.4);
}

.arrow-icon {
  transition: transform 0.2s ease;
}

.next-btn:hover .arrow-icon {
  transform: translateX(2px);
}

.business-card-area {
  position: absolute;
  bottom: 40px;
  right: 40px;
  width: 300px;
  z-index: 2;
}

.business-card-placeholder {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  text-align: center;
}

.gear-icons {
  position: relative;
  height: 40px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.gear {
  font-size: 20px;
  animation: rotate 3s linear infinite;
}

.gear-2 {
  animation-delay: -1s;
  animation-direction: reverse;
}

.gear-3 {
  animation-delay: -2s;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.business-card-placeholder h3 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  letter-spacing: 1px;
}

.card-fields {
  margin-bottom: 16px;
}

.card-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.card-field label {
  font-weight: 500;
  color: #666;
}

.card-field span {
  color: #333;
}

.lets-go-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;
  letter-spacing: 0.5px;
}

.lets-go-btn:hover {
  background: #218838;
}

/* Page Header Section - Matching Screenshot 2 */
.page-header {
  text-align: center;
  padding: 20px 0;
  background: white;
  border-bottom: 1px solid #e0e0e0;
}

.pages-title {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 400;
  color: #999;
  letter-spacing: 3px;
  text-transform: uppercase;
}

.flipbook-title {
  margin-bottom: 10px;
}

.title-editor .text-editor {
  font-size: 20px !important;
  font-weight: 600 !important;
  text-align: center;
  border: none;
  background: transparent;
  color: #333;
  min-height: auto;
}

/* Canvas Area - Main Content */
.canvas-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  position: relative;
  background: #f8f9fa;
}

.flipbook-canvas {
  width: 600px;
  height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.decorative-frame {
  position: absolute;
  top: 15px;
  left: 15px;
  right: 15px;
  bottom: 15px;
  border: 3px solid #8B5DBA;
  border-radius: 8px;
  pointer-events: none;
  z-index: 2;
}

.content-section {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.main-title-area {
  text-align: center;
  z-index: 3;
  position: relative;
}

.main-name-editor .text-editor {
  font-size: 32px !important;
  font-weight: bold !important;
  text-align: center;
  color: #2c3e50;
  border: none;
  background: transparent;
  min-height: auto;
  margin-bottom: 10px;
}

.profession-line {
  text-align: center;
}

.profession-line span {
  font-size: 18px;
  font-weight: 600;
  color: #34495e;
  letter-spacing: 2px;
}

/* Remove old business card form styles - moved to ContactDetails modal */

/* Navigation Arrow */
.navigation-arrow {
  position: absolute;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.next-arrow-btn {
  width: 50px;
  height: 50px;
  background: #8B5DBA;
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(139, 93, 186, 0.3);
}

.next-arrow-btn:hover {
  background: #7A4BA8;
  transform: scale(1.1);
}

/* Image Upload Area - Hidden by default, shown when no background */
.image-upload-area {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.upload-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #999;
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 8px;
  border: 2px dashed #ccc;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-placeholder:hover {
  border-color: #8B5DBA;
  background: rgba(248, 244, 255, 0.95);
}

/* Responsive Design */
@media (max-width: 768px) {
  .canvas-area {
    padding: 20px;
  }
  
  .flipbook-canvas {
    width: 100%;
    max-width: 500px;
    height: 350px;
  }
  
  .main-name-editor .text-editor {
    font-size: 24px !important;
  }
  
  .navigation-arrow {
    position: static;
    text-align: center;
    margin-top: 20px;
    transform: none;
  }
}
