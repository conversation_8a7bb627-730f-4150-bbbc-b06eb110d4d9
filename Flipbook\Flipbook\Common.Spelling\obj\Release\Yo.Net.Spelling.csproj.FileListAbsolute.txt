C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\Hunspellx64.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\Hunspellx86.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\Yo.Net.Spelling.dll.config
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\Yo.Net.Spelling.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\Yo.Net.Spelling.pdb
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\csc.exe
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\csc.exe.config
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\csc.rsp
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\csi.exe
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\csi.rsp
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.Scripting.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\Microsoft.CSharp.Core.targets
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\Microsoft.DiaSymReader.Native.x86.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\Microsoft.VisualBasic.Core.targets
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\System.AppContext.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\System.Collections.Immutable.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\System.IO.FileSystem.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\System.Reflection.Metadata.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\vbc.exe
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\vbc.exe.config
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\vbc.rsp
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\VBCSCompiler.exe
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\roslyn\VBCSCompiler.exe.config
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\NHunspell.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\bin\Release\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\obj\Release\Yo.Net.Spelling.Common.Spell.Resources.resources
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\obj\Release\Yo.Net.Spelling.csproj.GenerateResource.cache
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\obj\Release\Yo.Net.Spelling.csproj.CopyComplete
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\obj\Release\Yo.Net.Spelling.dll
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\obj\Release\Yo.Net.Spelling.pdb
C:\Users\<USER>\Source\Workspaces\TradeWorks\Common.Spelling\obj\Release\Yo.Net.Spelling.csprojAssemblyReference.cache
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\Hunspellx64.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\Hunspellx86.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\Yo.Net.Spelling.dll.config
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\Yo.Net.Spelling.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\Yo.Net.Spelling.pdb
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\csc.exe
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\csc.exe.config
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\csc.rsp
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\csi.exe
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\csi.rsp
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.Scripting.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CSharp.Core.targets
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.DiaSymReader.Native.x86.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.VisualBasic.Core.targets
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\System.AppContext.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\System.Collections.Immutable.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\System.IO.FileSystem.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\System.Reflection.Metadata.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\vbc.exe
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\vbc.exe.config
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\vbc.rsp
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\VBCSCompiler.exe
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\VBCSCompiler.exe.config
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\NHunspell.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\bin\Release\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.csprojAssemblyReference.cache
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.Common.Spell.Resources.resources
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.csproj.GenerateResource.cache
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.csproj.CopyComplete
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.dll
C:\Users\<USER>\Source\Workspaces\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.pdb
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\Hunspellx64.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\Hunspellx86.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\Yo.Net.Spelling.dll.config
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\Yo.Net.Spelling.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\Yo.Net.Spelling.pdb
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\csc.exe
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\csc.exe.config
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\csc.rsp
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\csi.exe
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\csi.rsp
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.CSharp.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.Scripting.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CSharp.Core.targets
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.DiaSymReader.Native.x86.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.VisualBasic.Core.targets
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\System.AppContext.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\System.Collections.Immutable.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\System.Diagnostics.StackTrace.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\System.IO.FileSystem.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\System.IO.FileSystem.Primitives.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\System.Reflection.Metadata.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\vbc.exe
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\vbc.exe.config
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\vbc.rsp
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\VBCSCompiler.exe
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\roslyn\VBCSCompiler.exe.config
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\NHunspell.dll
C:\Workspaces\Flipbook\Common.Spelling\bin\Release\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
C:\Workspaces\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.Common.Spell.Resources.resources
C:\Workspaces\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.csproj.GenerateResource.cache
C:\Workspaces\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.csproj.CopyComplete
C:\Workspaces\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.dll
C:\Workspaces\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.pdb
C:\Workspaces\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.csproj.CoreCompileInputs.cache
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\Hunspellx64.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\Hunspellx86.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\Yo.Net.Spelling.dll.config
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\Yo.Net.Spelling.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\Yo.Net.Spelling.pdb
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\csc.exe
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\csc.exe.config
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\csc.rsp
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\csi.exe
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\csi.rsp
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.CSharp.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.Scripting.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.CSharp.Core.targets
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.DiaSymReader.Native.x86.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\Microsoft.VisualBasic.Core.targets
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\System.AppContext.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\System.Collections.Immutable.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\System.Diagnostics.StackTrace.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\System.IO.FileSystem.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\System.IO.FileSystem.Primitives.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\System.Reflection.Metadata.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\vbc.exe
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\vbc.exe.config
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\vbc.rsp
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\VBCSCompiler.exe
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\roslyn\VBCSCompiler.exe.config
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\NHunspell.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\bin\Release\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.csproj.AssemblyReference.cache
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.Common.Spell.Resources.resources
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.csproj.GenerateResource.cache
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.csproj.CoreCompileInputs.cache
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\obj\Release\Yo.Net.S.DD18E0ED.Up2Date
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.dll
D:\projects\tradeworks\franklinreports\Flipbook\Flipbook\Flipbook\Common.Spelling\obj\Release\Yo.Net.Spelling.pdb
