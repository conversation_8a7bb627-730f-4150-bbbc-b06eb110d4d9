!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react-dom/test-utils"),require("react"),require("react-dom"),require("react-dom/client")):"function"==typeof define&&define.amd?define(["exports","react-dom/test-utils","react","react-dom","react-dom/client"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).TestingLibraryReact={},e.<PERSON>act<PERSON>estUtils,e.<PERSON>,e.ReactDOM,e.ReactDOMClient)}(this,(function(e,t,r,n,a){"use strict";function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function l(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}function i(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(r){if("default"!==r&&!(r in e)){var n=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,n.get?n:{enumerable:!0,get:function(){return t[r]}})}}))})),Object.freeze(e)}var u=l(t),s=l(r),d=o(n),c=l(a);const p=u.act;function m(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}function f(e){m().IS_REACT_ACT_ENVIRONMENT=e}function b(){return m().IS_REACT_ACT_ENVIRONMENT}const v=(y=p,e=>{const t=b();f(!0);try{let r=!1;const n=y((()=>{const t=e();return null!==t&&"object"==typeof t&&"function"==typeof t.then&&(r=!0),t}));return r?{then:(e,r)=>{n.then((r=>{f(t),e(r)}),(e=>{f(t),r(e)}))}}:(f(t),n)}catch(e){throw f(t),e}});var y,h={},g={exports:{}};!function(e){const t=function(e){return void 0===e&&(e=0),t=>`[${38+e};5;${t}m`},r=function(e){return void 0===e&&(e=0),(t,r,n)=>`[${38+e};2;${t};${r};${n}m`};Object.defineProperty(e,"exports",{enumerable:!0,get:function(){const e=new Map,n={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};n.color.gray=n.color.blackBright,n.bgColor.bgGray=n.bgColor.bgBlackBright,n.color.grey=n.color.blackBright,n.bgColor.bgGrey=n.bgColor.bgBlackBright;for(const[t,r]of Object.entries(n)){for(const[t,a]of Object.entries(r))n[t]={open:`[${a[0]}m`,close:`[${a[1]}m`},r[t]=n[t],e.set(a[0],a[1]);Object.defineProperty(n,t,{value:r,enumerable:!1})}return Object.defineProperty(n,"codes",{value:e,enumerable:!1}),n.color.close="[39m",n.bgColor.close="[49m",n.color.ansi256=t(),n.color.ansi16m=r(),n.bgColor.ansi256=t(10),n.bgColor.ansi16m=r(10),Object.defineProperties(n,{rgbToAnsi256:{value:(e,t,r)=>e===t&&t===r?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(t/255*5)+Math.round(r/255*5),enumerable:!1},hexToRgb:{value:e=>{const t=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(e.toString(16));if(!t)return[0,0,0];let{colorString:r}=t.groups;3===r.length&&(r=r.split("").map((e=>e+e)).join(""));const n=Number.parseInt(r,16);return[n>>16&255,n>>8&255,255&n]},enumerable:!1},hexToAnsi256:{value:e=>n.rgbToAnsi256(...n.hexToRgb(e)),enumerable:!1}}),n}})}(g);var C={};Object.defineProperty(C,"__esModule",{value:!0}),C.printIteratorEntries=function(e,t,r,n,a,o,l){void 0===l&&(l=": ");let i="",u=e.next();if(!u.done){i+=t.spacingOuter;const s=r+t.indent;for(;!u.done;){const r=o(u.value[0],t,s,n,a),d=o(u.value[1],t,s,n,a);i+=s+r+l+d,u=e.next(),u.done?t.min||(i+=","):i+=","+t.spacingInner}i+=t.spacingOuter+r}return i},C.printIteratorValues=function(e,t,r,n,a,o){let l="",i=e.next();if(!i.done){l+=t.spacingOuter;const u=r+t.indent;for(;!i.done;)l+=u+o(i.value,t,u,n,a),i=e.next(),i.done?t.min||(l+=","):l+=","+t.spacingInner;l+=t.spacingOuter+r}return l},C.printListItems=function(e,t,r,n,a,o){let l="";if(e.length){l+=t.spacingOuter;const i=r+t.indent;for(let r=0;r<e.length;r++)l+=i,r in e&&(l+=o(e[r],t,i,n,a)),r<e.length-1?l+=","+t.spacingInner:t.min||(l+=",");l+=t.spacingOuter+r}return l},C.printObjectProperties=function(e,t,r,n,a,o){let l="";const i=((e,t)=>{const r=Object.keys(e).sort(t);Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach((t=>{Object.getOwnPropertyDescriptor(e,t).enumerable&&r.push(t)}));return r})(e,t.compareKeys);if(i.length){l+=t.spacingOuter;const u=r+t.indent;for(let r=0;r<i.length;r++){const s=i[r],d=o(s,t,u,n,a),c=o(e[s],t,u,n,a);l+=u+d+": "+c,r<i.length-1?l+=","+t.spacingInner:t.min||(l+=",")}l+=t.spacingOuter+r}return l};var q={};Object.defineProperty(q,"__esModule",{value:!0}),q.test=q.serialize=q.default=void 0;var P=C,E="undefined"!=typeof globalThis?globalThis:void 0!==E?E:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),w=E["jest-symbol-do-not-touch"]||E.Symbol;const x="function"==typeof w&&w.for?w.for("jest.asymmetricMatcher"):1267621,R=" ",O=(e,t,r,n,a,o)=>{const l=e.toString();return"ArrayContaining"===l||"ArrayNotContaining"===l?++n>t.maxDepth?"["+l+"]":l+R+"["+(0,P.printListItems)(e.sample,t,r,n,a,o)+"]":"ObjectContaining"===l||"ObjectNotContaining"===l?++n>t.maxDepth?"["+l+"]":l+R+"{"+(0,P.printObjectProperties)(e.sample,t,r,n,a,o)+"}":"StringMatching"===l||"StringNotMatching"===l||"StringContaining"===l||"StringNotContaining"===l?l+R+o(e.sample,t,r,n,a):e.toAsymmetricMatcher()};q.serialize=O;const T=e=>e&&e.$$typeof===x;q.test=T;var _={serialize:O,test:T};q.default=_;var M={};Object.defineProperty(M,"__esModule",{value:!0}),M.test=M.serialize=M.default=void 0;var A=S((function(e){let{onlyFirst:t=!1}=void 0===e?{}:e;const r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(r,t?void 0:"g")})),j=S(g.exports);function S(e){return e&&e.__esModule?e:{default:e}}const B=e=>"string"==typeof e&&!!e.match((0,A.default)());M.test=B;const I=(e,t,r,n,a,o)=>o(e.replace((0,A.default)(),(e=>{switch(e){case j.default.red.close:case j.default.green.close:case j.default.cyan.close:case j.default.gray.close:case j.default.white.close:case j.default.yellow.close:case j.default.bgRed.close:case j.default.bgGreen.close:case j.default.bgYellow.close:case j.default.inverse.close:case j.default.dim.close:case j.default.bold.close:case j.default.reset.open:case j.default.reset.close:return"</>";case j.default.red.open:return"<red>";case j.default.green.open:return"<green>";case j.default.cyan.open:return"<cyan>";case j.default.gray.open:return"<gray>";case j.default.white.open:return"<white>";case j.default.yellow.open:return"<yellow>";case j.default.bgRed.open:return"<bgRed>";case j.default.bgGreen.open:return"<bgGreen>";case j.default.bgYellow.open:return"<bgYellow>";case j.default.inverse.open:return"<inverse>";case j.default.dim.open:return"<dim>";case j.default.bold.open:return"<bold>";default:return""}})),t,r,n,a);M.serialize=I;var N={serialize:I,test:B};M.default=N;var k={};Object.defineProperty(k,"__esModule",{value:!0}),k.test=k.serialize=k.default=void 0;var F=C;const L=["DOMStringMap","NamedNodeMap"],U=/^(HTML\w*Collection|NodeList)$/,D=e=>{return e&&e.constructor&&!!e.constructor.name&&(t=e.constructor.name,-1!==L.indexOf(t)||U.test(t));var t};k.test=D;const H=(e,t,r,n,a,o)=>{const l=e.constructor.name;return++n>t.maxDepth?"["+l+"]":(t.min?"":l+" ")+(-1!==L.indexOf(l)?"{"+(0,F.printObjectProperties)((e=>"NamedNodeMap"===e.constructor.name)(e)?Array.from(e).reduce(((e,t)=>(e[t.name]=t.value,e)),{}):{...e},t,r,n,a,o)+"}":"["+(0,F.printListItems)(Array.from(e),t,r,n,a,o)+"]")};k.serialize=H;var z={serialize:H,test:D};k.default=z;var V={},$={},W={};Object.defineProperty(W,"__esModule",{value:!0}),W.default=function(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")},Object.defineProperty($,"__esModule",{value:!0}),$.printText=$.printProps=$.printElementAsLeaf=$.printElement=$.printComment=$.printChildren=void 0;var G,Q=(G=W)&&G.__esModule?G:{default:G};$.printProps=(e,t,r,n,a,o,l)=>{const i=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let d=l(s,r,i,a,o);return"string"!=typeof s&&(-1!==d.indexOf("\n")&&(d=r.spacingOuter+i+d+r.spacingOuter+n),d="{"+d+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+d+u.value.close})).join("")};$.printChildren=(e,t,r,n,a,o)=>e.map((e=>t.spacingOuter+r+("string"==typeof e?J(e,t):o(e,t,r,n,a)))).join("");const J=(e,t)=>{const r=t.colors.content;return r.open+(0,Q.default)(e)+r.close};$.printText=J;$.printComment=(e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+(0,Q.default)(e)+"--\x3e"+r.close};$.printElement=(e,t,r,n,a)=>{const o=n.colors.tag;return o.open+"<"+e+(t&&o.close+t+n.spacingOuter+a+o.open)+(r?">"+o.close+r+n.spacingOuter+a+o.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+o.close};$.printElementAsLeaf=(e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close},Object.defineProperty(V,"__esModule",{value:!0}),V.test=V.serialize=V.default=void 0;var K=$;const X=/^((HTML|SVG)\w*)?Element$/,Y=e=>{var t;return(null==e||null===(t=e.constructor)||void 0===t?void 0:t.name)&&(e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,a="string"==typeof n&&n.includes("-")||(e=>{try{return"function"==typeof e.hasAttribute&&e.hasAttribute("is")}catch{return!1}})(e);return 1===r&&(X.test(t)||a)||3===r&&"Text"===t||8===r&&"Comment"===t||11===r&&"DocumentFragment"===t})(e)};function Z(e){return 11===e.nodeType}V.test=Y;const ee=(e,t,r,n,a,o)=>{if(function(e){return 3===e.nodeType}(e))return(0,K.printText)(e.data,t);if(function(e){return 8===e.nodeType}(e))return(0,K.printComment)(e.data,t);const l=Z(e)?"DocumentFragment":e.tagName.toLowerCase();return++n>t.maxDepth?(0,K.printElementAsLeaf)(l,t):(0,K.printElement)(l,(0,K.printProps)(Z(e)?[]:Array.from(e.attributes).map((e=>e.name)).sort(),Z(e)?{}:Array.from(e.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),t,r+t.indent,n,a,o),(0,K.printChildren)(Array.prototype.slice.call(e.childNodes||e.children),t,r+t.indent,n,a,o),t,r)};V.serialize=ee;var te={serialize:ee,test:Y};V.default=te;var re={};Object.defineProperty(re,"__esModule",{value:!0}),re.test=re.serialize=re.default=void 0;var ne=C;const ae="@@__IMMUTABLE_ORDERED__@@",oe=e=>"Immutable."+e,le=e=>"["+e+"]",ie=" ";const ue=(e,t,r,n,a,o,l)=>++n>t.maxDepth?le(oe(l)):oe(l)+ie+"["+(0,ne.printIteratorValues)(e.values(),t,r,n,a,o)+"]",se=(e,t,r,n,a,o)=>e["@@__IMMUTABLE_MAP__@@"]?((e,t,r,n,a,o,l)=>++n>t.maxDepth?le(oe(l)):oe(l)+ie+"{"+(0,ne.printIteratorEntries)(e.entries(),t,r,n,a,o)+"}")(e,t,r,n,a,o,e[ae]?"OrderedMap":"Map"):e["@@__IMMUTABLE_LIST__@@"]?ue(e,t,r,n,a,o,"List"):e["@@__IMMUTABLE_SET__@@"]?ue(e,t,r,n,a,o,e[ae]?"OrderedSet":"Set"):e["@@__IMMUTABLE_STACK__@@"]?ue(e,t,r,n,a,o,"Stack"):e["@@__IMMUTABLE_SEQ__@@"]?((e,t,r,n,a,o)=>{const l=oe("Seq");return++n>t.maxDepth?le(l):e["@@__IMMUTABLE_KEYED__@@"]?l+ie+"{"+(e._iter||e._object?(0,ne.printIteratorEntries)(e.entries(),t,r,n,a,o):"…")+"}":l+ie+"["+(e._iter||e._array||e._collection||e._iterable?(0,ne.printIteratorValues)(e.values(),t,r,n,a,o):"…")+"]"})(e,t,r,n,a,o):((e,t,r,n,a,o)=>{const l=oe(e._name||"Record");return++n>t.maxDepth?le(l):l+ie+"{"+(0,ne.printIteratorEntries)(function(e){let t=0;return{next(){if(t<e._keys.length){const r=e._keys[t++];return{done:!1,value:[r,e.get(r)]}}return{done:!0,value:void 0}}}}(e),t,r,n,a,o)+"}"})(e,t,r,n,a,o);re.serialize=se;const de=e=>e&&(!0===e["@@__IMMUTABLE_ITERABLE__@@"]||!0===e["@@__IMMUTABLE_RECORD__@@"]);re.test=de;var ce={serialize:se,test:de};re.default=ce;var pe={},me={exports:{}},fe={},be=60103,ve=60106,ye=60107,he=60108,ge=60114,Ce=60109,qe=60110,Pe=60112,Ee=60113,we=60120,xe=60115,Re=60116,Oe=60121,Te=60122,_e=60117,Me=60129,Ae=60131;if("function"==typeof Symbol&&Symbol.for){var je=Symbol.for;be=je("react.element"),ve=je("react.portal"),ye=je("react.fragment"),he=je("react.strict_mode"),ge=je("react.profiler"),Ce=je("react.provider"),qe=je("react.context"),Pe=je("react.forward_ref"),Ee=je("react.suspense"),we=je("react.suspense_list"),xe=je("react.memo"),Re=je("react.lazy"),Oe=je("react.block"),Te=je("react.server.block"),_e=je("react.fundamental"),Me=je("react.debug_trace_mode"),Ae=je("react.legacy_hidden")}function Se(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case be:switch(e=e.type){case ye:case ge:case he:case Ee:case we:return e;default:switch(e=e&&e.$$typeof){case qe:case Pe:case Re:case xe:case Ce:return e;default:return t}}case ve:return t}}}var Be=Ce,Ie=be,Ne=Pe,ke=ye,Fe=Re,Le=xe,Ue=ve,De=ge,He=he,ze=Ee;fe.ContextConsumer=qe,fe.ContextProvider=Be,fe.Element=Ie,fe.ForwardRef=Ne,fe.Fragment=ke,fe.Lazy=Fe,fe.Memo=Le,fe.Portal=Ue,fe.Profiler=De,fe.StrictMode=He,fe.Suspense=ze,fe.isAsyncMode=function(){return!1},fe.isConcurrentMode=function(){return!1},fe.isContextConsumer=function(e){return Se(e)===qe},fe.isContextProvider=function(e){return Se(e)===Ce},fe.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===be},fe.isForwardRef=function(e){return Se(e)===Pe},fe.isFragment=function(e){return Se(e)===ye},fe.isLazy=function(e){return Se(e)===Re},fe.isMemo=function(e){return Se(e)===xe},fe.isPortal=function(e){return Se(e)===ve},fe.isProfiler=function(e){return Se(e)===ge},fe.isStrictMode=function(e){return Se(e)===he},fe.isSuspense=function(e){return Se(e)===Ee},fe.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===ye||e===ge||e===Me||e===he||e===Ee||e===we||e===Ae||"object"==typeof e&&null!==e&&(e.$$typeof===Re||e.$$typeof===xe||e.$$typeof===Ce||e.$$typeof===qe||e.$$typeof===Pe||e.$$typeof===_e||e.$$typeof===Oe||e[0]===Te)},fe.typeOf=Se,me.exports=fe,Object.defineProperty(pe,"__esModule",{value:!0}),pe.test=pe.serialize=pe.default=void 0;var Ve=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=We(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var l=a?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(me.exports),$e=$;function We(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(We=function(e){return e?r:t})(e)}const Ge=function(e,t){return void 0===t&&(t=[]),Array.isArray(e)?e.forEach((e=>{Ge(e,t)})):null!=e&&!1!==e&&t.push(e),t},Qe=e=>{const t=e.type;if("string"==typeof t)return t;if("function"==typeof t)return t.displayName||t.name||"Unknown";if(Ve.isFragment(e))return"React.Fragment";if(Ve.isSuspense(e))return"React.Suspense";if("object"==typeof t&&null!==t){if(Ve.isContextProvider(e))return"Context.Provider";if(Ve.isContextConsumer(e))return"Context.Consumer";if(Ve.isForwardRef(e)){if(t.displayName)return t.displayName;const e=t.render.displayName||t.render.name||"";return""!==e?"ForwardRef("+e+")":"ForwardRef"}if(Ve.isMemo(e)){const e=t.displayName||t.type.displayName||t.type.name||"";return""!==e?"Memo("+e+")":"Memo"}}return"UNDEFINED"},Je=(e,t,r,n,a,o)=>++n>t.maxDepth?(0,$e.printElementAsLeaf)(Qe(e),t):(0,$e.printElement)(Qe(e),(0,$e.printProps)((e=>{const{props:t}=e;return Object.keys(t).filter((e=>"children"!==e&&void 0!==t[e])).sort()})(e),e.props,t,r+t.indent,n,a,o),(0,$e.printChildren)(Ge(e.props.children),t,r+t.indent,n,a,o),t,r);pe.serialize=Je;const Ke=e=>null!=e&&Ve.isElement(e);pe.test=Ke;var Xe={serialize:Je,test:Ke};pe.default=Xe;var Ye={};Object.defineProperty(Ye,"__esModule",{value:!0}),Ye.test=Ye.serialize=Ye.default=void 0;var Ze=$,et="undefined"!=typeof globalThis?globalThis:void 0!==et?et:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),tt=et["jest-symbol-do-not-touch"]||et.Symbol;const rt="function"==typeof tt&&tt.for?tt.for("react.test.json"):245830487,nt=(e,t,r,n,a,o)=>++n>t.maxDepth?(0,Ze.printElementAsLeaf)(e.type,t):(0,Ze.printElement)(e.type,e.props?(0,Ze.printProps)((e=>{const{props:t}=e;return t?Object.keys(t).filter((e=>void 0!==t[e])).sort():[]})(e),e.props,t,r+t.indent,n,a,o):"",e.children?(0,Ze.printChildren)(e.children,t,r+t.indent,n,a,o):"",t,r);Ye.serialize=nt;const at=e=>e&&e.$$typeof===rt;Ye.test=at;var ot={serialize:nt,test:at};Ye.default=ot,Object.defineProperty(h,"__esModule",{value:!0});var lt=h.default=h.DEFAULT_OPTIONS=void 0,it=h.format=Vt,ut=h.plugins=void 0,st=ht(g.exports),dt=C,ct=ht(q),pt=ht(M),mt=ht(k),ft=ht(V),bt=ht(re),vt=ht(pe),yt=ht(Ye);function ht(e){return e&&e.__esModule?e:{default:e}}const gt=Object.prototype.toString,Ct=Date.prototype.toISOString,qt=Error.prototype.toString,Pt=RegExp.prototype.toString,Et=e=>"function"==typeof e.constructor&&e.constructor.name||"Object",wt=/^Symbol\((.*)\)(.*)$/,xt=/\n/gi;class Rt extends Error{constructor(e,t){super(e),this.stack=t,this.name=this.constructor.name}}function Ot(e,t){return t?"[Function "+(e.name||"anonymous")+"]":"[Function]"}function Tt(e){return String(e).replace(wt,"Symbol($1)")}function _t(e){return"["+qt.call(e)+"]"}function Mt(e,t,r,n){if(!0===e||!1===e)return""+e;if(void 0===e)return"undefined";if(null===e)return"null";const a=typeof e;if("number"===a)return function(e){return Object.is(e,-0)?"-0":String(e)}(e);if("bigint"===a)return function(e){return String(`${e}n`)}(e);if("string"===a)return n?'"'+e.replace(/"|\\/g,"\\$&")+'"':'"'+e+'"';if("function"===a)return Ot(e,t);if("symbol"===a)return Tt(e);const o=gt.call(e);return"[object WeakMap]"===o?"WeakMap {}":"[object WeakSet]"===o?"WeakSet {}":"[object Function]"===o||"[object GeneratorFunction]"===o?Ot(e,t):"[object Symbol]"===o?Tt(e):"[object Date]"===o?isNaN(+e)?"Date { NaN }":Ct.call(e):"[object Error]"===o?_t(e):"[object RegExp]"===o?r?Pt.call(e).replace(/[\\^$*+?.()|[\]{}]/g,"\\$&"):Pt.call(e):e instanceof Error?_t(e):null}function At(e,t,r,n,a,o){if(-1!==a.indexOf(e))return"[Circular]";(a=a.slice()).push(e);const l=++n>t.maxDepth,i=t.min;if(t.callToJSON&&!l&&e.toJSON&&"function"==typeof e.toJSON&&!o)return Bt(e.toJSON(),t,r,n,a,!0);const u=gt.call(e);return"[object Arguments]"===u?l?"[Arguments]":(i?"":"Arguments ")+"["+(0,dt.printListItems)(e,t,r,n,a,Bt)+"]":function(e){return"[object Array]"===e||"[object ArrayBuffer]"===e||"[object DataView]"===e||"[object Float32Array]"===e||"[object Float64Array]"===e||"[object Int8Array]"===e||"[object Int16Array]"===e||"[object Int32Array]"===e||"[object Uint8Array]"===e||"[object Uint8ClampedArray]"===e||"[object Uint16Array]"===e||"[object Uint32Array]"===e}(u)?l?"["+e.constructor.name+"]":(i?"":t.printBasicPrototype||"Array"!==e.constructor.name?e.constructor.name+" ":"")+"["+(0,dt.printListItems)(e,t,r,n,a,Bt)+"]":"[object Map]"===u?l?"[Map]":"Map {"+(0,dt.printIteratorEntries)(e.entries(),t,r,n,a,Bt," => ")+"}":"[object Set]"===u?l?"[Set]":"Set {"+(0,dt.printIteratorValues)(e.values(),t,r,n,a,Bt)+"}":l||(e=>"undefined"!=typeof window&&e===window)(e)?"["+Et(e)+"]":(i?"":t.printBasicPrototype||"Object"!==Et(e)?Et(e)+" ":"")+"{"+(0,dt.printObjectProperties)(e,t,r,n,a,Bt)+"}"}function jt(e,t,r,n,a,o){let l;try{l=function(e){return null!=e.serialize}(e)?e.serialize(t,r,n,a,o,Bt):e.print(t,(e=>Bt(e,r,n,a,o)),(e=>{const t=n+r.indent;return t+e.replace(xt,"\n"+t)}),{edgeSpacing:r.spacingOuter,min:r.min,spacing:r.spacingInner},r.colors)}catch(e){throw new Rt(e.message,e.stack)}if("string"!=typeof l)throw new Error(`pretty-format: Plugin must return type "string" but instead returned "${typeof l}".`);return l}function St(e,t){for(let r=0;r<e.length;r++)try{if(e[r].test(t))return e[r]}catch(e){throw new Rt(e.message,e.stack)}return null}function Bt(e,t,r,n,a,o){const l=St(t.plugins,e);if(null!==l)return jt(l,e,t,r,n,a);const i=Mt(e,t.printFunctionName,t.escapeRegex,t.escapeString);return null!==i?i:At(e,t,r,n,a,o)}const It={comment:"gray",content:"reset",prop:"yellow",tag:"cyan",value:"green"},Nt=Object.keys(It),kt={callToJSON:!0,compareKeys:void 0,escapeRegex:!1,escapeString:!0,highlight:!1,indent:2,maxDepth:1/0,min:!1,plugins:[],printBasicPrototype:!0,printFunctionName:!0,theme:It};var Ft=h.DEFAULT_OPTIONS=kt;const Lt=e=>Nt.reduce(((t,r)=>{const n=e.theme&&void 0!==e.theme[r]?e.theme[r]:It[r],a=n&&st.default[n];if(!a||"string"!=typeof a.close||"string"!=typeof a.open)throw new Error(`pretty-format: Option "theme" has a key "${r}" whose value "${n}" is undefined in ansi-styles.`);return t[r]=a,t}),Object.create(null)),Ut=e=>e&&void 0!==e.printFunctionName?e.printFunctionName:kt.printFunctionName,Dt=e=>e&&void 0!==e.escapeRegex?e.escapeRegex:kt.escapeRegex,Ht=e=>e&&void 0!==e.escapeString?e.escapeString:kt.escapeString,zt=e=>{var t,r;return{callToJSON:e&&void 0!==e.callToJSON?e.callToJSON:kt.callToJSON,colors:e&&e.highlight?Lt(e):Nt.reduce(((e,t)=>(e[t]={close:"",open:""},e)),Object.create(null)),compareKeys:e&&"function"==typeof e.compareKeys?e.compareKeys:kt.compareKeys,escapeRegex:Dt(e),escapeString:Ht(e),indent:e&&e.min?"":(r=e&&void 0!==e.indent?e.indent:kt.indent,new Array(r+1).join(" ")),maxDepth:e&&void 0!==e.maxDepth?e.maxDepth:kt.maxDepth,min:e&&void 0!==e.min?e.min:kt.min,plugins:e&&void 0!==e.plugins?e.plugins:kt.plugins,printBasicPrototype:null===(t=null==e?void 0:e.printBasicPrototype)||void 0===t||t,printFunctionName:Ut(e),spacingInner:e&&e.min?" ":"\n",spacingOuter:e&&e.min?"":"\n"}};function Vt(e,t){if(t&&(function(e){if(Object.keys(e).forEach((e=>{if(!kt.hasOwnProperty(e))throw new Error(`pretty-format: Unknown option "${e}".`)})),e.min&&void 0!==e.indent&&0!==e.indent)throw new Error('pretty-format: Options "min" and "indent" cannot be used together.');if(void 0!==e.theme){if(null===e.theme)throw new Error('pretty-format: Option "theme" must not be null.');if("object"!=typeof e.theme)throw new Error(`pretty-format: Option "theme" must be of type "object" but instead received "${typeof e.theme}".`)}}(t),t.plugins)){const r=St(t.plugins,e);if(null!==r)return jt(r,e,zt(t),"",0,[])}const r=Mt(e,Ut(t),Dt(t),Ht(t));return null!==r?r:At(e,zt(t),"",0,[])}const $t={AsymmetricMatcher:ct.default,ConvertAnsi:pt.default,DOMCollection:mt.default,DOMElement:ft.default,Immutable:bt.default,ReactElement:vt.default,ReactTestComponent:yt.default};ut=h.plugins=$t;var Wt=Vt;lt=h.default=Wt;var Gt=i({__proto__:null,get DEFAULT_OPTIONS(){return Ft},format:it,get plugins(){return ut},get default(){return lt}},[h]),Qt=Object.prototype.toString;function Jt(e){return"function"==typeof e||"[object Function]"===Qt.call(e)}var Kt=Math.pow(2,53)-1;function Xt(e){var t=function(e){var t=Number(e);return isNaN(t)?0:0!==t&&isFinite(t)?(t>0?1:-1)*Math.floor(Math.abs(t)):t}(e);return Math.min(Math.max(t,0),Kt)}function Yt(e,t){var r=Array,n=Object(e);if(null==e)throw new TypeError("Array.from requires an array-like object - not null or undefined");if(void 0!==t&&!Jt(t))throw new TypeError("Array.from: when provided, the second argument must be a function");for(var a,o=Xt(n.length),l=Jt(r)?Object(new r(o)):new Array(o),i=0;i<o;)a=n[i],l[i]=t?t(a,i):a,i+=1;return l.length=o,l}function Zt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function er(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function tr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var rr=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];Zt(this,e),tr(this,"items",void 0),this.items=t}var t,r,n;return t=e,(r=[{key:"add",value:function(e){return!1===this.has(e)&&this.items.push(e),this}},{key:"clear",value:function(){this.items=[]}},{key:"delete",value:function(e){var t=this.items.length;return this.items=this.items.filter((function(t){return t!==e})),t!==this.items.length}},{key:"forEach",value:function(e){var t=this;this.items.forEach((function(r){e(r,r,t)}))}},{key:"has",value:function(e){return-1!==this.items.indexOf(e)}},{key:"size",get:function(){return this.items.length}}])&&er(t.prototype,r),n&&er(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),nr="undefined"==typeof Set?Set:rr;function ar(e){var t;return null!==(t=e.localName)&&void 0!==t?t:e.tagName.toLowerCase()}var or={article:"article",aside:"complementary",button:"button",datalist:"listbox",dd:"definition",details:"group",dialog:"dialog",dt:"term",fieldset:"group",figure:"figure",form:"form",footer:"contentinfo",h1:"heading",h2:"heading",h3:"heading",h4:"heading",h5:"heading",h6:"heading",header:"banner",hr:"separator",html:"document",legend:"legend",li:"listitem",math:"math",main:"main",menu:"list",nav:"navigation",ol:"list",optgroup:"group",option:"option",output:"status",progress:"progressbar",section:"region",summary:"button",table:"table",tbody:"rowgroup",textarea:"textbox",tfoot:"rowgroup",td:"cell",th:"columnheader",thead:"rowgroup",tr:"row",ul:"list"},lr={caption:new Set(["aria-label","aria-labelledby"]),code:new Set(["aria-label","aria-labelledby"]),deletion:new Set(["aria-label","aria-labelledby"]),emphasis:new Set(["aria-label","aria-labelledby"]),generic:new Set(["aria-label","aria-labelledby","aria-roledescription"]),insertion:new Set(["aria-label","aria-labelledby"]),paragraph:new Set(["aria-label","aria-labelledby"]),presentation:new Set(["aria-label","aria-labelledby"]),strong:new Set(["aria-label","aria-labelledby"]),subscript:new Set(["aria-label","aria-labelledby"]),superscript:new Set(["aria-label","aria-labelledby"])};function ir(e,t){return function(e,t){return["aria-atomic","aria-busy","aria-controls","aria-current","aria-describedby","aria-details","aria-dropeffect","aria-flowto","aria-grabbed","aria-hidden","aria-keyshortcuts","aria-label","aria-labelledby","aria-live","aria-owns","aria-relevant","aria-roledescription"].some((function(r){var n;return e.hasAttribute(r)&&!(null!==(n=lr[t])&&void 0!==n&&n.has(r))}))}(e,t)}function ur(e){var t=function(e){var t=e.getAttribute("role");if(null!==t){var r=t.trim().split(" ")[0];if(r.length>0)return r}return null}(e);if(null===t||"presentation"===t){var r=function(e){var t=or[ar(e)];if(void 0!==t)return t;switch(ar(e)){case"a":case"area":case"link":if(e.hasAttribute("href"))return"link";break;case"img":return""!==e.getAttribute("alt")||ir(e,"img")?"img":"presentation";case"input":var r=e.type;switch(r){case"button":case"image":case"reset":case"submit":return"button";case"checkbox":case"radio":return r;case"range":return"slider";case"email":case"tel":case"text":case"url":return e.hasAttribute("list")?"combobox":"textbox";case"search":return e.hasAttribute("list")?"combobox":"searchbox";case"number":return"spinbutton";default:return null}case"select":return e.hasAttribute("multiple")||e.size>1?"listbox":"combobox"}return null}(e);if("presentation"!==t||ir(e,r||""))return r}return t}function sr(e){return null!==e&&e.nodeType===e.ELEMENT_NODE}function dr(e){return sr(e)&&"caption"===ar(e)}function cr(e){return sr(e)&&"input"===ar(e)}function pr(e){return sr(e)&&"optgroup"===ar(e)}function mr(e){return sr(e)&&"table"===ar(e)}function fr(e){var t=(null===e.ownerDocument?e:e.ownerDocument).defaultView;if(null===t)throw new TypeError("no window available");return t}function br(e){return sr(e)&&"fieldset"===ar(e)}function vr(e){return sr(e)&&"legend"===ar(e)}function yr(e){return sr(e)&&"slot"===ar(e)}function hr(e){return sr(e)&&"svg"===ar(e)}function gr(e){return function(e){return sr(e)&&void 0!==e.ownerSVGElement}(e)&&"title"===ar(e)}function Cr(e,t){if(sr(e)&&e.hasAttribute(t)){var r=e.getAttribute(t).split(" "),n=e.getRootNode?e.getRootNode():e.ownerDocument;return r.map((function(e){return n.getElementById(e)})).filter((function(e){return null!==e}))}return[]}function qr(e,t){return!!sr(e)&&-1!==t.indexOf(ur(e))}function Pr(e){return e.trim().replace(/\s\s+/g," ")}function Er(e,t){if(!sr(e))return!1;if(e.hasAttribute("hidden")||"true"===e.getAttribute("aria-hidden"))return!0;var r=t(e);return"none"===r.getPropertyValue("display")||"hidden"===r.getPropertyValue("visibility")}function wr(e){return qr(e,["button","combobox","listbox","textbox"])||xr(e,"range")}function xr(e,t){if(!sr(e))return!1;if("range"===t)return qr(e,["meter","progressbar","scrollbar","slider","spinbutton"]);throw new TypeError("No knowledge about abstract role '".concat(t,"'. This is likely a bug :("))}function Rr(e,t){var r=Yt(e.querySelectorAll(t));return Cr(e,"aria-owns").forEach((function(e){r.push.apply(r,Yt(e.querySelectorAll(t)))})),r}function Or(e){return sr(t=e)&&"select"===ar(t)?e.selectedOptions||Rr(e,"[selected]"):Rr(e,'[aria-selected="true"]');var t}function Tr(e){return qr(e,["none","presentation"])}function _r(e){return dr(e)}function Mr(e){return qr(e,["button","cell","checkbox","columnheader","gridcell","heading","label","legend","link","menuitem","menuitemcheckbox","menuitemradio","option","radio","row","rowheader","switch","tab","tooltip","treeitem"])}function Ar(e){return cr(e)||sr(t=e)&&"textarea"===ar(t)?e.value:e.textContent||"";var t}function jr(e){var t=e.getPropertyValue("content");return/^["'].*["']$/.test(t)?t.slice(1,-1):""}function Sr(e){var t=ar(e);return"button"===t||"input"===t&&"hidden"!==e.getAttribute("type")||"meter"===t||"output"===t||"progress"===t||"select"===t||"textarea"===t}function Br(e){if(Sr(e))return e;var t=null;return e.childNodes.forEach((function(e){if(null===t&&sr(e)){var r=Br(e);null!==r&&(t=r)}})),t}function Ir(e){if(void 0!==e.control)return e.control;var t=e.getAttribute("for");return null!==t?e.ownerDocument.getElementById(t):Br(e)}function Nr(e){var t=e.labels;return null===t?t:void 0!==t?Yt(t):Sr(e)?Yt(e.ownerDocument.querySelectorAll("label")).filter((function(t){return Ir(t)===e})):null}function kr(e){var t=e.assignedNodes();return 0===t.length?Yt(e.childNodes):t}function Fr(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new nr,n=fr(e),a=t.compute,o=void 0===a?"name":a,l=t.computedStyleSupportsPseudoElements,i=void 0===l?void 0!==t.getComputedStyle:l,u=t.getComputedStyle,s=void 0===u?n.getComputedStyle.bind(n):u,d=t.hidden,c=void 0!==d&&d;function p(e,t){var r="";if(sr(e)&&i){var n=jr(s(e,"::before"));r="".concat(n," ").concat(r)}if((yr(e)?kr(e):Yt(e.childNodes).concat(Cr(e,"aria-owns"))).forEach((function(e){var n=f(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0}),a="inline"!==(sr(e)?s(e).getPropertyValue("display"):"inline")?" ":"";r+="".concat(a).concat(n).concat(a)})),sr(e)&&i){var a=jr(s(e,"::after"));r="".concat(r," ").concat(a)}return r.trim()}function m(e){if(!sr(e))return null;function t(e,t){var n=e.getAttributeNode(t);return null===n||r.has(n)||""===n.value.trim()?null:(r.add(n),n.value)}if(br(e)){r.add(e);for(var n=Yt(e.childNodes),a=0;a<n.length;a+=1){var o=n[a];if(vr(o))return f(o,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else if(mr(e)){r.add(e);for(var l=Yt(e.childNodes),i=0;i<l.length;i+=1){var u=l[i];if(dr(u))return f(u,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else{if(hr(e)){r.add(e);for(var s=Yt(e.childNodes),d=0;d<s.length;d+=1){var c=s[d];if(gr(c))return c.textContent}return null}if("img"===ar(e)||"area"===ar(e)){var m=t(e,"alt");if(null!==m)return m}else if(pr(e)){var b=t(e,"label");if(null!==b)return b}}if(cr(e)&&("button"===e.type||"submit"===e.type||"reset"===e.type)){var v=t(e,"value");if(null!==v)return v;if("submit"===e.type)return"Submit";if("reset"===e.type)return"Reset"}var y=Nr(e);if(null!==y&&0!==y.length)return r.add(e),Yt(y).map((function(e){return f(e,{isEmbeddedInLabel:!0,isReferenced:!1,recursion:!0})})).filter((function(e){return e.length>0})).join(" ");if(cr(e)&&"image"===e.type){var h=t(e,"alt");if(null!==h)return h;var g=t(e,"title");return null!==g?g:"Submit Query"}if(qr(e,["button"])){var C=p(e,{isEmbeddedInLabel:!1,isReferenced:!1});return""!==C?C:t(e,"title")}return t(e,"title")}function f(e,t){if(r.has(e))return"";if(!c&&Er(e,s)&&!t.isReferenced)return r.add(e),"";var n=Cr(e,"aria-labelledby");if("name"===o&&!t.isReferenced&&n.length>0)return n.map((function(e){return f(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!0,recursion:!1})})).join(" ");var a=t.recursion&&wr(e)&&"name"===o;if(!a){var l=(sr(e)&&e.getAttribute("aria-label")||"").trim();if(""!==l&&"name"===o)return r.add(e),l;if(!Tr(e)){var i=m(e);if(null!==i)return r.add(e),i}}if(qr(e,["menu"]))return r.add(e),"";if(a||t.isEmbeddedInLabel||t.isReferenced){if(qr(e,["combobox","listbox"])){r.add(e);var u=Or(e);return 0===u.length?cr(e)?e.value:"":Yt(u).map((function(e){return f(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0})})).join(" ")}if(xr(e,"range"))return r.add(e),e.hasAttribute("aria-valuetext")?e.getAttribute("aria-valuetext"):e.hasAttribute("aria-valuenow")?e.getAttribute("aria-valuenow"):e.getAttribute("value")||"";if(qr(e,["textbox"]))return r.add(e),Ar(e)}return Mr(e)||sr(e)&&t.isReferenced||_r(e)?(r.add(e),p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1})):e.nodeType===e.TEXT_NODE?(r.add(e),e.textContent||""):t.recursion?(r.add(e),p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1})):(r.add(e),"")}return Pr(f(e,{isEmbeddedInLabel:!1,isReferenced:"description"===o,recursion:!1}))}function Lr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ur(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Lr(Object(r),!0).forEach((function(t){Dr(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lr(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Dr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Hr(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=Cr(e,"aria-describedby").map((function(e){return Fr(e,Ur(Ur({},t),{},{compute:"description"}))})).join(" ");if(""===r){var n=e.getAttribute("title");r=null===n?"":n}return r}function zr(e){return qr(e,["caption","code","deletion","emphasis","generic","insertion","paragraph","presentation","strong","subscript","superscript"])}function Vr(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return zr(e)?"":Fr(e,t)}var $r={},Wr={};function Gr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Qr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Qr(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Qr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}Object.defineProperty(Wr,"__esModule",{value:!0}),Wr.default=void 0;var Jr=[["aria-activedescendant",{type:"id"}],["aria-atomic",{type:"boolean"}],["aria-autocomplete",{type:"token",values:["inline","list","both","none"]}],["aria-busy",{type:"boolean"}],["aria-checked",{type:"tristate"}],["aria-colcount",{type:"integer"}],["aria-colindex",{type:"integer"}],["aria-colspan",{type:"integer"}],["aria-controls",{type:"idlist"}],["aria-current",{type:"token",values:["page","step","location","date","time",!0,!1]}],["aria-describedby",{type:"idlist"}],["aria-details",{type:"id"}],["aria-disabled",{type:"boolean"}],["aria-dropeffect",{type:"tokenlist",values:["copy","execute","link","move","none","popup"]}],["aria-errormessage",{type:"id"}],["aria-expanded",{type:"boolean",allowundefined:!0}],["aria-flowto",{type:"idlist"}],["aria-grabbed",{type:"boolean",allowundefined:!0}],["aria-haspopup",{type:"token",values:[!1,!0,"menu","listbox","tree","grid","dialog"]}],["aria-hidden",{type:"boolean",allowundefined:!0}],["aria-invalid",{type:"token",values:["grammar",!1,"spelling",!0]}],["aria-keyshortcuts",{type:"string"}],["aria-label",{type:"string"}],["aria-labelledby",{type:"idlist"}],["aria-level",{type:"integer"}],["aria-live",{type:"token",values:["assertive","off","polite"]}],["aria-modal",{type:"boolean"}],["aria-multiline",{type:"boolean"}],["aria-multiselectable",{type:"boolean"}],["aria-orientation",{type:"token",values:["vertical","undefined","horizontal"]}],["aria-owns",{type:"idlist"}],["aria-placeholder",{type:"string"}],["aria-posinset",{type:"integer"}],["aria-pressed",{type:"tristate"}],["aria-readonly",{type:"boolean"}],["aria-relevant",{type:"tokenlist",values:["additions","all","removals","text"]}],["aria-required",{type:"boolean"}],["aria-roledescription",{type:"string"}],["aria-rowcount",{type:"integer"}],["aria-rowindex",{type:"integer"}],["aria-rowspan",{type:"integer"}],["aria-selected",{type:"boolean",allowundefined:!0}],["aria-setsize",{type:"integer"}],["aria-sort",{type:"token",values:["ascending","descending","none","other"]}],["aria-valuemax",{type:"number"}],["aria-valuemin",{type:"number"}],["aria-valuenow",{type:"number"}],["aria-valuetext",{type:"string"}]],Kr={entries:function(){return Jr},get:function(e){var t=Jr.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!this.get(e)},keys:function(){return Jr.map((function(e){return Gr(e,1)[0]}))},values:function(){return Jr.map((function(e){return Gr(e,2)[1]}))}};Wr.default=Kr;var Xr={};function Yr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Zr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Zr(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Zr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}Object.defineProperty(Xr,"__esModule",{value:!0}),Xr.default=void 0;var en=[["a",{reserved:!1}],["abbr",{reserved:!1}],["acronym",{reserved:!1}],["address",{reserved:!1}],["applet",{reserved:!1}],["area",{reserved:!1}],["article",{reserved:!1}],["aside",{reserved:!1}],["audio",{reserved:!1}],["b",{reserved:!1}],["base",{reserved:!0}],["bdi",{reserved:!1}],["bdo",{reserved:!1}],["big",{reserved:!1}],["blink",{reserved:!1}],["blockquote",{reserved:!1}],["body",{reserved:!1}],["br",{reserved:!1}],["button",{reserved:!1}],["canvas",{reserved:!1}],["caption",{reserved:!1}],["center",{reserved:!1}],["cite",{reserved:!1}],["code",{reserved:!1}],["col",{reserved:!0}],["colgroup",{reserved:!0}],["content",{reserved:!1}],["data",{reserved:!1}],["datalist",{reserved:!1}],["dd",{reserved:!1}],["del",{reserved:!1}],["details",{reserved:!1}],["dfn",{reserved:!1}],["dialog",{reserved:!1}],["dir",{reserved:!1}],["div",{reserved:!1}],["dl",{reserved:!1}],["dt",{reserved:!1}],["em",{reserved:!1}],["embed",{reserved:!1}],["fieldset",{reserved:!1}],["figcaption",{reserved:!1}],["figure",{reserved:!1}],["font",{reserved:!1}],["footer",{reserved:!1}],["form",{reserved:!1}],["frame",{reserved:!1}],["frameset",{reserved:!1}],["h1",{reserved:!1}],["h2",{reserved:!1}],["h3",{reserved:!1}],["h4",{reserved:!1}],["h5",{reserved:!1}],["h6",{reserved:!1}],["head",{reserved:!0}],["header",{reserved:!1}],["hgroup",{reserved:!1}],["hr",{reserved:!1}],["html",{reserved:!0}],["i",{reserved:!1}],["iframe",{reserved:!1}],["img",{reserved:!1}],["input",{reserved:!1}],["ins",{reserved:!1}],["kbd",{reserved:!1}],["keygen",{reserved:!1}],["label",{reserved:!1}],["legend",{reserved:!1}],["li",{reserved:!1}],["link",{reserved:!0}],["main",{reserved:!1}],["map",{reserved:!1}],["mark",{reserved:!1}],["marquee",{reserved:!1}],["menu",{reserved:!1}],["menuitem",{reserved:!1}],["meta",{reserved:!0}],["meter",{reserved:!1}],["nav",{reserved:!1}],["noembed",{reserved:!0}],["noscript",{reserved:!0}],["object",{reserved:!1}],["ol",{reserved:!1}],["optgroup",{reserved:!1}],["option",{reserved:!1}],["output",{reserved:!1}],["p",{reserved:!1}],["param",{reserved:!0}],["picture",{reserved:!0}],["pre",{reserved:!1}],["progress",{reserved:!1}],["q",{reserved:!1}],["rp",{reserved:!1}],["rt",{reserved:!1}],["rtc",{reserved:!1}],["ruby",{reserved:!1}],["s",{reserved:!1}],["samp",{reserved:!1}],["script",{reserved:!0}],["section",{reserved:!1}],["select",{reserved:!1}],["small",{reserved:!1}],["source",{reserved:!0}],["spacer",{reserved:!1}],["span",{reserved:!1}],["strike",{reserved:!1}],["strong",{reserved:!1}],["style",{reserved:!0}],["sub",{reserved:!1}],["summary",{reserved:!1}],["sup",{reserved:!1}],["table",{reserved:!1}],["tbody",{reserved:!1}],["td",{reserved:!1}],["textarea",{reserved:!1}],["tfoot",{reserved:!1}],["th",{reserved:!1}],["thead",{reserved:!1}],["time",{reserved:!1}],["title",{reserved:!0}],["tr",{reserved:!1}],["track",{reserved:!0}],["tt",{reserved:!1}],["u",{reserved:!1}],["ul",{reserved:!1}],["var",{reserved:!1}],["video",{reserved:!1}],["wbr",{reserved:!1}],["xmp",{reserved:!1}]],tn={entries:function(){return en},get:function(e){var t=en.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!this.get(e)},keys:function(){return en.map((function(e){return Yr(e,1)[0]}))},values:function(){return en.map((function(e){return Yr(e,2)[1]}))}};Xr.default=tn;var rn={},nn={},an={};Object.defineProperty(an,"__esModule",{value:!0}),an.default=void 0;var on={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};an.default=on;var ln={};Object.defineProperty(ln,"__esModule",{value:!0}),ln.default=void 0;var un={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};ln.default=un;var sn={};Object.defineProperty(sn,"__esModule",{value:!0}),sn.default=void 0;var dn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null},relatedConcepts:[{concept:{name:"input"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};sn.default=dn;var cn={};Object.defineProperty(cn,"__esModule",{value:!0}),cn.default=void 0;var pn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};cn.default=pn;var mn={};Object.defineProperty(mn,"__esModule",{value:!0}),mn.default=void 0;var fn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuemax":null,"aria-valuemin":null,"aria-valuenow":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};mn.default=fn;var bn={};Object.defineProperty(bn,"__esModule",{value:!0}),bn.default=void 0;var vn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{"aria-atomic":null,"aria-busy":null,"aria-controls":null,"aria-current":null,"aria-describedby":null,"aria-details":null,"aria-dropeffect":null,"aria-flowto":null,"aria-grabbed":null,"aria-hidden":null,"aria-keyshortcuts":null,"aria-label":null,"aria-labelledby":null,"aria-live":null,"aria-owns":null,"aria-relevant":null,"aria-roledescription":null},relatedConcepts:[{concept:{name:"rel"},module:"HTML"},{concept:{name:"role"},module:"XHTML"},{concept:{name:"type"},module:"Dublin Core"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};bn.default=vn;var yn={};Object.defineProperty(yn,"__esModule",{value:!0}),yn.default=void 0;var hn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"frontmatter"},module:"DTB"},{concept:{name:"level"},module:"DTB"},{concept:{name:"level"},module:"SMIL"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};yn.default=hn;var gn={};Object.defineProperty(gn,"__esModule",{value:!0}),gn.default=void 0;var Cn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};gn.default=Cn;var qn={};Object.defineProperty(qn,"__esModule",{value:!0}),qn.default=void 0;var Pn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","group"]]};qn.default=Pn;var En={};Object.defineProperty(En,"__esModule",{value:!0}),En.default=void 0;var wn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};En.default=wn;var xn={};Object.defineProperty(xn,"__esModule",{value:!0}),xn.default=void 0;var Rn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};xn.default=Rn;var On={};Object.defineProperty(On,"__esModule",{value:!0}),On.default=void 0;var Tn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-modal":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};On.default=Tn,Object.defineProperty(nn,"__esModule",{value:!0}),nn.default=void 0;var _n=Dn(an),Mn=Dn(ln),An=Dn(sn),jn=Dn(cn),Sn=Dn(mn),Bn=Dn(bn),In=Dn(yn),Nn=Dn(gn),kn=Dn(qn),Fn=Dn(En),Ln=Dn(xn),Un=Dn(On);function Dn(e){return e&&e.__esModule?e:{default:e}}var Hn=[["command",_n.default],["composite",Mn.default],["input",An.default],["landmark",jn.default],["range",Sn.default],["roletype",Bn.default],["section",In.default],["sectionhead",Nn.default],["select",kn.default],["structure",Fn.default],["widget",Ln.default],["window",Un.default]];nn.default=Hn;var zn={},Vn={};Object.defineProperty(Vn,"__esModule",{value:!0}),Vn.default=void 0;var $n={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"assertive"},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Vn.default=$n;var Wn={};Object.defineProperty(Wn,"__esModule",{value:!0}),Wn.default=void 0;var Gn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","alert"],["roletype","window","dialog"]]};Wn.default=Gn;var Qn={};Object.defineProperty(Qn,"__esModule",{value:!0}),Qn.default=void 0;var Jn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Qn.default=Jn;var Kn={};Object.defineProperty(Kn,"__esModule",{value:!0}),Kn.default=void 0;var Xn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"article"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]};Kn.default=Xn;var Yn={};Object.defineProperty(Yn,"__esModule",{value:!0}),Yn.default=void 0;var Zn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["direct descendant of document"],name:"header"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Yn.default=Zn;var ea={};Object.defineProperty(ea,"__esModule",{value:!0}),ea.default=void 0;var ta={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ea.default=ta;var ra={};Object.defineProperty(ra,"__esModule",{value:!0}),ra.default=void 0;var na={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-pressed":null},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-pressed"},{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"aria-expanded",value:"false"}],name:"summary"},module:"HTML"},{concept:{attributes:[{name:"aria-expanded",value:"true"}],constraints:["direct descendant of details element with the open attribute defined"],name:"summary"},module:"HTML"},{concept:{attributes:[{name:"type",value:"button"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"image"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"reset"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"submit"}],name:"input"},module:"HTML"},{concept:{name:"button"},module:"HTML"},{concept:{name:"trigger"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};ra.default=na;var aa={};Object.defineProperty(aa,"__esModule",{value:!0}),aa.default=void 0;var oa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:["figure","grid","table"],requiredContextRole:["figure","grid","table"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};aa.default=oa;var la={};Object.defineProperty(la,"__esModule",{value:!0}),la.default=void 0;var ia={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-colspan":null,"aria-rowindex":null,"aria-rowspan":null},relatedConcepts:[{concept:{constraints:["descendant of table"],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};la.default=ia;var ua={};Object.defineProperty(ua,"__esModule",{value:!0}),ua.default=void 0;var sa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};ua.default=sa;var da={};Object.defineProperty(da,"__esModule",{value:!0}),da.default=void 0;var ca={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};da.default=ca;var pa={};Object.defineProperty(pa,"__esModule",{value:!0}),pa.default=void 0;var ma={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{attributes:[{name:"scope",value:"col"}],concept:{name:"th"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};pa.default=ma;var fa={};Object.defineProperty(fa,"__esModule",{value:!0}),fa.default=void 0;var ba={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-expanded":"false","aria-haspopup":"listbox"},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{constraints:["undefined"],name:"size"}],name:"select"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{name:"size",value:1}],name:"select"},module:"HTML"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-expanded":"false"},superClass:[["roletype","widget","input"]]};fa.default=ba;var va={};Object.defineProperty(va,"__esModule",{value:!0}),va.default=void 0;var ya={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"aside"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};va.default=ya;var ha={};Object.defineProperty(ha,"__esModule",{value:!0}),ha.default=void 0;var ga={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["direct descendant of document"],name:"footer"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ha.default=ga;var Ca={};Object.defineProperty(Ca,"__esModule",{value:!0}),Ca.default=void 0;var qa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dd"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ca.default=qa;var Pa={};Object.defineProperty(Pa,"__esModule",{value:!0}),Pa.default=void 0;var Ea={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Pa.default=Ea;var wa={};Object.defineProperty(wa,"__esModule",{value:!0}),wa.default=void 0;var xa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dialog"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","window"]]};wa.default=xa;var Ra={};Object.defineProperty(Ra,"__esModule",{value:!0}),Ra.default=void 0;var Oa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{module:"DAISY Guide"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","list"]]};Ra.default=Oa;var Ta={};Object.defineProperty(Ta,"__esModule",{value:!0}),Ta.default=void 0;var _a={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}},{concept:{name:"body"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Ta.default=_a;var Ma={};Object.defineProperty(Ma,"__esModule",{value:!0}),Ma.default=void 0;var Aa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ma.default=Aa;var ja={};Object.defineProperty(ja,"__esModule",{value:!0}),ja.default=void 0;var Sa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["article"]],requiredProps:{},superClass:[["roletype","structure","section","list"]]};ja.default=Sa;var Ba={};Object.defineProperty(Ba,"__esModule",{value:!0}),Ba.default=void 0;var Ia={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"figure"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ba.default=Ia;var Na={};Object.defineProperty(Na,"__esModule",{value:!0}),Na.default=void 0;var ka={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"name"}],name:"form"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Na.default=ka;var Fa={};Object.defineProperty(Fa,"__esModule",{value:!0}),Fa.default=void 0;var La={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"span"},module:"HTML"},{concept:{name:"div"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Fa.default=La;var Ua={};Object.defineProperty(Ua,"__esModule",{value:!0}),Ua.default=void 0;var Da={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-multiselectable":null,"aria-readonly":null},relatedConcepts:[{concept:{attributes:[{name:"role",value:"grid"}],name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","table"]]};Ua.default=Da;var Ha={};Object.defineProperty(Ha,"__esModule",{value:!0}),Ha.default=void 0;var za={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-selected":null},relatedConcepts:[{concept:{attributes:[{name:"role",value:"gridcell"}],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","widget"]]};Ha.default=za;var Va={};Object.defineProperty(Va,"__esModule",{value:!0}),Va.default=void 0;var $a={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[{concept:{name:"details"},module:"HTML"},{concept:{name:"fieldset"},module:"HTML"},{concept:{name:"optgroup"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Va.default=$a;var Wa={};Object.defineProperty(Wa,"__esModule",{value:!0}),Wa.default=void 0;var Ga={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-level":"2"},relatedConcepts:[{concept:{name:"h1"},module:"HTML"},{concept:{name:"h2"},module:"HTML"},{concept:{name:"h3"},module:"HTML"},{concept:{name:"h4"},module:"HTML"},{concept:{name:"h5"},module:"HTML"},{concept:{name:"h6"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-level":"2"},superClass:[["roletype","structure","sectionhead"]]};Wa.default=Ga;var Qa={};Object.defineProperty(Qa,"__esModule",{value:!0}),Qa.default=void 0;var Ja={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"alt"}],name:"img"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"alt"}],name:"img"},module:"HTML"},{concept:{name:"imggroup"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Qa.default=Ja;var Ka={};Object.defineProperty(Ka,"__esModule",{value:!0}),Ka.default=void 0;var Xa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ka.default=Xa;var Ya={};Object.defineProperty(Ya,"__esModule",{value:!0}),Ya.default=void 0;var Za={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[{concept:{attributes:[{name:"href"}],name:"a"},module:"HTML"},{concept:{attributes:[{name:"href"}],name:"area"},module:"HTML"},{concept:{attributes:[{name:"href"}],name:"link"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};Ya.default=Za;var eo={};Object.defineProperty(eo,"__esModule",{value:!0}),eo.default=void 0;var to={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menu"},module:"HTML"},{concept:{name:"ol"},module:"HTML"},{concept:{name:"ul"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["listitem"]],requiredProps:{},superClass:[["roletype","structure","section"]]};eo.default=to;var ro={};Object.defineProperty(ro,"__esModule",{value:!0}),ro.default=void 0;var no={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-multiselectable":null,"aria-readonly":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[{concept:{attributes:[{constraints:[">1"],name:"size"},{name:"multiple"}],name:"select"},module:"HTML"},{concept:{attributes:[{constraints:[">1"],name:"size"}],name:"select"},module:"HTML"},{concept:{attributes:[{name:"multiple"}],name:"select"},module:"HTML"},{concept:{name:"datalist"},module:"HTML"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["option","group"],["option"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};ro.default=no;var ao={};Object.defineProperty(ao,"__esModule",{value:!0}),ao.default=void 0;var oo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{constraints:["direct descendant of ol, ul or menu"],name:"li"},module:"HTML"},{concept:{name:"item"},module:"XForms"}],requireContextRole:["directory","list"],requiredContextRole:["directory","list"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ao.default=oo;var lo={};Object.defineProperty(lo,"__esModule",{value:!0}),lo.default=void 0;var io={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-live":"polite"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};lo.default=io;var uo={};Object.defineProperty(uo,"__esModule",{value:!0}),uo.default=void 0;var so={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"main"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};uo.default=so;var co={};Object.defineProperty(co,"__esModule",{value:!0}),co.default=void 0;var po={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};co.default=po;var mo={};Object.defineProperty(mo,"__esModule",{value:!0}),mo.default=void 0;var fo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"math"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};mo.default=fo;var bo={};Object.defineProperty(bo,"__esModule",{value:!0}),bo.default=void 0;var vo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"vertical"},relatedConcepts:[{concept:{name:"MENU"},module:"JAPI"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"},{concept:{name:"sidebar"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};bo.default=vo;var yo={};Object.defineProperty(yo,"__esModule",{value:!0}),yo.default=void 0;var ho={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"toolbar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select","menu"],["roletype","structure","section","group","select","menu"]]};yo.default=ho;var go={};Object.defineProperty(go,"__esModule",{value:!0}),go.default=void 0;var Co={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"MENU_ITEM"},module:"JAPI"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"menuitem"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};go.default=Co;var qo={};Object.defineProperty(qo,"__esModule",{value:!0}),qo.default=void 0;var Po={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"],["roletype","widget","command","menuitem"]]};qo.default=Po;var Eo={};Object.defineProperty(Eo,"__esModule",{value:!0}),Eo.default=void 0;var wo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox","menuitemcheckbox"],["roletype","widget","command","menuitem","menuitemcheckbox"],["roletype","widget","input","radio"]]};Eo.default=wo;var xo={};Object.defineProperty(xo,"__esModule",{value:!0}),xo.default=void 0;var Ro={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null,"aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","structure","range"]]};xo.default=Ro;var Oo={};Object.defineProperty(Oo,"__esModule",{value:!0}),Oo.default=void 0;var To={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"nav"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Oo.default=To;var _o={};Object.defineProperty(_o,"__esModule",{value:!0}),_o.default=void 0;var Mo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};_o.default=Mo;var Ao={};Object.defineProperty(Ao,"__esModule",{value:!0}),Ao.default=void 0;var jo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ao.default=jo;var So={};Object.defineProperty(So,"__esModule",{value:!0}),So.default=void 0;var Bo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[{concept:{name:"item"},module:"XForms"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"option"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-selected":"false"},superClass:[["roletype","widget","input"]]};So.default=Bo;var Io={};Object.defineProperty(Io,"__esModule",{value:!0}),Io.default=void 0;var No={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Io.default=No;var ko={};Object.defineProperty(ko,"__esModule",{value:!0}),ko.default=void 0;var Fo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};ko.default=Fo;var Lo={};Object.defineProperty(Lo,"__esModule",{value:!0}),Lo.default=void 0;var Uo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null},relatedConcepts:[{concept:{name:"progress"},module:"HTML"},{concept:{name:"status"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","range"],["roletype","widget"]]};Lo.default=Uo;var Do={};Object.defineProperty(Do,"__esModule",{value:!0}),Do.default=void 0;var Ho={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"radio"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};Do.default=Ho;var zo={};Object.defineProperty(zo,"__esModule",{value:!0}),zo.default=void 0;var Vo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{name:"list"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["radio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};zo.default=Vo;var $o={};Object.defineProperty($o,"__esModule",{value:!0}),$o.default=void 0;var Wo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"section"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"section"},module:"HTML"},{concept:{name:"Device Independence Glossart perceivable unit"}},{concept:{name:"frame"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};$o.default=Wo;var Go={};Object.defineProperty(Go,"__esModule",{value:!0}),Go.default=void 0;var Qo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-expanded":null,"aria-level":null,"aria-posinset":null,"aria-rowindex":null,"aria-selected":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"tr"},module:"HTML"}],requireContextRole:["grid","rowgroup","table","treegrid"],requiredContextRole:["grid","rowgroup","table","treegrid"],requiredOwnedElements:[["cell"],["columnheader"],["gridcell"],["rowheader"]],requiredProps:{},superClass:[["roletype","structure","section","group"],["roletype","widget"]]};Go.default=Qo;var Jo={};Object.defineProperty(Jo,"__esModule",{value:!0}),Jo.default=void 0;var Ko={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"tbody"},module:"HTML"},{concept:{name:"tfoot"},module:"HTML"},{concept:{name:"thead"},module:"HTML"}],requireContextRole:["grid","table","treegrid"],requiredContextRole:["grid","table","treegrid"],requiredOwnedElements:[["row"]],requiredProps:{},superClass:[["roletype","structure"]]};Jo.default=Ko;var Xo={};Object.defineProperty(Xo,"__esModule",{value:!0}),Xo.default=void 0;var Yo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{concept:{attributes:[{name:"scope",value:"row"}],name:"th"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};Xo.default=Yo;var Zo={};Object.defineProperty(Zo,"__esModule",{value:!0}),Zo.default=void 0;var el={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-valuetext":null,"aria-orientation":"vertical","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-valuenow":null},superClass:[["roletype","structure","range"],["roletype","widget"]]};Zo.default=el;var tl={};Object.defineProperty(tl,"__esModule",{value:!0}),tl.default=void 0;var rl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};tl.default=rl;var nl={};Object.defineProperty(nl,"__esModule",{value:!0}),nl.default=void 0;var al={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input","textbox"]]};nl.default=al;var ol={};Object.defineProperty(ol,"__esModule",{value:!0}),ol.default=void 0;var ll={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0","aria-valuenow":null,"aria-valuetext":null},relatedConcepts:[{concept:{name:"hr"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};ol.default=ll;var il={};Object.defineProperty(il,"__esModule",{value:!0}),il.default=void 0;var ul={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-valuetext":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"range"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","widget","input"],["roletype","structure","range"]]};il.default=ul;var sl={};Object.defineProperty(sl,"__esModule",{value:!0}),sl.default=void 0;var dl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-valuetext":null,"aria-valuenow":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"number"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","widget","input"],["roletype","structure","range"]]};sl.default=dl;var cl={};Object.defineProperty(cl,"__esModule",{value:!0}),cl.default=void 0;var pl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"polite"},relatedConcepts:[{concept:{name:"output"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};cl.default=pl;var ml={};Object.defineProperty(ml,"__esModule",{value:!0}),ml.default=void 0;var fl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ml.default=fl;var bl={};Object.defineProperty(bl,"__esModule",{value:!0}),bl.default=void 0;var vl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};bl.default=vl;var yl={};Object.defineProperty(yl,"__esModule",{value:!0}),yl.default=void 0;var hl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};yl.default=hl;var gl={};Object.defineProperty(gl,"__esModule",{value:!0}),gl.default=void 0;var Cl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"button"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"]]};gl.default=Cl;var ql={};Object.defineProperty(ql,"__esModule",{value:!0}),ql.default=void 0;var Pl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[],requireContextRole:["tablist"],requiredContextRole:["tablist"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"],["roletype","widget"]]};ql.default=Pl;var El={};Object.defineProperty(El,"__esModule",{value:!0}),El.default=void 0;var wl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-colcount":null,"aria-rowcount":null},relatedConcepts:[{concept:{name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","structure","section"]]};El.default=wl;var xl={};Object.defineProperty(xl,"__esModule",{value:!0}),xl.default=void 0;var Rl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-multiselectable":null,"aria-orientation":"horizontal"},relatedConcepts:[{module:"DAISY",concept:{name:"guide"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["tab"]],requiredProps:{},superClass:[["roletype","widget","composite"]]};xl.default=Rl;var Ol={};Object.defineProperty(Ol,"__esModule",{value:!0}),Ol.default=void 0;var Tl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ol.default=Tl;var _l={};Object.defineProperty(_l,"__esModule",{value:!0}),_l.default=void 0;var Ml={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dfn"},module:"HTML"},{concept:{name:"dt"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};_l.default=Ml;var Al={};Object.defineProperty(Al,"__esModule",{value:!0}),Al.default=void 0;var jl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-multiline":null,"aria-placeholder":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"type"},{constraints:["undefined"],name:"list"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{name:"input"},module:"XForms"},{concept:{name:"textarea"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input"]]};Al.default=jl;var Sl={};Object.defineProperty(Sl,"__esModule",{value:!0}),Sl.default=void 0;var Bl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Sl.default=Bl;var Il={};Object.defineProperty(Il,"__esModule",{value:!0}),Il.default=void 0;var Nl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","status"]]};Il.default=Nl;var kl={};Object.defineProperty(kl,"__esModule",{value:!0}),kl.default=void 0;var Fl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"menubar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]};kl.default=Fl;var Ll={};Object.defineProperty(Ll,"__esModule",{value:!0}),Ll.default=void 0;var Ul={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ll.default=Ul;var Dl={};Object.defineProperty(Dl,"__esModule",{value:!0}),Dl.default=void 0;var Hl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-multiselectable":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["treeitem","group"],["treeitem"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};Dl.default=Hl;var zl={};Object.defineProperty(zl,"__esModule",{value:!0}),zl.default=void 0;var Vl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite","grid"],["roletype","structure","section","table","grid"],["roletype","widget","composite","select","tree"],["roletype","structure","section","group","select","tree"]]};zl.default=Vl;var $l={};Object.defineProperty($l,"__esModule",{value:!0}),$l.default=void 0;var Wl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[],requireContextRole:["group","tree"],requiredContextRole:["group","tree"],requiredOwnedElements:[],requiredProps:{"aria-selected":null},superClass:[["roletype","structure","section","listitem"],["roletype","widget","input","option"]]};$l.default=Wl,Object.defineProperty(zn,"__esModule",{value:!0}),zn.default=void 0;var Gl=qu(Vn),Ql=qu(Wn),Jl=qu(Qn),Kl=qu(Kn),Xl=qu(Yn),Yl=qu(ea),Zl=qu(ra),ei=qu(aa),ti=qu(la),ri=qu(ua),ni=qu(da),ai=qu(pa),oi=qu(fa),li=qu(va),ii=qu(ha),ui=qu(Ca),si=qu(Pa),di=qu(wa),ci=qu(Ra),pi=qu(Ta),mi=qu(Ma),fi=qu(ja),bi=qu(Ba),vi=qu(Na),yi=qu(Fa),hi=qu(Ua),gi=qu(Ha),Ci=qu(Va),qi=qu(Wa),Pi=qu(Qa),Ei=qu(Ka),wi=qu(Ya),xi=qu(eo),Ri=qu(ro),Oi=qu(ao),Ti=qu(lo),_i=qu(uo),Mi=qu(co),Ai=qu(mo),ji=qu(bo),Si=qu(yo),Bi=qu(go),Ii=qu(qo),Ni=qu(Eo),ki=qu(xo),Fi=qu(Oo),Li=qu(_o),Ui=qu(Ao),Di=qu(So),Hi=qu(Io),zi=qu(ko),Vi=qu(Lo),$i=qu(Do),Wi=qu(zo),Gi=qu($o),Qi=qu(Go),Ji=qu(Jo),Ki=qu(Xo),Xi=qu(Zo),Yi=qu(tl),Zi=qu(nl),eu=qu(ol),tu=qu(il),ru=qu(sl),nu=qu(cl),au=qu(ml),ou=qu(bl),lu=qu(yl),iu=qu(gl),uu=qu(ql),su=qu(El),du=qu(xl),cu=qu(Ol),pu=qu(_l),mu=qu(Al),fu=qu(Sl),bu=qu(Il),vu=qu(kl),yu=qu(Ll),hu=qu(Dl),gu=qu(zl),Cu=qu($l);function qu(e){return e&&e.__esModule?e:{default:e}}var Pu=[["alert",Gl.default],["alertdialog",Ql.default],["application",Jl.default],["article",Kl.default],["banner",Xl.default],["blockquote",Yl.default],["button",Zl.default],["caption",ei.default],["cell",ti.default],["checkbox",ri.default],["code",ni.default],["columnheader",ai.default],["combobox",oi.default],["complementary",li.default],["contentinfo",ii.default],["definition",ui.default],["deletion",si.default],["dialog",di.default],["directory",ci.default],["document",pi.default],["emphasis",mi.default],["feed",fi.default],["figure",bi.default],["form",vi.default],["generic",yi.default],["grid",hi.default],["gridcell",gi.default],["group",Ci.default],["heading",qi.default],["img",Pi.default],["insertion",Ei.default],["link",wi.default],["list",xi.default],["listbox",Ri.default],["listitem",Oi.default],["log",Ti.default],["main",_i.default],["marquee",Mi.default],["math",Ai.default],["menu",ji.default],["menubar",Si.default],["menuitem",Bi.default],["menuitemcheckbox",Ii.default],["menuitemradio",Ni.default],["meter",ki.default],["navigation",Fi.default],["none",Li.default],["note",Ui.default],["option",Di.default],["paragraph",Hi.default],["presentation",zi.default],["progressbar",Vi.default],["radio",$i.default],["radiogroup",Wi.default],["region",Gi.default],["row",Qi.default],["rowgroup",Ji.default],["rowheader",Ki.default],["scrollbar",Xi.default],["search",Yi.default],["searchbox",Zi.default],["separator",eu.default],["slider",tu.default],["spinbutton",ru.default],["status",nu.default],["strong",au.default],["subscript",ou.default],["superscript",lu.default],["switch",iu.default],["tab",uu.default],["table",su.default],["tablist",du.default],["tabpanel",cu.default],["term",pu.default],["textbox",mu.default],["time",fu.default],["timer",bu.default],["toolbar",vu.default],["tooltip",yu.default],["tree",hu.default],["treegrid",gu.default],["treeitem",Cu.default]];zn.default=Pu;var Eu={},wu={};Object.defineProperty(wu,"__esModule",{value:!0}),wu.default=void 0;var xu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"abstract [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};wu.default=xu;var Ru={};Object.defineProperty(Ru,"__esModule",{value:!0}),Ru.default=void 0;var Ou={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"acknowledgments [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ru.default=Ou;var Tu={};Object.defineProperty(Tu,"__esModule",{value:!0}),Tu.default=void 0;var _u={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"afterword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Tu.default=_u;var Mu={};Object.defineProperty(Mu,"__esModule",{value:!0}),Mu.default=void 0;var Au={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"appendix [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Mu.default=Au;var ju={};Object.defineProperty(ju,"__esModule",{value:!0}),ju.default=void 0;var Su={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","content"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"referrer [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};ju.default=Su;var Bu={};Object.defineProperty(Bu,"__esModule",{value:!0}),Bu.default=void 0;var Iu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"EPUB biblioentry [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-bibliography"],requiredContextRole:["doc-bibliography"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};Bu.default=Iu;var Nu={};Object.defineProperty(Nu,"__esModule",{value:!0}),Nu.default=void 0;var ku={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"bibliography [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-biblioentry"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Nu.default=ku;var Fu={};Object.defineProperty(Fu,"__esModule",{value:!0}),Fu.default=void 0;var Lu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"biblioref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};Fu.default=Lu;var Uu={};Object.defineProperty(Uu,"__esModule",{value:!0}),Uu.default=void 0;var Du={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"chapter [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Uu.default=Du;var Hu={};Object.defineProperty(Hu,"__esModule",{value:!0}),Hu.default=void 0;var zu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"colophon [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Hu.default=zu;var Vu={};Object.defineProperty(Vu,"__esModule",{value:!0}),Vu.default=void 0;var $u={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"conclusion [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Vu.default=$u;var Wu={};Object.defineProperty(Wu,"__esModule",{value:!0}),Wu.default=void 0;var Gu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"cover [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]};Wu.default=Gu;var Qu={};Object.defineProperty(Qu,"__esModule",{value:!0}),Qu.default=void 0;var Ju={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credit [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Qu.default=Ju;var Ku={};Object.defineProperty(Ku,"__esModule",{value:!0}),Ku.default=void 0;var Xu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credits [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ku.default=Xu;var Yu={};Object.defineProperty(Yu,"__esModule",{value:!0}),Yu.default=void 0;var Zu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"dedication [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Yu.default=Zu;var es={};Object.defineProperty(es,"__esModule",{value:!0}),es.default=void 0;var ts={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-endnotes"],requiredContextRole:["doc-endnotes"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};es.default=ts;var rs={};Object.defineProperty(rs,"__esModule",{value:!0}),rs.default=void 0;var ns={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnotes [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-endnote"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};rs.default=ns;var as={};Object.defineProperty(as,"__esModule",{value:!0}),as.default=void 0;var os={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epigraph [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};as.default=os;var ls={};Object.defineProperty(ls,"__esModule",{value:!0}),ls.default=void 0;var is={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epilogue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ls.default=is;var us={};Object.defineProperty(us,"__esModule",{value:!0}),us.default=void 0;var ss={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"errata [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};us.default=ss;var ds={};Object.defineProperty(ds,"__esModule",{value:!0}),ds.default=void 0;var cs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ds.default=cs;var ps={};Object.defineProperty(ps,"__esModule",{value:!0}),ps.default=void 0;var ms={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"footnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ps.default=ms;var fs={};Object.defineProperty(fs,"__esModule",{value:!0}),fs.default=void 0;var bs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"foreword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};fs.default=bs;var vs={};Object.defineProperty(vs,"__esModule",{value:!0}),vs.default=void 0;var ys={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossary [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["definition"],["term"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};vs.default=ys;var hs={};Object.defineProperty(hs,"__esModule",{value:!0}),hs.default=void 0;var gs={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};hs.default=gs;var Cs={};Object.defineProperty(Cs,"__esModule",{value:!0}),Cs.default=void 0;var qs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"index [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Cs.default=qs;var Ps={};Object.defineProperty(Ps,"__esModule",{value:!0}),Ps.default=void 0;var Es={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"introduction [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ps.default=Es;var ws={};Object.defineProperty(ws,"__esModule",{value:!0}),ws.default=void 0;var xs={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"noteref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};ws.default=xs;var Rs={};Object.defineProperty(Rs,"__esModule",{value:!0}),Rs.default=void 0;var Os={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"notice [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};Rs.default=Os;var Ts={};Object.defineProperty(Ts,"__esModule",{value:!0}),Ts.default=void 0;var _s={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"pagebreak [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","separator"]]};Ts.default=_s;var Ms={};Object.defineProperty(Ms,"__esModule",{value:!0}),Ms.default=void 0;var As={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"page-list [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Ms.default=As;var js={};Object.defineProperty(js,"__esModule",{value:!0}),js.default=void 0;var Ss={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"part [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};js.default=Ss;var Bs={};Object.defineProperty(Bs,"__esModule",{value:!0}),Bs.default=void 0;var Is={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"preface [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Bs.default=Is;var Ns={};Object.defineProperty(Ns,"__esModule",{value:!0}),Ns.default=void 0;var ks={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"prologue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ns.default=ks;var Fs={};Object.defineProperty(Fs,"__esModule",{value:!0}),Fs.default=void 0;var Ls={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"pullquote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["none"]]};Fs.default=Ls;var Us={};Object.defineProperty(Us,"__esModule",{value:!0}),Us.default=void 0;var Ds={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"qna [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Us.default=Ds;var Hs={};Object.defineProperty(Hs,"__esModule",{value:!0}),Hs.default=void 0;var zs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"subtitle [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"]]};Hs.default=zs;var Vs={};Object.defineProperty(Vs,"__esModule",{value:!0}),Vs.default=void 0;var $s={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"help [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};Vs.default=$s;var Ws={};Object.defineProperty(Ws,"__esModule",{value:!0}),Ws.default=void 0;var Gs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"toc [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Ws.default=Gs,Object.defineProperty(Eu,"__esModule",{value:!0}),Eu.default=void 0;var Qs=Sd(wu),Js=Sd(Ru),Ks=Sd(Tu),Xs=Sd(Mu),Ys=Sd(ju),Zs=Sd(Bu),ed=Sd(Nu),td=Sd(Fu),rd=Sd(Uu),nd=Sd(Hu),ad=Sd(Vu),od=Sd(Wu),ld=Sd(Qu),id=Sd(Ku),ud=Sd(Yu),sd=Sd(es),dd=Sd(rs),cd=Sd(as),pd=Sd(ls),md=Sd(us),fd=Sd(ds),bd=Sd(ps),vd=Sd(fs),yd=Sd(vs),hd=Sd(hs),gd=Sd(Cs),Cd=Sd(Ps),qd=Sd(ws),Pd=Sd(Rs),Ed=Sd(Ts),wd=Sd(Ms),xd=Sd(js),Rd=Sd(Bs),Od=Sd(Ns),Td=Sd(Fs),_d=Sd(Us),Md=Sd(Hs),Ad=Sd(Vs),jd=Sd(Ws);function Sd(e){return e&&e.__esModule?e:{default:e}}var Bd=[["doc-abstract",Qs.default],["doc-acknowledgments",Js.default],["doc-afterword",Ks.default],["doc-appendix",Xs.default],["doc-backlink",Ys.default],["doc-biblioentry",Zs.default],["doc-bibliography",ed.default],["doc-biblioref",td.default],["doc-chapter",rd.default],["doc-colophon",nd.default],["doc-conclusion",ad.default],["doc-cover",od.default],["doc-credit",ld.default],["doc-credits",id.default],["doc-dedication",ud.default],["doc-endnote",sd.default],["doc-endnotes",dd.default],["doc-epigraph",cd.default],["doc-epilogue",pd.default],["doc-errata",md.default],["doc-example",fd.default],["doc-footnote",bd.default],["doc-foreword",vd.default],["doc-glossary",yd.default],["doc-glossref",hd.default],["doc-index",gd.default],["doc-introduction",Cd.default],["doc-noteref",qd.default],["doc-notice",Pd.default],["doc-pagebreak",Ed.default],["doc-pagelist",wd.default],["doc-part",xd.default],["doc-preface",Rd.default],["doc-prologue",Od.default],["doc-pullquote",Td.default],["doc-qna",_d.default],["doc-subtitle",Md.default],["doc-tip",Ad.default],["doc-toc",jd.default]];Eu.default=Bd,Object.defineProperty(rn,"__esModule",{value:!0}),rn.default=void 0;var Id=Fd(nn),Nd=Fd(zn),kd=Fd(Eu);function Fd(e){return e&&e.__esModule?e:{default:e}}function Ld(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ud(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=Hd(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}function Dd(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||Hd(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Hd(e,t){if(e){if("string"==typeof e)return zd(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?zd(e,t):void 0}}function zd(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Vd=[].concat(Id.default,Nd.default,kd.default);Vd.forEach((function(e){var t,r=Dd(e,2)[1],n=Ud(r.superClass);try{for(n.s();!(t=n.n()).done;){var a,o=Ud(t.value);try{var l=function(){var e=a.value,t=Vd.find((function(t){return Dd(t,1)[0]===e}));if(t)for(var n=t[1],o=0,l=Object.keys(n.props);o<l.length;o++){var i=l[o];Object.prototype.hasOwnProperty.call(r.props,i)||Object.assign(r.props,Ld({},i,n.props[i]))}};for(o.s();!(a=o.n()).done;)l()}catch(e){o.e(e)}finally{o.f()}}}catch(e){n.e(e)}finally{n.f()}}));var $d={entries:function(){return Vd},get:function(e){var t=Vd.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!this.get(e)},keys:function(){return Vd.map((function(e){return Dd(e,1)[0]}))},values:function(){return Vd.map((function(e){return Dd(e,2)[1]}))}};rn.default=$d;var Wd={};Object.defineProperty(Wd,"__esModule",{value:!0}),Wd.default=void 0;var Gd=function(e){return e&&e.__esModule?e:{default:e}}(rn);function Qd(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Jd(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Jd(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Jd(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var Kd=[],Xd=Gd.default.keys(),Yd=0;Yd<Xd.length;Yd++){var Zd=Xd[Yd],ec=Gd.default.get(Zd);if(ec)for(var tc=[].concat(ec.baseConcepts,ec.relatedConcepts),rc=0;rc<tc.length;rc++){var nc=tc[rc];if("HTML"===nc.module){var ac=nc.concept;ac&&function(){var e=JSON.stringify(ac),t=Kd.find((function(t){return JSON.stringify(t[0])===e})),r=void 0;r=t?t[1]:[];for(var n=!0,a=0;a<r.length;a++)if(r[a]===Zd){n=!1;break}n&&r.push(Zd),Kd.push([ac,r])}()}}}var oc={entries:function(){return Kd},get:function(e){var t=Kd.find((function(t){return JSON.stringify(t[0])===JSON.stringify(e)}));return t&&t[1]},has:function(e){return!!this.get(e)},keys:function(){return Kd.map((function(e){return Qd(e,1)[0]}))},values:function(){return Kd.map((function(e){return Qd(e,2)[1]}))}};Wd.default=oc;var lc={};Object.defineProperty(lc,"__esModule",{value:!0}),lc.default=void 0;var ic=function(e){return e&&e.__esModule?e:{default:e}}(rn);function uc(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return sc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sc(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function sc(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var dc=[],cc=ic.default.keys(),pc=function(e){var t=cc[e],r=ic.default.get(t);if(r)for(var n=[].concat(r.baseConcepts,r.relatedConcepts),a=0;a<n.length;a++){var o=n[a];if("HTML"===o.module){var l=o.concept;if(l){var i=dc.find((function(e){return e[0]===t})),u=void 0;(u=i?i[1]:[]).push(l),dc.push([t,u])}}}},mc=0;mc<cc.length;mc++)pc(mc);var fc={entries:function(){return dc},get:function(e){var t=dc.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!this.get(e)},keys:function(){return dc.map((function(e){return uc(e,1)[0]}))},values:function(){return dc.map((function(e){return uc(e,2)[1]}))}};lc.default=fc,Object.defineProperty($r,"__esModule",{value:!0});var bc=$r.roles=Tc=$r.roleElements=$r.elementRoles=$r.dom=$r.aria=void 0,vc=qc(Wr),yc=qc(Xr),hc=qc(rn),gc=qc(Wd),Cc=qc(lc);function qc(e){return e&&e.__esModule?e:{default:e}}var Pc=vc.default;$r.aria=Pc;var Ec=yc.default;$r.dom=Ec;var wc=hc.default;bc=$r.roles=wc;var xc=gc.default,Rc=$r.elementRoles=xc,Oc=Cc.default,Tc=$r.roleElements=Oc,_c={exports:{}};!function(e){var t=function(){var e=String.fromCharCode,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",n={};function a(e,t){if(!n[e]){n[e]={};for(var r=0;r<e.length;r++)n[e][e.charAt(r)]=r}return n[e][t]}var o={compressToBase64:function(e){if(null==e)return"";var r=o._compress(e,6,(function(e){return t.charAt(e)}));switch(r.length%4){default:case 0:return r;case 1:return r+"===";case 2:return r+"==";case 3:return r+"="}},decompressFromBase64:function(e){return null==e?"":""==e?null:o._decompress(e.length,32,(function(r){return a(t,e.charAt(r))}))},compressToUTF16:function(t){return null==t?"":o._compress(t,15,(function(t){return e(t+32)}))+" "},decompressFromUTF16:function(e){return null==e?"":""==e?null:o._decompress(e.length,16384,(function(t){return e.charCodeAt(t)-32}))},compressToUint8Array:function(e){for(var t=o.compress(e),r=new Uint8Array(2*t.length),n=0,a=t.length;n<a;n++){var l=t.charCodeAt(n);r[2*n]=l>>>8,r[2*n+1]=l%256}return r},decompressFromUint8Array:function(t){if(null==t)return o.decompress(t);for(var r=new Array(t.length/2),n=0,a=r.length;n<a;n++)r[n]=256*t[2*n]+t[2*n+1];var l=[];return r.forEach((function(t){l.push(e(t))})),o.decompress(l.join(""))},compressToEncodedURIComponent:function(e){return null==e?"":o._compress(e,6,(function(e){return r.charAt(e)}))},decompressFromEncodedURIComponent:function(e){return null==e?"":""==e?null:(e=e.replace(/ /g,"+"),o._decompress(e.length,32,(function(t){return a(r,e.charAt(t))})))},compress:function(t){return o._compress(t,16,(function(t){return e(t)}))},_compress:function(e,t,r){if(null==e)return"";var n,a,o,l={},i={},u="",s="",d="",c=2,p=3,m=2,f=[],b=0,v=0;for(o=0;o<e.length;o+=1)if(u=e.charAt(o),Object.prototype.hasOwnProperty.call(l,u)||(l[u]=p++,i[u]=!0),s=d+u,Object.prototype.hasOwnProperty.call(l,s))d=s;else{if(Object.prototype.hasOwnProperty.call(i,d)){if(d.charCodeAt(0)<256){for(n=0;n<m;n++)b<<=1,v==t-1?(v=0,f.push(r(b)),b=0):v++;for(a=d.charCodeAt(0),n=0;n<8;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}else{for(a=1,n=0;n<m;n++)b=b<<1|a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a=0;for(a=d.charCodeAt(0),n=0;n<16;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}0==--c&&(c=Math.pow(2,m),m++),delete i[d]}else for(a=l[d],n=0;n<m;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1;0==--c&&(c=Math.pow(2,m),m++),l[s]=p++,d=String(u)}if(""!==d){if(Object.prototype.hasOwnProperty.call(i,d)){if(d.charCodeAt(0)<256){for(n=0;n<m;n++)b<<=1,v==t-1?(v=0,f.push(r(b)),b=0):v++;for(a=d.charCodeAt(0),n=0;n<8;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}else{for(a=1,n=0;n<m;n++)b=b<<1|a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a=0;for(a=d.charCodeAt(0),n=0;n<16;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}0==--c&&(c=Math.pow(2,m),m++),delete i[d]}else for(a=l[d],n=0;n<m;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1;0==--c&&(c=Math.pow(2,m),m++)}for(a=2,n=0;n<m;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1;for(;;){if(b<<=1,v==t-1){f.push(r(b));break}v++}return f.join("")},decompress:function(e){return null==e?"":""==e?null:o._decompress(e.length,32768,(function(t){return e.charCodeAt(t)}))},_decompress:function(t,r,n){var a,o,l,i,u,s,d,c=[],p=4,m=4,f=3,b="",v=[],y={val:n(0),position:r,index:1};for(a=0;a<3;a+=1)c[a]=a;for(l=0,u=Math.pow(2,2),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;switch(l){case 0:for(l=0,u=Math.pow(2,8),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;d=e(l);break;case 1:for(l=0,u=Math.pow(2,16),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;d=e(l);break;case 2:return""}for(c[3]=d,o=d,v.push(d);;){if(y.index>t)return"";for(l=0,u=Math.pow(2,f),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;switch(d=l){case 0:for(l=0,u=Math.pow(2,8),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;c[m++]=e(l),d=m-1,p--;break;case 1:for(l=0,u=Math.pow(2,16),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;c[m++]=e(l),d=m-1,p--;break;case 2:return v.join("")}if(0==p&&(p=Math.pow(2,f),f++),c[d])b=c[d];else{if(d!==m)return null;b=o+o.charAt(0)}v.push(b),c[m++]=o+b.charAt(0),o=b,0==--p&&(p=Math.pow(2,f),f++)}}};return o}();null!=e&&(e.exports=t)}(_c);var Mc=_c.exports;function Ac(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")}const jc=(e,t)=>{const r=t.colors.content;return r.open+Ac(e)+r.close},Sc=/^((HTML|SVG)\w*)?Element$/;function Bc(e){return 11===e.nodeType}function Ic(e){return{test:e=>{var t;return(null==e||null==(t=e.constructor)?void 0:t.name)&&(e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,a="string"==typeof n&&n.includes("-")||"function"==typeof e.hasAttribute&&e.hasAttribute("is");return 1===r&&(Sc.test(t)||a)||3===r&&"Text"===t||8===r&&"Comment"===t||11===r&&"DocumentFragment"===t})(e)},serialize:(t,r,n,a,o,l)=>{if(function(e){return 3===e.nodeType}(t))return jc(t.data,r);if(function(e){return 8===e.nodeType}(t))return((e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+Ac(e)+"--\x3e"+r.close})(t.data,r);const i=Bc(t)?"DocumentFragment":t.tagName.toLowerCase();return++a>r.maxDepth?((e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close})(i,r):((e,t,r,n,a)=>{const o=n.colors.tag;return o.open+"<"+e+(t&&o.close+t+n.spacingOuter+a+o.open)+(r?">"+o.close+r+n.spacingOuter+a+o.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+o.close})(i,((e,t,r,n,a,o,l)=>{const i=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let d=l(s,r,i,a,o);return"string"!=typeof s&&(-1!==d.indexOf("\n")&&(d=r.spacingOuter+i+d+r.spacingOuter+n),d="{"+d+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+d+u.value.close})).join("")})(Bc(t)?[]:Array.from(t.attributes).map((e=>e.name)).sort(),Bc(t)?{}:Array.from(t.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),r,n+r.indent,a,o,l),((e,t,r,n,a,o)=>e.map((e=>{const l="string"==typeof e?jc(e,t):o(e,t,r,n,a);return""===l&&"object"==typeof e&&null!==e&&3!==e.nodeType?"":t.spacingOuter+r+l})).join(""))(Array.prototype.slice.call(t.childNodes||t.children).filter(e),r,n+r.indent,a,o,l),r,n)}}}let Nc=null,kc=null,Fc=null;try{const e=module&&module.require;kc=e.call(module,"fs").readFileSync,Fc=e.call(module,"@babel/code-frame").codeFrameColumns,Nc=e.call(module,"chalk")}catch{}function Lc(){if(!kc||!Fc)return"";return function(e){const t=e.indexOf("(")+1,r=e.indexOf(")"),n=e.slice(t,r),a=n.split(":"),[o,l,i]=[a[0],parseInt(a[1],10),parseInt(a[2],10)];let u="";try{u=kc(o,"utf-8")}catch{return""}const s=Fc(u,{start:{line:l,column:i}},{highlightCode:!0,linesBelow:0});return Nc.dim(n)+"\n"+s+"\n"}((new Error).stack.split("\n").slice(1).find((e=>!e.includes("node_modules/"))))}function Uc(){return"undefined"!=typeof jest&&null!==jest&&(!0===setTimeout._isMockFunction||Object.prototype.hasOwnProperty.call(setTimeout,"clock"))}function Dc(){if("undefined"==typeof window)throw new Error("Could not find default container");return window.document}function Hc(e){if(e.defaultView)return e.defaultView;if(e.ownerDocument&&e.ownerDocument.defaultView)return e.ownerDocument.defaultView;if(e.window)return e.window;throw e.ownerDocument&&null===e.ownerDocument.defaultView?new Error("It looks like the window object is not available for the provided node."):e.then instanceof Function?new Error("It looks like you passed a Promise object instead of a DOM node. Did you do something like `fireEvent.click(screen.findBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`, or await the findBy query `fireEvent.click(await screen.findBy...`?"):Array.isArray(e)?new Error("It looks like you passed an Array instead of a DOM node. Did you do something like `fireEvent.click(screen.getAllBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`?"):"function"==typeof e.debug&&"function"==typeof e.logTestingPlaygroundURL?new Error("It looks like you passed a `screen` object. Did you do something like `fireEvent.click(screen, ...` when you meant to use a query, e.g. `fireEvent.click(screen.getBy..., `?"):new Error("The given node is not an Element, the node type is: "+typeof e+".")}function zc(e){if(!e||"function"!=typeof e.querySelector||"function"!=typeof e.querySelectorAll)throw new TypeError("Expected container to be an Element, a Document or a DocumentFragment but got "+function(e){if("object"==typeof e)return null===e?"null":e.constructor.name;return typeof e}(e)+".")}const{DOMCollection:Vc}=ut;function $c(e){return 8!==e.nodeType&&(1!==e.nodeType||!e.matches(Kc().defaultIgnore))}function Wc(e,t,r){if(void 0===r&&(r={}),e||(e=Dc().body),"number"!=typeof t&&(t=7e3),0===t)return"";e.documentElement&&(e=e.documentElement);let n=typeof e;if("object"===n?n=e.constructor.name:e={},!("outerHTML"in e))throw new TypeError("Expected an element or document but got "+n);const{filterNode:a=$c,...o}=r,l=it(e,{plugins:[Ic(a),Vc],printFunctionName:!1,highlight:"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.node,...o});return void 0!==t&&e.outerHTML.length>t?l.slice(0,t)+"...":l}const Gc=function(){const e=Lc();e?console.log(Wc(...arguments)+"\n\n"+e):console.log(Wc(...arguments))};let Qc={testIdAttribute:"data-testid",asyncUtilTimeout:1e3,asyncWrapper:e=>e(),unstable_advanceTimersWrapper:e=>e(),eventWrapper:e=>e(),defaultHidden:!1,defaultIgnore:"script, style",showOriginalStackTrace:!1,throwSuggestions:!1,getElementError(e,t){const r=Wc(t),n=new Error([e,"Ignored nodes: comments, "+Qc.defaultIgnore+"\n"+r].filter(Boolean).join("\n\n"));return n.name="TestingLibraryElementError",n},_disableExpensiveErrorDiagnostics:!1,computedStyleSupportsPseudoElements:!1};function Jc(e){"function"==typeof e&&(e=e(Qc)),Qc={...Qc,...e}}function Kc(){return Qc}const Xc=["button","meter","output","progress","select","textarea","input"];function Yc(e){return Xc.includes(e.nodeName.toLowerCase())?"":3===e.nodeType?e.textContent:Array.from(e.childNodes).map((e=>Yc(e))).join("")}function Zc(e){let t;return t="label"===e.tagName.toLowerCase()?Yc(e):e.value||e.textContent,t}function ep(e){var t;if(void 0!==e.labels)return null!=(t=e.labels)?t:[];if(!function(e){return/BUTTON|METER|OUTPUT|PROGRESS|SELECT|TEXTAREA/.test(e.tagName)||"INPUT"===e.tagName&&"hidden"!==e.getAttribute("type")}(e))return[];const r=e.ownerDocument.querySelectorAll("label");return Array.from(r).filter((t=>t.control===e))}function tp(e,t,r){let{selector:n="*"}=void 0===r?{}:r;const a=t.getAttribute("aria-labelledby"),o=a?a.split(" "):[];return o.length?o.map((t=>{const r=e.querySelector('[id="'+t+'"]');return r?{content:Zc(r),formControl:null}:{content:"",formControl:null}})):Array.from(ep(t)).map((e=>({content:Zc(e),formControl:Array.from(e.querySelectorAll("button, input, meter, output, progress, select, textarea")).filter((e=>e.matches(n)))[0]})))}function rp(e){if(null==e)throw new Error("It looks like "+e+" was passed instead of a matcher. Did you do something like getByText("+e+")?")}function np(e,t,r,n){if("string"!=typeof e)return!1;rp(r);const a=n(e);return"string"==typeof r||"number"==typeof r?a.toLowerCase().includes(r.toString().toLowerCase()):"function"==typeof r?r(a,t):ip(r,a)}function ap(e,t,r,n){if("string"!=typeof e)return!1;rp(r);const a=n(e);return r instanceof Function?r(a,t):r instanceof RegExp?ip(r,a):a===String(r)}function op(e){let{trim:t=!0,collapseWhitespace:r=!0}=void 0===e?{}:e;return e=>{let n=e;return n=t?n.trim():n,n=r?n.replace(/\s+/g," "):n,n}}function lp(e){let{trim:t,collapseWhitespace:r,normalizer:n}=e;if(!n)return op({trim:t,collapseWhitespace:r});if(void 0!==t||void 0!==r)throw new Error('trim and collapseWhitespace are not supported with a normalizer. If you want to use the default trim and collapseWhitespace logic in your normalizer, use "getDefaultNormalizer({trim, collapseWhitespace})" and compose that into your normalizer');return n}function ip(e,t){const r=e.test(t);return e.global&&0!==e.lastIndex&&(console.warn("To match all elements we had to reset the lastIndex of the RegExp because the global flag is enabled. We encourage to remove the global flag from the RegExp."),e.lastIndex=0),r}function up(e){return e.matches("input[type=submit], input[type=button], input[type=reset]")?e.value:Array.from(e.childNodes).filter((e=>3===e.nodeType&&Boolean(e.textContent))).map((e=>e.textContent)).join("")}const sp=function(e){function t(e){let{attributes:t=[]}=e;return t.length}function r(e){let{attributes:t=[]}=e;const r=t.findIndex((e=>e.value&&"type"===e.name&&"text"===e.value));r>=0&&(t=[...t.slice(0,r),...t.slice(r+1)]);const n=function(e){let{name:t,attributes:r}=e;return""+t+r.map((e=>{let{name:t,value:r,constraints:n=[]}=e;return-1!==n.indexOf("undefined")?":not(["+t+"])":r?"["+t+'="'+r+'"]':"["+t+"]"})).join("")}({...e,attributes:t});return e=>!(r>=0&&"text"!==e.type)&&e.matches(n)}let n=[];for(const[a,o]of e.entries())n=[...n,{match:r(a),roles:Array.from(o),specificity:t(a)}];return n.sort((function(e,t){let{specificity:r}=e,{specificity:n}=t;return n-r}))}(Rc);function dp(e){if(!0===e.hidden)return!0;if("true"===e.getAttribute("aria-hidden"))return!0;return"none"===e.ownerDocument.defaultView.getComputedStyle(e).display}function cp(e,t){void 0===t&&(t={});const{isSubtreeInaccessible:r=dp}=t;if("hidden"===e.ownerDocument.defaultView.getComputedStyle(e).visibility)return!0;let n=e;for(;n;){if(r(n))return!0;n=n.parentElement}return!1}function pp(e){for(const{match:t,roles:r}of sp)if(t(e))return[...r];return[]}function mp(e,t){let{hidden:r=!1}=void 0===t?{}:t;return function e(t){return[t,...Array.from(t.children).reduce(((t,r)=>[...t,...e(r)]),[])]}(e).filter((e=>!1!==r||!1===cp(e))).reduce(((e,t)=>{let r=[];return r=t.hasAttribute("role")?t.getAttribute("role").split(" ").slice(0,1):pp(t),r.reduce(((e,r)=>Array.isArray(e[r])?{...e,[r]:[...e[r],t]}:{...e,[r]:[t]}),e)}),{})}function fp(e,t){let{hidden:r,includeDescription:n}=t;const a=mp(e,{hidden:r});return Object.entries(a).filter((e=>{let[t]=e;return"generic"!==t})).map((e=>{let[t,r]=e;const a="-".repeat(50);return t+":\n\n"+r.map((e=>{const t='Name "'+Vr(e,{computedStyleSupportsPseudoElements:Kc().computedStyleSupportsPseudoElements})+'":\n',r=Wc(e.cloneNode(!1));if(n){return""+t+('Description "'+Hr(e,{computedStyleSupportsPseudoElements:Kc().computedStyleSupportsPseudoElements})+'":\n')+r}return""+t+r})).join("\n\n")+"\n\n"+a})).join("\n")}function bp(e,t){const r=e.getAttribute(t);return"true"===r||"false"!==r&&void 0}const vp=op();function yp(e){return new RegExp(function(e){return e.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&")}(e.toLowerCase()),"i")}function hp(e,t,r,n){let{variant:a,name:o}=n,l="";const i={},u=[["Role","TestId"].includes(e)?r:yp(r)];o&&(i.name=yp(o)),"Role"===e&&cp(t)&&(i.hidden=!0,l="Element is inaccessible. This means that the element and all its children are invisible to screen readers.\n    If you are using the aria-hidden prop, make sure this is the right choice for your case.\n    "),Object.keys(i).length>0&&u.push(i);const s=a+"By"+e;return{queryName:e,queryMethod:s,queryArgs:u,variant:a,warning:l,toString(){l&&console.warn(l);let[e,t]=u;return e="string"==typeof e?"'"+e+"'":e,t=t?", { "+Object.entries(t).map((e=>{let[t,r]=e;return t+": "+r})).join(", ")+" }":"",s+"("+e+t+")"}}}function gp(e,t,r){return r&&(!t||t.toLowerCase()===e.toLowerCase())}function Cp(e,t,r){var n,a;if(void 0===t&&(t="get"),e.matches(Kc().defaultIgnore))return;const o=null!=(n=e.getAttribute("role"))?n:null==(a=pp(e))?void 0:a[0];if("generic"!==o&&gp("Role",r,o))return hp("Role",e,o,{variant:t,name:Vr(e,{computedStyleSupportsPseudoElements:Kc().computedStyleSupportsPseudoElements})});const l=tp(document,e).map((e=>e.content)).join(" ");if(gp("LabelText",r,l))return hp("LabelText",e,l,{variant:t});const i=e.getAttribute("placeholder");if(gp("PlaceholderText",r,i))return hp("PlaceholderText",e,i,{variant:t});const u=vp(up(e));if(gp("Text",r,u))return hp("Text",e,u,{variant:t});if(gp("DisplayValue",r,e.value))return hp("DisplayValue",e,vp(e.value),{variant:t});const s=e.getAttribute("alt");if(gp("AltText",r,s))return hp("AltText",e,s,{variant:t});const d=e.getAttribute("title");if(gp("Title",r,d))return hp("Title",e,d,{variant:t});const c=e.getAttribute(Kc().testIdAttribute);return gp("TestId",r,c)?hp("TestId",e,c,{variant:t}):void 0}function qp(e,t){e.stack=t.stack.replace(t.message,e.message)}function Pp(e,t){let{container:r=Dc(),timeout:n=Kc().asyncUtilTimeout,showOriginalStackTrace:a=Kc().showOriginalStackTrace,stackTraceError:o,interval:l=50,onTimeout:i=(e=>(e.message=Kc().getElementError(e.message,r).message,e)),mutationObserverOptions:u={subtree:!0,childList:!0,attributes:!0,characterData:!0}}=t;if("function"!=typeof e)throw new TypeError("Received `callback` arg must be a function");return new Promise((async(t,s)=>{let d,c,p,m=!1,f="idle";const b=setTimeout((function(){let e;d?(e=d,a||"TestingLibraryElementError"!==e.name||qp(e,o)):(e=new Error("Timed out in waitFor."),a||qp(e,o)),y(i(e),null)}),n),v=Uc();if(v){const{unstable_advanceTimersWrapper:e}=Kc();for(g();!m;){if(!Uc()){const e=new Error("Changed from using fake timers to real timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to real timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return a||qp(e,o),void s(e)}if(e((()=>{jest.advanceTimersByTime(l)})),g(),m)break;await e((async()=>{await new Promise((e=>{setTimeout(e,0),jest.advanceTimersByTime(0)}))}))}}else{try{zc(r)}catch(e){return void s(e)}c=setInterval(h,l);const{MutationObserver:e}=Hc(r);p=new e(h),p.observe(r,u),g()}function y(e,r){m=!0,clearTimeout(b),v||(clearInterval(c),p.disconnect()),e?s(e):t(r)}function h(){if(Uc()){const e=new Error("Changed from using real timers to fake timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to fake timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return a||qp(e,o),s(e)}return g()}function g(){if("pending"!==f)try{const t=function(e){try{return Qc._disableExpensiveErrorDiagnostics=!0,e()}finally{Qc._disableExpensiveErrorDiagnostics=!1}}(e);"function"==typeof(null==t?void 0:t.then)?(f="pending",t.then((e=>{f="resolved",y(null,e)}),(e=>{f="rejected",d=e}))):y(null,t)}catch(e){d=e}}}))}function Ep(e,t){const r=new Error("STACK_TRACE_MESSAGE");return Kc().asyncWrapper((()=>Pp(e,{stackTraceError:r,...t})))}function wp(e,t){return Kc().getElementError(e,t)}function xp(e,t){return wp(e+"\n\n(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).",t)}function Rp(e,t,r,n){let{exact:a=!0,collapseWhitespace:o,trim:l,normalizer:i}=void 0===n?{}:n;const u=a?ap:np,s=lp({collapseWhitespace:o,trim:l,normalizer:i});return Array.from(t.querySelectorAll("["+e+"]")).filter((t=>u(t.getAttribute(e),t,r,s)))}function Op(e,t,r,n){const a=Rp(e,t,r,n);if(a.length>1)throw xp("Found multiple elements by ["+e+"="+r+"]",t);return a[0]||null}function Tp(e,t){return function(r){for(var n=arguments.length,a=new Array(n>1?n-1:0),o=1;o<n;o++)a[o-1]=arguments[o];const l=e(r,...a);if(l.length>1){const e=l.map((e=>wp(null,e).message)).join("\n\n");throw xp(t(r,...a)+"\n\nHere are the matching elements:\n\n"+e,r)}return l[0]||null}}function _p(e,t){return Kc().getElementError("A better query is available, try this:\n"+e.toString()+"\n",t)}function Mp(e,t){return function(r){for(var n=arguments.length,a=new Array(n>1?n-1:0),o=1;o<n;o++)a[o-1]=arguments[o];const l=e(r,...a);if(!l.length)throw Kc().getElementError(t(r,...a),r);return l}}function Ap(e){return(t,r,n,a)=>Ep((()=>e(t,r,n)),{container:t,...a})}const jp=(e,t,r)=>function(n){for(var a=arguments.length,o=new Array(a>1?a-1:0),l=1;l<a;l++)o[l-1]=arguments[l];const i=e(n,...o),[{suggest:u=Kc().throwSuggestions}={}]=o.slice(-1);if(i&&u){const e=Cp(i,r);if(e&&!t.endsWith(e.queryName))throw _p(e.toString(),n)}return i},Sp=(e,t,r)=>function(n){for(var a=arguments.length,o=new Array(a>1?a-1:0),l=1;l<a;l++)o[l-1]=arguments[l];const i=e(n,...o),[{suggest:u=Kc().throwSuggestions}={}]=o.slice(-1);if(i.length&&u){const e=[...new Set(i.map((e=>{var t;return null==(t=Cp(e,r))?void 0:t.toString()})))];if(1===e.length&&!t.endsWith(Cp(i[0],r).queryName))throw _p(e[0],n)}return i};function Bp(e,t,r){const n=jp(Tp(e,t),e.name,"query"),a=Mp(e,r),o=Tp(a,t),l=jp(o,e.name,"get");return[n,Sp(a,e.name.replace("query","get"),"getAll"),l,Ap(Sp(a,e.name,"findAll")),Ap(jp(o,e.name,"find"))]}var Ip=Object.freeze({__proto__:null,getElementError:wp,wrapAllByQueryWithSuggestion:Sp,wrapSingleQueryWithSuggestion:jp,getMultipleElementsFoundError:xp,queryAllByAttribute:Rp,queryByAttribute:Op,makeSingleQuery:Tp,makeGetAllQuery:Mp,makeFindQuery:Ap,buildQueries:Bp});const Np=function(e,t,r){let{exact:n=!0,trim:a,collapseWhitespace:o,normalizer:l}=void 0===r?{}:r;const i=n?ap:np,u=lp({collapseWhitespace:o,trim:a,normalizer:l}),s=function(e){return Array.from(e.querySelectorAll("label,input")).map((e=>({node:e,textToMatch:Zc(e)}))).filter((e=>{let{textToMatch:t}=e;return null!==t}))}(e);return s.filter((e=>{let{node:r,textToMatch:n}=e;return i(n,r,t,u)})).map((e=>{let{node:t}=e;return t}))},kp=function(e,t,r){let{selector:n="*",exact:a=!0,collapseWhitespace:o,trim:l,normalizer:i}=void 0===r?{}:r;zc(e);const u=a?ap:np,s=lp({collapseWhitespace:o,trim:l,normalizer:i}),d=Array.from(e.querySelectorAll("*")).filter((e=>ep(e).length||e.hasAttribute("aria-labelledby"))).reduce(((r,a)=>{const o=tp(e,a,{selector:n});o.filter((e=>Boolean(e.formControl))).forEach((e=>{u(e.content,e.formControl,t,s)&&e.formControl&&r.push(e.formControl)}));const l=o.filter((e=>Boolean(e.content))).map((e=>e.content));return u(l.join(" "),a,t,s)&&r.push(a),l.length>1&&l.forEach(((e,n)=>{u(e,a,t,s)&&r.push(a);const o=[...l];o.splice(n,1),o.length>1&&u(o.join(" "),a,t,s)&&r.push(a)})),r}),[]).concat(Rp("aria-label",e,t,{exact:a,normalizer:s}));return Array.from(new Set(d)).filter((e=>e.matches(n)))},Fp=function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),a=2;a<r;a++)n[a-2]=arguments[a];const o=kp(e,t,...n);if(!o.length){const r=Np(e,t,...n);if(r.length){const n=r.map((t=>Lp(e,t))).filter((e=>!!e));throw n.length?Kc().getElementError(n.map((e=>"Found a label with the text of: "+t+", however the element associated with this label (<"+e+" />) is non-labellable [https://html.spec.whatwg.org/multipage/forms.html#category-label]. If you really need to label a <"+e+" />, you can use aria-label or aria-labelledby instead.")).join("\n\n"),e):Kc().getElementError("Found a label with the text of: "+t+', however no form control was found associated to that label. Make sure you\'re using the "for" attribute or "aria-labelledby" attribute correctly.',e)}throw Kc().getElementError("Unable to find a label with the text of: "+t,e)}return o};function Lp(e,t){const r=t.getAttribute("for");if(!r)return null;const n=e.querySelector('[id="'+r+'"]');return n?n.tagName.toLowerCase():null}const Up=(e,t)=>"Found multiple elements with the text of: "+t,Dp=jp(Tp(kp,Up),kp.name,"query"),Hp=Tp(Fp,Up),zp=Ap(Sp(Fp,Fp.name,"findAll")),Vp=Ap(jp(Hp,Fp.name,"find")),$p=Sp(Fp,Fp.name,"getAll"),Wp=jp(Hp,Fp.name,"get"),Gp=Sp(kp,kp.name,"queryAll"),Qp=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return zc(t[0]),Rp("placeholder",...t)},Jp=Sp(Qp,Qp.name,"queryAll"),[Kp,Xp,Yp,Zp,em]=Bp(Qp,((e,t)=>"Found multiple elements with the placeholder text of: "+t),((e,t)=>"Unable to find an element with the placeholder text of: "+t)),tm=function(e,t,r){let{selector:n="*",exact:a=!0,collapseWhitespace:o,trim:l,ignore:i=Kc().defaultIgnore,normalizer:u}=void 0===r?{}:r;zc(e);const s=a?ap:np,d=lp({collapseWhitespace:o,trim:l,normalizer:u});let c=[];return"function"==typeof e.matches&&e.matches(n)&&(c=[e]),[...c,...Array.from(e.querySelectorAll(n))].filter((e=>!i||!e.matches(i))).filter((e=>s(up(e),e,t,d)))},rm=Sp(tm,tm.name,"queryAll"),[nm,am,om,lm,im]=Bp(tm,((e,t)=>"Found multiple elements with the text: "+t),(function(e,t,r){void 0===r&&(r={});const{collapseWhitespace:n,trim:a,normalizer:o}=r,l=lp({collapseWhitespace:n,trim:a,normalizer:o})(t.toString());return"Unable to find an element with the text: "+(l!==t.toString()?l+" (normalized from '"+t+"')":t)+". This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible."})),um=function(e,t,r){let{exact:n=!0,collapseWhitespace:a,trim:o,normalizer:l}=void 0===r?{}:r;zc(e);const i=n?ap:np,u=lp({collapseWhitespace:a,trim:o,normalizer:l});return Array.from(e.querySelectorAll("input,textarea,select")).filter((e=>{if("SELECT"===e.tagName){return Array.from(e.options).filter((e=>e.selected)).some((e=>i(up(e),e,t,u)))}return i(e.value,e,t,u)}))},sm=Sp(um,um.name,"queryAll"),[dm,cm,pm,mm,fm]=Bp(um,((e,t)=>"Found multiple elements with the display value: "+t+"."),((e,t)=>"Unable to find an element with the display value: "+t+".")),bm=/^(img|input|area|.+-.+)$/i,vm=function(e,t,r){return void 0===r&&(r={}),zc(e),Rp("alt",e,t,r).filter((e=>bm.test(e.tagName)))},ym=Sp(vm,vm.name,"queryAll"),[hm,gm,Cm,qm,Pm]=Bp(vm,((e,t)=>"Found multiple elements with the alt text: "+t),((e,t)=>"Unable to find an element with the alt text: "+t)),Em=function(e,t,r){let{exact:n=!0,collapseWhitespace:a,trim:o,normalizer:l}=void 0===r?{}:r;zc(e);const i=n?ap:np,u=lp({collapseWhitespace:a,trim:o,normalizer:l});return Array.from(e.querySelectorAll("[title], svg > title")).filter((e=>i(e.getAttribute("title"),e,t,u)||(e=>{var t;return"title"===e.tagName.toLowerCase()&&"svg"===(null==(t=e.parentElement)?void 0:t.tagName.toLowerCase())})(e)&&i(up(e),e,t,u)))},wm=Sp(Em,Em.name,"queryAll"),[xm,Rm,Om,Tm,_m]=Bp(Em,((e,t)=>"Found multiple elements with the title: "+t+"."),((e,t)=>"Unable to find an element with the title: "+t+"."));function Mm(e,t,r){let{exact:n=!0,collapseWhitespace:a,hidden:o=Kc().defaultHidden,name:l,description:i,trim:u,normalizer:s,queryFallbacks:d=!1,selected:c,checked:p,pressed:m,current:f,level:b,expanded:v}=void 0===r?{}:r;zc(e);const y=n?ap:np,h=lp({collapseWhitespace:a,trim:u,normalizer:s});var g,C,q,P,E;if(void 0!==c&&void 0===(null==(g=bc.get(t))?void 0:g.props["aria-selected"]))throw new Error('"aria-selected" is not supported on role "'+t+'".');if(void 0!==p&&void 0===(null==(C=bc.get(t))?void 0:C.props["aria-checked"]))throw new Error('"aria-checked" is not supported on role "'+t+'".');if(void 0!==m&&void 0===(null==(q=bc.get(t))?void 0:q.props["aria-pressed"]))throw new Error('"aria-pressed" is not supported on role "'+t+'".');if(void 0!==f&&void 0===(null==(P=bc.get(t))?void 0:P.props["aria-current"]))throw new Error('"aria-current" is not supported on role "'+t+'".');if(void 0!==b&&"heading"!==t)throw new Error('Role "'+t+'" cannot have "level" property.');if(void 0!==v&&void 0===(null==(E=bc.get(t))?void 0:E.props["aria-expanded"]))throw new Error('"aria-expanded" is not supported on role "'+t+'".');const w=new WeakMap;function x(e){return w.has(e)||w.set(e,dp(e)),w.get(e)}return Array.from(e.querySelectorAll(function(e,t,r){var n;if("string"!=typeof e)return"*";const a=t&&!r?'*[role~="'+e+'"]':"*[role]",o=null!=(n=Tc.get(e))?n:new Set,l=new Set(Array.from(o).map((e=>{let{name:t}=e;return t})));return[a].concat(Array.from(l)).join(",")}(t,n,s?h:void 0))).filter((e=>{if(e.hasAttribute("role")){const r=e.getAttribute("role");if(d)return r.split(" ").filter(Boolean).some((r=>y(r,e,t,h)));if(s)return y(r,e,t,h);const[n]=r.split(" ");return y(n,e,t,h)}return pp(e).some((r=>y(r,e,t,h)))})).filter((e=>void 0!==c?c===function(e){return"OPTION"===e.tagName?e.selected:bp(e,"aria-selected")}(e):void 0!==p?p===function(e){if(!("indeterminate"in e)||!e.indeterminate)return"checked"in e?e.checked:bp(e,"aria-checked")}(e):void 0!==m?m===function(e){return bp(e,"aria-pressed")}(e):void 0!==f?f===function(e){var t,r;return null!=(t=null!=(r=bp(e,"aria-current"))?r:e.getAttribute("aria-current"))&&t}(e):void 0!==v?v===function(e){return bp(e,"aria-expanded")}(e):void 0===b||b===function(e){return e.getAttribute("aria-level")&&Number(e.getAttribute("aria-level"))||{H1:1,H2:2,H3:3,H4:4,H5:5,H6:6}[e.tagName]}(e))).filter((e=>void 0===l||ap(Vr(e,{computedStyleSupportsPseudoElements:Kc().computedStyleSupportsPseudoElements}),e,l,(e=>e)))).filter((e=>void 0===i||ap(Hr(e,{computedStyleSupportsPseudoElements:Kc().computedStyleSupportsPseudoElements}),e,i,(e=>e)))).filter((e=>!1!==o||!1===cp(e,{isSubtreeInaccessible:x})))}const Am=e=>{let t="";return t=void 0===e?"":"string"==typeof e?' and name "'+e+'"':" and name `"+e+"`",t},jm=Sp(Mm,Mm.name,"queryAll"),[Sm,Bm,Im,Nm,km]=Bp(Mm,(function(e,t,r){let{name:n}=void 0===r?{}:r;return'Found multiple elements with the role "'+t+'"'+Am(n)}),(function(e,t,r){let{hidden:n=Kc().defaultHidden,name:a,description:o}=void 0===r?{}:r;if(Kc()._disableExpensiveErrorDiagnostics)return'Unable to find role="'+t+'"'+Am(a);let l,i="";Array.from(e.children).forEach((e=>{i+=fp(e,{hidden:n,includeDescription:void 0!==o})})),l=0===i.length?!1===n?"There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole":"There are no available roles.":("\nHere are the "+(!1===n?"accessible":"available")+" roles:\n\n  "+i.replace(/\n/g,"\n  ").replace(/\n\s\s\n/g,"\n\n")+"\n").trim();let u="";u=void 0===a?"":"string"==typeof a?' and name "'+a+'"':" and name `"+a+"`";let s="";return s=void 0===o?"":"string"==typeof o?' and description "'+o+'"':" and description `"+o+"`",("\nUnable to find an "+(!1===n?"accessible ":"")+'element with the role "'+t+'"'+u+s+"\n\n"+l).trim()})),Fm=()=>Kc().testIdAttribute,Lm=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return zc(t[0]),Rp(Fm(),...t)},Um=Sp(Lm,Lm.name,"queryAll"),[Dm,Hm,zm,Vm,$m]=Bp(Lm,((e,t)=>"Found multiple elements by: ["+Fm()+'="'+t+'"]'),((e,t)=>"Unable to find an element by: ["+Fm()+'="'+t+'"]'));var Wm=Object.freeze({__proto__:null,queryAllByLabelText:Gp,queryByLabelText:Dp,getAllByLabelText:$p,getByLabelText:Wp,findAllByLabelText:zp,findByLabelText:Vp,queryByPlaceholderText:Kp,queryAllByPlaceholderText:Jp,getByPlaceholderText:Yp,getAllByPlaceholderText:Xp,findAllByPlaceholderText:Zp,findByPlaceholderText:em,queryByText:nm,queryAllByText:rm,getByText:om,getAllByText:am,findAllByText:lm,findByText:im,queryByDisplayValue:dm,queryAllByDisplayValue:sm,getByDisplayValue:pm,getAllByDisplayValue:cm,findAllByDisplayValue:mm,findByDisplayValue:fm,queryByAltText:hm,queryAllByAltText:ym,getByAltText:Cm,getAllByAltText:gm,findAllByAltText:qm,findByAltText:Pm,queryByTitle:xm,queryAllByTitle:wm,getByTitle:Om,getAllByTitle:Rm,findAllByTitle:Tm,findByTitle:_m,queryByRole:Sm,queryAllByRole:jm,getAllByRole:Bm,getByRole:Im,findAllByRole:Nm,findByRole:km,queryByTestId:Dm,queryAllByTestId:Um,getByTestId:zm,getAllByTestId:Hm,findAllByTestId:Vm,findByTestId:$m});function Gm(e,t,r){return void 0===t&&(t=Wm),void 0===r&&(r={}),Object.keys(t).reduce(((r,n)=>{const a=t[n];return r[n]=a.bind(null,e),r}),r)}const Qm=e=>!e||Array.isArray(e)&&!e.length;function Jm(e){if(Qm(e))throw new Error("The element(s) given to waitForElementToBeRemoved are already removed. waitForElementToBeRemoved requires that the element(s) exist(s) before waiting for removal.")}const Km={copy:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},cut:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},paste:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionEnd:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionStart:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionUpdate:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},keyDown:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyPress:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyUp:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},focus:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},blur:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},focusIn:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},focusOut:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},change:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},input:{EventType:"InputEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},invalid:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!0}},submit:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},reset:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},click:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,button:0,composed:!0}},contextMenu:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dblClick:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drag:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragEnd:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragEnter:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragExit:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragLeave:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragOver:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragStart:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drop:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseDown:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseEnter:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseLeave:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseMove:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOut:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOver:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseUp:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},select:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},touchCancel:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},touchEnd:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchMove:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchStart:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},resize:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},scroll:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},wheel:{EventType:"WheelEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},abort:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlay:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlayThrough:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},durationChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},emptied:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},encrypted:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},ended:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedData:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedMetadata:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadStart:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},pause:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},play:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},playing:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},progress:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},rateChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeked:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeking:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},stalled:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},suspend:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},timeUpdate:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},volumeChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},waiting:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},load:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},error:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},animationStart:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationEnd:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationIteration:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionCancel:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionEnd:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!0}},transitionRun:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionStart:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},pointerOver:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerEnter:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},pointerDown:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerMove:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerUp:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerCancel:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},pointerOut:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerLeave:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},gotPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},lostPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},popState:{EventType:"PopStateEvent",defaultInit:{bubbles:!0,cancelable:!1}}},Xm={doubleClick:"dblClick"};function Ym(e,t){return Kc().eventWrapper((()=>{if(!t)throw new Error("Unable to fire an event - please provide an event object.");if(!e)throw new Error('Unable to fire a "'+t.type+'" event - please provide a DOM element.');return e.dispatchEvent(t)}))}function Zm(e,t,r,n){let{EventType:a="Event",defaultInit:o={}}=void 0===n?{}:n;if(!t)throw new Error('Unable to fire a "'+e+'" event - please provide a DOM element.');const l={...o,...r},{target:{value:i,files:u,...s}={}}=l;void 0!==i&&function(e,t){const{set:r}=Object.getOwnPropertyDescriptor(e,"value")||{},n=Object.getPrototypeOf(e),{set:a}=Object.getOwnPropertyDescriptor(n,"value")||{};if(a&&r!==a)a.call(e,t);else{if(!r)throw new Error("The given element does not have a value setter");r.call(e,t)}}(t,i),void 0!==u&&Object.defineProperty(t,"files",{configurable:!0,enumerable:!0,writable:!0,value:u}),Object.assign(t,s);const d=Hc(t),c=d[a]||d.Event;let p;if("function"==typeof c)p=new c(e,l);else{p=d.document.createEvent(a);const{bubbles:t,cancelable:r,detail:n,...o}=l;p.initEvent(e,t,r,n),Object.keys(o).forEach((e=>{p[e]=o[e]}))}return["dataTransfer","clipboardData"].forEach((e=>{const t=l[e];"object"==typeof t&&("function"==typeof d.DataTransfer?Object.defineProperty(p,e,{value:Object.getOwnPropertyNames(t).reduce(((e,r)=>(Object.defineProperty(e,r,{value:t[r]}),e)),new d.DataTransfer)}):Object.defineProperty(p,e,{value:t}))})),p}function ef(e){return"https://testing-playground.com/#markup="+(t=e,Mc.compressToEncodedURIComponent(t.replace(/[ \t]*[\n][ \t]*/g,"\n")));var t}Object.keys(Km).forEach((e=>{const{EventType:t,defaultInit:r}=Km[e],n=e.toLowerCase();Zm[e]=(e,a)=>Zm(n,e,a,{EventType:t,defaultInit:r}),Ym[e]=(t,r)=>Ym(t,Zm[e](t,r))})),Object.keys(Xm).forEach((e=>{const t=Xm[e];Ym[e]=function(){return Ym[t](...arguments)}}));const tf={debug:(e,t,r)=>Array.isArray(e)?e.forEach((e=>Gc(e,t,r))):Gc(e,t,r),logTestingPlaygroundURL:function(e){if(void 0===e&&(e=Dc().body),!e||!("innerHTML"in e))return void console.log("The element you're providing isn't a valid DOM element.");if(!e.innerHTML)return void console.log("The provided element doesn't have any children.");const t=ef(e.innerHTML);return console.log("Open this URL in your browser\n\n"+t),t}},rf="undefined"!=typeof document&&document.body?Gm(document.body,Wm,tf):Object.keys(Wm).reduce(((e,t)=>(e[t]=()=>{throw new TypeError("For queries bound to document.body a global document has to be available... Learn more: https://testing-library.com/s/screen-global-error")},e)),tf),nf=function(){return Ym(...arguments)};Object.keys(Ym).forEach((e=>{nf[e]=function(){return Ym[e](...arguments)}}));const af=nf.mouseEnter,of=nf.mouseLeave;nf.mouseEnter=function(){return af(...arguments),nf.mouseOver(...arguments)},nf.mouseLeave=function(){return of(...arguments),nf.mouseOut(...arguments)};const lf=nf.pointerEnter,uf=nf.pointerLeave;nf.pointerEnter=function(){return lf(...arguments),nf.pointerOver(...arguments)},nf.pointerLeave=function(){return uf(...arguments),nf.pointerOut(...arguments)};const sf=nf.select;nf.select=(e,t)=>{sf(e,t),e.focus(),nf.keyUp(e,t)};const df=nf.blur,cf=nf.focus;nf.blur=function(){return nf.focusOut(...arguments),df(...arguments)},nf.focus=function(){return nf.focusIn(...arguments),cf(...arguments)},Jc({unstable_advanceTimersWrapper:e=>v(e),asyncWrapper:async e=>{const t=b();f(!1);try{return await e()}finally{f(t)}},eventWrapper:e=>{let t;return v((()=>{t=e()})),t}});const pf=new Set,mf=[];function ff(e,t){let r,{hydrate:n,ui:a,wrapper:o}=t;return n?v((()=>{r=c.hydrateRoot(e,o?s.createElement(o,null,a):a)})):r=c.createRoot(e),{hydrate(){if(!n)throw new Error("Attempted to hydrate a non-hydrateable root. This is a bug in `@testing-library/react`.")},render(e){r.render(e)},unmount(){r.unmount()}}}function bf(e){return{hydrate(t){d.default.hydrate(t,e)},render(t){d.default.render(t,e)},unmount(){d.default.unmountComponentAtNode(e)}}}function vf(e,t){let{baseElement:r,container:n,hydrate:a,queries:o,root:l,wrapper:i}=t;const u=e=>i?s.createElement(i,null,e):e;return v((()=>{a?l.hydrate(u(e),n):l.render(u(e),n)})),{container:n,baseElement:r,debug:function(e,t,n){return void 0===e&&(e=r),Array.isArray(e)?e.forEach((e=>console.log(Wc(e,t,n)))):console.log(Wc(e,t,n))},unmount:()=>{v((()=>{l.unmount()}))},rerender:e=>{vf(u(e),{container:n,baseElement:r,root:l})},asFragment:()=>{if("function"==typeof document.createRange)return document.createRange().createContextualFragment(n.innerHTML);{const e=document.createElement("template");return e.innerHTML=n.innerHTML,e.content}},...Gm(r,o)}}function yf(e,t){let r,{container:n,baseElement:a=n,legacyRoot:o=!1,queries:l,hydrate:i=!1,wrapper:u}=void 0===t?{}:t;if(a||(a=document.body),n||(n=a.appendChild(document.createElement("div"))),pf.has(n))mf.forEach((e=>{e.container===n&&(r=e.root)}));else{r=(o?bf:ff)(n,{hydrate:i,ui:e,wrapper:u}),mf.push({container:n,root:r}),pf.add(n)}return vf(e,{container:n,baseElement:a,queries:l,hydrate:i,wrapper:u,root:r})}function hf(){mf.forEach((e=>{let{root:t,container:r}=e;v((()=>{t.unmount()})),r.parentNode===document.body&&document.body.removeChild(r)})),mf.length=0,pf.clear()}var gf;if(("undefined"==typeof process||null==(gf=process.env)||!gf.RTL_SKIP_AUTO_CLEANUP)&&("function"==typeof afterEach?afterEach((()=>{hf()})):"function"==typeof teardown&&teardown((()=>{hf()})),"function"==typeof beforeAll&&"function"==typeof afterAll)){let e=b();beforeAll((()=>{e=b(),f(!0)})),afterAll((()=>{f(e)}))}e.act=v,e.buildQueries=Bp,e.cleanup=hf,e.configure=Jc,e.createEvent=Zm,e.findAllByAltText=qm,e.findAllByDisplayValue=mm,e.findAllByLabelText=zp,e.findAllByPlaceholderText=Zp,e.findAllByRole=Nm,e.findAllByTestId=Vm,e.findAllByText=lm,e.findAllByTitle=Tm,e.findByAltText=Pm,e.findByDisplayValue=fm,e.findByLabelText=Vp,e.findByPlaceholderText=em,e.findByRole=km,e.findByTestId=$m,e.findByText=im,e.findByTitle=_m,e.fireEvent=nf,e.getAllByAltText=gm,e.getAllByDisplayValue=cm,e.getAllByLabelText=$p,e.getAllByPlaceholderText=Xp,e.getAllByRole=Bm,e.getAllByTestId=Hm,e.getAllByText=am,e.getAllByTitle=Rm,e.getByAltText=Cm,e.getByDisplayValue=pm,e.getByLabelText=Wp,e.getByPlaceholderText=Yp,e.getByRole=Im,e.getByTestId=zm,e.getByText=om,e.getByTitle=Om,e.getConfig=Kc,e.getDefaultNormalizer=op,e.getElementError=wp,e.getMultipleElementsFoundError=xp,e.getNodeText=up,e.getQueriesForElement=Gm,e.getRoles=mp,e.getSuggestedQuery=Cp,e.isInaccessible=cp,e.logDOM=Gc,e.logRoles=function(e,t){let{hidden:r=!1}=void 0===t?{}:t;return console.log(fp(e,{hidden:r}))},e.makeFindQuery=Ap,e.makeGetAllQuery=Mp,e.makeSingleQuery=Tp,e.prettyDOM=Wc,e.prettyFormat=Gt,e.queries=Wm,e.queryAllByAltText=ym,e.queryAllByAttribute=Rp,e.queryAllByDisplayValue=sm,e.queryAllByLabelText=Gp,e.queryAllByPlaceholderText=Jp,e.queryAllByRole=jm,e.queryAllByTestId=Um,e.queryAllByText=rm,e.queryAllByTitle=wm,e.queryByAltText=hm,e.queryByAttribute=Op,e.queryByDisplayValue=dm,e.queryByLabelText=Dp,e.queryByPlaceholderText=Kp,e.queryByRole=Sm,e.queryByTestId=Dm,e.queryByText=nm,e.queryByTitle=xm,e.queryHelpers=Ip,e.render=yf,e.renderHook=function(e,t){void 0===t&&(t={});const{initialProps:r,...n}=t,a=s.createRef();function o(t){let{renderCallbackProps:r}=t;const n=e(r);return s.useEffect((()=>{a.current=n})),null}const{rerender:l,unmount:i}=yf(s.createElement(o,{renderCallbackProps:r}),n);return{result:a,rerender:function(e){return l(s.createElement(o,{renderCallbackProps:e}))},unmount:i}},e.screen=rf,e.waitFor=Ep,e.waitForElementToBeRemoved=async function(e,t){const r=new Error("Timed out in waitForElementToBeRemoved.");if("function"!=typeof e){Jm(e);const t=(Array.isArray(e)?e:[e]).map((e=>{let t=e.parentElement;if(null===t)return()=>null;for(;t.parentElement;)t=t.parentElement;return()=>t.contains(e)?e:null}));e=()=>t.map((e=>e())).filter(Boolean)}return Jm(e()),Ep((()=>{let t;try{t=e()}catch(e){if("TestingLibraryElementError"===e.name)return;throw e}if(!Qm(t))throw r}),t)},e.within=Gm,e.wrapAllByQueryWithSuggestion=Sp,e.wrapSingleQueryWithSuggestion=jp,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=react.umd.min.js.map
