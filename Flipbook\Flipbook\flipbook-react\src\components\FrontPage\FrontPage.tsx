import React, { useState } from 'react';
import TextEditor from '../TextEditor/TextEditor';
import ImageLibrary from '../ImageLibrary/ImageLibrary';
import './FrontPage.css';

interface FrontPageProps {
  title: string;
  subtitle: string;
  backgroundImage: string;
  onTitleChange: (title: string) => void;
  onSubtitleChange: (subtitle: string) => void;
  onBackgroundImageChange: (imageUrl: string) => void;
  onNext: () => void;
}

const FrontPage: React.FC<FrontPageProps> = ({
  title,
  subtitle,
  backgroundImage,
  onTitleChange,
  onSubtitleChange,
  onBackgroundImageChange,
  onNext
}) => {
  const [activeEditor, setActiveEditor] = useState<'title' | 'subtitle' | 'name' | null>(null);
  const [showImageLibrary, setShowImageLibrary] = useState(false);

  const handleImageUploadClick = () => {
    setShowImageLibrary(true);
  };

  const handleImageSelect = (imageUrl: string) => {
    onBackgroundImageChange(imageUrl);
    setShowImageLibrary(false);
  };

  const handleEditorFocus = (editor: 'title' | 'subtitle' | 'name') => {
    setActiveEditor(editor);
  };

  const handleEditorBlur = () => {
    setActiveEditor(null);
  };

  return (
    <div className="front-page">
      {/* Top Header */}
      <div className="page-header">
        <h1 className="pages-title">PAGES</h1>
        <div className="flipbook-title">
          <TextEditor
            value={title}
            onChange={onTitleChange}
            placeholder="Hanash"
            isActive={activeEditor === 'title'}
            onFocus={() => handleEditorFocus('title')}
            onBlur={handleEditorBlur}
            className="title-editor"
          />
        </div>
      </div>

      {/* Main Content Area */}
      <div
        className="main-content"
        onClick={handleImageUploadClick}
        style={{
          backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'none'
        }}
      >
        {/* Decorative Border Frame */}
        <div className="decorative-border-frame"></div>

        {/* Center Content */}
        <div className="center-content">
          <div className="name-section">
            <TextEditor
              value={subtitle || "ZARA IRUM"}
              onChange={onSubtitleChange}
              placeholder="ZARA IRUM"
              isActive={activeEditor === 'subtitle'}
              onFocus={() => handleEditorFocus('subtitle')}
              onBlur={handleEditorBlur}
              className="main-name-editor"
            />
          </div>
          <div className="profession-title">
            ARCHITECT
          </div>
        </div>

        {/* Upload Hint */}
        {!backgroundImage && (
          <div className="upload-hint">
            <span>Tip: A single click on an image will open your Image editing tools. A double click will open your Image toolbar and Rollover</span>
          </div>
        )}
      </div>

      {/* Navigation Arrow */}
      <div className="navigation-controls">
        <button className="next-page-btn" onClick={onNext}>
          →
        </button>
      </div>
      
      {/* Image Library Modal */}
      <ImageLibrary
        isOpen={showImageLibrary}
        onClose={() => setShowImageLibrary(false)}
        onSelectImage={handleImageSelect}
      />
    </div>
  );
};

export default FrontPage;
