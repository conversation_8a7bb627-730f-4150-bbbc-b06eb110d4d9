/// <reference types="react" />
export declare const seedNextClassnames: (names: string[]) => string[];
export declare const resetStyled: (isServer?: boolean) => (<Target extends import("..").WebTarget>(tag: Target) => import("../constructors/constructWithOptions").Styled<"web", Target, Target extends import("../types").KnownTarget ? import("react").ComponentPropsWithRef<Target> : object, object, object>) & {
    a: import("../constructors/constructWithOptions").Styled<"web", "a", import("react").DetailedHTMLProps<import("react").AnchorHTMLAttributes<HTMLAnchorElement>, HTMLAnchorElement>, object, object>;
    abbr: import("../constructors/constructWithOptions").Styled<"web", "abbr", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    address: import("../constructors/constructWithOptions").Styled<"web", "address", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    area: import("../constructors/constructWithOptions").Styled<"web", "area", import("react").DetailedHTMLProps<import("react").AreaHTMLAttributes<HTMLAreaElement>, HTMLAreaElement>, object, object>;
    article: import("../constructors/constructWithOptions").Styled<"web", "article", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    aside: import("../constructors/constructWithOptions").Styled<"web", "aside", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    audio: import("../constructors/constructWithOptions").Styled<"web", "audio", import("react").DetailedHTMLProps<import("react").AudioHTMLAttributes<HTMLAudioElement>, HTMLAudioElement>, object, object>;
    b: import("../constructors/constructWithOptions").Styled<"web", "b", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    base: import("../constructors/constructWithOptions").Styled<"web", "base", import("react").DetailedHTMLProps<import("react").BaseHTMLAttributes<HTMLBaseElement>, HTMLBaseElement>, object, object>;
    bdi: import("../constructors/constructWithOptions").Styled<"web", "bdi", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    bdo: import("../constructors/constructWithOptions").Styled<"web", "bdo", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    big: import("../constructors/constructWithOptions").Styled<"web", "big", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    blockquote: import("../constructors/constructWithOptions").Styled<"web", "blockquote", import("react").DetailedHTMLProps<import("react").BlockquoteHTMLAttributes<HTMLQuoteElement>, HTMLQuoteElement>, object, object>;
    body: import("../constructors/constructWithOptions").Styled<"web", "body", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLBodyElement>, HTMLBodyElement>, object, object>;
    br: import("../constructors/constructWithOptions").Styled<"web", "br", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLBRElement>, HTMLBRElement>, object, object>;
    button: import("../constructors/constructWithOptions").Styled<"web", "button", import("react").DetailedHTMLProps<import("react").ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>, object, object>;
    canvas: import("../constructors/constructWithOptions").Styled<"web", "canvas", import("react").DetailedHTMLProps<import("react").CanvasHTMLAttributes<HTMLCanvasElement>, HTMLCanvasElement>, object, object>;
    caption: import("../constructors/constructWithOptions").Styled<"web", "caption", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    cite: import("../constructors/constructWithOptions").Styled<"web", "cite", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    code: import("../constructors/constructWithOptions").Styled<"web", "code", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    col: import("../constructors/constructWithOptions").Styled<"web", "col", import("react").DetailedHTMLProps<import("react").ColHTMLAttributes<HTMLTableColElement>, HTMLTableColElement>, object, object>;
    colgroup: import("../constructors/constructWithOptions").Styled<"web", "colgroup", import("react").DetailedHTMLProps<import("react").ColgroupHTMLAttributes<HTMLTableColElement>, HTMLTableColElement>, object, object>;
    data: import("../constructors/constructWithOptions").Styled<"web", "data", import("react").DetailedHTMLProps<import("react").DataHTMLAttributes<HTMLDataElement>, HTMLDataElement>, object, object>;
    datalist: import("../constructors/constructWithOptions").Styled<"web", "datalist", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDataListElement>, HTMLDataListElement>, object, object>;
    dd: import("../constructors/constructWithOptions").Styled<"web", "dd", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    del: import("../constructors/constructWithOptions").Styled<"web", "del", import("react").DetailedHTMLProps<import("react").DelHTMLAttributes<HTMLModElement>, HTMLModElement>, object, object>;
    details: import("../constructors/constructWithOptions").Styled<"web", "details", import("react").DetailedHTMLProps<import("react").DetailsHTMLAttributes<HTMLDetailsElement>, HTMLDetailsElement>, object, object>;
    dfn: import("../constructors/constructWithOptions").Styled<"web", "dfn", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    dialog: import("../constructors/constructWithOptions").Styled<"web", "dialog", import("react").DetailedHTMLProps<import("react").DialogHTMLAttributes<HTMLDialogElement>, HTMLDialogElement>, object, object>;
    div: import("../constructors/constructWithOptions").Styled<"web", "div", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, object, object>;
    dl: import("../constructors/constructWithOptions").Styled<"web", "dl", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDListElement>, HTMLDListElement>, object, object>;
    dt: import("../constructors/constructWithOptions").Styled<"web", "dt", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    em: import("../constructors/constructWithOptions").Styled<"web", "em", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    embed: import("../constructors/constructWithOptions").Styled<"web", "embed", import("react").DetailedHTMLProps<import("react").EmbedHTMLAttributes<HTMLEmbedElement>, HTMLEmbedElement>, object, object>;
    fieldset: import("../constructors/constructWithOptions").Styled<"web", "fieldset", import("react").DetailedHTMLProps<import("react").FieldsetHTMLAttributes<HTMLFieldSetElement>, HTMLFieldSetElement>, object, object>;
    figcaption: import("../constructors/constructWithOptions").Styled<"web", "figcaption", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    figure: import("../constructors/constructWithOptions").Styled<"web", "figure", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    footer: import("../constructors/constructWithOptions").Styled<"web", "footer", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    form: import("../constructors/constructWithOptions").Styled<"web", "form", import("react").DetailedHTMLProps<import("react").FormHTMLAttributes<HTMLFormElement>, HTMLFormElement>, object, object>;
    h1: import("../constructors/constructWithOptions").Styled<"web", "h1", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, object, object>;
    h2: import("../constructors/constructWithOptions").Styled<"web", "h2", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, object, object>;
    h3: import("../constructors/constructWithOptions").Styled<"web", "h3", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, object, object>;
    h4: import("../constructors/constructWithOptions").Styled<"web", "h4", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, object, object>;
    h5: import("../constructors/constructWithOptions").Styled<"web", "h5", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, object, object>;
    h6: import("../constructors/constructWithOptions").Styled<"web", "h6", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, object, object>;
    head: import("../constructors/constructWithOptions").Styled<"web", "head", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadElement>, HTMLHeadElement>, object, object>;
    header: import("../constructors/constructWithOptions").Styled<"web", "header", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    hgroup: import("../constructors/constructWithOptions").Styled<"web", "hgroup", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    hr: import("../constructors/constructWithOptions").Styled<"web", "hr", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHRElement>, HTMLHRElement>, object, object>;
    html: import("../constructors/constructWithOptions").Styled<"web", "html", import("react").DetailedHTMLProps<import("react").HtmlHTMLAttributes<HTMLHtmlElement>, HTMLHtmlElement>, object, object>;
    i: import("../constructors/constructWithOptions").Styled<"web", "i", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    iframe: import("../constructors/constructWithOptions").Styled<"web", "iframe", import("react").DetailedHTMLProps<import("react").IframeHTMLAttributes<HTMLIFrameElement>, HTMLIFrameElement>, object, object>;
    img: import("../constructors/constructWithOptions").Styled<"web", "img", import("react").DetailedHTMLProps<import("react").ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement>, object, object>;
    input: import("../constructors/constructWithOptions").Styled<"web", "input", import("react").DetailedHTMLProps<import("react").InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>, object, object>;
    ins: import("../constructors/constructWithOptions").Styled<"web", "ins", import("react").DetailedHTMLProps<import("react").InsHTMLAttributes<HTMLModElement>, HTMLModElement>, object, object>;
    kbd: import("../constructors/constructWithOptions").Styled<"web", "kbd", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    keygen: import("../constructors/constructWithOptions").Styled<"web", "keygen", import("react").DetailedHTMLProps<import("react").KeygenHTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    label: import("../constructors/constructWithOptions").Styled<"web", "label", import("react").DetailedHTMLProps<import("react").LabelHTMLAttributes<HTMLLabelElement>, HTMLLabelElement>, object, object>;
    legend: import("../constructors/constructWithOptions").Styled<"web", "legend", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLLegendElement>, HTMLLegendElement>, object, object>;
    li: import("../constructors/constructWithOptions").Styled<"web", "li", import("react").DetailedHTMLProps<import("react").LiHTMLAttributes<HTMLLIElement>, HTMLLIElement>, object, object>;
    link: import("../constructors/constructWithOptions").Styled<"web", "link", import("react").DetailedHTMLProps<import("react").LinkHTMLAttributes<HTMLLinkElement>, HTMLLinkElement>, object, object>;
    main: import("../constructors/constructWithOptions").Styled<"web", "main", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    map: import("../constructors/constructWithOptions").Styled<"web", "map", import("react").DetailedHTMLProps<import("react").MapHTMLAttributes<HTMLMapElement>, HTMLMapElement>, object, object>;
    mark: import("../constructors/constructWithOptions").Styled<"web", "mark", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    menu: import("../constructors/constructWithOptions").Styled<"web", "menu", import("react").DetailedHTMLProps<import("react").MenuHTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    menuitem: import("../constructors/constructWithOptions").Styled<"web", "menuitem", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    meta: import("../constructors/constructWithOptions").Styled<"web", "meta", import("react").DetailedHTMLProps<import("react").MetaHTMLAttributes<HTMLMetaElement>, HTMLMetaElement>, object, object>;
    meter: import("../constructors/constructWithOptions").Styled<"web", "meter", import("react").DetailedHTMLProps<import("react").MeterHTMLAttributes<HTMLMeterElement>, HTMLMeterElement>, object, object>;
    nav: import("../constructors/constructWithOptions").Styled<"web", "nav", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    noindex: import("../constructors/constructWithOptions").Styled<"web", "noindex", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    noscript: import("../constructors/constructWithOptions").Styled<"web", "noscript", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    object: import("../constructors/constructWithOptions").Styled<"web", "object", import("react").DetailedHTMLProps<import("react").ObjectHTMLAttributes<HTMLObjectElement>, HTMLObjectElement>, object, object>;
    ol: import("../constructors/constructWithOptions").Styled<"web", "ol", import("react").DetailedHTMLProps<import("react").OlHTMLAttributes<HTMLOListElement>, HTMLOListElement>, object, object>;
    optgroup: import("../constructors/constructWithOptions").Styled<"web", "optgroup", import("react").DetailedHTMLProps<import("react").OptgroupHTMLAttributes<HTMLOptGroupElement>, HTMLOptGroupElement>, object, object>;
    option: import("../constructors/constructWithOptions").Styled<"web", "option", import("react").DetailedHTMLProps<import("react").OptionHTMLAttributes<HTMLOptionElement>, HTMLOptionElement>, object, object>;
    output: import("../constructors/constructWithOptions").Styled<"web", "output", import("react").DetailedHTMLProps<import("react").OutputHTMLAttributes<HTMLOutputElement>, HTMLOutputElement>, object, object>;
    p: import("../constructors/constructWithOptions").Styled<"web", "p", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLParagraphElement>, HTMLParagraphElement>, object, object>;
    param: import("../constructors/constructWithOptions").Styled<"web", "param", import("react").DetailedHTMLProps<import("react").ParamHTMLAttributes<HTMLParamElement>, HTMLParamElement>, object, object>;
    picture: import("../constructors/constructWithOptions").Styled<"web", "picture", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    pre: import("../constructors/constructWithOptions").Styled<"web", "pre", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLPreElement>, HTMLPreElement>, object, object>;
    progress: import("../constructors/constructWithOptions").Styled<"web", "progress", import("react").DetailedHTMLProps<import("react").ProgressHTMLAttributes<HTMLProgressElement>, HTMLProgressElement>, object, object>;
    q: import("../constructors/constructWithOptions").Styled<"web", "q", import("react").DetailedHTMLProps<import("react").QuoteHTMLAttributes<HTMLQuoteElement>, HTMLQuoteElement>, object, object>;
    rp: import("../constructors/constructWithOptions").Styled<"web", "rp", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    rt: import("../constructors/constructWithOptions").Styled<"web", "rt", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    ruby: import("../constructors/constructWithOptions").Styled<"web", "ruby", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    s: import("../constructors/constructWithOptions").Styled<"web", "s", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    samp: import("../constructors/constructWithOptions").Styled<"web", "samp", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    slot: import("../constructors/constructWithOptions").Styled<"web", "slot", import("react").DetailedHTMLProps<import("react").SlotHTMLAttributes<HTMLSlotElement>, HTMLSlotElement>, object, object>;
    script: import("../constructors/constructWithOptions").Styled<"web", "script", import("react").DetailedHTMLProps<import("react").ScriptHTMLAttributes<HTMLScriptElement>, HTMLScriptElement>, object, object>;
    section: import("../constructors/constructWithOptions").Styled<"web", "section", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    select: import("../constructors/constructWithOptions").Styled<"web", "select", import("react").DetailedHTMLProps<import("react").SelectHTMLAttributes<HTMLSelectElement>, HTMLSelectElement>, object, object>;
    small: import("../constructors/constructWithOptions").Styled<"web", "small", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    source: import("../constructors/constructWithOptions").Styled<"web", "source", import("react").DetailedHTMLProps<import("react").SourceHTMLAttributes<HTMLSourceElement>, HTMLSourceElement>, object, object>;
    span: import("../constructors/constructWithOptions").Styled<"web", "span", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, object, object>;
    strong: import("../constructors/constructWithOptions").Styled<"web", "strong", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    style: import("../constructors/constructWithOptions").Styled<"web", "style", import("react").DetailedHTMLProps<import("react").StyleHTMLAttributes<HTMLStyleElement>, HTMLStyleElement>, object, object>;
    sub: import("../constructors/constructWithOptions").Styled<"web", "sub", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    summary: import("../constructors/constructWithOptions").Styled<"web", "summary", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    sup: import("../constructors/constructWithOptions").Styled<"web", "sup", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    table: import("../constructors/constructWithOptions").Styled<"web", "table", import("react").DetailedHTMLProps<import("react").TableHTMLAttributes<HTMLTableElement>, HTMLTableElement>, object, object>;
    template: import("../constructors/constructWithOptions").Styled<"web", "template", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTemplateElement>, HTMLTemplateElement>, object, object>;
    tbody: import("../constructors/constructWithOptions").Styled<"web", "tbody", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTableSectionElement>, HTMLTableSectionElement>, object, object>;
    td: import("../constructors/constructWithOptions").Styled<"web", "td", import("react").DetailedHTMLProps<import("react").TdHTMLAttributes<HTMLTableDataCellElement>, HTMLTableDataCellElement>, object, object>;
    textarea: import("../constructors/constructWithOptions").Styled<"web", "textarea", import("react").DetailedHTMLProps<import("react").TextareaHTMLAttributes<HTMLTextAreaElement>, HTMLTextAreaElement>, object, object>;
    tfoot: import("../constructors/constructWithOptions").Styled<"web", "tfoot", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTableSectionElement>, HTMLTableSectionElement>, object, object>;
    th: import("../constructors/constructWithOptions").Styled<"web", "th", import("react").DetailedHTMLProps<import("react").ThHTMLAttributes<HTMLTableHeaderCellElement>, HTMLTableHeaderCellElement>, object, object>;
    thead: import("../constructors/constructWithOptions").Styled<"web", "thead", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTableSectionElement>, HTMLTableSectionElement>, object, object>;
    time: import("../constructors/constructWithOptions").Styled<"web", "time", import("react").DetailedHTMLProps<import("react").TimeHTMLAttributes<HTMLTimeElement>, HTMLTimeElement>, object, object>;
    title: import("../constructors/constructWithOptions").Styled<"web", "title", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTitleElement>, HTMLTitleElement>, object, object>;
    tr: import("../constructors/constructWithOptions").Styled<"web", "tr", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTableRowElement>, HTMLTableRowElement>, object, object>;
    track: import("../constructors/constructWithOptions").Styled<"web", "track", import("react").DetailedHTMLProps<import("react").TrackHTMLAttributes<HTMLTrackElement>, HTMLTrackElement>, object, object>;
    u: import("../constructors/constructWithOptions").Styled<"web", "u", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    ul: import("../constructors/constructWithOptions").Styled<"web", "ul", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLUListElement>, HTMLUListElement>, object, object>;
    var: import("../constructors/constructWithOptions").Styled<"web", "var", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    video: import("../constructors/constructWithOptions").Styled<"web", "video", import("react").DetailedHTMLProps<import("react").VideoHTMLAttributes<HTMLVideoElement>, HTMLVideoElement>, object, object>;
    wbr: import("../constructors/constructWithOptions").Styled<"web", "wbr", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    webview: import("../constructors/constructWithOptions").Styled<"web", "webview", import("react").DetailedHTMLProps<import("react").WebViewHTMLAttributes<HTMLWebViewElement>, HTMLWebViewElement>, object, object>;
    svg: import("../constructors/constructWithOptions").Styled<"web", "svg", import("react").SVGProps<SVGSVGElement>, object, object>;
    animate: import("../constructors/constructWithOptions").Styled<"web", "animate", import("react").SVGProps<SVGElement>, object, object>;
    animateMotion: import("../constructors/constructWithOptions").Styled<"web", "animateMotion", import("react").SVGProps<SVGElement>, object, object>;
    animateTransform: import("../constructors/constructWithOptions").Styled<"web", "animateTransform", import("react").SVGProps<SVGElement>, object, object>;
    circle: import("../constructors/constructWithOptions").Styled<"web", "circle", import("react").SVGProps<SVGCircleElement>, object, object>;
    clipPath: import("../constructors/constructWithOptions").Styled<"web", "clipPath", import("react").SVGProps<SVGClipPathElement>, object, object>;
    defs: import("../constructors/constructWithOptions").Styled<"web", "defs", import("react").SVGProps<SVGDefsElement>, object, object>;
    desc: import("../constructors/constructWithOptions").Styled<"web", "desc", import("react").SVGProps<SVGDescElement>, object, object>;
    ellipse: import("../constructors/constructWithOptions").Styled<"web", "ellipse", import("react").SVGProps<SVGEllipseElement>, object, object>;
    feBlend: import("../constructors/constructWithOptions").Styled<"web", "feBlend", import("react").SVGProps<SVGFEBlendElement>, object, object>;
    feColorMatrix: import("../constructors/constructWithOptions").Styled<"web", "feColorMatrix", import("react").SVGProps<SVGFEColorMatrixElement>, object, object>;
    feComponentTransfer: import("../constructors/constructWithOptions").Styled<"web", "feComponentTransfer", import("react").SVGProps<SVGFEComponentTransferElement>, object, object>;
    feComposite: import("../constructors/constructWithOptions").Styled<"web", "feComposite", import("react").SVGProps<SVGFECompositeElement>, object, object>;
    feConvolveMatrix: import("../constructors/constructWithOptions").Styled<"web", "feConvolveMatrix", import("react").SVGProps<SVGFEConvolveMatrixElement>, object, object>;
    feDiffuseLighting: import("../constructors/constructWithOptions").Styled<"web", "feDiffuseLighting", import("react").SVGProps<SVGFEDiffuseLightingElement>, object, object>;
    feDisplacementMap: import("../constructors/constructWithOptions").Styled<"web", "feDisplacementMap", import("react").SVGProps<SVGFEDisplacementMapElement>, object, object>;
    feDistantLight: import("../constructors/constructWithOptions").Styled<"web", "feDistantLight", import("react").SVGProps<SVGFEDistantLightElement>, object, object>;
    feDropShadow: import("../constructors/constructWithOptions").Styled<"web", "feDropShadow", import("react").SVGProps<SVGFEDropShadowElement>, object, object>;
    feFlood: import("../constructors/constructWithOptions").Styled<"web", "feFlood", import("react").SVGProps<SVGFEFloodElement>, object, object>;
    feFuncA: import("../constructors/constructWithOptions").Styled<"web", "feFuncA", import("react").SVGProps<SVGFEFuncAElement>, object, object>;
    feFuncB: import("../constructors/constructWithOptions").Styled<"web", "feFuncB", import("react").SVGProps<SVGFEFuncBElement>, object, object>;
    feFuncG: import("../constructors/constructWithOptions").Styled<"web", "feFuncG", import("react").SVGProps<SVGFEFuncGElement>, object, object>;
    feFuncR: import("../constructors/constructWithOptions").Styled<"web", "feFuncR", import("react").SVGProps<SVGFEFuncRElement>, object, object>;
    feGaussianBlur: import("../constructors/constructWithOptions").Styled<"web", "feGaussianBlur", import("react").SVGProps<SVGFEGaussianBlurElement>, object, object>;
    feImage: import("../constructors/constructWithOptions").Styled<"web", "feImage", import("react").SVGProps<SVGFEImageElement>, object, object>;
    feMerge: import("../constructors/constructWithOptions").Styled<"web", "feMerge", import("react").SVGProps<SVGFEMergeElement>, object, object>;
    feMergeNode: import("../constructors/constructWithOptions").Styled<"web", "feMergeNode", import("react").SVGProps<SVGFEMergeNodeElement>, object, object>;
    feMorphology: import("../constructors/constructWithOptions").Styled<"web", "feMorphology", import("react").SVGProps<SVGFEMorphologyElement>, object, object>;
    feOffset: import("../constructors/constructWithOptions").Styled<"web", "feOffset", import("react").SVGProps<SVGFEOffsetElement>, object, object>;
    fePointLight: import("../constructors/constructWithOptions").Styled<"web", "fePointLight", import("react").SVGProps<SVGFEPointLightElement>, object, object>;
    feSpecularLighting: import("../constructors/constructWithOptions").Styled<"web", "feSpecularLighting", import("react").SVGProps<SVGFESpecularLightingElement>, object, object>;
    feSpotLight: import("../constructors/constructWithOptions").Styled<"web", "feSpotLight", import("react").SVGProps<SVGFESpotLightElement>, object, object>;
    feTile: import("../constructors/constructWithOptions").Styled<"web", "feTile", import("react").SVGProps<SVGFETileElement>, object, object>;
    feTurbulence: import("../constructors/constructWithOptions").Styled<"web", "feTurbulence", import("react").SVGProps<SVGFETurbulenceElement>, object, object>;
    filter: import("../constructors/constructWithOptions").Styled<"web", "filter", import("react").SVGProps<SVGFilterElement>, object, object>;
    foreignObject: import("../constructors/constructWithOptions").Styled<"web", "foreignObject", import("react").SVGProps<SVGForeignObjectElement>, object, object>;
    g: import("../constructors/constructWithOptions").Styled<"web", "g", import("react").SVGProps<SVGGElement>, object, object>;
    image: import("../constructors/constructWithOptions").Styled<"web", "image", import("react").SVGProps<SVGImageElement>, object, object>;
    line: import("../constructors/constructWithOptions").Styled<"web", "line", import("react").SVGProps<SVGLineElement>, object, object>;
    linearGradient: import("../constructors/constructWithOptions").Styled<"web", "linearGradient", import("react").SVGProps<SVGLinearGradientElement>, object, object>;
    marker: import("../constructors/constructWithOptions").Styled<"web", "marker", import("react").SVGProps<SVGMarkerElement>, object, object>;
    mask: import("../constructors/constructWithOptions").Styled<"web", "mask", import("react").SVGProps<SVGMaskElement>, object, object>;
    metadata: import("../constructors/constructWithOptions").Styled<"web", "metadata", import("react").SVGProps<SVGMetadataElement>, object, object>;
    mpath: import("../constructors/constructWithOptions").Styled<"web", "mpath", import("react").SVGProps<SVGElement>, object, object>;
    path: import("../constructors/constructWithOptions").Styled<"web", "path", import("react").SVGProps<SVGPathElement>, object, object>;
    pattern: import("../constructors/constructWithOptions").Styled<"web", "pattern", import("react").SVGProps<SVGPatternElement>, object, object>;
    polygon: import("../constructors/constructWithOptions").Styled<"web", "polygon", import("react").SVGProps<SVGPolygonElement>, object, object>;
    polyline: import("../constructors/constructWithOptions").Styled<"web", "polyline", import("react").SVGProps<SVGPolylineElement>, object, object>;
    radialGradient: import("../constructors/constructWithOptions").Styled<"web", "radialGradient", import("react").SVGProps<SVGRadialGradientElement>, object, object>;
    rect: import("../constructors/constructWithOptions").Styled<"web", "rect", import("react").SVGProps<SVGRectElement>, object, object>;
    stop: import("../constructors/constructWithOptions").Styled<"web", "stop", import("react").SVGProps<SVGStopElement>, object, object>;
    switch: import("../constructors/constructWithOptions").Styled<"web", "switch", import("react").SVGProps<SVGSwitchElement>, object, object>;
    symbol: import("../constructors/constructWithOptions").Styled<"web", "symbol", import("react").SVGProps<SVGSymbolElement>, object, object>;
    text: import("../constructors/constructWithOptions").Styled<"web", "text", import("react").SVGProps<SVGTextElement>, object, object>;
    textPath: import("../constructors/constructWithOptions").Styled<"web", "textPath", import("react").SVGProps<SVGTextPathElement>, object, object>;
    tspan: import("../constructors/constructWithOptions").Styled<"web", "tspan", import("react").SVGProps<SVGTSpanElement>, object, object>;
    use: import("../constructors/constructWithOptions").Styled<"web", "use", import("react").SVGProps<SVGUseElement>, object, object>;
    view: import("../constructors/constructWithOptions").Styled<"web", "view", import("react").SVGProps<SVGViewElement>, object, object>;
};
export declare const rehydrateTestStyles: () => void;
export declare const stripComments: (str: string) => string;
export declare const stripWhitespace: (str: string) => string;
export declare const getCSS: (scope: Document | HTMLElement) => string;
export declare const expectCSSMatches: (_expectation: string, opts?: {
    ignoreWhitespace: boolean;
}) => string;
export declare const getRenderedCSS: () => string;
