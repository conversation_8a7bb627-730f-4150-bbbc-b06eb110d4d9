{"version": "2.1.4", "name": "babel-plugin-styled-components", "description": "Improve the debugging experience and add server-side rendering support to styled-components", "repository": "styled-components/babel-plugin-styled-components", "homepage": "https://styled-components.com/docs/tooling#babel-plugin", "contributors": ["<PERSON> <vladimir.da<PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "main": "lib/index.js", "files": ["lib", "LICENSE.md", "README.md"], "license": "MIT", "devDependencies": {"@babel/cli": "^7.22.5", "@babel/core": "^7.22.5", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-transform-modules-commonjs": "^7.22.5", "@babel/preset-env": "^7.22.5", "babel-test": "^0.2.4", "jest": "^29.5.0", "jest-file-snapshot": "^0.5.0", "prettier": "^2.8.8", "rimraf": "^5.0.1", "styled-components": "^6.0.0-rc.6"}, "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-module-imports": "^7.22.5", "@babel/plugin-syntax-jsx": "^7.22.5", "lodash": "^4.17.21", "picomatch": "^2.3.1"}, "peerDependencies": {"styled-components": ">= 2"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> lib", "style": "prettier --write src/**/*.js", "build": "babel src -d lib", "test": "jest", "test:watch": "yarn test -- --watch", "prepublish": "yarn clean && yarn build"}, "keywords": ["styled-components", "css-in-js", "babel-plugin", "server-side rendering", "ssr", "displayName"], "jest": {"testEnvironment": "node", "snapshotSerializers": ["<rootDir>/test/whitespaceTrimmingSerializer.js"], "watchPathIgnorePatterns": ["fixtures\\/[^/]+\\/(output|error)\\.js"]}, "packageManager": "yarn@3.5.0"}