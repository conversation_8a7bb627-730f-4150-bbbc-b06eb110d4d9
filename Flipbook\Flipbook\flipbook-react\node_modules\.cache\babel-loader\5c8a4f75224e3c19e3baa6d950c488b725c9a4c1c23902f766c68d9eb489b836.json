{"ast": null, "code": "import React,{useState,useEffect}from'react';import images from'../../assets/images';import'./FlipbookPreview.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FlipbookPreview=_ref=>{let{portfolioId,pageCount,isInspiration,onClose,onCopyToAccount}=_ref;const[currentPage,setCurrentPage]=useState(1);const[isPlaying,setIsPlaying]=useState(false);const[showCopyModal,setShowCopyModal]=useState(false);const[flipbookTitle,setFlipbookTitle]=useState('');const[pageContent,setPageContent]=useState('');const[loading,setLoading]=useState(true);useEffect(()=>{console.log(\"Loading flipbook: \".concat(portfolioId,\" with \").concat(pageCount,\" pages\"));// Set appropriate title based on the flipbook\nif(portfolioId==='2154'){setFlipbookTitle('Original Flipbook');}else{setFlipbookTitle(\"Flipbook \".concat(portfolioId));}},[portfolioId,pageCount]);// Load page content from the flipbook HTML\nconst loadPageContent=async page=>{setLoading(true);try{// Always use mock content for now since we have the authentic HTML structure\n// In production, this would attempt to load from the real API first\nif(portfolioId==='2154'){setPageContent(getMockOriginalFlipbookContent(page));}else{setPageContent(\"<div class=\\\"page-content\\\"><h2>Page \".concat(page,\"</h2><p>Content for page \").concat(page,\" of \").concat(flipbookTitle,\"</p></div>\"));}}catch(error){console.error('Error loading page content:',error);// Fallback to mock content\nif(portfolioId==='2154'){setPageContent(getMockOriginalFlipbookContent(page));}else{setPageContent(\"<div class=\\\"page-content\\\"><h2>Page \".concat(page,\"</h2><p>Content for page \").concat(page,\" of \").concat(flipbookTitle,\"</p></div>\"));}}finally{setLoading(false);}};// Mock content for Original Flipbook based on the provided HTML structure\nconst getMockOriginalFlipbookContent=page=>{if(page===1){// Cover page with LINK SALAS ARCHITECT\nreturn\"\\n        <div class=\\\"book-content\\\">\\n          <div class=\\\"div-flip-main-left\\\">\\n            <style>\\n              .flip_wrapper {\\n                width: min(90vw, 1200px);\\n                height: min(80vh, 900px);\\n                margin: 0 auto 40px;\\n                border: 1px solid #000000;\\n                position: relative;\\n                background: url('/Flipbooks/EndPapers/90-min.jpg') 0% 0% / 200%;\\n              }\\n              .architect-content {\\n                display: flex;\\n                flex-direction: column;\\n                align-items: center;\\n                justify-content: center;\\n                height: 100%;\\n                text-align: center;\\n                padding: 40px;\\n                background-color: rgba(210, 196, 177, 0.9);\\n                border: 7px solid rgb(210, 196, 177);\\n              }\\n              .architect-label {\\n                font-family: 'AvenirLTStd-Book', Arial, sans-serif;\\n                font-size: 21pt;\\n                color: #333;\\n                margin-bottom: 10px;\\n                letter-spacing: 2px;\\n              }\\n              .architect-title {\\n                font-family: 'OptimaBold', Arial, sans-serif;\\n                font-size: 28pt;\\n                font-weight: bold;\\n                color: #333;\\n                letter-spacing: 3px;\\n                border-top: 3px solid #000;\\n                padding-top: 10px;\\n                width: 200px;\\n                margin: 0 auto;\\n              }\\n            </style>\\n            <div class=\\\"flip_wrapper\\\">\\n              <div class=\\\"architect-content\\\" style=\\\"background-image: url('\".concat(images.resumeInspiration.portfolio2.page1,\"'); background-size: cover; background-position: center;\\\">\\n                <div class=\\\"architect-label\\\" style=\\\"background: rgba(255,255,255,0.9); padding: 10px 20px; border-radius: 5px;\\\">LINK SALAS</div>\\n                <div class=\\\"architect-title\\\" style=\\\"background: rgba(255,255,255,0.9); padding: 10px 20px; border-radius: 5px; margin-top: 10px;\\\">ARCHITECT</div>\\n              </div>\\n            </div>\\n          </div>\\n          <span class=\\\"page-number\\\">\").concat(page,\"</span>\\n        </div>\\n      \");}else if(page===2){// Resume page with two columns - cleaned up version without broken images\nreturn\"\\n        <div class=\\\"book-content\\\">\\n          <div class=\\\"div-flip-main-left\\\">\\n            <div class=\\\"container divLayout2\\\">\\n              <div class=\\\"common_margin_layout\\\">\\n                <div class=\\\"bb-custom-wrapper\\\">\\n                  <div id=\\\"bb-bookblock\\\" class=\\\"bb-bookblock\\\">\\n                    <div class=\\\"bb-item show_item\\\">\\n                      <div class=\\\"bg_end_paper\\\" style=\\\"background: url('\".concat(images.endPapers.big03,\"') 0% 0% / 100%;\\\">\\n                        <div style=\\\"border: 11px solid rgb(210, 196, 177); background-color: rgb(210, 196, 177); height: 100%;\\\">\\n                          <div class=\\\"divlayout2inner\\\">\\n                            <div class=\\\"custom_side_wrapper\\\" style=\\\"display: flex; height: 100%;\\\">\\n                              <div class=\\\"bb-custom-side frederich_img frederich_img_Left\\\" style=\\\"flex: 1; background: white; margin: clamp(8px, 1.2vw, 18px); padding: clamp(15px, 2.5vw, 35px); box-sizing: border-box; min-width: 0; overflow: visible;\\\">\\n                                <div class=\\\"content_wrap\\\">\\n                                  <div class=\\\"top_heading_wrap divTopHeadingLeft\\\">\\n                                    <div style=\\\"font-size: 23pt; font-family: 'Gill Sans', Arial, sans-serif; font-weight: bold; margin-bottom: 20px;\\\">Link Salas</div>\\n                                  </div>\\n                                  <div class=\\\"content_wrap_in ip_left\\\">\\n                                    <div class=\\\"left_sec\\\">\\n                                      <div style=\\\"margin-bottom: 20px;\\\">\\n                                        <div style=\\\"font-size: 16pt; font-family: 'Avenir', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;\\\">Objective</div>\\n                                        <div style=\\\"font-size: 10pt; font-family: 'Palatino', serif; line-height: 1.4;\\\">To find a position as a junior designer in a residential architectural firm, preferably located in New York City.</div>\\n                                      </div>\\n                                      <div style=\\\"margin-bottom: 20px;\\\">\\n                                        <div style=\\\"font-size: 16pt; font-family: 'Avenir', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;\\\">Personal Statement</div>\\n                                        <div style=\\\"font-size: 10pt; font-family: 'Palatino', serif; line-height: 1.4;\\\">My professors have consistently mentioned my strong spatial aptitude, fine drawing skills and AutoCAD expertise. I have a strong interest in neoclassical design, especially as it applies to modern office structures.</div>\\n                                      </div>\\n                                      <div style=\\\"margin-bottom: 20px;\\\">\\n                                        <div style=\\\"font-size: 16pt; font-family: 'Avenir', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;\\\">Professional Interests</div>\\n                                        <div style=\\\"font-size: 10pt; font-family: 'Palatino', serif; line-height: 1.4;\\\">As a 2016 summer intern at SOM, I have focused on office layouts, beam strength and office desk optimization. I have the privilege of working with John Smith in the 200 Park Avenue renovation. Previously, I was a summer intern with Cesar Pelli, where we focused on enhancements to the San Francisco Billing Tower, particularly the elevator optimization.</div>\\n                                      </div>\\n                                    </div>\\n                                  </div>\\n                                </div>\\n                              </div>\\n                              <div class=\\\"bb-custom-side frederich_img frederich_img1\\\" style=\\\"flex: 1; background: white; margin: clamp(8px, 1.2vw, 18px); padding: clamp(15px, 2.5vw, 35px); box-sizing: border-box; min-width: 0; overflow: visible;\\\">\\n                                <div class=\\\"content_wrap\\\">\\n                                  <div class=\\\"top_heading_wrap divTopHeadingRight\\\">\\n                                    <div style=\\\"font-size: 23pt; font-family: 'Gill Sans', Arial, sans-serif; font-weight: bold; margin-bottom: 20px;\\\">Resume Highlights</div>\\n                                  </div>\\n                                  <div class=\\\"content_wrap_in\\\">\\n                                    <div style=\\\"margin-bottom: 20px;\\\">\\n                                      <div style=\\\"font-size: 16pt; font-family: 'Avenir', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;\\\">Education</div>\\n                                      <div style=\\\"font-size: 10pt; font-family: 'Palatino', serif; line-height: 1.4;\\\"><strong>University of Johannesburg - Jozi, South Africa</strong><br>Diploma in Architectural Technology (2011)</div>\\n                                    </div>\\n                                    <div style=\\\"margin-bottom: 20px;\\\">\\n                                      <div style=\\\"font-size: 16pt; font-family: 'Avenir', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;\\\">Experience</div>\\n                                      <div style=\\\"font-size: 10pt; font-family: 'Palatino', serif; line-height: 1.4;\\\"><strong>Bespoke Careers - London, August 2012 to Present</strong><br>Interiors, Graphics and Visualisations Recruitment Administrator<br>Accurately prepared project consultation document. Revise shop drawings and project submittals for project compliance. Communicate and coordinate with clients, consultants, contractors and teams.</div>\\n                                    </div>\\n                                    <div style=\\\"margin-bottom: 20px;\\\">\\n                                      <div style=\\\"font-size: 16pt; font-family: 'Avenir', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;\\\">Skill Proficiencies</div>\\n                                      <div style=\\\"font-size: 10pt; font-family: 'Palatino', serif; line-height: 1.4;\\\">FOH maintenance<br>Public event and exhibition setup<br>Intern management and scheduling</div>\\n                                    </div>\\n                                  </div>\\n                                </div>\\n                                <div class=\\\"footer_url\\\" style=\\\"position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); text-align: center;\\\">\\n                                  <div style=\\\"font-size: 10pt; color: #333;\\\">flipbook.franklinreport.com/Flipbook/UserPreview/LSalas1</div>\\n                                </div>\\n                              </div>\\n                            </div>\\n                          </div>\\n                        </div>\\n                      </div>\\n                    </div>\\n                  </div>\\n                </div>\\n              </div>\\n            </div>\\n          </div>\\n          <span class=\\\"page-number\\\">\").concat(page,\"</span>\\n        </div>\\n      \");}else if(page===3){// Page 3 - Right side of the resume spread (same content but right-aligned)\nreturn\"\\n        <div class=\\\"book-content\\\">\\n          <div class=\\\"div-flip-main-right\\\">\\n            <style>\\n              .resume-container-right {\\n                width: min(90vw, 1200px);\\n                height: min(80vh, 900px);\\n                margin: 0 auto;\\n                border: 11px solid rgb(210, 196, 177);\\n                background-color: rgb(210, 196, 177);\\n                display: flex;\\n                font-family: Arial, sans-serif;\\n                background: url('/Flipbooks/EndPapers/90-min.jpg') 0% 0% / 100%;\\n              }\\n              .left-column-right, .right-column-right {\\n                flex: 1;\\n                padding: clamp(15px, 2.5vw, 35px);\\n                background: white;\\n                margin: clamp(8px, 1.2vw, 18px);\\n                box-sizing: border-box;\\n                min-width: 0;\\n                overflow: visible;\\n              }\\n              .name-header-right {\\n                font-family: 'GillSansMT-Bold', Arial, sans-serif;\\n                font-size: 23pt;\\n                font-weight: bold;\\n                margin-bottom: 20px;\\n              }\\n              .section-header-right {\\n                font-family: 'AvenirLTStd-Book', Arial, sans-serif;\\n                font-size: 16pt;\\n                font-weight: bold;\\n                border-bottom: 1px solid #000;\\n                padding-bottom: 5px;\\n                margin: 15px 0 10px 0;\\n              }\\n              .section-content-right {\\n                font-family: 'PalatinoLTStd', Arial, sans-serif;\\n                font-size: 10pt;\\n                line-height: 1.4;\\n                margin-bottom: 15px;\\n              }\\n              .footer-url-right {\\n                position: absolute;\\n                bottom: 20px;\\n                left: 50%;\\n                transform: translateX(-50%);\\n                font-size: 10pt;\\n                color: #333;\\n                text-align: center;\\n                width: 100%;\\n              }\\n            </style>\\n            <div class=\\\"resume-container-right\\\">\\n              <div class=\\\"left-column-right\\\">\\n                <div class=\\\"name-header-right\\\">Link Salas</div>\\n                <div class=\\\"section-header-right\\\">Objective</div>\\n                <div class=\\\"section-content-right\\\">To find a position as a junior designer in a residential architectural firm, preferably located in New York City.</div>\\n                <div class=\\\"section-header-right\\\">Personal Statement</div>\\n                <div class=\\\"section-content-right\\\">My professors have consistently mentioned my strong spatial aptitude, fine drawing skills and AutoCAD expertise. I have a strong interest in neoclassical design, especially as it applies to modern office structures.</div>\\n                <div class=\\\"section-header-right\\\">Professional Interests</div>\\n                <div class=\\\"section-content-right\\\">As a 2016 summer intern at SOM, I have focused on office layouts, beam strength and office desk optimization. I have the privilege of working with John Smith in the 200 Park Avenue renovation. Previously, I was a summer intern with Cesar Pelli, where we focused on enhancements to the San Francisco Billing Tower, particularly the elevator optimization.</div>\\n              </div>\\n              <div class=\\\"right-column-right\\\">\\n                <div class=\\\"name-header-right\\\">Resume Highlights</div>\\n                <div class=\\\"section-header-right\\\">Education</div>\\n                <div class=\\\"section-content-right\\\"><strong>University of Johannesburg - Jozi, South Africa</strong><br>Diploma in Architectural Technology (2011)</div>\\n                <div class=\\\"section-header-right\\\">Experience</div>\\n                <div class=\\\"section-content-right\\\"><strong>Bespoke Careers - London, August 2012 to Present</strong><br>Interiors, Graphics and Visualisations Recruitment Administrator<br>Accurately prepared project consultation document. Revise shop drawings and project submittals for project compliance. Communicate and coordinate with clients, consultants, contractors and teams.<br><br><strong>The Architecture Foundation - London, February 2008 - January 2009</strong><br>Office administration</div>\\n                <div class=\\\"section-header-right\\\">Skill Proficiencies</div>\\n                <div class=\\\"section-content-right\\\">FOH maintenance<br>Public event and exhibition setup<br>Intern management and scheduling</div>\\n              </div>\\n            </div>\\n            <div class=\\\"footer-url-right\\\">flipbook.franklinreport.com/Flipbook/UserPreview/LSalas1</div>\\n          </div>\\n          <span class=\\\"page-number\\\">\".concat(page,\"</span>\\n        </div>\\n      \");}else if(page===4){// Page 4 - Union Station project page\nreturn\"\\n        <div class=\\\"book-content\\\">\\n          <div class=\\\"div-flip-main-left\\\">\\n            <style>\\n              .project-container {\\n                width: min(90vw, 1200px);\\n                height: min(80vh, 900px);\\n                margin: 0 auto;\\n                display: flex;\\n                font-family: Arial, sans-serif;\\n                background: white;\\n              }\\n              .project-image {\\n                width: 50%;\\n                display: flex;\\n                align-items: center;\\n                justify-content: center;\\n                background: #f5f5f5;\\n                border: 1px solid #ddd;\\n              }\\n              .project-image img {\\n                width: 70%;\\n                height: auto;\\n                opacity: 0.7;\\n              }\\n              .project-content {\\n                width: 50%;\\n                padding: 49px;\\n                display: flex;\\n                flex-direction: column;\\n                justify-content: flex-start;\\n              }\\n              .project-title {\\n                font-family: 'AvenirLTStd-Book', Arial, sans-serif;\\n                font-size: 22pt;\\n                color: #333;\\n                margin-bottom: 10px;\\n                font-weight: normal;\\n              }\\n              .project-byline {\\n                font-family: 'MillerText-Roman', serif;\\n                font-size: 9pt;\\n                color: #666;\\n                margin-bottom: 20px;\\n                line-height: 1.2;\\n              }\\n              .project-body {\\n                font-family: 'PalatinoLTStd', serif;\\n                font-size: 8.25pt;\\n                line-height: 1.6;\\n                color: #333;\\n                text-align: justify;\\n              }\\n            </style>\\n            <div class=\\\"project-container\\\">\\n              <div class=\\\"project-image\\\">\\n                <img src=\\\"\".concat(images.architecture.grimshawBeyerBlinder,\"\\\" alt=\\\"Union Station\\\" onError=\\\"this.src='\").concat(images.flipbook.blankImg,\"'\\\" />\\n              </div>\\n              <div class=\\\"project-content\\\">\\n                <div class=\\\"project-title\\\">UNION STATION</div>\\n                <div class=\\\"project-byline\\\">In Washington, DC</div>\\n                <div class=\\\"project-body\\\">\\n                  In 2015, Beyer Blinder &amp;Belle were selected to oversee the redesign of Washington DC's historic Union Station\\u2013the remit was to take the iconic Beaux Arts transportation hub into its second century. Central to the challenge of the project was stabilizing its aging infrastructure and updating its internal systems without overly impacting the facade. The scope of work was significant, and the process involved collaboration with AmTrak, the city government, the federal government, and a small army of engineers, electricians, consultants, and other construction specialists.\\n                </div>\\n              </div>\\n            </div>\\n          </div>\\n          <span class=\\\"page-number\\\">\").concat(page,\"</span>\\n        </div>\\n      \");}else if(page===5){// Page 5 - Union Station project right side of spread\nreturn\"\\n        <div class=\\\"book-content\\\">\\n          <div class=\\\"div-flip-main-right\\\">\\n            <style>\\n              .project-container-right {\\n                width: min(90vw, 1200px);\\n                height: min(80vh, 900px);\\n                margin: 0 auto;\\n                display: flex;\\n                font-family: Arial, sans-serif;\\n                background: white;\\n              }\\n              .project-image-right {\\n                width: 50%;\\n                display: flex;\\n                align-items: center;\\n                justify-content: center;\\n                background: #f5f5f5;\\n                border: 1px solid #ddd;\\n              }\\n              .project-image-right img {\\n                width: 70%;\\n                height: auto;\\n                opacity: 0.7;\\n              }\\n              .project-content-right {\\n                width: 50%;\\n                padding: 49px;\\n                display: flex;\\n                flex-direction: column;\\n                justify-content: flex-start;\\n              }\\n              .project-title-right {\\n                font-family: 'AvenirLTStd-Book', Arial, sans-serif;\\n                font-size: 22pt;\\n                color: #333;\\n                margin-bottom: 10px;\\n                font-weight: normal;\\n              }\\n              .project-byline-right {\\n                font-family: 'MillerText-Roman', serif;\\n                font-size: 9pt;\\n                color: #666;\\n                margin-bottom: 20px;\\n                line-height: 1.2;\\n              }\\n              .project-body-right {\\n                font-family: 'PalatinoLTStd', serif;\\n                font-size: 8.25pt;\\n                line-height: 1.6;\\n                color: #333;\\n                text-align: justify;\\n              }\\n            </style>\\n            <div class=\\\"project-container-right\\\">\\n              <div class=\\\"project-image-right\\\">\\n                <img src=\\\"\".concat(images.architecture.mcMillen,\"\\\" alt=\\\"Union Station Interior\\\" onError=\\\"this.src='\").concat(images.flipbook.blankImg,\"'\\\" />\\n              </div>\\n              <div class=\\\"project-content-right\\\">\\n                <div class=\\\"project-title-right\\\">UNION STATION</div>\\n                <div class=\\\"project-byline-right\\\">In Washington, DC</div>\\n                <div class=\\\"project-body-right\\\">\\n                  In 2015, Beyer Blinder &amp;Belle were selected to oversee the redesign of Washington DC's historic Union Station\\u2013the remit was to take the iconic Beaux Arts transportation hub into its second century. Central to the challenge of the project was stabilizing its aging infrastructure and updating its internal systems without overly impacting the facade. The scope of work was significant, and the process involved collaboration with AmTrak, the city government, the federal government, and a small army of engineers, electricians, consultants, and other construction specialists.\\n                </div>\\n              </div>\\n            </div>\\n          </div>\\n          <span class=\\\"page-number\\\">\").concat(page,\"</span>\\n        </div>\\n      \");}else if(page===6){// Page 6 - Midcentury View project\nreturn\"\\n        <div class=\\\"book-content\\\">\\n          <div class=\\\"div-flip-main-left\\\">\\n            <style>\\n              .midcentury-container {\\n                width: min(90vw, 1200px);\\n                height: min(80vh, 900px);\\n                margin: 0 auto;\\n                display: flex;\\n                font-family: Arial, sans-serif;\\n                background: white;\\n              }\\n              .midcentury-image {\\n                width: 50%;\\n                display: flex;\\n                align-items: center;\\n                justify-content: center;\\n                background: #f5f5f5;\\n                border: 1px solid #ddd;\\n              }\\n              .midcentury-image img {\\n                width: 90%;\\n                height: 90%;\\n                object-fit: cover;\\n              }\\n              .midcentury-content {\\n                width: 50%;\\n                padding: 40px;\\n                display: flex;\\n                flex-direction: column;\\n                justify-content: flex-start;\\n              }\\n              .midcentury-title {\\n                font-family: 'AvenirLTStd-Book', Arial, sans-serif;\\n                font-size: 22pt;\\n                color: #333;\\n                margin-bottom: 10px;\\n                font-weight: normal;\\n              }\\n              .midcentury-byline {\\n                font-family: 'MillerText-Roman', serif;\\n                font-size: 9pt;\\n                color: #666;\\n                margin-bottom: 20px;\\n                line-height: 1.2;\\n              }\\n              .midcentury-body {\\n                font-family: 'ACaslonPro-Regular', serif;\\n                font-size: 12pt;\\n                line-height: 1.6;\\n                color: #333;\\n                text-align: justify;\\n                margin-bottom: 30px;\\n              }\\n              .midcentury-images {\\n                display: flex;\\n                flex-direction: column;\\n                gap: 15px;\\n              }\\n              .midcentury-small-image {\\n                display: flex;\\n                align-items: center;\\n                gap: 15px;\\n              }\\n              .midcentury-small-image img {\\n                width: 80px;\\n                height: 60px;\\n                object-fit: cover;\\n                border: 1px solid #ddd;\\n              }\\n              .midcentury-caption {\\n                font-family: 'MillerText-Roman', serif;\\n                font-size: 9pt;\\n                color: #666;\\n              }\\n            </style>\\n            <div class=\\\"midcentury-container\\\">\\n              <div class=\\\"midcentury-image\\\">\\n                <img src=\\\"\".concat(images.architecture.quincy,\"\\\" alt=\\\"Midcentury View\\\" onError={(e) => {e.currentTarget.src = '\").concat(images.flipbook.blankImg,\"'}} />\\n              </div>\\n              <div class=\\\"midcentury-content\\\">\\n                <div class=\\\"midcentury-title\\\">MIDCENTURY VIEW</div>\\n                <div class=\\\"midcentury-byline\\\">For the Harvey Family</div>\\n                <div class=\\\"midcentury-body\\\">\\n                  This aerie retreat exemplifies midcentury California minimalism with a few rustic touches. As a junior architect on the project, I coordinated the structural research team to ensure adherence to local code.\\n                  <br><br>\\n                  Adobe Caslon\\n                </div>\\n                <div class=\\\"midcentury-images\\\">\\n                  <div class=\\\"midcentury-small-image\\\">\\n                    <img src=\\\"\").concat(images.architecture.overlook,\"\\\" alt=\\\"Master Bedroom\\\" onError={(e) => {e.currentTarget.src = '\").concat(images.flipbook.blankImg,\"'}} />\\n                    <div class=\\\"midcentury-caption\\\">Master Bedroom</div>\\n                  </div>\\n                  <div class=\\\"midcentury-small-image\\\">\\n                    <img src=\\\"\").concat(images.architecture.kellerCenter,\"\\\" alt=\\\"The Sunset\\\" onError={(e) => {e.currentTarget.src = '\").concat(images.flipbook.blankImg,\"'}} />\\n                    <div class=\\\"midcentury-caption\\\">The Sunset</div>\\n                  </div>\\n                </div>\\n              </div>\\n            </div>\\n          </div>\\n          <span class=\\\"page-number\\\">\").concat(page,\"</span>\\n        </div>\\n      \");}else{// Sample portfolio pages with architectural content\nconst samplePages=getSampleArchitecturalPage(page);return samplePages;}};// Get project image by index\nconst getProjectImage=(projectIndex,imageIndex)=>{const architectureImageList=[images.architecture.grimshawBeyerBlinder,images.architecture.peterAaron,images.architecture.mcMillen,images.architecture.quincy,images.architecture.overlook,images.architecture.kellerCenter,images.architecture.shingleHouse,images.architecture.fredArchitect];const baseIndex=(projectIndex*3+imageIndex)%architectureImageList.length;return architectureImageList[baseIndex];};// Generate sample architectural portfolio pages\nconst getSampleArchitecturalPage=page=>{const projects=[{title:\"DOWNTOWN LOFT RENOVATION\",location:\"In Manhattan, NYC\",description:\"A complete transformation of a 1920s industrial space into a modern residential loft. The project focused on maintaining the authentic brick and steel structure while introducing contemporary living elements. Custom millwork and strategic lighting highlight the dramatic ceiling heights.\",images:[\"loft-main.jpg\",\"loft-kitchen.jpg\",\"loft-living.jpg\"]},{title:\"COASTAL RESIDENCE\",location:\"In Malibu, California\",description:\"This oceanfront home was designed to maximize the connection between interior and exterior spaces. Floor-to-ceiling glass walls provide unobstructed ocean views while weathered cedar siding helps the structure blend with the natural landscape.\",images:[\"coastal-exterior.jpg\",\"coastal-deck.jpg\",\"coastal-interior.jpg\"]},{title:\"CORPORATE HEADQUARTERS\",location:\"In Seattle, Washington\",description:\"A 12-story mixed-use development combining office space with retail at street level. The design emphasizes natural light and flexible workspace configurations. Sustainable materials and energy-efficient systems earned the project LEED Gold certification.\",images:[\"corp-exterior.jpg\",\"corp-lobby.jpg\",\"corp-office.jpg\"]},{title:\"HISTORIC CHURCH RESTORATION\",location:\"In Charleston, South Carolina\",description:\"Careful restoration of an 1850s Gothic Revival church damaged by hurricane flooding. The project required specialized conservation techniques to preserve original stonework while updating mechanical systems and accessibility features.\",images:[\"church-exterior.jpg\",\"church-interior.jpg\",\"church-detail.jpg\"]},{title:\"MOUNTAIN CABIN RETREAT\",location:\"In Aspen, Colorado\",description:\"A contemporary interpretation of traditional alpine architecture. Local stone and reclaimed timber create harmony with the mountain environment while large windows frame dramatic valley views. Radiant floor heating and passive solar design ensure year-round comfort.\",images:[\"cabin-exterior.jpg\",\"cabin-interior.jpg\",\"cabin-view.jpg\"]},{title:\"URBAN MIXED-USE DEVELOPMENT\",location:\"In Portland, Oregon\",description:\"A transit-oriented development combining affordable housing, retail, and community spaces. The design promotes walkability and social interaction through shared courtyards and ground-floor activation. Green roofs and rainwater collection systems address local environmental priorities.\",images:[\"urban-street.jpg\",\"urban-courtyard.jpg\",\"urban-rooftop.jpg\"]},{title:\"UNIVERSITY LIBRARY EXPANSION\",location:\"In Berkeley, California\",description:\"A sensitive addition to a 1960s brutalist library building. The new wing provides collaborative study spaces and technology centers while respecting the existing concrete structure. Natural ventilation and daylighting reduce energy consumption.\",images:[\"library-exterior.jpg\",\"library-reading.jpg\",\"library-study.jpg\"]},{title:\"WATERFRONT PARK PAVILION\",location:\"In San Diego, California\",description:\"A series of interconnected pavilions providing shelter and amenities for a popular bayside park. The structures use weathering steel and cast concrete to withstand marine conditions while creating dramatic shadows and framing water views.\",images:[\"pavilion-water.jpg\",\"pavilion-structure.jpg\",\"pavilion-sunset.jpg\"]}];const projectIndex=Math.floor((page-7)/2)%projects.length;const project=projects[projectIndex];const isRightPage=page%2===1;const layoutClass=isRightPage?\"div-flip-main-right\":\"div-flip-main-left\";const marginStyle=\"\";return\"\\n      <div class=\\\"book-content\\\">\\n        <div class=\\\"\".concat(layoutClass,\"\\\" style=\\\"\").concat(marginStyle,\"\\\">\\n          <div class=\\\"container\\\">\\n            <div class=\\\"common_margin_layout\\\">\\n              <div class=\\\"bb-custom-wrapper\\\">\\n                <div id=\\\"bb-bookblock\\\" class=\\\"bb-bookblock\\\">\\n                  <div class=\\\"bb-item show_item\\\">\\n                    <div style=\\\"display: flex; height: 576px; background: white;\\\">\\n                      <div style=\\\"width: 50%; display: flex; align-items: center; justify-content: center; background: #f8f8f8; border: 1px solid #ddd;\\\">\\n                        <img src=\\\"\").concat(getProjectImage(projectIndex,0),\"\\\" alt=\\\"\").concat(project.title,\"\\\" style=\\\"width: 90%; height: 90%; object-fit: cover;\\\" onError=\\\"this.style.display='none'\\\" />\\n                      </div>\\n                      <div style=\\\"width: 50%; padding: 49px; display: flex; flex-direction: column; justify-content: flex-start;\\\">\\n                        <div style=\\\"font-family: 'Avenir', Arial, sans-serif; font-size: 22pt; color: #333; margin-bottom: 10px; font-weight: normal;\\\">\").concat(project.title,\"</div>\\n                        <div style=\\\"font-family: 'Times New Roman', serif; font-size: 9pt; color: #666; margin-bottom: 20px; line-height: 1.2;\\\">\").concat(project.location,\"</div>\\n                        <div style=\\\"font-family: 'Palatino', serif; font-size: 8.25pt; line-height: 1.6; color: #333; text-align: justify; margin-bottom: 30px;\\\">\\n                          \").concat(project.description,\"\\n                        </div>\\n                        <div style=\\\"display: flex; flex-direction: column; gap: 15px;\\\">\\n                          <div style=\\\"display: flex; align-items: center; gap: 15px;\\\">\\n                            <img src=\\\"\").concat(getProjectImage(projectIndex,1),\"\\\" alt=\\\"\").concat(project.images[1],\"\\\" style=\\\"width: 60px; height: 45px; object-fit: cover; border: 1px solid #ccc; border-radius: 2px;\\\" onError=\\\"this.style.display='none'\\\" />\\n                            <div style=\\\"font-family: 'Times New Roman', serif; font-size: 9pt; color: #666;\\\">\").concat(project.images[1],\"</div>\\n                          </div>\\n                          <div style=\\\"display: flex; align-items: center; gap: 15px;\\\">\\n                            <img src=\\\"\").concat(getProjectImage(projectIndex,2),\"\\\" alt=\\\"\").concat(project.images[2],\"\\\" style=\\\"width: 60px; height: 45px; object-fit: cover; border: 1px solid #ccc; border-radius: 2px;\\\" onError=\\\"this.style.display='none'\\\" />\\n                            <div style=\\\"font-family: 'Times New Roman', serif; font-size: 9pt; color: #666;\\\">\").concat(project.images[2],\"</div>\\n                          </div>\\n                        </div>\\n                      </div>\\n                    </div>\\n                  </div>\\n                </div>\\n              </div>\\n            </div>\\n          </div>\\n        </div>\\n        <span class=\\\"page-number\\\">\").concat(page,\"</span>\\n      </div>\\n    \");};// Load content when page changes\nuseEffect(()=>{loadPageContent(currentPage);},[currentPage,portfolioId,isInspiration]);const handlePreviousPage=()=>{if(currentPage>1){setCurrentPage(currentPage-1);}};const handleNextPage=()=>{if(currentPage<pageCount){setCurrentPage(currentPage+1);}};const handlePlay=()=>{setIsPlaying(true);// Implement auto-play functionality\n};const handlePause=()=>{setIsPlaying(false);};const handleCopyFlipbook=()=>{if(isInspiration&&onCopyToAccount&&flipbookTitle.trim()){onCopyToAccount(portfolioId,flipbookTitle.trim());setShowCopyModal(false);}};const handleSliderChange=event=>{setCurrentPage(parseInt(event.target.value));};return/*#__PURE__*/_jsx(\"div\",{className:\"flipbook-preview-overlay\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flipbook-preview\",children:[/*#__PURE__*/_jsx(\"header\",{className:\"preview-header\",children:/*#__PURE__*/_jsx(\"div\",{className:\"header-content\",children:/*#__PURE__*/_jsx(\"div\",{className:\"header-left\",children:/*#__PURE__*/_jsx(\"button\",{className:\"logo-btn\",onClick:onClose,title:\"Return to My Flipbooks\",children:/*#__PURE__*/_jsx(\"span\",{style:{color:'white',fontWeight:'bold',fontSize:'18px'},children:\"Flipbook\"})})})})}),isInspiration&&/*#__PURE__*/_jsx(\"div\",{className:\"inspiration-controls\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"controls-container\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"copy-btn\",onClick:()=>setShowCopyModal(true),children:\"COPY THIS INSPIRATION FLIPBOOK TO YOUR ACCOUNT TO EDIT & SAVE\"}),/*#__PURE__*/_jsx(\"button\",{className:\"return-btn\",onClick:onClose,children:\"RETURN TO MY FLIPBOOKS\"})]})}),!isInspiration&&/*#__PURE__*/_jsxs(\"div\",{className:\"user-controls\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"controls-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"playback-controls\",children:!isPlaying?/*#__PURE__*/_jsxs(\"button\",{className:\"play-btn\",onClick:handlePlay,children:[/*#__PURE__*/_jsx(\"i\",{className:\"fa fa-play\"}),\"\\xA0\\xA0Play\"]}):/*#__PURE__*/_jsxs(\"button\",{className:\"pause-btn\",onClick:handlePause,children:[/*#__PURE__*/_jsx(\"i\",{className:\"fa fa-pause\"}),\"\\xA0\\xA0Pause\"]})}),/*#__PURE__*/_jsx(\"button\",{className:\"return-btn\",onClick:onClose,children:\"Return to Page Builder\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"page-indicators\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"page-indicator first-page\",children:\"Front Cover Page\"}),/*#__PURE__*/_jsx(\"div\",{className:\"page-indicator last-page\",children:\"Back Page\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flipbook-viewer\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flipbook-canvas\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"book-container\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flipbook-content\",children:loading?/*#__PURE__*/_jsxs(\"div\",{className:\"page-loading\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Loading page \",currentPage,\"...\"]})]}):/*#__PURE__*/_jsx(\"div\",{className:\"flipbook-page-html\",dangerouslySetInnerHTML:{__html:pageContent}})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"navigation-controls\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"nav-btn prev-btn\",onClick:handlePreviousPage,disabled:currentPage===1,children:/*#__PURE__*/_jsx(\"svg\",{width:\"24\",height:\"24\",viewBox:\"0 0 24 24\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M15 18L9 12L15 6\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\"})})}),/*#__PURE__*/_jsx(\"button\",{className:\"nav-btn next-btn\",onClick:handleNextPage,disabled:currentPage===pageCount,children:/*#__PURE__*/_jsx(\"svg\",{width:\"24\",height:\"24\",viewBox:\"0 0 24 24\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M9 18L15 12L9 6\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"page-slider\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"range\",min:\"1\",max:pageCount,value:currentPage,onChange:handleSliderChange,className:\"slider\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"page-counter\",children:[currentPage,\" / \",pageCount]})]})]})}),showCopyModal&&/*#__PURE__*/_jsx(\"div\",{className:\"modal-overlay\",children:/*#__PURE__*/_jsx(\"div\",{className:\"copy-modal\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"modal-content\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"close-btn\",onClick:()=>setShowCopyModal(false),children:\"\\xD7\"}),/*#__PURE__*/_jsx(\"div\",{className:\"modal-icon\",children:/*#__PURE__*/_jsxs(\"svg\",{width:\"60\",height:\"60\",viewBox:\"0 0 24 24\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M4 19.5A2.5 2.5 0 0 1 6.5 17H20\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M6.5 2H20V22H6.5A2.5 2.5 0 0 1 4 19.5V4.5A2.5 2.5 0 0 1 6.5 2Z\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\"})]})}),/*#__PURE__*/_jsx(\"h2\",{children:\"NAME MY FLIPBOOK\"}),/*#__PURE__*/_jsx(\"div\",{className:\"input-group\",children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:flipbookTitle,onChange:e=>setFlipbookTitle(e.target.value),placeholder:\"Enter flipbook title\",maxLength:38,className:\"title-input\"})}),/*#__PURE__*/_jsx(\"button\",{className:\"submit-btn\",onClick:handleCopyFlipbook,disabled:!flipbookTitle.trim(),children:\"LET'S GO!\"})]})})})]})});};export default FlipbookPreview;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "images", "jsx", "_jsx", "jsxs", "_jsxs", "FlipbookPreview", "_ref", "portfolioId", "pageCount", "isInspiration", "onClose", "onCopyToAccount", "currentPage", "setCurrentPage", "isPlaying", "setIsPlaying", "showCopyModal", "setShowCopyModal", "flipbookTitle", "setFlipbookTitle", "pageContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "console", "log", "concat", "loadPageContent", "page", "getMockOriginalFlipbookContent", "error", "resumeInspiration", "portfolio2", "page1", "endPapers", "big03", "architecture", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flipbook", "blankImg", "mcMillen", "quincy", "overlook", "kellerCenter", "samplePages", "getSampleArchitecturalPage", "getProjectImage", "projectIndex", "imageIndex", "architectureImageList", "peter<PERSON><PERSON><PERSON>", "shingleHouse", "fredArchitect", "baseIndex", "length", "projects", "title", "location", "description", "Math", "floor", "project", "isRightPage", "layoutClass", "marginStyle", "handlePreviousPage", "handleNextPage", "handlePlay", "handlePause", "handleCopyFlipbook", "trim", "handleSliderChange", "event", "parseInt", "target", "value", "className", "children", "onClick", "style", "color", "fontWeight", "fontSize", "dangerouslySetInnerHTML", "__html", "disabled", "width", "height", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "type", "min", "max", "onChange", "e", "placeholder", "max<PERSON><PERSON><PERSON>"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/FlipbookPreview/FlipbookPreview.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { getImageById, resolveImagePath } from '../../utils/imageIndex';\nimport { handleImageError } from '../../utils/imageManager';\nimport images from '../../assets/images';\nimport './FlipbookPreview.css';\n\ninterface FlipbookPreviewProps {\n  portfolioId: string;\n  pageCount: number;\n  isInspiration: boolean;\n  onClose: () => void;\n  onCopyToAccount?: (portfolioId: string, title: string) => void;\n}\n\nconst FlipbookPreview: React.FC<FlipbookPreviewProps> = ({\n  portfolioId,\n  pageCount,\n  isInspiration,\n  onClose,\n  onCopyToAccount\n}) => {\n  const [currentPage, setCurrentPage] = useState(1);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [showCopyModal, setShowCopyModal] = useState(false);\n  const [flipbookTitle, setFlipbookTitle] = useState('');\n  const [pageContent, setPageContent] = useState<string>('');\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    console.log(`Loading flipbook: ${portfolioId} with ${pageCount} pages`);\n    \n    // Set appropriate title based on the flipbook\n    if (portfolioId === '2154') {\n      setFlipbookTitle('Original Flipbook');\n    } else {\n      setFlipbookTitle(`Flipbook ${portfolioId}`);\n    }\n  }, [portfolioId, pageCount]);\n\n  // Load page content from the flipbook HTML\n  const loadPageContent = async (page: number) => {\n    setLoading(true);\n    try {\n      // Always use mock content for now since we have the authentic HTML structure\n      // In production, this would attempt to load from the real API first\n      if (portfolioId === '2154') {\n        setPageContent(getMockOriginalFlipbookContent(page));\n      } else {\n        setPageContent(`<div class=\"page-content\"><h2>Page ${page}</h2><p>Content for page ${page} of ${flipbookTitle}</p></div>`);\n      }\n    } catch (error) {\n      console.error('Error loading page content:', error);\n      // Fallback to mock content\n      if (portfolioId === '2154') {\n        setPageContent(getMockOriginalFlipbookContent(page));\n      } else {\n        setPageContent(`<div class=\"page-content\"><h2>Page ${page}</h2><p>Content for page ${page} of ${flipbookTitle}</p></div>`);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mock content for Original Flipbook based on the provided HTML structure\n  const getMockOriginalFlipbookContent = (page: number) => {\n    if (page === 1) {\n      // Cover page with LINK SALAS ARCHITECT\n      return `\n        <div class=\"book-content\">\n          <div class=\"div-flip-main-left\">\n            <style>\n              .flip_wrapper {\n                width: min(90vw, 1200px);\n                height: min(80vh, 900px);\n                margin: 0 auto 40px;\n                border: 1px solid #000000;\n                position: relative;\n                background: url('/Flipbooks/EndPapers/90-min.jpg') 0% 0% / 200%;\n              }\n              .architect-content {\n                display: flex;\n                flex-direction: column;\n                align-items: center;\n                justify-content: center;\n                height: 100%;\n                text-align: center;\n                padding: 40px;\n                background-color: rgba(210, 196, 177, 0.9);\n                border: 7px solid rgb(210, 196, 177);\n              }\n              .architect-label {\n                font-family: 'AvenirLTStd-Book', Arial, sans-serif;\n                font-size: 21pt;\n                color: #333;\n                margin-bottom: 10px;\n                letter-spacing: 2px;\n              }\n              .architect-title {\n                font-family: 'OptimaBold', Arial, sans-serif;\n                font-size: 28pt;\n                font-weight: bold;\n                color: #333;\n                letter-spacing: 3px;\n                border-top: 3px solid #000;\n                padding-top: 10px;\n                width: 200px;\n                margin: 0 auto;\n              }\n            </style>\n            <div class=\"flip_wrapper\">\n              <div class=\"architect-content\" style=\"background-image: url('${images.resumeInspiration.portfolio2.page1}'); background-size: cover; background-position: center;\">\n                <div class=\"architect-label\" style=\"background: rgba(255,255,255,0.9); padding: 10px 20px; border-radius: 5px;\">LINK SALAS</div>\n                <div class=\"architect-title\" style=\"background: rgba(255,255,255,0.9); padding: 10px 20px; border-radius: 5px; margin-top: 10px;\">ARCHITECT</div>\n              </div>\n            </div>\n          </div>\n          <span class=\"page-number\">${page}</span>\n        </div>\n      `;\n    } else if (page === 2) {\n      // Resume page with two columns - cleaned up version without broken images\n      return `\n        <div class=\"book-content\">\n          <div class=\"div-flip-main-left\">\n            <div class=\"container divLayout2\">\n              <div class=\"common_margin_layout\">\n                <div class=\"bb-custom-wrapper\">\n                  <div id=\"bb-bookblock\" class=\"bb-bookblock\">\n                    <div class=\"bb-item show_item\">\n                      <div class=\"bg_end_paper\" style=\"background: url('${images.endPapers.big03}') 0% 0% / 100%;\">\n                        <div style=\"border: 11px solid rgb(210, 196, 177); background-color: rgb(210, 196, 177); height: 100%;\">\n                          <div class=\"divlayout2inner\">\n                            <div class=\"custom_side_wrapper\" style=\"display: flex; height: 100%;\">\n                              <div class=\"bb-custom-side frederich_img frederich_img_Left\" style=\"flex: 1; background: white; margin: clamp(8px, 1.2vw, 18px); padding: clamp(15px, 2.5vw, 35px); box-sizing: border-box; min-width: 0; overflow: visible;\">\n                                <div class=\"content_wrap\">\n                                  <div class=\"top_heading_wrap divTopHeadingLeft\">\n                                    <div style=\"font-size: 23pt; font-family: 'Gill Sans', Arial, sans-serif; font-weight: bold; margin-bottom: 20px;\">Link Salas</div>\n                                  </div>\n                                  <div class=\"content_wrap_in ip_left\">\n                                    <div class=\"left_sec\">\n                                      <div style=\"margin-bottom: 20px;\">\n                                        <div style=\"font-size: 16pt; font-family: 'Avenir', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;\">Objective</div>\n                                        <div style=\"font-size: 10pt; font-family: 'Palatino', serif; line-height: 1.4;\">To find a position as a junior designer in a residential architectural firm, preferably located in New York City.</div>\n                                      </div>\n                                      <div style=\"margin-bottom: 20px;\">\n                                        <div style=\"font-size: 16pt; font-family: 'Avenir', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;\">Personal Statement</div>\n                                        <div style=\"font-size: 10pt; font-family: 'Palatino', serif; line-height: 1.4;\">My professors have consistently mentioned my strong spatial aptitude, fine drawing skills and AutoCAD expertise. I have a strong interest in neoclassical design, especially as it applies to modern office structures.</div>\n                                      </div>\n                                      <div style=\"margin-bottom: 20px;\">\n                                        <div style=\"font-size: 16pt; font-family: 'Avenir', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;\">Professional Interests</div>\n                                        <div style=\"font-size: 10pt; font-family: 'Palatino', serif; line-height: 1.4;\">As a 2016 summer intern at SOM, I have focused on office layouts, beam strength and office desk optimization. I have the privilege of working with John Smith in the 200 Park Avenue renovation. Previously, I was a summer intern with Cesar Pelli, where we focused on enhancements to the San Francisco Billing Tower, particularly the elevator optimization.</div>\n                                      </div>\n                                    </div>\n                                  </div>\n                                </div>\n                              </div>\n                              <div class=\"bb-custom-side frederich_img frederich_img1\" style=\"flex: 1; background: white; margin: clamp(8px, 1.2vw, 18px); padding: clamp(15px, 2.5vw, 35px); box-sizing: border-box; min-width: 0; overflow: visible;\">\n                                <div class=\"content_wrap\">\n                                  <div class=\"top_heading_wrap divTopHeadingRight\">\n                                    <div style=\"font-size: 23pt; font-family: 'Gill Sans', Arial, sans-serif; font-weight: bold; margin-bottom: 20px;\">Resume Highlights</div>\n                                  </div>\n                                  <div class=\"content_wrap_in\">\n                                    <div style=\"margin-bottom: 20px;\">\n                                      <div style=\"font-size: 16pt; font-family: 'Avenir', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;\">Education</div>\n                                      <div style=\"font-size: 10pt; font-family: 'Palatino', serif; line-height: 1.4;\"><strong>University of Johannesburg - Jozi, South Africa</strong><br>Diploma in Architectural Technology (2011)</div>\n                                    </div>\n                                    <div style=\"margin-bottom: 20px;\">\n                                      <div style=\"font-size: 16pt; font-family: 'Avenir', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;\">Experience</div>\n                                      <div style=\"font-size: 10pt; font-family: 'Palatino', serif; line-height: 1.4;\"><strong>Bespoke Careers - London, August 2012 to Present</strong><br>Interiors, Graphics and Visualisations Recruitment Administrator<br>Accurately prepared project consultation document. Revise shop drawings and project submittals for project compliance. Communicate and coordinate with clients, consultants, contractors and teams.</div>\n                                    </div>\n                                    <div style=\"margin-bottom: 20px;\">\n                                      <div style=\"font-size: 16pt; font-family: 'Avenir', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;\">Skill Proficiencies</div>\n                                      <div style=\"font-size: 10pt; font-family: 'Palatino', serif; line-height: 1.4;\">FOH maintenance<br>Public event and exhibition setup<br>Intern management and scheduling</div>\n                                    </div>\n                                  </div>\n                                </div>\n                                <div class=\"footer_url\" style=\"position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); text-align: center;\">\n                                  <div style=\"font-size: 10pt; color: #333;\">flipbook.franklinreport.com/Flipbook/UserPreview/LSalas1</div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <span class=\"page-number\">${page}</span>\n        </div>\n      `;\n    } else if (page === 3) {\n      // Page 3 - Right side of the resume spread (same content but right-aligned)\n      return `\n        <div class=\"book-content\">\n          <div class=\"div-flip-main-right\">\n            <style>\n              .resume-container-right {\n                width: min(90vw, 1200px);\n                height: min(80vh, 900px);\n                margin: 0 auto;\n                border: 11px solid rgb(210, 196, 177);\n                background-color: rgb(210, 196, 177);\n                display: flex;\n                font-family: Arial, sans-serif;\n                background: url('/Flipbooks/EndPapers/90-min.jpg') 0% 0% / 100%;\n              }\n              .left-column-right, .right-column-right {\n                flex: 1;\n                padding: clamp(15px, 2.5vw, 35px);\n                background: white;\n                margin: clamp(8px, 1.2vw, 18px);\n                box-sizing: border-box;\n                min-width: 0;\n                overflow: visible;\n              }\n              .name-header-right {\n                font-family: 'GillSansMT-Bold', Arial, sans-serif;\n                font-size: 23pt;\n                font-weight: bold;\n                margin-bottom: 20px;\n              }\n              .section-header-right {\n                font-family: 'AvenirLTStd-Book', Arial, sans-serif;\n                font-size: 16pt;\n                font-weight: bold;\n                border-bottom: 1px solid #000;\n                padding-bottom: 5px;\n                margin: 15px 0 10px 0;\n              }\n              .section-content-right {\n                font-family: 'PalatinoLTStd', Arial, sans-serif;\n                font-size: 10pt;\n                line-height: 1.4;\n                margin-bottom: 15px;\n              }\n              .footer-url-right {\n                position: absolute;\n                bottom: 20px;\n                left: 50%;\n                transform: translateX(-50%);\n                font-size: 10pt;\n                color: #333;\n                text-align: center;\n                width: 100%;\n              }\n            </style>\n            <div class=\"resume-container-right\">\n              <div class=\"left-column-right\">\n                <div class=\"name-header-right\">Link Salas</div>\n                <div class=\"section-header-right\">Objective</div>\n                <div class=\"section-content-right\">To find a position as a junior designer in a residential architectural firm, preferably located in New York City.</div>\n                <div class=\"section-header-right\">Personal Statement</div>\n                <div class=\"section-content-right\">My professors have consistently mentioned my strong spatial aptitude, fine drawing skills and AutoCAD expertise. I have a strong interest in neoclassical design, especially as it applies to modern office structures.</div>\n                <div class=\"section-header-right\">Professional Interests</div>\n                <div class=\"section-content-right\">As a 2016 summer intern at SOM, I have focused on office layouts, beam strength and office desk optimization. I have the privilege of working with John Smith in the 200 Park Avenue renovation. Previously, I was a summer intern with Cesar Pelli, where we focused on enhancements to the San Francisco Billing Tower, particularly the elevator optimization.</div>\n              </div>\n              <div class=\"right-column-right\">\n                <div class=\"name-header-right\">Resume Highlights</div>\n                <div class=\"section-header-right\">Education</div>\n                <div class=\"section-content-right\"><strong>University of Johannesburg - Jozi, South Africa</strong><br>Diploma in Architectural Technology (2011)</div>\n                <div class=\"section-header-right\">Experience</div>\n                <div class=\"section-content-right\"><strong>Bespoke Careers - London, August 2012 to Present</strong><br>Interiors, Graphics and Visualisations Recruitment Administrator<br>Accurately prepared project consultation document. Revise shop drawings and project submittals for project compliance. Communicate and coordinate with clients, consultants, contractors and teams.<br><br><strong>The Architecture Foundation - London, February 2008 - January 2009</strong><br>Office administration</div>\n                <div class=\"section-header-right\">Skill Proficiencies</div>\n                <div class=\"section-content-right\">FOH maintenance<br>Public event and exhibition setup<br>Intern management and scheduling</div>\n              </div>\n            </div>\n            <div class=\"footer-url-right\">flipbook.franklinreport.com/Flipbook/UserPreview/LSalas1</div>\n          </div>\n          <span class=\"page-number\">${page}</span>\n        </div>\n      `;\n    } else if (page === 4) {\n      // Page 4 - Union Station project page\n      return `\n        <div class=\"book-content\">\n          <div class=\"div-flip-main-left\">\n            <style>\n              .project-container {\n                width: min(90vw, 1200px);\n                height: min(80vh, 900px);\n                margin: 0 auto;\n                display: flex;\n                font-family: Arial, sans-serif;\n                background: white;\n              }\n              .project-image {\n                width: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                background: #f5f5f5;\n                border: 1px solid #ddd;\n              }\n              .project-image img {\n                width: 70%;\n                height: auto;\n                opacity: 0.7;\n              }\n              .project-content {\n                width: 50%;\n                padding: 49px;\n                display: flex;\n                flex-direction: column;\n                justify-content: flex-start;\n              }\n              .project-title {\n                font-family: 'AvenirLTStd-Book', Arial, sans-serif;\n                font-size: 22pt;\n                color: #333;\n                margin-bottom: 10px;\n                font-weight: normal;\n              }\n              .project-byline {\n                font-family: 'MillerText-Roman', serif;\n                font-size: 9pt;\n                color: #666;\n                margin-bottom: 20px;\n                line-height: 1.2;\n              }\n              .project-body {\n                font-family: 'PalatinoLTStd', serif;\n                font-size: 8.25pt;\n                line-height: 1.6;\n                color: #333;\n                text-align: justify;\n              }\n            </style>\n            <div class=\"project-container\">\n              <div class=\"project-image\">\n                <img src=\"${images.architecture.grimshawBeyerBlinder}\" alt=\"Union Station\" onError=\"this.src='${images.flipbook.blankImg}'\" />\n              </div>\n              <div class=\"project-content\">\n                <div class=\"project-title\">UNION STATION</div>\n                <div class=\"project-byline\">In Washington, DC</div>\n                <div class=\"project-body\">\n                  In 2015, Beyer Blinder &amp;Belle were selected to oversee the redesign of Washington DC's historic Union Station–the remit was to take the iconic Beaux Arts transportation hub into its second century. Central to the challenge of the project was stabilizing its aging infrastructure and updating its internal systems without overly impacting the facade. The scope of work was significant, and the process involved collaboration with AmTrak, the city government, the federal government, and a small army of engineers, electricians, consultants, and other construction specialists.\n                </div>\n              </div>\n            </div>\n          </div>\n          <span class=\"page-number\">${page}</span>\n        </div>\n      `;\n    } else if (page === 5) {\n      // Page 5 - Union Station project right side of spread\n      return `\n        <div class=\"book-content\">\n          <div class=\"div-flip-main-right\">\n            <style>\n              .project-container-right {\n                width: min(90vw, 1200px);\n                height: min(80vh, 900px);\n                margin: 0 auto;\n                display: flex;\n                font-family: Arial, sans-serif;\n                background: white;\n              }\n              .project-image-right {\n                width: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                background: #f5f5f5;\n                border: 1px solid #ddd;\n              }\n              .project-image-right img {\n                width: 70%;\n                height: auto;\n                opacity: 0.7;\n              }\n              .project-content-right {\n                width: 50%;\n                padding: 49px;\n                display: flex;\n                flex-direction: column;\n                justify-content: flex-start;\n              }\n              .project-title-right {\n                font-family: 'AvenirLTStd-Book', Arial, sans-serif;\n                font-size: 22pt;\n                color: #333;\n                margin-bottom: 10px;\n                font-weight: normal;\n              }\n              .project-byline-right {\n                font-family: 'MillerText-Roman', serif;\n                font-size: 9pt;\n                color: #666;\n                margin-bottom: 20px;\n                line-height: 1.2;\n              }\n              .project-body-right {\n                font-family: 'PalatinoLTStd', serif;\n                font-size: 8.25pt;\n                line-height: 1.6;\n                color: #333;\n                text-align: justify;\n              }\n            </style>\n            <div class=\"project-container-right\">\n              <div class=\"project-image-right\">\n                <img src=\"${images.architecture.mcMillen}\" alt=\"Union Station Interior\" onError=\"this.src='${images.flipbook.blankImg}'\" />\n              </div>\n              <div class=\"project-content-right\">\n                <div class=\"project-title-right\">UNION STATION</div>\n                <div class=\"project-byline-right\">In Washington, DC</div>\n                <div class=\"project-body-right\">\n                  In 2015, Beyer Blinder &amp;Belle were selected to oversee the redesign of Washington DC's historic Union Station–the remit was to take the iconic Beaux Arts transportation hub into its second century. Central to the challenge of the project was stabilizing its aging infrastructure and updating its internal systems without overly impacting the facade. The scope of work was significant, and the process involved collaboration with AmTrak, the city government, the federal government, and a small army of engineers, electricians, consultants, and other construction specialists.\n                </div>\n              </div>\n            </div>\n          </div>\n          <span class=\"page-number\">${page}</span>\n        </div>\n      `;\n    } else if (page === 6) {\n      // Page 6 - Midcentury View project\n      return `\n        <div class=\"book-content\">\n          <div class=\"div-flip-main-left\">\n            <style>\n              .midcentury-container {\n                width: min(90vw, 1200px);\n                height: min(80vh, 900px);\n                margin: 0 auto;\n                display: flex;\n                font-family: Arial, sans-serif;\n                background: white;\n              }\n              .midcentury-image {\n                width: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                background: #f5f5f5;\n                border: 1px solid #ddd;\n              }\n              .midcentury-image img {\n                width: 90%;\n                height: 90%;\n                object-fit: cover;\n              }\n              .midcentury-content {\n                width: 50%;\n                padding: 40px;\n                display: flex;\n                flex-direction: column;\n                justify-content: flex-start;\n              }\n              .midcentury-title {\n                font-family: 'AvenirLTStd-Book', Arial, sans-serif;\n                font-size: 22pt;\n                color: #333;\n                margin-bottom: 10px;\n                font-weight: normal;\n              }\n              .midcentury-byline {\n                font-family: 'MillerText-Roman', serif;\n                font-size: 9pt;\n                color: #666;\n                margin-bottom: 20px;\n                line-height: 1.2;\n              }\n              .midcentury-body {\n                font-family: 'ACaslonPro-Regular', serif;\n                font-size: 12pt;\n                line-height: 1.6;\n                color: #333;\n                text-align: justify;\n                margin-bottom: 30px;\n              }\n              .midcentury-images {\n                display: flex;\n                flex-direction: column;\n                gap: 15px;\n              }\n              .midcentury-small-image {\n                display: flex;\n                align-items: center;\n                gap: 15px;\n              }\n              .midcentury-small-image img {\n                width: 80px;\n                height: 60px;\n                object-fit: cover;\n                border: 1px solid #ddd;\n              }\n              .midcentury-caption {\n                font-family: 'MillerText-Roman', serif;\n                font-size: 9pt;\n                color: #666;\n              }\n            </style>\n            <div class=\"midcentury-container\">\n              <div class=\"midcentury-image\">\n                <img src=\"${images.architecture.quincy}\" alt=\"Midcentury View\" onError={(e) => {e.currentTarget.src = '${images.flipbook.blankImg}'}} />\n              </div>\n              <div class=\"midcentury-content\">\n                <div class=\"midcentury-title\">MIDCENTURY VIEW</div>\n                <div class=\"midcentury-byline\">For the Harvey Family</div>\n                <div class=\"midcentury-body\">\n                  This aerie retreat exemplifies midcentury California minimalism with a few rustic touches. As a junior architect on the project, I coordinated the structural research team to ensure adherence to local code.\n                  <br><br>\n                  Adobe Caslon\n                </div>\n                <div class=\"midcentury-images\">\n                  <div class=\"midcentury-small-image\">\n                    <img src=\"${images.architecture.overlook}\" alt=\"Master Bedroom\" onError={(e) => {e.currentTarget.src = '${images.flipbook.blankImg}'}} />\n                    <div class=\"midcentury-caption\">Master Bedroom</div>\n                  </div>\n                  <div class=\"midcentury-small-image\">\n                    <img src=\"${images.architecture.kellerCenter}\" alt=\"The Sunset\" onError={(e) => {e.currentTarget.src = '${images.flipbook.blankImg}'}} />\n                    <div class=\"midcentury-caption\">The Sunset</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <span class=\"page-number\">${page}</span>\n        </div>\n      `;\n    } else {\n      // Sample portfolio pages with architectural content\n      const samplePages = getSampleArchitecturalPage(page);\n      return samplePages;\n    }\n  };\n\n  // Get project image by index\n  const getProjectImage = (projectIndex: number, imageIndex: number) => {\n    const architectureImageList = [\n      images.architecture.grimshawBeyerBlinder,\n      images.architecture.peterAaron,\n      images.architecture.mcMillen,\n      images.architecture.quincy,\n      images.architecture.overlook,\n      images.architecture.kellerCenter,\n      images.architecture.shingleHouse,\n      images.architecture.fredArchitect\n    ];\n    \n    const baseIndex = (projectIndex * 3 + imageIndex) % architectureImageList.length;\n    return architectureImageList[baseIndex];\n  };\n\n  // Generate sample architectural portfolio pages\n  const getSampleArchitecturalPage = (page: number) => {\n    const projects = [\n      {\n        title: \"DOWNTOWN LOFT RENOVATION\",\n        location: \"In Manhattan, NYC\",\n        description: \"A complete transformation of a 1920s industrial space into a modern residential loft. The project focused on maintaining the authentic brick and steel structure while introducing contemporary living elements. Custom millwork and strategic lighting highlight the dramatic ceiling heights.\",\n        images: [\"loft-main.jpg\", \"loft-kitchen.jpg\", \"loft-living.jpg\"]\n      },\n      {\n        title: \"COASTAL RESIDENCE\",\n        location: \"In Malibu, California\", \n        description: \"This oceanfront home was designed to maximize the connection between interior and exterior spaces. Floor-to-ceiling glass walls provide unobstructed ocean views while weathered cedar siding helps the structure blend with the natural landscape.\",\n        images: [\"coastal-exterior.jpg\", \"coastal-deck.jpg\", \"coastal-interior.jpg\"]\n      },\n      {\n        title: \"CORPORATE HEADQUARTERS\",\n        location: \"In Seattle, Washington\",\n        description: \"A 12-story mixed-use development combining office space with retail at street level. The design emphasizes natural light and flexible workspace configurations. Sustainable materials and energy-efficient systems earned the project LEED Gold certification.\",\n        images: [\"corp-exterior.jpg\", \"corp-lobby.jpg\", \"corp-office.jpg\"]\n      },\n      {\n        title: \"HISTORIC CHURCH RESTORATION\",\n        location: \"In Charleston, South Carolina\",\n        description: \"Careful restoration of an 1850s Gothic Revival church damaged by hurricane flooding. The project required specialized conservation techniques to preserve original stonework while updating mechanical systems and accessibility features.\",\n        images: [\"church-exterior.jpg\", \"church-interior.jpg\", \"church-detail.jpg\"]\n      },\n      {\n        title: \"MOUNTAIN CABIN RETREAT\", \n        location: \"In Aspen, Colorado\",\n        description: \"A contemporary interpretation of traditional alpine architecture. Local stone and reclaimed timber create harmony with the mountain environment while large windows frame dramatic valley views. Radiant floor heating and passive solar design ensure year-round comfort.\",\n        images: [\"cabin-exterior.jpg\", \"cabin-interior.jpg\", \"cabin-view.jpg\"]\n      },\n      {\n        title: \"URBAN MIXED-USE DEVELOPMENT\",\n        location: \"In Portland, Oregon\",\n        description: \"A transit-oriented development combining affordable housing, retail, and community spaces. The design promotes walkability and social interaction through shared courtyards and ground-floor activation. Green roofs and rainwater collection systems address local environmental priorities.\",\n        images: [\"urban-street.jpg\", \"urban-courtyard.jpg\", \"urban-rooftop.jpg\"]\n      },\n      {\n        title: \"UNIVERSITY LIBRARY EXPANSION\",\n        location: \"In Berkeley, California\",\n        description: \"A sensitive addition to a 1960s brutalist library building. The new wing provides collaborative study spaces and technology centers while respecting the existing concrete structure. Natural ventilation and daylighting reduce energy consumption.\",\n        images: [\"library-exterior.jpg\", \"library-reading.jpg\", \"library-study.jpg\"]\n      },\n      {\n        title: \"WATERFRONT PARK PAVILION\",\n        location: \"In San Diego, California\", \n        description: \"A series of interconnected pavilions providing shelter and amenities for a popular bayside park. The structures use weathering steel and cast concrete to withstand marine conditions while creating dramatic shadows and framing water views.\",\n        images: [\"pavilion-water.jpg\", \"pavilion-structure.jpg\", \"pavilion-sunset.jpg\"]\n      }\n    ];\n\n    const projectIndex = Math.floor((page - 7) / 2) % projects.length;\n    const project = projects[projectIndex];\n    const isRightPage = (page % 2) === 1;\n    const layoutClass = isRightPage ? \"div-flip-main-right\" : \"div-flip-main-left\";\n    const marginStyle = \"\";\n\n    return `\n      <div class=\"book-content\">\n        <div class=\"${layoutClass}\" style=\"${marginStyle}\">\n          <div class=\"container\">\n            <div class=\"common_margin_layout\">\n              <div class=\"bb-custom-wrapper\">\n                <div id=\"bb-bookblock\" class=\"bb-bookblock\">\n                  <div class=\"bb-item show_item\">\n                    <div style=\"display: flex; height: 576px; background: white;\">\n                      <div style=\"width: 50%; display: flex; align-items: center; justify-content: center; background: #f8f8f8; border: 1px solid #ddd;\">\n                        <img src=\"${getProjectImage(projectIndex, 0)}\" alt=\"${project.title}\" style=\"width: 90%; height: 90%; object-fit: cover;\" onError=\"this.style.display='none'\" />\n                      </div>\n                      <div style=\"width: 50%; padding: 49px; display: flex; flex-direction: column; justify-content: flex-start;\">\n                        <div style=\"font-family: 'Avenir', Arial, sans-serif; font-size: 22pt; color: #333; margin-bottom: 10px; font-weight: normal;\">${project.title}</div>\n                        <div style=\"font-family: 'Times New Roman', serif; font-size: 9pt; color: #666; margin-bottom: 20px; line-height: 1.2;\">${project.location}</div>\n                        <div style=\"font-family: 'Palatino', serif; font-size: 8.25pt; line-height: 1.6; color: #333; text-align: justify; margin-bottom: 30px;\">\n                          ${project.description}\n                        </div>\n                        <div style=\"display: flex; flex-direction: column; gap: 15px;\">\n                          <div style=\"display: flex; align-items: center; gap: 15px;\">\n                            <img src=\"${getProjectImage(projectIndex, 1)}\" alt=\"${project.images[1]}\" style=\"width: 60px; height: 45px; object-fit: cover; border: 1px solid #ccc; border-radius: 2px;\" onError=\"this.style.display='none'\" />\n                            <div style=\"font-family: 'Times New Roman', serif; font-size: 9pt; color: #666;\">${project.images[1]}</div>\n                          </div>\n                          <div style=\"display: flex; align-items: center; gap: 15px;\">\n                            <img src=\"${getProjectImage(projectIndex, 2)}\" alt=\"${project.images[2]}\" style=\"width: 60px; height: 45px; object-fit: cover; border: 1px solid #ccc; border-radius: 2px;\" onError=\"this.style.display='none'\" />\n                            <div style=\"font-family: 'Times New Roman', serif; font-size: 9pt; color: #666;\">${project.images[2]}</div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <span class=\"page-number\">${page}</span>\n      </div>\n    `;\n  };\n\n  // Load content when page changes\n  useEffect(() => {\n    loadPageContent(currentPage);\n  }, [currentPage, portfolioId, isInspiration]);\n\n  const handlePreviousPage = () => {\n    if (currentPage > 1) {\n      setCurrentPage(currentPage - 1);\n    }\n  };\n\n  const handleNextPage = () => {\n    if (currentPage < pageCount) {\n      setCurrentPage(currentPage + 1);\n    }\n  };\n\n  const handlePlay = () => {\n    setIsPlaying(true);\n    // Implement auto-play functionality\n  };\n\n  const handlePause = () => {\n    setIsPlaying(false);\n  };\n\n  const handleCopyFlipbook = () => {\n    if (isInspiration && onCopyToAccount && flipbookTitle.trim()) {\n      onCopyToAccount(portfolioId, flipbookTitle.trim());\n      setShowCopyModal(false);\n    }\n  };\n\n  const handleSliderChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    setCurrentPage(parseInt(event.target.value));\n  };\n\n  return (\n    <div className=\"flipbook-preview-overlay\">\n      <div className=\"flipbook-preview\">\n        {/* Header */}\n        <header className=\"preview-header\">\n          <div className=\"header-content\">\n            <div className=\"header-left\">\n              <button \n                className=\"logo-btn\"\n                onClick={onClose}\n                title=\"Return to My Flipbooks\"\n              >\n                <span style={{color: 'white', fontWeight: 'bold', fontSize: '18px'}}>Flipbook</span>\n              </button>\n            </div>\n          </div>\n        </header>\n\n        {/* Inspiration Controls */}\n        {isInspiration && (\n          <div className=\"inspiration-controls\">\n            <div className=\"controls-container\">\n              <button \n                className=\"copy-btn\"\n                onClick={() => setShowCopyModal(true)}\n              >\n                COPY THIS INSPIRATION FLIPBOOK TO YOUR ACCOUNT TO EDIT & SAVE\n              </button>\n              <button \n                className=\"return-btn\"\n                onClick={onClose}\n              >\n                RETURN TO MY FLIPBOOKS\n              </button>\n            </div>\n          </div>\n        )}\n\n        {/* User Flipbook Controls */}\n        {!isInspiration && (\n          <div className=\"user-controls\">\n            <div className=\"controls-container\">\n              <div className=\"playback-controls\">\n                {!isPlaying ? (\n                  <button className=\"play-btn\" onClick={handlePlay}>\n                    <i className=\"fa fa-play\"></i>&nbsp;&nbsp;Play\n                  </button>\n                ) : (\n                  <button className=\"pause-btn\" onClick={handlePause}>\n                    <i className=\"fa fa-pause\"></i>&nbsp;&nbsp;Pause\n                  </button>\n                )}\n              </div>\n              <button className=\"return-btn\" onClick={onClose}>\n                Return to Page Builder\n              </button>\n            </div>\n            <div className=\"page-indicators\">\n              <div className=\"page-indicator first-page\">Front Cover Page</div>\n              <div className=\"page-indicator last-page\">Back Page</div>\n            </div>\n          </div>\n        )}\n\n        {/* Flipbook Viewer */}\n        <div className=\"flipbook-viewer\">\n          <div className=\"flipbook-canvas\">\n            <div className=\"book-container\">\n              <div className=\"flipbook-content\">\n                {loading ? (\n                  <div className=\"page-loading\">\n                    <div className=\"loading-spinner\"></div>\n                    <p>Loading page {currentPage}...</p>\n                  </div>\n                ) : (\n                  <div \n                    className=\"flipbook-page-html\"\n                    dangerouslySetInnerHTML={{ __html: pageContent }}\n                  />\n                )}\n              </div>\n            </div>\n            <div className=\"navigation-controls\">\n              <button \n                className=\"nav-btn prev-btn\"\n                onClick={handlePreviousPage}\n                disabled={currentPage === 1}\n              >\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M15 18L9 12L15 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                </svg>\n              </button>\n              \n              <button \n                className=\"nav-btn next-btn\"\n                onClick={handleNextPage}\n                disabled={currentPage === pageCount}\n              >\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M9 18L15 12L9 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                </svg>\n              </button>\n            </div>\n\n            {/* Page Slider */}\n            <div className=\"page-slider\">\n              <input\n                type=\"range\"\n                min=\"1\"\n                max={pageCount}\n                value={currentPage}\n                onChange={handleSliderChange}\n                className=\"slider\"\n              />\n              <div className=\"page-counter\">\n                {currentPage} / {pageCount}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Copy Modal */}\n        {showCopyModal && (\n          <div className=\"modal-overlay\">\n            <div className=\"copy-modal\">\n              <div className=\"modal-content\">\n                <button \n                  className=\"close-btn\"\n                  onClick={() => setShowCopyModal(false)}\n                >\n                  ×\n                </button>\n                <div className=\"modal-icon\">\n                  <svg width=\"60\" height=\"60\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M4 19.5A2.5 2.5 0 0 1 6.5 17H20\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  <path d=\"M6.5 2H20V22H6.5A2.5 2.5 0 0 1 4 19.5V4.5A2.5 2.5 0 0 1 6.5 2Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                </svg>\n                </div>\n                <h2>NAME MY FLIPBOOK</h2>\n                <div className=\"input-group\">\n                  <input\n                    type=\"text\"\n                    value={flipbookTitle}\n                    onChange={(e) => setFlipbookTitle(e.target.value)}\n                    placeholder=\"Enter flipbook title\"\n                    maxLength={38}\n                    className=\"title-input\"\n                  />\n                </div>\n                <button \n                  className=\"submit-btn\"\n                  onClick={handleCopyFlipbook}\n                  disabled={!flipbookTitle.trim()}\n                >\n                  LET'S GO!\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default FlipbookPreview;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAGlD,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAU/B,KAAM,CAAAC,eAA+C,CAAGC,IAAA,EAMlD,IANmD,CACvDC,WAAW,CACXC,SAAS,CACTC,aAAa,CACbC,OAAO,CACPC,eACF,CAAC,CAAAL,IAAA,CACC,KAAM,CAACM,WAAW,CAAEC,cAAc,CAAC,CAAGf,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAACgB,SAAS,CAAEC,YAAY,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACkB,aAAa,CAAEC,gBAAgB,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACoB,aAAa,CAAEC,gBAAgB,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACsB,WAAW,CAAEC,cAAc,CAAC,CAAGvB,QAAQ,CAAS,EAAE,CAAC,CAC1D,KAAM,CAACwB,OAAO,CAAEC,UAAU,CAAC,CAAGzB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACdyB,OAAO,CAACC,GAAG,sBAAAC,MAAA,CAAsBnB,WAAW,WAAAmB,MAAA,CAASlB,SAAS,UAAQ,CAAC,CAEvE;AACA,GAAID,WAAW,GAAK,MAAM,CAAE,CAC1BY,gBAAgB,CAAC,mBAAmB,CAAC,CACvC,CAAC,IAAM,CACLA,gBAAgB,aAAAO,MAAA,CAAanB,WAAW,CAAE,CAAC,CAC7C,CACF,CAAC,CAAE,CAACA,WAAW,CAAEC,SAAS,CAAC,CAAC,CAE5B;AACA,KAAM,CAAAmB,eAAe,CAAG,KAAO,CAAAC,IAAY,EAAK,CAC9CL,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA;AACA,GAAIhB,WAAW,GAAK,MAAM,CAAE,CAC1Bc,cAAc,CAACQ,8BAA8B,CAACD,IAAI,CAAC,CAAC,CACtD,CAAC,IAAM,CACLP,cAAc,yCAAAK,MAAA,CAAuCE,IAAI,8BAAAF,MAAA,CAA4BE,IAAI,SAAAF,MAAA,CAAOR,aAAa,cAAY,CAAC,CAC5H,CACF,CAAE,MAAOY,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD;AACA,GAAIvB,WAAW,GAAK,MAAM,CAAE,CAC1Bc,cAAc,CAACQ,8BAA8B,CAACD,IAAI,CAAC,CAAC,CACtD,CAAC,IAAM,CACLP,cAAc,yCAAAK,MAAA,CAAuCE,IAAI,8BAAAF,MAAA,CAA4BE,IAAI,SAAAF,MAAA,CAAOR,aAAa,cAAY,CAAC,CAC5H,CACF,CAAC,OAAS,CACRK,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAM,8BAA8B,CAAID,IAAY,EAAK,CACvD,GAAIA,IAAI,GAAK,CAAC,CAAE,CACd;AACA,woDAAAF,MAAA,CA2CuE1B,MAAM,CAAC+B,iBAAiB,CAACC,UAAU,CAACC,KAAK,ieAAAP,MAAA,CAMhFE,IAAI,oCAGtC,CAAC,IAAM,IAAIA,IAAI,GAAK,CAAC,CAAE,CACrB;AACA,8bAAAF,MAAA,CAQoE1B,MAAM,CAACkC,SAAS,CAACC,KAAK,wnNAAAT,MAAA,CA6D1DE,IAAI,oCAGtC,CAAC,IAAM,IAAIA,IAAI,GAAK,CAAC,CAAE,CACrB;AACA,6lJAAAF,MAAA,CA4EgCE,IAAI,oCAGtC,CAAC,IAAM,IAAIA,IAAI,GAAK,CAAC,CAAE,CACrB;AACA,44DAAAF,MAAA,CAwDsB1B,MAAM,CAACoC,YAAY,CAACC,oBAAoB,kDAAAX,MAAA,CAA4C1B,MAAM,CAACsC,QAAQ,CAACC,QAAQ,+9BAAAb,MAAA,CAWlGE,IAAI,oCAGtC,CAAC,IAAM,IAAIA,IAAI,GAAK,CAAC,CAAE,CACrB;AACA,m8DAAAF,MAAA,CAwDsB1B,MAAM,CAACoC,YAAY,CAACI,QAAQ,2DAAAd,MAAA,CAAqD1B,MAAM,CAACsC,QAAQ,CAACC,QAAQ,u/BAAAb,MAAA,CAW/FE,IAAI,oCAGtC,CAAC,IAAM,IAAIA,IAAI,GAAK,CAAC,CAAE,CACrB;AACA,goFAAAF,MAAA,CA8EsB1B,MAAM,CAACoC,YAAY,CAACK,MAAM,wEAAAf,MAAA,CAAmE1B,MAAM,CAACsC,QAAQ,CAACC,QAAQ,6tBAAAb,MAAA,CAYjH1B,MAAM,CAACoC,YAAY,CAACM,QAAQ,uEAAAhB,MAAA,CAAkE1B,MAAM,CAACsC,QAAQ,CAACC,QAAQ,4MAAAb,MAAA,CAItH1B,MAAM,CAACoC,YAAY,CAACO,YAAY,mEAAAjB,MAAA,CAA8D1B,MAAM,CAACsC,QAAQ,CAACC,QAAQ,yOAAAb,MAAA,CAOhHE,IAAI,oCAGtC,CAAC,IAAM,CACL;AACA,KAAM,CAAAgB,WAAW,CAAGC,0BAA0B,CAACjB,IAAI,CAAC,CACpD,MAAO,CAAAgB,WAAW,CACpB,CACF,CAAC,CAED;AACA,KAAM,CAAAE,eAAe,CAAGA,CAACC,YAAoB,CAAEC,UAAkB,GAAK,CACpE,KAAM,CAAAC,qBAAqB,CAAG,CAC5BjD,MAAM,CAACoC,YAAY,CAACC,oBAAoB,CACxCrC,MAAM,CAACoC,YAAY,CAACc,UAAU,CAC9BlD,MAAM,CAACoC,YAAY,CAACI,QAAQ,CAC5BxC,MAAM,CAACoC,YAAY,CAACK,MAAM,CAC1BzC,MAAM,CAACoC,YAAY,CAACM,QAAQ,CAC5B1C,MAAM,CAACoC,YAAY,CAACO,YAAY,CAChC3C,MAAM,CAACoC,YAAY,CAACe,YAAY,CAChCnD,MAAM,CAACoC,YAAY,CAACgB,aAAa,CAClC,CAED,KAAM,CAAAC,SAAS,CAAG,CAACN,YAAY,CAAG,CAAC,CAAGC,UAAU,EAAIC,qBAAqB,CAACK,MAAM,CAChF,MAAO,CAAAL,qBAAqB,CAACI,SAAS,CAAC,CACzC,CAAC,CAED;AACA,KAAM,CAAAR,0BAA0B,CAAIjB,IAAY,EAAK,CACnD,KAAM,CAAA2B,QAAQ,CAAG,CACf,CACEC,KAAK,CAAE,0BAA0B,CACjCC,QAAQ,CAAE,mBAAmB,CAC7BC,WAAW,CAAE,iSAAiS,CAC9S1D,MAAM,CAAE,CAAC,eAAe,CAAE,kBAAkB,CAAE,iBAAiB,CACjE,CAAC,CACD,CACEwD,KAAK,CAAE,mBAAmB,CAC1BC,QAAQ,CAAE,uBAAuB,CACjCC,WAAW,CAAE,qPAAqP,CAClQ1D,MAAM,CAAE,CAAC,sBAAsB,CAAE,kBAAkB,CAAE,sBAAsB,CAC7E,CAAC,CACD,CACEwD,KAAK,CAAE,wBAAwB,CAC/BC,QAAQ,CAAE,wBAAwB,CAClCC,WAAW,CAAE,gQAAgQ,CAC7Q1D,MAAM,CAAE,CAAC,mBAAmB,CAAE,gBAAgB,CAAE,iBAAiB,CACnE,CAAC,CACD,CACEwD,KAAK,CAAE,6BAA6B,CACpCC,QAAQ,CAAE,+BAA+B,CACzCC,WAAW,CAAE,4OAA4O,CACzP1D,MAAM,CAAE,CAAC,qBAAqB,CAAE,qBAAqB,CAAE,mBAAmB,CAC5E,CAAC,CACD,CACEwD,KAAK,CAAE,wBAAwB,CAC/BC,QAAQ,CAAE,oBAAoB,CAC9BC,WAAW,CAAE,4QAA4Q,CACzR1D,MAAM,CAAE,CAAC,oBAAoB,CAAE,oBAAoB,CAAE,gBAAgB,CACvE,CAAC,CACD,CACEwD,KAAK,CAAE,6BAA6B,CACpCC,QAAQ,CAAE,qBAAqB,CAC/BC,WAAW,CAAE,+RAA+R,CAC5S1D,MAAM,CAAE,CAAC,kBAAkB,CAAE,qBAAqB,CAAE,mBAAmB,CACzE,CAAC,CACD,CACEwD,KAAK,CAAE,8BAA8B,CACrCC,QAAQ,CAAE,yBAAyB,CACnCC,WAAW,CAAE,sPAAsP,CACnQ1D,MAAM,CAAE,CAAC,sBAAsB,CAAE,qBAAqB,CAAE,mBAAmB,CAC7E,CAAC,CACD,CACEwD,KAAK,CAAE,0BAA0B,CACjCC,QAAQ,CAAE,0BAA0B,CACpCC,WAAW,CAAE,gPAAgP,CAC7P1D,MAAM,CAAE,CAAC,oBAAoB,CAAE,wBAAwB,CAAE,qBAAqB,CAChF,CAAC,CACF,CAED,KAAM,CAAA+C,YAAY,CAAGY,IAAI,CAACC,KAAK,CAAC,CAAChC,IAAI,CAAG,CAAC,EAAI,CAAC,CAAC,CAAG2B,QAAQ,CAACD,MAAM,CACjE,KAAM,CAAAO,OAAO,CAAGN,QAAQ,CAACR,YAAY,CAAC,CACtC,KAAM,CAAAe,WAAW,CAAIlC,IAAI,CAAG,CAAC,GAAM,CAAC,CACpC,KAAM,CAAAmC,WAAW,CAAGD,WAAW,CAAG,qBAAqB,CAAG,oBAAoB,CAC9E,KAAM,CAAAE,WAAW,CAAG,EAAE,CAEtB,oEAAAtC,MAAA,CAEkBqC,WAAW,gBAAArC,MAAA,CAAYsC,WAAW,+hBAAAtC,MAAA,CAQpBoB,eAAe,CAACC,YAAY,CAAE,CAAC,CAAC,cAAArB,MAAA,CAAUmC,OAAO,CAACL,KAAK,qaAAA9B,MAAA,CAG8DmC,OAAO,CAACL,KAAK,+JAAA9B,MAAA,CACpBmC,OAAO,CAACJ,QAAQ,4MAAA/B,MAAA,CAEtImC,OAAO,CAACH,WAAW,mQAAAhC,MAAA,CAIPoB,eAAe,CAACC,YAAY,CAAE,CAAC,CAAC,cAAArB,MAAA,CAAUmC,OAAO,CAAC7D,MAAM,CAAC,CAAC,CAAC,qQAAA0B,MAAA,CACYmC,OAAO,CAAC7D,MAAM,CAAC,CAAC,CAAC,gLAAA0B,MAAA,CAGxFoB,eAAe,CAACC,YAAY,CAAE,CAAC,CAAC,cAAArB,MAAA,CAAUmC,OAAO,CAAC7D,MAAM,CAAC,CAAC,CAAC,qQAAA0B,MAAA,CACYmC,OAAO,CAAC7D,MAAM,CAAC,CAAC,CAAC,2SAAA0B,MAAA,CAW5FE,IAAI,gCAGtC,CAAC,CAED;AACA7B,SAAS,CAAC,IAAM,CACd4B,eAAe,CAACf,WAAW,CAAC,CAC9B,CAAC,CAAE,CAACA,WAAW,CAAEL,WAAW,CAAEE,aAAa,CAAC,CAAC,CAE7C,KAAM,CAAAwD,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,GAAIrD,WAAW,CAAG,CAAC,CAAE,CACnBC,cAAc,CAACD,WAAW,CAAG,CAAC,CAAC,CACjC,CACF,CAAC,CAED,KAAM,CAAAsD,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAItD,WAAW,CAAGJ,SAAS,CAAE,CAC3BK,cAAc,CAACD,WAAW,CAAG,CAAC,CAAC,CACjC,CACF,CAAC,CAED,KAAM,CAAAuD,UAAU,CAAGA,CAAA,GAAM,CACvBpD,YAAY,CAAC,IAAI,CAAC,CAClB;AACF,CAAC,CAED,KAAM,CAAAqD,WAAW,CAAGA,CAAA,GAAM,CACxBrD,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAED,KAAM,CAAAsD,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,GAAI5D,aAAa,EAAIE,eAAe,EAAIO,aAAa,CAACoD,IAAI,CAAC,CAAC,CAAE,CAC5D3D,eAAe,CAACJ,WAAW,CAAEW,aAAa,CAACoD,IAAI,CAAC,CAAC,CAAC,CAClDrD,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAC,CAED,KAAM,CAAAsD,kBAAkB,CAAIC,KAA0C,EAAK,CACzE3D,cAAc,CAAC4D,QAAQ,CAACD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAC9C,CAAC,CAED,mBACEzE,IAAA,QAAK0E,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvCzE,KAAA,QAAKwE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAE/B3E,IAAA,WAAQ0E,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAChC3E,IAAA,QAAK0E,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B3E,IAAA,QAAK0E,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B3E,IAAA,WACE0E,SAAS,CAAC,UAAU,CACpBE,OAAO,CAAEpE,OAAQ,CACjB8C,KAAK,CAAC,wBAAwB,CAAAqB,QAAA,cAE9B3E,IAAA,SAAM6E,KAAK,CAAE,CAACC,KAAK,CAAE,OAAO,CAAEC,UAAU,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAM,CAAE,CAAAL,QAAA,CAAC,UAAQ,CAAM,CAAC,CAC9E,CAAC,CACN,CAAC,CACH,CAAC,CACA,CAAC,CAGRpE,aAAa,eACZP,IAAA,QAAK0E,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnCzE,KAAA,QAAKwE,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjC3E,IAAA,WACE0E,SAAS,CAAC,UAAU,CACpBE,OAAO,CAAEA,CAAA,GAAM7D,gBAAgB,CAAC,IAAI,CAAE,CAAA4D,QAAA,CACvC,+DAED,CAAQ,CAAC,cACT3E,IAAA,WACE0E,SAAS,CAAC,YAAY,CACtBE,OAAO,CAAEpE,OAAQ,CAAAmE,QAAA,CAClB,wBAED,CAAQ,CAAC,EACN,CAAC,CACH,CACN,CAGA,CAACpE,aAAa,eACbL,KAAA,QAAKwE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BzE,KAAA,QAAKwE,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjC3E,IAAA,QAAK0E,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC/B,CAAC/D,SAAS,cACTV,KAAA,WAAQwE,SAAS,CAAC,UAAU,CAACE,OAAO,CAAEX,UAAW,CAAAU,QAAA,eAC/C3E,IAAA,MAAG0E,SAAS,CAAC,YAAY,CAAI,CAAC,eAChC,EAAQ,CAAC,cAETxE,KAAA,WAAQwE,SAAS,CAAC,WAAW,CAACE,OAAO,CAAEV,WAAY,CAAAS,QAAA,eACjD3E,IAAA,MAAG0E,SAAS,CAAC,aAAa,CAAI,CAAC,gBACjC,EAAQ,CACT,CACE,CAAC,cACN1E,IAAA,WAAQ0E,SAAS,CAAC,YAAY,CAACE,OAAO,CAAEpE,OAAQ,CAAAmE,QAAA,CAAC,wBAEjD,CAAQ,CAAC,EACN,CAAC,cACNzE,KAAA,QAAKwE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B3E,IAAA,QAAK0E,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAC,kBAAgB,CAAK,CAAC,cACjE3E,IAAA,QAAK0E,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,WAAS,CAAK,CAAC,EACtD,CAAC,EACH,CACN,cAGD3E,IAAA,QAAK0E,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BzE,KAAA,QAAKwE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B3E,IAAA,QAAK0E,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B3E,IAAA,QAAK0E,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC9BvD,OAAO,cACNlB,KAAA,QAAKwE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B3E,IAAA,QAAK0E,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvCxE,KAAA,MAAAyE,QAAA,EAAG,eAAa,CAACjE,WAAW,CAAC,KAAG,EAAG,CAAC,EACjC,CAAC,cAENV,IAAA,QACE0E,SAAS,CAAC,oBAAoB,CAC9BO,uBAAuB,CAAE,CAAEC,MAAM,CAAEhE,WAAY,CAAE,CAClD,CACF,CACE,CAAC,CACH,CAAC,cACNhB,KAAA,QAAKwE,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC3E,IAAA,WACE0E,SAAS,CAAC,kBAAkB,CAC5BE,OAAO,CAAEb,kBAAmB,CAC5BoB,QAAQ,CAAEzE,WAAW,GAAK,CAAE,CAAAiE,QAAA,cAE5B3E,IAAA,QAAKoF,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,KAAK,CAAC,4BAA4B,CAAAb,QAAA,cAC5F3E,IAAA,SAAMyF,CAAC,CAAC,kBAAkB,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAC,CAAC,CAC5G,CAAC,CACA,CAAC,cAET7F,IAAA,WACE0E,SAAS,CAAC,kBAAkB,CAC5BE,OAAO,CAAEZ,cAAe,CACxBmB,QAAQ,CAAEzE,WAAW,GAAKJ,SAAU,CAAAqE,QAAA,cAEpC3E,IAAA,QAAKoF,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,KAAK,CAAC,4BAA4B,CAAAb,QAAA,cAC5F3E,IAAA,SAAMyF,CAAC,CAAC,iBAAiB,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAC,CAAC,CAC3G,CAAC,CACA,CAAC,EACN,CAAC,cAGN3F,KAAA,QAAKwE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3E,IAAA,UACE8F,IAAI,CAAC,OAAO,CACZC,GAAG,CAAC,GAAG,CACPC,GAAG,CAAE1F,SAAU,CACfmE,KAAK,CAAE/D,WAAY,CACnBuF,QAAQ,CAAE5B,kBAAmB,CAC7BK,SAAS,CAAC,QAAQ,CACnB,CAAC,cACFxE,KAAA,QAAKwE,SAAS,CAAC,cAAc,CAAAC,QAAA,EAC1BjE,WAAW,CAAC,KAAG,CAACJ,SAAS,EACvB,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CAGLQ,aAAa,eACZd,IAAA,QAAK0E,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B3E,IAAA,QAAK0E,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBzE,KAAA,QAAKwE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B3E,IAAA,WACE0E,SAAS,CAAC,WAAW,CACrBE,OAAO,CAAEA,CAAA,GAAM7D,gBAAgB,CAAC,KAAK,CAAE,CAAA4D,QAAA,CACxC,MAED,CAAQ,CAAC,cACT3E,IAAA,QAAK0E,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBzE,KAAA,QAAKkF,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,KAAK,CAAC,4BAA4B,CAAAb,QAAA,eAC9F3E,IAAA,SAAMyF,CAAC,CAAC,iCAAiC,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAC,CAAC,cAC9H7F,IAAA,SAAMyF,CAAC,CAAC,gEAAgE,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAC,CAAC,EAC1J,CAAC,CACD,CAAC,cACN7F,IAAA,OAAA2E,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzB3E,IAAA,QAAK0E,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B3E,IAAA,UACE8F,IAAI,CAAC,MAAM,CACXrB,KAAK,CAAEzD,aAAc,CACrBiF,QAAQ,CAAGC,CAAC,EAAKjF,gBAAgB,CAACiF,CAAC,CAAC1B,MAAM,CAACC,KAAK,CAAE,CAClD0B,WAAW,CAAC,sBAAsB,CAClCC,SAAS,CAAE,EAAG,CACd1B,SAAS,CAAC,aAAa,CACxB,CAAC,CACC,CAAC,cACN1E,IAAA,WACE0E,SAAS,CAAC,YAAY,CACtBE,OAAO,CAAET,kBAAmB,CAC5BgB,QAAQ,CAAE,CAACnE,aAAa,CAACoD,IAAI,CAAC,CAAE,CAAAO,QAAA,CACjC,WAED,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,CACH,CACN,EACE,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}