<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20150318 at Tue Aug 25 11:23:17 2015
 By uniteet7
Copyright (c) <PERSON>, 2005. All rights reserved.
</metadata>
<defs>
<font id="PROXIMANOVASEMIBOLDITALIC" horiz-adv-x="394" >
  <font-face 
    font-family="PROXIMANOVASEMIBOLDITALIC"
    font-weight="600"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 5 6 3 0 0 2 0 4"
    ascent="790"
    descent="-210"
    x-height="483"
    cap-height="667"
    bbox="-226 -275 1147 897"
    underline-thickness="20"
    underline-position="-113"
    unicode-range="U+0020-FB04"
  />
<missing-glyph horiz-adv-x="493" 
d="M442 -90h-351v842h351v-842zM410 -60v782h-288v-782h288z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="858" 
d="M145 0h-105l86 392h-80l21 91h79l6 28q40 166 172 166q77 0 122 -45l-54 -63q-22 22 -57 22q-32 0 -50.5 -19.5t-27.5 -61.5l-6 -27h99l-21 -91h-98zM454 0h-105l86 392h-80l21 91h79l6 27q36 167 172 167q58 0 98 -25l-38 -75q-18 14 -49 14q-60 0 -78 -81l-6 -27h99
l-21 -91h-98zM557 -196q-85 0 -126 41l46 75q24 -30 65 -30q64 0 83 82l113 511h105l-113 -511q-18 -82 -59 -125t-114 -43zM810 542q-25 0 -42 16.5t-17 39.5q0 33 23.5 53.5t50.5 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23.5 -53.5t-50.5 -20.5z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="550" 
d="M145 0h-105l86 392h-80l21 91h79l6 27q36 167 172 167q58 0 98 -25l-38 -75q-18 14 -49 14q-60 0 -78 -81l-6 -27h99l-21 -91h-98zM249 -196q-85 0 -126 41l46 75q24 -30 65 -30q64 0 83 82l113 511h105l-113 -511q-18 -82 -59 -125t-114 -43zM502 542q-25 0 -42 16.5
t-17 39.5q0 33 23.5 53.5t50.5 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23.5 -53.5t-50.5 -20.5z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="617" 
d="M145 0h-105l86 392h-80l21 91h79l6 28q40 166 172 166q77 0 122 -45l-54 -63q-22 22 -57 22q-32 0 -50.5 -19.5t-27.5 -61.5l-6 -27h99l-21 -91h-98zM454 0h-105l86 392h-80l21 91h79l6 28q40 166 172 166q77 0 122 -45l-54 -63q-22 22 -57 22q-32 0 -50.5 -19.5
t-27.5 -61.5l-6 -27h99l-21 -91h-98z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="550" 
d="M145 0h-105l86 392h-80l21 91h79l6 27q36 167 172 167q58 0 98 -25l-38 -75q-18 14 -49 14q-60 0 -78 -81l-6 -27h99l-21 -91h-98zM502 542q-25 0 -42 16.5t-17 39.5q0 33 23.5 53.5t50.5 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23.5 -53.5t-50.5 -20.5zM428 0h-105
l107 483h105z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="550" 
d="M145 0h-105l86 392h-80l21 91h79l6 27q36 167 172 167q58 0 98 -25l-38 -75q-18 14 -49 14q-60 0 -78 -81l-6 -27h99l-21 -91h-98zM428 0h-105l147 667h105z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="858" 
d="M145 0h-105l86 392h-80l21 91h79l6 28q40 166 172 166q77 0 122 -45l-54 -63q-22 22 -57 22q-32 0 -50.5 -19.5t-27.5 -61.5l-6 -27h99l-21 -91h-98zM454 0h-105l86 392h-80l21 91h79l6 27q36 167 172 167q58 0 98 -25l-38 -75q-18 14 -49 14q-60 0 -78 -81l-6 -27h99
l-21 -91h-98zM810 542q-25 0 -42 16.5t-17 39.5q0 33 23.5 53.5t50.5 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23.5 -53.5t-50.5 -20.5zM736 0h-105l107 483h105z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="858" 
d="M145 0h-105l86 392h-80l21 91h79l6 28q40 166 172 166q77 0 122 -45l-54 -63q-22 22 -57 22q-32 0 -50.5 -19.5t-27.5 -61.5l-6 -27h99l-21 -91h-98zM454 0h-105l86 392h-80l21 91h79l6 27q36 167 172 167q58 0 98 -25l-38 -75q-18 14 -49 14q-60 0 -78 -81l-6 -27h99
l-21 -91h-98zM736 0h-105l147 667h105z" />
    <glyph glyph-name="f_b" unicode="fb" horiz-adv-x="890" 
d="M145 0h-105l86 392h-80l21 91h79l6 27q36 167 172 167q58 0 98 -25l-38 -75q-18 14 -49 14q-60 0 -78 -81l-6 -27h99l-21 -91h-98zM603 -12q-51 0 -93.5 21.5t-66.5 59.5l-15 -69h-105l147 667h105l-54 -248q62 76 151 76q84 0 134.5 -52.5t50.5 -146.5q0 -77 -29 -146.5
t-88.5 -115.5t-136.5 -46zM587 81q69 0 114.5 58t45.5 136q0 58 -33 92.5t-86 34.5q-37 0 -70.5 -19t-55.5 -49l-42 -187q17 -29 50.5 -47.5t76.5 -18.5z" />
    <glyph glyph-name="f_h" unicode="fh" horiz-adv-x="877" 
d="M145 0h-105l86 392h-80l21 91h79l6 27q36 167 172 167q58 0 98 -25l-38 -75q-18 14 -49 14q-60 0 -78 -81l-6 -27h99l-21 -91h-98zM755 0h-105l67 304q4 16 4 30q0 68 -90 68q-63 0 -128 -65l-74 -337h-105l147 667h105l-56 -249q81 77 165 77q68 0 108.5 -32.5
t40.5 -88.5q0 -21 -6 -41z" />
    <glyph glyph-name="f_k" unicode="fk" horiz-adv-x="835" 
d="M145 0h-105l86 392h-80l21 91h79l6 27q36 167 172 167q58 0 98 -25l-38 -75q-18 14 -49 14q-60 0 -78 -81l-6 -27h99l-21 -91h-98zM775 0h-126l-106 203l-86 -73l-29 -130h-105l147 667h105l-91 -410l260 226h134l-249 -219z" />
    <glyph glyph-name=".notdef" horiz-adv-x="493" 
d="M442 -90h-351v842h351v-842zM410 -60v782h-288v-782h288z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="258" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="244" 
d="M159 205h-91l83 462h128zM75 -11q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="380" 
d="M162 391h-47q12 199 13 217q2 30 21 49.5t48 19.5q21 0 36.5 -15t15.5 -38q0 -10 -7 -27zM329 391h-47q12 199 13 217q1 30 21 49.5t48 19.5q21 0 36.5 -15t15.5 -38q0 -10 -7 -27z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="607" 
d="M268 0h-80l98 175h-102l-97 -175h-80l97 175h-100l37 69h102l99 179h-102l36 69h105l96 175h80l-96 -175h101l96 175h80l-97 -175h101l-35 -69h-104l-100 -179h105l-36 -69h-107zM323 244l100 179h-101l-99 -179h100z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="605" 
d="M303 -12h-12l-19 -88h-80l21 97q-138 27 -198 114l74 85q50 -70 148 -95l42 193q-38 15 -64 28t-54 34t-42 49.5t-14 64.5q0 84 70 146t181 62h9l20 90h80l-22 -100q107 -23 168 -97l-75 -84q-43 53 -117 76l-39 -174q39 -15 65 -28.5t54.5 -35t43 -50.5t14.5 -65
q0 -92 -69 -157t-185 -65zM433 188q0 24 -20.5 42t-59.5 35l-39 -173q58 1 88.5 30t30.5 66zM229 490q0 -39 78 -72l35 157h-3q-46 0 -78 -25.5t-32 -59.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="747" 
d="M142 0h-67l573 667h68zM519 -12q-65 0 -110 38t-45 99q0 85 53.5 139.5t128.5 54.5q65 0 110 -37.5t45 -99.5q0 -84 -53.5 -139t-128.5 -55zM522 55q42 0 72 34t30 86q0 34 -23 56t-58 22q-42 0 -72 -34.5t-30 -86.5q0 -33 23 -55t58 -22zM240 346q-65 0 -110.5 38
t-45.5 99q0 85 54 139.5t128 54.5q65 0 110.5 -37.5t45.5 -99.5q0 -84 -54 -139t-128 -55zM242 413q42 0 72 34t30 86q0 34 -22.5 56t-57.5 22q-42 0 -72 -34.5t-30 -86.5q0 -33 22.5 -55t57.5 -22z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="645" 
d="M555 0h-128q-27 35 -34 45q-85 -57 -174 -57q-93 0 -151 43.5t-58 128.5q0 88 51.5 138t141.5 85q-19 60 -19 110q0 77 57 130.5t138 53.5q69 0 116 -33.5t47 -94.5q0 -34 -13 -62.5t-30 -46.5t-49.5 -36t-55 -27t-63.5 -24q16 -33 43 -80q34 -60 53 -90q59 65 98 133
l76 -48q-66 -96 -127 -156q36 -54 81 -112zM235 72q52 0 111 41q-47 72 -62 99q-30 51 -53 100q-105 -51 -105 -139q0 -48 31.5 -74.5t77.5 -26.5zM290 493q0 -32 13 -74q71 25 107 51t36 69q0 29 -18 45t-46 16q-35 0 -63.5 -29t-28.5 -78z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="214" 
d="M162 391h-47q12 199 13 217q2 30 21 49.5t48 19.5q21 0 36.5 -15t15.5 -38q0 -10 -7 -27z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="276" 
d="M172 -156l-75 -43q-73 123 -73 299q0 156 70.5 309.5t196.5 275.5l54 -55q-211 -263 -211 -561q0 -104 38 -225z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="277" 
d="M103 642l76 43q72 -121 72 -299q0 -156 -70.5 -309.5t-196.5 -275.5l-54 55q211 264 211 562q0 103 -38 224z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="347" 
d="M230 376h-57l31 115l-107 -49l-16 50l110 40l-88 60l38 44l80 -69l20 110h56l-31 -115l107 50l17 -51l-109 -39l86 -61l-38 -44l-80 69z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="502" 
d="M486 304h-185l-46 -208h-74l46 208h-185l15 67h185l44 201h74l-44 -201h185z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="245" 
d="M155 58q0 -52 -38.5 -104.5t-95.5 -80.5l-39 41q71 34 92 84h-9q-23 0 -38.5 15.5t-15.5 41.5q0 30 23.5 52t53.5 22q28 0 47.5 -18.5t19.5 -52.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="300" 
d="M260 197h-240l19 90h240z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="246" 
d="M75 -11q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="315" 
d="M21 -20h-81l393 707h82z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="617" 
d="M282 -12q-108 0 -170 70.5t-62 194.5q0 106 39.5 202.5t115.5 159t170 62.5q108 0 169 -69.5t61 -194.5q0 -106 -39 -202.5t-114.5 -159.5t-169.5 -63zM292 92q58 0 104 53.5t68 127t22 146.5q0 71 -30 112.5t-93 41.5q-58 0 -104 -53t-68.5 -126t-22.5 -147
q0 -71 30.5 -113t93.5 -42z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="380" 
d="M255 0h-117l114 516l-134 -114l-52 71l234 194h102z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="595" 
d="M479 0h-483l24 111q95 55 153.5 91t120 78.5t94.5 73.5t54 65t21 65q0 42 -36 66t-94 24q-98 0 -169 -62l-51 84q41 38 102.5 59.5t125.5 21.5q102 0 173 -48.5t71 -136.5q0 -98 -98.5 -191.5t-280.5 -197.5h296z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="574" 
d="M268 -12q-91 0 -167.5 37.5t-109.5 101.5l81 69q28 -49 82 -76.5t113 -27.5q64 0 99.5 31.5t35.5 81.5q0 38 -33 61t-100 23q-53 0 -73 -2l24 106q10 -1 92 -1q63 0 103 23.5t40 71.5q0 38 -38 62.5t-103 24.5q-94 0 -165 -59l-44 79q88 83 220 83q113 0 181.5 -45.5
t68.5 -125.5q0 -67 -55.5 -114t-125.5 -54q55 -13 89.5 -49.5t34.5 -94.5q0 -88 -72 -147t-178 -59z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="580" 
d="M409 0h-117l33 151h-318l24 104l358 412h167l-91 -413h89l-23 -103h-89zM348 254l68 308l-271 -308h203z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="600" 
d="M289 -12q-183 0 -263 131l84 72q56 -99 182 -99q62 0 102.5 38t40.5 93q0 49 -37.5 77t-98.5 28q-71 0 -130 -43l-75 32l76 350h437l-22 -103h-320l-41 -184q54 44 136 44q84 0 140.5 -50t56.5 -138q0 -103 -75.5 -175.5t-192.5 -72.5z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="601" 
d="M293 -12q-117 0 -182 66.5t-65 186.5q0 62 13.5 123.5t42.5 118t69 100t97.5 69t123.5 25.5q140 0 213 -94l-74 -82q-44 72 -147 72q-77 0 -128.5 -58t-75.5 -142q-6 -18 -7 -24q30 31 79.5 54t101.5 23q90 0 149 -49.5t59 -131.5q0 -105 -79 -181t-190 -76zM294 92
q62 0 104 41t42 97q0 46 -38.5 73.5t-98.5 27.5q-78 0 -142 -64q-1 -8 -1 -40q0 -61 36.5 -98t97.5 -37z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="532" 
d="M198 0h-135l375 564h-340l22 103h481l-18 -81z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="596" 
d="M280 -12q-110 0 -184 45t-74 130q0 69 51 120.5t141 72.5q-46 20 -77 55.5t-31 81.5q0 61 39 104t94.5 61.5t118.5 18.5q100 0 173 -44t73 -124q0 -68 -49 -114.5t-131 -61.5q117 -58 117 -154q0 -85 -77 -138t-184 -53zM330 390q67 5 107.5 30.5t40.5 67.5q0 37 -37 61
t-91 24q-52 0 -89.5 -25.5t-37.5 -67.5q0 -30 33.5 -55.5t73.5 -34.5zM284 92q55 0 97.5 27.5t42.5 71.5q0 36 -35.5 63.5t-80.5 38.5q-70 -5 -116 -34t-46 -74q0 -44 38.5 -68.5t99.5 -24.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="601" 
d="M346 678q117 0 182 -66.5t65 -186.5q0 -78 -22.5 -154t-64 -139.5t-109 -103t-149.5 -39.5q-140 0 -214 95l74 81q47 -72 147 -72q77 0 129 59t75 142q5 11 7 24q-29 -31 -78.5 -54t-102.5 -23q-90 0 -148.5 49t-58.5 131q0 105 78.5 181t189.5 76zM345 574
q-62 0 -103.5 -40.5t-41.5 -97.5q0 -46 38.5 -73t98.5 -27q79 0 141 64q2 16 2 40q0 61 -37 97.5t-98 36.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="237" 
d="M75 -11q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5zM155 351q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="245" 
d="M155 58q0 -52 -38.5 -104.5t-95.5 -80.5l-39 41q71 34 92 84h-9q-23 0 -38.5 15.5t-15.5 41.5q0 30 23.5 52t53.5 22q28 0 47.5 -18.5t19.5 -52.5zM155 351q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="502" 
d="M438 90l-398 207l17 76l490 207l-19 -84l-400 -165l327 -164z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="502" 
d="M506 396h-444l15 67h444zM464 205h-444l15 68h444z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="502" 
d="M484 297l-490 -207l19 84l402 164l-329 165l17 77l398 -207z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="459" 
d="M273 226l-95 -30q-19 31 -19 71t22 70.5t53 48.5t62 34.5t53 38t22 48.5q0 31 -25.5 49t-73.5 18q-77 0 -135 -55l-50 80q88 78 202 78q89 0 148 -40t59 -107q0 -49 -24.5 -85.5t-59 -57t-69 -38t-59 -39.5t-24.5 -51q0 -19 13 -33zM178 -11q-27 0 -45.5 19t-18.5 46
q0 31 22.5 53t54.5 22q26 0 44.5 -19t18.5 -45q0 -31 -22.5 -53.5t-53.5 -22.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="783" 
d="M372 -70q-136 0 -226.5 90.5t-90.5 223.5q0 160 120 277t277 117q140 0 227.5 -90t87.5 -221q0 -113 -57.5 -177.5t-129.5 -64.5q-41 0 -65.5 22.5t-28.5 54.5l-1 7q-27 -38 -67 -61t-83 -23q-71 0 -113 46t-42 120q0 100 70.5 174t159.5 74t127 -72l11 57h94l-54 -256
q-2 -10 -2 -22q0 -23 11.5 -35.5t29.5 -12.5q36 0 68.5 42.5t32.5 127.5q0 121 -76.5 197t-202.5 76q-143 0 -249.5 -107t-106.5 -248q0 -119 80.5 -199.5t203.5 -80.5q97 0 183 56l20 -29q-98 -63 -208 -63zM369 161q74 0 127 76l26 126q-9 23 -32 41.5t-58 18.5
q-65 0 -109.5 -50t-44.5 -115q0 -43 24.5 -70t66.5 -27z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="673" 
d="M618 0h-127l-21 129h-306l-77 -129h-140l410 667h146zM460 232l-50 318l-190 -318h240z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="649" 
d="M358 0h-341l147 667h304q80 0 128.5 -45t48.5 -112t-45.5 -118t-107.5 -58q43 -12 69.5 -50.5t26.5 -84.5q0 -81 -58 -140t-172 -59zM412 392q56 0 81.5 29t25.5 70q0 31 -23.5 52t-58.5 21h-178l-38 -172h191zM351 103q55 0 87 31t32 75q0 35 -23.5 57.5t-63.5 22.5
h-185l-41 -186h194z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="682" 
d="M371 -12q-139 0 -229.5 81.5t-90.5 214.5q0 173 113 283.5t270 110.5q106 0 178.5 -48t107.5 -127l-112 -40q-23 55 -71 83t-109 28q-103 0 -178.5 -80.5t-75.5 -202.5q0 -88 55.5 -143.5t147.5 -55.5q96 0 167 77l89 -63q-52 -61 -122 -89.5t-140 -28.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="710" 
d="M264 0h-247l147 667h243q115 0 199.5 -81t84.5 -204q0 -70 -25 -135.5t-74 -122t-134 -90.5t-194 -34zM259 564l-102 -461h128q128 0 206.5 79t78.5 192q0 82 -52.5 136t-131.5 54h-127z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="577" 
d="M474 0h-457l147 667h457l-22 -103h-340l-38 -172h333l-23 -103h-333l-41 -186h340z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="563" 
d="M134 0h-117l147 667h457l-22 -103h-340l-38 -172h333l-23 -103h-333z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="717" 
d="M371 -12q-139 0 -229.5 81.5t-90.5 214.5q0 170 113 282t280 112q96 0 165.5 -44t103.5 -113l-106 -46q-21 45 -70 72t-109 27q-103 0 -178.5 -81.5t-75.5 -201.5q0 -87 55.5 -143t147.5 -56q84 0 152 59l23 105h-200l23 103h316l-56 -253q-107 -118 -264 -118z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="723" 
d="M598 0h-117l64 292h-347l-64 -292h-117l147 667h117l-60 -272h347l60 272h117z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="259" 
d="M134 0h-117l147 667h117z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="482" 
d="M162 -12q-58 0 -108.5 20.5t-79.5 58.5l66 87q37 -62 115 -62q52 0 86 32.5t46 88.5l100 454h117l-101 -455q-50 -224 -241 -224z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="621" 
d="M561 0h-138l-164 286l-78 -73l-47 -213h-117l147 667h117l-68 -308l323 308h151l-340 -314z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="506" 
d="M420 0h-411l147 667h117l-124 -564h294z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="835" 
d="M710 0h-117l114 515l-318 -515h-50l-91 515l-114 -515h-117l147 667h161l80 -458l283 458h169z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="720" 
d="M595 0h-113l-241 483l-107 -483h-117l147 667h120l237 -468l104 468h117z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="765" 
d="M371 -12q-138 0 -229 80.5t-91 215.5q0 166 111 280t272 114q137 0 228 -80t91 -215q0 -165 -110.5 -280t-271.5 -115zM377 92q105 0 179 81.5t74 201.5q0 92 -57.5 145.5t-146.5 53.5q-105 0 -178.5 -81.5t-73.5 -201.5q0 -91 57.5 -145t145.5 -54z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="609" 
d="M134 0h-117l147 667h286q77 0 129 -52.5t52 -126.5q0 -41 -14.5 -80.5t-44 -76t-83.5 -59t-125 -22.5h-175zM371 353q63 0 100.5 35t37.5 88q0 38 -27 63t-68 25h-155l-47 -211h159z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="765" 
d="M371 -12q-138 0 -229 80.5t-91 215.5q0 166 111 280t272 114q137 0 228 -80t91 -215q0 -96 -40.5 -179t-110.5 -138l37 -55l-93 -48l-35 52q-68 -27 -140 -27zM377 92q39 0 75 12l-67 102l94 47l62 -95q89 84 89 217q0 92 -57.5 145.5t-146.5 53.5q-105 0 -178.5 -81.5
t-73.5 -201.5q0 -91 57.5 -145t145.5 -54z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="629" 
d="M534 0h-130l-93 249h-122l-55 -249h-117l147 667h268q81 0 140 -50t59 -132q0 -90 -56.5 -153.5t-145.5 -74.5zM371 352h1q63 0 100 34.5t37 87.5q0 39 -29 64.5t-68 25.5h-153l-47 -212h159z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="594" 
d="M295 -12q-93 0 -171 34t-117 89l74 85q35 -48 94.5 -76t128.5 -28q59 0 90 29.5t31 66.5q0 30 -34 52.5t-82 40t-96 38.5t-82 59.5t-34 91.5q0 84 70 146t181 62q78 0 146 -28.5t109 -78.5l-75 -84q-34 43 -87.5 65.5t-109.5 22.5q-46 0 -78 -25.5t-32 -59.5
q0 -27 34 -48.5t82 -39t96 -39t82 -61t34 -92.5q0 -92 -69 -157t-185 -65z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="577" 
d="M293 0h-117l125 564h-202l22 103h521l-22 -103h-202z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="720" 
d="M348 -12q-133 0 -205.5 61.5t-72.5 166.5q0 20 5 48l89 403h119l-89 -401q-4 -24 -4 -36q1 -60 42.5 -99.5t115.5 -39.5q79 0 123.5 45t63.5 130l88 401h119l-89 -403q-29 -132 -100.5 -204t-204.5 -72z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="673" 
d="M356 0h-146l-115 667h127l83 -539l321 539h139z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="903" 
d="M652 0h-125l-21 493l-238 -493h-125l-43 667h127l17 -513l251 513h93l25 -513l243 513h134z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="661" 
d="M604 0h-133l-135 257l-239 -257h-148l329 350l-166 317h132l124 -240l219 240h149l-309 -331z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="637" 
d="M324 0h-117l61 277l-173 390h127l120 -285l246 285h141l-344 -390z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="589" 
d="M491 0h-501l21 95l438 469h-334l23 103h493l-21 -94l-438 -470h341z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="265" 
d="M150 -190h-203l193 868h203l-16 -72h-125l-161 -724h126z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="315" 
d="M178 -20l-80 707h78l80 -707h-78z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="265" 
d="M124 -190h-203l16 72h126l160 724h-125l16 72h203z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="437" 
d="M437 333h-76l-61 261l-177 -261h-85l236 334h77z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="564" 
d="M488 -112h-570l17 72h569z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="240" 
d="M309 556h-75l-133 144h100z" />
    <glyph glyph-name="grave" unicode="&#x2cb;" horiz-adv-x="240" 
d="M309 556h-75l-133 144h100z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="581" 
d="M286 495q108 0 160 -81l15 69h105l-107 -483h-105l15 64q-64 -76 -152 -76q-83 0 -134 53t-51 147q0 120 70 213.5t184 93.5zM302 402q-69 0 -114.5 -57.5t-45.5 -135.5q0 -58 33.5 -93t85.5 -35q74 0 127 69l41 187q-17 29 -50.5 47t-76.5 18z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="581" 
d="M294 -12q-51 0 -93.5 21.5t-66.5 59.5l-15 -69h-105l147 667h105l-54 -248q62 76 151 76q84 0 134.5 -52.5t50.5 -146.5q0 -77 -29 -146.5t-88.5 -115.5t-136.5 -46zM278 81q69 0 114.5 58t45.5 136q0 58 -33 92.5t-86 34.5q-37 0 -70.5 -19t-55.5 -49l-42 -187
q17 -29 50.5 -47.5t76.5 -18.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="498" 
d="M264 -12q-104 0 -167.5 59.5t-63.5 158.5q0 120 81 204.5t197 84.5q133 0 189 -93l-79 -60q-34 60 -110 60t-123 -56t-47 -135q0 -61 37.5 -95.5t95.5 -34.5q67 0 111 53l57 -70q-72 -76 -178 -76z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="581" 
d="M286 495q108 0 160 -81l56 253h105l-148 -667h-105l15 64q-64 -76 -152 -76q-83 0 -134 53t-51 147q0 120 70 213.5t184 93.5zM302 402q-69 0 -114.5 -57.5t-45.5 -135.5q0 -58 33.5 -93t85.5 -35q74 0 127 69l41 187q-17 29 -50.5 47t-76.5 18z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="557" 
d="M277 -12q-112 0 -178 58.5t-66 157.5q0 120 79.5 205.5t196.5 85.5q95 0 154.5 -58t59.5 -155q0 -38 -10 -75h-373q-1 -3 -1 -16q0 -46 38.5 -81.5t107.5 -35.5q79 0 129 41l34 -74q-76 -53 -171 -53zM426 282h2q0 2 0.5 7t0.5 6q0 50 -33 82t-94 32q-56 0 -99 -37.5
t-52 -89.5h275z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="309" 
d="M145 0h-105l86 392h-80l21 91h79l6 28q40 166 172 166q77 0 122 -45l-54 -63q-22 22 -57 22q-32 0 -50.5 -19.5t-27.5 -61.5l-6 -27h99l-21 -91h-98z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="581" 
d="M212 -196q-151 0 -226 83l63 70q48 -67 161 -67q118 0 148 135l12 53q-61 -77 -155 -77q-79 0 -131 49.5t-52 147.5q0 119 69.5 208t181.5 89q47 0 91 -22t71 -59l16 69h105l-102 -458q-50 -221 -252 -221zM262 94q35 0 68.5 18.5t57.5 46.5l39 175q-18 31 -53.5 49.5
t-78.5 18.5q-67 0 -110 -54.5t-43 -130.5q0 -56 33 -89.5t87 -33.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="568" 
d="M446 0h-105l67 304q4 16 4 30q0 68 -90 68q-63 0 -128 -65l-74 -337h-105l147 667h105l-56 -249q81 77 165 77q68 0 108.5 -32.5t40.5 -88.5q0 -21 -6 -41z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="241" 
d="M193 542q-25 0 -42 16.5t-17 39.5q0 33 23.5 53.5t50.5 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23.5 -53.5t-50.5 -20.5zM119 0h-105l107 483h105z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="241" 
d="M-60 -196q-85 0 -126 41l46 75q24 -30 65 -30q64 0 83 82l113 511h105l-113 -511q-18 -82 -59 -125t-114 -43zM193 542q-25 0 -42 16.5t-17 39.5q0 33 23.5 53.5t50.5 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23.5 -53.5t-50.5 -20.5z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="527" 
d="M466 0h-126l-106 203l-86 -73l-29 -130h-105l147 667h105l-91 -410l260 226h134l-249 -219z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="241" 
d="M119 0h-105l147 667h105z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="834" 
d="M712 0h-105l68 308q3 15 3 26q0 32 -21.5 50t-53.5 18q-59 0 -113 -65l-75 -337h-105l68 308q4 15 4 29q0 29 -20 47t-56 18q-57 0 -112 -65l-75 -337h-105l107 483h105l-15 -65q72 77 149 77q61 0 96.5 -33t35.5 -61v-2q80 96 165 96q56 0 95 -32t39 -89q0 -11 -6 -41z
" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="568" 
d="M446 0h-105l67 303q4 20 4 31q0 68 -90 68q-65 0 -128 -65l-75 -337h-105l107 483h105l-15 -65q81 77 165 77q68 0 108.5 -32.5t40.5 -88.5q0 -19 -6 -41z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="574" 
d="M264 -12q-108 0 -169.5 60.5t-61.5 157.5q0 116 79 202.5t196 86.5q108 0 169.5 -60t61.5 -157q0 -116 -79 -203t-196 -87zM268 81q69 0 115.5 58t46.5 134q0 60 -33.5 94.5t-92.5 34.5q-69 0 -115.5 -57.5t-46.5 -134.5q0 -60 33.5 -94.5t92.5 -34.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="579" 
d="M294 -12q-51 0 -93.5 21.5t-66.5 59.5l-56 -253h-105l148 667h105l-14 -64q62 76 151 76q84 0 134.5 -52.5t50.5 -146.5q0 -77 -29 -146.5t-88.5 -115.5t-136.5 -46zM278 81q69 0 114.5 58t45.5 136q0 58 -33 92.5t-86 34.5q-37 0 -70.5 -19t-55.5 -49l-42 -187
q17 -29 50.5 -47.5t76.5 -18.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="579" 
d="M286 495q108 0 160 -81l15 69h105l-148 -667h-105l56 248q-64 -76 -152 -76q-83 0 -134 53t-51 147q0 120 70 213.5t184 93.5zM302 402q-69 0 -114.5 -57.5t-45.5 -135.5q0 -58 33.5 -93t85.5 -35q74 0 127 69l41 187q-17 29 -50.5 47t-76.5 18z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="347" 
d="M119 0h-105l107 483h105l-15 -67q71 79 175 79l-24 -106q-17 5 -42 5q-36 0 -71 -19t-58 -48z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="472" 
d="M214 -12q-140 0 -218 87l62 72q23 -29 69 -52t96 -23q40 0 64 19t24 46q0 21 -25.5 36.5t-62.5 27.5t-74 27t-62.5 43t-25.5 67q0 65 51.5 111t140.5 46q60 0 112.5 -21.5t84.5 -56.5l-57 -70q-17 26 -58 45.5t-85 19.5q-39 0 -61.5 -16t-22.5 -41q0 -23 39 -41.5
t85.5 -32t85.5 -46.5t39 -82q0 -70 -54 -117.5t-147 -47.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="319" 
d="M188 -12q-61 0 -95.5 24.5t-34.5 73.5q0 13 3 31l61 275h-80l21 91h80l29 132h105l-29 -132h98l-21 -91h-98l-55 -249q-2 -12 -2 -18q0 -44 48 -44q23 0 40 13l6 -84q-30 -22 -76 -22z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="567" 
d="M121 483h105l-67 -302q-5 -25 -5 -31q0 -35 24.5 -52t65.5 -17q65 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="503" 
d="M254 0h-113l-90 483h109l60 -362l222 362h116z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="755" 
d="M546 0h-110l-35 354l-190 -354h-110l-43 483h106l24 -351l193 351h92l37 -351l180 351h113z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="497" 
d="M438 0h-114l-86 178l-165 -178h-122l232 248l-114 235h114l78 -163l151 163h122l-220 -235z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="503" 
d="M-47 -185l35 92q18 -8 49 -8q37 0 65 40l38 58l-89 486h109l60 -362l222 362h116l-360 -570q-36 -58 -76.5 -83.5t-94.5 -25.5q-43 0 -74 11z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="476" 
d="M376 0h-384l18 80l303 312h-234l21 91h379l-17 -76l-308 -315h241z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="277" 
d="M163 -190h-47q-52 0 -89.5 33.5t-37.5 85.5q0 15 3 29l42 187q2 12 2 18q0 22 -10.5 36t-28.5 14l14 62q52 0 67 68l42 186q34 149 175 149h61l-16 -72h-61q-65 0 -82 -76l-45 -205q-17 -73 -65 -89q29 -15 29 -55q0 -18 -4 -34l-42 -188q-2 -12 -2 -20q0 -25 16 -41
t41 -16h54z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="214" 
d="M163 -20h-72v707h72v-707z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="277" 
d="M113 678h47q52 0 89.5 -33.5t37.5 -85.5q0 -13 -4 -29l-41 -187q-2 -12 -2 -17q0 -22 10.5 -36.5t28.5 -14.5l-14 -62q-52 0 -67 -68l-42 -186q-34 -149 -175 -149h-61l16 72h61q65 0 82 76l45 205q17 73 65 89q-29 15 -29 55q0 18 4 34l42 188q2 12 2 20q0 25 -16 41
t-41 16h-54z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="507" 
d="M501 667l71 -8q-54 -242 -184 -242q-39 0 -61.5 18.5t-29 45.5t-10 54t-12.5 45.5t-29 18.5q-32 0 -61.5 -52t-46.5 -131l-72 9q53 241 185 241q33 0 54.5 -13.5t29 -34t13 -44t7.5 -44t11 -34t26 -13.5q32 0 62 53t47 131z" />
    <glyph glyph-name="nbspace" unicode="&#xa0;" horiz-adv-x="258" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="244" 
d="M84 279h91l-83 -463h-129zM168 494q26 0 44.5 -19t18.5 -45q0 -31 -22.5 -53.5t-53.5 -22.5q-27 0 -45.5 19t-18.5 46q0 31 22.5 53t54.5 22z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="498" 
d="M169 -100l21 97q-73 19 -115 74t-42 135q0 117 77.5 201t190.5 88l16 70h78l-17 -77q81 -20 122 -86l-79 -60q-23 38 -64 52l-70 -313q58 6 98 53l57 -70q-71 -75 -176 -76l-19 -88h-78zM141 211q0 -87 71 -118l68 306q-63 -12 -101 -65.5t-38 -122.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="536" 
d="M27 269l15 69h94q-2 5 -9.5 18t-10.5 19t-8 18.5t-7.5 21.5t-4.5 21.5t-2 24.5q0 88 74.5 152t179.5 64q81 0 142.5 -34t79.5 -93l-105 -45q-8 35 -39 55.5t-73 20.5q-55 0 -94 -37t-39 -95q0 -21 5.5 -39t17.5 -40.5t16 -31.5h144l-15 -69h-117q-3 -45 -33.5 -86.5
t-66.5 -62.5q23 8 46 8q31 0 77 -18.5t73 -18.5q46 0 86 38l26 -93q-49 -49 -120 -49q-47 0 -114.5 22.5t-100.5 22.5q-49 0 -113 -39l-22 83q66 28 109 76.5t43 99.5q0 3 -0.5 8.5t-0.5 8.5h-133z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="522" 
d="M461 136l-34 -27l-37 49q-61 -44 -132 -44q-83 0 -145 59l-79 -66l-23 31l77 63q-31 56 -31 108q0 95 80 173l-37 49l32 26l39 -48q60 43 133 43q81 0 143 -57l78 64l25 -30l-78 -64q31 -51 31 -108q0 -91 -80 -173zM454 351q0 68 -43.5 110.5t-111.5 42.5
q-77 0 -135 -56.5t-58 -132.5q0 -68 43.5 -110.5t111.5 -42.5q77 0 135 56.5t58 132.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="637" 
d="M326 0h-117l26 118h-241l16 68h240l20 91h-240l15 68h195l-143 322h127l120 -285l246 285h141l-284 -322h190l-15 -68h-235l-20 -91h235l-15 -68h-235z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="214" 
d="M163 -20h-72v316h72v-316zM163 371h-72v316h72v-316z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="477" 
d="M454 328q0 -48 -32 -87.5t-88 -57.5q65 -35 65 -101q0 -68 -55 -115.5t-142 -47.5q-149 0 -229 83l61 63q26 -33 71.5 -52.5t95.5 -19.5q41 0 67 21.5t26 51.5q0 24 -25.5 41.5t-61.5 30t-72 27.5t-61.5 43.5t-25.5 69.5q0 50 35 87.5t100 52.5q-80 35 -80 107
q0 64 52.5 108t137.5 44q137 0 206 -74l-57 -57q-23 27 -62.5 42t-81.5 15t-67 -19t-25 -47q0 -24 25.5 -40.5t62 -28.5t73 -26.5t62 -43.5t25.5 -70zM345 305q0 51 -79 77q-56 -8 -84 -31t-28 -53q0 -29 25 -46.5t77 -34.5q45 12 67 36t22 52z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="285" 
d="M387 615q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM178 615q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="778" 
d="M754 334q0 -143 -101.5 -244t-244.5 -101q-142 0 -243 101t-101 244t100.5 244t243.5 101t244.5 -101t101.5 -244zM720 334q0 129 -91.5 220t-220.5 91q-128 0 -219 -91t-91 -220q0 -128 91.5 -219.5t218.5 -91.5q129 0 220.5 91.5t91.5 219.5zM506 207l23 -31
q-63 -54 -137 -54q-87 0 -139.5 52.5t-52.5 132.5q0 100 71 170t163 70q108 0 158 -81l-34 -24q-37 68 -125 68q-73 0 -132 -58.5t-59 -143.5q0 -62 41.5 -105.5t108.5 -43.5q66 0 114 48z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="412" 
d="M269 587q-45 0 -76 -37.5t-31 -89.5q0 -39 21 -59.5t55 -20.5q45 0 81 40l29 131q-25 36 -79 36zM368 640h77l-69 -314h-78l9 42q-43 -50 -100 -50q-53 0 -89 35t-36 99q0 81 49 138.5t120 57.5q74 0 108 -49z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="490" 
d="M245 63h-96l-120 180l200 177h104l-204 -182zM415 63h-96l-120 180l200 177h104l-204 -182z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="511" 
d="M521 463l-57 -258h-70l42 191h-374l15 67h444z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="494" 
d="M508 465q0 -88 -62.5 -150t-150.5 -62t-149.5 62t-61.5 150t62 150t150 62t150 -61.5t62 -150.5zM478 465q0 76 -53.5 129t-129.5 53q-75 0 -127.5 -53t-52.5 -129q0 -75 52.5 -128t127.5 -53q76 0 129.5 53.5t53.5 127.5zM368 343h-39l-42 96h-45l-21 -96h-33l53 243h93
q30 0 50 -18t20 -46q0 -34 -25 -58t-55 -24zM369 519q0 38 -42 38h-59l-19 -88h66q24 0 39 14.5t15 35.5z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="363" 
d="M439 591h-362l14 62h362z" />
    <glyph glyph-name="macron" unicode="&#x203e;" horiz-adv-x="363" 
d="M439 591h-362l14 62h362z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="302" 
d="M348 547q0 -53 -38.5 -91t-91.5 -38q-54 0 -91.5 38t-37.5 91q0 54 37.5 92t91.5 38q53 0 91.5 -38t38.5 -92zM288 547q0 29 -21 49.5t-49 20.5t-48.5 -20.5t-20.5 -49.5t19.5 -48.5t48.5 -19.5t50 20t21 48z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="502" 
d="M491 325h-185l-46 -207h-74l46 207h-185l15 68h185l44 200h74l-44 -200h185zM419 0h-444l14 68h445z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" 
d="M392 421h-305l14 64q156 91 212.5 135.5t56.5 87.5q0 26 -21.5 40t-53.5 14q-68 0 -112 -44l-34 53q59 56 149 56q66 0 110.5 -29.5t44.5 -83.5q0 -56 -58 -108.5t-175 -119.5h186z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" 
d="M419 534q0 -47 -41.5 -83.5t-111.5 -36.5q-57 0 -102.5 19.5t-68.5 53.5l51 49q39 -57 117 -57q36 0 56 18.5t20 46.5q0 23 -25.5 36.5t-68.5 13.5q-25 0 -32 -1l14 64q13 -1 63 -1q38 0 59.5 14.5t21.5 40.5q0 24 -25 37.5t-60 13.5q-60 0 -106 -39l-28 51q58 53 141 53
q71 0 115.5 -27t44.5 -77q0 -41 -30 -69.5t-81 -32.5q33 -8 55 -30.5t22 -56.5z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="240" 
d="M341 700l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="567" 
d="M78 -184h-105l148 667h105l-71 -322q-5 -43 15.5 -62t63.5 -19q74 0 138 64l75 339h105l-107 -483h-105l14 64q-86 -76 -187 -76q-27 0 -50 6z" />
    <glyph glyph-name="mu" unicode="&#x3bc;" horiz-adv-x="567" 
d="M78 -184h-105l148 667h105l-71 -322q-5 -43 15.5 -62t63.5 -19q74 0 138 64l75 339h105l-107 -483h-105l14 64q-86 -76 -187 -76q-27 0 -50 6z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="449" 
d="M315 -100h-55l159 712h-83l-159 -712h-55l94 423q-57 0 -97 44.5t-40 111.5q0 73 56.5 130.5t137.5 57.5h212z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="246" 
d="M116 175q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5z" />
    <glyph glyph-name="periodcentered" unicode="&#x2219;" horiz-adv-x="246" 
d="M116 175q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="214" 
d="M26 -194q-75 0 -115 40l31 43q35 -35 85 -35q26 0 40 11t14 29q0 26 -32 26q-20 0 -32 -15l-36 23l49 83h55l-39 -64q14 11 32 11q25 0 41.5 -16.5t16.5 -44.5q0 -41 -31 -66t-79 -25z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="264" 
d="M243 421h-81l64 293l-79 -66l-37 50l151 123h70z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="405" 
d="M241 318q-72 0 -114.5 40t-42.5 105q0 77 55 131t131 54q72 0 114.5 -40t42.5 -104q0 -78 -55 -132t-131 -54zM242 381q48 0 77 37.5t29 86.5q0 36 -21.5 57.5t-57.5 21.5q-48 0 -76.5 -37t-28.5 -86q0 -36 21 -58t57 -22z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="490" 
d="M77 420h96l120 -180l-200 -177h-104l204 182zM247 420h96l120 -180l-200 -177h-104l204 182z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="806" 
d="M712 667l-574 -667h-67l574 667h67zM743 92h-54l-20 -92h-80l20 92h-197l13 57l220 251h112l-54 -244h54zM623 156l38 171l-154 -171h116zM209 267h-81l64 293l-79 -66l-37 50l151 123h70z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="841" 
d="M712 667l-574 -667h-67l574 667h67zM747 0h-305l14 64q156 91 212.5 135.5t56.5 87.5q0 26 -21.5 40t-53.5 14q-68 0 -112 -44l-34 53q59 56 149 56q66 0 110.5 -29.5t44.5 -83.5q0 -56 -58 -108.5t-175 -119.5h186zM209 267h-81l64 293l-79 -66l-37 50l151 123h70z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="903" 
d="M809 667l-574 -667h-67l574 667h67zM839 92h-54l-20 -92h-80l20 92h-197l13 57l220 251h112l-54 -244h54zM719 156l38 171l-154 -171h116zM385 380q0 -47 -41.5 -83.5t-111.5 -36.5q-57 0 -102.5 19.5t-68.5 53.5l51 49q39 -57 117 -57q36 0 56 18.5t20 46.5
q0 23 -25.5 36.5t-68.5 13.5q-25 0 -32 -1l14 64q13 -1 63 -1q38 0 59.5 14.5t21.5 40.5q0 24 -25 37.5t-60 13.5q-60 0 -106 -39l-28 51q58 53 141 53q71 0 115.5 -27t44.5 -77q0 -41 -30 -69.5t-81 -32.5q33 -8 55 -30.5t22 -56.5z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="396" 
d="M184 257l95 30q20 -32 20 -71q0 -40 -22 -70.5t-53.5 -48.5t-62.5 -34.5t-53 -38t-22 -48.5q0 -31 26 -49.5t73 -18.5q77 0 135 55l51 -80q-88 -78 -202 -78q-89 0 -148.5 40t-59.5 107q0 49 24.5 85.5t59.5 57.5t69.5 38.5t59 39.5t24.5 51q0 19 -14 33zM280 494
q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="673" 
d="M520 728h-75l-133 144h100zM618 0h-127l-21 129h-306l-77 -129h-140l410 667h146zM460 232l-50 318l-190 -318h240z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="673" 
d="M642 872l-197 -144h-77l172 144h102zM618 0h-127l-21 129h-306l-77 -129h-140l410 667h146zM460 232l-50 318l-190 -318h240z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="673" 
d="M588 728h-64l-57 97l-95 -97h-69l125 144h96zM618 0h-127l-21 129h-306l-77 -129h-140l410 667h146zM460 232l-50 318l-190 -318h240z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="673" 
d="M515 726q-31 0 -50 19.5t-34 39t-35 19.5q-36 0 -52 -72h-59q28 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q36 0 53 72h58q-28 -134 -119 -134zM618 0h-127l-21 129h-306l-77 -129h-140l410 667h146zM460 232l-50 318l-190 -318h240z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="673" 
d="M620 787q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM411 787q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM618 0h-127l-21 129h-306l-77 -129h-140l410 667h146z
M460 232l-50 318l-190 -318h240z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="673" 
d="M448 692q-41 0 -67 26t-26 67q0 46 33.5 79t78.5 33q41 0 67 -26t26 -67q0 -45 -33 -78.5t-79 -33.5zM452 743q23 0 40 16.5t17 39.5q0 21 -12.5 34t-33.5 13q-23 0 -40 -16.5t-17 -39.5q0 -20 13 -33.5t33 -13.5zM618 0h-127l-21 129h-306l-77 -129h-140l410 667h146z
M460 232l-50 318l-190 -318h240z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="960" 
d="M858 0h-457l28 129h-242l-105 -129h-138l557 667h504l-22 -103h-340l-38 -172h333l-23 -103h-333l-41 -186h340zM452 232l69 311l-256 -311h187z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="682" 
d="M371 -12q-13 0 -20 1l-25 -42q14 11 32 11q25 0 41.5 -16.5t16.5 -44.5q0 -41 -31 -66t-79 -25q-75 0 -115 40l31 43q35 -35 85 -35q26 0 40 11t14 29q0 26 -32 26q-20 0 -32 -15l-36 23l39 67q-112 20 -180.5 97t-68.5 192q0 173 113 283.5t270 110.5q106 0 178.5 -48
t107.5 -127l-112 -40q-23 55 -71 83t-109 28q-103 0 -178.5 -80.5t-75.5 -202.5q0 -88 55.5 -143.5t147.5 -55.5q96 0 167 77l89 -63q-52 -61 -122 -89.5t-140 -28.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="577" 
d="M482 728h-75l-133 144h100zM474 0h-457l147 667h457l-22 -103h-340l-38 -172h333l-23 -103h-333l-41 -186h340z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="577" 
d="M604 872l-197 -144h-77l172 144h102zM474 0h-457l147 667h457l-22 -103h-340l-38 -172h333l-23 -103h-333l-41 -186h340z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="577" 
d="M549 728h-64l-57 97l-95 -97h-69l125 144h96zM474 0h-457l147 667h457l-22 -103h-340l-38 -172h333l-23 -103h-333l-41 -186h340z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="577" 
d="M583 787q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM374 787q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM474 0h-457l147 667h457l-22 -103h-340l-38 -172h333
l-23 -103h-333l-41 -186h340z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="259" 
d="M311 728h-75l-133 144h100zM134 0h-117l147 667h117z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="259" 
d="M436 872l-197 -144h-77l172 144h102zM134 0h-117l147 667h117z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="259" 
d="M379 728h-64l-57 97l-95 -97h-69l125 144h96zM134 0h-117l147 667h117z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="259" 
d="M412 786q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 14.5t-15 37.5q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM203 786q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 14.5t-14.5 37.5q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM134 0h-117l147 667h117z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="736" 
d="M290 0h-247l63 287h-87l21 94h87l63 286h243q115 0 199.5 -81t84.5 -204q0 -70 -25 -135.5t-74 -122t-134 -90.5t-194 -34zM314 103q127 1 204.5 79.5t77.5 191.5q0 82 -52.5 136t-131.5 54h-127l-41 -183h158l-21 -94h-158l-40 -184h131z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="720" 
d="M536 726q-31 0 -50 19.5t-34 39t-35 19.5q-36 0 -52 -72h-59q28 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q36 0 53 72h58q-28 -134 -119 -134zM595 0h-113l-241 483l-107 -483h-117l147 667h120l237 -468l104 468h117z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="765" 
d="M568 728h-75l-133 144h100zM371 -12q-138 0 -229 80.5t-91 215.5q0 166 111 280t272 114q137 0 228 -80t91 -215q0 -165 -110.5 -280t-271.5 -115zM377 92q105 0 179 81.5t74 201.5q0 92 -57.5 145.5t-146.5 53.5q-105 0 -178.5 -81.5t-73.5 -201.5q0 -91 57.5 -145
t145.5 -54z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="765" 
d="M688 872l-197 -144h-77l172 144h102zM371 -12q-138 0 -229 80.5t-91 215.5q0 166 111 280t272 114q137 0 228 -80t91 -215q0 -165 -110.5 -280t-271.5 -115zM377 92q105 0 179 81.5t74 201.5q0 92 -57.5 145.5t-146.5 53.5q-105 0 -178.5 -81.5t-73.5 -201.5
q0 -91 57.5 -145t145.5 -54z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="765" 
d="M635 728h-64l-57 97l-95 -97h-69l125 144h96zM371 -12q-138 0 -229 80.5t-91 215.5q0 166 111 280t272 114q137 0 228 -80t91 -215q0 -165 -110.5 -280t-271.5 -115zM377 92q105 0 179 81.5t74 201.5q0 92 -57.5 145.5t-146.5 53.5q-105 0 -178.5 -81.5t-73.5 -201.5
q0 -91 57.5 -145t145.5 -54z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="765" 
d="M561 726q-31 0 -50 19.5t-34 39t-35 19.5q-36 0 -52 -72h-59q28 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q36 0 53 72h58q-28 -134 -119 -134zM371 -12q-138 0 -229 80.5t-91 215.5q0 166 111 280t272 114q137 0 228 -80t91 -215q0 -165 -110.5 -280
t-271.5 -115zM377 92q105 0 179 81.5t74 201.5q0 92 -57.5 145.5t-146.5 53.5q-105 0 -178.5 -81.5t-73.5 -201.5q0 -91 57.5 -145t145.5 -54z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="765" 
d="M666 787q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM457 787q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM371 -12q-138 0 -229 80.5t-91 215.5q0 166 111 280t272 114
q137 0 228 -80t91 -215q0 -165 -110.5 -280t-271.5 -115zM377 92q105 0 179 81.5t74 201.5q0 92 -57.5 145.5t-146.5 53.5q-105 0 -178.5 -81.5t-73.5 -201.5q0 -91 57.5 -145t145.5 -54z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="502" 
d="M375 144l-110 142l-178 -146l-43 52l180 147l-111 142l54 43l110 -142l179 146l41 -51l-178 -147l109 -142z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="765" 
d="M371 -12q-119 0 -202 59l-44 -47h-87l84 89q-71 77 -71 195q0 166 111 280t272 114q111 0 193 -54l41 43h87l-79 -83q77 -80 77 -201q0 -165 -110.5 -280t-271.5 -115zM377 92q105 0 179 81.5t74 201.5q0 72 -36 121l-345 -365q51 -39 128 -39zM174 291q0 -66 31 -114
l343 363q-53 34 -122 34q-105 0 -178.5 -81.5t-73.5 -201.5z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="720" 
d="M544 728h-75l-133 144h100zM348 -12q-133 0 -205.5 61.5t-72.5 166.5q0 20 5 48l89 403h119l-89 -401q-4 -24 -4 -36q1 -60 42.5 -99.5t115.5 -39.5q79 0 123.5 45t63.5 130l88 401h119l-89 -403q-29 -132 -100.5 -204t-204.5 -72z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="720" 
d="M665 872l-197 -144h-77l172 144h102zM348 -12q-133 0 -205.5 61.5t-72.5 166.5q0 20 5 48l89 403h119l-89 -401q-4 -24 -4 -36q1 -60 42.5 -99.5t115.5 -39.5q79 0 123.5 45t63.5 130l88 401h119l-89 -403q-29 -132 -100.5 -204t-204.5 -72z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="720" 
d="M615 728h-64l-57 97l-95 -97h-69l125 144h96zM348 -12q-133 0 -205.5 61.5t-72.5 166.5q0 20 5 48l89 403h119l-89 -401q-4 -24 -4 -36q1 -60 42.5 -99.5t115.5 -39.5q79 0 123.5 45t63.5 130l88 401h119l-89 -403q-29 -132 -100.5 -204t-204.5 -72z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="720" 
d="M644 787q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM435 787q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM348 -12q-133 0 -205.5 61.5t-72.5 166.5q0 20 5 48l89 403
h119l-89 -401q-4 -24 -4 -36q1 -60 42.5 -99.5t115.5 -39.5q79 0 123.5 45t63.5 130l88 401h119l-89 -403q-29 -132 -100.5 -204t-204.5 -72z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="637" 
d="M626 872l-197 -144h-77l172 144h102zM324 0h-117l61 277l-173 390h127l120 -285l246 285h141l-344 -390z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="609" 
d="M134 0h-117l147 667h117l-24 -112h168q76 0 128.5 -53t52.5 -127q0 -42 -15 -82.5t-45.5 -76t-84 -57.5t-122.5 -22h-175zM346 240q65 0 101.5 36.5t36.5 87.5q0 38 -27.5 63t-67.5 25h-155l-47 -212h159z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="623" 
d="M588 152q0 -67 -54.5 -115.5t-141.5 -48.5q-70 0 -119 21.5t-91 68.5l61 66q57 -71 152 -71q40 0 64.5 20t24.5 46q0 20 -23.5 34.5t-57 25.5t-67.5 24.5t-57.5 39.5t-23.5 63q0 38 18 66.5t44.5 45t52.5 29.5t44 27.5t18 32.5q0 26 -26 41.5t-64 15.5q-44 0 -74 -26
t-41 -73l-107 -485h-105l107 485q18 85 75.5 138.5t149.5 53.5q76 0 136 -37t60 -94q0 -37 -19 -64.5t-46 -43t-54 -29t-46 -30t-19 -36.5q0 -19 23.5 -32.5t57.5 -24.5t67.5 -25.5t57 -42t23.5 -66.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="581" 
d="M286 495q108 0 160 -81l15 69h105l-107 -483h-105l15 64q-64 -76 -152 -76q-83 0 -134 53t-51 147q0 120 70 213.5t184 93.5zM302 402q-69 0 -114.5 -57.5t-45.5 -135.5q0 -58 33.5 -93t85.5 -35q74 0 127 69l41 187q-17 29 -50.5 47t-76.5 18zM415 556h-75l-133 144h100
z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="581" 
d="M286 495q108 0 160 -81l15 69h105l-107 -483h-105l15 64q-64 -76 -152 -76q-83 0 -134 53t-51 147q0 120 70 213.5t184 93.5zM302 402q-69 0 -114.5 -57.5t-45.5 -135.5q0 -58 33.5 -93t85.5 -35q74 0 127 69l41 187q-17 29 -50.5 47t-76.5 18zM537 700l-197 -144h-77
l172 144h102z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="581" 
d="M286 495q108 0 160 -81l15 69h105l-107 -483h-105l15 64q-64 -76 -152 -76q-83 0 -134 53t-51 147q0 120 70 213.5t184 93.5zM302 402q-69 0 -114.5 -57.5t-45.5 -135.5q0 -58 33.5 -93t85.5 -35q74 0 127 69l41 187q-17 29 -50.5 47t-76.5 18zM485 556h-64l-57 97
l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="581" 
d="M286 495q108 0 160 -81l15 69h105l-107 -483h-105l15 64q-64 -76 -152 -76q-83 0 -134 53t-51 147q0 120 70 213.5t184 93.5zM302 402q-69 0 -114.5 -57.5t-45.5 -135.5q0 -58 33.5 -93t85.5 -35q74 0 127 69l41 187q-17 29 -50.5 47t-76.5 18zM410 554q-31 0 -50 19.5
t-34 39t-35 19.5q-36 0 -52 -72h-59q28 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q36 0 53 72h58q-28 -134 -119 -134z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="581" 
d="M286 495q108 0 160 -81l15 69h105l-107 -483h-105l15 64q-64 -76 -152 -76q-83 0 -134 53t-51 147q0 120 70 213.5t184 93.5zM302 402q-69 0 -114.5 -57.5t-45.5 -135.5q0 -58 33.5 -93t85.5 -35q74 0 127 69l41 187q-17 29 -50.5 47t-76.5 18zM517 615q0 -26 -20 -45.5
t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM308 615q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="581" 
d="M286 495q108 0 160 -81l15 69h105l-107 -483h-105l15 64q-64 -76 -152 -76q-83 0 -134 53t-51 147q0 120 70 213.5t184 93.5zM302 402q-69 0 -114.5 -57.5t-45.5 -135.5q0 -58 33.5 -93t85.5 -35q74 0 127 69l41 187q-17 29 -50.5 47t-76.5 18zM352 543q-41 0 -67 26
t-26 67q0 46 33.5 79t78.5 33q41 0 67 -26t26 -67q0 -45 -33 -78.5t-79 -33.5zM356 594q23 0 40 16.5t17 39.5q0 21 -12.5 34t-33.5 13q-23 0 -40 -16.5t-17 -39.5q0 -20 13 -33.5t33 -13.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="924" 
d="M657 -12q-134 0 -177 107l-21 -95h-105l15 64q-64 -76 -152 -76q-83 0 -134 53t-51 147q0 120 70 213.5t184 93.5q108 0 160 -81l15 69h105l-15 -67q69 79 158 79q87 0 134 -54t47 -152q0 -45 -10 -82h-370q-1 -3 -1 -16q0 -46 38.5 -80.5t107.5 -34.5q76 0 126 41
l34 -76q-76 -53 -158 -53zM302 402q-69 0 -114.5 -57.5t-45.5 -135.5q0 -58 33.5 -93t85.5 -35q74 0 127 69l41 187q-17 29 -50.5 47t-76.5 18zM795 282q0 2 0.5 7t0.5 6q0 50 -31.5 82t-92.5 32q-56 0 -99 -37.5t-52 -89.5h274z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="498" 
d="M211 -194q-75 0 -115 40l31 43q35 -35 85 -35q26 0 40 11t14 29q0 26 -32 26q-20 0 -32 -15l-36 23l38 66q-79 15 -125 71.5t-46 140.5q0 120 81 204.5t197 84.5q133 0 189 -93l-79 -60q-34 60 -110 60t-123 -56t-47 -135q0 -61 37.5 -95.5t95.5 -34.5q67 0 111 53
l57 -70q-72 -76 -178 -76h-8l-25 -41q14 11 32 11q25 0 41.5 -16.5t16.5 -44.5q0 -41 -31 -66t-79 -25z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="557" 
d="M277 -12q-112 0 -178 58.5t-66 157.5q0 120 79.5 205.5t196.5 85.5q95 0 154.5 -58t59.5 -155q0 -38 -10 -75h-373q-1 -3 -1 -16q0 -46 38.5 -81.5t107.5 -35.5q79 0 129 41l34 -74q-76 -53 -171 -53zM426 282h2q0 2 0.5 7t0.5 6q0 50 -33 82t-94 32q-56 0 -99 -37.5
t-52 -89.5h275zM428 556h-75l-133 144h100z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="557" 
d="M277 -12q-112 0 -178 58.5t-66 157.5q0 120 79.5 205.5t196.5 85.5q95 0 154.5 -58t59.5 -155q0 -38 -10 -75h-373q-1 -3 -1 -16q0 -46 38.5 -81.5t107.5 -35.5q79 0 129 41l34 -74q-76 -53 -171 -53zM426 282h2q0 2 0.5 7t0.5 6q0 50 -33 82t-94 32q-56 0 -99 -37.5
t-52 -89.5h275zM551 700l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="557" 
d="M277 -12q-112 0 -178 58.5t-66 157.5q0 120 79.5 205.5t196.5 85.5q95 0 154.5 -58t59.5 -155q0 -38 -10 -75h-373q-1 -3 -1 -16q0 -46 38.5 -81.5t107.5 -35.5q79 0 129 41l34 -74q-76 -53 -171 -53zM426 282h2q0 2 0.5 7t0.5 6q0 50 -33 82t-94 32q-56 0 -99 -37.5
t-52 -89.5h275zM497 556h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="557" 
d="M277 -12q-112 0 -178 58.5t-66 157.5q0 120 79.5 205.5t196.5 85.5q95 0 154.5 -58t59.5 -155q0 -38 -10 -75h-373q-1 -3 -1 -16q0 -46 38.5 -81.5t107.5 -35.5q79 0 129 41l34 -74q-76 -53 -171 -53zM426 282h2q0 2 0.5 7t0.5 6q0 50 -33 82t-94 32q-56 0 -99 -37.5
t-52 -89.5h275zM529 615q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM320 615q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="241" 
d="M264 556h-75l-133 144h100zM119 0h-105l107 483h105z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="241" 
d="M387 700l-197 -144h-77l172 144h102zM119 0h-105l107 483h105z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="241" 
d="M332 556h-64l-57 97l-95 -97h-69l125 144h96zM119 0h-105l107 483h105z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="241" 
d="M365 615q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM156 615q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM119 0h-105l107 483h105z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="574" 
d="M164 496l-11 52l125 37q-24 22 -58 47l74 78q57 -44 102 -90l114 34l11 -51l-86 -26q109 -132 109 -272q0 -133 -76.5 -225t-200.5 -92q-106 0 -171 58t-65 151q0 113 71.5 192t174.5 79q56 0 99.5 -27t66.5 -80q-24 87 -120 183zM269 81q66 0 111.5 51t45.5 124
q0 49 -34 84.5t-97 35.5q-65 0 -110 -50t-45 -122q0 -56 34 -89.5t95 -33.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="568" 
d="M451 0h-105l67 303q4 20 4 31q0 68 -90 68q-65 0 -128 -65l-75 -337h-105l107 483h105l-15 -65q81 77 165 77q68 0 108.5 -32.5t40.5 -88.5q0 -19 -6 -41zM424 554q-31 0 -50 19.5t-34 39t-35 19.5q-36 0 -52 -72h-59q28 134 118 134q24 0 42 -12t26.5 -27t22 -27
t29.5 -12q36 0 53 72h58q-28 -134 -119 -134z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="574" 
d="M264 -12q-108 0 -169.5 60.5t-61.5 157.5q0 116 79 202.5t196 86.5q108 0 169.5 -60t61.5 -157q0 -116 -79 -203t-196 -87zM268 81q69 0 115.5 58t46.5 134q0 60 -33.5 94.5t-92.5 34.5q-69 0 -115.5 -57.5t-46.5 -134.5q0 -60 33.5 -94.5t92.5 -34.5zM431 556h-75
l-133 144h100z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="574" 
d="M264 -12q-108 0 -169.5 60.5t-61.5 157.5q0 116 79 202.5t196 86.5q108 0 169.5 -60t61.5 -157q0 -116 -79 -203t-196 -87zM268 81q69 0 115.5 58t46.5 134q0 60 -33.5 94.5t-92.5 34.5q-69 0 -115.5 -57.5t-46.5 -134.5q0 -60 33.5 -94.5t92.5 -34.5zM553 700l-197 -144
h-77l172 144h102z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="574" 
d="M264 -12q-108 0 -169.5 60.5t-61.5 157.5q0 116 79 202.5t196 86.5q108 0 169.5 -60t61.5 -157q0 -116 -79 -203t-196 -87zM268 81q69 0 115.5 58t46.5 134q0 60 -33.5 94.5t-92.5 34.5q-69 0 -115.5 -57.5t-46.5 -134.5q0 -60 33.5 -94.5t92.5 -34.5zM500 556h-64
l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="574" 
d="M264 -12q-108 0 -169.5 60.5t-61.5 157.5q0 116 79 202.5t196 86.5q108 0 169.5 -60t61.5 -157q0 -116 -79 -203t-196 -87zM268 81q69 0 115.5 58t46.5 134q0 60 -33.5 94.5t-92.5 34.5q-69 0 -115.5 -57.5t-46.5 -134.5q0 -60 33.5 -94.5t92.5 -34.5zM427 554
q-31 0 -50 19.5t-34 39t-35 19.5q-36 0 -52 -72h-59q28 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q36 0 53 72h58q-28 -134 -119 -134z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="574" 
d="M264 -12q-108 0 -169.5 60.5t-61.5 157.5q0 116 79 202.5t196 86.5q108 0 169.5 -60t61.5 -157q0 -116 -79 -203t-196 -87zM268 81q69 0 115.5 58t46.5 134q0 60 -33.5 94.5t-92.5 34.5q-69 0 -115.5 -57.5t-46.5 -134.5q0 -60 33.5 -94.5t92.5 -34.5zM531 615
q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM322 615q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="511" 
d="M495 304h-453l15 67h453zM230 98q-21 0 -35.5 14.5t-14.5 35.5q0 24 17.5 41t42.5 17q20 0 34.5 -14.5t14.5 -35.5q0 -24 -17.5 -41t-41.5 -17zM311 466q-20 0 -34.5 14.5t-14.5 35.5q0 24 17.5 41t41.5 17q21 0 35.5 -14.5t14.5 -35.5q0 -24 -18 -41t-42 -17z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="574" 
d="M67 0h-73l78 74q-39 54 -39 132q0 116 79 202.5t196 86.5q93 0 155 -48l37 36h73l-75 -72q41 -57 41 -133q0 -116 -79 -203t-196 -87q-96 0 -158 49zM268 81q69 0 115.5 58t46.5 134q0 35 -12 62l-236 -226q33 -28 86 -28zM142 210q0 -35 10 -59l235 224q-33 27 -83 27
q-69 0 -115.5 -57.5t-46.5 -134.5z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="567" 
d="M121 483h105l-67 -302q-5 -25 -5 -31q0 -35 24.5 -52t65.5 -17q65 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40zM430 556h-75l-133 144h100z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="567" 
d="M121 483h105l-67 -302q-5 -25 -5 -31q0 -35 24.5 -52t65.5 -17q65 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40zM551 700l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="567" 
d="M121 483h105l-67 -302q-5 -25 -5 -31q0 -35 24.5 -52t65.5 -17q65 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40zM496 556h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="567" 
d="M121 483h105l-67 -302q-5 -25 -5 -31q0 -35 24.5 -52t65.5 -17q65 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40zM531 615q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15
t15 -37zM322 615q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="503" 
d="M-47 -185l35 92q18 -8 49 -8q37 0 65 40l38 58l-89 486h109l60 -362l222 362h116l-360 -570q-36 -58 -76.5 -83.5t-94.5 -25.5q-43 0 -74 11zM518 700l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="579" 
d="M294 -12q-51 0 -93.5 21.5t-66.5 59.5l-56 -253h-105l188 851h105l-54 -248q62 76 151 76q84 0 134.5 -52.5t50.5 -146.5q0 -77 -29 -146.5t-88.5 -115.5t-136.5 -46zM278 81q69 0 114.5 58t45.5 136q0 58 -33 92.5t-86 34.5q-37 0 -70.5 -19t-55.5 -49l-42 -187
q17 -29 50.5 -47.5t76.5 -18.5z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="503" 
d="M-47 -185l35 92q18 -8 49 -8q37 0 65 40l38 58l-89 486h109l60 -362l222 362h116l-360 -570q-36 -58 -76.5 -83.5t-94.5 -25.5q-43 0 -74 11zM496 615q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM287 615q0 -26 -20 -45.5
t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="673" 
d="M618 0h-127l-21 129h-306l-77 -129h-140l410 667h146zM460 232l-50 318l-190 -318h240zM628 748h-362l14 62h362z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="581" 
d="M286 495q108 0 160 -81l15 69h105l-107 -483h-105l15 64q-64 -76 -152 -76q-83 0 -134 53t-51 147q0 120 70 213.5t184 93.5zM302 402q-69 0 -114.5 -57.5t-45.5 -135.5q0 -58 33.5 -93t85.5 -35q74 0 127 69l41 187q-17 29 -50.5 47t-76.5 18zM525 576h-362l14 62h362z
" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="673" 
d="M618 0h-127l-21 129h-306l-77 -129h-140l410 667h146zM460 232l-50 318l-190 -318h240zM636 806q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q34 -67 113 -67q80 0 142 67z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="581" 
d="M286 495q108 0 160 -81l15 69h105l-107 -483h-105l15 64q-64 -76 -152 -76q-83 0 -134 53t-51 147q0 120 70 213.5t184 93.5zM302 402q-69 0 -114.5 -57.5t-45.5 -135.5q0 -58 33.5 -93t85.5 -35q74 0 127 69l41 187q-17 29 -50.5 47t-76.5 18zM534 647q-85 -88 -194 -88
q-53 0 -94 23.5t-63 64.5l55 41q34 -67 113 -67q80 0 142 67z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="673" 
d="M610 -102l38 -31q-39 -53 -93 -53q-39 0 -64 19.5t-25 54.5q0 66 65 112h-40l-21 129h-306l-77 -129h-140l410 667h146l115 -667q-98 -43 -98 -103q0 -17 10.5 -26.5t28.5 -9.5q30 0 51 37zM460 232l-50 318l-190 -318h240z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="581" 
d="M451 -102l38 -31q-39 -53 -93 -53q-39 0 -64 19.5t-25 54.5q0 66 65 112h-18l15 64q-64 -76 -152 -76q-83 0 -134 53t-51 147q0 120 70 213.5t184 93.5q108 0 160 -81l15 69h105l-107 -483q-98 -43 -98 -103q0 -17 10.5 -26.5t28.5 -9.5q30 0 51 37zM302 402
q-69 0 -114.5 -57.5t-45.5 -135.5q0 -58 33.5 -93t85.5 -35q74 0 127 69l41 187q-17 29 -50.5 47t-76.5 18z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="682" 
d="M371 -12q-139 0 -229.5 81.5t-90.5 214.5q0 173 113 283.5t270 110.5q106 0 178.5 -48t107.5 -127l-112 -40q-23 55 -71 83t-109 28q-103 0 -178.5 -80.5t-75.5 -202.5q0 -88 55.5 -143.5t147.5 -55.5q96 0 167 77l89 -63q-52 -61 -122 -89.5t-140 -28.5zM695 872
l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="498" 
d="M264 -12q-104 0 -167.5 59.5t-63.5 158.5q0 120 81 204.5t197 84.5q133 0 189 -93l-79 -60q-34 60 -110 60t-123 -56t-47 -135q0 -61 37.5 -95.5t95.5 -34.5q67 0 111 53l57 -70q-72 -76 -178 -76zM560 700l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="682" 
d="M371 -12q-139 0 -229.5 81.5t-90.5 214.5q0 173 113 283.5t270 110.5q106 0 178.5 -48t107.5 -127l-112 -40q-23 55 -71 83t-109 28q-103 0 -178.5 -80.5t-75.5 -202.5q0 -88 55.5 -143.5t147.5 -55.5q96 0 167 77l89 -63q-52 -61 -122 -89.5t-140 -28.5zM641 728h-64
l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="498" 
d="M264 -12q-104 0 -167.5 59.5t-63.5 158.5q0 120 81 204.5t197 84.5q133 0 189 -93l-79 -60q-34 60 -110 60t-123 -56t-47 -135q0 -61 37.5 -95.5t95.5 -34.5q67 0 111 53l57 -70q-72 -76 -178 -76zM506 556h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="682" 
d="M371 -12q-139 0 -229.5 81.5t-90.5 214.5q0 173 113 283.5t270 110.5q106 0 178.5 -48t107.5 -127l-112 -40q-23 55 -71 83t-109 28q-103 0 -178.5 -80.5t-75.5 -202.5q0 -88 55.5 -143.5t147.5 -55.5q96 0 167 77l89 -63q-52 -61 -122 -89.5t-140 -28.5zM572 787
q0 -26 -20.5 -46t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5t46 19.5q23 0 38 -15t15 -37z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="498" 
d="M264 -12q-104 0 -167.5 59.5t-63.5 158.5q0 120 81 204.5t197 84.5q133 0 189 -93l-79 -60q-34 60 -110 60t-123 -56t-47 -135q0 -61 37.5 -95.5t95.5 -34.5q67 0 111 53l57 -70q-72 -76 -178 -76zM432 626q0 -26 -20.5 -46t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5
t46 19.5q23 0 38 -15t15 -37z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="682" 
d="M371 -12q-139 0 -229.5 81.5t-90.5 214.5q0 173 113 283.5t270 110.5q106 0 178.5 -48t107.5 -127l-112 -40q-23 55 -71 83t-109 28q-103 0 -178.5 -80.5t-75.5 -202.5q0 -88 55.5 -143.5t147.5 -55.5q96 0 167 77l89 -63q-52 -61 -122 -89.5t-140 -28.5zM548 728h-96
l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="498" 
d="M264 -12q-104 0 -167.5 59.5t-63.5 158.5q0 120 81 204.5t197 84.5q133 0 189 -93l-79 -60q-34 60 -110 60t-123 -56t-47 -135q0 -61 37.5 -95.5t95.5 -34.5q67 0 111 53l57 -70q-72 -76 -178 -76zM413 556h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="710" 
d="M264 0h-247l147 667h243q115 0 199.5 -81t84.5 -204q0 -70 -25 -135.5t-74 -122t-134 -90.5t-194 -34zM259 564l-102 -461h128q128 0 206.5 79t78.5 192q0 82 -52.5 136t-131.5 54h-127zM514 728h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="671" 
d="M286 495q108 0 160 -81l56 253h105l-148 -667h-105l15 64q-64 -76 -152 -76q-83 0 -134 53t-51 147q0 120 70 213.5t184 93.5zM302 402q-69 0 -114.5 -57.5t-45.5 -135.5q0 -58 33.5 -93t85.5 -35q74 0 127 69l41 187q-17 29 -50.5 47t-76.5 18zM755 622q0 -43 -32 -87
t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="736" 
d="M290 0h-247l63 287h-87l21 94h87l63 286h243q115 0 199.5 -81t84.5 -204q0 -70 -25 -135.5t-74 -122t-134 -90.5t-194 -34zM314 103q127 1 204.5 79.5t77.5 191.5q0 82 -52.5 136t-131.5 54h-127l-41 -183h158l-21 -94h-158l-40 -184h131z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="586" 
d="M286 495q108 0 160 -81l26 117h-159l13 62h159l17 74h105l-17 -74h52l-12 -62h-53l-118 -531h-105l15 64q-64 -76 -152 -76q-83 0 -134 53t-51 147q0 120 70 213.5t184 93.5zM302 402q-69 0 -114.5 -57.5t-45.5 -135.5q0 -58 33.5 -93t85.5 -35q74 0 127 69l41 187
q-17 29 -50.5 47t-76.5 18z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="577" 
d="M474 0h-457l147 667h457l-22 -103h-340l-38 -172h333l-23 -103h-333l-41 -186h340zM589 748h-362l14 62h362z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="557" 
d="M277 -12q-112 0 -178 58.5t-66 157.5q0 120 79.5 205.5t196.5 85.5q95 0 154.5 -58t59.5 -155q0 -38 -10 -75h-373q-1 -3 -1 -16q0 -46 38.5 -81.5t107.5 -35.5q79 0 129 41l34 -74q-76 -53 -171 -53zM426 282h2q0 2 0.5 7t0.5 6q0 50 -33 82t-94 32q-56 0 -99 -37.5
t-52 -89.5h275zM534 576h-362l14 62h362z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="577" 
d="M474 0h-457l147 667h457l-22 -103h-340l-38 -172h333l-23 -103h-333l-41 -186h340zM596 806q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q34 -67 113 -67q80 0 142 67z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="557" 
d="M277 -12q-112 0 -178 58.5t-66 157.5q0 120 79.5 205.5t196.5 85.5q95 0 154.5 -58t59.5 -155q0 -38 -10 -75h-373q-1 -3 -1 -16q0 -46 38.5 -81.5t107.5 -35.5q79 0 129 41l34 -74q-76 -53 -171 -53zM426 282h2q0 2 0.5 7t0.5 6q0 50 -33 82t-94 32q-56 0 -99 -37.5
t-52 -89.5h275zM550 647q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q34 -67 113 -67q80 0 142 67z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="577" 
d="M474 0h-457l147 667h457l-22 -103h-340l-38 -172h333l-23 -103h-333l-41 -186h340zM481 787q0 -26 -20.5 -46t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5t46 19.5q23 0 38 -15t15 -37z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="557" 
d="M277 -12q-112 0 -178 58.5t-66 157.5q0 120 79.5 205.5t196.5 85.5q95 0 154.5 -58t59.5 -155q0 -38 -10 -75h-373q-1 -3 -1 -16q0 -46 38.5 -81.5t107.5 -35.5q79 0 129 41l34 -74q-76 -53 -171 -53zM426 282h2q0 2 0.5 7t0.5 6q0 50 -33 82t-94 32q-56 0 -99 -37.5
t-52 -89.5h275zM428 626q0 -26 -20.5 -46t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5t46 19.5q23 0 38 -15t15 -37z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="577" 
d="M466 -102l38 -31q-39 -53 -93 -53q-39 0 -64 19.5t-25 54.5q0 66 65 112h-370l147 667h457l-22 -103h-340l-38 -172h333l-23 -103h-333l-41 -186h340l-23 -103q-98 -43 -98 -103q0 -17 10.5 -26.5t28.5 -9.5q30 0 51 37z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="557" 
d="M352 -102l38 -31q-39 -53 -93 -53q-39 0 -64 19.5t-25 54.5q0 57 50 101q-104 5 -164.5 62.5t-60.5 152.5q0 120 79.5 205.5t196.5 85.5q95 0 154.5 -58t59.5 -155q0 -38 -10 -75h-373q-1 -3 -1 -16q0 -46 38.5 -81.5t107.5 -35.5q79 0 129 41l34 -74q-37 -27 -88 -41
q-98 -43 -98 -103q0 -17 10.5 -26.5t28.5 -9.5q30 0 51 37zM428 282q0 2 0.5 7t0.5 6q0 50 -33 82t-94 32q-56 0 -99 -37.5t-52 -89.5h277z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="577" 
d="M474 0h-457l147 667h457l-22 -103h-340l-38 -172h333l-23 -103h-333l-41 -186h340zM457 728h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="557" 
d="M277 -12q-112 0 -178 58.5t-66 157.5q0 120 79.5 205.5t196.5 85.5q95 0 154.5 -58t59.5 -155q0 -38 -10 -75h-373q-1 -3 -1 -16q0 -46 38.5 -81.5t107.5 -35.5q79 0 129 41l34 -74q-76 -53 -171 -53zM426 282h2q0 2 0.5 7t0.5 6q0 50 -33 82t-94 32q-56 0 -99 -37.5
t-52 -89.5h275zM402 556h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="717" 
d="M371 -12q-139 0 -229.5 81.5t-90.5 214.5q0 170 113 282t280 112q96 0 165.5 -44t103.5 -113l-106 -46q-21 45 -70 72t-109 27q-103 0 -178.5 -81.5t-75.5 -201.5q0 -87 55.5 -143t147.5 -56q84 0 152 59l23 105h-200l23 103h316l-56 -253q-107 -118 -264 -118zM644 728
h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="581" 
d="M212 -196q-151 0 -226 83l63 70q48 -67 161 -67q118 0 148 135l12 53q-61 -77 -155 -77q-79 0 -131 49.5t-52 147.5q0 119 69.5 208t181.5 89q47 0 91 -22t71 -59l16 69h105l-102 -458q-50 -221 -252 -221zM262 94q35 0 68.5 18.5t57.5 46.5l39 175q-18 31 -53.5 49.5
t-78.5 18.5q-67 0 -110 -54.5t-43 -130.5q0 -56 33 -89.5t87 -33.5zM507 548h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="717" 
d="M371 -12q-139 0 -229.5 81.5t-90.5 214.5q0 170 113 282t280 112q96 0 165.5 -44t103.5 -113l-106 -46q-21 45 -70 72t-109 27q-103 0 -178.5 -81.5t-75.5 -201.5q0 -87 55.5 -143t147.5 -56q84 0 152 59l23 105h-200l23 103h316l-56 -253q-107 -118 -264 -118zM693 806
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q33 -67 113 -67t142 67z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="581" 
d="M212 -196q-151 0 -226 83l63 70q48 -67 161 -67q118 0 148 135l12 53q-61 -77 -155 -77q-79 0 -131 49.5t-52 147.5q0 119 69.5 208t181.5 89q47 0 91 -22t71 -59l16 69h105l-102 -458q-50 -221 -252 -221zM262 94q35 0 68.5 18.5t57.5 46.5l39 175q-18 31 -53.5 49.5
t-78.5 18.5q-67 0 -110 -54.5t-43 -130.5q0 -56 33 -89.5t87 -33.5zM553 647q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q33 -67 113 -67t142 67z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="717" 
d="M371 -12q-139 0 -229.5 81.5t-90.5 214.5q0 170 113 282t280 112q96 0 165.5 -44t103.5 -113l-106 -46q-21 45 -70 72t-109 27q-103 0 -178.5 -81.5t-75.5 -201.5q0 -87 55.5 -143t147.5 -56q84 0 152 59l23 105h-200l23 103h316l-56 -253q-107 -118 -264 -118zM572 787
q0 -26 -20.5 -46t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5t46 19.5q23 0 38 -15t15 -37z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="581" 
d="M212 -196q-151 0 -226 83l63 70q48 -67 161 -67q118 0 148 135l12 53q-61 -77 -155 -77q-79 0 -131 49.5t-52 147.5q0 119 69.5 208t181.5 89q47 0 91 -22t71 -59l16 69h105l-102 -458q-50 -221 -252 -221zM262 94q35 0 68.5 18.5t57.5 46.5l39 175q-18 31 -53.5 49.5
t-78.5 18.5q-67 0 -110 -54.5t-43 -130.5q0 -56 33 -89.5t87 -33.5zM437 626q0 -26 -20.5 -46t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5t46 19.5q23 0 38 -15t15 -37z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="717" 
d="M371 -12q-139 0 -229.5 81.5t-90.5 214.5q0 170 113 282t280 112q96 0 165.5 -44t103.5 -113l-106 -46q-21 45 -70 72t-109 27q-103 0 -178.5 -81.5t-75.5 -201.5q0 -87 55.5 -143t147.5 -56q84 0 152 59l23 105h-200l23 103h316l-56 -253q-107 -118 -264 -118zM372 -121
q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="581" 
d="M307 616q0 44 33 88.5t80 67.5l32 -33q-26 -13 -48.5 -33t-30.5 -41h8q18 0 31 -12.5t13 -34.5q0 -24 -19.5 -42.5t-43.5 -18.5q-23 0 -39 16t-16 43zM212 -196q-151 0 -226 83l63 70q48 -67 161 -67q118 0 148 135l12 53q-61 -77 -155 -77q-79 0 -131 49.5t-52 147.5
q0 119 69.5 208t181.5 89q47 0 91 -22t71 -59l16 69h105l-102 -458q-50 -221 -252 -221zM262 94q35 0 68.5 18.5t57.5 46.5l39 175q-18 31 -53.5 49.5t-78.5 18.5q-67 0 -110 -54.5t-43 -130.5q0 -56 33 -89.5t87 -33.5z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="723" 
d="M598 0h-117l64 292h-347l-64 -292h-117l147 667h117l-60 -272h347l60 272h117zM610 728h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="568" 
d="M446 0h-105l67 304q4 16 4 30q0 68 -90 68q-63 0 -128 -65l-74 -337h-105l147 667h105l-56 -249q81 77 165 77q68 0 108.5 -32.5t40.5 -88.5q0 -21 -6 -41zM373 728h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="741" 
d="M608 0h-117l64 292h-347l-64 -292h-117l110 501h-70l13 62h71l23 104h117l-23 -104h347l23 104h117l-23 -104h68l-13 -62h-69zM231 395h347l23 106h-347z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="572" 
d="M450 0h-105l67 304q4 16 4 30q0 68 -90 68q-63 0 -128 -65l-74 -337h-105l117 531h-63l14 62h62l17 74h105l-17 -74h150l-14 -62h-150l-25 -113q81 77 165 77q68 0 108.5 -32.5t40.5 -88.5q0 -21 -6 -41z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="259" 
d="M134 0h-117l147 667h117zM308 726q-31 0 -50 19.5t-34 39t-35 19.5q-36 0 -52 -72h-59q28 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q36 0 53 72h58q-28 -134 -119 -134z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="241" 
d="M119 0h-105l107 483h105zM260 539q-31 0 -50 19.5t-34 39t-35 19.5q-36 0 -52 -72h-59q28 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q36 0 53 72h58q-28 -134 -119 -134z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="259" 
d="M134 0h-117l147 667h117zM422 748h-362l14 62h362z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="241" 
d="M119 0h-105l107 483h105zM375 576h-362l14 62h362z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="259" 
d="M134 0h-117l147 667h117zM431 806q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q33 -67 113 -67t142 67z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="241" 
d="M119 0h-105l107 483h105zM389 647q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q33 -67 113 -67t142 67z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="259" 
d="M126 -102l38 -31q-39 -53 -93 -53q-39 0 -64 19.5t-25 54.5q0 66 65 112h-30l147 667h117l-147 -667q-98 -43 -98 -103q0 -17 10.5 -26.5t28.5 -9.5q30 0 51 37z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="241" 
d="M111 -102l38 -31q-39 -53 -93 -53q-39 0 -64 19.5t-25 54.5q0 66 65 112h-18l107 483h105l-107 -483q-98 -43 -98 -103q0 -17 10.5 -26.5t28.5 -9.5q30 0 51 37zM193 542q-25 0 -42 16.5t-17 39.5q0 33 23.5 53.5t50.5 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23.5 -53.5
t-50.5 -20.5z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="259" 
d="M134 0h-117l147 667h117zM309 787q0 -26 -20.5 -46t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5t46 19.5q23 0 38 -15t15 -37z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="241" 
d="M119 0h-105l107 483h105z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="741" 
d="M134 0h-117l147 667h117zM421 -12q-58 0 -108.5 20.5t-79.5 58.5l66 87q37 -62 115 -62q52 0 86 32.5t46 88.5l100 454h117l-101 -455q-50 -224 -241 -224z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="482" 
d="M193 542q-25 0 -42 16.5t-17 39.5q0 33 23.5 53.5t50.5 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23.5 -53.5t-50.5 -20.5zM119 0h-105l107 483h105zM181 -196q-85 0 -126 41l46 75q24 -30 65 -30q64 0 83 82l113 511h105l-113 -511q-18 -82 -59 -125t-114 -43zM434 542
q-25 0 -42 16.5t-17 39.5q0 33 23.5 53.5t50.5 20.5q25 0 42 -16.5t17 -39.5q0 -33 -23.5 -53.5t-50.5 -20.5z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="482" 
d="M162 -12q-58 0 -108.5 20.5t-79.5 58.5l66 87q37 -62 115 -62q52 0 86 32.5t46 88.5l100 454h117l-101 -455q-50 -224 -241 -224zM603 728h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="241" 
d="M-60 -196q-85 0 -126 41l48 82q24 -30 65 -30q63 0 81 75l113 511h105l-113 -511q-18 -82 -59 -125t-114 -43zM334 556h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="630" 
d="M561 0h-138l-164 286l-78 -73l-47 -213h-117l147 667h117l-68 -308l323 308h151l-340 -314zM296 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="527" 
d="M466 0h-126l-106 203l-86 -73l-29 -130h-105l147 667h105l-91 -410l260 226h134l-249 -219zM245 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="527" 
d="M466 0h-126l-106 203l-86 -73l-29 -130h-105l107 483h105l-51 -226l260 226h134l-249 -219z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="506" 
d="M420 0h-411l147 667h117l-124 -564h294zM534 872l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="241" 
d="M119 0h-105l147 667h105zM425 872l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="506" 
d="M420 0h-411l147 667h117l-124 -564h294zM232 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="241" 
d="M119 0h-105l147 667h105zM102 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="506" 
d="M420 0h-411l147 667h117l-124 -564h294zM464 622q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="328" 
d="M119 0h-105l147 667h105zM426 622q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="537" 
d="M420 0h-411l147 667h117l-124 -564h294zM408 273q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="351" 
d="M119 0h-105l147 667h105zM290 175q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="530" 
d="M1 205l21 97l88 45l71 320h117l-56 -252l96 49l-21 -98l-96 -49l-47 -214h294l-23 -103h-411l55 250z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="299" 
d="M2 214l18 79l98 50l72 324h105l-58 -264l99 51l-17 -78l-100 -51l-71 -325h-105l58 265z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="720" 
d="M595 0h-113l-241 483l-107 -483h-117l147 667h120l237 -468l104 468h117zM664 872l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="568" 
d="M451 0h-105l67 303q4 20 4 31q0 68 -90 68q-65 0 -128 -65l-75 -337h-105l107 483h105l-15 -65q81 77 165 77q68 0 108.5 -32.5t40.5 -88.5q0 -19 -6 -41zM550 700l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="720" 
d="M595 0h-113l-241 483l-107 -483h-117l147 667h120l237 -468l104 468h117zM341 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="568" 
d="M451 0h-105l67 303q4 20 4 31q0 68 -90 68q-65 0 -128 -65l-75 -337h-105l107 483h105l-15 -65q81 77 165 77q68 0 108.5 -32.5t40.5 -88.5q0 -19 -6 -41zM265 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18
q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="720" 
d="M595 0h-113l-241 483l-107 -483h-117l147 667h120l237 -468l104 468h117zM516 728h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="568" 
d="M451 0h-105l67 303q4 20 4 31q0 68 -90 68q-65 0 -128 -65l-75 -337h-105l107 483h105l-15 -65q81 77 165 77q68 0 108.5 -32.5t40.5 -88.5q0 -19 -6 -41zM402 556h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="568" 
d="M451 0h-105l67 303q4 20 4 31q0 68 -90 68q-65 0 -128 -65l-75 -337h-105l107 483h105l-15 -65q81 77 165 77q68 0 108.5 -32.5t40.5 -88.5q0 -19 -6 -41zM266 697q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18
q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="720" 
d="M360 -196q-128 0 -188 79l63 77q16 -28 46.5 -45t68.5 -17q97 0 129 108l-238 477l-107 -483h-117l147 667h120l237 -468l104 468h117l-142 -642q-49 -221 -240 -221z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="568" 
d="M267 -196q-85 0 -126 41l48 82q27 -30 65 -30q64 0 80 75l74 331q4 24 4 30q0 69 -90 69q-65 0 -128 -65l-75 -337h-105l107 483h105l-15 -65q81 77 165 77q66 0 107.5 -31.5t41.5 -90.5q0 -12 -6 -40l-80 -361q-37 -168 -172 -168z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="765" 
d="M371 -12q-138 0 -229 80.5t-91 215.5q0 166 111 280t272 114q137 0 228 -80t91 -215q0 -165 -110.5 -280t-271.5 -115zM377 92q105 0 179 81.5t74 201.5q0 92 -57.5 145.5t-146.5 53.5q-105 0 -178.5 -81.5t-73.5 -201.5q0 -91 57.5 -145t145.5 -54zM674 748h-362l14 62
h362z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="574" 
d="M264 -12q-108 0 -169.5 60.5t-61.5 157.5q0 116 79 202.5t196 86.5q108 0 169.5 -60t61.5 -157q0 -116 -79 -203t-196 -87zM268 81q69 0 115.5 58t46.5 134q0 60 -33.5 94.5t-92.5 34.5q-69 0 -115.5 -57.5t-46.5 -134.5q0 -60 33.5 -94.5t92.5 -34.5zM541 576h-362
l14 62h362z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="765" 
d="M371 -12q-138 0 -229 80.5t-91 215.5q0 166 111 280t272 114q137 0 228 -80t91 -215q0 -165 -110.5 -280t-271.5 -115zM377 92q105 0 179 81.5t74 201.5q0 92 -57.5 145.5t-146.5 53.5q-105 0 -178.5 -81.5t-73.5 -201.5q0 -91 57.5 -145t145.5 -54zM681 806
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q33 -67 113 -67t142 67z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="574" 
d="M264 -12q-108 0 -169.5 60.5t-61.5 157.5q0 116 79 202.5t196 86.5q108 0 169.5 -60t61.5 -157q0 -116 -79 -203t-196 -87zM268 81q69 0 115.5 58t46.5 134q0 60 -33.5 94.5t-92.5 34.5q-69 0 -115.5 -57.5t-46.5 -134.5q0 -60 33.5 -94.5t92.5 -34.5zM551 647
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q33 -67 113 -67t142 67z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="765" 
d="M371 -12q-138 0 -229 80.5t-91 215.5q0 166 111 280t272 114q137 0 228 -80t91 -215q0 -165 -110.5 -280t-271.5 -115zM377 92q105 0 179 81.5t74 201.5q0 92 -57.5 145.5t-146.5 53.5q-105 0 -178.5 -81.5t-73.5 -201.5q0 -91 57.5 -145t145.5 -54zM582 872l-147 -144
h-65l122 144h90zM728 872l-147 -144h-65l122 144h90z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="574" 
d="M264 -12q-108 0 -169.5 60.5t-61.5 157.5q0 116 79 202.5t196 86.5q108 0 169.5 -60t61.5 -157q0 -116 -79 -203t-196 -87zM268 81q69 0 115.5 58t46.5 134q0 60 -33.5 94.5t-92.5 34.5q-69 0 -115.5 -57.5t-46.5 -134.5q0 -60 33.5 -94.5t92.5 -34.5zM449 700l-147 -144
h-65l122 144h90zM595 700l-147 -144h-65l122 144h90z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1093" 
d="M992 0h-457l18 87q-81 -99 -222 -99q-120 0 -200 83.5t-80 212.5q0 86 30.5 160t82 124.5t120.5 79.5t144 29q168 0 227 -131l27 121h457l-23 -103h-340l-38 -172h333l-23 -103h-333l-41 -186h340zM581 212l47 210q-16 72 -67.5 111.5t-127.5 39.5q-110 0 -184.5 -81.5
t-74.5 -200.5q0 -90 55 -144.5t141 -54.5q64 0 120 32t91 88z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="945" 
d="M902 207h-369q-1 -2 -1 -16q0 -47 38.5 -82t105.5 -35q76 0 127 41l34 -74q-72 -53 -169 -53q-78 0 -131.5 37.5t-69.5 82.5q-18 -22 -26.5 -32t-30.5 -30.5t-40.5 -30.5t-47.5 -18.5t-60 -8.5q-103 0 -166 58.5t-63 159.5q0 114 77 201.5t196 87.5q74 0 123 -37.5
t66 -82.5q18 24 34 40.5t42 37t58.5 31.5t70.5 11q92 0 152 -56.5t60 -156.5q0 -37 -10 -75zM544 282h273q0 1 0.5 5.5t0.5 7.5q0 49 -32 81.5t-93 32.5q-58 0 -100 -40t-49 -87zM426 273q0 65 -35 97t-89 32q-66 0 -113 -56.5t-47 -135.5q0 -60 32.5 -94.5t91.5 -34.5
q68 0 114 57t46 135z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="629" 
d="M534 0h-130l-93 249h-122l-55 -249h-117l147 667h268q81 0 140 -50t59 -132q0 -90 -56.5 -153.5t-145.5 -74.5zM371 352h1q63 0 100 34.5t37 87.5q0 39 -29 64.5t-68 25.5h-153l-47 -212h159zM620 872l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="347" 
d="M119 0h-105l107 483h105l-15 -67q71 79 175 79l-24 -106q-17 5 -42 5q-36 0 -71 -19t-58 -48zM464 700l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="629" 
d="M534 0h-130l-93 249h-122l-55 -249h-117l147 667h268q81 0 140 -50t59 -132q0 -90 -56.5 -153.5t-145.5 -74.5zM371 352h1q63 0 100 34.5t37 87.5q0 39 -29 64.5t-68 25.5h-153l-47 -212h159zM297 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8
q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="347" 
d="M119 0h-105l107 483h105l-15 -67q71 79 175 79l-24 -106q-17 5 -42 5q-36 0 -71 -19t-58 -48zM155 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="629" 
d="M534 0h-130l-93 249h-122l-55 -249h-117l147 667h268q81 0 140 -50t59 -132q0 -90 -56.5 -153.5t-145.5 -74.5zM371 352h1q63 0 100 34.5t37 87.5q0 39 -29 64.5t-68 25.5h-153l-47 -212h159zM473 728h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="347" 
d="M119 0h-105l107 483h105l-15 -67q71 79 175 79l-24 -106q-17 5 -42 5q-36 0 -71 -19t-58 -48zM316 556h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="594" 
d="M295 -12q-93 0 -171 34t-117 89l74 85q35 -48 94.5 -76t128.5 -28q59 0 90 29.5t31 66.5q0 30 -34 52.5t-82 40t-96 38.5t-82 59.5t-34 91.5q0 84 70 146t181 62q78 0 146 -28.5t109 -78.5l-75 -84q-34 43 -87.5 65.5t-109.5 22.5q-46 0 -78 -25.5t-32 -59.5
q0 -27 34 -48.5t82 -39t96 -39t82 -61t34 -92.5q0 -92 -69 -157t-185 -65zM599 872l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="472" 
d="M214 -12q-140 0 -218 87l62 72q23 -29 69 -52t96 -23q40 0 64 19t24 46q0 21 -25.5 36.5t-62.5 27.5t-74 27t-62.5 43t-25.5 67q0 65 51.5 111t140.5 46q60 0 112.5 -21.5t84.5 -56.5l-57 -70q-17 26 -58 45.5t-85 19.5q-39 0 -61.5 -16t-22.5 -41q0 -23 39 -41.5
t85.5 -32t85.5 -46.5t39 -82q0 -70 -54 -117.5t-147 -47.5zM504 700l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="594" 
d="M501 728h-64l-57 97l-95 -97h-69l125 144h96zM295 -12q-93 0 -171 34t-117 89l74 85q35 -48 94.5 -76t128.5 -28q59 0 90 29.5t31 66.5q0 30 -34 52.5t-82 40t-96 38.5t-82 59.5t-34 91.5q0 84 70 146t181 62q78 0 146 -28.5t109 -78.5l-75 -84q-34 43 -87.5 65.5
t-109.5 22.5q-46 0 -78 -25.5t-32 -59.5q0 -27 34 -48.5t82 -39t96 -39t82 -61t34 -92.5q0 -92 -69 -157t-185 -65z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="472" 
d="M214 -12q-140 0 -218 87l62 72q23 -29 69 -52t96 -23q40 0 64 19t24 46q0 21 -25.5 36.5t-62.5 27.5t-74 27t-62.5 43t-25.5 67q0 65 51.5 111t140.5 46q60 0 112.5 -21.5t84.5 -56.5l-57 -70q-17 26 -58 45.5t-85 19.5q-39 0 -61.5 -16t-22.5 -41q0 -23 39 -41.5
t85.5 -32t85.5 -46.5t39 -82q0 -70 -54 -117.5t-147 -47.5zM445 556h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="594" 
d="M210 -194q-75 0 -115 40l31 43q35 -35 85 -35q26 0 40 11t14 29q0 26 -32 26q-20 0 -32 -15l-36 23l40 69q-138 27 -198 114l74 85q35 -48 94.5 -76t128.5 -28q59 0 90 29.5t31 66.5q0 30 -34 52.5t-82 40t-96 38.5t-82 59.5t-34 91.5q0 84 70 146t181 62q78 0 146 -28.5
t109 -78.5l-75 -84q-34 43 -87.5 65.5t-109.5 22.5q-46 0 -78 -25.5t-32 -59.5q0 -27 34 -48.5t82 -39t96 -39t82 -61t34 -92.5q0 -92 -69 -157t-185 -65q-15 0 -39 2l-26 -43q14 11 32 11q25 0 41.5 -16.5t16.5 -44.5q0 -41 -31 -66t-79 -25z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="472" 
d="M153 -194q-75 0 -115 40l31 43q35 -35 85 -35q26 0 40 11t14 29q0 26 -32 26q-20 0 -32 -15l-36 23l39 66q-95 17 -151 81l62 72q23 -29 69 -52t96 -23q40 0 64 19t24 46q0 21 -25.5 36.5t-62.5 27.5t-74 27t-62.5 43t-25.5 67q0 65 51.5 111t140.5 46q60 0 112.5 -21.5
t84.5 -56.5l-57 -70q-17 26 -58 45.5t-85 19.5q-39 0 -61.5 -16t-22.5 -41q0 -23 39 -41.5t85.5 -32t85.5 -46.5t39 -82q0 -70 -54 -117.5t-147 -47.5h-16l-25 -41q14 11 32 11q25 0 41.5 -16.5t16.5 -44.5q0 -41 -31 -66t-79 -25z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="594" 
d="M446 728h-96l-64 144h65l56 -96l96 96h68zM295 -12q-93 0 -171 34t-117 89l74 85q35 -48 94.5 -76t128.5 -28q59 0 90 29.5t31 66.5q0 30 -34 52.5t-82 40t-96 38.5t-82 59.5t-34 91.5q0 84 70 146t181 62q78 0 146 -28.5t109 -78.5l-75 -84q-34 43 -87.5 65.5
t-109.5 22.5q-46 0 -78 -25.5t-32 -59.5q0 -27 34 -48.5t82 -39t96 -39t82 -61t34 -92.5q0 -92 -69 -157t-185 -65z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="472" 
d="M214 -12q-140 0 -218 87l62 72q23 -29 69 -52t96 -23q40 0 64 19t24 46q0 21 -25.5 36.5t-62.5 27.5t-74 27t-62.5 43t-25.5 67q0 65 51.5 111t140.5 46q60 0 112.5 -21.5t84.5 -56.5l-57 -70q-17 26 -58 45.5t-85 19.5q-39 0 -61.5 -16t-22.5 -41q0 -23 39 -41.5
t85.5 -32t85.5 -46.5t39 -82q0 -70 -54 -117.5t-147 -47.5zM352 556h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="577" 
d="M293 0h-117l125 564h-202l22 103h521l-22 -103h-202zM270 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x21a;" horiz-adv-x="577" 
d="M293 0h-117l125 564h-202l22 103h521l-22 -103h-202zM270 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="319" 
d="M188 -12q-61 0 -95.5 24.5t-34.5 73.5q0 13 3 31l61 275h-80l21 91h80l29 132h105l-29 -132h98l-21 -91h-98l-55 -249q-2 -12 -2 -18q0 -44 48 -44q23 0 40 13l6 -84q-30 -22 -76 -22zM141 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5
t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x21b;" horiz-adv-x="319" 
d="M188 -12q-61 0 -95.5 24.5t-34.5 73.5q0 13 3 31l61 275h-80l21 91h80l29 132h105l-29 -132h98l-21 -91h-98l-55 -249q-2 -12 -2 -18q0 -44 48 -44q23 0 40 13l6 -84q-30 -22 -76 -22zM141 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5
t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="577" 
d="M293 0h-117l125 564h-202l22 103h521l-22 -103h-202zM445 728h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="352" 
d="M188 -12q-61 0 -95.5 24.5t-34.5 73.5q0 13 3 31l61 275h-80l21 91h80l29 132h105l-29 -132h98l-21 -91h-98l-55 -249q-2 -12 -2 -18q0 -44 48 -44q23 0 40 13l6 -84q-30 -22 -76 -22zM465 697q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5
t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="577" 
d="M293 0h-117l61 276h-140l13 62h141l50 226h-202l22 103h521l-22 -103h-202l-50 -226h140l-13 -62h-141z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="319" 
d="M188 -12q-61 0 -95.5 24.5t-34.5 73.5q0 13 3 31l22 100h-80l14 62h80l25 113h-80l21 91h80l29 132h105l-29 -132h98l-21 -91h-98l-25 -113h68l-13 -62h-69l-16 -74q-2 -12 -2 -18q0 -44 48 -44q23 0 40 13l6 -84q-30 -22 -76 -22z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="720" 
d="M348 -12q-133 0 -205.5 61.5t-72.5 166.5q0 20 5 48l89 403h119l-89 -401q-4 -24 -4 -36q1 -60 42.5 -99.5t115.5 -39.5q79 0 123.5 45t63.5 130l88 401h119l-89 -403q-29 -132 -100.5 -204t-204.5 -72zM540 726q-31 0 -50 19.5t-34 39t-35 19.5q-36 0 -52 -72h-59
q28 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q36 0 53 72h58q-28 -134 -119 -134z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="567" 
d="M121 483h105l-67 -302q-5 -25 -5 -31q0 -35 24.5 -52t65.5 -17q65 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40zM423 554q-31 0 -50 19.5t-34 39t-35 19.5q-36 0 -52 -72h-59q28 134 118 134q24 0 42 -12t26.5 -27
t22 -27t29.5 -12q36 0 53 72h58q-28 -134 -119 -134z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="720" 
d="M348 -12q-133 0 -205.5 61.5t-72.5 166.5q0 20 5 48l89 403h119l-89 -401q-4 -24 -4 -36q1 -60 42.5 -99.5t115.5 -39.5q79 0 123.5 45t63.5 130l88 401h119l-89 -403q-29 -132 -100.5 -204t-204.5 -72zM653 748h-362l14 62h362z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="567" 
d="M121 483h105l-67 -302q-5 -25 -5 -31q0 -35 24.5 -52t65.5 -17q65 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40zM538 576h-362l14 62h362z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="720" 
d="M348 -12q-133 0 -205.5 61.5t-72.5 166.5q0 20 5 48l89 403h119l-89 -401q-4 -24 -4 -36q1 -60 42.5 -99.5t115.5 -39.5q79 0 123.5 45t63.5 130l88 401h119l-89 -403q-29 -132 -100.5 -204t-204.5 -72zM662 806q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41
q34 -67 113 -67q80 0 142 67z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="567" 
d="M121 483h105l-67 -302q-5 -25 -5 -31q0 -35 24.5 -52t65.5 -17q65 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40zM550 647q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q34 -67 113 -67q80 0 142 67z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="720" 
d="M348 -12q-133 0 -205.5 61.5t-72.5 166.5q0 20 5 48l89 403h119l-89 -401q-4 -24 -4 -36q1 -60 42.5 -99.5t115.5 -39.5q79 0 123.5 45t63.5 130l88 401h119l-89 -403q-29 -132 -100.5 -204t-204.5 -72zM472 692q-41 0 -67 26t-26 67q0 46 33.5 79t78.5 33q41 0 67 -26
t26 -67q0 -45 -33 -78.5t-79 -33.5zM476 743q23 0 40 16.5t17 39.5q0 21 -12.5 34t-33.5 13q-23 0 -40 -16.5t-17 -39.5q0 -20 13 -33.5t33 -13.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="567" 
d="M121 483h105l-67 -302q-5 -25 -5 -31q0 -35 24.5 -52t65.5 -17q65 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40zM364 550q-41 0 -67 26t-26 67q0 46 33.5 79t78.5 33q41 0 67 -26t26 -67q0 -45 -33 -78.5t-79 -33.5z
M368 601q23 0 40 16.5t17 39.5q0 21 -12.5 34t-33.5 13q-23 0 -40 -16.5t-17 -39.5q0 -20 13 -33.5t33 -13.5z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="720" 
d="M348 -12q-133 0 -205.5 61.5t-72.5 166.5q0 20 5 48l89 403h119l-89 -401q-4 -24 -4 -36q1 -60 42.5 -99.5t115.5 -39.5q79 0 123.5 45t63.5 130l88 401h119l-89 -403q-29 -132 -100.5 -204t-204.5 -72zM561 872l-147 -144h-65l122 144h90zM707 872l-147 -144h-65
l122 144h90z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="567" 
d="M121 483h105l-67 -302q-5 -25 -5 -31q0 -35 24.5 -52t65.5 -17q65 0 129 66l74 336h105l-107 -483h-105l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40zM445 700l-147 -144h-65l122 144h90zM591 700l-147 -144h-65l122 144h90z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="720" 
d="M437 -102l38 -31q-39 -53 -93 -53q-39 0 -64 19.5t-25 54.5q0 57 49 100q-130 1 -201 62.5t-71 165.5q0 20 5 48l89 403h119l-89 -401q-4 -24 -4 -36q1 -60 42.5 -99.5t115.5 -39.5q79 0 123.5 45t63.5 130l88 401h119l-89 -403q-48 -220 -208 -264q-98 -43 -98 -103
q0 -17 10.5 -26.5t28.5 -9.5q30 0 51 37z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="567" 
d="M437 -102l38 -31q-39 -53 -93 -53q-39 0 -64 19.5t-25 54.5q0 66 65 112h-18l15 66q-82 -78 -165 -78q-68 0 -108 32.5t-40 89.5q0 15 5 40l74 333h105l-67 -302q-5 -25 -5 -31q0 -35 24.5 -52t65.5 -17q65 0 129 66l74 336h105l-107 -483q-98 -43 -98 -103
q0 -17 10.5 -26.5t28.5 -9.5q30 0 51 37z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="903" 
d="M652 0h-125l-21 493l-238 -493h-125l-43 667h127l17 -513l251 513h93l25 -513l243 513h134zM702 728h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="755" 
d="M546 0h-110l-35 354l-190 -354h-110l-43 483h106l24 -351l193 351h92l37 -351l180 351h113zM589 556h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="637" 
d="M570 728h-64l-57 97l-95 -97h-69l125 144h96zM324 0h-117l61 277l-173 390h127l120 -285l246 285h141l-344 -390z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="503" 
d="M-47 -185l35 92q18 -8 49 -8q37 0 65 40l38 58l-89 486h109l60 -362l222 362h116l-360 -570q-36 -58 -76.5 -83.5t-94.5 -25.5q-43 0 -74 11zM464 556h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="637" 
d="M604 787q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM395 787q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM324 0h-117l61 277l-173 390h127l120 -285l246 285h141
l-344 -390z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="589" 
d="M491 0h-501l21 95l438 469h-334l23 103h493l-21 -94l-438 -470h341zM598 872l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="476" 
d="M376 0h-384l18 80l303 312h-234l21 91h379l-17 -76l-308 -315h241zM504 700l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="589" 
d="M491 0h-501l21 95l438 469h-334l23 103h493l-21 -94l-438 -470h341zM469 787q0 -26 -20.5 -46t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5t46 19.5q23 0 38 -15t15 -37z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="476" 
d="M376 0h-384l18 80l303 312h-234l21 91h379l-17 -76l-308 -315h241zM380 626q0 -26 -20.5 -46t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5t46 19.5q23 0 38 -15t15 -37z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="589" 
d="M451 728h-96l-64 144h65l56 -96l96 96h68zM491 0h-501l21 95l438 469h-334l23 103h493l-21 -94l-438 -470h341z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="476" 
d="M376 0h-384l18 80l303 312h-234l21 91h379l-17 -76l-308 -315h241zM350 556h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="309" 
d="M145 0h-105l86 392h-80l21 91h79l6 28q40 166 172 166q77 0 122 -45l-54 -63q-22 22 -57 22q-32 0 -50.5 -19.5t-27.5 -61.5z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="445" 
d="M80 -161h-119l90 406h-60v93h80l41 182q35 157 169 157q78 0 126 -45l-47 -81q-19 23 -54 23q-30 0 -51 -20t-29 -57l-35 -159h124v-93h-144z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="960" 
d="M858 0h-457l28 129h-242l-105 -129h-138l557 667h504l-22 -103h-340l-38 -172h333l-23 -103h-333l-41 -186h340zM452 232l69 311l-256 -311h187zM814 872l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="924" 
d="M657 -12q-134 0 -177 107l-21 -95h-105l15 64q-64 -76 -152 -76q-83 0 -134 53t-51 147q0 120 70 213.5t184 93.5q108 0 160 -81l15 69h105l-15 -67q69 79 158 79q87 0 134 -54t47 -152q0 -45 -10 -82h-370q-1 -3 -1 -16q0 -46 38.5 -80.5t107.5 -34.5q76 0 126 41
l34 -76q-76 -53 -158 -53zM302 402q-69 0 -114.5 -57.5t-45.5 -135.5q0 -58 33.5 -93t85.5 -35q74 0 127 69l41 187q-17 29 -50.5 47t-76.5 18zM795 282q0 2 0.5 7t0.5 6q0 50 -31.5 82t-92.5 32q-56 0 -99 -37.5t-52 -89.5h274zM691 700l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="765" 
d="M688 872l-197 -144h-77l172 144h102zM371 -12q-119 0 -202 59l-44 -47h-87l84 89q-71 77 -71 195q0 166 111 280t272 114q111 0 193 -54l41 43h87l-79 -83q77 -80 77 -201q0 -165 -110.5 -280t-271.5 -115zM377 92q105 0 179 81.5t74 201.5q0 72 -36 121l-345 -365
q51 -39 128 -39zM174 291q0 -66 31 -114l343 363q-53 34 -122 34q-105 0 -178.5 -81.5t-73.5 -201.5z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="574" 
d="M553 700l-197 -144h-77l172 144h102zM67 0h-73l78 74q-39 54 -39 132q0 116 79 202.5t196 86.5q93 0 155 -48l37 36h73l-75 -72q41 -57 41 -133q0 -116 -79 -203t-196 -87q-96 0 -158 49zM268 81q69 0 115.5 58t46.5 134q0 35 -12 62l-236 -226q33 -28 86 -28zM142 210
q0 -35 10 -59l235 224q-33 27 -83 27q-69 0 -115.5 -57.5t-46.5 -134.5z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="605" 
d="M303 -12q-93 0 -171 34t-117 89l74 85q35 -48 94.5 -76t128.5 -28q59 0 90 29.5t31 66.5q0 30 -34 52.5t-82 40t-96 38.5t-82 59.5t-34 91.5q0 84 70 146t181 62q78 0 146 -28.5t109 -78.5l-75 -84q-34 43 -87.5 65.5t-109.5 22.5q-46 0 -78 -25.5t-32 -59.5
q0 -27 34 -48.5t82 -39t96 -39t82 -61t34 -92.5q0 -92 -69 -157t-185 -65zM282 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="477" 
d="M214 -12q-140 0 -218 87l62 72q23 -29 69 -52t96 -23q40 0 64 19t24 46q0 21 -25.5 36.5t-62.5 27.5t-74 27t-62.5 43t-25.5 67q0 65 51.5 111t140.5 46q60 0 112.5 -21.5t84.5 -56.5l-57 -70q-17 26 -58 45.5t-85 19.5q-39 0 -61.5 -16t-22.5 -41q0 -23 39 -41.5
t85.5 -32t85.5 -46.5t39 -82q0 -70 -54 -117.5t-147 -47.5zM220 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="hsuperior" unicode="&#x2b0;" horiz-adv-x="406" 
d="M397 326h-78l43 193q2 10 2 18q0 46 -58 46q-38 0 -82 -42l-47 -215h-77l96 434h77l-35 -161q47 49 108 49q48 0 74.5 -21t26.5 -60q0 -15 -4 -31z" />
    <glyph glyph-name="hsuperior" unicode="&#xf637;" horiz-adv-x="406" 
d="M397 326h-78l43 193q2 10 2 18q0 46 -58 46q-38 0 -82 -42l-47 -215h-77l96 434h77l-35 -161q47 49 108 49q48 0 74.5 -21t26.5 -60q0 -15 -4 -31z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="285" 
d="M354 556h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="285" 
d="M261 556h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="351" 
d="M440 647q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q34 -67 113 -67q80 0 142 67z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="148" 
d="M217 626q0 -26 -20.5 -46t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5t46 19.5q23 0 38 -15t15 -37z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="204" 
d="M182 550q-41 0 -67 26t-26 67q0 46 33.5 79t78.5 33q41 0 67 -26t26 -67q0 -45 -33 -78.5t-79 -33.5zM186 601q23 0 40 16.5t17 39.5q0 21 -12.5 34t-33.5 13q-23 0 -40 -16.5t-17 -39.5q0 -20 13 -33.5t33 -13.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="188" 
d="M67 -102l38 -31q-39 -53 -93 -53q-39 0 -64 19.5t-25 54.5q0 43 29.5 79.5t83.5 59.5l39 -27q-98 -43 -98 -103q0 -17 10.5 -26.5t28.5 -9.5q30 0 51 37z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="321" 
d="M300 554q-31 0 -50 19.5t-34 39t-35 19.5q-36 0 -52 -72h-59q28 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q36 0 53 72h58q-28 -134 -119 -134z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="324" 
d="M279 700l-147 -144h-65l122 144h90zM425 700l-147 -144h-65l122 144h90z" />
    <glyph glyph-name="Delta" unicode="&#x394;" horiz-adv-x="673" 
d="M618 0h-671l410 667h146zM480 103l-70 447l-268 -447h338z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="673" 
d="M618 0h-671l410 667h146zM480 103l-70 447l-268 -447h338z" />
    <glyph glyph-name="Omega" unicode="&#x3a9;" horiz-adv-x="765" 
d="M6 0l22 103h144q-112 89 -112 223q0 88 43 167.5t130 132t200 52.5q141 0 231.5 -75.5t90.5 -205.5q0 -185 -198 -294h118l-23 -103h-256l9 44l13 59q93 20 152 96t59 177q0 90 -54 143.5t-147 53.5q-111 0 -182 -83t-71 -197q0 -131 110 -190l-13 -59l-10 -44h-256z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="765" 
d="M6 0l22 103h144q-112 89 -112 223q0 88 43 167.5t130 132t200 52.5q141 0 231.5 -75.5t90.5 -205.5q0 -185 -198 -294h118l-23 -103h-256l9 44l13 59q93 20 152 96t59 177q0 90 -54 143.5t-147 53.5q-111 0 -182 -83t-71 -197q0 -131 110 -190l-13 -59l-10 -44h-256z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="595" 
d="M441 0h-106l90 407h-182l-90 -407h-106l90 407h-73l20 93h539l-20 -93h-72z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="593" 
d="M553 197h-533l19 90h533z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="833" 
d="M793 197h-773l19 90h773z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="245" 
d="M99 494q0 52 38.5 104t94.5 80l39 -41q-71 -34 -92 -84h10q22 0 37.5 -15t15.5 -41q0 -30 -23 -52.5t-53 -22.5q-28 0 -47.5 19t-19.5 53z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="245" 
d="M276 606q0 -52 -38.5 -104.5t-94.5 -79.5l-39 41q71 34 92 84q-2 -1 -10 -1q-22 0 -37.5 15.5t-15.5 41.5q0 30 23 52t53 22q29 0 48 -18.5t19 -52.5z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="245" 
d="M155 58q0 -52 -38.5 -104.5t-95.5 -80.5l-39 41q71 34 92 84h-9q-23 0 -38.5 15.5t-15.5 41.5q0 30 23.5 52t53.5 22q28 0 47.5 -18.5t19.5 -52.5z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="430" 
d="M287 494q0 52 38.5 104t95.5 80l39 -41q-71 -34 -92 -84h9q23 0 38.5 -15t15.5 -41q0 -30 -23.5 -52.5t-53.5 -22.5q-28 0 -47.5 19t-19.5 53zM103 494q0 52 38.5 104t94.5 80l39 -41q-71 -34 -92 -84h10q22 0 37.5 -15t15.5 -41q0 -30 -23 -52.5t-53 -22.5
q-28 0 -47.5 19t-19.5 53z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="430" 
d="M276 606q0 -52 -38.5 -104.5t-94.5 -79.5l-39 41q71 34 92 84q-2 -1 -10 -1q-22 0 -37.5 15.5t-15.5 41.5q0 30 23 52t53 22q29 0 48 -18.5t19 -52.5zM461 606q0 -52 -38.5 -104t-95.5 -80l-39 41q71 34 92 84q-2 -1 -9 -1q-23 0 -38.5 15.5t-15.5 41.5q0 30 23.5 52
t53.5 22q28 0 47.5 -18.5t19.5 -52.5z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="430" 
d="M155 58q0 -52 -38.5 -104.5t-95.5 -80.5l-39 41q71 34 92 84h-9q-23 0 -38.5 15.5t-15.5 41.5q0 30 23.5 52t53.5 22q28 0 47.5 -18.5t19.5 -52.5zM339 58q0 -52 -38 -104.5t-95 -80.5l-39 41q71 34 92 84h-10q-22 0 -37.5 15.5t-15.5 41.5q0 30 23 52t53 22
q29 0 48 -18.5t19 -52.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="271" 
d="M312 526l-88 4l-41 -203h-59l49 203l-90 -4l12 54l89 -3l18 100h59l-26 -100l89 3z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="271" 
d="M324 580l-12 -54l-88 4l-45 -202l90 4l-12 -54l-88 3l-19 -101h-59l27 101l-90 -3l12 54l88 -4l45 202l-90 -4l12 54l88 -3l19 100h59l-26 -100z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="358" 
d="M294 254q0 -51 -38 -88t-89 -37q-44 0 -73.5 29.5t-29.5 73.5q0 51 38.5 87.5t89.5 36.5q44 0 73 -29t29 -73z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="735" 
d="M75 -11q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5zM320 -11q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5zM565 -11q-26 0 -44.5 19t-18.5 46
q0 31 22.5 53t53.5 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1106" 
d="M142 0h-67l573 667h68zM519 -12q-65 0 -110 38t-45 99q0 85 53.5 139.5t128.5 54.5q65 0 110 -37.5t45 -99.5q0 -84 -53.5 -139t-128.5 -55zM522 55q42 0 72 34t30 86q0 34 -23 56t-58 22q-42 0 -72 -34.5t-30 -86.5q0 -33 23 -55t58 -22zM240 346q-65 0 -110.5 38
t-45.5 99q0 85 54 139.5t128 54.5q65 0 110.5 -37.5t45.5 -99.5q0 -84 -54 -139t-128 -55zM242 413q42 0 72 34t30 86q0 34 -22.5 56t-57.5 22q-42 0 -72 -34.5t-30 -86.5q0 -33 22.5 -55t57.5 -22zM878 -12q-65 0 -110 38t-45 99q0 85 53.5 139.5t128.5 54.5
q65 0 110 -37.5t45 -99.5q0 -84 -53.5 -139t-128.5 -55zM881 55q42 0 72 34t30 86q0 34 -23 56t-58 22q-42 0 -72 -34.5t-30 -86.5q0 -33 23 -55t58 -22z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="320" 
d="M245 63h-96l-120 180l200 177h104l-204 -182z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="320" 
d="M77 420h96l120 -180l-200 -177h-104l204 182z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="151" 
d="M415 667l-574 -667h-67l574 667h67z" />
    <glyph glyph-name="fraction" unicode="&#x2215;" horiz-adv-x="151" 
d="M415 667l-574 -667h-67l574 667h67z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" 
d="M445 669q0 -37 -11 -79t-33.5 -82.5t-63 -67t-91.5 -26.5q-71 0 -110.5 43.5t-39.5 114.5q0 37 11 79t33.5 82.5t63 67t91.5 26.5q71 0 110.5 -43.5t39.5 -114.5zM364 672q0 90 -71 90q-30 0 -53.5 -20.5t-36.5 -51.5t-19.5 -62.5t-6.5 -58.5q0 -90 71 -90q39 0 67 36
t38.5 77.5t10.5 79.5z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" 
d="M414 513h-54l-20 -92h-80l20 92h-197l13 57l220 251h112l-54 -244h54zM294 577l38 171l-154 -171h116z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" 
d="M416 569q0 -66 -46 -110.5t-120 -44.5q-54 0 -98.5 19t-65.5 53l55 48q34 -55 107 -55q39 0 62.5 22.5t23.5 55.5q0 30 -21.5 45.5t-56.5 15.5q-46 0 -83 -34l-53 19l49 218h277l-14 -65h-197l-23 -109q36 31 83 31q51 0 86 -30t35 -79z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" 
d="M422 567q0 -63 -45 -108t-119 -45q-76 0 -117.5 41t-41.5 111q0 107 62 184t151 77q87 0 135 -48l-47 -54q-29 37 -90 37q-55 0 -91 -48.5t-36 -84.5v-4q49 57 110 57q54 0 91.5 -31t37.5 -84zM339 557q0 29 -22 45.5t-58 16.5q-43 0 -80 -44q-2 -5 -2 -20q0 -34 22 -55
t58 -21t59 23.5t23 54.5z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="367" 
d="M439 766l-231 -345h-91l231 335h-215l14 65h304z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" 
d="M413 526q0 -48 -42 -80t-115 -32q-71 0 -121 28t-50 80q0 42 34 73.5t88 38.5q-71 27 -71 84q0 53 44.5 81t105.5 28q64 0 113.5 -26.5t49.5 -75.5q0 -40 -29 -69.5t-84 -35.5q32 -11 54.5 -36.5t22.5 -57.5zM366 712q0 25 -21.5 39t-55.5 14q-32 0 -53.5 -15.5
t-21.5 -40.5q0 -23 21.5 -37.5t42.5 -19.5q35 3 61.5 17.5t26.5 42.5zM334 535q0 21 -20 38.5t-48 24.5q-43 -2 -71 -20t-28 -46q0 -25 25 -40.5t58 -15.5q37 0 60.5 16.5t23.5 42.5z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" 
d="M121 676q0 63 45 107.5t119 44.5q76 0 117.5 -40.5t41.5 -110.5q0 -107 -62 -184.5t-151 -77.5q-85 0 -135 48l47 54q30 -37 90 -37q55 0 91 48.5t36 84.5v4q-51 -57 -110 -57q-54 0 -91.5 31.5t-37.5 84.5zM204 686q0 -30 22 -46t58 -16q43 0 80 44q2 5 2 19
q0 35 -21.5 55.5t-58.5 20.5q-36 0 -59 -23t-23 -54z" />
    <glyph glyph-name="parenleftsuperior" unicode="&#x207d;" horiz-adv-x="179" 
d="M198 376l-53 -20q-48 71 -48 184q0 97 45 187.5t120 158.5l44 -20q-131 -175 -131 -357q0 -75 23 -133z" />
    <glyph glyph-name="parenrightsuperior" unicode="&#x207e;" horiz-adv-x="179" 
d="M156 866l52 20q48 -71 48 -184q0 -97 -45 -187.5t-120 -158.5l-44 20q131 173 131 357q0 73 -22 133z" />
    <glyph glyph-name="nsuperior" unicode="&#x207f;" horiz-adv-x="406" 
d="M370 326h-78l43 193q2 10 2 18q0 46 -58 46q-37 0 -82 -43l-47 -214h-77l69 314h77l-8 -41q47 49 108 49q48 0 74.5 -21t26.5 -60q0 -15 -4 -31z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" 
d="M329 102q0 -37 -11 -79t-33.5 -82.5t-63 -67t-91.5 -26.5q-71 0 -110.5 43.5t-39.5 114.5q0 37 11 79t33.5 82.5t63 67t91.5 26.5q71 0 110.5 -43.5t39.5 -114.5zM248 105q0 90 -71 90q-30 0 -53.5 -20.5t-36.5 -51.5t-19.5 -62.5t-6.5 -58.5q0 -90 71 -90q39 0 67 36
t38.5 77.5t10.5 79.5z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="264" 
d="M118 -146h-81l64 293l-79 -66l-37 50l151 123h70z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" 
d="M267 -146h-305l14 64q156 91 212.5 135.5t56.5 87.5q0 26 -21.5 40t-53.5 14q-68 0 -112 -44l-34 53q59 56 149 56q66 0 110.5 -29.5t44.5 -83.5q0 -56 -58 -108.5t-175 -119.5h186z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" 
d="M294 -33q0 -47 -41.5 -83.5t-111.5 -36.5q-57 0 -102.5 19.5t-68.5 53.5l51 49q39 -57 117 -57q36 0 56 18.5t20 46.5q0 23 -25.5 36.5t-68.5 13.5q-25 0 -32 -1l14 64q13 -1 63 -1q38 0 59.5 14.5t21.5 40.5q0 24 -25 37.5t-60 13.5q-60 0 -106 -39l-28 51q58 53 141 53
q71 0 115.5 -27t44.5 -77q0 -41 -30 -69.5t-81 -32.5q33 -8 55 -30.5t22 -56.5z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" 
d="M298 -54h-54l-20 -92h-80l20 92h-197l13 57l220 251h112l-54 -244h54zM178 10l38 171l-154 -171h116z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" 
d="M300 2q0 -66 -46 -110.5t-120 -44.5q-54 0 -98.5 19t-65.5 53l55 48q34 -55 107 -55q39 0 62.5 22.5t23.5 55.5q0 30 -21.5 45.5t-56.5 15.5q-46 0 -83 -34l-53 19l49 218h277l-14 -65h-197l-23 -109q36 31 83 31q51 0 86 -30t35 -79z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" 
d="M306 0q0 -63 -45 -108t-119 -45q-76 0 -117.5 41t-41.5 111q0 107 62 184t151 77q87 0 135 -48l-47 -54q-29 37 -90 37q-55 0 -91 -48.5t-36 -84.5v-4q49 57 110 57q54 0 91.5 -31t37.5 -84zM223 -10q0 29 -22 45.5t-58 16.5q-43 0 -80 -44q-2 -5 -2 -20q0 -34 22 -55
t58 -21t59 23.5t23 54.5z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="367" 
d="M323 199l-231 -345h-91l231 335h-215l14 65h304z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" 
d="M297 -41q0 -48 -42 -80t-115 -32q-71 0 -121 28t-50 80q0 42 34 73.5t88 38.5q-71 27 -71 84q0 53 44.5 81t105.5 28q64 0 113.5 -26.5t49.5 -75.5q0 -40 -29 -69.5t-84 -35.5q32 -11 54.5 -36.5t22.5 -57.5zM250 145q0 25 -21.5 39t-55.5 14q-32 0 -53.5 -15.5
t-21.5 -40.5q0 -23 21.5 -37.5t42.5 -19.5q35 3 61.5 17.5t26.5 42.5zM218 -32q0 21 -20 38.5t-48 24.5q-43 -2 -71 -20t-28 -46q0 -25 25 -40.5t58 -15.5q37 0 60.5 16.5t23.5 42.5z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" 
d="M5 109q0 63 45 107.5t119 44.5q76 0 117.5 -40.5t41.5 -110.5q0 -107 -62 -184.5t-151 -77.5q-85 0 -135 48l47 54q30 -37 90 -37q55 0 91 48.5t36 84.5v4q-51 -57 -110 -57q-54 0 -91.5 31.5t-37.5 84.5zM88 119q0 -30 22 -46t58 -16q43 0 80 44q2 5 2 19
q0 35 -21.5 55.5t-58.5 20.5q-36 0 -59 -23t-23 -54z" />
    <glyph glyph-name="parenleftinferior" unicode="&#x208d;" horiz-adv-x="179" 
d="M73 -191l-53 -20q-48 71 -48 184q0 97 45 187.5t120 158.5l44 -20q-131 -175 -131 -357q0 -75 23 -133z" />
    <glyph glyph-name="parenrightinferior" unicode="&#x208e;" horiz-adv-x="179" 
d="M31 299l52 20q48 -71 48 -184q0 -97 -45 -187.5t-120 -158.5l-44 20q131 173 131 357q0 73 -22 133z" />
    <glyph glyph-name="colonmonetary" unicode="&#x20a1;" horiz-adv-x="690" 
d="M378 -12q-26 0 -50 3l-50 -91h-66l57 103q-31 9 -60 24l-70 -127h-66l88 159q-103 82 -103 225q0 173 113 283.5t270 110.5q29 0 61 -5l53 95h66l-61 -110q30 -10 58 -27l76 137h66l-96 -173q40 -40 63 -92l-112 -40q-10 21 -12 23l-219 -394q96 0 167 77l89 -63
q-52 -61 -122 -89.5t-140 -28.5zM181 291q0 -74 39 -125l227 407q-2 0 -6 0.5t-6 0.5q-103 0 -178.5 -80.5t-75.5 -202.5zM264 125q23 -16 59 -26l240 433q-24 20 -56 31z" />
    <glyph glyph-name="frenchfranc" unicode="&#x20a3;" horiz-adv-x="572" 
d="M144 0h-117l26 121h-72l14 62h72l107 484h457l-22 -103h-340l-38 -172h333l-23 -103h-333l-24 -106h174l-14 -62h-173z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="536" 
d="M41 331l12 57h57q-16 37 -16 73q0 88 74.5 152t179.5 64q81 0 142.5 -34t79.5 -93l-105 -45q-8 35 -39 55.5t-73 20.5q-55 0 -94 -37t-39 -95q0 -29 14 -61h180l-12 -57h-140q10 -27 10 -53v-7h116l-12 -57h-120q-30 -63 -85 -94q23 8 46 8q31 0 77 -18.5t73 -18.5
q46 0 86 38l26 -93q-49 -49 -120 -49q-47 0 -114.5 22.5t-100.5 22.5q-49 0 -113 -39l-22 83q53 22 92.5 59t52.5 79h-139l12 57h132q-4 28 -19 60h-99z" />
    <glyph glyph-name="uni20A6" unicode="&#x20a6;" horiz-adv-x="739" 
d="M605 0h-113l-118 236h-178l-52 -236h-117l52 236h-72l14 62h71l17 78h-70l13 62h71l51 229h120l116 -229h174l51 229h117l-51 -229h69l-13 -62h-70l-17 -78h69l-14 -62h-68zM251 483l-41 -185h133zM441 376l90 -177l39 177h-129z" />
    <glyph glyph-name="peseta" unicode="&#x20a7;" horiz-adv-x="662" 
d="M144 0h-117l94 427h-71l14 63h71l39 177h286q76 0 128 -52t53 -125h69l-14 -63h-63q-20 -75 -84 -126t-175 -51h-175zM381 353q47 0 80 20t48 54h-271l-16 -74h159zM269 564l-17 -74h266q-5 32 -31 53t-63 21h-155z" />
    <glyph glyph-name="rupee" unicode="&#x20a8;" horiz-adv-x="1049" 
d="M534 0h-130l-93 249h-122l-55 -249h-117l147 667h268q81 0 140 -50t59 -132q0 -90 -56.5 -153.5t-145.5 -74.5zM371 352h1q63 0 100 34.5t37 87.5q0 39 -29 64.5t-68 25.5h-153l-47 -212h159zM791 -12q-140 0 -218 87l62 72q23 -29 69 -52t96 -23q40 0 64 19t24 46
q0 21 -26 36.5t-62.5 27.5t-73 27t-62.5 43t-26 67q0 65 51.5 111t140.5 46q60 0 112.5 -21.5t84.5 -56.5l-57 -70q-17 26 -58 45.5t-85 19.5q-39 0 -61.5 -16t-22.5 -41q0 -23 39 -41.5t85.5 -32t85.5 -46.5t39 -82q0 -70 -54 -117.5t-147 -47.5z" />
    <glyph glyph-name="uni20A9" unicode="&#x20a9;" horiz-adv-x="903" 
d="M652 0h-125l-10 236h-135l-114 -236h-125l-15 236h-114l14 62h96l-5 78h-74l13 62h57l-15 229h127l7 -229h149l112 229h93l11 -229h148l109 229h134l-116 -229h53l-13 -62h-72l-39 -78h93l-14 -62h-111zM602 376l11 -222l105 222h-116zM236 376l8 -222l108 222h-116z
M506 493l-94 -195h102z" />
    <glyph glyph-name="afii57636" unicode="&#x20aa;" horiz-adv-x="835" 
d="M527 0h-314l83 379h100l-64 -288h193q59 0 90 32t47 102l72 325h100l-74 -334q-24 -110 -75.5 -163t-157.5 -53zM135 550h232q101 0 162.5 -39.5t61.5 -128.5q0 -27 -8 -69l-31 -143h-100l32 145q5 20 5 43q0 50 -31 75.5t-86 25.5h-156l-102 -459h-100z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="703" 
d="M397 -12q-123 0 -210 65.5t-106 176.5h-59l15 68h40q1 35 8 73h-32l16 68h36q44 110 141 174.5t214 64.5q106 0 178.5 -48t107.5 -127l-112 -40q-23 55 -71 83t-109 28q-67 0 -125 -36.5t-93 -98.5h281l-15 -68h-292q-10 -38 -10 -73h286l-15 -68h-263q18 -64 69 -101
t126 -37q96 0 167 77l89 -63q-52 -61 -122 -89.5t-140 -28.5z" />
    <glyph glyph-name="afii61289" unicode="&#x2113;" horiz-adv-x="504" 
d="M126 505l-86 -388q-3 -19 -3 -35q0 -44 34.5 -69t95.5 -25q48 0 76 22l-7 80q-17 -13 -40 -13t-36 11.5t-13 28.5q0 10 3 22l27 122l188 107q33 19 54.5 32t55 38.5t51.5 52t18 53.5q0 58 -58 95.5t-135 37.5q-90 0 -148.5 -45.5t-76.5 -126.5zM345 443l-146 -83l30 134
q11 53 43 81t79 28q37 0 64 -18t27 -47q0 -26 -23 -46t-74 -49z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="1125" 
d="M595 0h-113l-241 483l-107 -483h-117l147 667h120l237 -468l104 468h117zM961 350q-72 0 -114.5 40t-42.5 105q0 77 55 131t131 54q72 0 114.5 -40t42.5 -104q0 -78 -55 -132t-131 -54zM962 413q48 0 77 37.5t29 86.5q0 36 -21.5 57.5t-57.5 21.5q-48 0 -76.5 -37
t-28.5 -86q0 -36 21 -58t57 -22z" />
    <glyph glyph-name="published" unicode="&#x2117;" horiz-adv-x="778" 
d="M754 334q0 -142 -101.5 -243.5t-244.5 -101.5q-142 0 -243 101t-101 244t101 244t243 101q143 0 244.5 -101.5t101.5 -243.5zM720 334q0 128 -92 219.5t-220 91.5t-219 -91.5t-91 -219.5t91.5 -219.5t218.5 -91.5q128 0 220 91.5t92 219.5zM594 431q0 -56 -42 -93
t-104 -37h-122l-37 -170h-41l89 405h134q52 0 87.5 -28.5t35.5 -76.5zM550 428q0 32 -23 51t-62 19h-95l-35 -158h119q43 0 69.5 25.5t26.5 62.5z" />
    <glyph glyph-name="servicemark" unicode="&#x2120;" horiz-adv-x="476" 
d="M484 447h-28l40 182l-115 -182h-8l-35 182l-40 -182h-28l48 220h42l29 -160l99 160h44zM247 511q0 -32 -23 -52t-62 -20q-69 0 -96 40l22 19q23 -33 72 -33q24 0 38 11t14 29q0 15 -18 26.5t-39 18.5t-39 21t-18 34q0 29 23.5 48.5t60.5 19.5q63 0 86 -35l-23 -17
q-20 28 -65 28q-22 0 -35 -11t-13 -26t18 -26t39.5 -17t39.5 -21t18 -37z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="454" 
d="M464 447h-28l40 182l-115 -182h-8l-35 182l-40 -182h-28l48 220h43l29 -160l99 160h43zM257 641h-62l-43 -194h-28l43 194h-62l5 26h152z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="842" 
d="M829 324h-632q-4 0 -4 -5v-190q0 -15 9 -23q98 -103 240 -103q151 0 250 115h57q-54 -63 -135 -99.5t-174 -36.5q-161 0 -275.5 103t-114.5 248t114.5 248t275.5 103t275.5 -103t114.5 -248zM687 349v191q0 14 -10 24q-97 99 -236 99t-238 -102q-10 -10 -10 -24v-188
q0 -6 5 -6h485q4 0 4 6z" />
    <glyph glyph-name="onethird" unicode="&#x2153;" horiz-adv-x="841" 
d="M712 667l-574 -667h-67l574 667h67zM774 113q0 -47 -41.5 -83.5t-111.5 -36.5q-57 0 -102.5 19.5t-68.5 53.5l51 49q39 -57 117 -57q36 0 56 18.5t20 46.5q0 23 -25.5 36.5t-68.5 13.5q-25 0 -32 -1l14 64q13 -1 63 -1q38 0 59.5 14.5t21.5 40.5q0 24 -25 37.5t-60 13.5
q-60 0 -106 -39l-28 51q58 53 141 53q71 0 115.5 -27t44.5 -77q0 -41 -30 -69.5t-81 -32.5q33 -8 55 -30.5t22 -56.5zM209 267h-81l64 293l-79 -66l-37 50l151 123h70z" />
    <glyph glyph-name="twothirds" unicode="&#x2154;" horiz-adv-x="938" 
d="M809 667l-574 -667h-67l574 667h67zM870 113q0 -47 -41.5 -83.5t-111.5 -36.5q-57 0 -102.5 19.5t-68.5 53.5l51 49q39 -57 117 -57q36 0 56 18.5t20 46.5q0 23 -25.5 36.5t-68.5 13.5q-25 0 -32 -1l14 64q13 -1 63 -1q38 0 59.5 14.5t21.5 40.5q0 24 -25 37.5t-60 13.5
q-60 0 -106 -39l-28 51q58 53 141 53q71 0 115.5 -27t44.5 -77q0 -41 -30 -69.5t-81 -32.5q33 -8 55 -30.5t22 -56.5zM358 267h-305l14 64q156 91 212.5 135.5t56.5 87.5q0 26 -21.5 40t-53.5 14q-68 0 -112 -44l-34 53q59 56 149 56q66 0 110.5 -29.5t44.5 -83.5
q0 -56 -58 -108.5t-175 -119.5h186z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="841" 
d="M712 667l-574 -667h-67l574 667h67zM777 105q0 -48 -42 -80t-115 -32q-71 0 -121 28t-50 80q0 42 34 73.5t88 38.5q-71 27 -71 84q0 53 44.5 81t105.5 28q64 0 113.5 -26.5t49.5 -75.5q0 -40 -29 -69.5t-84 -35.5q32 -11 54.5 -36.5t22.5 -57.5zM730 291q0 25 -21.5 39
t-55.5 14q-32 0 -53.5 -15.5t-21.5 -40.5q0 -23 21.5 -37.5t42.5 -19.5q35 3 61.5 17.5t26.5 42.5zM698 114q0 21 -20 38.5t-48 24.5q-43 -2 -71 -20t-28 -46q0 -25 25 -40.5t58 -15.5q37 0 60.5 16.5t23.5 42.5zM209 267h-81l64 293l-79 -66l-37 50l151 123h70z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="938" 
d="M809 667l-574 -667h-67l574 667h67zM873 105q0 -48 -42 -80t-115 -32q-71 0 -121 28t-50 80q0 42 34 73.5t88 38.5q-71 27 -71 84q0 53 44.5 81t105.5 28q64 0 113.5 -26.5t49.5 -75.5q0 -40 -29 -69.5t-84 -35.5q32 -11 54.5 -36.5t22.5 -57.5zM826 291q0 25 -21.5 39
t-55.5 14q-32 0 -53.5 -15.5t-21.5 -40.5q0 -23 21.5 -37.5t42.5 -19.5q35 3 61.5 17.5t26.5 42.5zM794 114q0 21 -20 38.5t-48 24.5q-43 -2 -71 -20t-28 -46q0 -25 25 -40.5t58 -15.5q37 0 60.5 16.5t23.5 42.5zM385 380q0 -47 -41.5 -83.5t-111.5 -36.5q-57 0 -102.5 19.5
t-68.5 53.5l51 49q39 -57 117 -57q36 0 56 18.5t20 46.5q0 23 -25.5 36.5t-68.5 13.5q-25 0 -32 -1l14 64q13 -1 63 -1q38 0 59.5 14.5t21.5 40.5q0 24 -25 37.5t-60 13.5q-60 0 -106 -39l-28 51q58 53 141 53q71 0 115.5 -27t44.5 -77q0 -41 -30 -69.5t-81 -32.5
q33 -8 55 -30.5t22 -56.5z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="933" 
d="M804 667l-574 -667h-67l574 667h67zM868 105q0 -48 -42 -80t-115 -32q-71 0 -121 28t-50 80q0 42 34 73.5t88 38.5q-71 27 -71 84q0 53 44.5 81t105.5 28q64 0 113.5 -26.5t49.5 -75.5q0 -40 -29 -69.5t-84 -35.5q32 -11 54.5 -36.5t22.5 -57.5zM821 291q0 25 -21.5 39
t-55.5 14q-32 0 -53.5 -15.5t-21.5 -40.5q0 -23 21.5 -37.5t42.5 -19.5q35 3 61.5 17.5t26.5 42.5zM789 114q0 21 -20 38.5t-48 24.5q-43 -2 -71 -20t-28 -46q0 -25 25 -40.5t58 -15.5q37 0 60.5 16.5t23.5 42.5zM391 415q0 -66 -46 -110.5t-120 -44.5q-54 0 -98.5 19
t-65.5 53l55 48q34 -55 107 -55q39 0 62.5 22.5t23.5 55.5q0 30 -21.5 45.5t-56.5 15.5q-46 0 -83 -34l-53 19l49 218h277l-14 -65h-197l-23 -109q36 31 83 31q51 0 86 -30t35 -79z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="855" 
d="M726 667l-574 -667h-67l574 667h67zM790 105q0 -48 -42 -80t-115 -32q-71 0 -121 28t-50 80q0 42 34 73.5t88 38.5q-71 27 -71 84q0 53 44.5 81t105.5 28q64 0 113.5 -26.5t49.5 -75.5q0 -40 -29 -69.5t-84 -35.5q32 -11 54.5 -36.5t22.5 -57.5zM743 291q0 25 -21.5 39
t-55.5 14q-32 0 -53.5 -15.5t-21.5 -40.5q0 -23 21.5 -37.5t42.5 -19.5q35 3 61.5 17.5t26.5 42.5zM711 114q0 21 -20 38.5t-48 24.5q-43 -2 -71 -20t-28 -46q0 -25 25 -40.5t58 -15.5q37 0 60.5 16.5t23.5 42.5zM414 612l-231 -345h-91l231 335h-215l14 65h304z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" horiz-adv-x="582" 
d="M558 211h-269l12 -130l-281 205l281 204l-12 -129h269v-150z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" horiz-adv-x="582" 
d="M236 17v269l-130 -12l205 281l204 -281l-129 12v-269h-150z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" horiz-adv-x="582" 
d="M604 286l-281 -205l12 130h-268v150h268l-12 129z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" horiz-adv-x="582" 
d="M386 555v-269l130 12l-205 -281l-204 281l129 -12v269h150z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="574" 
d="M267 -12q-106 0 -172 58t-66 151q0 113 72.5 192t175.5 79q56 0 99.5 -27t66.5 -80q-19 69 -80.5 140.5t-142.5 130.5l74 78q250 -188 250 -405q0 -133 -76.5 -225t-200.5 -92zM269 81q66 0 111.5 51t45.5 124q0 49 -34 84.5t-97 35.5q-65 0 -110 -50t-45 -122
q0 -56 34 -89.5t95 -33.5z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="754" 
d="M545 -90h-117l145 654h-251l-144 -654h-117l144 654h-106l22 103h698l-23 -103h-106z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="582" 
d="M419 292l-157 272h341l23 103h-483l-22 -102l155 -268l-279 -284l-22 -103h484l22 103h-337z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="502" 
d="M485 301h-444l15 67h444z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="701" 
d="M360 0h-73l-51 313l-152 -53l-8 65l224 75l46 -294l338 561h82z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="676" 
d="M188 164q-67 0 -104.5 38t-37.5 103q0 80 49.5 141.5t131.5 61.5q51 0 89.5 -28t55.5 -78q71 106 155 106q66 0 104 -38.5t38 -103.5q0 -79 -50 -140.5t-132 -61.5q-52 0 -90 27.5t-55 77.5q-71 -105 -154 -105zM486 231q48 0 79.5 36.5t31.5 89.5q0 37 -24 60t-60 23
q-68 0 -124 -108q7 -48 32.5 -74.5t64.5 -26.5zM203 231q68 0 124 108q-6 48 -32 74.5t-65 26.5q-49 0 -80 -36t-31 -89q0 -37 23.5 -60.5t60.5 -23.5z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="363" 
d="M1 -100h-60l15 72h62q31 0 52 21t29 56l127 570q33 149 175 149h61l-17 -72h-61q-31 0 -52 -20.5t-29 -55.5l-127 -571q-33 -149 -175 -149z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="502" 
d="M20 205l16 74q43 -31 105 -31q45 0 110 27.5t123 27.5q61 0 106 -25l-16 -74q-43 31 -105 31q-46 0 -111 -27t-122 -27q-61 0 -106 24zM61 391l17 74q41 -31 104 -31q46 0 111 27t122 27q64 0 106 -25l-16 -74q-44 32 -105 32q-46 0 -111 -27.5t-122 -27.5q-62 0 -106 25
z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="502" 
d="M99 93h-70l96 112h-105l15 68h148l106 123h-227l15 67h269l96 111h71l-96 -111h104l-15 -67h-146l-106 -123h225l-15 -68h-269z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="502" 
d="M442 105l-398 207l17 76l490 207l-19 -84l-401 -165l328 -164zM419 0h-444l14 68h445z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="502" 
d="M487 312l-490 -207l19 84l401 164l-328 165l17 77l398 -207zM419 0h-445l15 68h444z" />
    <glyph glyph-name="triangleright" unicode="&#x22b2;" horiz-adv-x="587" 
d="M609 286l-535 -309v618z" />
    <glyph glyph-name="triangleleft" unicode="&#x22b3;" horiz-adv-x="587" 
d="M555 595v-618l-535 309z" />
    <glyph glyph-name="uni2318" unicode="&#x2318;" horiz-adv-x="667" 
d="M202 153l15 71h-77q-31 0 -54.5 -23.5t-23.5 -55.5q0 -26 17 -43.5t42 -17.5q32 0 53 18.5t28 50.5zM266 444l13 60q2 7 2 19q0 28 -17 44.5t-44 16.5q-33 0 -56 -22t-23 -55q0 -29 18.5 -46t48.5 -17h58zM269 265h137l31 137h-137zM565 160q0 29 -18.5 46.5t-48.5 17.5
h-58l-13 -61q-2 -6 -2 -19q0 -27 17 -43.5t44 -16.5q33 0 56 22t23 54zM644 522q0 26 -17 44t-42 18q-65 0 -81 -70l-15 -70h77q32 0 55 23t23 55zM607 160q0 -48 -39 -83t-89 -35q-42 0 -70 28t-28 70q0 14 3 29l13 55h-137l-18 -79q-11 -46 -45 -74.5t-80 -28.5
q-41 0 -70 28t-29 69q0 49 38 87.5t87 38.5h83l31 137h-58q-45 0 -72.5 29.5t-27.5 75.5q0 48 38.5 83t88.5 35q42 0 70 -28t28 -70q0 -14 -3 -29l-12 -54h137l17 78q11 46 45 74.5t80 28.5q42 0 71 -28t29 -69q0 -50 -38 -88t-87 -38h-83l-31 -137h55q46 0 74.5 -29
t28.5 -76z" />
    <glyph glyph-name="uni2318" unicode="&#xf8ff;" horiz-adv-x="667" 
d="M202 153l15 71h-77q-31 0 -54.5 -23.5t-23.5 -55.5q0 -26 17 -43.5t42 -17.5q32 0 53 18.5t28 50.5zM266 444l13 60q2 7 2 19q0 28 -17 44.5t-44 16.5q-33 0 -56 -22t-23 -55q0 -29 18.5 -46t48.5 -17h58zM269 265h137l31 137h-137zM565 160q0 29 -18.5 46.5t-48.5 17.5
h-58l-13 -61q-2 -6 -2 -19q0 -27 17 -43.5t44 -16.5q33 0 56 22t23 54zM644 522q0 26 -17 44t-42 18q-65 0 -81 -70l-15 -70h77q32 0 55 23t23 55zM607 160q0 -48 -39 -83t-89 -35q-42 0 -70 28t-28 70q0 14 3 29l13 55h-137l-18 -79q-11 -46 -45 -74.5t-80 -28.5
q-41 0 -70 28t-29 69q0 49 38 87.5t87 38.5h83l31 137h-58q-45 0 -72.5 29.5t-27.5 75.5q0 48 38.5 83t88.5 35q42 0 70 -28t28 -70q0 -14 -3 -29l-12 -54h137l17 78q11 46 45 74.5t80 28.5q42 0 71 -28t29 -69q0 -50 -38 -88t-87 -38h-83l-31 -137h55q46 0 74.5 -29
t28.5 -76z" />
    <glyph glyph-name="blacksquare" unicode="&#x25a0;" horiz-adv-x="661" 
d="M638 0h-571v572h571v-572z" />
    <glyph glyph-name="triagup" unicode="&#x25b2;" horiz-adv-x="616" 
d="M637 29l-619 1l309 535z" />
    <glyph glyph-name="triagdn" unicode="&#x25bc;" horiz-adv-x="616" 
d="M637 510l-310 -535l-309 535h619z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="375" 
d="M144 0h-21l-84 334l231 333h21l85 -333zM142 45l197 293l-67 284l-196 -292z" />
    <glyph glyph-name="circlesolid" unicode="&#x25cf;" horiz-adv-x="695" 
d="M664 285q0 -123 -87 -210t-210 -87t-210 87t-87 210t87 210t210 87t210 -87t87 -210z" />
    <glyph glyph-name="blackstar" unicode="&#x2605;" horiz-adv-x="759" 
d="M779 421l-235 -171l90 -275l-235 170l-234 -170l89 275l-234 171h290l89 276l90 -276h290z" />
    <glyph glyph-name="ballotbox" unicode="&#x2610;" horiz-adv-x="661" 
d="M636 0h-571v572h571v-572zM563 68v437h-424v-437h424z" />
    <glyph glyph-name="boxchecked" unicode="&#x2611;" horiz-adv-x="661" 
d="M656 0h-571v572h493l89 163l58 -37l-69 -128v-570zM583 68v368l-187 -345l-202 246l53 50l139 -170l156 288h-383v-437h424z" />
    <glyph glyph-name="diamond" unicode="&#x2666;" horiz-adv-x="501" 
d="M521 285l-251 -250l-250 250l250 251z" />
    <glyph glyph-name="checkmark" unicode="&#x2713;" horiz-adv-x="531" 
d="M551 698l-329 -607l-202 246l53 50l139 -170l281 518z" />
    <glyph glyph-name="perthousand.onum" unicode="&#xf628;" horiz-adv-x="894" 
d="M335 442q0 -62 -43.5 -108.5t-107.5 -46.5q-55 0 -88.5 32t-33.5 87q0 63 42.5 109.5t106.5 46.5q56 0 90 -32.5t34 -87.5zM593 550l-505 -550h-63l503 550h65zM269 440q0 29 -16 47.5t-44 18.5q-33 0 -57 -29.5t-24 -67.5q0 -29 16 -47t44 -18q33 0 57 28.5t24 67.5z
M556 145q0 -62 -43.5 -109t-107.5 -47q-55 0 -89 32t-34 88q0 63 43 109.5t107 46.5q55 0 89.5 -32.5t34.5 -87.5zM489 142q0 29 -16 47.5t-44 18.5q-33 0 -56.5 -29t-23.5 -68q0 -29 15.5 -46.5t43.5 -17.5q33 0 57 28.5t24 66.5zM845 145q0 -62 -43.5 -109t-107.5 -47
q-55 0 -88.5 32t-33.5 88q0 63 42.5 109.5t106.5 46.5q55 0 89.5 -32.5t34.5 -87.5zM779 142q0 29 -16 47.5t-44 18.5q-33 0 -57 -29t-24 -68q0 -28 16 -46t44 -18q33 0 57 28.5t24 66.5z" />
    <glyph glyph-name="zero.tzero" unicode="&#xf638;" horiz-adv-x="624" 
d="M286 -12q-98 0 -158 58l-40 -46h-89l87 101q-32 62 -32 152q0 106 39.5 202.5t115.5 159t170 62.5q96 0 156 -57l40 47h88l-87 -101q33 -64 33 -153q0 -106 -39 -202.5t-114.5 -159.5t-169.5 -63zM296 92q58 0 104 53.5t68 127t22 146.5q0 17 -4 43l-283 -329
q32 -41 93 -41zM172 247q0 -23 3 -43l284 329q-32 40 -92 40q-58 0 -104 -53t-68.5 -126t-22.5 -147z" />
    <glyph glyph-name="zero.tnum" unicode="&#xf639;" horiz-adv-x="613" 
d="M280 -12q-108 0 -170 70.5t-62 194.5q0 106 39.5 202.5t115.5 159t170 62.5q108 0 169 -69.5t61 -194.5q0 -106 -39 -202.5t-114.5 -159.5t-169.5 -63zM290 92q58 0 104 53.5t68 127t22 146.5q0 71 -30 112.5t-93 41.5q-58 0 -104 -53t-68.5 -126t-22.5 -147
q0 -71 30.5 -113t93.5 -42z" />
    <glyph glyph-name="two.tnum" unicode="&#xf63a;" horiz-adv-x="613" 
d="M497 0h-483l24 111q95 55 153.5 91t120 78.5t94.5 73.5t54 65t21 65q0 42 -36 66t-94 24q-98 0 -169 -62l-51 84q41 38 102.5 59.5t125.5 21.5q102 0 173 -48.5t71 -136.5q0 -98 -98.5 -191.5t-280.5 -197.5h296z" />
    <glyph glyph-name="three.tnum" unicode="&#xf63b;" horiz-adv-x="613" 
d="M297 -12q-91 0 -167.5 37.5t-109.5 101.5l81 69q28 -49 82 -76.5t113 -27.5q64 0 99.5 31.5t35.5 81.5q0 38 -33 61t-100 23q-53 0 -73 -2l24 106q10 -1 92 -1q63 0 103 23.5t40 71.5q0 38 -38 62.5t-103 24.5q-94 0 -165 -59l-44 79q88 83 220 83q113 0 181.5 -45.5
t68.5 -125.5q0 -67 -55.5 -114t-125.5 -54q55 -13 89.5 -49.5t34.5 -94.5q0 -88 -72 -147t-178 -59z" />
    <glyph glyph-name="four.tnum" unicode="&#xf63c;" horiz-adv-x="613" 
d="M423 0h-117l33 151h-318l24 104l358 412h167l-91 -413h89l-23 -103h-89zM362 254l68 308l-271 -308h203z" />
    <glyph glyph-name="five.tnum" unicode="&#xf63d;" horiz-adv-x="613" 
d="M294 -12q-183 0 -263 131l84 72q56 -99 182 -99q62 0 102.5 38t40.5 93q0 49 -37.5 77t-98.5 28q-71 0 -130 -43l-75 32l76 350h437l-22 -103h-320l-41 -184q54 44 136 44q84 0 140.5 -50t56.5 -138q0 -103 -75.5 -175.5t-192.5 -72.5z" />
    <glyph glyph-name="six.tnum" unicode="&#xf63e;" horiz-adv-x="613" 
d="M302 -12q-117 0 -182 66.5t-65 186.5q0 62 13.5 123.5t42.5 118t69 100t97.5 69t123.5 25.5q140 0 213 -94l-74 -82q-44 72 -147 72q-77 0 -128.5 -58t-75.5 -142q-6 -18 -7 -24q30 31 79.5 54t101.5 23q90 0 149 -49.5t59 -131.5q0 -105 -79 -181t-190 -76zM303 92
q62 0 104 41t42 97q0 46 -38.5 73.5t-98.5 27.5q-78 0 -142 -64q-1 -8 -1 -40q0 -61 36.5 -98t97.5 -37z" />
    <glyph glyph-name="seven.tnum" unicode="&#xf63f;" horiz-adv-x="613" 
d="M238 0h-135l375 564h-340l22 103h481l-18 -81z" />
    <glyph glyph-name="eight.tnum" unicode="&#xf640;" horiz-adv-x="613" 
d="M289 -12q-110 0 -184 45t-74 130q0 69 51 120.5t141 72.5q-46 20 -77 55.5t-31 81.5q0 61 39 104t94.5 61.5t118.5 18.5q100 0 173 -44t73 -124q0 -68 -49 -114.5t-131 -61.5q117 -58 117 -154q0 -85 -77 -138t-184 -53zM339 390q67 5 107.5 30.5t40.5 67.5q0 37 -37 61
t-91 24q-52 0 -89.5 -25.5t-37.5 -67.5q0 -30 33.5 -55.5t73.5 -34.5zM293 92q55 0 97.5 27.5t42.5 71.5q0 36 -35.5 63.5t-80.5 38.5q-70 -5 -116 -34t-46 -74q0 -44 38.5 -68.5t99.5 -24.5z" />
    <glyph glyph-name="nine.tnum" unicode="&#xf641;" horiz-adv-x="613" 
d="M351 678q117 0 182 -66.5t65 -186.5q0 -78 -22.5 -154t-64 -139.5t-109 -103t-149.5 -39.5q-140 0 -214 95l74 81q47 -72 147 -72q77 0 129 59t75 142q5 11 7 24q-29 -31 -78.5 -54t-102.5 -23q-90 0 -148.5 49t-58.5 131q0 105 78.5 181t189.5 76zM350 574
q-62 0 -103.5 -40.5t-41.5 -97.5q0 -46 38.5 -73t98.5 -27q79 0 141 64q2 16 2 40q0 61 -37 97.5t-98 36.5z" />
    <glyph glyph-name="percent.onum" unicode="&#xf642;" horiz-adv-x="604" 
d="M335 442q0 -62 -43.5 -108.5t-107.5 -46.5q-55 0 -88.5 32t-33.5 87q0 63 42.5 109.5t106.5 46.5q56 0 90 -32.5t34 -87.5zM593 550l-505 -550h-63l503 550h65zM269 440q0 29 -16 47.5t-44 18.5q-33 0 -57 -29.5t-24 -67.5q0 -29 16 -47t44 -18q33 0 57 28.5t24 67.5z
M556 145q0 -62 -43.5 -109t-107.5 -47q-55 0 -89 32t-34 88q0 63 43 109.5t107 46.5q55 0 89.5 -32.5t34.5 -87.5zM489 142q0 29 -16 47.5t-44 18.5q-33 0 -56.5 -29t-23.5 -68q0 -29 15.5 -46.5t43.5 -17.5q33 0 57 28.5t24 66.5z" />
    <glyph glyph-name="zero.tonum" unicode="&#xf643;" horiz-adv-x="613" 
d="M586 323q0 -56 -19.5 -114t-55 -108t-93.5 -81.5t-127 -31.5q-120 0 -186 63.5t-66 176.5q0 56 19.5 113.5t55 107.5t93.5 81.5t127 31.5q120 0 186 -63.5t66 -175.5zM466 315q0 70 -34 106.5t-99 36.5q-55 0 -96.5 -37.5t-59.5 -86.5t-18 -98q0 -70 34 -107t99 -37
q55 0 96.5 37.5t59.5 87t18 98.5z" />
    <glyph glyph-name="one.tonum" unicode="&#xf644;" horiz-adv-x="613" 
d="M374 0h-117l88 399l-134 -114l-52 71l234 194h102z" />
    <glyph glyph-name="two.tonum" unicode="&#xf645;" horiz-adv-x="613" 
d="M475 0h-453l21 99q405 132 405 270q0 41 -33.5 65t-86.5 24q-102 0 -181 -69l-51 82q100 91 244 91q101 0 166.5 -44.5t65.5 -122.5q0 -92 -90.5 -166t-222.5 -126h238z" />
    <glyph glyph-name="three.tonum" unicode="&#xf646;" horiz-adv-x="613" 
d="M271 -134q-91 0 -167.5 37.5t-109.5 101.5l81 69q28 -49 82 -76.5t113 -27.5q64 0 99.5 31.5t35.5 81.5q0 38 -33 61t-100 23q-53 0 -73 -2l24 106q10 -1 92 -1q63 0 103 23.5t40 71.5q0 38 -38 62.5t-103 24.5q-94 0 -165 -59l-44 79q88 83 220 83q113 0 181.5 -45.5
t68.5 -125.5q0 -67 -55.5 -114t-125.5 -54q55 -13 89.5 -49.5t34.5 -94.5q0 -88 -72 -147t-178 -59z" />
    <glyph glyph-name="four.tonum" unicode="&#xf647;" horiz-adv-x="613" 
d="M521 39h-89l-35 -156h-117l35 156h-319l21 94l366 417h161l-90 -408h89zM337 142l67 303l-270 -303h203z" />
    <glyph glyph-name="five.tonum" unicode="&#xf648;" horiz-adv-x="613" 
d="M267 -129q-183 0 -263 131l84 72q56 -99 182 -99q62 0 102.5 38t40.5 93q0 49 -37.5 77t-98.5 28q-71 0 -130 -43l-75 32l76 350h437l-22 -103h-320l-41 -184q54 44 136 44q84 0 140.5 -50t56.5 -138q0 -103 -75.5 -175.5t-192.5 -72.5z" />
    <glyph glyph-name="six.tonum" unicode="&#xf649;" horiz-adv-x="613" 
d="M302 -12q-117 0 -182 66.5t-65 186.5q0 62 13.5 123.5t42.5 118t69 100t97.5 69t123.5 25.5q140 0 213 -94l-74 -82q-44 72 -147 72q-77 0 -128.5 -58t-75.5 -142q-6 -18 -7 -24q30 31 79.5 54t101.5 23q90 0 149 -49.5t59 -131.5q0 -105 -79 -181t-190 -76zM303 92
q62 0 104 41t42 97q0 46 -38.5 73.5t-98.5 27.5q-78 0 -142 -64q-1 -8 -1 -40q0 -61 36.5 -98t97.5 -37z" />
    <glyph glyph-name="seven.tonum" unicode="&#xf64a;" horiz-adv-x="613" 
d="M212 -117h-135l375 564h-340l22 103h481l-18 -81z" />
    <glyph glyph-name="eight.tonum" unicode="&#xf64b;" horiz-adv-x="613" 
d="M289 -12q-110 0 -184 45t-74 130q0 69 51 120.5t141 72.5q-46 20 -77 55.5t-31 81.5q0 61 39 104t94.5 61.5t118.5 18.5q100 0 173 -44t73 -124q0 -68 -49 -114.5t-131 -61.5q117 -58 117 -154q0 -85 -77 -138t-184 -53zM339 390q67 5 107.5 30.5t40.5 67.5q0 37 -37 61
t-91 24q-52 0 -89.5 -25.5t-37.5 -67.5q0 -30 33.5 -55.5t73.5 -34.5zM293 92q55 0 97.5 27.5t42.5 71.5q0 36 -35.5 63.5t-80.5 38.5q-70 -5 -116 -34t-46 -74q0 -44 38.5 -68.5t99.5 -24.5z" />
    <glyph glyph-name="nine.tonum" unicode="&#xf64c;" horiz-adv-x="613" 
d="M324 561q117 0 182 -66.5t65 -186.5q0 -78 -22.5 -154t-64 -139.5t-109 -103t-149.5 -39.5q-140 0 -214 95l74 81q47 -72 147 -72q77 0 129 59t75 142q5 11 7 24q-29 -31 -78.5 -54t-102.5 -23q-90 0 -148.5 49t-58.5 131q0 105 78.5 181t189.5 76zM323 457
q-62 0 -103.5 -40.5t-41.5 -97.5q0 -46 38.5 -73t98.5 -27q79 0 141 64q2 16 2 40q0 61 -37 97.5t-98 36.5z" />
    <glyph glyph-name="colonmonetary.onum" unicode="&#xf64d;" horiz-adv-x="584" 
d="M174 -53l32 58q-24 8 -46 20l-43 -78h-52l58 104q-84 68 -84 188q0 132 96 227.5t232 95.5q21 0 41 -3l22 40h51l-28 -50q24 -8 46 -20l39 70h52l-54 -96q41 -34 61 -82l-98 -37q-4 9 -16 25l-181 -325h18q86 0 144 71l81 -51q-93 -116 -230 -116q-34 0 -64 6l-26 -47
h-51zM151 246q0 -56 28 -95l176 315q-82 -3 -143 -67t-61 -153zM212 117q20 -16 44 -23l193 346q-23 14 -46 20z" />
    <glyph glyph-name="Euro.onum" unicode="&#xf64e;" horiz-adv-x="611" 
d="M416 301h-231q-7 -30 -7 -52h227l-12 -54h-208q15 -52 58.5 -81.5t103.5 -29.5q86 0 144 71l81 -51q-93 -116 -230 -116q-110 0 -184 56.5t-89 150.5h-54l11 54h40q2 35 6 52h-34l12 53h38q36 92 119.5 150t186.5 58q81 0 142.5 -38.5t87.5 -102.5l-98 -37
q-17 37 -54 59.5t-82 22.5q-56 0 -105.5 -30.5t-78.5 -81.5h222z" />
    <glyph glyph-name="florin.onum" unicode="&#xf64f;" horiz-adv-x="385" 
d="M82 -119h-119l73 327h-46v76h63l26 116q36 161 165 161q61 0 97 -36l-40 -83q-13 16 -39 16q-51 0 -67 -75l-23 -99h94v-76h-110z" />
    <glyph glyph-name="numbersign.onum" unicode="&#xf650;" horiz-adv-x="508" 
d="M523 399l-31 -59h-83l-70 -126h85l-33 -60h-85l-85 -154h-73l85 154h-82l-85 -154h-73l85 154h-80l33 60h82l69 126h-82l31 59h84l85 151h73l-86 -151h82l85 151h73l-85 -151h81zM335 340h-80l-70 -126h81z" />
    <glyph glyph-name="sterling.onum" unicode="&#xf651;" horiz-adv-x="439" 
d="M10 224l14 60h62q-21 52 -21 90q0 79 62 133t144 54q66 0 117.5 -28.5t68.5 -79.5l-90 -35q-9 32 -34 48.5t-56 16.5q-44 0 -74.5 -32t-30.5 -83q0 -32 22 -84h128l-13 -60h-98q1 -4 1 -10q0 -30 -23 -66t-46 -50q9 3 21 3q27 0 65.5 -12.5t57.5 -12.5q39 0 69 30l25 -77
q-38 -40 -102 -40q-39 0 -92 17t-85 17q-39 0 -88 -31l-21 70q54 21 87 56t33 76q0 10 -4 30h-99z" />
    <glyph glyph-name="yen.onum" unicode="&#xf652;" horiz-adv-x="557" 
d="M280 0h-108l24 110h-202l14 59h201l13 59h-201l13 59h160l-127 263h123l106 -228l203 228h125l-241 -263h158l-14 -59h-197l-13 -59h197l-13 -59h-197z" />
    <glyph glyph-name="cent.onum" unicode="&#xf654;" horiz-adv-x="584" 
d="M315 -12h-3l-16 -75h-79l18 84q-90 20 -143 85t-53 157q0 130 94 225.5t228 97.5l14 60h79l-16 -69q117 -30 159 -132l-98 -37q-25 51 -82 72l-83 -372q77 6 130 71l81 -51q-93 -116 -230 -116zM151 246q0 -56 28.5 -96t77.5 -56l83 370q-78 -9 -133.5 -71.5
t-55.5 -146.5z" />
    <glyph glyph-name="zero.dnom" unicode="&#xf655;" 
d="M361 248q0 -37 -11 -79t-33.5 -82.5t-63 -67t-91.5 -26.5q-71 0 -110.5 43.5t-39.5 114.5q0 37 11 79t33.5 82.5t63 67t91.5 26.5q71 0 110.5 -43.5t39.5 -114.5zM280 251q0 90 -71 90q-30 0 -53.5 -20.5t-36.5 -51.5t-19.5 -62.5t-6.5 -58.5q0 -90 71 -90q39 0 67 36
t38.5 77.5t10.5 79.5z" />
    <glyph glyph-name="one.dnom" unicode="&#xf656;" horiz-adv-x="264" 
d="M150 0h-81l64 293l-79 -66l-37 50l151 123h70z" />
    <glyph glyph-name="two.dnom" unicode="&#xf657;" 
d="M299 0h-305l14 64q156 91 212.5 135.5t56.5 87.5q0 26 -21.5 40t-53.5 14q-68 0 -112 -44l-34 53q59 56 149 56q66 0 110.5 -29.5t44.5 -83.5q0 -56 -58 -108.5t-175 -119.5h186z" />
    <glyph glyph-name="three.dnom" unicode="&#xf658;" 
d="M326 113q0 -47 -41.5 -83.5t-111.5 -36.5q-57 0 -102.5 19.5t-68.5 53.5l51 49q39 -57 117 -57q36 0 56 18.5t20 46.5q0 23 -25.5 36.5t-68.5 13.5q-25 0 -32 -1l14 64q13 -1 63 -1q38 0 59.5 14.5t21.5 40.5q0 24 -25 37.5t-60 13.5q-60 0 -106 -39l-28 51q58 53 141 53
q71 0 115.5 -27t44.5 -77q0 -41 -30 -69.5t-81 -32.5q33 -8 55 -30.5t22 -56.5z" />
    <glyph glyph-name="four.dnom" unicode="&#xf659;" 
d="M330 92h-54l-20 -92h-80l20 92h-197l13 57l220 251h112l-54 -244h54zM210 156l38 171l-154 -171h116z" />
    <glyph glyph-name="five.dnom" unicode="&#xf65a;" 
d="M332 148q0 -66 -46 -110.5t-120 -44.5q-54 0 -98.5 19t-65.5 53l55 48q34 -55 107 -55q39 0 62.5 22.5t23.5 55.5q0 30 -21.5 45.5t-56.5 15.5q-46 0 -83 -34l-53 19l49 218h277l-14 -65h-197l-23 -109q36 31 83 31q51 0 86 -30t35 -79z" />
    <glyph glyph-name="six.dnom" unicode="&#xf65b;" 
d="M338 146q0 -63 -45 -108t-119 -45q-76 0 -117.5 41t-41.5 111q0 107 62 184t151 77q87 0 135 -48l-47 -54q-29 37 -90 37q-55 0 -91 -48.5t-36 -84.5v-4q49 57 110 57q54 0 91.5 -31t37.5 -84zM255 136q0 29 -22 45.5t-58 16.5q-43 0 -80 -44q-2 -5 -2 -20q0 -34 22 -55
t58 -21t59 23.5t23 54.5z" />
    <glyph glyph-name="seven.dnom" unicode="&#xf65c;" horiz-adv-x="367" 
d="M355 345l-231 -345h-91l231 335h-215l14 65h304z" />
    <glyph glyph-name="eight.dnom" unicode="&#xf65d;" 
d="M329 105q0 -48 -42 -80t-115 -32q-71 0 -121 28t-50 80q0 42 34 73.5t88 38.5q-71 27 -71 84q0 53 44.5 81t105.5 28q64 0 113.5 -26.5t49.5 -75.5q0 -40 -29 -69.5t-84 -35.5q32 -11 54.5 -36.5t22.5 -57.5zM282 291q0 25 -21.5 39t-55.5 14q-32 0 -53.5 -15.5
t-21.5 -40.5q0 -23 21.5 -37.5t42.5 -19.5q35 3 61.5 17.5t26.5 42.5zM250 114q0 21 -20 38.5t-48 24.5q-43 -2 -71 -20t-28 -46q0 -25 25 -40.5t58 -15.5q37 0 60.5 16.5t23.5 42.5z" />
    <glyph glyph-name="nine.dnom" unicode="&#xf65e;" 
d="M37 255q0 63 45 107.5t119 44.5q76 0 117.5 -40.5t41.5 -110.5q0 -107 -62 -184.5t-151 -77.5q-85 0 -135 48l47 54q30 -37 90 -37q55 0 91 48.5t36 84.5v4q-51 -57 -110 -57q-54 0 -91.5 31.5t-37.5 84.5zM120 265q0 -30 22 -46t58 -16q43 0 80 44q2 5 2 19
q0 35 -21.5 55.5t-58.5 20.5q-36 0 -59 -23t-23 -54z" />
    <glyph glyph-name="zero.numr" unicode="&#xf661;" 
d="M420 515q0 -37 -11 -79t-33.5 -82.5t-63 -67t-91.5 -26.5q-71 0 -110.5 43.5t-39.5 114.5q0 37 11 79t33.5 82.5t63 67t91.5 26.5q71 0 110.5 -43.5t39.5 -114.5zM339 518q0 90 -71 90q-30 0 -53.5 -20.5t-36.5 -51.5t-19.5 -62.5t-6.5 -58.5q0 -90 71 -90q39 0 67 36
t38.5 77.5t10.5 79.5z" />
    <glyph glyph-name="one.numr" unicode="&#xf662;" horiz-adv-x="264" 
d="M209 267h-81l64 293l-79 -66l-37 50l151 123h70z" />
    <glyph glyph-name="two.numr" unicode="&#xf663;" 
d="M358 267h-305l14 64q156 91 212.5 135.5t56.5 87.5q0 26 -21.5 40t-53.5 14q-68 0 -112 -44l-34 53q59 56 149 56q66 0 110.5 -29.5t44.5 -83.5q0 -56 -58 -108.5t-175 -119.5h186z" />
    <glyph glyph-name="three.numr" unicode="&#xf664;" 
d="M385 380q0 -47 -41.5 -83.5t-111.5 -36.5q-57 0 -102.5 19.5t-68.5 53.5l51 49q39 -57 117 -57q36 0 56 18.5t20 46.5q0 23 -25.5 36.5t-68.5 13.5q-25 0 -32 -1l14 64q13 -1 63 -1q38 0 59.5 14.5t21.5 40.5q0 24 -25 37.5t-60 13.5q-60 0 -106 -39l-28 51q58 53 141 53
q71 0 115.5 -27t44.5 -77q0 -41 -30 -69.5t-81 -32.5q33 -8 55 -30.5t22 -56.5z" />
    <glyph glyph-name="four.numr" unicode="&#xf665;" 
d="M389 359h-54l-20 -92h-80l20 92h-197l13 57l220 251h112l-54 -244h54zM269 423l38 171l-154 -171h116z" />
    <glyph glyph-name="five.numr" unicode="&#xf666;" 
d="M391 415q0 -66 -46 -110.5t-120 -44.5q-54 0 -98.5 19t-65.5 53l55 48q34 -55 107 -55q39 0 62.5 22.5t23.5 55.5q0 30 -21.5 45.5t-56.5 15.5q-46 0 -83 -34l-53 19l49 218h277l-14 -65h-197l-23 -109q36 31 83 31q51 0 86 -30t35 -79z" />
    <glyph glyph-name="six.numr" unicode="&#xf667;" 
d="M397 413q0 -63 -45 -108t-119 -45q-76 0 -117.5 41t-41.5 111q0 107 62 184t151 77q87 0 135 -48l-47 -54q-29 37 -90 37q-55 0 -91 -48.5t-36 -84.5v-4q49 57 110 57q54 0 91.5 -31t37.5 -84zM314 403q0 29 -22 45.5t-58 16.5q-43 0 -80 -44q-2 -5 -2 -20q0 -34 22 -55
t58 -21t59 23.5t23 54.5z" />
    <glyph glyph-name="seven.numr" unicode="&#xf668;" horiz-adv-x="367" 
d="M414 612l-231 -345h-91l231 335h-215l14 65h304z" />
    <glyph glyph-name="eight.numr" unicode="&#xf669;" 
d="M388 372q0 -48 -42 -80t-115 -32q-71 0 -121 28t-50 80q0 42 34 73.5t88 38.5q-71 27 -71 84q0 53 44.5 81t105.5 28q64 0 113.5 -26.5t49.5 -75.5q0 -40 -29 -69.5t-84 -35.5q32 -11 54.5 -36.5t22.5 -57.5zM341 558q0 25 -21.5 39t-55.5 14q-32 0 -53.5 -15.5
t-21.5 -40.5q0 -23 21.5 -37.5t42.5 -19.5q35 3 61.5 17.5t26.5 42.5zM309 381q0 21 -20 38.5t-48 24.5q-43 -2 -71 -20t-28 -46q0 -25 25 -40.5t58 -15.5q37 0 60.5 16.5t23.5 42.5z" />
    <glyph glyph-name="nine.numr" unicode="&#xf66a;" 
d="M96 522q0 63 45 107.5t119 44.5q76 0 117.5 -40.5t41.5 -110.5q0 -107 -62 -184.5t-151 -77.5q-86 0 -135 48l47 54q30 -37 90 -37q55 0 91 48.5t36 84.5v4q-51 -57 -110 -57q-54 0 -91.5 31.5t-37.5 84.5zM179 532q0 -30 22 -46t58 -16q43 0 80 44q2 5 2 19
q0 35 -21.5 55.5t-58.5 20.5q-36 0 -59 -23t-23 -54z" />
    <glyph glyph-name="Abreve.smcp" unicode="&#xf66d;" horiz-adv-x="586" 
d="M295 550h131l106 -550h-120l-19 104h-258l-64 -104h-125zM381 199l-43 240l-150 -240h193zM568 687q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q34 -67 113 -67q80 0 142 67z" />
    <glyph glyph-name="Amacron.smcp" unicode="&#xf66e;" horiz-adv-x="586" 
d="M295 550h131l106 -550h-120l-19 104h-258l-64 -104h-125zM381 199l-43 240l-150 -240h193zM560 626h-362l14 62h362z" />
    <glyph glyph-name="Aogonek.smcp" unicode="&#xf66f;" horiz-adv-x="586" 
d="M295 550h131l106 -550q-98 -43 -98 -103q0 -17 10.5 -26.5t28.5 -9.5q30 0 51 37l38 -31q-39 -53 -93 -53q-39 0 -64 19.5t-25 54.5q0 66 65 112h-33l-19 104h-258l-64 -104h-125zM381 199l-43 240l-150 -240h193z" />
    <glyph glyph-name="AEacute.smcp" unicode="&#xf670;" horiz-adv-x="835" 
d="M712 750l-197 -144h-77l172 144h102zM735 0h-401l22 104h-199l-86 -104h-126l468 550h443l-21 -95h-292l-28 -127h286l-21 -95h-286l-30 -138h292zM377 199l53 240l-200 -240h147z" />
    <glyph glyph-name="Cacute.smcp" unicode="&#xf671;" horiz-adv-x="584" 
d="M315 -12q-123 0 -199.5 70t-76.5 181q0 132 96 227.5t232 95.5q81 0 142.5 -38.5t87.5 -102.5l-98 -37q-17 37 -54 59.5t-82 22.5q-85 0 -148.5 -64.5t-63.5 -155.5q0 -73 47.5 -117.5t121.5 -44.5q86 0 144 71l81 -51q-93 -116 -230 -116zM614 750l-197 -144h-77
l172 144h102z" />
    <glyph glyph-name="Ccaron.smcp" unicode="&#xf672;" horiz-adv-x="584" 
d="M315 -12q-123 0 -199.5 70t-76.5 181q0 132 96 227.5t232 95.5q81 0 142.5 -38.5t87.5 -102.5l-98 -37q-17 37 -54 59.5t-82 22.5q-85 0 -148.5 -64.5t-63.5 -155.5q0 -73 47.5 -117.5t121.5 -44.5q86 0 144 71l81 -51q-93 -116 -230 -116zM467 606h-96l-64 144h65
l56 -96l96 96h68z" />
    <glyph glyph-name="Ccircumflex.smcp" unicode="&#xf673;" horiz-adv-x="584" 
d="M315 -12q-123 0 -199.5 70t-76.5 181q0 132 96 227.5t232 95.5q81 0 142.5 -38.5t87.5 -102.5l-98 -37q-17 37 -54 59.5t-82 22.5q-85 0 -148.5 -64.5t-63.5 -155.5q0 -73 47.5 -117.5t121.5 -44.5q86 0 144 71l81 -51q-93 -116 -230 -116zM560 606h-64l-57 97l-95 -97
h-69l125 144h96z" />
    <glyph glyph-name="Cdotaccent.smcp" unicode="&#xf674;" horiz-adv-x="584" 
d="M315 -12q-123 0 -199.5 70t-76.5 181q0 132 96 227.5t232 95.5q81 0 142.5 -38.5t87.5 -102.5l-98 -37q-17 37 -54 59.5t-82 22.5q-85 0 -148.5 -64.5t-63.5 -155.5q0 -73 47.5 -117.5t121.5 -44.5q86 0 144 71l81 -51q-93 -116 -230 -116zM493 686q0 -26 -20.5 -46
t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5t46 19.5q23 0 38 -15t15 -37z" />
    <glyph glyph-name="Dcaron.smcp" unicode="&#xf675;" horiz-adv-x="622" 
d="M234 0h-231l121 550h190q117 0 194.5 -66t77.5 -170q0 -56 -20 -109.5t-60 -100.5t-110.5 -75.5t-161.5 -28.5zM255 95q104 0 161 64t57 147q0 67 -46.5 108t-125.5 41h-88l-79 -360h121zM433 606h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="Dcroat.smcp" unicode="&#xf676;" horiz-adv-x="631" 
d="M244 0h-231l53 240h-58l17 77h58l51 233h190q117 0 194.5 -66t77.5 -170q0 -56 -20 -109.5t-60 -100.5t-110.5 -75.5t-161.5 -28.5zM265 95q104 0 161 64t57 147q0 67 -46.5 108t-125.5 41h-88l-30 -138h120l-17 -77h-120l-32 -145h121z" />
    <glyph glyph-name="Ebreve.smcp" unicode="&#xf677;" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-28 -127h286l-21 -95h-286l-31 -138h292zM542 687q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q34 -67 113 -67q80 0 142 67z" />
    <glyph glyph-name="Ecaron.smcp" unicode="&#xf678;" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-28 -127h286l-21 -95h-286l-31 -138h292zM399 606h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="Edotaccent.smcp" unicode="&#xf679;" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-28 -127h286l-21 -95h-286l-31 -138h292zM430 686q0 -26 -20.5 -46t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5t46 19.5q23 0 38 -15t15 -37z" />
    <glyph glyph-name="Emacron.smcp" unicode="&#xf67a;" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-28 -127h286l-21 -95h-286l-31 -138h292zM533 626h-362l14 62h362z" />
    <glyph glyph-name="Eng.smcp" unicode="&#xf67b;" horiz-adv-x="642" 
d="M307 -196q-117 0 -168 72l64 77q31 -52 95 -52q91 0 111 98v1l-199 393l-87 -393h-109l121 550h117l195 -382l85 382h109l-124 -563q-20 -93 -71 -138t-139 -45z" />
    <glyph glyph-name="Eogonek.smcp" unicode="&#xf67c;" horiz-adv-x="515" 
d="M407 -102l38 -31q-39 -53 -93 -53q-39 0 -64 19.5t-25 54.5q0 66 65 112h-314l121 550h401l-21 -95h-291l-28 -127h286l-21 -95h-286l-31 -138h292l-21 -95q-98 -43 -98 -103q0 -17 10.5 -26.5t28.5 -9.5q30 0 51 37z" />
    <glyph glyph-name="Gbreve.smcp" unicode="&#xf67d;" horiz-adv-x="620" 
d="M318 -12q-120 0 -200 67.5t-80 179.5q0 132 95 229.5t235 97.5q76 0 137.5 -34.5t91.5 -90.5l-97 -45q-18 32 -56 53t-83 21q-89 0 -149 -66t-60 -156q0 -70 49 -115t122 -45q62 0 109 30l22 97h-148l20 89h256l-52 -240q-97 -72 -212 -72zM612 687q-85 -88 -194 -88
q-53 0 -94 23.5t-63 64.5l55 41q34 -67 113 -67q80 0 142 67z" />
    <glyph glyph-name="Gcircumflex.smcp" unicode="&#xf67e;" horiz-adv-x="620" 
d="M318 -12q-120 0 -200 67.5t-80 179.5q0 132 95 229.5t235 97.5q76 0 137.5 -34.5t91.5 -90.5l-97 -45q-18 32 -56 53t-83 21q-89 0 -149 -66t-60 -156q0 -70 49 -115t122 -45q62 0 109 30l22 97h-148l20 89h256l-52 -240q-97 -72 -212 -72zM560 606h-64l-57 97l-95 -97
h-69l125 144h96z" />
    <glyph glyph-name="Gcommaaccent.smcp" unicode="&#xf67f;" horiz-adv-x="620" 
d="M318 -12q-120 0 -200 67.5t-80 179.5q0 132 95 229.5t235 97.5q76 0 137.5 -34.5t91.5 -90.5l-97 -45q-18 32 -56 53t-83 21q-89 0 -149 -66t-60 -156q0 -70 49 -115t122 -45q62 0 109 30l22 97h-148l20 89h256l-52 -240q-97 -72 -212 -72zM318 -121q0 -43 -32 -87
t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Gdotaccent.smcp" unicode="&#xf680;" horiz-adv-x="620" 
d="M318 -12q-120 0 -200 67.5t-80 179.5q0 132 95 229.5t235 97.5q76 0 137.5 -34.5t91.5 -90.5l-97 -45q-18 32 -56 53t-83 21q-89 0 -149 -66t-60 -156q0 -70 49 -115t122 -45q62 0 109 30l22 97h-148l20 89h256l-52 -240q-97 -72 -212 -72zM492 686q0 -26 -20.5 -46
t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5t46 19.5q23 0 38 -15t15 -37z" />
    <glyph glyph-name="Hcircumflex.smcp" unicode="&#xf681;" horiz-adv-x="644" 
d="M522 0h-108l51 235h-290l-51 -235h-110l121 550h110l-49 -220h290l49 220h108zM543 606h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Hbar.smcp" unicode="&#xf682;" horiz-adv-x="662" 
d="M532 0h-108l51 235h-290l-51 -235h-110l90 411h-67l13 62h68l17 77h110l-17 -77h290l17 77h108l-17 -77h65l-14 -62h-65zM206 330h290l18 81h-290z" />
    <glyph glyph-name="Ibreve.smcp" unicode="&#xf683;" horiz-adv-x="242" 
d="M122 0h-110l121 550h110zM393 687q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q33 -67 113 -67t142 67z" />
    <glyph glyph-name="IJ.smcp" unicode="&#xf684;" horiz-adv-x="674" 
d="M124 0h-110l121 550h110zM380 -12q-115 0 -168 73l65 75q31 -52 94 -52q90 0 112 98l81 368h109l-83 -377q-21 -97 -72.5 -141t-137.5 -44z" />
    <glyph glyph-name="Imacron.smcp" unicode="&#xf685;" horiz-adv-x="242" 
d="M122 0h-110l121 550h110zM388 626h-362l14 62h362z" />
    <glyph glyph-name="Iogonek.smcp" unicode="&#xf686;" horiz-adv-x="242" 
d="M115 -102l38 -31q-39 -53 -93 -53q-39 0 -64 19.5t-25 54.5q0 66 65 112h-23l121 550h110l-121 -550q-98 -43 -98 -103q0 -17 10.5 -26.5t28.5 -9.5q30 0 51 37z" />
    <glyph glyph-name="Itilde.smcp" unicode="&#xf687;" horiz-adv-x="242" 
d="M123 0h-110l121 550h110zM270 589q-31 0 -50 19.5t-34 39t-35 19.5q-36 0 -52 -72h-59q28 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q36 0 53 72h58q-28 -134 -119 -134z" />
    <glyph glyph-name="Jcircumflex.smcp" unicode="&#xf688;" horiz-adv-x="428" 
d="M134 -12q-115 0 -168 73l65 75q31 -52 94 -52q90 0 112 98l81 368h109l-83 -377q-21 -97 -72.5 -141t-137.5 -44zM528 606h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Kcommaaccent.smcp" unicode="&#xf689;" horiz-adv-x="553" 
d="M492 0h-131l-141 228l-58 -52l-38 -176h-110l121 550h110l-55 -248l270 248h136l-292 -259zM258 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Lacute.smcp" unicode="&#xf68a;" horiz-adv-x="464" 
d="M376 0h-362l121 550h110l-101 -455h253zM530 750l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Lcaron.smcp" unicode="&#xf68b;" horiz-adv-x="464" 
d="M376 0h-362l121 550h110l-101 -455h253zM419 502q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Lcommaaccent.smcp" unicode="&#xf68c;" horiz-adv-x="464" 
d="M376 0h-362l121 550h110l-101 -455h253zM227 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Ldot.smcp" unicode="&#xf68d;" horiz-adv-x="488" 
d="M376 0h-362l121 550h110l-101 -455h253zM376 230q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5z" />
    <glyph glyph-name="Nacute.smcp" unicode="&#xf68e;" horiz-adv-x="642" 
d="M520 0h-111l-199 393l-87 -393h-109l121 550h117l195 -382l85 382h109zM597 750l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Ncaron.smcp" unicode="&#xf68f;" horiz-adv-x="642" 
d="M520 0h-111l-199 393l-87 -393h-109l121 550h117l195 -382l85 382h109zM450 606h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="Ncommaaccent.smcp" unicode="&#xf690;" horiz-adv-x="642" 
d="M520 0h-111l-199 393l-87 -393h-109l121 550h117l195 -382l85 382h109zM299 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Obreve.smcp" unicode="&#xf691;" horiz-adv-x="658" 
d="M312 -12q-123 0 -198 70.5t-75 181.5q0 131 92.5 226.5t227.5 95.5q123 0 198 -70t75 -182q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147.5 66t58.5 153q0 71 -44.5 117t-119.5 46q-89 0 -147 -65.5t-58 -153.5q0 -70 44 -116.5t119 -46.5zM603 687
q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q33 -67 113 -67t142 67z" />
    <glyph glyph-name="Ohungarumlaut.smcp" unicode="&#xf692;" horiz-adv-x="658" 
d="M312 -12q-123 0 -198 70.5t-75 181.5q0 131 92.5 226.5t227.5 95.5q123 0 198 -70t75 -182q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147.5 66t58.5 153q0 71 -44.5 117t-119.5 46q-89 0 -147 -65.5t-58 -153.5q0 -70 44 -116.5t119 -46.5zM504 750l-147 -144h-65
l122 144h90zM650 750l-147 -144h-65l122 144h90z" />
    <glyph glyph-name="Omacron.smcp" unicode="&#xf693;" horiz-adv-x="658" 
d="M312 -12q-123 0 -198 70.5t-75 181.5q0 131 92.5 226.5t227.5 95.5q123 0 198 -70t75 -182q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147.5 66t58.5 153q0 71 -44.5 117t-119.5 46q-89 0 -147 -65.5t-58 -153.5q0 -70 44 -116.5t119 -46.5zM596 631h-362l14 62
h362z" />
    <glyph glyph-name="Oslashacute.smcp" unicode="&#xf694;" horiz-adv-x="658" 
d="M312 -12q-101 0 -171 48l-35 -36h-87l76 77q-56 67 -56 163q0 131 92.5 226.5t227.5 95.5q99 0 168 -47l34 35h87l-74 -76q58 -67 58 -164q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147.5 66t58.5 153q0 51 -25 92l-279 -283q40 -28 98 -28zM151 247q0 -50 23 -90
l278 282q-41 27 -96 27q-89 0 -147 -65.5t-58 -153.5zM608 750l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Racute.smcp" unicode="&#xf695;" horiz-adv-x="562" 
d="M468 0h-123l-80 200h-97l-44 -200h-110l121 550h232q84 0 134 -42.5t50 -108.5q0 -75 -47 -129t-130 -63zM327 295h8q47 0 74.5 27t27.5 67q0 29 -22 47.5t-59 18.5h-132l-35 -160h138zM550 750l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Rcaron.smcp" unicode="&#xf696;" horiz-adv-x="562" 
d="M468 0h-123l-80 200h-97l-44 -200h-110l121 550h232q84 0 134 -42.5t50 -108.5q0 -75 -47 -129t-130 -63zM327 295h8q47 0 74.5 27t27.5 67q0 29 -22 47.5t-59 18.5h-132l-35 -160h138zM403 606h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="Rcommaaccent.smcp" unicode="&#xf697;" horiz-adv-x="562" 
d="M468 0h-123l-80 200h-97l-44 -200h-110l121 550h232q84 0 134 -42.5t50 -108.5q0 -75 -47 -129t-130 -63zM327 295h8q47 0 74.5 27t27.5 67q0 29 -22 47.5t-59 18.5h-132l-35 -160h138zM255 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5
t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Sacute.smcp" unicode="&#xf698;" horiz-adv-x="511" 
d="M246 -12q-80 0 -146.5 25t-105.5 72l69 80q28 -37 79 -59t110 -22q53 0 76.5 21.5t23.5 53.5q0 24 -29 42t-70 32t-82 31t-70 50t-29 79q0 67 62 118t154 51q70 0 130 -25.5t95 -66.5l-69 -75q-25 31 -71 51t-91 20q-41 0 -68 -19.5t-27 -48.5q0 -23 29 -39.5t70 -30
t82 -30.5t70 -51.5t29 -83.5q0 -71 -57.5 -123t-163.5 -52zM534 750l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Scedilla.smcp" unicode="&#xf699;" horiz-adv-x="511" 
d="M172 -194q-75 0 -115 40l31 43q35 -35 85 -35q26 0 40 11t14 29q0 26 -32 26q-20 0 -32 -15l-36 23l39 67q-114 19 -172 90l69 80q28 -37 79 -59t110 -22q53 0 76.5 21.5t23.5 53.5q0 24 -29 42t-70 32t-82 31t-70 50t-29 79q0 67 62 118t154 51q70 0 130 -25.5t95 -66.5
l-69 -75q-25 31 -71 51t-91 20q-41 0 -68 -19.5t-27 -48.5q0 -23 29 -39.5t70 -30t82 -30.5t70 -51.5t29 -83.5q0 -71 -57.5 -123t-163.5 -52q-5 0 -14.5 0.5t-14.5 0.5l-25 -42q14 11 32 11q25 0 41.5 -16.5t16.5 -44.5q0 -41 -31 -66t-79 -25z" />
    <glyph glyph-name="Scircumflex.smcp" unicode="&#xf69a;" horiz-adv-x="511" 
d="M246 -12q-80 0 -146.5 25t-105.5 72l69 80q28 -37 79 -59t110 -22q53 0 76.5 21.5t23.5 53.5q0 24 -29 42t-70 32t-82 31t-70 50t-29 79q0 67 62 118t154 51q70 0 130 -25.5t95 -66.5l-69 -75q-25 31 -71 51t-91 20q-41 0 -68 -19.5t-27 -48.5q0 -23 29 -39.5t70 -30
t82 -30.5t70 -51.5t29 -83.5q0 -71 -57.5 -123t-163.5 -52zM481 606h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Scommaaccent.smcp" unicode="&#xf69b;" horiz-adv-x="511" 
d="M246 -12q-80 0 -146.5 25t-105.5 72l69 80q28 -37 79 -59t110 -22q53 0 76.5 21.5t23.5 53.5q0 24 -29 42t-70 32t-82 31t-70 50t-29 79q0 67 62 118t154 51q70 0 130 -25.5t95 -66.5l-69 -75q-25 31 -71 51t-91 20q-41 0 -68 -19.5t-27 -48.5q0 -23 29 -39.5t70 -30
t82 -30.5t70 -51.5t29 -83.5q0 -71 -57.5 -123t-163.5 -52zM237 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Tbar.smcp" unicode="&#xf69c;" horiz-adv-x="480" 
d="M241 0h-109l48 221h-108l14 62h108l38 172h-163l21 95h434l-21 -95h-162l-38 -172h109l-14 -62h-109z" />
    <glyph glyph-name="Tcaron.smcp" unicode="&#xf69d;" horiz-adv-x="480" 
d="M241 0h-109l100 455h-163l21 95h434l-21 -95h-162zM370 606h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="Tcommaaccent.smcp" unicode="&#xf69e;" horiz-adv-x="480" 
d="M241 0h-109l100 455h-163l21 95h434l-21 -95h-162zM218 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="Ubreve.smcp" unicode="&#xf69f;" horiz-adv-x="636" 
d="M306 -12q-119 0 -184.5 50.5t-65.5 133.5q0 19 5 44l74 334h110l-72 -325q-3 -17 -3 -32q0 -52 36 -80.5t98 -28.5q119 0 149 141l72 325h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM591 687q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q33 -67 113 -67t142 67z
" />
    <glyph glyph-name="Uhungarumlaut.smcp" unicode="&#xf6a0;" horiz-adv-x="636" 
d="M306 -12q-119 0 -184.5 50.5t-65.5 133.5q0 19 5 44l74 334h110l-72 -325q-3 -17 -3 -32q0 -52 36 -80.5t98 -28.5q119 0 149 141l72 325h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM492 750l-147 -144h-65l122 144h90zM638 750l-147 -144h-65l122 144h90z" />
    <glyph glyph-name="Umacron.smcp" unicode="&#xf6a1;" horiz-adv-x="636" 
d="M306 -12q-119 0 -184.5 50.5t-65.5 133.5q0 19 5 44l74 334h110l-72 -325q-3 -17 -3 -32q0 -52 36 -80.5t98 -28.5q119 0 149 141l72 325h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM585 626h-362l14 62h362z" />
    <glyph glyph-name="Uogonek.smcp" unicode="&#xf6a2;" horiz-adv-x="636" 
d="M388 -102l38 -31q-39 -53 -93 -53q-39 0 -64 19.5t-25 54.5q0 57 49 100q-114 3 -175.5 53t-61.5 131q0 19 5 44l74 334h110l-72 -325q-3 -17 -3 -32q0 -52 36 -80.5t98 -28.5q119 0 149 141l72 325h110l-74 -334q-40 -178 -165 -216q-98 -43 -98 -103q0 -17 10.5 -26.5
t28.5 -9.5q30 0 51 37z" />
    <glyph glyph-name="Uring.smcp" unicode="&#xf6a3;" horiz-adv-x="636" 
d="M306 -12q-119 0 -184.5 50.5t-65.5 133.5q0 19 5 44l74 334h110l-72 -325q-3 -17 -3 -32q0 -52 36 -80.5t98 -28.5q119 0 149 141l72 325h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM411 600q-41 0 -67 26t-26 67q0 46 33.5 79t78.5 33q41 0 67 -26t26 -67
q0 -45 -33 -78.5t-79 -33.5zM415 651q23 0 40 16.5t17 39.5q0 21 -12.5 34t-33.5 13q-23 0 -40 -16.5t-17 -39.5q0 -20 13 -33.5t33 -13.5z" />
    <glyph glyph-name="Utilde.smcp" unicode="&#xf6a4;" horiz-adv-x="636" 
d="M306 -12q-119 0 -184.5 50.5t-65.5 133.5q0 19 5 44l74 334h110l-72 -325q-3 -17 -3 -32q0 -52 36 -80.5t98 -28.5q119 0 149 141l72 325h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM469 604q-31 0 -50 19.5t-34 39t-35 19.5q-36 0 -52 -72h-59q28 134 118 134
q24 0 42 -12t26.5 -27t22 -27t29.5 -12q36 0 53 72h58q-28 -134 -119 -134z" />
    <glyph glyph-name="Wcircumflex.smcp" unicode="&#xf6a6;" horiz-adv-x="783" 
d="M615 606h-64l-57 97l-95 -97h-69l125 144h96zM560 0h-114l-23 390l-193 -390h-115l-44 550h121l18 -405l204 405h87l26 -405l197 405h122z" />
    <glyph glyph-name="Ycircumflex.smcp" unicode="&#xf6a9;" horiz-adv-x="557" 
d="M280 0h-108l50 229l-155 321h123l106 -228l203 228h125l-294 -321zM504 606h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Zacute.smcp" unicode="&#xf6ab;" horiz-adv-x="521" 
d="M429 0h-446l18 80l370 374h-287l21 96h439l-18 -80l-370 -374h294zM536 750l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Zdotaccent.smcp" unicode="&#xf6ac;" horiz-adv-x="521" 
d="M429 0h-446l18 80l370 374h-287l21 96h439l-18 -80l-370 -374h294zM425 686q0 -26 -20.5 -46t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5t46 19.5q23 0 38 -15t15 -37z" />
    <glyph glyph-name="Idotaccent.smcp" unicode="&#xf6ad;" horiz-adv-x="221" 
d="M113 0h-110l121 550h110zM268 686q0 -26 -20.5 -46t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5t46 19.5q23 0 38 -15t15 -37z" />
    <glyph glyph-name="parenleft.case" unicode="&#xf6ae;" horiz-adv-x="276" 
d="M192 -66l-75 -43q-73 123 -73 299q0 156 70.5 309.5t196.5 275.5l54 -55q-211 -263 -211 -561q0 -104 38 -225z" />
    <glyph glyph-name="parenright.case" unicode="&#xf6af;" horiz-adv-x="277" 
d="M123 732l76 43q72 -121 72 -299q0 -156 -70.5 -309.5t-196.5 -275.5l-54 55q211 264 211 562q0 103 -38 224z" />
    <glyph glyph-name="bracketleft.case" unicode="&#xf6b0;" horiz-adv-x="265" 
d="M170 -100h-203l193 868h203l-16 -72h-125l-161 -724h126z" />
    <glyph glyph-name="bracketright.case" unicode="&#xf6b1;" horiz-adv-x="265" 
d="M144 -100h-203l16 72h126l160 724h-125l16 72h203z" />
    <glyph glyph-name="braceleft.case" unicode="&#xf6b2;" horiz-adv-x="277" 
d="M183 -100h-47q-52 0 -89.5 33.5t-37.5 85.5q0 15 3 29l42 187q2 12 2 18q0 22 -10.5 36t-28.5 14l14 62q52 0 67 68l42 186q34 149 175 149h61l-16 -72h-61q-65 0 -82 -76l-45 -205q-17 -73 -65 -89q29 -15 29 -55q0 -18 -4 -34l-42 -188q-2 -12 -2 -20q0 -25 16 -41
t41 -16h54z" />
    <glyph glyph-name="braceright.case" unicode="&#xf6b3;" horiz-adv-x="277" 
d="M133 768h47q52 0 89.5 -33.5t37.5 -85.5q0 -13 -4 -29l-41 -187q-2 -12 -2 -17q0 -22 10.5 -36.5t28.5 -14.5l-14 -62q-52 0 -67 -68l-42 -186q-34 -149 -175 -149h-61l16 72h61q65 0 82 76l45 205q17 73 65 89q-29 15 -29 55q0 18 4 34l42 188q2 12 2 20q0 25 -16 41
t-41 16h-54z" />
    <glyph glyph-name="exclamdown.case" unicode="&#xf6b4;" horiz-adv-x="244" 
d="M124 463h91l-83 -463h-129zM208 678q26 0 44.5 -19t18.5 -45q0 -31 -22.5 -53.5t-53.5 -22.5q-27 0 -45.5 19t-18.5 46q0 31 22.5 53t54.5 22z" />
    <glyph glyph-name="questiondown.case" unicode="&#xf6b5;" horiz-adv-x="459" 
d="M224 437l95 30q20 -32 20 -71q0 -40 -22 -70.5t-53.5 -48.5t-62.5 -34.5t-53 -38t-22 -48.5q0 -31 26 -49.5t73 -18.5q77 0 135 55l51 -80q-88 -78 -202 -78q-89 0 -148.5 40t-59.5 107q0 49 24.5 85.5t59.5 57.5t69.5 38.5t59 39.5t24.5 51q0 19 -14 33zM320 674
q26 0 44.5 -19t18.5 -46q0 -31 -22.5 -53t-53.5 -22q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5z" />
    <glyph glyph-name="guillemotleft.case" unicode="&#xf6b6;" horiz-adv-x="490" 
d="M267 162h-96l-120 180l200 177h104l-204 -182zM437 162h-96l-120 180l200 177h104l-204 -182z" />
    <glyph glyph-name="guillemotright.case" unicode="&#xf6b7;" horiz-adv-x="490" 
d="M99 519h96l120 -180l-200 -177h-104l204 182zM269 519h96l120 -180l-200 -177h-104l204 182z" />
    <glyph glyph-name="guilsinglleft.case" unicode="&#xf6b8;" horiz-adv-x="320" 
d="M267 162h-96l-120 180l200 177h104l-204 -182z" />
    <glyph glyph-name="guilsinglright.case" unicode="&#xf6b9;" horiz-adv-x="320" 
d="M99 519h96l120 -180l-200 -177h-104l204 182z" />
    <glyph glyph-name="hyphen.case" unicode="&#xf6ba;" horiz-adv-x="300" 
d="M282 296h-240l19 90h240z" />
    <glyph glyph-name="endash.case" unicode="&#xf6bb;" horiz-adv-x="593" 
d="M575 296h-533l19 90h533z" />
    <glyph glyph-name="emdash.case" unicode="&#xf6bc;" horiz-adv-x="833" 
d="M815 296h-773l19 90h773z" />
    <glyph glyph-name="periodcentered.case" unicode="&#xf6bd;" horiz-adv-x="246" 
d="M137 273q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5z" />
    <glyph glyph-name="bullet.case" unicode="&#xf6be;" horiz-adv-x="358" 
d="M316 353q0 -51 -38 -88t-89 -37q-44 0 -73.5 29.5t-29.5 73.5q0 51 38.5 87.5t89.5 36.5q44 0 73 -29.5t29 -72.5z" />
    <glyph glyph-name="cent.case" unicode="&#xf6bf;" horiz-adv-x="498" 
d="M189 -9l21 97q-73 19 -115 74t-42 135q0 117 77.5 201t190.5 88l16 70h78l-17 -77q81 -20 122 -86l-79 -60q-23 38 -64 52l-70 -313q58 6 98 53l57 -70q-72 -76 -176 -76l-19 -88h-78zM161 302q0 -87 71 -118l68 306q-63 -12 -101 -65.5t-38 -122.5z" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="245" 
d="M100 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5z" />
    <glyph glyph-name="one.tnum" unicode="&#xf6dc;" horiz-adv-x="613" 
d="M374 0h-117l114 516l-134 -114l-52 71l234 194h102z" />
    <glyph glyph-name="rupiah" unicode="&#xf6dd;" horiz-adv-x="1156" 
d="M534 0h-130l-93 249h-122l-55 -249h-117l147 667h268q81 0 140 -50t59 -132q0 -90 -56.5 -153.5t-145.5 -74.5zM371 352h1q63 0 100 34.5t37 87.5q0 39 -29 64.5t-68 25.5h-153l-47 -212h159zM871 -12q-51 0 -93.5 21.5t-66.5 59.5l-56 -253h-105l148 667h105l-14 -64
q62 76 151 76q84 0 134.5 -52.5t50.5 -146.5q0 -77 -29 -146.5t-88.5 -115.5t-136.5 -46zM855 81q69 0 114.5 58t45.5 136q0 58 -33 92.5t-86 34.5q-37 0 -70.5 -19t-55.5 -49l-42 -187q17 -29 50.5 -47.5t76.5 -18.5z" />
    <glyph glyph-name="centinferior" unicode="&#xf6df;" horiz-adv-x="310" 
d="M66 -151l13 59q-46 11 -73.5 44.5t-27.5 81.5q0 69 48 119.5t119 52.5l9 42h53l-11 -48q52 -13 75 -53l-56 -37q-11 22 -32 31l-40 -181q27 5 53 33l43 -42q-41 -46 -109 -49l-11 -53h-53zM53 37q0 -53 39 -70l39 178q-34 -7 -56 -38t-22 -70z" />
    <glyph glyph-name="centsuperior" unicode="&#xf6e0;" horiz-adv-x="310" 
d="M191 416l13 58q-46 12 -73.5 45.5t-27.5 81.5q0 69 48 119.5t119 52.5l9 42h53l-11 -48q52 -13 75 -53l-56 -37q-11 22 -32 31l-40 -181q27 5 53 33l43 -42q-42 -48 -109 -49l-11 -53h-53zM178 604q0 -52 39 -71l39 179q-34 -7 -56 -38t-22 -70z" />
    <glyph glyph-name="commainferior" unicode="&#xf6e1;" horiz-adv-x="149" 
d="M41 -102q0 -31 -23.5 -67t-61.5 -57l-24 29q16 8 33 22.5t22 27.5q-3 -1 -5 -1q-13 0 -24 11t-11 29q0 21 15.5 35.5t36.5 14.5q17 0 29.5 -12t12.5 -32z" />
    <glyph glyph-name="commasuperior" unicode="&#xf6e2;" horiz-adv-x="149" 
d="M166 465q0 -31 -23.5 -67t-61.5 -57l-24 29q16 8 33 22.5t22 27.5q-3 -1 -5 -1q-13 0 -24 11t-11 29q0 21 15.5 35.5t36.5 14.5q17 0 29.5 -12t12.5 -32z" />
    <glyph glyph-name="dollarinferior" unicode="&#xf6e3;" horiz-adv-x="378" 
d="M136 -153h-4l-11 -53h-54l13 58q-88 15 -124 69l55 49q30 -42 82 -56l25 112q-26 10 -42 17t-35 20t-28.5 31.5t-9.5 42.5q0 49 43.5 86t114.5 37h9l12 55h54l-14 -63q66 -17 102 -59l-54 -45q-21 29 -62 43l-23 -104q53 -19 83 -43t30 -67q0 -55 -41 -92.5t-121 -37.5z
M212 -38q0 27 -44 46l-22 -100q31 2 48.5 17t17.5 37zM89 150q0 -26 47 -45l20 95q-29 -2 -48 -15.5t-19 -34.5z" />
    <glyph glyph-name="dollarsuperior" unicode="&#xf6e4;" horiz-adv-x="378" 
d="M261 414h-4l-11 -53h-54l13 58q-91 16 -124 69l55 49q31 -43 82 -57l25 113q-26 10 -42 17t-35 20t-28.5 31.5t-9.5 42.5q0 49 43.5 86t114.5 37h9l12 55h54l-14 -63q66 -17 102 -59l-54 -45q-21 29 -62 43l-23 -104q53 -19 83 -43t30 -67q0 -55 -41 -92.5t-121 -37.5z
M337 529q0 27 -44 46l-22 -100q31 1 48.5 16t17.5 38zM214 717q0 -26 47 -45l20 95q-29 -2 -48 -15.5t-19 -34.5z" />
    <glyph glyph-name="hypheninferior" unicode="&#xf6e5;" horiz-adv-x="184" 
d="M116 23h-148l14 61h148z" />
    <glyph glyph-name="hyphensuperior" unicode="&#xf6e6;" horiz-adv-x="184" 
d="M241 591h-148l14 61h148z" />
    <glyph glyph-name="periodinferior" unicode="&#xf6e7;" horiz-adv-x="152" 
d="M45 -100q0 -20 -16 -35.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 16 36t37 15q19 0 30.5 -11.5t11.5 -30.5z" />
    <glyph glyph-name="periodsuperior" unicode="&#xf6e8;" horiz-adv-x="152" 
d="M170 467q0 -20 -16 -35.5t-37 -15.5q-18 0 -30 12t-12 30q0 21 16 36t37 15q19 0 30.5 -11.5t11.5 -30.5z" />
    <glyph glyph-name="asuperior" unicode="&#xf6e9;" horiz-adv-x="382" 
d="M269 587q-45 0 -76 -37.5t-31 -89.5q0 -39 21 -59.5t55 -20.5q45 0 81 40l29 131q-25 36 -79 36zM368 640h77l-69 -314h-78l9 42q-43 -50 -100 -50q-53 0 -89 35t-36 99q0 81 49 138.5t120 57.5q74 0 108 -49z" />
    <glyph glyph-name="bsuperior" unicode="&#xf6ea;" horiz-adv-x="412" 
d="M249 380q45 0 76 37.5t31 88.5q0 39 -21 59.5t-55 20.5q-45 0 -81 -40l-29 -131q24 -35 79 -35zM150 326h-77l96 434h78l-36 -162q43 50 100 50q53 0 89 -34.5t36 -98.5q0 -81 -49 -139t-120 -58q-73 0 -108 50z" />
    <glyph glyph-name="dsuperior" unicode="&#xf6eb;" horiz-adv-x="412" 
d="M269 587q-45 0 -76 -37.5t-31 -89.5q0 -39 21 -59.5t55 -20.5q45 0 81 40l29 131q-25 36 -79 36zM394 760h78l-96 -434h-78l9 42q-43 -50 -100 -50q-53 0 -89 35t-36 99q0 81 49 138.5t120 57.5q74 0 108 -49z" />
    <glyph glyph-name="esuperior" unicode="&#xf6ec;" 
d="M249 318q-74 0 -120 38t-46 107q0 74 54.5 129.5t132.5 55.5q61 0 103 -37t42 -103q0 -28 -6 -50h-247v-8q0 -30 26 -52t68 -22q48 0 86 26l23 -48q-50 -36 -116 -36zM345 512q1 2 1 7q0 32 -21 52.5t-57 20.5q-39 0 -64 -24t-34 -56h175z" />
    <glyph glyph-name="isuperior" unicode="&#xf6ed;" horiz-adv-x="175" 
d="M186 677q-17 0 -28.5 12t-11.5 29q0 22 16 37.5t38 15.5q17 0 29 -12t12 -29q0 -22 -16.5 -37.5t-38.5 -15.5zM144 326h-77l69 314h77z" />
    <glyph glyph-name="lsuperior" unicode="&#xf6ee;" horiz-adv-x="187" 
d="M150 326h-77l96 434h77z" />
    <glyph glyph-name="msuperior" unicode="&#xf6ef;" horiz-adv-x="585" 
d="M549 326h-77l44 199q2 14 2 18q0 40 -48 40q-37 0 -74 -43l-47 -214h-77l44 199q3 11 3 18q0 40 -49 40q-34 0 -73 -43l-47 -214h-77l69 314h77l-8 -41q42 49 101 49q35 0 59.5 -16t31.5 -41q46 57 108 57q39 0 64.5 -20.5t25.5 -56.5q0 -9 -3 -22z" />
    <glyph glyph-name="osuperior" unicode="&#xf6f0;" horiz-adv-x="405" 
d="M241 318q-72 0 -114.5 40t-42.5 105q0 77 55 131t131 54q72 0 114.5 -40t42.5 -104q0 -78 -55 -132t-131 -54zM242 381q48 0 77 37.5t29 86.5q0 36 -21.5 57.5t-57.5 21.5q-48 0 -76.5 -37t-28.5 -86q0 -36 21 -58t57 -22z" />
    <glyph glyph-name="rsuperior" unicode="&#xf6f1;" horiz-adv-x="250" 
d="M150 326h-77l69 314h77l-9 -43q54 51 116 51l-16 -70q-12 2 -22 2q-24 0 -50 -11.5t-41 -28.5z" />
    <glyph glyph-name="ssuperior" unicode="&#xf6f2;" horiz-adv-x="334" 
d="M203 318q-94 0 -147 62l42 43q16 -21 45.5 -36t61.5 -15q26 0 41.5 11.5t15.5 27.5q0 19 -25.5 32.5t-56.5 21.5t-56.5 29.5t-25.5 54.5q0 41 35 70t95 29q91 0 134 -53l-40 -42q-36 41 -95 41q-25 0 -39.5 -10.5t-14.5 -27.5t26 -28.5t57.5 -19t57.5 -29.5t26 -58
q0 -41 -37 -72t-100 -31z" />
    <glyph glyph-name="tsuperior" unicode="&#xf6f3;" horiz-adv-x="223" 
d="M180 318q-36 0 -60.5 15.5t-24.5 49.5q0 10 2 22l40 180h-54l12 55h54l19 86h77l-19 -86h66l-12 -55h-66l-37 -167q-2 -8 -2 -14q0 -24 29 -24q14 0 28 9l6 -54q-24 -17 -58 -17z" />
    <glyph glyph-name="Lslash.smcp" unicode="&#xf6f9;" horiz-adv-x="481" 
d="M-11 154l22 97l85 43l56 256h110l-43 -193l97 50l-22 -97l-96 -50l-37 -165h253l-21 -95h-362l43 197z" />
    <glyph glyph-name="OE.smcp" unicode="&#xf6fa;" horiz-adv-x="940" 
d="M840 0h-402l15 71q-28 -40 -73 -61.5t-96 -21.5q-110 0 -177.5 70t-67.5 182q0 131 92 226.5t229 95.5q60 0 108.5 -25.5t71.5 -76.5l19 90h402l-21 -95h-292l-28 -127h286l-21 -95h-286l-30 -138h292zM480 193l35 157q-14 58 -58 87t-100 29q-87 0 -146.5 -64.5
t-59.5 -154.5q0 -74 45.5 -118.5t117.5 -44.5q54 0 97.5 28.5t68.5 80.5z" />
    <glyph glyph-name="Scaron.smcp" unicode="&#xf6fd;" horiz-adv-x="511" 
d="M246 -12q-80 0 -146.5 25t-105.5 72l69 80q28 -37 79 -59t110 -22q53 0 76.5 21.5t23.5 53.5q0 24 -29 42t-70 32t-82 31t-70 50t-29 79q0 67 62 118t154 51q70 0 130 -25.5t95 -66.5l-69 -75q-25 31 -71 51t-91 20q-41 0 -68 -19.5t-27 -48.5q0 -23 29 -39.5t70 -30
t82 -30.5t70 -51.5t29 -83.5q0 -71 -57.5 -123t-163.5 -52zM388 606h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="Zcaron.smcp" unicode="&#xf6ff;" horiz-adv-x="521" 
d="M429 0h-446l18 80l370 374h-287l21 96h439l-18 -80l-370 -374h294zM387 606h-96l-64 144h65l56 -96l96 96h68z" />
    <glyph glyph-name="exclam.smcp" unicode="&#xf721;" horiz-adv-x="196" 
d="M223 550l-95 -361h-84l63 361h116zM50 -11q-26 0 -45 19t-19 46q0 31 23 53t54 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5z" />
    <glyph glyph-name="dollar.onum" unicode="&#xf724;" horiz-adv-x="511" 
d="M246 -12h-4l-14 -62h-79l15 69q-112 21 -170 90l69 80q40 -54 123 -74l34 153q-44 16 -72.5 31t-52 46t-23.5 72q0 67 62 118t154 51h2l15 66h79l-17 -76q93 -21 146 -82l-69 -75q-34 41 -98 61l-31 -137q33 -12 55.5 -23t47 -29.5t37 -44.5t12.5 -59q0 -71 -57.5 -123
t-163.5 -52zM352 159q0 32 -59 58l-30 -133q47 3 68 24t21 51zM187 398q0 -31 55 -53l27 120q-37 -3 -59.5 -22t-22.5 -45z" />
    <glyph glyph-name="ampersand.smcp" unicode="&#xf726;" horiz-adv-x="563" 
d="M476 0h-117q-9 11 -27 35q-75 -46 -150 -46q-81 0 -132 39.5t-51 109.5q0 74 44 115t120 68q-18 48 -18 88q0 65 49 109t119 44q59 0 101.5 -29.5t42.5 -79.5q0 -35 -14 -61.5t-43 -45.5t-53.5 -30t-66.5 -25q16 -29 38 -62q31 -51 46 -73q51 53 87 123l70 -41
q-54 -85 -115 -144q24 -35 70 -94zM197 63q45 0 92 30q-40 61 -52 82q-31 51 -47 83q-87 -40 -87 -108q0 -39 25.5 -63t68.5 -24zM239 409q0 -23 13 -60q59 19 89 39.5t30 56.5q0 23 -14.5 35.5t-39.5 12.5q-34 0 -56 -23t-22 -61z" />
    <glyph glyph-name="zero.onum" unicode="&#xf730;" horiz-adv-x="617" 
d="M588 323q0 -56 -19.5 -114t-55 -108t-93.5 -81.5t-127 -31.5q-120 0 -186 63.5t-66 176.5q0 56 19.5 113.5t55 107.5t93.5 81.5t127 31.5q120 0 186 -63.5t66 -175.5zM468 315q0 70 -34 106.5t-99 36.5q-55 0 -96.5 -37.5t-59.5 -86.5t-18 -98q0 -70 34 -107t99 -37
q55 0 96.5 37.5t59.5 87t18 98.5z" />
    <glyph glyph-name="one.onum" unicode="&#xf731;" horiz-adv-x="380" 
d="M255 0h-117l88 399l-134 -114l-52 71l234 194h102z" />
    <glyph glyph-name="two.onum" unicode="&#xf732;" horiz-adv-x="581" 
d="M454 0h-453l21 99q405 132 405 270q0 41 -33.5 65t-86.5 24q-101 0 -181 -69l-51 82q100 91 244 91q101 0 166.5 -44.5t65.5 -122.5q0 -92 -90.5 -166t-222.5 -126h238z" />
    <glyph glyph-name="three.onum" unicode="&#xf733;" horiz-adv-x="568" 
d="M236 -129q-91 0 -167.5 37.5t-109.5 101.5l81 69q28 -49 82 -76.5t113 -27.5q64 0 99.5 31.5t35.5 81.5q0 38 -33 61t-100 23q-53 0 -73 -2l24 106q10 -1 92 -1q63 0 103 23.5t40 71.5q0 38 -38 62.5t-103 24.5q-94 0 -165 -59l-44 79q88 83 220 83q113 0 181.5 -45.5
t68.5 -125.5q0 -67 -55.5 -114t-125.5 -54q55 -13 89.5 -49.5t34.5 -94.5q0 -88 -72 -147t-178 -59z" />
    <glyph glyph-name="four.onum" unicode="&#xf734;" horiz-adv-x="580" 
d="M507 39h-89l-35 -156h-117l35 156h-319l21 94l366 417h161l-90 -408h89zM323 142l67 303l-270 -303h203z" />
    <glyph glyph-name="five.onum" unicode="&#xf735;" horiz-adv-x="600" 
d="M263 -129q-183 0 -263 131l84 72q56 -99 182 -99q62 0 102.5 38t40.5 93q0 49 -37.5 77t-98.5 28q-71 0 -130 -43l-75 32l76 350h437l-22 -103h-320l-41 -184q54 44 136 44q84 0 140.5 -50t56.5 -138q0 -103 -75.5 -175.5t-192.5 -72.5z" />
    <glyph glyph-name="six.onum" unicode="&#xf736;" horiz-adv-x="601" 
d="M293 -12q-117 0 -182 66.5t-65 186.5q0 62 13.5 123.5t42.5 118t69 100t97.5 69t123.5 25.5q140 0 213 -94l-74 -82q-44 72 -147 72q-77 0 -128.5 -58t-75.5 -142q-6 -18 -7 -24q30 31 79.5 54t101.5 23q90 0 149 -49.5t59 -131.5q0 -105 -79 -181t-190 -76zM294 92
q62 0 104 41t42 97q0 46 -38.5 73.5t-98.5 27.5q-78 0 -142 -64q-1 -8 -1 -40q0 -61 36.5 -98t97.5 -37z" />
    <glyph glyph-name="seven.onum" unicode="&#xf737;" horiz-adv-x="532" 
d="M172 -117h-135l375 564h-340l22 103h481l-18 -81z" />
    <glyph glyph-name="eight.onum" unicode="&#xf738;" horiz-adv-x="596" 
d="M280 -12q-110 0 -184 45t-74 130q0 69 51 120.5t141 72.5q-46 20 -77 55.5t-31 81.5q0 61 39 104t94.5 61.5t118.5 18.5q100 0 173 -44t73 -124q0 -68 -49 -114.5t-131 -61.5q117 -58 117 -154q0 -85 -77 -138t-184 -53zM330 390q67 5 107.5 30.5t40.5 67.5q0 37 -37 61
t-91 24q-52 0 -89.5 -25.5t-37.5 -67.5q0 -30 33.5 -55.5t73.5 -34.5zM284 92q55 0 97.5 27.5t42.5 71.5q0 36 -35.5 63.5t-80.5 38.5q-70 -5 -116 -34t-46 -74q0 -44 38.5 -68.5t99.5 -24.5z" />
    <glyph glyph-name="nine.onum" unicode="&#xf739;" horiz-adv-x="601" 
d="M320 561q117 0 182 -66.5t65 -186.5q0 -78 -22.5 -154t-64 -139.5t-109 -103t-149.5 -39.5q-140 0 -214 95l74 81q47 -72 147 -72q77 0 129 59t75 142q5 11 7 24q-29 -31 -78.5 -54t-102.5 -23q-90 0 -148.5 49t-58.5 131q0 105 78.5 181t189.5 76zM319 457
q-62 0 -103.5 -40.5t-41.5 -97.5q0 -46 38.5 -73t98.5 -27q79 0 141 64q2 16 2 40q0 61 -37 97.5t-98 36.5z" />
    <glyph glyph-name="question.smcp" unicode="&#xf73f;" horiz-adv-x="396" 
d="M420 436q0 -38 -20.5 -66t-50 -43.5t-58.5 -28.5t-49.5 -29t-20.5 -36q0 -15 15 -29l-86 -24q-21 26 -21 52q0 32 18.5 55.5t44.5 37t52.5 25.5t45 28.5t18.5 38.5q0 26 -24 40.5t-64 14.5q-70 0 -129 -54l-39 70q76 73 184 73q82 0 133 -34.5t51 -90.5zM147 -11
q-27 0 -45.5 19t-18.5 46q0 31 22.5 53t54.5 22q26 0 44.5 -19t18.5 -45q0 -31 -22.5 -53.5t-53.5 -22.5z" />
    <glyph glyph-name="A.smcp" unicode="&#xf761;" horiz-adv-x="586" 
d="M295 550h131l106 -550h-120l-19 104h-258l-64 -104h-125zM381 199l-43 240l-150 -240h193z" />
    <glyph glyph-name="B.smcp" unicode="&#xf762;" horiz-adv-x="573" 
d="M324 0h-310l121 550h260q74 0 120 -33.5t46 -91.5q0 -53 -34 -95t-90 -51q36 -13 57.5 -43.5t21.5 -66.5q0 -74 -51.5 -121.5t-140.5 -47.5zM145 95h173q38 0 62.5 21.5t24.5 55.5q0 27 -18.5 44t-50.5 17h-161zM356 328h10q36 0 57 22t21 52q0 26 -19.5 39.5t-51.5 13.5
h-149l-29 -127h161z" />
    <glyph glyph-name="C.smcp" unicode="&#xf763;" horiz-adv-x="584" 
d="M315 -12q-123 0 -199.5 70t-76.5 181q0 132 96 227.5t232 95.5q81 0 142.5 -38.5t87.5 -102.5l-98 -37q-17 37 -54 59.5t-82 22.5q-85 0 -148.5 -64.5t-63.5 -155.5q0 -73 47.5 -117.5t121.5 -44.5q86 0 144 71l81 -51q-93 -116 -230 -116z" />
    <glyph glyph-name="D.smcp" unicode="&#xf764;" horiz-adv-x="622" 
d="M245 0h-231l121 550h190q117 0 194.5 -66t77.5 -170q0 -56 -20 -109.5t-60 -100.5t-110.5 -75.5t-161.5 -28.5zM266 95q104 0 161 64t57 147q0 67 -46.5 108t-125.5 41h-88l-79 -360h121z" />
    <glyph glyph-name="E.smcp" unicode="&#xf765;" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-28 -127h286l-21 -95h-286l-31 -138h292z" />
    <glyph glyph-name="F.smcp" unicode="&#xf766;" horiz-adv-x="501" 
d="M124 0h-110l121 550h401l-20 -95h-292l-28 -127h286l-21 -95h-286z" />
    <glyph glyph-name="G.smcp" unicode="&#xf767;" horiz-adv-x="620" 
d="M318 -12q-120 0 -200 67.5t-80 179.5q0 132 95 229.5t235 97.5q76 0 137.5 -34.5t91.5 -90.5l-97 -45q-18 32 -56 53t-83 21q-89 0 -149 -66t-60 -156q0 -70 49 -115t122 -45q62 0 109 30l22 97h-148l20 89h256l-52 -240q-97 -72 -212 -72z" />
    <glyph glyph-name="H.smcp" unicode="&#xf768;" horiz-adv-x="644" 
d="M522 0h-108l51 235h-290l-51 -235h-110l121 550h110l-49 -220h290l49 220h108z" />
    <glyph glyph-name="I.smcp" unicode="&#xf769;" horiz-adv-x="246" 
d="M124 0h-110l121 550h110z" />
    <glyph glyph-name="J.smcp" unicode="&#xf76a;" horiz-adv-x="428" 
d="M134 -12q-115 0 -168 73l65 75q31 -52 94 -52q90 0 112 98l81 368h109l-83 -377q-21 -97 -72.5 -141t-137.5 -44z" />
    <glyph glyph-name="K.smcp" unicode="&#xf76b;" horiz-adv-x="553" 
d="M492 0h-131l-141 228l-58 -52l-38 -176h-110l121 550h110l-55 -248l270 248h136l-292 -259z" />
    <glyph glyph-name="L.smcp" unicode="&#xf76c;" horiz-adv-x="464" 
d="M376 0h-362l121 550h110l-101 -455h253z" />
    <glyph glyph-name="M.smcp" unicode="&#xf76d;" horiz-adv-x="741" 
d="M619 0h-109l89 403l-258 -403h-49l-79 403l-89 -403h-110l121 550h146l73 -375l238 375h148z" />
    <glyph glyph-name="N.smcp" unicode="&#xf76e;" horiz-adv-x="642" 
d="M520 0h-111l-199 393l-87 -393h-109l121 550h117l195 -382l85 382h109z" />
    <glyph glyph-name="O.smcp" unicode="&#xf76f;" horiz-adv-x="658" 
d="M312 -12q-123 0 -198 70.5t-75 181.5q0 131 92.5 226.5t227.5 95.5q123 0 198 -70t75 -182q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147.5 66t58.5 153q0 71 -44.5 117t-119.5 46q-89 0 -147 -65.5t-58 -153.5q0 -70 44 -116.5t119 -46.5z" />
    <glyph glyph-name="P.smcp" unicode="&#xf770;" horiz-adv-x="541" 
d="M124 0h-110l121 550h230q85 0 134.5 -42t49.5 -110q0 -81 -57 -139.5t-151 -58.5h-173zM330 295h8q44 0 71 27.5t27 67.5q0 30 -22 47.5t-60 17.5h-130l-35 -160h141z" />
    <glyph glyph-name="Q.smcp" unicode="&#xf771;" horiz-adv-x="658" 
d="M312 -12q-123 0 -198 70.5t-75 181.5q0 131 92.5 226.5t227.5 95.5q123 0 198 -70t75 -182q0 -74 -32.5 -140.5t-89.5 -112.5l27 -40l-77 -49l-29 43q-59 -23 -119 -23zM314 84q31 0 63 10l-51 79l76 49l52 -80q66 64 66 161q0 71 -44.5 117t-119.5 46q-89 0 -147 -65.5
t-58 -153.5q0 -70 44 -116.5t119 -46.5z" />
    <glyph glyph-name="R.smcp" unicode="&#xf772;" horiz-adv-x="562" 
d="M468 0h-123l-80 200h-97l-44 -200h-110l121 550h232q84 0 134 -42.5t50 -108.5q0 -75 -47 -129t-130 -63zM327 295h8q47 0 74.5 27t27.5 67q0 29 -22 47.5t-59 18.5h-132l-35 -160h138z" />
    <glyph glyph-name="S.smcp" unicode="&#xf773;" horiz-adv-x="511" 
d="M246 -12q-80 0 -146.5 25t-105.5 72l69 80q28 -37 79 -59t110 -22q53 0 76.5 21.5t23.5 53.5q0 24 -29 42t-70 32t-82 31t-70 50t-29 79q0 67 62 118t154 51q70 0 130 -25.5t95 -66.5l-69 -75q-25 31 -71 51t-91 20q-41 0 -68 -19.5t-27 -48.5q0 -23 29 -39.5t70 -30
t82 -30.5t70 -51.5t29 -83.5q0 -71 -57.5 -123t-163.5 -52z" />
    <glyph glyph-name="T.smcp" unicode="&#xf774;" horiz-adv-x="480" 
d="M241 0h-109l100 455h-163l21 95h434l-21 -95h-162z" />
    <glyph glyph-name="U.smcp" unicode="&#xf775;" horiz-adv-x="636" 
d="M306 -12q-119 0 -184.5 50.5t-65.5 133.5q0 19 5 44l74 334h110l-72 -325q-3 -17 -3 -32q0 -52 36 -80.5t98 -28.5q119 0 149 141l72 325h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57z" />
    <glyph glyph-name="V.smcp" unicode="&#xf776;" horiz-adv-x="586" 
d="M305 0h-131l-107 550h122l75 -431l266 431h123z" />
    <glyph glyph-name="W.smcp" unicode="&#xf777;" horiz-adv-x="783" 
d="M560 0h-114l-23 390l-193 -390h-115l-44 550h121l18 -405l204 405h87l26 -405l197 405h122z" />
    <glyph glyph-name="X.smcp" unicode="&#xf778;" horiz-adv-x="577" 
d="M522 0h-127l-113 209l-203 -209h-132l279 282l-144 268h127l101 -195l188 195h131l-261 -267z" />
    <glyph glyph-name="Y.smcp" unicode="&#xf779;" horiz-adv-x="557" 
d="M280 0h-108l50 229l-155 321h123l106 -228l203 228h125l-294 -321z" />
    <glyph glyph-name="Z.smcp" unicode="&#xf77a;" horiz-adv-x="521" 
d="M429 0h-446l18 80l370 374h-287l21 96h439l-18 -80l-370 -374h294z" />
    <glyph glyph-name="exclamdown.smcp" unicode="&#xf7a1;" horiz-adv-x="196" 
d="M-27 0l95 361h84l-63 -361h-116zM146 561q26 0 45 -19t19 -46q0 -31 -23 -53t-54 -22q-27 0 -45.5 19t-18.5 45q0 31 22.5 53.5t54.5 22.5z" />
    <glyph glyph-name="questiondown.smcp" unicode="&#xf7b8;" horiz-adv-x="396" 
d="M-24 114q0 38 20.5 66t50 43.5t58.5 28.5t49.5 29t20.5 36q0 15 -15 29l86 24q21 -26 21 -52q0 -32 -18.5 -55.5t-44.5 -37t-52.5 -25.5t-45 -28.5t-18.5 -38.5q0 -26 24 -40.5t64 -14.5q70 0 129 54l39 -70q-76 -73 -184 -73q-82 0 -133 34.5t-51 90.5zM249 561
q27 0 45.5 -19t18.5 -46q0 -31 -22.5 -53t-54.5 -22q-26 0 -44.5 19t-18.5 45q0 31 22.5 53.5t53.5 22.5z" />
    <glyph glyph-name="Agrave.smcp" unicode="&#xf7e0;" horiz-adv-x="586" 
d="M295 550h131l106 -550h-120l-19 104h-258l-64 -104h-125zM381 199l-43 240l-150 -240h193zM449 606h-75l-133 144h100z" />
    <glyph glyph-name="Aacute.smcp" unicode="&#xf7e1;" horiz-adv-x="586" 
d="M295 550h131l106 -550h-120l-19 104h-258l-64 -104h-125zM381 199l-43 240l-150 -240h193zM572 750l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Acircumflex.smcp" unicode="&#xf7e2;" horiz-adv-x="586" 
d="M295 550h131l106 -550h-120l-19 104h-258l-64 -104h-125zM381 199l-43 240l-150 -240h193zM517 606h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Atilde.smcp" unicode="&#xf7e3;" horiz-adv-x="586" 
d="M295 550h131l106 -550h-120l-19 104h-258l-64 -104h-125zM381 199l-43 240l-150 -240h193zM445 604q-31 0 -50 19.5t-34 39t-35 19.5q-36 0 -52 -72h-59q28 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q36 0 53 72h58q-28 -134 -119 -134z" />
    <glyph glyph-name="Adieresis.smcp" unicode="&#xf7e4;" horiz-adv-x="586" 
d="M295 550h131l106 -550h-120l-19 104h-258l-64 -104h-125zM381 199l-43 240l-150 -240h193zM551 675q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM342 675q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5
t46 19.5q22 0 37 -15t15 -37z" />
    <glyph glyph-name="Aring.smcp" unicode="&#xf7e5;" horiz-adv-x="586" 
d="M295 550h131l106 -550h-120l-19 104h-258l-64 -104h-125zM381 199l-43 240l-150 -240h193zM385 600q-41 0 -67 26t-26 67q0 46 33.5 79t78.5 33q41 0 67 -26t26 -67q0 -45 -33 -78.5t-79 -33.5zM389 651q23 0 40 16.5t17 39.5q0 21 -12.5 34t-33.5 13q-23 0 -40 -16.5
t-17 -39.5q0 -20 13 -33.5t33 -13.5z" />
    <glyph glyph-name="AE.smcp" unicode="&#xf7e6;" horiz-adv-x="835" 
d="M735 0h-401l22 104h-199l-86 -104h-126l468 550h443l-21 -95h-292l-28 -127h286l-21 -95h-286l-30 -138h292zM377 199l53 240l-200 -240h147z" />
    <glyph glyph-name="Ccedilla.smcp" unicode="&#xf7e7;" horiz-adv-x="584" 
d="M254 -194q-75 0 -115 40l31 43q35 -35 85 -35q26 0 40 11t14 29q0 26 -32 26q-20 0 -32 -15l-36 23l39 66q-96 18 -152.5 84t-56.5 161q0 132 96 227.5t232 95.5q81 0 142.5 -38.5t87.5 -102.5l-98 -37q-17 37 -54 59.5t-82 22.5q-85 0 -148.5 -64.5t-63.5 -155.5
q0 -73 47.5 -117.5t121.5 -44.5q86 0 144 71l81 -51q-93 -116 -230 -116h-16l-25 -41q14 11 32 11q25 0 41.5 -16.5t16.5 -44.5q0 -41 -31 -66t-79 -25z" />
    <glyph glyph-name="Egrave.smcp" unicode="&#xf7e8;" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-28 -127h286l-21 -95h-286l-31 -138h292zM426 606h-75l-133 144h100z" />
    <glyph glyph-name="Eacute.smcp" unicode="&#xf7e9;" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-28 -127h286l-21 -95h-286l-31 -138h292zM549 750l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Ecircumflex.smcp" unicode="&#xf7ea;" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-28 -127h286l-21 -95h-286l-31 -138h292zM495 606h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Edieresis.smcp" unicode="&#xf7eb;" horiz-adv-x="515" 
d="M415 0h-401l121 550h401l-21 -95h-291l-28 -127h286l-21 -95h-286l-31 -138h292zM526 675q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM317 675q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5
q22 0 37 -15t15 -37z" />
    <glyph glyph-name="Igrave.smcp" unicode="&#xf7ec;" horiz-adv-x="242" 
d="M124 0h-110l121 550h110zM276 606h-75l-133 144h100z" />
    <glyph glyph-name="Iacute.smcp" unicode="&#xf7ed;" horiz-adv-x="242" 
d="M124 0h-110l121 550h110zM399 750l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Icircumflex.smcp" unicode="&#xf7ee;" horiz-adv-x="242" 
d="M124 0h-110l121 550h110zM344 606h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Idieresis.smcp" unicode="&#xf7ef;" horiz-adv-x="242" 
d="M124 0h-110l121 550h110zM379 675q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM170 675q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37z" />
    <glyph glyph-name="Eth.smcp" unicode="&#xf7f0;" horiz-adv-x="631" 
d="M244 0h-231l53 240h-58l17 77h58l51 233h190q117 0 194.5 -66t77.5 -170q0 -56 -20 -109.5t-60 -100.5t-110.5 -75.5t-161.5 -28.5zM265 95q104 0 161 64t57 147q0 67 -46.5 108t-125.5 41h-88l-30 -138h120l-17 -77h-120l-32 -145h121z" />
    <glyph glyph-name="Ntilde.smcp" unicode="&#xf7f1;" horiz-adv-x="642" 
d="M520 0h-111l-199 393l-87 -393h-109l121 550h117l195 -382l85 382h109zM471 604q-31 0 -50 19.5t-34 39t-35 19.5q-36 0 -52 -72h-59q28 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q36 0 53 72h58q-28 -134 -119 -134z" />
    <glyph glyph-name="Ograve.smcp" unicode="&#xf7f2;" horiz-adv-x="658" 
d="M312 -12q-123 0 -198 70.5t-75 181.5q0 131 92.5 226.5t227.5 95.5q123 0 198 -70t75 -182q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147.5 66t58.5 153q0 71 -44.5 117t-119.5 46q-89 0 -147 -65.5t-58 -153.5q0 -70 44 -116.5t119 -46.5zM484 606h-75l-133 144
h100z" />
    <glyph glyph-name="Oacute.smcp" unicode="&#xf7f3;" horiz-adv-x="658" 
d="M312 -12q-123 0 -198 70.5t-75 181.5q0 131 92.5 226.5t227.5 95.5q123 0 198 -70t75 -182q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147.5 66t58.5 153q0 71 -44.5 117t-119.5 46q-89 0 -147 -65.5t-58 -153.5q0 -70 44 -116.5t119 -46.5zM608 750l-197 -144h-77
l172 144h102z" />
    <glyph glyph-name="Ocircumflex.smcp" unicode="&#xf7f4;" horiz-adv-x="658" 
d="M312 -12q-123 0 -198 70.5t-75 181.5q0 131 92.5 226.5t227.5 95.5q123 0 198 -70t75 -182q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147.5 66t58.5 153q0 71 -44.5 117t-119.5 46q-89 0 -147 -65.5t-58 -153.5q0 -70 44 -116.5t119 -46.5zM554 606h-64l-57 97
l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Otilde.smcp" unicode="&#xf7f5;" horiz-adv-x="658" 
d="M312 -12q-123 0 -198 70.5t-75 181.5q0 131 92.5 226.5t227.5 95.5q123 0 198 -70t75 -182q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147.5 66t58.5 153q0 71 -44.5 117t-119.5 46q-89 0 -147 -65.5t-58 -153.5q0 -70 44 -116.5t119 -46.5zM483 604
q-31 0 -50 19.5t-34 39t-35 19.5q-36 0 -52 -72h-59q28 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q36 0 53 72h58q-28 -134 -119 -134z" />
    <glyph glyph-name="Odieresis.smcp" unicode="&#xf7f6;" horiz-adv-x="658" 
d="M312 -12q-123 0 -198 70.5t-75 181.5q0 131 92.5 226.5t227.5 95.5q123 0 198 -70t75 -182q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147.5 66t58.5 153q0 71 -44.5 117t-119.5 46q-89 0 -147 -65.5t-58 -153.5q0 -70 44 -116.5t119 -46.5zM588 675
q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM379 675q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37z" />
    <glyph glyph-name="Oslash.smcp" unicode="&#xf7f8;" horiz-adv-x="658" 
d="M312 -12q-100 0 -169 47l-35 -35h-91l77 78q-55 68 -55 162q0 131 92.5 226.5t227.5 95.5q96 0 167 -46l33 34h91l-76 -77q58 -67 58 -163q0 -131 -92.5 -226.5t-227.5 -95.5zM314 84q89 0 147.5 66t58.5 153q0 52 -24 91l-279 -283q41 -27 97 -27zM151 247q0 -48 22 -89
l278 282q-42 26 -95 26q-89 0 -147 -65.5t-58 -153.5z" />
    <glyph glyph-name="Ugrave.smcp" unicode="&#xf7f9;" horiz-adv-x="636" 
d="M306 -12q-119 0 -184.5 50.5t-65.5 133.5q0 19 5 44l74 334h110l-72 -325q-3 -17 -3 -32q0 -52 36 -80.5t98 -28.5q119 0 149 141l72 325h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM473 606h-75l-133 144h100z" />
    <glyph glyph-name="Uacute.smcp" unicode="&#xf7fa;" horiz-adv-x="636" 
d="M306 -12q-119 0 -184.5 50.5t-65.5 133.5q0 19 5 44l74 334h110l-72 -325q-3 -17 -3 -32q0 -52 36 -80.5t98 -28.5q119 0 149 141l72 325h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM597 750l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Ucircumflex.smcp" unicode="&#xf7fb;" horiz-adv-x="636" 
d="M306 -12q-119 0 -184.5 50.5t-65.5 133.5q0 19 5 44l74 334h110l-72 -325q-3 -17 -3 -32q0 -52 36 -80.5t98 -28.5q119 0 149 141l72 325h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM541 606h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Udieresis.smcp" unicode="&#xf7fc;" horiz-adv-x="636" 
d="M306 -12q-119 0 -184.5 50.5t-65.5 133.5q0 19 5 44l74 334h110l-72 -325q-3 -17 -3 -32q0 -52 36 -80.5t98 -28.5q119 0 149 141l72 325h110l-74 -334q-25 -114 -84.5 -171t-170.5 -57zM576 675q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5
q22 0 37 -15t15 -37zM367 675q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37z" />
    <glyph glyph-name="Yacute.smcp" unicode="&#xf7fd;" horiz-adv-x="557" 
d="M280 0h-108l50 229l-155 321h123l106 -228l203 228h125l-294 -321zM558 750l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="Thorn.smcp" unicode="&#xf7fe;" horiz-adv-x="541" 
d="M124 0h-110l121 550h110l-19 -86h120q82 0 133 -41.5t51 -111.5q0 -79 -57 -138.5t-151 -59.5h-174zM308 208h10q44 0 71.5 27.5t27.5 67.5q0 32 -23 49t-61 17h-128l-36 -161h139z" />
    <glyph glyph-name="Ydieresis.smcp" unicode="&#xf7ff;" horiz-adv-x="557" 
d="M280 0h-108l50 229l-155 321h123l106 -228l203 228h125l-294 -321zM538 675q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM329 675q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15
t15 -37z" />
    <glyph glyph-name="f.alt1" horiz-adv-x="309" 
d="M145 0h-105l86 392h-80l21 91h79l6 27q36 167 172 167q58 0 98 -25l-38 -75q-18 14 -49 14q-60 0 -78 -81l-6 -27h99l-21 -91h-98z" />
    <glyph glyph-name="l.alt1" horiz-adv-x="286" 
d="M166 -12q-61 0 -95.5 25t-34.5 69q0 19 4 35l121 550h105l-116 -524q-2 -16 -2 -22q0 -19 13 -29.5t35 -10.5q25 0 40 13l6 -84q-26 -22 -76 -22z" />
    <glyph glyph-name="a.alt1" horiz-adv-x="537" 
d="M422 0h-105l11 51q-63 -63 -147 -63q-67 0 -113.5 36.5t-46.5 103.5q0 81 52 129.5t127 48.5q104 0 169 -68l15 66q2 8 2 25q0 35 -33 57t-86 22q-74 0 -137 -50l-28 75q83 62 186 62q89 0 147.5 -36.5t58.5 -109.5q0 -13 -6 -49zM227 60q67 0 113 48l17 72
q-34 54 -122 54q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -57t71.5 -20z" />
    <glyph glyph-name="y.alt1" horiz-adv-x="567" 
d="M199 -196q-151 0 -227 83l64 70q22 -33 66 -50t95 -17q117 0 149 135l12 55q-86 -76 -164 -76q-65 0 -106.5 31t-41.5 90q0 21 5 41l70 317h105l-64 -287q-4 -24 -4 -31q0 -68 90 -68q65 0 128 65l71 321h105l-102 -458q-23 -105 -86.5 -163t-164.5 -58z" />
    <glyph glyph-name="G.alt1" horiz-adv-x="717" 
d="M356 -12q-131 0 -218 82.5t-87 213.5q0 174 116 284t282 110q93 0 162.5 -44.5t101.5 -112.5l-106 -46q-22 45 -69.5 72t-104.5 27q-104 0 -181.5 -81t-77.5 -202q0 -89 54 -144t142 -55q80 0 136.5 46t82.5 118h-237l23 103h359q-31 -180 -128.5 -275.5t-249.5 -95.5z
" />
    <glyph glyph-name="G.smcp.alt1" horiz-adv-x="637" 
d="M316 -12q-121 0 -199 69.5t-78 179.5q0 138 98 231.5t231 93.5q76 0 136.5 -34t95.5 -93l-97 -45q-20 35 -58 55.5t-82 20.5q-85 0 -148.5 -63.5t-63.5 -155.5q0 -74 49 -118.5t124 -44.5q57 0 104 34.5t63 91.5h-185l20 90h294q-17 -140 -102.5 -226t-201.5 -86z" />
    <glyph glyph-name="Gbreve.alt1" horiz-adv-x="738" 
d="M693 806q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q33 -67 113 -67t142 67zM356 -12q-131 0 -218 82.5t-87 213.5q0 174 116 284t282 110q93 0 162.5 -44.5t101.5 -112.5l-106 -46q-22 45 -69.5 72t-104.5 27q-104 0 -181.5 -81t-77.5 -202q0 -89 54 -144t142 -55
q80 0 136.5 46t82.5 118h-237l23 103h359q-31 -180 -128.5 -275.5t-249.5 -95.5z" />
    <glyph glyph-name="Gbreve.smcp.alt1" horiz-adv-x="633" 
d="M612 687q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q34 -67 113 -67q80 0 142 67zM316 -12q-121 0 -199 69.5t-78 179.5q0 138 98 231.5t231 93.5q76 0 136.5 -34t95.5 -93l-97 -45q-20 35 -58 55.5t-82 20.5q-85 0 -148.5 -63.5t-63.5 -155.5q0 -74 49 -118.5
t124 -44.5q57 0 104 34.5t63 91.5h-185l20 90h294q-17 -140 -102.5 -226t-201.5 -86z" />
    <glyph glyph-name="Gcommaaccent.alt1" horiz-adv-x="738" 
d="M372 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM356 -12q-131 0 -218 82.5t-87 213.5q0 174 116 284t282 110q93 0 162.5 -44.5t101.5 -112.5l-106 -46q-22 45 -69.5 72
t-104.5 27q-104 0 -181.5 -81t-77.5 -202q0 -89 54 -144t142 -55q80 0 136.5 46t82.5 118h-237l23 103h359q-31 -180 -128.5 -275.5t-249.5 -95.5z" />
    <glyph glyph-name="Gcommaaccent.smcp.alt1" horiz-adv-x="637" 
d="M318 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM316 -12q-121 0 -199 69.5t-78 179.5q0 138 98 231.5t231 93.5q76 0 136.5 -34t95.5 -93l-97 -45q-20 35 -58 55.5t-82 20.5
q-85 0 -148.5 -63.5t-63.5 -155.5q0 -74 49 -118.5t124 -44.5q57 0 104 34.5t63 91.5h-185l20 90h294q-17 -140 -102.5 -226t-201.5 -86z" />
    <glyph glyph-name="lslash.alt1" horiz-adv-x="314" 
d="M2 214l18 79l98 50l72 324h105l-58 -264l99 51l-17 -78l-100 -51l-40 -182q-2 -16 -2 -22q0 -19 13 -29.5t35 -10.5q25 0 40 13l6 -84q-26 -22 -76 -22q-61 0 -95.5 25t-34.5 69q0 19 4 35l32 148z" />
    <glyph glyph-name="lacute.alt1" horiz-adv-x="286" 
d="M425 872l-197 -144h-77l172 144h102zM166 -12q-61 0 -95.5 25t-34.5 69q0 19 4 35l121 550h105l-116 -524q-2 -16 -2 -22q0 -19 13 -29.5t35 -10.5q25 0 40 13l6 -84q-26 -22 -76 -22z" />
    <glyph glyph-name="lcommaaccent.alt1" horiz-adv-x="286" 
d="M113 -121q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM166 -12q-61 0 -95.5 25t-34.5 69q0 19 4 35l121 550h105l-116 -524q-2 -16 -2 -22q0 -19 13 -29.5t35 -10.5q25 0 40 13l6 -84
q-26 -22 -76 -22z" />
    <glyph glyph-name="lcaron.alt1" horiz-adv-x="331" 
d="M431 622q0 -43 -32 -87t-79 -67l-32 33q25 12 47.5 32t31.5 41h-8q-18 0 -31 12.5t-13 33.5q0 24 19 42t43 18q23 0 38.5 -15.5t15.5 -42.5zM166 -12q-61 0 -95.5 25t-34.5 69q0 19 4 35l121 550h105l-116 -524q-2 -16 -2 -22q0 -19 13 -29.5t35 -10.5q25 0 40 13l6 -84
q-26 -22 -76 -22z" />
    <glyph glyph-name="aacute.alt1" horiz-adv-x="537" 
d="M544 700l-197 -144h-77l172 144h102zM422 0h-105l11 51q-63 -63 -147 -63q-67 0 -113.5 36.5t-46.5 103.5q0 81 52 129.5t127 48.5q104 0 169 -68l15 66q2 8 2 25q0 35 -33 57t-86 22q-74 0 -137 -50l-28 75q83 62 186 62q89 0 147.5 -36.5t58.5 -109.5q0 -13 -6 -49z
M227 60q67 0 113 48l17 72q-34 54 -122 54q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -57t71.5 -20z" />
    <glyph glyph-name="agrave.alt1" horiz-adv-x="537" 
d="M419 556h-75l-133 144h100zM422 0h-105l11 51q-63 -63 -147 -63q-67 0 -113.5 36.5t-46.5 103.5q0 81 52 129.5t127 48.5q104 0 169 -68l15 66q2 8 2 25q0 35 -33 57t-86 22q-74 0 -137 -50l-28 75q83 62 186 62q89 0 147.5 -36.5t58.5 -109.5q0 -13 -6 -49zM227 60
q67 0 113 48l17 72q-34 54 -122 54q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -57t71.5 -20z" />
    <glyph glyph-name="acircumflex.alt1" horiz-adv-x="537" 
d="M490 556h-64l-57 97l-95 -97h-69l125 144h96zM422 0h-105l11 51q-63 -63 -147 -63q-67 0 -113.5 36.5t-46.5 103.5q0 81 52 129.5t127 48.5q104 0 169 -68l15 66q2 8 2 25q0 35 -33 57t-86 22q-74 0 -137 -50l-28 75q83 62 186 62q89 0 147.5 -36.5t58.5 -109.5
q0 -13 -6 -49zM227 60q67 0 113 48l17 72q-34 54 -122 54q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -57t71.5 -20z" />
    <glyph glyph-name="adieresis.alt1" horiz-adv-x="537" 
d="M518 615q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM309 615q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM422 0h-105l11 51q-63 -63 -147 -63q-67 0 -113.5 36.5
t-46.5 103.5q0 81 52 129.5t127 48.5q104 0 169 -68l15 66q2 8 2 25q0 35 -33 57t-86 22q-74 0 -137 -50l-28 75q83 62 186 62q89 0 147.5 -36.5t58.5 -109.5q0 -13 -6 -49zM227 60q67 0 113 48l17 72q-34 54 -122 54q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -57t71.5 -20z
" />
    <glyph glyph-name="atilde.alt1" horiz-adv-x="537" 
d="M415 554q-31 0 -50 19.5t-34 39t-35 19.5q-36 0 -52 -72h-59q28 134 118 134q24 0 42 -12t26.5 -27t22 -27t29.5 -12q36 0 53 72h58q-28 -134 -119 -134zM422 0h-105l11 51q-63 -63 -147 -63q-67 0 -113.5 36.5t-46.5 103.5q0 81 52 129.5t127 48.5q104 0 169 -68l15 66
q2 8 2 25q0 35 -33 57t-86 22q-74 0 -137 -50l-28 75q83 62 186 62q89 0 147.5 -36.5t58.5 -109.5q0 -13 -6 -49zM227 60q67 0 113 48l17 72q-34 54 -122 54q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -57t71.5 -20z" />
    <glyph glyph-name="aring.alt1" horiz-adv-x="537" 
d="M356 550q-41 0 -67 26t-26 67q0 46 33.5 79t78.5 33q41 0 67 -26t26 -67q0 -45 -33 -78.5t-79 -33.5zM360 601q23 0 40 16.5t17 39.5q0 21 -12.5 34t-33.5 13q-23 0 -40 -16.5t-17 -39.5q0 -20 13 -33.5t33 -13.5zM422 0h-105l11 51q-63 -63 -147 -63q-67 0 -113.5 36.5
t-46.5 103.5q0 81 52 129.5t127 48.5q104 0 169 -68l15 66q2 8 2 25q0 35 -33 57t-86 22q-74 0 -137 -50l-28 75q83 62 186 62q89 0 147.5 -36.5t58.5 -109.5q0 -13 -6 -49zM227 60q67 0 113 48l17 72q-34 54 -122 54q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -57t71.5 -20z
" />
    <glyph glyph-name="amacron.alt1" horiz-adv-x="537" 
d="M531 591h-362l14 62h362zM422 0h-105l11 51q-63 -63 -147 -63q-67 0 -113.5 36.5t-46.5 103.5q0 81 52 129.5t127 48.5q104 0 169 -68l15 66q2 8 2 25q0 35 -33 57t-86 22q-74 0 -137 -50l-28 75q83 62 186 62q89 0 147.5 -36.5t58.5 -109.5q0 -13 -6 -49zM227 60
q67 0 113 48l17 72q-34 54 -122 54q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -57t71.5 -20z" />
    <glyph glyph-name="abreve.alt1" horiz-adv-x="537" 
d="M538 647q-85 -88 -194 -88q-53 0 -94 23.5t-63 64.5l55 41q34 -67 113 -67q80 0 142 67zM422 0h-105l11 51q-63 -63 -147 -63q-67 0 -113.5 36.5t-46.5 103.5q0 81 52 129.5t127 48.5q104 0 169 -68l15 66q2 8 2 25q0 35 -33 57t-86 22q-74 0 -137 -50l-28 75
q83 62 186 62q89 0 147.5 -36.5t58.5 -109.5q0 -13 -6 -49zM227 60q67 0 113 48l17 72q-34 54 -122 54q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -57t71.5 -20z" />
    <glyph glyph-name="aogonek.alt1" horiz-adv-x="537" 
d="M422 0q-98 -43 -98 -103q0 -17 10.5 -26.5t28.5 -9.5q30 0 51 37l38 -31q-39 -53 -93 -53q-39 0 -64 19.5t-25 54.5q0 66 65 112h-18l11 51q-63 -63 -147 -63q-67 0 -113.5 36.5t-46.5 103.5q0 81 52 129.5t127 48.5q104 0 169 -68l15 66q2 8 2 25q0 35 -33 57t-86 22
q-74 0 -137 -50l-28 75q83 62 186 62q89 0 147.5 -36.5t58.5 -109.5q0 -13 -6 -49zM227 60q67 0 113 48l17 72q-34 54 -122 54q-48 0 -77 -27.5t-29 -69.5q0 -37 26.5 -57t71.5 -20z" />
    <glyph glyph-name="yacute.alt1" horiz-adv-x="567" 
d="M549 700l-197 -144h-77l172 144h102zM199 -196q-151 0 -227 83l64 70q22 -33 66 -50t95 -17q117 0 149 135l12 55q-86 -76 -164 -76q-65 0 -106.5 31t-41.5 90q0 21 5 41l70 317h105l-64 -287q-4 -24 -4 -31q0 -68 90 -68q65 0 128 65l71 321h105l-102 -458
q-23 -105 -86.5 -163t-164.5 -58z" />
    <glyph glyph-name="ydieresis.alt1" horiz-adv-x="567" 
d="M525 615q0 -26 -20 -45.5t-46 -19.5q-23 0 -37.5 15t-14.5 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM316 615q0 -26 -20 -45.5t-46 -19.5q-22 0 -37 15t-15 37q0 26 20 45.5t46 19.5q22 0 37 -15t15 -37zM199 -196q-151 0 -227 83l64 70q22 -33 66 -50t95 -17
q117 0 149 135l12 55q-86 -76 -164 -76q-65 0 -106.5 31t-41.5 90q0 21 5 41l70 317h105l-64 -287q-4 -24 -4 -31q0 -68 90 -68q65 0 128 65l71 321h105l-102 -458q-23 -105 -86.5 -163t-164.5 -58z" />
    <glyph glyph-name="ae.alt1" horiz-adv-x="886" 
d="M843 207h-373v-3q0 -3 -0.5 -6.5t-0.5 -6.5q0 -46 39 -81.5t107 -35.5q78 0 129 41l33 -74q-70 -53 -161 -53q-146 0 -209 101q-101 -101 -217 -101q-71 0 -120 36t-49 104q0 83 53 130.5t126 47.5q114 0 169 -66l15 64q2 9 2 25q0 37 -33.5 58t-85.5 21q-71 0 -137 -50
l-28 75q81 62 186 62q147 0 166 -104q29 47 79 75.5t114 28.5q88 0 146.5 -56.5t58.5 -156.5q0 -34 -9 -75zM483 282h274q1 5 1 14q0 49 -34.5 81t-90.5 32q-61 0 -98 -37t-52 -90zM361 132q-5 18 -5 41v8q-14 24 -49 38.5t-72 14.5q-46 0 -76 -27t-30 -70q0 -37 26.5 -57
t71.5 -20q77 0 134 72z" />
    <glyph glyph-name="zero.pzero" horiz-adv-x="617" 
d="M282 -12q-98 0 -159 58l-40 -46h-87l87 100q-33 62 -33 153q0 106 39.5 202.5t115.5 159t170 62.5q96 0 156 -57l41 47h86l-87 -100q34 -63 34 -154q0 -106 -39 -202.5t-114.5 -159.5t-169.5 -63zM292 92q58 0 104 53.5t68 127t22 146.5q0 16 -4 44l-284 -329
q31 -42 94 -42zM168 247q0 -23 3 -44l284 329q-32 41 -92 41q-58 0 -104 -53t-68.5 -126t-22.5 -147z" />
    <glyph glyph-name="Gcircumflex.alt1" horiz-adv-x="738" 
d="M639 728h-64l-57 97l-95 -97h-69l125 144h96zM356 -12q-131 0 -218 82.5t-87 213.5q0 174 116 284t282 110q93 0 162.5 -44.5t101.5 -112.5l-106 -46q-22 45 -69.5 72t-104.5 27q-104 0 -181.5 -81t-77.5 -202q0 -89 54 -144t142 -55q80 0 136.5 46t82.5 118h-237l23 103
h359q-31 -180 -128.5 -275.5t-249.5 -95.5z" />
    <glyph glyph-name="Gdotaccent.alt1" horiz-adv-x="738" 
d="M568 787q0 -26 -20.5 -46t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5t46 19.5q23 0 38 -15t15 -37zM356 -12q-131 0 -218 82.5t-87 213.5q0 174 116 284t282 110q93 0 162.5 -44.5t101.5 -112.5l-106 -46q-22 45 -69.5 72t-104.5 27q-104 0 -181.5 -81t-77.5 -202
q0 -89 54 -144t142 -55q80 0 136.5 46t82.5 118h-237l23 103h359q-31 -180 -128.5 -275.5t-249.5 -95.5z" />
    <glyph glyph-name="Gcircumflex.smcp.alt1" horiz-adv-x="620" 
d="M316 -12q-121 0 -199 69.5t-78 179.5q0 138 98 231.5t231 93.5q76 0 136.5 -34t95.5 -93l-97 -45q-20 35 -58 55.5t-82 20.5q-85 0 -148.5 -63.5t-63.5 -155.5q0 -74 49 -118.5t124 -44.5q57 0 104 34.5t63 91.5h-185l20 90h294q-17 -140 -102.5 -226t-201.5 -86z
M555 606h-64l-57 97l-95 -97h-69l125 144h96z" />
    <glyph glyph-name="Gdotaccent.smcp.alt1" horiz-adv-x="620" 
d="M316 -12q-121 0 -199 69.5t-78 179.5q0 138 98 231.5t231 93.5q76 0 136.5 -34t95.5 -93l-97 -45q-20 35 -58 55.5t-82 20.5q-85 0 -148.5 -63.5t-63.5 -155.5q0 -74 49 -118.5t124 -44.5q57 0 104 34.5t63 91.5h-185l20 90h294q-17 -140 -102.5 -226t-201.5 -86z
M493 686q0 -26 -20.5 -46t-46.5 -20q-22 0 -37 15t-15 38q0 26 20 45.5t46 19.5q23 0 38 -15t15 -37z" />
    <glyph glyph-name="aeacute.alt1" horiz-adv-x="886" 
d="M843 207h-373v-3q0 -3 -0.5 -6.5t-0.5 -6.5q0 -46 39 -81.5t107 -35.5q78 0 129 41l33 -74q-70 -53 -161 -53q-146 0 -209 101q-101 -101 -217 -101q-71 0 -120 36t-49 104q0 83 53 130.5t126 47.5q114 0 169 -66l15 64q2 9 2 25q0 37 -33.5 58t-85.5 21q-71 0 -137 -50
l-28 75q81 62 186 62q147 0 166 -104q29 47 79 75.5t114 28.5q88 0 146.5 -56.5t58.5 -156.5q0 -34 -9 -75zM483 282h274q1 5 1 14q0 49 -34.5 81t-90.5 32q-61 0 -98 -37t-52 -90zM361 132q-5 18 -5 41v8q-14 24 -49 38.5t-72 14.5q-46 0 -76 -27t-30 -70q0 -37 26.5 -57
t71.5 -20q77 0 134 72zM723 700l-197 -144h-77l172 144h102z" />
    <glyph glyph-name="ycircumflex.alt1" horiz-adv-x="567" 
d="M497 556h-64l-57 97l-95 -97h-69l125 144h96zM199 -196q-151 0 -227 83l64 70q22 -33 66 -50t95 -17q117 0 149 135l12 55q-86 -76 -164 -76q-65 0 -106.5 31t-41.5 90q0 21 5 41l70 317h105l-64 -287q-4 -24 -4 -31q0 -68 90 -68q65 0 128 65l71 321h105l-102 -458
q-23 -105 -86.5 -163t-164.5 -58z" />
    <glyph glyph-name="ldot.alt1" horiz-adv-x="351" 
d="M166 -12q-61 0 -95.5 25t-34.5 69q0 19 4 35l121 550h105l-116 -524q-2 -16 -2 -22q0 -19 13 -29.5t35 -10.5q25 0 40 13l6 -84q-26 -22 -76 -22zM290 175q-26 0 -44.5 19t-18.5 46q0 31 22.5 53t53.5 22q27 0 45.5 -19t18.5 -45q0 -31 -22.5 -53.5t-54.5 -22.5z" />
    <glyph glyph-name="fl.alt1" horiz-adv-x="594" 
d="M145 0h-105l86 392h-80l21 91h79l6 27q36 167 172 167q58 0 98 -25l-38 -75q-18 14 -49 14q-60 0 -78 -81l-6 -27h99l-21 -91h-98zM475 -12q-61 0 -95.5 25t-34.5 69q0 19 4 35l121 550h105l-116 -524q-2 -16 -2 -22q0 -19 13 -29.5t35 -10.5q25 0 40 13l6 -84
q-26 -22 -76 -22z" />
    <glyph glyph-name="f_f_l.alt1" horiz-adv-x="903" 
d="M145 0h-105l86 392h-80l21 91h79l6 28q40 166 172 166q77 0 122 -45l-54 -63q-22 22 -57 22q-32 0 -50.5 -19.5t-27.5 -61.5l-6 -27h99l-21 -91h-98zM454 0h-105l86 392h-80l21 91h79l6 27q36 167 172 167q58 0 98 -25l-38 -75q-18 14 -49 14q-60 0 -78 -81l-6 -27h99
l-21 -91h-98zM783 -12q-61 0 -95.5 25t-34.5 69q0 19 4 35l121 550h105l-116 -524q-2 -16 -2 -22q0 -19 13 -29.5t35 -10.5q25 0 40 13l6 -84q-26 -22 -76 -22z" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="J,Jcircumflex"
	k="-15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="75" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="V"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="W,Wcircumflex"
	k="53" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="75" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="question"
	k="75" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="asterisk"
	k="95" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="bullet.case"
	k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="dagger"
	k="95" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="daggerdbl"
	k="35" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="95" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="f,longs,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="t,tcommaaccent,tcaron,tbar"
	k="18" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="u,uogonek,y.alt1"
	k="13" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="v"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="w"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="y,yacute,ydieresis,ycircumflex"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="V.smcp"
	k="35" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="Wcircumflex.smcp,W.smcp"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Delta"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="55" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="110" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="53" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="15" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="V"
	k="60" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="W,Wcircumflex"
	k="55" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="90" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="question"
	k="55" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="asterisk"
	k="60" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="dagger"
	k="75" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="daggerdbl"
	k="20" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="80" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="25" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	k="18" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="V.smcp"
	k="38" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="Wcircumflex.smcp,W.smcp"
	k="24" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="55" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-10" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	k="10" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="bullet"
	k="18" />
    <hkern g1="Abreve.smcp,Amacron.smcp,Aogonek.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp"
	g2="question.smcp"
	k="45" />
    <hkern g1="B"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="15" />
    <hkern g1="B"
	g2="V"
	k="15" />
    <hkern g1="B"
	g2="W,Wcircumflex"
	k="10" />
    <hkern g1="B"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="30" />
    <hkern g1="B"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="B"
	g2="Jcircumflex.smcp,J.smcp"
	k="15" />
    <hkern g1="B"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="10" />
    <hkern g1="B.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="40" />
    <hkern g1="B.smcp"
	g2="V"
	k="20" />
    <hkern g1="B.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="40" />
    <hkern g1="B.smcp"
	g2="question"
	k="45" />
    <hkern g1="B.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="25" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="-5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="-5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="V"
	k="13" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="W,Wcircumflex"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="question"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="bullet.case"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="5" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="75" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="5" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="V"
	k="30" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="W,Wcircumflex"
	k="25" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="65" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="question"
	k="25" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="V.smcp"
	k="10" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Wcircumflex.smcp,W.smcp"
	k="10" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="15" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-5" />
    <hkern g1="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,C.smcp,Ccedilla.smcp"
	g2="X.smcp"
	k="5" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="J,Jcircumflex"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="38" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="V"
	k="35" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="W,Wcircumflex"
	k="33" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="60" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="question"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Jcircumflex.smcp,J.smcp"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="X"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="15" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Thorn,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="25" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="75" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="15" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp"
	g2="V"
	k="50" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="80" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp"
	g2="W,Wcircumflex"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp"
	g2="X"
	k="50" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="35" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp"
	g2="V.smcp"
	k="25" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp"
	g2="Wcircumflex.smcp,W.smcp"
	k="33" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp"
	g2="X.smcp"
	k="25" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="35" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="15" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp"
	g2="question"
	k="50" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="30" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="5" />
    <hkern g1="Dcaron.smcp,Dcroat.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,D.smcp,O.smcp,Q.smcp,Eth.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,Thorn.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="15" />
    <hkern g1="Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,OE.smcp,E.smcp,AE.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="10" />
    <hkern g1="F"
	g2="X.smcp"
	k="10" />
    <hkern g1="F"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="50" />
    <hkern g1="F"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="45" />
    <hkern g1="F"
	g2="Jcircumflex.smcp,J.smcp"
	k="60" />
    <hkern g1="F"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="40" />
    <hkern g1="F"
	g2="J,Jcircumflex"
	k="65" />
    <hkern g1="F"
	g2="ampersand"
	k="10" />
    <hkern g1="F"
	g2="ampersand.smcp"
	k="25" />
    <hkern g1="F.smcp"
	g2="X"
	k="25" />
    <hkern g1="F.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="15" />
    <hkern g1="F.smcp"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="25" />
    <hkern g1="F.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="50" />
    <hkern g1="F.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="10" />
    <hkern g1="F.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="25" />
    <hkern g1="F.smcp"
	g2="J,Jcircumflex"
	k="40" />
    <hkern g1="F.smcp"
	g2="ampersand.smcp"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="V"
	k="25" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="35" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="W,Wcircumflex"
	k="13" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="X"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="question"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="5" />
    <hkern g1="Gbreve.smcp,Gcommaaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="40" />
    <hkern g1="Gbreve.smcp,Gcommaaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="5" />
    <hkern g1="Gbreve.smcp,Gcommaaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="20" />
    <hkern g1="Gbreve.smcp,Gcommaaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1"
	g2="question"
	k="35" />
    <hkern g1="Gbreve.smcp,Gcommaaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="5" />
    <hkern g1="Gbreve.smcp,Gcommaaccent.smcp,G.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="60" />
    <hkern g1="Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp"
	g2="V"
	k="20" />
    <hkern g1="Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="35" />
    <hkern g1="Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp"
	g2="W,Wcircumflex"
	k="5" />
    <hkern g1="Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp"
	g2="question"
	k="5" />
    <hkern g1="Hbar.smcp,Imacron.smcp,Iogonek.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Idotaccent.smcp,H.smcp,I.smcp,M.smcp,N.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Ntilde.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="45" />
    <hkern g1="K,Kcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="55" />
    <hkern g1="K,Kcommaaccent"
	g2="V.smcp"
	k="45" />
    <hkern g1="K,Kcommaaccent"
	g2="Wcircumflex.smcp,W.smcp"
	k="40" />
    <hkern g1="K,Kcommaaccent"
	g2="X.smcp"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="60" />
    <hkern g1="K,Kcommaaccent"
	g2="ampersand.smcp"
	k="5" />
    <hkern g1="K,Kcommaaccent"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="50" />
    <hkern g1="K,Kcommaaccent"
	g2="bullet.case"
	k="70" />
    <hkern g1="K,Kcommaaccent"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="70" />
    <hkern g1="K,Kcommaaccent"
	g2="a,c,d,e,g,o,q,ae,ccedilla,eth,oslash,aogonek,dcaron,eogonek,oe,aeacute"
	k="30" />
    <hkern g1="K,Kcommaaccent"
	g2="f,longs,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="30" />
    <hkern g1="K,Kcommaaccent"
	g2="t,tcommaaccent,tcaron,tbar"
	k="45" />
    <hkern g1="K,Kcommaaccent"
	g2="u,uogonek,y.alt1"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="v"
	k="60" />
    <hkern g1="K,Kcommaaccent"
	g2="w"
	k="40" />
    <hkern g1="K,Kcommaaccent"
	g2="x"
	k="30" />
    <hkern g1="K,Kcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex"
	k="60" />
    <hkern g1="K,Kcommaaccent"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="K,Kcommaaccent"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="55" />
    <hkern g1="K,Kcommaaccent"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	k="50" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="V"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="30" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="W,Wcircumflex"
	k="15" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="15" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="50" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="25" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="35" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="45" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="Kcommaaccent.smcp,K.smcp"
	g2="bullet"
	k="45" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="V"
	k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="125" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="W,Wcircumflex"
	k="70" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="question"
	k="110" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="130" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="100" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="ampersand"
	k="5" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="bullet.case"
	k="110" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="75" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="a,c,d,e,g,o,q,ae,ccedilla,eth,oslash,aogonek,dcaron,eogonek,oe,aeacute"
	k="15" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="t,tcommaaccent,tcaron,tbar"
	k="35" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="u,uogonek,y.alt1"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="25" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="asterisk"
	k="170" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="dagger"
	k="130" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="daggerdbl"
	k="95" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="v"
	k="55" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="w"
	k="35" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="y,yacute,ydieresis,ycircumflex"
	k="55" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="35" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="75" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="V.smcp"
	k="55" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Wcircumflex.smcp,W.smcp"
	k="45" />
    <hkern g1="L,Lacute,Lcommaaccent,Lslash"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="90" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="40" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="85" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	k="15" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="V.smcp"
	k="80" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Wcircumflex.smcp,W.smcp"
	k="60" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="100" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="asterisk"
	k="115" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="50" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="105" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="V"
	k="100" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="W,Wcircumflex"
	k="55" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="130" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="110" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="dagger"
	k="100" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="question"
	k="75" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="question.smcp"
	k="55" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="bullet"
	k="30" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="daggerdbl"
	k="30" />
    <hkern g1="Lacute.smcp,Lcommaaccent.smcp,Lslash.smcp,L.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="30" />
    <hkern g1="P"
	g2="V"
	k="5" />
    <hkern g1="P"
	g2="W,Wcircumflex"
	k="5" />
    <hkern g1="P"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="20" />
    <hkern g1="P"
	g2="hyphen,periodcentered,endash,emdash"
	k="30" />
    <hkern g1="P"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="75" />
    <hkern g1="P"
	g2="J,Jcircumflex"
	k="115" />
    <hkern g1="P"
	g2="X"
	k="15" />
    <hkern g1="P"
	g2="ampersand"
	k="30" />
    <hkern g1="P"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="P"
	g2="a,c,d,e,g,o,q,ae,ccedilla,eth,oslash,aogonek,dcaron,eogonek,oe,aeacute"
	k="15" />
    <hkern g1="P"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="80" />
    <hkern g1="P"
	g2="Jcircumflex.smcp,J.smcp"
	k="100" />
    <hkern g1="P"
	g2="ampersand.smcp"
	k="40" />
    <hkern g1="P.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="35" />
    <hkern g1="P.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="45" />
    <hkern g1="P.smcp"
	g2="J,Jcircumflex"
	k="30" />
    <hkern g1="P.smcp"
	g2="X"
	k="35" />
    <hkern g1="P.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="50" />
    <hkern g1="P.smcp"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="45" />
    <hkern g1="P.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="75" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="V"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="22" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand"
	k="5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,c,d,e,g,o,q,ae,ccedilla,eth,oslash,aogonek,dcaron,eogonek,oe,aeacute"
	k="30" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand.smcp"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="bullet.case"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="s,scedilla,scommaaccent"
	k="10" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="5" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="V.smcp"
	k="5" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="20" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="45" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="55" />
    <hkern g1="Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,R.smcp"
	g2="question"
	k="35" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="v"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="w"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="y,yacute,ydieresis,ycircumflex"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="V.smcp"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="Wcircumflex.smcp,W.smcp"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="V"
	k="13" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="W,Wcircumflex"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="33" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="t,tcommaaccent,tcaron,tbar"
	k="15" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="x"
	k="15" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="15" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	g2="V.smcp"
	k="10" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	g2="Wcircumflex.smcp,W.smcp"
	k="3" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="20" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="70" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	g2="V"
	k="25" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	g2="W,Wcircumflex"
	k="5" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="40" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	g2="question"
	k="30" />
    <hkern g1="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="-10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="13" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="55" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="J,Jcircumflex"
	k="85" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="ampersand"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="95" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="a,c,d,e,g,o,q,ae,ccedilla,eth,oslash,aogonek,dcaron,eogonek,oe,aeacute"
	k="105" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="bullet.case"
	k="70" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="-5" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="f,longs,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="m,n,p,r,z,ncommaaccent,eng,rcommaaccent"
	k="75" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="s,scedilla,scommaaccent"
	k="85" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="v"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="w"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="x"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="y,yacute,ydieresis,ycircumflex"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="hyphen,periodcentered,endash,emdash"
	k="60" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="90" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="65" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp"
	k="45" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="Jcircumflex.smcp,J.smcp"
	k="105" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="20" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="V.smcp"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="Wcircumflex.smcp,W.smcp"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="X.smcp"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="30" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="ampersand.smcp"
	k="85" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
	g2="u,uogonek,y.alt1"
	k="75" />
    <hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="30" />
    <hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="38" />
    <hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="15" />
    <hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="75" />
    <hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	k="-5" />
    <hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	g2="ampersand.smcp"
	k="35" />
    <hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="30" />
    <hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="20" />
    <hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	g2="V"
	k="5" />
    <hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="60" />
    <hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	g2="ampersand"
	k="35" />
    <hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	g2="J,Jcircumflex"
	k="65" />
    <hkern g1="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	g2="bullet"
	k="30" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="15" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="Jcircumflex.smcp,J.smcp"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="J,Jcircumflex"
	k="20" />
    <hkern g1="IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="3" />
    <hkern g1="IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="-5" />
    <hkern g1="IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="10" />
    <hkern g1="IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="40" />
    <hkern g1="IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	g2="V"
	k="20" />
    <hkern g1="IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	g2="question"
	k="15" />
    <hkern g1="IJ.smcp,Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,J.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="25" />
    <hkern g1="V"
	g2="f,longs,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="25" />
    <hkern g1="V"
	g2="m,n,p,r,z,ncommaaccent,eng,rcommaaccent"
	k="50" />
    <hkern g1="V"
	g2="s,scedilla,scommaaccent"
	k="45" />
    <hkern g1="V"
	g2="v"
	k="15" />
    <hkern g1="V"
	g2="w"
	k="10" />
    <hkern g1="V"
	g2="x"
	k="25" />
    <hkern g1="V"
	g2="y,yacute,ydieresis,ycircumflex"
	k="10" />
    <hkern g1="V"
	g2="hyphen,periodcentered,endash,emdash"
	k="55" />
    <hkern g1="V"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="40" />
    <hkern g1="V"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="35" />
    <hkern g1="V"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp"
	k="20" />
    <hkern g1="V"
	g2="Jcircumflex.smcp,J.smcp"
	k="90" />
    <hkern g1="V"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	k="20" />
    <hkern g1="V"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="5" />
    <hkern g1="V"
	g2="Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	k="20" />
    <hkern g1="V"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="5" />
    <hkern g1="V"
	g2="ampersand.smcp"
	k="45" />
    <hkern g1="V"
	g2="u,uogonek,y.alt1"
	k="50" />
    <hkern g1="V"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="40" />
    <hkern g1="V"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="V"
	g2="ampersand"
	k="20" />
    <hkern g1="V"
	g2="J,Jcircumflex"
	k="95" />
    <hkern g1="V"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="5" />
    <hkern g1="V"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="30" />
    <hkern g1="V"
	g2="bullet.case"
	k="35" />
    <hkern g1="V"
	g2="a,c,d,e,g,o,q,ae,ccedilla,eth,oslash,aogonek,dcaron,eogonek,oe,aeacute"
	k="60" />
    <hkern g1="V"
	g2="t,tcommaaccent,tcaron,tbar"
	k="15" />
    <hkern g1="V"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="-13" />
    <hkern g1="V.smcp"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="23" />
    <hkern g1="V.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="5" />
    <hkern g1="V.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="65" />
    <hkern g1="V.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	k="-10" />
    <hkern g1="V.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="15" />
    <hkern g1="V.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="25" />
    <hkern g1="V.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="65" />
    <hkern g1="V.smcp"
	g2="ampersand"
	k="20" />
    <hkern g1="V.smcp"
	g2="J,Jcircumflex"
	k="50" />
    <hkern g1="V.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="5" />
    <hkern g1="W"
	g2="f,longs,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="15" />
    <hkern g1="W"
	g2="m,n,p,r,z,ncommaaccent,eng,rcommaaccent"
	k="30" />
    <hkern g1="W"
	g2="s,scedilla,scommaaccent"
	k="25" />
    <hkern g1="W"
	g2="u,uogonek,y.alt1"
	k="30" />
    <hkern g1="W"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="38" />
    <hkern g1="W"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="65" />
    <hkern g1="W"
	g2="ampersand"
	k="10" />
    <hkern g1="W"
	g2="J,Jcircumflex"
	k="50" />
    <hkern g1="W"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="8" />
    <hkern g1="W"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="5" />
    <hkern g1="W"
	g2="bullet.case"
	k="13" />
    <hkern g1="W"
	g2="a,c,d,e,g,o,q,ae,ccedilla,eth,oslash,aogonek,dcaron,eogonek,oe,aeacute"
	k="40" />
    <hkern g1="W"
	g2="t,tcommaaccent,tcaron,tbar"
	k="15" />
    <hkern g1="W"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="45" />
    <hkern g1="W"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="20" />
    <hkern g1="W"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp"
	k="5" />
    <hkern g1="W"
	g2="Jcircumflex.smcp,J.smcp"
	k="45" />
    <hkern g1="W"
	g2="x"
	k="25" />
    <hkern g1="W"
	g2="v"
	k="5" />
    <hkern g1="W"
	g2="y,yacute,ydieresis,ycircumflex"
	k="5" />
    <hkern g1="W"
	g2="hyphen,periodcentered,endash,emdash"
	k="35" />
    <hkern g1="W"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="-10" />
    <hkern g1="W"
	g2="ampersand.smcp"
	k="10" />
    <hkern g1="W.smcp"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="16" />
    <hkern g1="W.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="13" />
    <hkern g1="W.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="45" />
    <hkern g1="W.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="W.smcp"
	g2="ampersand.smcp"
	k="5" />
    <hkern g1="W.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="10" />
    <hkern g1="W.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="25" />
    <hkern g1="W.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="55" />
    <hkern g1="W.smcp"
	g2="ampersand"
	k="5" />
    <hkern g1="W.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	k="-3" />
    <hkern g1="W.smcp"
	g2="J,Jcircumflex"
	k="25" />
    <hkern g1="W.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="5" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="Dcaron.smcp,Dcroat.smcp,Ecaron.smcp,Edotaccent.smcp,Emacron.smcp,Eogonek.smcp,Hcircumflex.smcp,Hbar.smcp,Imacron.smcp,Iogonek.smcp,Kcommaaccent.smcp,Lacute.smcp,Lcaron.smcp,Lcommaaccent.smcp,Nacute.smcp,Ncaron.smcp,Ncommaaccent.smcp,Racute.smcp,Rcaron.smcp,Rcommaaccent.smcp,Idotaccent.smcp,Lslash.smcp,B.smcp,D.smcp,E.smcp,F.smcp,H.smcp,I.smcp,K.smcp,L.smcp,M.smcp,N.smcp,P.smcp,R.smcp,Egrave.smcp,Eacute.smcp,Ecircumflex.smcp,Edieresis.smcp,Igrave.smcp,Iacute.smcp,Icircumflex.smcp,Idieresis.smcp,Eth.smcp,Ntilde.smcp"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="Jcircumflex.smcp,J.smcp"
	k="110" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="x"
	k="75" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="v"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="y,yacute,ydieresis,ycircumflex"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="hyphen,periodcentered,endash,emdash"
	k="120" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="8" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="ampersand.smcp"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="55" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="ampersand"
	k="43" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="J,Jcircumflex"
	k="115" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="bullet.case"
	k="75" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="colon,semicolon"
	k="65" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="95" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="a,c,d,e,g,o,q,ae,ccedilla,eth,oslash,aogonek,dcaron,eogonek,oe,aeacute"
	k="125" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="f,longs,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="55" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="m,n,p,r,z,ncommaaccent,eng,rcommaaccent"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="s,scedilla,scommaaccent"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="t,tcommaaccent,tcaron,tbar"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="u,uogonek,y.alt1"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="w"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="V.smcp"
	k="5" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="Wcircumflex.smcp,W.smcp"
	k="5" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis"
	g2="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	k="10" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="25" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="15" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	g2="Jcircumflex.smcp,J.smcp"
	k="95" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	g2="ampersand.smcp"
	k="45" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="45" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="25" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="70" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	g2="ampersand"
	k="45" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	g2="Sacute.smcp,Scedilla.smcp,Scircumflex.smcp,Scommaaccent.smcp,Scaron.smcp,S.smcp"
	k="-10" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	g2="J,Jcircumflex"
	k="70" />
    <hkern g1="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="bullet.case"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="60" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="30" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="20" />
    <hkern g1="Zacute.smcp,Zdotaccent.smcp,Zcaron.smcp,Z.smcp"
	g2="V"
	k="5" />
    <hkern g1="ampersand"
	g2="y,yacute,ydieresis,ycircumflex"
	k="20" />
    <hkern g1="ampersand"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="85" />
    <hkern g1="ampersand"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="103" />
    <hkern g1="ampersand"
	g2="V.smcp"
	k="55" />
    <hkern g1="ampersand"
	g2="Wcircumflex.smcp,W.smcp"
	k="20" />
    <hkern g1="ampersand"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="85" />
    <hkern g1="ampersand"
	g2="V"
	k="65" />
    <hkern g1="ampersand"
	g2="W,Wcircumflex"
	k="50" />
    <hkern g1="ampersand"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="75" />
    <hkern g1="ampersand"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="15" />
    <hkern g1="ampersand.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="110" />
    <hkern g1="ampersand.smcp"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="45" />
    <hkern g1="ampersand.smcp"
	g2="V.smcp"
	k="45" />
    <hkern g1="ampersand.smcp"
	g2="Wcircumflex.smcp,W.smcp"
	k="30" />
    <hkern g1="ampersand.smcp"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="80" />
    <hkern g1="ampersand.smcp"
	g2="V"
	k="80" />
    <hkern g1="ampersand.smcp"
	g2="W,Wcircumflex"
	k="20" />
    <hkern g1="ampersand.smcp"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="75" />
    <hkern g1="ampersand.smcp"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="20" />
    <hkern g1="asterisk"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="60" />
    <hkern g1="asterisk"
	g2="Jcircumflex.smcp,J.smcp"
	k="105" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="95" />
    <hkern g1="asterisk"
	g2="J,Jcircumflex"
	k="120" />
    <hkern g1="b,o,p,oslash,thorn,f_b"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="105" />
    <hkern g1="b,o,p,oslash,thorn,f_b"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="5" />
    <hkern g1="b,o,p,oslash,thorn,f_b"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="120" />
    <hkern g1="b,o,p,oslash,thorn,f_b"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="10" />
    <hkern g1="b,o,p,oslash,thorn,f_b"
	g2="question"
	k="50" />
    <hkern g1="b,o,p,oslash,thorn,f_b"
	g2="V"
	k="60" />
    <hkern g1="b,o,p,oslash,thorn,f_b"
	g2="W,Wcircumflex"
	k="40" />
    <hkern g1="b,o,p,oslash,thorn,f_b"
	g2="x"
	k="35" />
    <hkern g1="bullet"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="3" />
    <hkern g1="bullet"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="50" />
    <hkern g1="bullet.case"
	g2="V"
	k="55" />
    <hkern g1="bullet.case"
	g2="W,Wcircumflex"
	k="28" />
    <hkern g1="bullet.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="25" />
    <hkern g1="bullet.case"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="70" />
    <hkern g1="bullet.case"
	g2="J,Jcircumflex"
	k="80" />
    <hkern g1="bullet.case"
	g2="X"
	k="60" />
    <hkern g1="bullet.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="95" />
    <hkern g1="bullet.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="50" />
    <hkern g1="c,cent,ccedilla"
	g2="V"
	k="40" />
    <hkern g1="c,cent,ccedilla"
	g2="W,Wcircumflex"
	k="25" />
    <hkern g1="c,cent,ccedilla"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="75" />
    <hkern g1="c,cent,ccedilla"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="65" />
    <hkern g1="c,cent,ccedilla"
	g2="question"
	k="25" />
    <hkern g1="c,cent,ccedilla"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="5" />
    <hkern g1="colon,semicolon"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="65" />
    <hkern g1="e,ae,eogonek,oe,aeacute,ae.alt1,aeacute.alt1"
	g2="V"
	k="55" />
    <hkern g1="e,ae,eogonek,oe,aeacute,ae.alt1,aeacute.alt1"
	g2="W,Wcircumflex"
	k="40" />
    <hkern g1="e,ae,eogonek,oe,aeacute,ae.alt1,aeacute.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="95" />
    <hkern g1="e,ae,eogonek,oe,aeacute,ae.alt1,aeacute.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="110" />
    <hkern g1="e,ae,eogonek,oe,aeacute,ae.alt1,aeacute.alt1"
	g2="question"
	k="40" />
    <hkern g1="e,ae,eogonek,oe,aeacute,ae.alt1,aeacute.alt1"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="5" />
    <hkern g1="e,ae,eogonek,oe,aeacute,ae.alt1,aeacute.alt1"
	g2="asterisk"
	k="15" />
    <hkern g1="e,ae,eogonek,oe,aeacute,ae.alt1,aeacute.alt1"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="5" />
    <hkern g1="eth"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="5" />
    <hkern g1="f,longs,f_f,f.alt1"
	g2="V"
	k="-65" />
    <hkern g1="f,longs,f_f,f.alt1"
	g2="W,Wcircumflex"
	k="-65" />
    <hkern g1="f,longs,f_f,f.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="-40" />
    <hkern g1="f,longs,f_f,f.alt1"
	g2="X"
	k="-40" />
    <hkern g1="f,longs,f_f,f.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="-60" />
    <hkern g1="f,longs,f_f,f.alt1"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-40" />
    <hkern g1="f,longs,f_f,f.alt1"
	g2="question"
	k="-55" />
    <hkern g1="f,longs,f_f,f.alt1"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="-65" />
    <hkern g1="f,longs,f_f,f.alt1"
	g2="asterisk"
	k="-65" />
    <hkern g1="f,longs,f_f,f.alt1"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="45" />
    <hkern g1="f,longs,f_f,f.alt1"
	g2="ampersand"
	k="5" />
    <hkern g1="f,longs,f_f,f.alt1"
	g2="ampersand.smcp"
	k="5" />
    <hkern g1="f,longs,f_f,f.alt1"
	g2="parenright,bracketright,braceright,parenright.case,bracketright.case,braceright.case"
	k="-70" />
    <hkern g1="f,longs,f_f,f.alt1"
	g2="exclam,B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Imacron,Iogonek,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Racute,Rcommaaccent,Rcaron"
	k="-40" />
    <hkern g1="f,longs,f_f,f.alt1"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="-25" />
    <hkern g1="f,longs,f_f,f.alt1"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-40" />
    <hkern g1="g,q,eng,y.alt1"
	g2="V"
	k="50" />
    <hkern g1="g,q,eng,y.alt1"
	g2="W,Wcircumflex"
	k="30" />
    <hkern g1="g,q,eng,y.alt1"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="75" />
    <hkern g1="g,q,eng,y.alt1"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="90" />
    <hkern g1="g,q,eng,y.alt1"
	g2="question"
	k="25" />
    <hkern g1="g,q,eng,y.alt1"
	g2="j,jcircumflex"
	k="-50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="V"
	k="30" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="W,Wcircumflex"
	k="5" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="45" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="J,Jcircumflex"
	k="65" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="X"
	k="75" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="95" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="60" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="V"
	k="55" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="W,Wcircumflex"
	k="35" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="30" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="X"
	k="45" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="115" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="50" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Wcircumflex.smcp,W.smcp"
	k="10" />
    <hkern g1="k,kcommaaccent,kgreenlandic,f_k"
	g2="V"
	k="25" />
    <hkern g1="k,kcommaaccent,kgreenlandic,f_k"
	g2="W,Wcircumflex"
	k="25" />
    <hkern g1="k,kcommaaccent,kgreenlandic,f_k"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="55" />
    <hkern g1="k,kcommaaccent,kgreenlandic,f_k"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="70" />
    <hkern g1="k,kcommaaccent,kgreenlandic,f_k"
	g2="a,c,d,e,g,o,q,ae,ccedilla,eth,oslash,aogonek,dcaron,eogonek,oe,aeacute"
	k="5" />
    <hkern g1="k,kcommaaccent,kgreenlandic,f_k"
	g2="bullet"
	k="25" />
    <hkern g1="k,kcommaaccent,kgreenlandic,f_k"
	g2="hyphen,periodcentered,endash,emdash"
	k="25" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V"
	k="85" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="W,Wcircumflex"
	k="65" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Tbar.smcp,Tcaron.smcp,Tcommaaccent.smcp,T.smcp"
	k="60" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="95" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="90" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="80" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="30" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="j,jcircumflex"
	k="-40" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Ycircumflex.smcp,Y.smcp,Yacute.smcp,Ydieresis.smcp"
	k="70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Wcircumflex.smcp,W.smcp"
	k="55" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="a,c,d,e,g,o,q,ae,ccedilla,eth,oslash,aogonek,dcaron,eogonek,oe,aeacute"
	k="5" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="15" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="f,longs,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="25" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="t,tcommaaccent,tcaron,tbar"
	k="25" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="v"
	k="65" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="w"
	k="40" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex"
	k="55" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="one"
	k="90" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="zero,six"
	k="25" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="seven"
	k="70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V.smcp"
	k="65" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Ubreve.smcp,Uhungarumlaut.smcp,Umacron.smcp,Uogonek.smcp,Uring.smcp,U.smcp,Ugrave.smcp,Uacute.smcp,Ucircumflex.smcp,Udieresis.smcp"
	k="10" />
    <hkern g1="questiondown"
	g2="j,jcircumflex"
	k="-160" />
    <hkern g1="questiondown.case"
	g2="V"
	k="75" />
    <hkern g1="questiondown.case"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="100" />
    <hkern g1="questiondown.case"
	g2="W,Wcircumflex"
	k="65" />
    <hkern g1="questiondown.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="95" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="100" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="5" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="Abreve.smcp,Amacron.smcp,Aogonek.smcp,AEacute.smcp,A.smcp,Agrave.smcp,Aacute.smcp,Acircumflex.smcp,Atilde.smcp,Adieresis.smcp,Aring.smcp,AE.smcp"
	k="80" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="20" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="s,scedilla,scommaaccent"
	k="30" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="J,Jcircumflex"
	k="100" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="80" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="ampersand"
	k="-5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="W,Wcircumflex"
	k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="50" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="85" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="40" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="V"
	k="25" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="X"
	k="15" />
    <hkern g1="s,scedilla,scommaaccent"
	g2="W,Wcircumflex"
	k="45" />
    <hkern g1="s,scedilla,scommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="95" />
    <hkern g1="s,scedilla,scommaaccent"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="80" />
    <hkern g1="s,scedilla,scommaaccent"
	g2="V"
	k="45" />
    <hkern g1="s,scedilla,scommaaccent"
	g2="X"
	k="5" />
    <hkern g1="s,scedilla,scommaaccent"
	g2="question"
	k="45" />
    <hkern g1="s,scedilla,scommaaccent"
	g2="quotedbl,quotesingle,registered,quoteleft,quoteright,quotedblleft,quotedblright,trademark"
	k="20" />
    <hkern g1="t,tcommaaccent,tbar"
	g2="W,Wcircumflex"
	k="15" />
    <hkern g1="t,tcommaaccent,tbar"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="35" />
    <hkern g1="t,tcommaaccent,tbar"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="40" />
    <hkern g1="t,tcommaaccent,tbar"
	g2="V"
	k="30" />
    <hkern g1="a,u,z,aogonek,uogonek"
	g2="W,Wcircumflex"
	k="30" />
    <hkern g1="a,u,z,aogonek,uogonek"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="90" />
    <hkern g1="a,u,z,aogonek,uogonek"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="75" />
    <hkern g1="a,u,z,aogonek,uogonek"
	g2="V"
	k="50" />
    <hkern g1="a,u,z,aogonek,uogonek"
	g2="question"
	k="25" />
    <hkern g1="v"
	g2="W,Wcircumflex"
	k="5" />
    <hkern g1="v"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="60" />
    <hkern g1="v"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="20" />
    <hkern g1="v"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="65" />
    <hkern g1="v"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="40" />
    <hkern g1="v"
	g2="V"
	k="15" />
    <hkern g1="v"
	g2="X"
	k="35" />
    <hkern g1="w"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="60" />
    <hkern g1="w"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="15" />
    <hkern g1="w"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="40" />
    <hkern g1="w"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="40" />
    <hkern g1="w"
	g2="V"
	k="10" />
    <hkern g1="w"
	g2="X"
	k="40" />
    <hkern g1="y,yacute,ydieresis,ycircumflex"
	g2="W,Wcircumflex"
	k="5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="60" />
    <hkern g1="y,yacute,ydieresis,ycircumflex"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Delta"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="55" />
    <hkern g1="y,yacute,ydieresis,ycircumflex"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="40" />
    <hkern g1="y,yacute,ydieresis,ycircumflex"
	g2="V"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex"
	g2="X"
	k="35" />
    <hkern g1="y,yacute,ydieresis,ycircumflex"
	g2="question"
	k="5" />
    <hkern g1="X"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,G.alt1,Gbreve.alt1,Gcommaaccent.alt1,Gcircumflex.alt1,Gdotaccent.alt1"
	k="30" />
    <hkern g1="X"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="50" />
    <hkern g1="X"
	g2="bullet.case"
	k="60" />
    <hkern g1="X"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="75" />
    <hkern g1="X"
	g2="y,yacute,ydieresis,ycircumflex"
	k="35" />
    <hkern g1="X"
	g2="w"
	k="40" />
    <hkern g1="X"
	g2="v"
	k="35" />
    <hkern g1="X"
	g2="f,longs,f_f_j,f_j,f_f,fi,fl,f_f_i,f_f_l,f.alt1,f_h,f_k,fl.alt1,f_f_l.alt1"
	k="30" />
    <hkern g1="X"
	g2="t,tcommaaccent,tcaron,tbar"
	k="30" />
    <hkern g1="X"
	g2="hyphen,periodcentered,endash,emdash"
	k="45" />
    <hkern g1="X.smcp"
	g2="Cacute.smcp,Ccaron.smcp,Ccircumflex.smcp,Cdotaccent.smcp,Gbreve.smcp,Gcircumflex.smcp,Gcommaaccent.smcp,Gdotaccent.smcp,Obreve.smcp,Ohungarumlaut.smcp,Omacron.smcp,Oslashacute.smcp,OE.smcp,C.smcp,G.smcp,O.smcp,Q.smcp,Ccedilla.smcp,Ograve.smcp,Oacute.smcp,Ocircumflex.smcp,Otilde.smcp,Odieresis.smcp,Oslash.smcp,G.smcp.alt1,Gbreve.smcp.alt1,Gcommaaccent.smcp.alt1,Gcircumflex.smcp.alt1,Gdotaccent.smcp.alt1"
	k="25" />
    <hkern g1="X.smcp"
	g2="ampersand"
	k="20" />
    <hkern g1="X.smcp"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="25" />
    <hkern g1="zero,nine"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="25" />
    <hkern g1="seven"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="95" />
    <hkern g1="x"
	g2="W,Wcircumflex"
	k="25" />
    <hkern g1="x"
	g2="Y,Yacute,Ycircumflex,Ydieresis"
	k="75" />
    <hkern g1="x"
	g2="T,Tcommaaccent,Tcaron,Tbar"
	k="50" />
    <hkern g1="x"
	g2="V"
	k="25" />
    <hkern g1="x"
	g2="a,c,d,e,g,o,q,ae,ccedilla,eth,oslash,aogonek,dcaron,eogonek,oe,aeacute"
	k="35" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.case,bracketleft.case,braceleft.case"
	g2="j,jcircumflex"
	k="-110" />
    <hkern g1="one.numr"
	g2="fraction"
	k="-25" />
    <hkern g1="seven.numr"
	g2="fraction"
	k="50" />
    <hkern g1="fraction"
	g2="four.dnom"
	k="50" />
    <hkern g1="fraction"
	g2="one.dnom"
	k="-10" />
  </font>
</defs></svg>
