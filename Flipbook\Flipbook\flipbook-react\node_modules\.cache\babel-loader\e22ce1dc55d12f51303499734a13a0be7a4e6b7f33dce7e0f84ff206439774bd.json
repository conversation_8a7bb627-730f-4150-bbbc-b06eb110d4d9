{"ast": null, "code": "var _jsxFileName = \"D:\\\\TradeWorks\\\\Flipbook\\\\Flipbook\\\\Flipbook\\\\flipbook-react\\\\src\\\\components\\\\FlipbookEditor\\\\FlipbookEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport FrontPage from '../FrontPage/FrontPage';\nimport ResumePage from '../ResumePage/ResumePage';\nimport ContactDetails from '../ContactDetails/ContactDetails';\nimport NameFlipbook from '../NameFlipbook/NameFlipbook';\nimport AddPageModal from '../AddPageModal/AddPageModal';\nimport TextToolbar from '../TextToolbar/TextToolbar';\nimport { useAuth } from '../../hooks/useAuth';\nimport { useFlipbooks } from '../../hooks/useFlipbooks';\nimport { apiService } from '../../services/api.service';\nimport './FlipbookEditor.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FlipbookEditor = () => {\n  _s();\n  const {\n    portfolioId\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useAuth();\n  const {\n    userFlipbooks,\n    updateFlipbook,\n    refreshUserFlipbooks\n  } = useFlipbooks();\n  const [flipbookTitle, setFlipbookTitle] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(3); // Front Cover + 1 Content Page + Back Cover\n  const [isNewFlipbook, setIsNewFlipbook] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [lastSaved, setLastSaved] = useState(null);\n  const [showContactModal, setShowContactModal] = useState(false);\n  const [showNameModal, setShowNameModal] = useState(false);\n  const [hasCreatedFlipbook, setHasCreatedFlipbook] = useState(false);\n  const [showAddPageModal, setShowAddPageModal] = useState(false);\n  const [draggedPageIndex, setDraggedPageIndex] = useState(null);\n  const [dragOverPageIndex, setDragOverPageIndex] = useState(null);\n  const [pageOrder, setPageOrder] = useState([]);\n\n  // Text formatting state\n  const [activeTextEditor, setActiveTextEditor] = useState(null);\n  const [currentTextFormatting, setCurrentTextFormatting] = useState({\n    fontFamily: 'Arial',\n    fontSize: '14',\n    bold: false,\n    italic: false,\n    underline: false,\n    textAlign: 'left',\n    color: '#000000',\n    backgroundColor: 'transparent',\n    lineHeight: '1.4',\n    letterSpacing: '0'\n  });\n  const [showTextToolbar, setShowTextToolbar] = useState(false);\n  const [isTyping, setIsTyping] = useState(false);\n  const [pendingFormatting, setPendingFormatting] = useState(null);\n\n  // Flipbook content data\n  const [flipbookData, setFlipbookData] = useState({\n    title: 'Welcome to My Portfolio',\n    subtitle: 'Professional Excellence in Every Detail',\n    frontPageBackground: '',\n    resumeSections: [{\n      id: 'objective',\n      title: 'Objective',\n      content: ''\n    }, {\n      id: 'education',\n      title: 'Education',\n      content: ''\n    }, {\n      id: 'experience',\n      title: 'Experience',\n      content: ''\n    }, {\n      id: 'skills',\n      title: 'Skill Proficiencies',\n      content: ''\n    }, {\n      id: 'personal',\n      title: 'Personal Statement',\n      content: ''\n    }, {\n      id: 'interests',\n      title: 'Professional Interests',\n      content: ''\n    }],\n    profileImage: '',\n    contactInfo: {\n      name: 'Zara Irum',\n      mobile: '************',\n      email: '<EMAIL>',\n      flipbookUrl: 'https://flipbook.franklinreport.com/2137 2236 <EMAIL>'\n    }\n  });\n  useEffect(() => {\n    // Check if we're creating a new flipbook (no portfolioId in URL)\n    if (!portfolioId && !hasCreatedFlipbook) {\n      setIsNewFlipbook(true);\n      setShowNameModal(true);\n    } else {\n      setIsNewFlipbook(false);\n      // Load existing flipbook data here\n      const flipbook = userFlipbooks.find(fb => fb.PortfolioID.toString() === portfolioId);\n      if (flipbook) {\n        setFlipbookTitle(flipbook.PortfolioTitle);\n        // Ensure minimum of 3 pages (front cover, content, back cover)\n        const pageCount = Math.max(flipbook.PageCount || 3, 3);\n        console.log('Loading flipbook with page count:', pageCount, 'from stored:', flipbook.PageCount);\n        setTotalPages(pageCount);\n      } else {\n        // If flipbook not found in userFlipbooks, ensure we still have 3 pages minimum\n        console.log('Flipbook not found, setting default 3 pages');\n        setTotalPages(3);\n      }\n    }\n  }, [portfolioId, userFlipbooks, hasCreatedFlipbook]);\n\n  // Initialize page order when total pages changes\n  useEffect(() => {\n    console.log('Total pages changed to:', totalPages);\n    setPageOrder(Array.from({\n      length: totalPages\n    }, (_, i) => i));\n  }, [totalPages]);\n  const handleSave = async () => {\n    if (!portfolioId) {\n      // Handle case where no portfolio ID exists\n      return;\n    }\n    setIsSaving(true);\n    try {\n      const success = await updateFlipbook({\n        portfolioId: parseInt(portfolioId),\n        title: flipbookTitle\n      });\n      if (success) {\n        setLastSaved(new Date());\n        // Refresh flipbooks to get latest data\n        await refreshUserFlipbooks();\n      }\n    } catch (error) {\n      console.error('Error saving flipbook:', error);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handleTitleEdit = () => {\n    const newTitle = prompt('Enter new flipbook title:', flipbookTitle);\n    if (newTitle && newTitle.trim()) {\n      setFlipbookTitle(newTitle.trim());\n    }\n  };\n  const handlePreview = () => {\n    if (portfolioId) {\n      navigate(`/viewer/${portfolioId}`);\n    }\n  };\n  const handleClose = () => {\n    navigate('/');\n  };\n  const handleAddPage = () => {\n    setShowAddPageModal(true);\n  };\n  const handleSelectPageTemplate = template => {\n    // Add the new page with the selected template\n    setTotalPages(prev => prev + 1);\n    // Navigate to the new page\n    setCurrentPage(totalPages + 1);\n    console.log('Selected template:', template);\n  };\n\n  // Page drag and drop handlers\n  const handlePageDragStart = (e, pageIndex) => {\n    // Don't allow dragging front cover (0) or back cover (last page)\n    if (pageIndex === 0 || pageIndex === totalPages - 1) {\n      e.preventDefault();\n      return;\n    }\n    setDraggedPageIndex(pageIndex);\n    e.dataTransfer.effectAllowed = 'move';\n    e.dataTransfer.setData('text/plain', pageIndex.toString());\n  };\n  const handlePageDragEnd = () => {\n    setDraggedPageIndex(null);\n    setDragOverPageIndex(null);\n  };\n  const handlePageDragOver = (e, targetIndex) => {\n    e.preventDefault();\n    // Don't allow dropping on front cover (0) or back cover (last page)\n    if (targetIndex === 0 || targetIndex === totalPages - 1) {\n      return;\n    }\n    e.dataTransfer.dropEffect = 'move';\n    setDragOverPageIndex(targetIndex);\n  };\n  const handlePageDragLeave = () => {\n    setDragOverPageIndex(null);\n  };\n  const handlePageDrop = (e, targetIndex) => {\n    e.preventDefault();\n    if (draggedPageIndex === null || draggedPageIndex === targetIndex) return;\n\n    // Don't allow dropping on front cover (0) or back cover (last page)\n    if (targetIndex === 0 || targetIndex === totalPages - 1) {\n      return;\n    }\n    const newPageOrder = [...pageOrder];\n    const draggedPage = newPageOrder[draggedPageIndex];\n\n    // Remove dragged page\n    newPageOrder.splice(draggedPageIndex, 1);\n\n    // Insert at new position\n    const insertIndex = draggedPageIndex < targetIndex ? targetIndex - 1 : targetIndex;\n    newPageOrder.splice(insertIndex, 0, draggedPage);\n    setPageOrder(newPageOrder);\n    setDraggedPageIndex(null);\n    setDragOverPageIndex(null);\n\n    // Update current page if necessary\n    if (currentPage === draggedPageIndex + 1) {\n      setCurrentPage(insertIndex + 1);\n    }\n  };\n\n  // Get page label based on original index\n  const getPageLabel = originalIndex => {\n    if (originalIndex === 0) return 'Front Cover';\n    if (originalIndex === totalPages - 1) return 'Back Cover';\n    return `Page ${originalIndex + 1}`;\n  };\n\n  // Check if a page is draggable\n  const isPageDraggable = index => {\n    return index !== 0 && index !== totalPages - 1;\n  };\n\n  // Text formatting handlers\n  const handleTextEditorFocus = (editorRef, formatting) => {\n    setActiveTextEditor(editorRef);\n    setShowTextToolbar(true);\n\n    // Don't override formatting if we have pending formatting from toolbar\n    if (!pendingFormatting && formatting) {\n      setCurrentTextFormatting(formatting);\n    }\n  };\n  const handleTextEditorBlur = event => {\n    // Don't hide toolbar if clicking on the toolbar itself\n    if (event !== null && event !== void 0 && event.relatedTarget) {\n      const clickedElement = event.relatedTarget;\n      const toolbar = document.querySelector('.text-toolbar');\n      if (toolbar && toolbar.contains(clickedElement)) {\n        return; // Don't hide toolbar if clicking on it\n      }\n    }\n\n    // Don't hide toolbar immediately - let it stay visible for a moment\n    setTimeout(() => {\n      // Double-check if we should still hide the toolbar\n      const activeElement = document.activeElement;\n      const toolbar = document.querySelector('.text-toolbar');\n\n      // Don't hide if focus is on toolbar or a text editor\n      if (toolbar && toolbar.contains(activeElement)) {\n        return;\n      }\n      if (activeElement && activeElement.classList.contains('text-editor')) {\n        return;\n      }\n      setShowTextToolbar(false);\n      setActiveTextEditor(null);\n    }, 150);\n  };\n  const handleTextFormattingChange = formatting => {\n    const newFormatting = {\n      ...currentTextFormatting,\n      ...formatting\n    };\n    setCurrentTextFormatting(newFormatting);\n    setPendingFormatting(newFormatting);\n    setIsTyping(false);\n  };\n  const handleApplyFormatting = (command, value) => {\n    if (!activeTextEditor) return;\n\n    // Use the TextEditor's applyCommand method if available\n    if (activeTextEditor.applyCommand) {\n      activeTextEditor.applyCommand(command, value);\n    } else {\n      // Fallback to direct document.execCommand\n      activeTextEditor.focus();\n      switch (command) {\n        case 'bold':\n        case 'italic':\n        case 'underline':\n          document.execCommand(command, false);\n          break;\n        case 'fontSize':\n          // Apply font size via CSS for better control\n          const selection = window.getSelection();\n          if (selection && selection.rangeCount > 0) {\n            const range = selection.getRangeAt(0);\n            if (!range.collapsed) {\n              const span = document.createElement('span');\n              span.style.fontSize = value + 'pt';\n              try {\n                range.surroundContents(span);\n              } catch (e) {\n                // Fallback to execCommand if surroundContents fails\n                document.execCommand('fontSize', false, '7');\n                // Then apply CSS size\n                const selectedElements = activeTextEditor.querySelectorAll('font[size=\"7\"]');\n                selectedElements.forEach(el => {\n                  el.style.fontSize = value + 'pt';\n                });\n              }\n            }\n          }\n          break;\n        case 'fontName':\n        case 'foreColor':\n        case 'backColor':\n        case 'justifyLeft':\n        case 'justifyCenter':\n        case 'justifyRight':\n        case 'justifyFull':\n        case 'insertUnorderedList':\n        case 'insertOrderedList':\n        case 'createLink':\n          document.execCommand(command, false, value);\n          break;\n      }\n    }\n\n    // Set pending formatting for when user starts typing\n    setPendingFormatting(currentTextFormatting);\n  };\n  const handleFormattingApplied = () => {\n    // Clear pending formatting after it has been applied\n    setPendingFormatting(null);\n  };\n  const handleNameSubmit = async name => {\n    try {\n      // Create the flipbook on the server with 3 pages (front cover, content, back cover)\n      const response = await apiService.createNewFlipbook({\n        title: name,\n        pageCount: 3\n      });\n      if (response.success && response.data) {\n        const newPortfolioId = response.data;\n        setFlipbookTitle(name);\n        setShowNameModal(false);\n        setHasCreatedFlipbook(true);\n\n        // Refresh flipbooks to get latest data\n        await refreshUserFlipbooks();\n\n        // Update URL to include the new portfolio ID\n        navigate(`/editor/${newPortfolioId}`, {\n          replace: true\n        });\n      } else {\n        console.error('Failed to create flipbook:', response.error);\n        alert('Failed to create flipbook. Please try again.');\n      }\n    } catch (error) {\n      console.error('Error creating flipbook:', error);\n      alert('An error occurred while creating the flipbook.');\n    }\n  };\n  const handleNameModalClose = () => {\n    // If user closes modal without naming, redirect to dashboard\n    navigate('/');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flipbook-editor\",\n    children: [/*#__PURE__*/_jsxDEV(TextToolbar, {\n      isVisible: showTextToolbar,\n      currentFormatting: currentTextFormatting,\n      onFormatChange: handleTextFormattingChange,\n      onApplyFormatting: handleApplyFormatting\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-sidebar\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"toolbar-icons sidebar-section\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"icon-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"icon-btn\",\n              title: \"Select\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\uD83D\\uDCCC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"icon-btn\",\n              title: \"Text\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"icon-btn\",\n              title: \"Image\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\uD83D\\uDDBC\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"icon-btn\",\n              title: \"Shape\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u2B1B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"icon-btn\",\n              title: \"Line\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\uD83D\\uDCCF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"icon-btn\",\n              title: \"Crop\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u2702\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"click-drag-section\",\n          children: [\"CLICK & DRAG\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 25\n          }, this), \"TO REORDER PAGES\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pages-section sidebar-section\",\n          children: pageOrder.map((originalIndex, displayIndex) => {\n            const isDraggable = isPageDraggable(originalIndex);\n            const isDragging = draggedPageIndex === displayIndex;\n            const isDragOver = dragOverPageIndex === displayIndex;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `page-thumbnail ${currentPage === displayIndex + 1 ? 'active' : ''} ${isDragging ? 'dragging' : ''} ${isDragOver && isDraggable ? 'drag-over' : ''} ${!isDraggable ? 'not-draggable' : 'draggable'}`,\n              draggable: isDraggable,\n              onClick: () => setCurrentPage(displayIndex + 1),\n              onDragStart: e => handlePageDragStart(e, displayIndex),\n              onDragEnd: handlePageDragEnd,\n              onDragOver: e => handlePageDragOver(e, displayIndex),\n              onDragLeave: handlePageDragLeave,\n              onDrop: e => handlePageDrop(e, displayIndex),\n              title: isDraggable ? `Drag to reorder - ${getPageLabel(originalIndex)}` : getPageLabel(originalIndex),\n              children: [isDraggable && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"page-drag-handle\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u22EE\\u22EE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"page-label\",\n                children: getPageLabel(originalIndex)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 19\n              }, this), !isDraggable && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"fixed-page-indicator\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\uD83D\\uDCCC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 21\n              }, this)]\n            }, `page-${originalIndex}`, true, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"add-page-section\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"add-page-btn\",\n            onClick: handleAddPage,\n            children: \"+ Add New Page\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-canvas\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"canvas-container\",\n          children: !flipbookTitle ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"waiting-for-name\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"placeholder-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                children: \"Creating New Flipbook...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Please name your flipbook to continue\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"page-canvas\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pages-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                children: \"PAGES\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"flipbook-title\",\n                children: flipbookTitle\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 17\n            }, this), currentPage === 1 && totalPages >= 1 && /*#__PURE__*/_jsxDEV(FrontPage, {\n              title: flipbookTitle || flipbookData.title,\n              subtitle: flipbookData.subtitle,\n              backgroundImage: flipbookData.frontPageBackground,\n              onTitleChange: title => {\n                setFlipbookData(prev => ({\n                  ...prev,\n                  title\n                }));\n              },\n              onSubtitleChange: subtitle => setFlipbookData(prev => ({\n                ...prev,\n                subtitle\n              })),\n              onBackgroundImageChange: imageUrl => setFlipbookData(prev => ({\n                ...prev,\n                frontPageBackground: imageUrl\n              })),\n              onNext: () => setCurrentPage(2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 17\n            }, this), currentPage === 2 && totalPages >= 2 && /*#__PURE__*/_jsxDEV(ResumePage, {\n              sections: flipbookData.resumeSections,\n              profileImage: flipbookData.profileImage,\n              onSectionChange: (sectionId, content) => {\n                setFlipbookData(prev => ({\n                  ...prev,\n                  resumeSections: prev.resumeSections.map(section => section.id === sectionId ? {\n                    ...section,\n                    content\n                  } : section)\n                }));\n              },\n              onSectionDelete: sectionId => {\n                setFlipbookData(prev => ({\n                  ...prev,\n                  resumeSections: prev.resumeSections.filter(section => section.id !== sectionId)\n                }));\n              },\n              onSectionReorder: newSections => {\n                setFlipbookData(prev => ({\n                  ...prev,\n                  resumeSections: newSections\n                }));\n              },\n              onProfileImageChange: imageUrl => setFlipbookData(prev => ({\n                ...prev,\n                profileImage: imageUrl\n              })),\n              onTextEditorFocus: handleTextEditorFocus,\n              onTextEditorBlur: handleTextEditorBlur,\n              pendingFormatting: pendingFormatting,\n              onFormattingApplied: handleFormattingApplied,\n              onPrevious: () => setCurrentPage(1),\n              onNext: () => setCurrentPage(3)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 17\n            }, this), currentPage === 3 && totalPages >= 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"back-page\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"back-page-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"contact-info-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    children: \"Contact Information\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"contact-card\",\n                    onClick: () => setShowContactModal(true),\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"contact-item\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"label\",\n                        children: \"Name:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 563,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"value\",\n                        children: flipbookData.contactInfo.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 564,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 562,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"contact-item\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"label\",\n                        children: \"Mobile:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 567,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"value\",\n                        children: flipbookData.contactInfo.mobile\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 568,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 566,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"contact-item\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"label\",\n                        children: \"Email:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 571,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"value\",\n                        children: flipbookData.contactInfo.email\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 572,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 570,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"contact-item\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"label\",\n                        children: \"Portfolio URL:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 575,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"value\",\n                        children: flipbookData.contactInfo.flipbookUrl\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 576,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 574,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"edit-hint\",\n                      children: \"Click to edit contact details\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 578,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flipbook-branding\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"brand-logo\",\n                    children: \"\\uD83D\\uDCDA Flipbook\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Created with Flipbook\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"navigation-controls\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"nav-btn prev-btn\",\n                    onClick: () => setCurrentPage(2),\n                    title: \"Previous Page\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"arrow-icon\",\n                      children: \"\\u2190\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 591,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 590,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 17\n            }, this), currentPage > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"page-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                children: [\"Page \", currentPage]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Additional page content can be added here.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"navigation-controls\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"nav-btn prev-btn\",\n                  onClick: () => setCurrentPage(Math.max(1, currentPage - 1)),\n                  title: \"Previous Page\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"arrow-icon\",\n                    children: \"\\u2190\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 21\n                }, this), currentPage < totalPages && /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"nav-btn next-btn\",\n                  onClick: () => setCurrentPage(Math.min(totalPages, currentPage + 1)),\n                  title: \"Next Page\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"arrow-icon\",\n                    children: \"\\u2192\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ContactDetails, {\n      isOpen: showContactModal,\n      onClose: () => setShowContactModal(false),\n      contactInfo: flipbookData.contactInfo,\n      onSave: contactInfo => {\n        setFlipbookData(prev => ({\n          ...prev,\n          contactInfo\n        }));\n        setShowContactModal(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 623,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(NameFlipbook, {\n      isOpen: showNameModal,\n      onClose: handleNameModalClose,\n      onSubmit: handleNameSubmit\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 634,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AddPageModal, {\n      isOpen: showAddPageModal,\n      onClose: () => setShowAddPageModal(false),\n      onSelectTemplate: handleSelectPageTemplate\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 641,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 399,\n    columnNumber: 5\n  }, this);\n};\n_s(FlipbookEditor, \"GOPu4ZFr9ADPRcClH+qCilpsuzM=\", false, function () {\n  return [useParams, useNavigate, useAuth, useFlipbooks];\n});\n_c = FlipbookEditor;\nexport default FlipbookEditor;\nvar _c;\n$RefreshReg$(_c, \"FlipbookEditor\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "FrontPage", "ResumePage", "ContactDetails", "NameFlipbook", "AddPageModal", "TextToolbar", "useAuth", "useFlipbooks", "apiService", "jsxDEV", "_jsxDEV", "FlipbookEditor", "_s", "portfolioId", "navigate", "user", "userFlipbooks", "updateFlipbook", "refreshUserFlipbooks", "flipbookTitle", "setFlipbookTitle", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "isNewFlipbook", "setIsNewFlipbook", "isSaving", "setIsSaving", "lastSaved", "setLastSaved", "showContactModal", "setShowContactModal", "showNameModal", "setShowNameModal", "hasCreatedFlipbook", "setHasCreatedFlipbook", "showAddPageModal", "setShowAddPageModal", "draggedPageIndex", "setDraggedPageIndex", "dragOverPageIndex", "setDragOverPageIndex", "pageOrder", "setPageOrder", "activeTextEditor", "setActiveTextEditor", "currentTextFormatting", "setCurrentTextFormatting", "fontFamily", "fontSize", "bold", "italic", "underline", "textAlign", "color", "backgroundColor", "lineHeight", "letterSpacing", "showTextToolbar", "setShowTextToolbar", "isTyping", "setIsTyping", "pendingFormatting", "setPendingFormatting", "flipbookData", "setFlipbookData", "title", "subtitle", "frontPageBackground", "resumeSections", "id", "content", "profileImage", "contactInfo", "name", "mobile", "email", "flipbookUrl", "flipbook", "find", "fb", "PortfolioID", "toString", "PortfolioTitle", "pageCount", "Math", "max", "PageCount", "console", "log", "Array", "from", "length", "_", "i", "handleSave", "success", "parseInt", "Date", "error", "handleTitleEdit", "newTitle", "prompt", "trim", "handlePreview", "handleClose", "handleAddPage", "handleSelectPageTemplate", "template", "prev", "handlePageDragStart", "e", "pageIndex", "preventDefault", "dataTransfer", "effectAllowed", "setData", "handlePageDragEnd", "handlePageDragOver", "targetIndex", "dropEffect", "handlePageDragLeave", "handlePageDrop", "newPageOrder", "draggedPage", "splice", "insertIndex", "getPageLabel", "originalIndex", "isPageDraggable", "index", "handleTextEditorFocus", "editor<PERSON><PERSON>", "formatting", "handleTextEditorBlur", "event", "relatedTarget", "clickedElement", "toolbar", "document", "querySelector", "contains", "setTimeout", "activeElement", "classList", "handleTextFormattingChange", "newFormatting", "handleApplyFormatting", "command", "value", "applyCommand", "focus", "execCommand", "selection", "window", "getSelection", "rangeCount", "range", "getRangeAt", "collapsed", "span", "createElement", "style", "surroundContents", "selectedElements", "querySelectorAll", "for<PERSON>ach", "el", "handleFormattingApplied", "handleNameSubmit", "response", "createNewFlipbook", "data", "newPortfolioId", "replace", "alert", "handleNameModalClose", "className", "children", "isVisible", "currentFormatting", "onFormatChange", "onApplyFormatting", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "displayIndex", "isDraggable", "isDragging", "isDragOver", "draggable", "onClick", "onDragStart", "onDragEnd", "onDragOver", "onDragLeave", "onDrop", "backgroundImage", "onTitleChange", "onSubtitleChange", "onBackgroundImageChange", "imageUrl", "onNext", "sections", "onSectionChange", "sectionId", "section", "onSectionDelete", "filter", "onSectionReorder", "newSections", "onProfileImageChange", "onTextEditorFocus", "onTextEditorBlur", "onFormattingApplied", "onPrevious", "min", "isOpen", "onClose", "onSave", "onSubmit", "onSelectTemplate", "_c", "$RefreshReg$"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/components/FlipbookEditor/FlipbookEditor.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Header from '../Header/Header';\nimport FrontPage from '../FrontPage/FrontPage';\nimport ResumePage from '../ResumePage/ResumePage';\nimport ContactDetails from '../ContactDetails/ContactDetails';\nimport NameFlipbook from '../NameFlipbook/NameFlipbook';\nimport AddPageModal from '../AddPageModal/AddPageModal';\nimport TextToolbar from '../TextToolbar/TextToolbar';\nimport { useAuth } from '../../hooks/useAuth';\nimport { useFlipbooks } from '../../hooks/useFlipbooks';\nimport { apiService } from '../../services/api.service';\nimport './FlipbookEditor.css';\n\ninterface FlipbookEditorProps {}\n\ninterface ResumeSection {\n  id: string;\n  title: string;\n  content: string;\n}\n\ninterface ContactInfo {\n  name: string;\n  mobile: string;\n  email: string;\n  flipbookUrl: string;\n}\n\ninterface FlipbookData {\n  title: string;\n  subtitle: string;\n  frontPageBackground: string;\n  resumeSections: ResumeSection[];\n  profileImage: string;\n  contactInfo: ContactInfo;\n}\n\nconst FlipbookEditor: React.FC<FlipbookEditorProps> = () => {\n  const { portfolioId } = useParams<{ portfolioId: string }>();\n  const navigate = useNavigate();\n  const { user } = useAuth();\n  const { userFlipbooks, updateFlipbook, refreshUserFlipbooks } = useFlipbooks();\n  \n  const [flipbookTitle, setFlipbookTitle] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(3); // Front Cover + 1 Content Page + Back Cover\n  const [isNewFlipbook, setIsNewFlipbook] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [lastSaved, setLastSaved] = useState<Date | null>(null);\n  const [showContactModal, setShowContactModal] = useState(false);\n  const [showNameModal, setShowNameModal] = useState(false);\n  const [hasCreatedFlipbook, setHasCreatedFlipbook] = useState(false);\n  const [showAddPageModal, setShowAddPageModal] = useState(false);\n  const [draggedPageIndex, setDraggedPageIndex] = useState<number | null>(null);\n  const [dragOverPageIndex, setDragOverPageIndex] = useState<number | null>(null);\n  const [pageOrder, setPageOrder] = useState<number[]>([]);\n  \n  // Text formatting state\n  const [activeTextEditor, setActiveTextEditor] = useState<HTMLDivElement | null>(null);\n  const [currentTextFormatting, setCurrentTextFormatting] = useState({\n    fontFamily: 'Arial',\n    fontSize: '14',\n    bold: false,\n    italic: false,\n    underline: false,\n    textAlign: 'left' as 'left' | 'center' | 'right' | 'justify',\n    color: '#000000',\n    backgroundColor: 'transparent',\n    lineHeight: '1.4',\n    letterSpacing: '0'\n  });\n  const [showTextToolbar, setShowTextToolbar] = useState(false);\n  const [isTyping, setIsTyping] = useState(false);\n  const [pendingFormatting, setPendingFormatting] = useState<any>(null);\n  \n  // Flipbook content data\n  const [flipbookData, setFlipbookData] = useState<FlipbookData>({\n    title: 'Welcome to My Portfolio',\n    subtitle: 'Professional Excellence in Every Detail',\n    frontPageBackground: '',\n    resumeSections: [\n      { id: 'objective', title: 'Objective', content: '' },\n      { id: 'education', title: 'Education', content: '' },\n      { id: 'experience', title: 'Experience', content: '' },\n      { id: 'skills', title: 'Skill Proficiencies', content: '' },\n      { id: 'personal', title: 'Personal Statement', content: '' },\n      { id: 'interests', title: 'Professional Interests', content: '' }\n    ],\n    profileImage: '',\n    contactInfo: {\n      name: 'Zara Irum',\n      mobile: '************',\n      email: '<EMAIL>',\n      flipbookUrl: 'https://flipbook.franklinreport.com/2137 2236 <EMAIL>'\n    }\n  });\n\n  useEffect(() => {\n    // Check if we're creating a new flipbook (no portfolioId in URL)\n    if (!portfolioId && !hasCreatedFlipbook) {\n      setIsNewFlipbook(true);\n      setShowNameModal(true);\n    } else {\n      setIsNewFlipbook(false);\n      // Load existing flipbook data here\n      const flipbook = userFlipbooks.find(fb => fb.PortfolioID.toString() === portfolioId);\n      if (flipbook) {\n        setFlipbookTitle(flipbook.PortfolioTitle);\n        // Ensure minimum of 3 pages (front cover, content, back cover)\n        const pageCount = Math.max(flipbook.PageCount || 3, 3);\n        console.log('Loading flipbook with page count:', pageCount, 'from stored:', flipbook.PageCount);\n        setTotalPages(pageCount);\n      } else {\n        // If flipbook not found in userFlipbooks, ensure we still have 3 pages minimum\n        console.log('Flipbook not found, setting default 3 pages');\n        setTotalPages(3);\n      }\n    }\n  }, [portfolioId, userFlipbooks, hasCreatedFlipbook]);\n\n  // Initialize page order when total pages changes\n  useEffect(() => {\n    console.log('Total pages changed to:', totalPages);\n    setPageOrder(Array.from({ length: totalPages }, (_, i) => i));\n  }, [totalPages]);\n\n  const handleSave = async () => {\n    if (!portfolioId) {\n      // Handle case where no portfolio ID exists\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      const success = await updateFlipbook({\n        portfolioId: parseInt(portfolioId),\n        title: flipbookTitle\n      });\n      \n      if (success) {\n        setLastSaved(new Date());\n        // Refresh flipbooks to get latest data\n        await refreshUserFlipbooks();\n      }\n    } catch (error) {\n      console.error('Error saving flipbook:', error);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleTitleEdit = () => {\n    const newTitle = prompt('Enter new flipbook title:', flipbookTitle);\n    if (newTitle && newTitle.trim()) {\n      setFlipbookTitle(newTitle.trim());\n    }\n  };\n\n  const handlePreview = () => {\n    if (portfolioId) {\n      navigate(`/viewer/${portfolioId}`);\n    }\n  };\n\n  const handleClose = () => {\n    navigate('/');\n  };\n\n  const handleAddPage = () => {\n    setShowAddPageModal(true);\n  };\n\n  const handleSelectPageTemplate = (template: any) => {\n    // Add the new page with the selected template\n    setTotalPages(prev => prev + 1);\n    // Navigate to the new page\n    setCurrentPage(totalPages + 1);\n    console.log('Selected template:', template);\n  };\n\n  // Page drag and drop handlers\n  const handlePageDragStart = (e: React.DragEvent, pageIndex: number) => {\n    // Don't allow dragging front cover (0) or back cover (last page)\n    if (pageIndex === 0 || pageIndex === totalPages - 1) {\n      e.preventDefault();\n      return;\n    }\n    setDraggedPageIndex(pageIndex);\n    e.dataTransfer.effectAllowed = 'move';\n    e.dataTransfer.setData('text/plain', pageIndex.toString());\n  };\n\n  const handlePageDragEnd = () => {\n    setDraggedPageIndex(null);\n    setDragOverPageIndex(null);\n  };\n\n  const handlePageDragOver = (e: React.DragEvent, targetIndex: number) => {\n    e.preventDefault();\n    // Don't allow dropping on front cover (0) or back cover (last page)\n    if (targetIndex === 0 || targetIndex === totalPages - 1) {\n      return;\n    }\n    e.dataTransfer.dropEffect = 'move';\n    setDragOverPageIndex(targetIndex);\n  };\n\n  const handlePageDragLeave = () => {\n    setDragOverPageIndex(null);\n  };\n\n  const handlePageDrop = (e: React.DragEvent, targetIndex: number) => {\n    e.preventDefault();\n    \n    if (draggedPageIndex === null || draggedPageIndex === targetIndex) return;\n    \n    // Don't allow dropping on front cover (0) or back cover (last page)\n    if (targetIndex === 0 || targetIndex === totalPages - 1) {\n      return;\n    }\n\n    const newPageOrder = [...pageOrder];\n    const draggedPage = newPageOrder[draggedPageIndex];\n    \n    // Remove dragged page\n    newPageOrder.splice(draggedPageIndex, 1);\n    \n    // Insert at new position\n    const insertIndex = draggedPageIndex < targetIndex ? targetIndex - 1 : targetIndex;\n    newPageOrder.splice(insertIndex, 0, draggedPage);\n    \n    setPageOrder(newPageOrder);\n    setDraggedPageIndex(null);\n    setDragOverPageIndex(null);\n    \n    // Update current page if necessary\n    if (currentPage === draggedPageIndex + 1) {\n      setCurrentPage(insertIndex + 1);\n    }\n  };\n\n  // Get page label based on original index\n  const getPageLabel = (originalIndex: number) => {\n    if (originalIndex === 0) return 'Front Cover';\n    if (originalIndex === totalPages - 1) return 'Back Cover';\n    return `Page ${originalIndex + 1}`;\n  };\n\n  // Check if a page is draggable\n  const isPageDraggable = (index: number) => {\n    return index !== 0 && index !== totalPages - 1;\n  };\n\n  // Text formatting handlers\n  const handleTextEditorFocus = (editorRef: HTMLDivElement, formatting: any) => {\n    setActiveTextEditor(editorRef);\n    setShowTextToolbar(true);\n    \n    // Don't override formatting if we have pending formatting from toolbar\n    if (!pendingFormatting && formatting) {\n      setCurrentTextFormatting(formatting);\n    }\n  };\n\n  const handleTextEditorBlur = (event?: React.FocusEvent) => {\n    // Don't hide toolbar if clicking on the toolbar itself\n    if (event?.relatedTarget) {\n      const clickedElement = event.relatedTarget as HTMLElement;\n      const toolbar = document.querySelector('.text-toolbar');\n      if (toolbar && toolbar.contains(clickedElement)) {\n        return; // Don't hide toolbar if clicking on it\n      }\n    }\n    \n    // Don't hide toolbar immediately - let it stay visible for a moment\n    setTimeout(() => {\n      // Double-check if we should still hide the toolbar\n      const activeElement = document.activeElement as HTMLElement;\n      const toolbar = document.querySelector('.text-toolbar');\n      \n      // Don't hide if focus is on toolbar or a text editor\n      if (toolbar && toolbar.contains(activeElement)) {\n        return;\n      }\n      \n      if (activeElement && activeElement.classList.contains('text-editor')) {\n        return;\n      }\n      \n      setShowTextToolbar(false);\n      setActiveTextEditor(null);\n    }, 150);\n  };\n\n  const handleTextFormattingChange = (formatting: any) => {\n    const newFormatting = { ...currentTextFormatting, ...formatting };\n    setCurrentTextFormatting(newFormatting);\n    setPendingFormatting(newFormatting);\n    setIsTyping(false);\n  };\n\n  const handleApplyFormatting = (command: string, value?: string) => {\n    if (!activeTextEditor) return;\n    \n    // Use the TextEditor's applyCommand method if available\n    if ((activeTextEditor as any).applyCommand) {\n      (activeTextEditor as any).applyCommand(command, value);\n    } else {\n      // Fallback to direct document.execCommand\n      activeTextEditor.focus();\n      \n      switch (command) {\n        case 'bold':\n        case 'italic':\n        case 'underline':\n          document.execCommand(command, false);\n          break;\n        case 'fontSize':\n          // Apply font size via CSS for better control\n          const selection = window.getSelection();\n          if (selection && selection.rangeCount > 0) {\n            const range = selection.getRangeAt(0);\n            if (!range.collapsed) {\n              const span = document.createElement('span');\n              span.style.fontSize = value + 'pt';\n              try {\n                range.surroundContents(span);\n              } catch (e) {\n                // Fallback to execCommand if surroundContents fails\n                document.execCommand('fontSize', false, '7');\n                // Then apply CSS size\n                const selectedElements = activeTextEditor.querySelectorAll('font[size=\"7\"]');\n                selectedElements.forEach(el => {\n                  (el as HTMLElement).style.fontSize = value + 'pt';\n                });\n              }\n            }\n          }\n          break;\n        case 'fontName':\n        case 'foreColor':\n        case 'backColor':\n        case 'justifyLeft':\n        case 'justifyCenter':\n        case 'justifyRight':\n        case 'justifyFull':\n        case 'insertUnorderedList':\n        case 'insertOrderedList':\n        case 'createLink':\n          document.execCommand(command, false, value);\n          break;\n      }\n    }\n    \n    // Set pending formatting for when user starts typing\n    setPendingFormatting(currentTextFormatting);\n  };\n  \n  const handleFormattingApplied = () => {\n    // Clear pending formatting after it has been applied\n    setPendingFormatting(null);\n  };\n\n  const handleNameSubmit = async (name: string) => {\n    try {\n      // Create the flipbook on the server with 3 pages (front cover, content, back cover)\n      const response = await apiService.createNewFlipbook({ \n        title: name,\n        pageCount: 3 \n      });\n      if (response.success && response.data) {\n        const newPortfolioId = response.data;\n        setFlipbookTitle(name);\n        setShowNameModal(false);\n        setHasCreatedFlipbook(true);\n        \n        // Refresh flipbooks to get latest data\n        await refreshUserFlipbooks();\n        \n        // Update URL to include the new portfolio ID\n        navigate(`/editor/${newPortfolioId}`, { replace: true });\n      } else {\n        console.error('Failed to create flipbook:', response.error);\n        alert('Failed to create flipbook. Please try again.');\n      }\n    } catch (error) {\n      console.error('Error creating flipbook:', error);\n      alert('An error occurred while creating the flipbook.');\n    }\n  };\n\n  const handleNameModalClose = () => {\n    // If user closes modal without naming, redirect to dashboard\n    navigate('/');\n  };\n\n  return (\n    <div className=\"flipbook-editor\">\n      {/* Common Text Toolbar - Fixed at top */}\n      <TextToolbar\n        isVisible={showTextToolbar}\n        currentFormatting={currentTextFormatting}\n        onFormatChange={handleTextFormattingChange}\n        onApplyFormatting={handleApplyFormatting}\n      />\n      \n      <div className=\"editor-content\">\n        <div className=\"editor-sidebar\">\n          {/* Toolbar Icons */}\n          <div className=\"toolbar-icons sidebar-section\">\n            <div className=\"icon-grid\">\n              <button className=\"icon-btn\" title=\"Select\">\n                <span>📌</span>\n              </button>\n              <button className=\"icon-btn\" title=\"Text\">\n                <span>A</span>\n              </button>\n              <button className=\"icon-btn\" title=\"Image\">\n                <span>🖼️</span>\n              </button>\n              <button className=\"icon-btn\" title=\"Shape\">\n                <span>⬛</span>\n              </button>\n              <button className=\"icon-btn\" title=\"Line\">\n                <span>📏</span>\n              </button>\n              <button className=\"icon-btn\" title=\"Crop\">\n                <span>✂️</span>\n              </button>\n            </div>\n          </div>\n          \n          {/* Click & Drag Section */}\n          <div className=\"click-drag-section\">\n            CLICK & DRAG<br/>TO REORDER PAGES\n          </div>\n          \n          {/* Pages Section */}\n          <div className=\"pages-section sidebar-section\">\n            {pageOrder.map((originalIndex, displayIndex) => {\n              const isDraggable = isPageDraggable(originalIndex);\n              const isDragging = draggedPageIndex === displayIndex;\n              const isDragOver = dragOverPageIndex === displayIndex;\n              \n              return (\n                <div \n                  key={`page-${originalIndex}`}\n                  className={`page-thumbnail ${\n                    currentPage === displayIndex + 1 ? 'active' : ''\n                  } ${\n                    isDragging ? 'dragging' : ''\n                  } ${\n                    isDragOver && isDraggable ? 'drag-over' : ''\n                  } ${\n                    !isDraggable ? 'not-draggable' : 'draggable'\n                  }`}\n                  draggable={isDraggable}\n                  onClick={() => setCurrentPage(displayIndex + 1)}\n                  onDragStart={(e) => handlePageDragStart(e, displayIndex)}\n                  onDragEnd={handlePageDragEnd}\n                  onDragOver={(e) => handlePageDragOver(e, displayIndex)}\n                  onDragLeave={handlePageDragLeave}\n                  onDrop={(e) => handlePageDrop(e, displayIndex)}\n                  title={isDraggable ? `Drag to reorder - ${getPageLabel(originalIndex)}` : getPageLabel(originalIndex)}\n                >\n                  {isDraggable && (\n                    <div className=\"page-drag-handle\">\n                      <span>⋮⋮</span>\n                    </div>\n                  )}\n                  <span className=\"page-label\">{getPageLabel(originalIndex)}</span>\n                  {!isDraggable && (\n                    <div className=\"fixed-page-indicator\">\n                      <span>📌</span>\n                    </div>\n                  )}\n                </div>\n              );\n            })}\n          </div>\n          \n          {/* Add Page Section */}\n          <div className=\"add-page-section\">\n            <button className=\"add-page-btn\" onClick={handleAddPage}>\n              + Add New Page\n            </button>\n          </div>\n        </div>\n\n        <div className=\"editor-canvas\">\n          <div className=\"canvas-container\">\n            {!flipbookTitle ? (\n              <div className=\"waiting-for-name\">\n                <div className=\"placeholder-content\">\n                  <h2>Creating New Flipbook...</h2>\n                  <p>Please name your flipbook to continue</p>\n                </div>\n              </div>\n            ) : (\n              <div className=\"page-canvas\">\n                {/* Pages Header */}\n                <div className=\"pages-header\">\n                  <h1>PAGES</h1>\n                  <h2 className=\"flipbook-title\">{flipbookTitle}</h2>\n                </div>\n                \n                {currentPage === 1 && totalPages >= 1 && (\n                <FrontPage\n                  title={flipbookTitle || flipbookData.title}\n                  subtitle={flipbookData.subtitle}\n                  backgroundImage={flipbookData.frontPageBackground}\n                  onTitleChange={(title) => {\n                    setFlipbookData(prev => ({ ...prev, title }));\n                  }}\n                  onSubtitleChange={(subtitle) => setFlipbookData(prev => ({ ...prev, subtitle }))}\n                  onBackgroundImageChange={(imageUrl) => setFlipbookData(prev => ({ ...prev, frontPageBackground: imageUrl }))}\n                  onNext={() => setCurrentPage(2)}\n                />\n              )}\n              \n              {currentPage === 2 && totalPages >= 2 && (\n                <ResumePage\n                  sections={flipbookData.resumeSections}\n                  profileImage={flipbookData.profileImage}\n                  onSectionChange={(sectionId, content) => {\n                    setFlipbookData(prev => ({\n                      ...prev,\n                      resumeSections: prev.resumeSections.map(section =>\n                        section.id === sectionId ? { ...section, content } : section\n                      )\n                    }));\n                  }}\n                  onSectionDelete={(sectionId) => {\n                    setFlipbookData(prev => ({\n                      ...prev,\n                      resumeSections: prev.resumeSections.filter(section => section.id !== sectionId)\n                    }));\n                  }}\n                  onSectionReorder={(newSections) => {\n                    setFlipbookData(prev => ({\n                      ...prev,\n                      resumeSections: newSections\n                    }));\n                  }}\n                  onProfileImageChange={(imageUrl) => setFlipbookData(prev => ({ ...prev, profileImage: imageUrl }))}\n                  onTextEditorFocus={handleTextEditorFocus}\n                  onTextEditorBlur={handleTextEditorBlur}\n                  pendingFormatting={pendingFormatting}\n                  onFormattingApplied={handleFormattingApplied}\n                  onPrevious={() => setCurrentPage(1)}\n                  onNext={() => setCurrentPage(3)}\n                />\n              )}\n              \n              {currentPage === 3 && totalPages >= 3 && (\n                <div className=\"back-page\">\n                  <div className=\"back-page-content\">\n                    <div className=\"contact-info-section\">\n                      <h2>Contact Information</h2>\n                      <div className=\"contact-card\" onClick={() => setShowContactModal(true)}>\n                        <div className=\"contact-item\">\n                          <span className=\"label\">Name:</span>\n                          <span className=\"value\">{flipbookData.contactInfo.name}</span>\n                        </div>\n                        <div className=\"contact-item\">\n                          <span className=\"label\">Mobile:</span>\n                          <span className=\"value\">{flipbookData.contactInfo.mobile}</span>\n                        </div>\n                        <div className=\"contact-item\">\n                          <span className=\"label\">Email:</span>\n                          <span className=\"value\">{flipbookData.contactInfo.email}</span>\n                        </div>\n                        <div className=\"contact-item\">\n                          <span className=\"label\">Portfolio URL:</span>\n                          <span className=\"value\">{flipbookData.contactInfo.flipbookUrl}</span>\n                        </div>\n                        <div className=\"edit-hint\">\n                          Click to edit contact details\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flipbook-branding\">\n                      <div className=\"brand-logo\">📚 Flipbook</div>\n                      <p>Created with Flipbook</p>\n                    </div>\n                    \n                    <div className=\"navigation-controls\">\n                      <button className=\"nav-btn prev-btn\" onClick={() => setCurrentPage(2)} title=\"Previous Page\">\n                        <span className=\"arrow-icon\">←</span>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              )}\n              \n              {currentPage > 3 && (\n                <div className=\"page-content\">\n                  <h1>Page {currentPage}</h1>\n                  <p>Additional page content can be added here.</p>\n                  <div className=\"navigation-controls\">\n                    <button className=\"nav-btn prev-btn\" onClick={() => setCurrentPage(Math.max(1, currentPage - 1))} title=\"Previous Page\">\n                      <span className=\"arrow-icon\">←</span>\n                    </button>\n                    {currentPage < totalPages && (\n                      <button className=\"nav-btn next-btn\" onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))} title=\"Next Page\">\n                        <span className=\"arrow-icon\">→</span>\n                      </button>\n                    )}\n                  </div>\n                </div>\n              )}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Properties panel can be added later if needed */}\n      </div>\n      \n      {/* Contact Details Modal */}\n      <ContactDetails\n        isOpen={showContactModal}\n        onClose={() => setShowContactModal(false)}\n        contactInfo={flipbookData.contactInfo}\n        onSave={(contactInfo) => {\n          setFlipbookData(prev => ({ ...prev, contactInfo }));\n          setShowContactModal(false);\n        }}\n      />\n      \n      {/* Name Flipbook Modal */}\n      <NameFlipbook\n        isOpen={showNameModal}\n        onClose={handleNameModalClose}\n        onSubmit={handleNameSubmit}\n      />\n      \n      {/* Add Page Modal */}\n      <AddPageModal\n        isOpen={showAddPageModal}\n        onClose={() => setShowAddPageModal(false)}\n        onSelectTemplate={handleSelectPageTemplate}\n      />\n    </div>\n  );\n};\n\nexport default FlipbookEditor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AAEzD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,UAAU,QAAQ,4BAA4B;AACvD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA0B9B,MAAMC,cAA6C,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1D,MAAM;IAAEC;EAAY,CAAC,GAAGf,SAAS,CAA0B,CAAC;EAC5D,MAAMgB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgB;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEU,aAAa;IAAEC,cAAc;IAAEC;EAAqB,CAAC,GAAGX,YAAY,CAAC,CAAC;EAE9E,MAAM,CAACY,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAc,IAAI,CAAC;EAC7D,MAAM,CAACmC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5C,QAAQ,CAAgB,IAAI,CAAC;EAC7E,MAAM,CAAC6C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9C,QAAQ,CAAgB,IAAI,CAAC;EAC/E,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAW,EAAE,CAAC;;EAExD;EACA,MAAM,CAACiD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAwB,IAAI,CAAC;EACrF,MAAM,CAACmD,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGpD,QAAQ,CAAC;IACjEqD,UAAU,EAAE,OAAO;IACnBC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,KAAK;IACXC,MAAM,EAAE,KAAK;IACbC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,MAAiD;IAC5DC,KAAK,EAAE,SAAS;IAChBC,eAAe,EAAE,aAAa;IAC9BC,UAAU,EAAE,KAAK;IACjBC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiE,QAAQ,EAAEC,WAAW,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACmE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpE,QAAQ,CAAM,IAAI,CAAC;;EAErE;EACA,MAAM,CAACqE,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAe;IAC7DuE,KAAK,EAAE,yBAAyB;IAChCC,QAAQ,EAAE,yCAAyC;IACnDC,mBAAmB,EAAE,EAAE;IACvBC,cAAc,EAAE,CACd;MAAEC,EAAE,EAAE,WAAW;MAAEJ,KAAK,EAAE,WAAW;MAAEK,OAAO,EAAE;IAAG,CAAC,EACpD;MAAED,EAAE,EAAE,WAAW;MAAEJ,KAAK,EAAE,WAAW;MAAEK,OAAO,EAAE;IAAG,CAAC,EACpD;MAAED,EAAE,EAAE,YAAY;MAAEJ,KAAK,EAAE,YAAY;MAAEK,OAAO,EAAE;IAAG,CAAC,EACtD;MAAED,EAAE,EAAE,QAAQ;MAAEJ,KAAK,EAAE,qBAAqB;MAAEK,OAAO,EAAE;IAAG,CAAC,EAC3D;MAAED,EAAE,EAAE,UAAU;MAAEJ,KAAK,EAAE,oBAAoB;MAAEK,OAAO,EAAE;IAAG,CAAC,EAC5D;MAAED,EAAE,EAAE,WAAW;MAAEJ,KAAK,EAAE,wBAAwB;MAAEK,OAAO,EAAE;IAAG,CAAC,CAClE;IACDC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE;MACXC,IAAI,EAAE,WAAW;MACjBC,MAAM,EAAE,cAAc;MACtBC,KAAK,EAAE,gBAAgB;MACvBC,WAAW,EAAE;IACf;EACF,CAAC,CAAC;EAEFjF,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACgB,WAAW,IAAI,CAACsB,kBAAkB,EAAE;MACvCT,gBAAgB,CAAC,IAAI,CAAC;MACtBQ,gBAAgB,CAAC,IAAI,CAAC;IACxB,CAAC,MAAM;MACLR,gBAAgB,CAAC,KAAK,CAAC;MACvB;MACA,MAAMqD,QAAQ,GAAG/D,aAAa,CAACgE,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACC,WAAW,CAACC,QAAQ,CAAC,CAAC,KAAKtE,WAAW,CAAC;MACpF,IAAIkE,QAAQ,EAAE;QACZ3D,gBAAgB,CAAC2D,QAAQ,CAACK,cAAc,CAAC;QACzC;QACA,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACR,QAAQ,CAACS,SAAS,IAAI,CAAC,EAAE,CAAC,CAAC;QACtDC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEL,SAAS,EAAE,cAAc,EAAEN,QAAQ,CAACS,SAAS,CAAC;QAC/FhE,aAAa,CAAC6D,SAAS,CAAC;MAC1B,CAAC,MAAM;QACL;QACAI,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1DlE,aAAa,CAAC,CAAC,CAAC;MAClB;IACF;EACF,CAAC,EAAE,CAACX,WAAW,EAAEG,aAAa,EAAEmB,kBAAkB,CAAC,CAAC;;EAEpD;EACAtC,SAAS,CAAC,MAAM;IACd4F,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEnE,UAAU,CAAC;IAClDqB,YAAY,CAAC+C,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAEtE;IAAW,CAAC,EAAE,CAACuE,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC,CAAC;EAC/D,CAAC,EAAE,CAACxE,UAAU,CAAC,CAAC;EAEhB,MAAMyE,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACnF,WAAW,EAAE;MAChB;MACA;IACF;IAEAe,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF,MAAMqE,OAAO,GAAG,MAAMhF,cAAc,CAAC;QACnCJ,WAAW,EAAEqF,QAAQ,CAACrF,WAAW,CAAC;QAClCsD,KAAK,EAAEhD;MACT,CAAC,CAAC;MAEF,IAAI8E,OAAO,EAAE;QACXnE,YAAY,CAAC,IAAIqE,IAAI,CAAC,CAAC,CAAC;QACxB;QACA,MAAMjF,oBAAoB,CAAC,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOkF,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRxE,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMyE,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,QAAQ,GAAGC,MAAM,CAAC,2BAA2B,EAAEpF,aAAa,CAAC;IACnE,IAAImF,QAAQ,IAAIA,QAAQ,CAACE,IAAI,CAAC,CAAC,EAAE;MAC/BpF,gBAAgB,CAACkF,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;IACnC;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI5F,WAAW,EAAE;MACfC,QAAQ,CAAC,WAAWD,WAAW,EAAE,CAAC;IACpC;EACF,CAAC;EAED,MAAM6F,WAAW,GAAGA,CAAA,KAAM;IACxB5F,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,MAAM6F,aAAa,GAAGA,CAAA,KAAM;IAC1BrE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMsE,wBAAwB,GAAIC,QAAa,IAAK;IAClD;IACArF,aAAa,CAACsF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B;IACAxF,cAAc,CAACC,UAAU,GAAG,CAAC,CAAC;IAC9BkE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEmB,QAAQ,CAAC;EAC7C,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAGA,CAACC,CAAkB,EAAEC,SAAiB,KAAK;IACrE;IACA,IAAIA,SAAS,KAAK,CAAC,IAAIA,SAAS,KAAK1F,UAAU,GAAG,CAAC,EAAE;MACnDyF,CAAC,CAACE,cAAc,CAAC,CAAC;MAClB;IACF;IACA1E,mBAAmB,CAACyE,SAAS,CAAC;IAC9BD,CAAC,CAACG,YAAY,CAACC,aAAa,GAAG,MAAM;IACrCJ,CAAC,CAACG,YAAY,CAACE,OAAO,CAAC,YAAY,EAAEJ,SAAS,CAAC9B,QAAQ,CAAC,CAAC,CAAC;EAC5D,CAAC;EAED,MAAMmC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B9E,mBAAmB,CAAC,IAAI,CAAC;IACzBE,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAM6E,kBAAkB,GAAGA,CAACP,CAAkB,EAAEQ,WAAmB,KAAK;IACtER,CAAC,CAACE,cAAc,CAAC,CAAC;IAClB;IACA,IAAIM,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAKjG,UAAU,GAAG,CAAC,EAAE;MACvD;IACF;IACAyF,CAAC,CAACG,YAAY,CAACM,UAAU,GAAG,MAAM;IAClC/E,oBAAoB,CAAC8E,WAAW,CAAC;EACnC,CAAC;EAED,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAChChF,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMiF,cAAc,GAAGA,CAACX,CAAkB,EAAEQ,WAAmB,KAAK;IAClER,CAAC,CAACE,cAAc,CAAC,CAAC;IAElB,IAAI3E,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAKiF,WAAW,EAAE;;IAEnE;IACA,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAKjG,UAAU,GAAG,CAAC,EAAE;MACvD;IACF;IAEA,MAAMqG,YAAY,GAAG,CAAC,GAAGjF,SAAS,CAAC;IACnC,MAAMkF,WAAW,GAAGD,YAAY,CAACrF,gBAAgB,CAAC;;IAElD;IACAqF,YAAY,CAACE,MAAM,CAACvF,gBAAgB,EAAE,CAAC,CAAC;;IAExC;IACA,MAAMwF,WAAW,GAAGxF,gBAAgB,GAAGiF,WAAW,GAAGA,WAAW,GAAG,CAAC,GAAGA,WAAW;IAClFI,YAAY,CAACE,MAAM,CAACC,WAAW,EAAE,CAAC,EAAEF,WAAW,CAAC;IAEhDjF,YAAY,CAACgF,YAAY,CAAC;IAC1BpF,mBAAmB,CAAC,IAAI,CAAC;IACzBE,oBAAoB,CAAC,IAAI,CAAC;;IAE1B;IACA,IAAIrB,WAAW,KAAKkB,gBAAgB,GAAG,CAAC,EAAE;MACxCjB,cAAc,CAACyG,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAIC,aAAqB,IAAK;IAC9C,IAAIA,aAAa,KAAK,CAAC,EAAE,OAAO,aAAa;IAC7C,IAAIA,aAAa,KAAK1G,UAAU,GAAG,CAAC,EAAE,OAAO,YAAY;IACzD,OAAO,QAAQ0G,aAAa,GAAG,CAAC,EAAE;EACpC,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIC,KAAa,IAAK;IACzC,OAAOA,KAAK,KAAK,CAAC,IAAIA,KAAK,KAAK5G,UAAU,GAAG,CAAC;EAChD,CAAC;;EAED;EACA,MAAM6G,qBAAqB,GAAGA,CAACC,SAAyB,EAAEC,UAAe,KAAK;IAC5ExF,mBAAmB,CAACuF,SAAS,CAAC;IAC9BzE,kBAAkB,CAAC,IAAI,CAAC;;IAExB;IACA,IAAI,CAACG,iBAAiB,IAAIuE,UAAU,EAAE;MACpCtF,wBAAwB,CAACsF,UAAU,CAAC;IACtC;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAIC,KAAwB,IAAK;IACzD;IACA,IAAIA,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEC,aAAa,EAAE;MACxB,MAAMC,cAAc,GAAGF,KAAK,CAACC,aAA4B;MACzD,MAAME,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,eAAe,CAAC;MACvD,IAAIF,OAAO,IAAIA,OAAO,CAACG,QAAQ,CAACJ,cAAc,CAAC,EAAE;QAC/C,OAAO,CAAC;MACV;IACF;;IAEA;IACAK,UAAU,CAAC,MAAM;MACf;MACA,MAAMC,aAAa,GAAGJ,QAAQ,CAACI,aAA4B;MAC3D,MAAML,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,eAAe,CAAC;;MAEvD;MACA,IAAIF,OAAO,IAAIA,OAAO,CAACG,QAAQ,CAACE,aAAa,CAAC,EAAE;QAC9C;MACF;MAEA,IAAIA,aAAa,IAAIA,aAAa,CAACC,SAAS,CAACH,QAAQ,CAAC,aAAa,CAAC,EAAE;QACpE;MACF;MAEAlF,kBAAkB,CAAC,KAAK,CAAC;MACzBd,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMoG,0BAA0B,GAAIZ,UAAe,IAAK;IACtD,MAAMa,aAAa,GAAG;MAAE,GAAGpG,qBAAqB;MAAE,GAAGuF;IAAW,CAAC;IACjEtF,wBAAwB,CAACmG,aAAa,CAAC;IACvCnF,oBAAoB,CAACmF,aAAa,CAAC;IACnCrF,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMsF,qBAAqB,GAAGA,CAACC,OAAe,EAAEC,KAAc,KAAK;IACjE,IAAI,CAACzG,gBAAgB,EAAE;;IAEvB;IACA,IAAKA,gBAAgB,CAAS0G,YAAY,EAAE;MACzC1G,gBAAgB,CAAS0G,YAAY,CAACF,OAAO,EAAEC,KAAK,CAAC;IACxD,CAAC,MAAM;MACL;MACAzG,gBAAgB,CAAC2G,KAAK,CAAC,CAAC;MAExB,QAAQH,OAAO;QACb,KAAK,MAAM;QACX,KAAK,QAAQ;QACb,KAAK,WAAW;UACdT,QAAQ,CAACa,WAAW,CAACJ,OAAO,EAAE,KAAK,CAAC;UACpC;QACF,KAAK,UAAU;UACb;UACA,MAAMK,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;UACvC,IAAIF,SAAS,IAAIA,SAAS,CAACG,UAAU,GAAG,CAAC,EAAE;YACzC,MAAMC,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;YACrC,IAAI,CAACD,KAAK,CAACE,SAAS,EAAE;cACpB,MAAMC,IAAI,GAAGrB,QAAQ,CAACsB,aAAa,CAAC,MAAM,CAAC;cAC3CD,IAAI,CAACE,KAAK,CAACjH,QAAQ,GAAGoG,KAAK,GAAG,IAAI;cAClC,IAAI;gBACFQ,KAAK,CAACM,gBAAgB,CAACH,IAAI,CAAC;cAC9B,CAAC,CAAC,OAAOjD,CAAC,EAAE;gBACV;gBACA4B,QAAQ,CAACa,WAAW,CAAC,UAAU,EAAE,KAAK,EAAE,GAAG,CAAC;gBAC5C;gBACA,MAAMY,gBAAgB,GAAGxH,gBAAgB,CAACyH,gBAAgB,CAAC,gBAAgB,CAAC;gBAC5ED,gBAAgB,CAACE,OAAO,CAACC,EAAE,IAAI;kBAC5BA,EAAE,CAAiBL,KAAK,CAACjH,QAAQ,GAAGoG,KAAK,GAAG,IAAI;gBACnD,CAAC,CAAC;cACJ;YACF;UACF;UACA;QACF,KAAK,UAAU;QACf,KAAK,WAAW;QAChB,KAAK,WAAW;QAChB,KAAK,aAAa;QAClB,KAAK,eAAe;QACpB,KAAK,cAAc;QACnB,KAAK,aAAa;QAClB,KAAK,qBAAqB;QAC1B,KAAK,mBAAmB;QACxB,KAAK,YAAY;UACfV,QAAQ,CAACa,WAAW,CAACJ,OAAO,EAAE,KAAK,EAAEC,KAAK,CAAC;UAC3C;MACJ;IACF;;IAEA;IACAtF,oBAAoB,CAACjB,qBAAqB,CAAC;EAC7C,CAAC;EAED,MAAM0H,uBAAuB,GAAGA,CAAA,KAAM;IACpC;IACAzG,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAM0G,gBAAgB,GAAG,MAAO/F,IAAY,IAAK;IAC/C,IAAI;MACF;MACA,MAAMgG,QAAQ,GAAG,MAAMnK,UAAU,CAACoK,iBAAiB,CAAC;QAClDzG,KAAK,EAAEQ,IAAI;QACXU,SAAS,EAAE;MACb,CAAC,CAAC;MACF,IAAIsF,QAAQ,CAAC1E,OAAO,IAAI0E,QAAQ,CAACE,IAAI,EAAE;QACrC,MAAMC,cAAc,GAAGH,QAAQ,CAACE,IAAI;QACpCzJ,gBAAgB,CAACuD,IAAI,CAAC;QACtBzC,gBAAgB,CAAC,KAAK,CAAC;QACvBE,qBAAqB,CAAC,IAAI,CAAC;;QAE3B;QACA,MAAMlB,oBAAoB,CAAC,CAAC;;QAE5B;QACAJ,QAAQ,CAAC,WAAWgK,cAAc,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAK,CAAC,CAAC;MAC1D,CAAC,MAAM;QACLtF,OAAO,CAACW,KAAK,CAAC,4BAA4B,EAAEuE,QAAQ,CAACvE,KAAK,CAAC;QAC3D4E,KAAK,CAAC,8CAA8C,CAAC;MACvD;IACF,CAAC,CAAC,OAAO5E,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD4E,KAAK,CAAC,gDAAgD,CAAC;IACzD;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAnK,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,oBACEJ,OAAA;IAAKwK,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAE9BzK,OAAA,CAACL,WAAW;MACV+K,SAAS,EAAEzH,eAAgB;MAC3B0H,iBAAiB,EAAEtI,qBAAsB;MACzCuI,cAAc,EAAEpC,0BAA2B;MAC3CqC,iBAAiB,EAAEnC;IAAsB;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAEFjL,OAAA;MAAKwK,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BzK,OAAA;QAAKwK,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAE7BzK,OAAA;UAAKwK,SAAS,EAAC,+BAA+B;UAAAC,QAAA,eAC5CzK,OAAA;YAAKwK,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBzK,OAAA;cAAQwK,SAAS,EAAC,UAAU;cAAC/G,KAAK,EAAC,QAAQ;cAAAgH,QAAA,eACzCzK,OAAA;gBAAAyK,QAAA,EAAM;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACTjL,OAAA;cAAQwK,SAAS,EAAC,UAAU;cAAC/G,KAAK,EAAC,MAAM;cAAAgH,QAAA,eACvCzK,OAAA;gBAAAyK,QAAA,EAAM;cAAC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACTjL,OAAA;cAAQwK,SAAS,EAAC,UAAU;cAAC/G,KAAK,EAAC,OAAO;cAAAgH,QAAA,eACxCzK,OAAA;gBAAAyK,QAAA,EAAM;cAAG;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACTjL,OAAA;cAAQwK,SAAS,EAAC,UAAU;cAAC/G,KAAK,EAAC,OAAO;cAAAgH,QAAA,eACxCzK,OAAA;gBAAAyK,QAAA,EAAM;cAAC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACTjL,OAAA;cAAQwK,SAAS,EAAC,UAAU;cAAC/G,KAAK,EAAC,MAAM;cAAAgH,QAAA,eACvCzK,OAAA;gBAAAyK,QAAA,EAAM;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACTjL,OAAA;cAAQwK,SAAS,EAAC,UAAU;cAAC/G,KAAK,EAAC,MAAM;cAAAgH,QAAA,eACvCzK,OAAA;gBAAAyK,QAAA,EAAM;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjL,OAAA;UAAKwK,SAAS,EAAC,oBAAoB;UAAAC,QAAA,GAAC,cACtB,eAAAzK,OAAA;YAAA8K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,oBACnB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGNjL,OAAA;UAAKwK,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAC3CxI,SAAS,CAACiJ,GAAG,CAAC,CAAC3D,aAAa,EAAE4D,YAAY,KAAK;YAC9C,MAAMC,WAAW,GAAG5D,eAAe,CAACD,aAAa,CAAC;YAClD,MAAM8D,UAAU,GAAGxJ,gBAAgB,KAAKsJ,YAAY;YACpD,MAAMG,UAAU,GAAGvJ,iBAAiB,KAAKoJ,YAAY;YAErD,oBACEnL,OAAA;cAEEwK,SAAS,EAAE,kBACT7J,WAAW,KAAKwK,YAAY,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,IAEhDE,UAAU,GAAG,UAAU,GAAG,EAAE,IAE5BC,UAAU,IAAIF,WAAW,GAAG,WAAW,GAAG,EAAE,IAE5C,CAACA,WAAW,GAAG,eAAe,GAAG,WAAW,EAC3C;cACHG,SAAS,EAAEH,WAAY;cACvBI,OAAO,EAAEA,CAAA,KAAM5K,cAAc,CAACuK,YAAY,GAAG,CAAC,CAAE;cAChDM,WAAW,EAAGnF,CAAC,IAAKD,mBAAmB,CAACC,CAAC,EAAE6E,YAAY,CAAE;cACzDO,SAAS,EAAE9E,iBAAkB;cAC7B+E,UAAU,EAAGrF,CAAC,IAAKO,kBAAkB,CAACP,CAAC,EAAE6E,YAAY,CAAE;cACvDS,WAAW,EAAE5E,mBAAoB;cACjC6E,MAAM,EAAGvF,CAAC,IAAKW,cAAc,CAACX,CAAC,EAAE6E,YAAY,CAAE;cAC/C1H,KAAK,EAAE2H,WAAW,GAAG,qBAAqB9D,YAAY,CAACC,aAAa,CAAC,EAAE,GAAGD,YAAY,CAACC,aAAa,CAAE;cAAAkD,QAAA,GAErGW,WAAW,iBACVpL,OAAA;gBAAKwK,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BzK,OAAA;kBAAAyK,QAAA,EAAM;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CACN,eACDjL,OAAA;gBAAMwK,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEnD,YAAY,CAACC,aAAa;cAAC;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAChE,CAACG,WAAW,iBACXpL,OAAA;gBAAKwK,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,eACnCzK,OAAA;kBAAAyK,QAAA,EAAM;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CACN;YAAA,GA7BI,QAAQ1D,aAAa,EAAE;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8BzB,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNjL,OAAA;UAAKwK,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BzK,OAAA;YAAQwK,SAAS,EAAC,cAAc;YAACgB,OAAO,EAAEvF,aAAc;YAAAwE,QAAA,EAAC;UAEzD;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjL,OAAA;QAAKwK,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BzK,OAAA;UAAKwK,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC9B,CAAChK,aAAa,gBACbT,OAAA;YAAKwK,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BzK,OAAA;cAAKwK,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClCzK,OAAA;gBAAAyK,QAAA,EAAI;cAAwB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjCjL,OAAA;gBAAAyK,QAAA,EAAG;cAAqC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENjL,OAAA;YAAKwK,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAE1BzK,OAAA;cAAKwK,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BzK,OAAA;gBAAAyK,QAAA,EAAI;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdjL,OAAA;gBAAIwK,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAEhK;cAAa;gBAAAqK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,EAELtK,WAAW,KAAK,CAAC,IAAIE,UAAU,IAAI,CAAC,iBACrCb,OAAA,CAACV,SAAS;cACRmE,KAAK,EAAEhD,aAAa,IAAI8C,YAAY,CAACE,KAAM;cAC3CC,QAAQ,EAAEH,YAAY,CAACG,QAAS;cAChCoI,eAAe,EAAEvI,YAAY,CAACI,mBAAoB;cAClDoI,aAAa,EAAGtI,KAAK,IAAK;gBACxBD,eAAe,CAAC4C,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE3C;gBAAM,CAAC,CAAC,CAAC;cAC/C,CAAE;cACFuI,gBAAgB,EAAGtI,QAAQ,IAAKF,eAAe,CAAC4C,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE1C;cAAS,CAAC,CAAC,CAAE;cACjFuI,uBAAuB,EAAGC,QAAQ,IAAK1I,eAAe,CAAC4C,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEzC,mBAAmB,EAAEuI;cAAS,CAAC,CAAC,CAAE;cAC7GC,MAAM,EAAEA,CAAA,KAAMvL,cAAc,CAAC,CAAC;YAAE;cAAAkK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CACF,EAEAtK,WAAW,KAAK,CAAC,IAAIE,UAAU,IAAI,CAAC,iBACnCb,OAAA,CAACT,UAAU;cACT6M,QAAQ,EAAE7I,YAAY,CAACK,cAAe;cACtCG,YAAY,EAAER,YAAY,CAACQ,YAAa;cACxCsI,eAAe,EAAEA,CAACC,SAAS,EAAExI,OAAO,KAAK;gBACvCN,eAAe,CAAC4C,IAAI,KAAK;kBACvB,GAAGA,IAAI;kBACPxC,cAAc,EAAEwC,IAAI,CAACxC,cAAc,CAACsH,GAAG,CAACqB,OAAO,IAC7CA,OAAO,CAAC1I,EAAE,KAAKyI,SAAS,GAAG;oBAAE,GAAGC,OAAO;oBAAEzI;kBAAQ,CAAC,GAAGyI,OACvD;gBACF,CAAC,CAAC,CAAC;cACL,CAAE;cACFC,eAAe,EAAGF,SAAS,IAAK;gBAC9B9I,eAAe,CAAC4C,IAAI,KAAK;kBACvB,GAAGA,IAAI;kBACPxC,cAAc,EAAEwC,IAAI,CAACxC,cAAc,CAAC6I,MAAM,CAACF,OAAO,IAAIA,OAAO,CAAC1I,EAAE,KAAKyI,SAAS;gBAChF,CAAC,CAAC,CAAC;cACL,CAAE;cACFI,gBAAgB,EAAGC,WAAW,IAAK;gBACjCnJ,eAAe,CAAC4C,IAAI,KAAK;kBACvB,GAAGA,IAAI;kBACPxC,cAAc,EAAE+I;gBAClB,CAAC,CAAC,CAAC;cACL,CAAE;cACFC,oBAAoB,EAAGV,QAAQ,IAAK1I,eAAe,CAAC4C,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAErC,YAAY,EAAEmI;cAAS,CAAC,CAAC,CAAE;cACnGW,iBAAiB,EAAEnF,qBAAsB;cACzCoF,gBAAgB,EAAEjF,oBAAqB;cACvCxE,iBAAiB,EAAEA,iBAAkB;cACrC0J,mBAAmB,EAAEhD,uBAAwB;cAC7CiD,UAAU,EAAEA,CAAA,KAAMpM,cAAc,CAAC,CAAC,CAAE;cACpCuL,MAAM,EAAEA,CAAA,KAAMvL,cAAc,CAAC,CAAC;YAAE;cAAAkK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CACF,EAEAtK,WAAW,KAAK,CAAC,IAAIE,UAAU,IAAI,CAAC,iBACnCb,OAAA;cAAKwK,SAAS,EAAC,WAAW;cAAAC,QAAA,eACxBzK,OAAA;gBAAKwK,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCzK,OAAA;kBAAKwK,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnCzK,OAAA;oBAAAyK,QAAA,EAAI;kBAAmB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5BjL,OAAA;oBAAKwK,SAAS,EAAC,cAAc;oBAACgB,OAAO,EAAEA,CAAA,KAAMlK,mBAAmB,CAAC,IAAI,CAAE;oBAAAmJ,QAAA,gBACrEzK,OAAA;sBAAKwK,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BzK,OAAA;wBAAMwK,SAAS,EAAC,OAAO;wBAAAC,QAAA,EAAC;sBAAK;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpCjL,OAAA;wBAAMwK,SAAS,EAAC,OAAO;wBAAAC,QAAA,EAAElH,YAAY,CAACS,WAAW,CAACC;sBAAI;wBAAA6G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3D,CAAC,eACNjL,OAAA;sBAAKwK,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BzK,OAAA;wBAAMwK,SAAS,EAAC,OAAO;wBAAAC,QAAA,EAAC;sBAAO;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACtCjL,OAAA;wBAAMwK,SAAS,EAAC,OAAO;wBAAAC,QAAA,EAAElH,YAAY,CAACS,WAAW,CAACE;sBAAM;wBAAA4G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CAAC,eACNjL,OAAA;sBAAKwK,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BzK,OAAA;wBAAMwK,SAAS,EAAC,OAAO;wBAAAC,QAAA,EAAC;sBAAM;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrCjL,OAAA;wBAAMwK,SAAS,EAAC,OAAO;wBAAAC,QAAA,EAAElH,YAAY,CAACS,WAAW,CAACG;sBAAK;wBAAA2G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC,eACNjL,OAAA;sBAAKwK,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BzK,OAAA;wBAAMwK,SAAS,EAAC,OAAO;wBAAAC,QAAA,EAAC;sBAAc;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC7CjL,OAAA;wBAAMwK,SAAS,EAAC,OAAO;wBAAAC,QAAA,EAAElH,YAAY,CAACS,WAAW,CAACI;sBAAW;wBAAA0G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClE,CAAC,eACNjL,OAAA;sBAAKwK,SAAS,EAAC,WAAW;sBAAAC,QAAA,EAAC;oBAE3B;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENjL,OAAA;kBAAKwK,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCzK,OAAA;oBAAKwK,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAW;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7CjL,OAAA;oBAAAyK,QAAA,EAAG;kBAAqB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eAENjL,OAAA;kBAAKwK,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,eAClCzK,OAAA;oBAAQwK,SAAS,EAAC,kBAAkB;oBAACgB,OAAO,EAAEA,CAAA,KAAM5K,cAAc,CAAC,CAAC,CAAE;oBAAC6C,KAAK,EAAC,eAAe;oBAAAgH,QAAA,eAC1FzK,OAAA;sBAAMwK,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAC;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAEAtK,WAAW,GAAG,CAAC,iBACdX,OAAA;cAAKwK,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BzK,OAAA;gBAAAyK,QAAA,GAAI,OAAK,EAAC9J,WAAW;cAAA;gBAAAmK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3BjL,OAAA;gBAAAyK,QAAA,EAAG;cAA0C;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjDjL,OAAA;gBAAKwK,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClCzK,OAAA;kBAAQwK,SAAS,EAAC,kBAAkB;kBAACgB,OAAO,EAAEA,CAAA,KAAM5K,cAAc,CAACgE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAElE,WAAW,GAAG,CAAC,CAAC,CAAE;kBAAC8C,KAAK,EAAC,eAAe;kBAAAgH,QAAA,eACrHzK,OAAA;oBAAMwK,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAC;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,EACRtK,WAAW,GAAGE,UAAU,iBACvBb,OAAA;kBAAQwK,SAAS,EAAC,kBAAkB;kBAACgB,OAAO,EAAEA,CAAA,KAAM5K,cAAc,CAACgE,IAAI,CAACqI,GAAG,CAACpM,UAAU,EAAEF,WAAW,GAAG,CAAC,CAAC,CAAE;kBAAC8C,KAAK,EAAC,WAAW;kBAAAgH,QAAA,eAC1HzK,OAAA;oBAAMwK,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAC;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH,CAAC,eAGNjL,OAAA,CAACR,cAAc;MACb0N,MAAM,EAAE7L,gBAAiB;MACzB8L,OAAO,EAAEA,CAAA,KAAM7L,mBAAmB,CAAC,KAAK,CAAE;MAC1C0C,WAAW,EAAET,YAAY,CAACS,WAAY;MACtCoJ,MAAM,EAAGpJ,WAAW,IAAK;QACvBR,eAAe,CAAC4C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEpC;QAAY,CAAC,CAAC,CAAC;QACnD1C,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IAAE;MAAAwJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFjL,OAAA,CAACP,YAAY;MACXyN,MAAM,EAAE3L,aAAc;MACtB4L,OAAO,EAAE5C,oBAAqB;MAC9B8C,QAAQ,EAAErD;IAAiB;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAGFjL,OAAA,CAACN,YAAY;MACXwN,MAAM,EAAEvL,gBAAiB;MACzBwL,OAAO,EAAEA,CAAA,KAAMvL,mBAAmB,CAAC,KAAK,CAAE;MAC1C0L,gBAAgB,EAAEpH;IAAyB;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC/K,EAAA,CAjmBID,cAA6C;EAAA,QACzBb,SAAS,EAChBC,WAAW,EACXO,OAAO,EACwCC,YAAY;AAAA;AAAA0N,EAAA,GAJxEtN,cAA6C;AAmmBnD,eAAeA,cAAc;AAAC,IAAAsN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}