{"version": 3, "file": "static/css/main.daa20e95.css", "mappings": "AAIA,KAQE,wBAAyB,CAPzB,QAAS,CACT,SAOF,CAMA,WACE,YACF,CAEA,KACE,uEAEF,CAGA,oBACE,SACF,CAEA,0BACE,kBACF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CC1CA,uBACE,iBAAkB,CAClB,UACF,CAEA,qBAEE,kBAAmB,CAGnB,eAAiB,CAEjB,wBAAmB,CAAnB,kBAAmB,CACnB,yBAA0B,CAC1B,8BAAwC,CARxC,YAAa,CAEb,OAAQ,CAUR,MAAO,CAEP,eAAgB,CAChB,eAAgB,CAZhB,gBAAiB,CAOjB,iBAAkB,CAGlB,OAAQ,CAFR,SAAU,CAKV,kBAAmB,CAPnB,WAQF,CAEA,eAGE,OACF,CAEA,4BAJE,kBAAmB,CADnB,YAkBF,CAbA,aAIE,gBAAuB,CADvB,sBAA6B,CAE7B,iBAAkB,CAOlB,UAAW,CAHX,cAAe,CACf,cAAe,CARf,WAAY,CAMZ,sBAAuB,CAGvB,uBAAyB,CAVzB,UAYF,CAEA,mBACE,kBAAmB,CACnB,iBACF,CAEA,oBACE,kBAAmB,CAEnB,oBAAqB,CADrB,UAEF,CAEA,gBAKE,eAAiB,CAFjB,wBAAyB,CACzB,iBAAkB,CAGlB,cAAe,CADf,cAAe,CALf,WAAY,CAOZ,cAAe,CANf,eAOF,CAEA,sBAEE,oBAAqB,CADrB,YAEF,CAEA,eAME,eAAgB,CAHhB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,WAAY,CAKZ,SAAU,CANV,UAOF,CAEA,6CACE,SACF,CAEA,qCACE,WAAY,CACZ,iBACF,CAEA,mBAGE,kBAAmB,CADnB,WAAY,CAEZ,YAAa,CAHb,SAIF,CAEA,aAWE,oBAAqB,CANrB,eAAiB,CAFjB,wBAAyB,CACzB,iBAAkB,CAIlB,UAAW,CAKX,aAAc,CAPd,cAAe,CACf,eAAgB,CANhB,eAAgB,CAShB,YAAa,CAEb,wBAAyB,CAVzB,YAAa,CAYb,eAAgB,CALhB,uBAMF,CAEA,uCAEE,oBAAqB,CACrB,8BACF,CAEA,0BAEE,UAAW,CADX,8BAA+B,CAE/B,mBACF,CAEA,eACE,cACF,CAEA,0BACE,eACF,CAEA,gCAEE,mBAAoB,CACpB,SACF,CAEA,gBACE,iBACF,CAGA,yBACE,qBACE,eAAgB,CAChB,eACF,CAEA,aAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAEA,gBAGE,cAAe,CAFf,WAAY,CACZ,cAEF,CAEA,eAEE,WAAY,CADZ,UAEF,CACF,CCzKA,uBAQE,kBAAmB,CAFnB,oBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YACF,CAEA,qBAKE,eAAiB,CACjB,kBAAmB,CAGnB,+BAAyC,CAFzC,YAAa,CACb,qBAAsB,CALtB,UAAW,CACX,gBAAiB,CAFjB,eAAgB,CADhB,SASF,CAEA,sBAEE,kBAAmB,CAGnB,+BAAgC,CAJhC,YAAa,CAEb,6BAA8B,CAC9B,YAEF,CAEA,yBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,QAIF,CAEA,WAIE,kBAAmB,CAGnB,cAGF,CAEA,iBACE,kBAEF,CAEA,oBAEE,+BAAgC,CADhC,YAEF,CAEA,SAIE,gBAAuB,CAMvB,WAAoC,CAApC,6BAAoC,CALpC,UAAW,CAGX,cAAe,CAPf,QAAO,CAKP,cAAe,CACf,eAAgB,CALhB,iBAAkB,CAOlB,uBAEF,CAOA,+BAHE,kBAAmB,CADnB,aAQF,CAJA,gBAEE,2BAEF,CAEA,uBACE,QAAO,CAEP,eAAgB,CADhB,YAEF,CAEA,gBACE,kBACF,CAEA,iBAOE,kBAAmB,CANnB,sBAAuB,CACvB,iBAAkB,CAGlB,cAAe,CAFf,iBAAkB,CAClB,iBAAkB,CAElB,uBAEF,CAEA,uBAEE,kBAAmB,CADnB,oBAEF,CAEA,0BAEE,kBAAmB,CADnB,oBAAqB,CAErB,qBACF,CAOA,mBAGE,UAAW,CADX,cAAe,CADf,cAGF,CAEA,uBACE,UAAW,CACX,cACF,CAEA,aAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAEF,CAEA,YAME,eAAiB,CALjB,wBAAyB,CACzB,iBAAkB,CAElB,cAAe,CADf,eAAgB,CAEhB,uBAEF,CAEA,kBACE,oBAAqB,CAErB,+BAAyC,CADzC,0BAEF,CAEA,eACE,iBAAkB,CAElB,kBAAmB,CADnB,eAEF,CAEA,mBAEE,WAAY,CACZ,gBAAiB,CAFjB,UAGF,CAEA,YACE,YACF,CAEA,YAIE,UAAW,CAFX,cAAe,CACf,eAAgB,CAFhB,cAAiB,CAIjB,eAAgB,CAChB,sBAAuB,CACvB,kBACF,CAEA,YAKE,kBAAmB,CACnB,iBAAkB,CAJlB,UAAW,CADX,cAAe,CAGf,eAAgB,CADhB,wBAIF,CAEA,aAIE,UAAW,CAHX,gBAAmB,CAEnB,iBAAkB,CADlB,iBAGF,CAEA,eAEE,cAAe,CADf,QAEF,CAEA,sBAEE,4BAA6B,CAD7B,YAAa,CAEb,gBACF,CC7MA,YAKE,eAAmB,CAFnB,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAIZ,iBAAkB,CALlB,UAMF,CAGA,aAKE,UACF,CAEA,aAEE,cAKF,CAEA,gBACE,eACF,CAEA,2BACE,wBAA0B,CAO1B,gBACF,CAGA,cASE,kBAAmB,CAHnB,uBAA2B,CAC3B,2BAA4B,CAF5B,qBAAsB,CAFtB,cAAe,CAKf,YAAa,CAPb,QAAO,CASP,sBAAuB,CANvB,eAAgB,CAFhB,iBASF,CAIA,2BAME,6XAQoH,CAIpH,uDAEgB,CALhB,2EAEsB,CAItB,0BAAyC,CACzC,iBAAkB,CAjBlB,WAAY,CAFZ,SAAU,CAoBV,mBAAoB,CAtBpB,iBAAkB,CAGlB,UAAW,CAFX,QAAS,CAsBT,SACF,CAGA,gBAGE,iBAAkB,CAFlB,iBAAkB,CAClB,SAEF,CAMA,+BACE,wBAA0B,CAS1B,kBAAmB,CAFnB,iBAAkB,CAClB,wBAEF,CAEA,kBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CAEnB,eAAgB,CADhB,wBAEF,CAEA,aACE,cAAe,CACf,kBACF,CAEA,sBAEE,cAAe,CACf,eAAgB,CAFhB,cAGF,CAEA,0BAEE,UAAW,CADX,cAEF,CAGA,aAKE,oBAAoC,CAKpC,qBAAsB,CAHtB,kBAAmB,CALnB,WAAY,CAOZ,UAAW,CADX,cAAe,CALf,QAAS,CAGT,gBAAiB,CALjB,iBAAkB,CAGlB,0BAA2B,CAO3B,SACF,CAEA,iCACE,oBAAqC,CACrC,oBAAqB,CACrB,aACF,CAGA,qBAEE,UAIF,CAEA,eAYE,kBAAmB,CATnB,kDAA6D,CAE7D,WAAY,CACZ,iBAAkB,CAIlB,+BAA8C,CAN9C,UAAY,CAIZ,cAAe,CAGf,YAAa,CAJb,cAAe,CALf,WAAY,CAWZ,sBAAuB,CAJvB,uBAAyB,CARzB,UAaF,CAEA,qBAEE,+BAA8C,CAD9C,oBAEF,CAGA,kEAEE,iCAA0B,CAA1B,yBAA0B,CAD1B,0BAEF,CAEA,uDACE,oBAAqC,CAErC,kBAAmB,CACnB,2BAAyC,CAFzC,YAGF,CAGA,yBACE,+BACE,wBAA0B,CAC1B,kBACF,CAEA,kBACE,cAAe,CACf,kBACF,CAEA,2BAIE,WAAY,CAFZ,SAAU,CACV,UAAW,CAFX,QAIF,CAEA,qBACE,UACF,CAEA,eAGE,cAAe,CADf,WAAY,CADZ,UAGF,CACF,CAEA,iBAIE,kBAAmB,CAFnB,YAAa,CADb,QAAO,CAEP,qBAAsB,CAEtB,sBAAuB,CAEvB,aAAc,CADd,eAEF,CAEA,iCAGE,kBAAmB,CADnB,UAEF,CAEA,2BAME,iCAA0B,CAA1B,yBAA0B,CAD1B,oBAAoC,CAEpC,0BAAyC,CALzC,wBAA0B,CAC1B,eAAiB,CAFjB,eAOF,CAEA,8BAKE,iCAA0B,CAA1B,yBAA0B,CAD1B,gBAAoC,CAEpC,0BAAyC,CAJzC,wBAA0B,CAD1B,eAAgB,CAEhB,iBAIF,CAEA,qBAEE,UAGF,CAEA,UAUE,kBAAmB,CALnB,WAAY,CACZ,iBAAkB,CAOlB,+BAA8C,CAN9C,cAAe,CAEf,YAAa,CADb,cAAe,CANf,WAAY,CASZ,sBAAuB,CACvB,uBAAyB,CAXzB,UAaF,CAEA,gBAGE,+BAA8C,CAD9C,qCAEF,CAUA,oBAEE,WAAY,CADZ,iBAAkB,CAElB,UAAW,CACX,WAAY,CACZ,SACF,CAEA,2BACE,eAAiB,CAIjB,wBAAyB,CAFzB,iBAAkB,CAClB,+BAAyC,CAFzC,YAAa,CAIb,iBACF,CAEA,YAKE,kBAAmB,CADnB,YAAa,CAGb,OAAQ,CALR,WAAY,CAIZ,sBAAuB,CAHvB,kBAAmB,CAFnB,iBAOF,CAEA,MAEE,mCAAoC,CADpC,cAEF,CAEA,QACE,mBAAoB,CACpB,2BACF,CAEA,QACE,mBACF,CAWA,8BAIE,UAAW,CAFX,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CAJnB,eAKF,CAEA,aACE,kBACF,CAEA,YAGE,kBAAmB,CAFnB,YAAa,CAIb,cAAe,CAHf,6BAA8B,CAE9B,iBAEF,CAEA,kBAEE,UAAW,CADX,eAEF,CAEA,iBACE,UACF,CAEA,aACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAMZ,cAAe,CAFf,cAAe,CACf,eAAgB,CAGhB,mBAAqB,CANrB,gBAAiB,CAKjB,8BAEF,CAEA,mBACE,kBACF,CAGA,aAGE,eAAiB,CACjB,+BAAgC,CAFhC,cAAe,CADf,iBAIF,CAEA,aAIE,UAAW,CAFX,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CAJnB,eAAkB,CAKlB,wBACF,CAEA,gBACE,kBACF,CAEA,2BAKE,gBAAuB,CADvB,WAAY,CAEZ,UAAW,CALX,wBAA0B,CAC1B,yBAA2B,CAK3B,eAAgB,CAJhB,iBAKF,CAGA,aAGE,kBAAmB,CAInB,kBAAmB,CALnB,YAAa,CADb,QAAO,CAGP,sBAAuB,CACvB,YAAa,CACb,iBAEF,CAEA,iBAGE,eAAiB,CACjB,iBAAkB,CAClB,+BAAyC,CAHzC,YAAa,CAKb,eAAgB,CANhB,WAOF,CAEA,kBAME,wBAAyB,CACzB,iBAAkB,CAFlB,WAAY,CAFZ,SAAU,CAKV,mBAAoB,CAPpB,iBAAkB,CAGlB,UAAW,CAFX,QAAS,CAOT,SACF,CAEA,iBAOE,kBAAmB,CAFnB,QAAS,CACT,YAAa,CAEb,sBAAuB,CALvB,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAON,SACF,CAEA,iBAGE,iBAAkB,CAFlB,iBAAkB,CAClB,SAEF,CAEA,+BAME,gBAAuB,CADvB,WAAY,CADZ,aAAc,CAHd,wBAA0B,CAC1B,yBAA4B,CAM5B,kBAAmB,CADnB,eAAgB,CAJhB,iBAMF,CAEA,iBACE,iBACF,CAEA,sBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,kBACF,CAKA,kBACE,iBAAkB,CAClB,UAAW,CACX,OAAQ,CACR,0BAA2B,CAC3B,UACF,CAEA,gBAGE,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAIlB,+BAA8C,CAN9C,UAAY,CAIZ,cAAe,CADf,cAAe,CALf,WAAY,CAOZ,uBAAyB,CARzB,UAUF,CAEA,sBACE,kBAAmB,CACnB,oBACF,CAGA,mBAKE,QAAS,CAFT,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAIN,SACF,CAEA,oBAOE,oBAAoC,CAGpC,sBAAuB,CADvB,iBAAkB,CAHlB,UAAW,CAKX,cAAe,CARf,QAAS,CAKT,YAAa,CAPb,iBAAkB,CAIlB,iBAAkB,CAHlB,OAAQ,CAER,8BAAgC,CAQhC,uBACF,CAEA,0BAEE,oBAAqC,CADrC,oBAEF,CAGA,yBACE,aACE,YACF,CAEA,iBAGE,YAAa,CADb,eAAgB,CADhB,UAGF,CAEA,+BACE,wBACF,CAEA,kBAGE,eAAgB,CAFhB,eAAgB,CAChB,iBAAkB,CAElB,cACF,CACF,CCrkBA,aAIE,eAAiB,CAGjB,qBAAsB,CALtB,WAAY,CAGZ,eAAgB,CAChB,YAAa,CAHb,iBAAkB,CAFlB,UAOF,CAGA,gBACE,WAAY,CAEZ,wBAAiB,CAAjB,gBACF,CAEA,sBAEE,+BAA+C,CAD/C,0BAEF,CAEA,yBAIE,+BAAyC,CAHzC,UAAY,CACZ,iCAAmC,CACnC,YAEF,CAEA,mDAEE,oBAAmC,CACnC,yBAA0B,CAC1B,iBAAkB,CAClB,gBACF,CAEA,+BACE,4BAA6B,CAC7B,cACF,CAEA,aAQE,kBAAmB,CAKnB,oBAAmC,CACnC,iBAAkB,CAClB,UAAY,CALZ,WAAY,CAHZ,YAAa,CASb,cAAe,CAVf,WAAY,CAGZ,sBAAuB,CAPvB,UAAW,CASX,SAAU,CAVV,iBAAkB,CAElB,OAAQ,CACR,0BAA2B,CAQ3B,2BAA6B,CAP7B,UAYF,CAEA,oBACE,eACF,CAEA,mCACE,SACF,CAEA,qBAcE,6BAA8B,CAT9B,kBAAmB,CAGnB,kBAAmB,CANnB,YAAa,CAIb,UAAY,CAGZ,cAAe,CACf,eAAgB,CAPhB,QAAS,CAST,mBAAqB,CALrB,gBAAiB,CANjB,iBAAkB,CAUlB,wBAAyB,CAPzB,0BAA2B,CAS3B,WAEF,CAEA,iBACE,MAAW,SAAU,CAAE,mCAAsC,CAC7D,IAAM,UAAY,CAAE,sCAAyC,CAC/D,CAEA,gBAME,eAAiB,CAgBjB,sCAAwC,CAdxC,oKAYG,CACH,wCAA0C,CAd1C,uBAA8B,CAiB9B,iBAAkB,CADlB,+BAA0C,CApB1C,YAAa,CADb,wBAAyB,CAEzB,iBAAkB,CAHlB,UAAW,CAIX,SAoBF,CAEA,aAGE,kDAA6D,CAC7D,8BAGF,CAEA,2BAJE,YAAa,CAJb,QAAO,CAKP,qBAAsB,CAJtB,iBAaF,CANA,cAGE,eAGF,CAEA,cACE,kBACF,CAEA,iBAIE,aAAc,CAEd,iCAAqC,CAJrC,cAAe,CACf,eAAiB,CAIjB,kBAAmB,CANnB,QAAS,CAIT,iBAGF,CAEA,eAEE,YAAa,CACb,sBAAuB,CAFvB,kBAGF,CAEA,iBAGE,cAAe,CADf,YAAa,CAEb,iBAAkB,CAHlB,WAIF,CAEA,eAME,wBAAyB,CAHzB,iBAAkB,CADlB,WAAY,CAEZ,eAAgB,CAChB,iBAAkB,CAElB,uBAAyB,CANzB,UAOF,CAEA,qBAEE,+BAA8C,CAD9C,qBAEF,CAEA,mBAEE,WAAY,CACZ,gBAAiB,CAFjB,UAGF,CAEA,eAQE,kBAAmB,CAFnB,oBAAmC,CADnC,QAAS,CAOT,UAAY,CALZ,YAAa,CAMb,cAAe,CACf,eAAgB,CALhB,sBAAuB,CANvB,MAAO,CAOP,SAAU,CATV,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CASN,2BAIF,CAEA,oCACE,SACF,CAEA,mBAOE,kBAAmB,CAEnB,oBAAoC,CANpC,yBAA0B,CAC1B,iBAAkB,CAOlB,aAAc,CANd,YAAa,CACb,qBAAsB,CAJtB,WAAY,CAMZ,sBAAuB,CAEvB,uBAAyB,CATzB,UAWF,CAEA,yBACE,oBAAmC,CACnC,qBACF,CAEA,kBACE,cAAe,CACf,iBACF,CAEA,qBAEE,cAAe,CACf,eAAgB,CAEhB,eAAgB,CAJhB,QAAS,CAGT,iBAEF,CAEA,+BAGE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,QAAS,CACT,eACF,CAEA,cACE,kBAAmB,CACnB,iBACF,CAEA,iBAIE,aAAc,CACd,iCAAqC,CAHrC,cAAe,CACf,eAAiB,CAGjB,mBAAqB,CALrB,QAMF,CAEA,gBACE,oBAAoC,CAIpC,0BAAyC,CAHzC,iBAAkB,CAElB,8BAAwC,CAGxC,kBAAmB,CAJnB,YAAa,CAGb,uBAEF,CAEA,6BACE,eACF,CAEA,8BACE,kBACF,CAEA,sBACE,+BACF,CAEA,gBAGE,kBAAmB,CAGnB,+BAAgC,CALhC,YAAa,CACb,6BAA8B,CAE9B,kBAAmB,CACnB,kBAAmB,CAEnB,iBACF,CAEA,mBAGE,aAAc,CAEd,iCAAqC,CAHrC,cAAe,CAEf,eAAiB,CAHjB,QAKF,CAEA,oBACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAFlB,aAAc,CAFd,cAAe,CACf,cAAe,CAEf,WAAY,CAEZ,uBACF,CAEA,0BACE,oBAAkC,CAClC,oBACF,CAEA,iBACE,iBACF,CAEA,iCAKE,eAAiB,CADjB,wBAAyB,CAFzB,cAAe,CACf,eAAgB,CAFhB,eAKF,CAEA,+EAEE,oBACF,CAEA,qBAME,qBAAsB,CACtB,QAAS,CAJT,WAAY,CAKZ,UACF,CAEA,SAYE,eAAiB,CATjB,wBAAyB,CAQzB,+BAA8C,CAE9C,aAAc,CAPd,cAAe,CAJf,WAAY,CADZ,UAaF,CAEA,gBACE,kBAAmB,CAEnB,oBAAqB,CADrB,UAEF,CAEA,gBACE,kBAAmB,CAEnB,oBAAqB,CADrB,UAEF,CA2BA,sBAME,QAAS,CACT,SACF,CAEA,6CAGE,kBAAmB,CAGnB,iBAAkB,CAClB,cAAe,CACf,eAAgB,CANhB,gBAAiB,CAQjB,uBAEF,CAEA,yDAEE,kBAAmB,CACnB,0BACF,CAEA,oBAME,2LAGkF,CAJlF,QAAS,CAFT,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAQN,SACF,CAGA,yBACE,gBACE,qBACF,CAEA,2BAEE,SAAU,CACV,WACF,CAEA,aAEE,+BAAgC,CADhC,iBAEF,CAEA,qBAIE,aACF,CAEA,2CAJE,sBAAuB,CAFvB,eAAgB,CAChB,cAUF,CALA,sBAIE,eACF,CAEA,iBACE,cACF,CAEA,iBAEE,YAAa,CADb,WAEF,CACF,CAGA,sBAEE,WAAY,CAGZ,YAAa,CACb,QAAS,CAHT,QAAS,CAFT,iBAAkB,CAGlB,0BAA2B,CAG3B,UACF,CAEA,6CAEE,kDAAqD,CAErD,WAAY,CAEZ,kBAAmB,CAOnB,+BAA8C,CAV9C,UAAY,CAQZ,cAAe,CAJf,cAAe,CACf,eAAiB,CAEjB,mBAAqB,CALrB,iBAAkB,CAIlB,wBAAyB,CAGzB,uBAEF,CAEA,yDAIE,kDAAqD,CADrD,+BAA8C,CAD9C,0BAGF,CChgBA,yBAQE,kBAAmB,CAGnB,iCAA0B,CAA1B,yBAA0B,CAL1B,gBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,uBAIE,qBAAsB,CACtB,iBAAkB,CAClB,gCAA2C,CAJ3C,eAAgB,CAKhB,eAAgB,CANhB,SASF,CAEA,qCATE,eAAiB,CAKjB,YAAa,CACb,qBAWF,CARA,cAGE,kBAAmB,CAGnB,UAAW,CAFX,sBAIF,CAGA,aAEE,kBAAmB,CADnB,YAAa,CAGb,OAAQ,CAER,WAAY,CAHZ,sBAAuB,CAEvB,kBAEF,CAEA,WAEE,mCAAoC,CADpC,cAEF,CAEA,cACE,mBAAoB,CACpB,2BACF,CAEA,cACE,mBACF,CAEA,kBACE,GAAO,sBAAyB,CAChC,GAAK,uBAA2B,CAClC,CAEA,iBAEE,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CAJnB,QAAS,CAGT,iBAEF,CAEA,WAOE,kBAAmB,CAInB,cAKF,CAEA,gCACE,oBAAoC,CACpC,qBACF,CAEA,oBAEE,kBAAmB,CADnB,UAEF,CAEA,cAEE,QAAO,CACP,eAAgB,CAFhB,YAGF,CAEA,YACE,kBACF,CAEA,kBAKE,UAAW,CAJX,aAAc,CAEd,cAAe,CACf,eAAgB,CAFhB,iBAIF,CAEA,kBAOE,gBAAuB,CAHvB,WAA6B,CAA7B,4BAA6B,CAI7B,qBAAsB,CAEtB,aAAc,CALd,cAAe,CAIf,YAAa,CAPb,cAAe,CASf,eAAgB,CALhB,uBAAyB,CALzB,UAWF,CAEA,wBACE,2BACF,CAEA,wBACE,oBAAqB,CACrB,8BACF,CAEA,2BACE,kBAAmB,CACnB,UAAW,CACX,kBACF,CAEA,eAEE,aAAc,CAGd,eACF,CAEA,2BAPE,aAAc,CAEd,cAAe,CACf,cAUF,CANA,YAEE,UAAW,CAGX,iBACF,CAEA,cAKE,4BAA6B,CAJ7B,YAAa,CAEb,QAAS,CADT,wBAAyB,CAIzB,eAAgB,CAFhB,gBAGF,CAEA,KAUE,OAAQ,CACR,eAEF,CAEA,cACE,UAAY,CAEZ,cACF,CAEA,eACE,kBAAmB,CACnB,UACF,CAEA,oCACE,kBAAmB,CACnB,0BACF,CAEA,aACE,kBAAmB,CACnB,UACF,CAEA,kCACE,kBAAmB,CACnB,0BACF,CAEA,iBAIE,0BAA2B,CAF3B,WAAY,CADZ,UAMF,CAOA,iBACE,kBAAmB,CAEnB,4BAA6B,CAD7B,iBAEF,CAEA,oBAIE,UAAW,CAFX,cAAe,CACf,eAAgB,CAGhB,mBAAqB,CALrB,eAAkB,CAIlB,wBAEF,CAEA,cACE,eAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CAClB,YACF,CAEA,cAEE,cAAe,CACf,eAAgB,CAFhB,iBAGF,CAEA,yBACE,eACF,CAEA,qBACE,aAAc,CAId,oBAAqB,CAHrB,eAAgB,CAChB,gBAAiB,CACjB,cAEF,CAGA,yBACE,uBAEE,eAAgB,CADhB,SAEF,CAEA,6CAGE,YACF,CAEA,cACE,qBACF,CAEA,KACE,UACF,CAEA,qBAEE,aAAc,CACd,iBAAkB,CAClB,cAAe,CAHf,cAIF,CACF,CAGA,uBACE,iBACF,CAEA,oCACE,YACF,CAGA,uBACE,mCACF,CAEA,wBACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CChUA,uBAQE,kBAAmB,CAGnB,iCAA0B,CAA1B,yBAA0B,CAL1B,oBAA8B,CAC9B,YAAa,CAFb,YAAa,CAIb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,WAAY,CAMZ,YAEF,CAEA,qBAGE,eAAiB,CACjB,qBAAsB,CACtB,eAAgB,CAGhB,+BAA0C,CAN1C,cAAe,CAKf,sBAAuB,CADvB,iBAAkB,CALlB,WAQF,CA+BA,eACE,kBACF,CAEA,WAEE,oBAAqB,CAErB,WAAY,CACZ,aAAc,CAJd,iBAAkB,CAElB,UAGF,CAEA,YAWE,kBAAmB,CAPnB,eAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CAOlB,gCAA0C,CAH1C,YAAa,CAPb,WAAY,CASZ,sBAAuB,CAJvB,SAAU,CAPV,iBAAkB,CAMlB,OAAQ,CAOR,uBAAwB,CAZxB,UAAW,CAOX,SAMF,CAEA,WAGE,aAAc,CACd,iBAAkB,CAHlB,cAAe,CACf,eAGF,CAEA,YAIE,kBAAmB,CACnB,qBAAsB,CACtB,iBAAkB,CAIlB,gCAA0C,CAP1C,WAAY,CAKZ,QAAS,CAPT,iBAAkB,CAMlB,OAAQ,CALR,UAAW,CAOX,SAEF,CAEA,kBAKE,kBAAmB,CACnB,qBAAsB,CACtB,iBAAkB,CANlB,UAAW,CAGX,WAAY,CAKZ,QAAS,CAPT,iBAAkB,CAMlB,OAAQ,CALR,UAAW,CAOX,SACF,CAGA,aAKE,kBAAmB,CADnB,eAGF,CAGA,WACE,UACF,CAEA,eACE,kBAAmB,CACnB,iBACF,CAEA,YAEE,gBAAuB,CAEvB,WAA6B,CAA7B,4BAA6B,CAG7B,UAAW,CAGX,aAAc,CAJd,cAAe,CAEf,YAAa,CAHb,cAAe,CAMf,eAAgB,CAFhB,gCAAkC,CARlC,UAWF,CAEA,kBACE,2BACF,CAEA,yBACE,UAAW,CACX,iBACF,CAGA,YACE,kBAAmB,CAQnB,kBAAmB,CAGnB,+BAA6C,CAP7C,cAAe,CAEf,kBAAmB,CAHnB,iBAAkB,CAIlB,wBAAyB,CAGzB,uBAEF,CAEA,iCACE,kBAAmB,CAEnB,+BACF,CAEA,qBACE,eAIF,CAGA,yBACE,qBAEE,sBAAuB,CADvB,WAEF,CAEA,aACE,cAAe,CACf,kBACF,CAEA,WAEE,WAAY,CADZ,UAEF,CAEA,YAEE,WAAY,CADZ,UAEF,CAEA,WACE,cACF,CACF,CAEA,yBACE,qBAEE,sBAAuB,CADvB,WAEF,CACF,CC5NA,wBAQE,kBAAmB,CAGnB,iCAA0B,CAA1B,yBAA0B,CAL1B,gBAA8B,CAC9B,YAAa,CAFb,YAAa,CAIb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,WAAY,CAMZ,YAEF,CAEA,gBAKE,eAAiB,CACjB,qBAAsB,CACtB,eAAgB,CAIhB,gCAA0C,CAF1C,YAAa,CACb,qBAAsB,CAPtB,WAAY,CACZ,gBAAiB,CAFjB,gBAAiB,CAMjB,iBAAkB,CAPlB,UAWF,CAGA,cAGE,+BAAgC,CAFhC,iBAAkB,CAGlB,iBAAkB,CAFlB,iBAGF,CAEA,WAeE,iBAAkB,CAPlB,UAAW,CADX,eAAiB,CAIjB,WAAY,CARZ,UAAW,CAaX,uBAAyB,CANzB,UAOF,CAEA,iBACE,kBAAmB,CACnB,UACF,CAEA,aAGE,UAAW,CAFX,cAAe,CACf,eAAgB,CAGhB,kBAAmB,CADnB,cAAiB,CAEjB,wBACF,CAEA,gBAEE,UAAW,CADX,cAAe,CAGf,iBAAkB,CADlB,QAEF,CAGA,eACE,QAAO,CACP,eAAgB,CAChB,iBACF,CAEA,kBACE,kBACF,CAEA,eAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAIhB,kBAAmB,CAFnB,eAAkB,CAClB,wBAEF,CAEA,gBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAGA,eAKE,sBAA6B,CAF7B,iBAAkB,CAFlB,cAAe,CAGf,eAAgB,CAFhB,uBAIF,CAEA,qBAEE,+BAA0C,CAD1C,0BAEF,CAEA,wBACE,oBAAqB,CACrB,+BACF,CAEA,kBAGE,kBAAmB,CACnB,wBAAyB,CAFzB,YAAa,CAIb,eAAgB,CADhB,iBAAkB,CAJlB,UAMF,CAEA,yBACE,iBACF,CAEA,YAME,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAIZ,cAAe,CANf,WAAY,CAJZ,iBAAkB,CAElB,SAAU,CADV,OAAQ,CAER,UAAW,CAWX,UACF,CAEA,2BALE,kBAAmB,CADnB,YAAa,CAFb,cAAe,CAIf,sBAgBF,CAZA,eAME,kBAAmB,CACnB,4BAA6B,CAF7B,UAAW,CADX,eAAgB,CAOhB,OAAQ,CAVR,gBAAiB,CACjB,iBAUF,CAEA,SACE,aAAc,CACd,cACF,CAGA,iBAKE,OAAQ,CAHR,WAAY,CAEZ,WAAY,CAHZ,UAKF,CASA,6BALE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,OAQF,CAEA,mBACE,eAAgB,CAChB,qBAAsB,CACtB,QAAO,CACP,eACF,CAEA,YACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,WAEE,eAAgB,CAChB,iBAAkB,CAFlB,UAGF,CAEA,gBACE,SACF,CAEA,YACE,YAAa,CACb,qBAAsB,CACtB,OAAQ,CACR,iBACF,CAEA,YAGE,YAAQ,CAFR,YAAa,CAEb,OAAQ,CADR,mCAEF,CAEA,aAGE,cACF,CAGA,+BANE,eAAgB,CAChB,qBASF,CAJA,kBACE,UAGF,CAEA,aAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,OACF,CAEA,aAEE,eAAgB,CADhB,UAAW,CAEX,iBACF,CAEA,kBACE,UAAW,CACX,SACF,CAEA,WAGE,YAAQ,CAFR,YAAa,CAGb,QAAO,CADP,OAAQ,CADR,mCAGF,CAQA,uBALE,eAAgB,CAChB,qBAUF,CANA,iBAEE,UAAW,CAGX,iBAAkB,CAJlB,UAKF,CAEA,gBACE,YAAa,CACb,OAAQ,CACR,UACF,CAEA,iBAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,OACF,CAEA,YACE,YAAa,CAEb,QAAO,CADP,OAEF,CAGA,gBAEE,eAAgB,CAChB,qBAAsB,CAFtB,QAGF,CAEA,eAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,OACF,CAGA,iBAEE,kBAAmB,CAInB,kBAAmB,CALnB,YAAa,CAIb,WAAY,CAFZ,sBAAuB,CACvB,UAGF,CAEA,kBAEE,UAAW,CADX,cAAe,CAEf,iBACF,CAGA,sBACE,YAAa,CACb,QAAS,CACT,eACF,CAEA,gBAME,kBAAmB,CAFnB,kBAAmB,CADnB,yBAA0B,CAK1B,iBAAkB,CAHlB,YAAa,CAHb,YAAa,CAKb,sBAAuB,CANvB,WAQF,CAEA,mBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAGhB,kBAAmB,CACnB,eAAgB,CANhB,iBAAkB,CAIlB,wBAGF,CAEA,kBAIE,aAAS,CAFT,YAAa,CADb,QAAO,CAGP,QAAS,CADT,wDAEF,CAEA,cACE,WAAY,CACZ,6BACF,CAEA,oBACE,qBACF,CAEA,iBAGE,eAAiB,CACjB,qBAAsB,CACtB,iBAAkB,CAElB,YAAa,CACb,qBAAsB,CACtB,OAAQ,CAPR,WAAY,CAIZ,WAAY,CALZ,UASF,CAGA,yCACE,QACF,CAEA,2CAEE,eAAgB,CAChB,qBAAsB,CAFtB,WAGF,CAEA,wCAEE,eAAgB,CADhB,UAAW,CAEX,iBACF,CAEA,yCAEE,eAAgB,CADhB,UAAW,CAEX,iBACF,CAEA,yCACE,YAAa,CAEb,QAAO,CADP,OAEF,CAEA,yCAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,OACF,CAEA,yCAEE,eAAgB,CADhB,UAAW,CAEX,iBACF,CAEA,6CACE,YAAa,CAEb,QAAO,CADP,OAEF,CAEA,6EAEE,QACF,CAEA,sCACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,0CAEE,eAAgB,CADhB,UAAW,CAEX,iBACF,CAEA,2CAGE,YAAQ,CAFR,YAAa,CAGb,QAAO,CADP,OAAQ,CADR,mCAGF,CAEA,wCACE,eAAgB,CAChB,qBACF,CAGA,cAEE,4BAA6B,CAC7B,YAAa,CACb,wBAAyB,CAHzB,iBAIF,CAOA,wCAHE,kBAAmB,CADnB,YAsBF,CAlBA,iBACE,eAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CASlB,aAAc,CANd,cAAe,CAEf,qBAAsB,CAKtB,cAAe,CACf,eAAgB,CAThB,WAAY,CAKZ,sBAAuB,CAMvB,mBAAqB,CACrB,eAAgB,CAFhB,wBAAyB,CAJzB,uBAAyB,CAPzB,UAcF,CAEA,sCACE,kBAAmB,CACnB,UAAY,CACZ,oBACF,CAEA,0BAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAEA,aACE,iBACF,CAEA,WACE,cAAe,CACf,eACF,CAGA,yBACE,gBAEE,WAAY,CADZ,UAEF,CAEA,eACE,iBACF,CAEA,gBAEE,QAAS,CADT,wDAEF,CAEA,kBACE,YACF,CAEA,sBACE,qBACF,CAEA,gBAEE,YAAa,CADb,UAEF,CAEA,kBAEE,QAAS,CADT,wDAEF,CAEA,iBACE,WACF,CACF,CAEA,yBACE,cACE,iBACF,CAEA,aACE,cACF,CAEA,gBACE,wDACF,CAEA,iBAGE,aAAc,CADd,WAAY,CADZ,UAGF,CAEA,WACE,cACF,CACF,CCpkBA,cACE,kDAA6D,CAgB7D,WAAkB,CAAlB,+BAAkB,CAblB,eAAgB,CAahB,yBAAkB,CANlB,8BAAyC,CACzC,cAAe,CAHf,yCAA8C,CAC9C,cAAe,CAFf,QAAS,CAOT,QAAS,CAVT,iBAAkB,CASlB,WAIF,CAEA,+BAbE,kBAAmB,CADnB,YAAa,CAOb,iBAYF,CALA,iBAGE,OAEF,CAEA,eAEE,UAAW,CACX,cAAe,CAFf,eAAiB,CAIjB,mBAAqB,CADrB,wBAEF,CAEA,qBAEE,UAAW,CADX,cAAe,CAGf,mBAAqB,CACrB,gBAAiB,CAFjB,wBAGF,CAGA,iFAOE,eAAiB,CAFjB,qBAAsB,CACtB,iBAAkB,CAIlB,cAAe,CAFf,cAAe,CACf,cAAe,CALf,eAAgB,CAOhB,gCACF,CAEA,oBACE,eACF,CAEA,kBACE,cACF,CAEA,2CAEE,cACF,CAEA,yGAIE,iBACF,CAEA,yGAKE,oBAAqB,CACrB,4BAA2C,CAF3C,YAGF,CAGA,iDAKE,qBAAsB,CACtB,iBAAkB,CAHlB,YAAa,CACb,OAAQ,CAGR,eACF,CAEA,YAWE,kBAAmB,CAVnB,eAAiB,CAYjB,WAA4B,CAA5B,2BAA4B,CAT5B,cAAe,CAMf,YAAa,CAJb,mBAAoB,CADpB,cAAe,CAIf,WAAY,CAGZ,sBAAuB,CAJvB,cAAe,CALf,eAAgB,CAIhB,uBAOF,CAEA,uBACE,iBACF,CAEA,kBACE,kBACF,CAEA,mBACE,kBAAmB,CACnB,UACF,CAEA,mBACE,yBACF,CAGA,eACE,YAAa,CACb,OACF,CAEA,wBACE,iBACF,CAEA,WASE,kBAAmB,CARnB,eAAiB,CACjB,qBAAsB,CACtB,iBAAkB,CAElB,cAAe,CAGf,YAAa,CADb,WAAY,CAGZ,sBAAuB,CANvB,WAAY,CAOZ,uBAAyB,CALzB,UAMF,CAEA,iBACE,iBAAkB,CAClB,8BACF,CAEA,iBAOE,kBAAmB,CAEnB,iBAAkB,CANlB,aAAc,CAGd,YAAa,CAJb,cAAe,CADf,eAAiB,CAIjB,WAAY,CAGZ,sBAAuB,CAJvB,UAMF,CAEA,cACE,cACF,CAEA,cAIE,eAAiB,CACjB,qBAAsB,CACtB,iBAAkB,CAElB,+BAA0C,CAL1C,MAAO,CAOP,cAAe,CAHf,WAAY,CANZ,iBAAkB,CAClB,QAAS,CAOT,YAEF,CAEA,YAGE,YAAQ,CAFR,YAAa,CAEb,OAAQ,CADR,mCAAqC,CAErC,WACF,CAEA,cAGE,qBAAsB,CACtB,iBAAkB,CAClB,cAAe,CAHf,WAAY,CAIZ,6BAA+B,CAL/B,UAMF,CAEA,oBAEE,iBAAkB,CADlB,oBAEF,CAGA,UAUE,kBAAmB,CATnB,eAAiB,CACjB,qBAAsB,CACtB,iBAAkB,CAElB,cAAe,CAIf,YAAa,CAHb,cAAe,CAEf,WAAY,CAGZ,sBAAuB,CAPvB,eAAgB,CAQhB,uBAAyB,CALzB,UAMF,CAEA,gBACE,kBAAmB,CACnB,iBACF,CAGA,yCAIE,eAAgB,CAHhB,UAAW,CAEX,WAAY,CAEZ,YAAa,CAHb,SAIF,CAEA,qDACE,YACF,CAGA,yBACE,cAGE,cAAe,CADf,OAAQ,CADR,eAGF,CAEA,oBACE,cACF,CAEA,YAIE,cAAe,CAFf,WAAY,CADZ,cAAe,CAEf,eAEF,CAEA,qBAGE,WAAY,CADZ,UAEF,CAEA,yCACE,YACF,CACF,CAEA,yBACE,cACE,cAAe,CAEf,OAAQ,CADR,eAEF,CAEA,iBACE,aACF,CAEA,sCAEE,cACF,CAEA,YACE,mCAAqC,CACrC,WACF,CACF,CCtSA,iBAIE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAFtB,YAIF,CAEA,gBAGE,YACF,CAGA,yBACE,kDAA6D,CAC7D,UAAY,CACZ,cACF,CAEA,mCACE,YAAa,CAGb,eAAgB,CADhB,QAAS,CADT,sBAAuB,CAIvB,kBAAmB,CADnB,wBAEF,CAEA,mCAIE,iBAAkB,CAHlB,UAAY,CAEZ,gBAAiB,CADjB,oBAAqB,CAGrB,8BACF,CAEA,yCACE,oBACF,CAOA,+BAHE,eAAiB,CADjB,+BAWF,CAPA,gBAGE,6BAA8B,CAC9B,iBAGF,CAEA,8BAPE,kBAAmB,CADnB,YAYF,CAJA,cAGE,QACF,CAEA,gBAME,kBAAmB,CAFnB,aAAmC,CAAnC,iCAAmC,CAInC,cAAe,CAHf,YAAa,CAHb,cAAe,CACf,eAAgB,CAIhB,OAAQ,CANR,QAAS,CAQT,yBACF,CAEA,sBACE,aAAqC,CAArC,mCACF,CAEA,WACE,kDAA6D,CAK7D,iBAAkB,CAJlB,UAAY,CACZ,cAAe,CACf,eAAgB,CAIhB,mBAAqB,CAHrB,eAAgB,CAEhB,wBAEF,CAEA,YAEE,aAAiC,CAAjC,+BAAiC,CADjC,cAAe,CAEf,iBACF,CAEA,gBAEE,kBAAmB,CAGnB,kBAAmB,CAEnB,wBAAyB,CADzB,iBAAkB,CALlB,YAAa,CAEb,QAAS,CACT,gBAIF,CAEA,WAEE,UAAW,CADX,cAAe,CAEf,cAAe,CACf,iBACF,CAEA,eACE,YAAa,CACb,QACF,CAEA,gBAEE,YAAa,CADb,QAAO,CAEP,eACF,CAEA,gBAEE,kBAAmB,CAInB,8BAA+B,CAH/B,UAAY,CACZ,YAAa,CACb,qBAAsB,CAJtB,WAMF,CAEA,iCACE,+BACF,CAGA,eACE,gBACF,CAEA,WAGE,YAAQ,CAFR,YAAa,CAEb,OAAQ,CADR,mCAEF,CAEA,UAUE,kBAAmB,CATnB,kBAAmB,CACnB,wBAAyB,CAGzB,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CAGf,YAAa,CADb,cAAe,CAGf,sBAAuB,CACvB,eAAgB,CARhB,WAAY,CAGZ,uBAMF,CAEA,gBACE,kBAAmB,CACnB,oBACF,CAEA,iBACE,kBACF,CAcA,+BACE,QAAO,CACP,gBACF,CAEA,gCAME,kBAAmB,CAFnB,mBAAsB,CAHtB,eAAiB,CAEjB,iBAAkB,CAMlB,UAAW,CACX,cAAe,CALf,YAAa,CAGb,cAAe,CADf,sBAAuB,CALvB,iBAAkB,CAUlB,iBAAkB,CADlB,uBAEF,CAEA,uCACE,wBACF,CAEA,sCACE,qBACF,CAGA,0CACE,WAAY,CACZ,iBACF,CAEA,8CACE,cAAe,CACf,UACF,CAEA,yCAIE,+BAAyC,CAHzC,UAAY,CACZ,gCAAkC,CAClC,YAEF,CAEA,0CAEE,oBAAmC,CADnC,wBAEF,CAEA,kCAWE,kBAAmB,CAJnB,oBAAmC,CAEnC,iBAAkB,CADlB,UAAY,CAQZ,WAAY,CANZ,YAAa,CAGb,aAAc,CAPd,WAAY,CAMZ,sBAAuB,CAVvB,SAAU,CAYV,SAAU,CAbV,iBAAkB,CAElB,OAAQ,CACR,0BAA2B,CAW3B,2BAA6B,CAV7B,UAYF,CAEA,yCACE,eACF,CAEA,kEACE,SACF,CAEA,sCAKE,UAAW,CADX,cAAe,CAHf,iBAAkB,CAElB,SAAU,CADV,OAIF,CAEA,4BACE,iBAAkB,CAClB,SACF,CAEA,oDAOE,qFAMC,CAPD,QAAS,CALT,UAAW,CAGX,MAAO,CAUP,mBAAoB,CAZpB,iBAAkB,CAGlB,OAAQ,CAFR,KAYF,CAEA,oCAUE,4CAA6C,CAT7C,kBAAmB,CACnB,UAAY,CAGZ,cAAe,CACf,eAAgB,CAEhB,mBAAqB,CACrB,eAAgB,CALhB,gBAAiB,CADjB,iBAAkB,CAIlB,wBAIF,CAEA,sBACE,MAAW,SAAY,CACvB,IAAM,UAAc,CACtB,CAEA,kCACE,gBAAiB,CACjB,iBACF,CAEA,8BAEE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAMZ,cAAe,CAFf,cAAe,CACf,eAAgB,CAHhB,WAAY,CAKZ,wBAAyB,CATzB,UAUF,CAEA,+BAEE,+BAAgC,CADhC,gBAEF,CAEA,2BAGE,YAAQ,CAFR,YAAa,CAEb,OAAQ,CADR,mCAEF,CAEA,0BAQE,kBAAmB,CALnB,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAMZ,cAAe,CAHf,YAAa,CALb,WAAY,CAOZ,sBAAuB,CAEvB,8BAAgC,CAVhC,UAWF,CAMA,iEACE,kBACF,CAEA,yBACE,YACF,CAEA,YACE,+BACF,CAEA,+BAIE,UAAW,CAFX,cAAe,CACf,eAAgB,CAGhB,mBAAqB,CALrB,eAAkB,CAIlB,wBAEF,CAEA,WAGE,YAAQ,CAFR,YAAa,CAEb,OAAQ,CADR,mCAEF,CAEA,UAOE,kBAAmB,CAHnB,eAAiB,CADjB,wBAAyB,CAEzB,iBAAkB,CAIlB,cAAe,CAHf,YAAa,CAKb,cAAe,CATf,WAAY,CAMZ,sBAAuB,CAEvB,uBAAyB,CATzB,UAWF,CAEA,gBACE,kBAAmB,CACnB,oBACF,CAEA,iBACE,oBACF,CAEA,aACE,QAAO,CACP,eACF,CAEA,YACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,gBAGE,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,WAAY,CAKZ,eAAgB,CADhB,uBAAyB,CALzB,UAOF,CAEA,sBACE,oBACF,CAEA,uBAEE,kBAAmB,CADnB,oBAEF,CAEA,mBAIE,kBAAmB,CAEnB,eAAiB,CAHjB,YAAa,CADb,WAAY,CAGZ,sBAIF,CAEA,iCAJE,UAAW,CACX,cAAe,CAPf,UAoBF,CAVA,cAIE,gBAAuB,CADvB,sBAAuB,CAEvB,iBAAkB,CAElB,cAAe,CALf,WAAY,CAMZ,uBAEF,CAEA,oBACE,oBAAqB,CACrB,aACF,CAEA,eAGE,kBAAmB,CAGnB,kBAAmB,CAJnB,YAAa,CADb,QAAO,CAGP,sBAAuB,CACvB,YAEF,CAEA,kBAGE,eAAiB,CAIjB,wBAAyB,CAHzB,iBAAkB,CAClB,8BAAwC,CAJxC,eAAgB,CAKhB,eAAgB,CAJhB,UAMF,CAEA,aAEE,mBAAsB,CACtB,eAAiB,CACjB,iBAAkB,CAHlB,UAIF,CAGA,cAGE,eAAiB,CACjB,+BAAgC,CAChC,kBAAmB,CAHnB,cAAe,CADf,iBAKF,CAEA,iBAIE,UAAW,CAFX,cAAe,CACf,eAAiB,CAEjB,kBAAmB,CAJnB,eAAkB,CAKlB,wBACF,CAEA,8BAIE,aAAc,CACd,iCAAqC,CAHrC,cAAe,CACf,eAAiB,CAFjB,QAKF,CAEA,cAGE,eAAgB,CAFhB,YAGF,CAEA,iBAEE,UAAW,CACX,cAAe,CAFf,eAGF,CAEA,gBACE,eAGF,CAEA,iCAJE,UAAW,CACX,eAMF,CAEA,iBACE,iBACF,CAEA,kBAEE,eAAiB,CACjB,6BAA8B,CAC9B,YAAa,CAHb,WAIF,CAEA,qBAIE,UAAW,CAFX,cAAe,CACf,eAAgB,CAGhB,mBAAqB,CALrB,eAAkB,CAIlB,wBAEF,CAEA,gBACE,kBACF,CAEA,sBAKE,UAAW,CAJX,aAAc,CAEd,cAAe,CACf,eAAgB,CAGhB,mBAAqB,CALrB,iBAAkB,CAIlB,wBAEF,CAEA,kCAME,uBAAwB,CACxB,eAAgB,CAJhB,kBAAmB,CACnB,iBAAkB,CAFlB,UAAW,CAGX,YAAa,CAJb,UAOF,CAEA,wDACE,uBAAwB,CACxB,eAAgB,CAGhB,kBAAmB,CACnB,iBAAkB,CAClB,cAAe,CAHf,WAAY,CADZ,UAKF,CAEA,kCAGE,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,WAAY,CADZ,UAKF,CAEA,oBACE,YAAa,CACb,OACF,CAEA,yBACE,QAAO,CAEP,cAAe,CADf,WAEF,CAGA,KAGE,eAAiB,CADjB,wBAAyB,CAGzB,iBAAkB,CADlB,UAAW,CAHX,gBAAiB,CAOjB,uBAKF,CAEA,WACE,kBAAmB,CACnB,iBACF,CAEA,cAEE,kBAAmB,CADnB,UAEF,CAEA,iBAGE,oBACF,CAEA,uBAEE,oBACF,CAEA,mBAGE,oBACF,CAEA,yBAEE,oBACF,CAEA,YAEE,cAAe,CADf,eAEF,CAGA,WAGE,kDAKF,CAEA,8BALE,kBAAmB,CADnB,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CACvB,iBAAkB,CANlB,UAkBF,CATA,mBAKE,qBAAsB,CAFtB,YAMF,CAEA,sBACE,eAAiB,CAIjB,wBAAyB,CAFzB,kBAAmB,CACnB,+BAAyC,CAEzC,eAAgB,CAJhB,YAAa,CAKb,UACF,CAEA,yBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,eAAkB,CAIlB,iBACF,CAEA,cAME,kBAAmB,CAFnB,wBAAyB,CACzB,iBAAkB,CAJlB,cAAe,CAEf,YAAa,CADb,uBAKF,CAEA,oBACE,oBAAqB,CAErB,+BAA8C,CAD9C,0BAEF,CAEA,cAGE,kBAAmB,CAFnB,YAAa,CAIb,cAAe,CAHf,6BAA8B,CAE9B,kBAEF,CAEA,yBACE,eACF,CAEA,qBAEE,aAAc,CADd,eAAgB,CAEhB,cACF,CAEA,qBACE,aAAc,CAEd,QAAO,CACP,gBAAiB,CAFjB,gBAGF,CAEA,WAME,4BAA6B,CAH7B,UAAW,CADX,cAAe,CAKf,iBAAkB,CAHlB,eAAgB,CAChB,gBAAiB,CAJjB,iBAOF,CAEA,mBAEE,WAAY,CAGZ,UAAW,CAJX,iBAAkB,CAElB,UAAW,CACX,iBAEF,CAEA,YAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,iBACF,CAEA,qBAEE,cAAe,CADf,QAEF,CAEA,qBAEE,SAGF,CAEA,SAWE,+BAAyC,CALzC,cAAe,CAIf,uBAEF,CAEA,UACE,kBAAmB,CACnB,UACF,CAEA,UACE,kBAAmB,CACnB,UACF,CAEA,eAEE,+BAA0C,CAD1C,oBAEF,CAEA,gBACE,kBACF,CAEA,gBACE,kBACF,CAEA,YACE,6BACF,CAEA,4BACE,0BACF,CAEA,4BACE,yBACF,CAGA,kBAKE,kBAAmB,CAHnB,mBAAsB,CACtB,eAAiB,CAIjB,sBAAuB,CACvB,iBAAkB,CAJlB,YAAa,CAEb,sBAAuB,CALvB,UAQF,CAEA,qBAEE,UAAW,CADX,iBAEF,CAEA,wBAIE,UAAW,CAFX,cAAe,CACf,eAAgB,CAFhB,eAIF,CAEA,uBAGE,UAAW,CADX,cAAe,CADf,QAGF,CC51BA,0BAME,0BAAoC,CADpC,QAAS,CAGT,YAAa,CACb,qBAAsB,CANtB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAKN,YAGF,CAEA,kBAKE,wBAAyB,CAFzB,YAAa,CACb,qBAAsB,CAFtB,WAAY,CADZ,UAKF,CAGA,gBAIE,kBAAmB,CAHnB,kDAA6D,CAK7D,8BAAwC,CAHxC,YAAa,CADb,WAAY,CAGZ,cAEF,CAEA,gBAIE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAF9B,UAIF,CAEA,UACE,eAAgB,CAChB,WAAY,CAGZ,iBAAkB,CAFlB,cAAe,CACf,gBAAiB,CAEjB,oCACF,CAEA,gBACE,0BACF,CAEA,cACE,WAAY,CACZ,UACF,CAGA,qCAEE,qBAAuB,CACvB,+BAAgC,CAChC,cACF,CAEA,oBAKE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAF9B,aAAc,CADd,gBAAiB,CAKjB,cAAe,CACf,iBACF,CAEA,UACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAJlB,aAAc,CAEd,cAAe,CADf,eAAgB,CAEhB,gBAAiB,CAGjB,yBAA0B,CAD1B,uBAEF,CAEA,gBACE,wBAAyB,CACzB,UAAY,CACZ,oBACF,CAEA,YACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAJlB,aAAc,CAEd,cAAe,CADf,eAAgB,CAEhB,gBAAiB,CAGjB,yBAA0B,CAD1B,uBAEF,CAEA,kBACE,wBAAyB,CACzB,UAAY,CACZ,oBACF,CAEA,mBACE,YAAa,CACb,QACF,CAEA,qBAUE,kBAAmB,CARnB,wBAAyB,CAEzB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CAEf,YAAa,CADb,eAAgB,CAHhB,iBAAkB,CAMlB,oCACF,CAEA,iCAEE,wBACF,CAEA,iBAIE,YAAa,CACb,QAAS,CAHT,QAAS,CADT,iBAAkB,CAElB,0BAGF,CAEA,gBACE,wBAAyB,CAGzB,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CACf,eAAgB,CAHhB,gBAIF,CAEA,YACE,wBACF,CAEA,WACE,wBACF,CAGA,iBAIE,kDAA6D,CAH7D,QAAO,CACP,eAAgB,CAChB,iBAEF,CACA,iBASE,qBAAsB,CAFtB,aAAc,CACd,YAEF,CAEA,iCARE,kBAAmB,CADnB,YAAa,CADb,WAAY,CAGZ,sBAAuB,CACvB,iBAAkB,CALlB,UAoBF,CATA,gBAIE,2BAA6B,CAF7B,0BAOF,CAEA,kBAME,4BAA8B,CAE9B,yBAA4B,CAC5B,2BAA6B,CAC7B,yCAAoD,CALpD,sBAAwB,CAHxB,qBAAuB,CAKvB,gCAAkC,CAHlC,oCAAuC,CADvC,oCAAuC,CAQvC,uBAAyB,CAVzB,oBAWF,CAQA,+BAJE,kBAAmB,CADnB,YAAa,CAEb,sBAUF,CAPA,cAME,eAAiB,CAJjB,WAAY,CADZ,UAMF,CAEA,kBAEE,aAAc,CADd,iBAEF,CAEA,qBAGE,aAAc,CAFd,cAAe,CACf,kBAEF,CAEA,oBACE,cAAe,CACf,aACF,CAGA,qBAKE,YAAa,CACb,6BAA8B,CAC9B,cAAe,CACf,mBAAoB,CAPpB,iBAAkB,CAClB,OAAQ,CACR,0BAA2B,CAC3B,UAKF,CAEA,SAQE,kBAAmB,CAPnB,oBAAoC,CACpC,WAAY,CACZ,iBAAkB,CAOlB,2BAAyC,CAJzC,cAAe,CACf,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CAGvB,kBAAmB,CADnB,uBAAyB,CAPzB,UASF,CAEA,8BACE,eAAiB,CACjB,oBACF,CAEA,kBAEE,kBAAmB,CADnB,UAEF,CAEA,aAEE,WAAY,CADZ,UAEF,CAGA,aAME,kBAAmB,CAEnB,oBAAoC,CAEpC,kBAAmB,CARnB,WAAY,CASZ,+BAAyC,CANzC,YAAa,CAEb,QAAS,CAJT,QAAS,CAMT,iBAAkB,CARlB,iBAAkB,CAGlB,0BAQF,CAEA,QAME,uBAAwB,CACxB,eAAgB,CAHhB,kBAAmB,CADnB,iBAAkB,CADlB,UAAW,CAGX,YAAa,CAJb,WAOF,CAEA,8BACE,uBAAwB,CACxB,eAAgB,CAIhB,kBAAmB,CAEnB,qBAAuB,CAHvB,iBAAkB,CAIlB,0BAAwC,CAFxC,cAAe,CAHf,WAAY,CADZ,UAOF,CAEA,0BAIE,kBAAmB,CAEnB,qBAAuB,CAHvB,iBAAkB,CAIlB,0BAAwC,CAFxC,cAAe,CAHf,WAAY,CADZ,UAOF,CAEA,cAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,cAAe,CACf,iBACF,CAGA,eAQE,kBAAmB,CAFnB,oBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YACF,CAEA,YACE,eAAiB,CACjB,kBAAmB,CACnB,gCAA0C,CAC1C,eAAgB,CAChB,SACF,CAEA,eACE,YAAa,CAEb,iBAAkB,CADlB,iBAEF,CAEA,WAYE,kBAAmB,CARnB,eAAgB,CAChB,WAAY,CASZ,iBAAkB,CANlB,aAAc,CADd,cAAe,CAIf,YAAa,CALb,cAAe,CAIf,WAAY,CAGZ,sBAAuB,CAZvB,iBAAkB,CAElB,UAAW,CADX,QAAS,CAaT,oCAAsC,CANtC,UAOF,CAEA,iBACE,wBACF,CAEA,YACE,kBACF,CAEA,gBAEE,WAAY,CADZ,UAEF,CAEA,eAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAGhB,kBACF,CAEA,4BAJE,kBAMF,CAEA,aAGE,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,iBAAkB,CAIlB,gCAAkC,CALlC,UAMF,CAEA,mBAEE,oBAAqB,CADrB,YAEF,CAEA,YACE,kDAA6D,CAE7D,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAMZ,cAAe,CAFf,cAAe,CACf,eAAgB,CAGhB,mBAAqB,CANrB,iBAAkB,CAKlB,uBAEF,CAEA,iCAEE,+BAA8C,CAD9C,0BAEF,CAEA,qBAIE,eAAgB,CAFhB,kBAAmB,CADnB,UAAY,CAEZ,cAEF,CAGA,yBACE,oBAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,iBAGE,sBAAuB,CAFvB,eAAgB,CAChB,cAEF,CAEA,qBACE,WACF,CAEA,eACE,WAAY,CACZ,YACF,CAEA,gBAEE,eAAgB,CADhB,cAEF,CAEA,iBACE,WACF,CAGA,+CACE,wCACF,CAEA,+CACE,0CACF,CACF,CAGA,mBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAKtB,4BAAgC,CAFhC,WAAY,CADZ,sBAAuB,CAEvB,iBAEF,CAEA,iBAGE,UAAW,CAFX,cAAe,CACf,eAAgB,CAGhB,kBAAmB,CADnB,iBAEF,CAEA,iBAGE,UAAW,CAFX,cAAe,CACf,eAAiB,CAEjB,kBACF,CAEA,yBAEE,WAAY,CAIZ,UAAW,CADX,cAAe,CAFf,QAAS,CAIT,QAAS,CANT,iBAAkB,CAGlB,0BAIF,CAGA,oBAOE,kBAAmB,CAFnB,eAAiB,CACjB,YAAa,CAJb,WAAY,CAMZ,sBAAuB,CALvB,aAAc,CACd,iBAAkB,CAHlB,UAQF,CAEA,kCAOE,yBAA2B,CAD3B,yBAEF,CAEA,mHANE,4BAA8B,CAD9B,sBAAwB,CADxB,qBAAuB,CAGvB,gCAAkC,CAJlC,oBAiBF,CARA,iFAOE,2BACF,CAGA,yCAUE,qBAAuB,CAHvB,mBAAqB,CAHrB,kBAA2B,CAI3B,oBAAsB,CACtB,kBAAoB,CAJpB,iCAAmC,CACnC,iCAKF,CAEA,kCAKE,+BAAiC,CADjC,gCAAmC,CAEnC,uBAAyB,CAJzB,yBAA2B,CAD3B,wBAA0B,CAE1B,gCAIF,CAGA,kFAKE,kCAA+C,CAD/C,kDAAiE,CAIjE,+BAAiC,CADjC,sBAAwB,CAJxB,gCAAmC,CAGnC,uBAAyB,CAGzB,0BAA4B,CAP5B,gCAQF,CAGA,8HAOE,yBAA4B,CAD5B,sBAAwB,CAFxB,gCAAmC,CACnC,uBAAyB,CAFzB,gCAKF,CAGA,+KAYE,4BAA8B,CAG9B,+BAAiC,CAJjC,sBAAwB,CALxB,qBAAuB,CAOvB,gCAAkC,CAJlC,kBAAoB,CADpB,oCAAuC,CADvC,oCAAuC,CAOvC,0BAA4B,CAJ5B,mBAAqB,CALrB,oBAWF,CAGA,yCAKE,+BAAiC,CADjC,kCAAsC,CAEtC,0BAA4B,CAH5B,oBAIF,CAGA,wBAEE,WAAY,CADZ,cAEF,CAGA,yPAOE,sBACF,CAUA,+KAKE,qBAAuB,CACvB,kBAAoB,CACpB,mBAAqB,CAHrB,oBAIF,CAGA,yCACE,sBAAwB,CACxB,qBACF,CAEA,oCAEE,yBAA4B,CAK5B,+BAAiC,CANjC,kBAAkB,CAElB,oCAAwC,CAExC,qBAAuB,CACvB,0BAA4B,CAF5B,sCAIF,CAGA,8CACE,4DACF,CAEA,6CACE,+DAAwE,CACxE,yBACF,CAEA,8CACE,iDACF,CAEA,2CACE,oDACF,CAEA,gDACE,4DACF,CAGA,uEAGE,yBAA2B,CAE3B,kCACF,CAGA,0NAME,gCAAmC,CAEnC,yBAA2B,CAD3B,wBAA0B,CAF1B,gCAIF,CAGA,4BACE,wBACF,CAGA,mHAME,8BAAgC,CAChC,+BAAiC,CAJjC,sBAAwB,CACxB,qBAAuB,CACvB,0BAGF,CAGA,qGAME,+BAAiC,CAFjC,wBAA0B,CAC1B,0BAA4B,CAF5B,oBAIF,CAGA,+CACE,wCACF,CAEA,+CACE,0CACF,CAEA,+CACE,0CACF,CAEA,+CACE,0CACF,CAEA,8CACE,0CACF,CAEA,iDACE,0CACF,CAEA,+CACE,0CACF,CAGA,sBACE,+BACF,CAEA,uEAME,8BAAgC,CAHhC,0BAA4B,CAC5B,+BAAiC,CACjC,4BAEF,CAEA,iCAME,gBAAoC,CAEpC,iBAAkB,CANlB,WAAY,CAGZ,UAAW,CADX,cAAe,CAGf,eAAgB,CANhB,iBAAkB,CAElB,UAAW,CAMX,WACF,CAGA,cAGE,kBAAmB,CAGnB,UAAW,CALX,YAAa,CACb,qBAAsB,CAGtB,YAAa,CADb,sBAGF,CAEA,+BAME,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAEA,gBACE,cAAe,CACf,QACF,CCtzBA,iBAGE,oBAA8B,CAG9B,MAAO,CAFP,cAAe,CACf,KAAM,CAJN,WAAY,CAMZ,YAIF,CAEA,0CAJE,kBAAmB,CADnB,YAAa,CANb,YAAa,CAQb,sBAWF,CARA,yBAME,kBAAmB,CACnB,UAAW,CALX,qBAMF,CAEA,0CAME,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAEA,uBAGE,kBAAmB,CAGnB,kBAAmB,CACnB,UAAW,CANX,YAAa,CACb,qBAAsB,CAGtB,YAAa,CADb,sBAAuB,CAKvB,YAAa,CADb,iBAEF,CAEA,0BACE,UAAW,CAEX,cAAe,CADf,kBAEF,CAEA,yBAEE,cAAe,CADf,kBAAmB,CAEnB,eACF,CAEA,4BAEE,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CACf,cAAe,CANf,iBAAkB,CAOlB,8BACF,CAEA,kCACE,kBACF,CCtEA,WAGE,kBAAmB,CAEnB,kDAA6D,CAH7D,YAAa,CAEb,sBAAuB,CAHvB,gBAAiB,CAKjB,iBACF,CAEA,mBAIE,eAAiB,CAEjB,kBAAmB,CACnB,gCAA0C,CAL1C,eAAgB,CAGhB,iBAAkB,CAJlB,iBAAkB,CAElB,UAKF,CAEA,YAGE,aAAc,CAFd,eAAgB,CAChB,eAAgB,CAEhB,aAAc,CACd,kBAAmB,CACnB,iCACF,CAEA,sBAEE,UAAW,CADX,cAAe,CAGf,eAAgB,CADhB,kBAEF,CAEA,qBAEE,UAAW,CADX,cAAe,CAEf,eAAgB,CAChB,kBAAmB,CAEnB,gBAAiB,CACjB,iBAAkB,CAFlB,eAGF,CAEA,mBACE,YAAa,CAIb,cAAe,CAHf,QAAS,CACT,sBAAuB,CACvB,kBAEF,CAEA,eACE,4BAA6B,CAC7B,gBACF,CAEA,kBAEE,UAAW,CADX,cAAe,CAGf,eAAgB,CADhB,kBAEF,CAEA,kBAIE,YAAa,CAGb,cAAe,CADf,QAAS,CADT,sBAAuB,CAJvB,eAAgB,CAEhB,QAAS,CADT,SAMF,CAEA,kBACE,QACF,CAEA,iBAME,iBAAkB,CALlB,aAAc,CAMd,oBAAqB,CAJrB,eAAgB,CAEhB,gBAAiB,CAHjB,oBAAqB,CAErB,yBAIF,CAEA,uBAEE,oBAAmC,CADnC,aAEF,CAGA,KAUE,kBAAmB,CARnB,WAAY,CACZ,iBAAkB,CAClB,cAAe,CAKf,mBAAoB,CAJpB,cAAe,CACf,eAAgB,CAKhB,sBAAuB,CACvB,eAAgB,CAXhB,iBAAkB,CAOlB,oBAAqB,CADrB,uBAMF,CAEA,iBACE,kBAAmB,CACnB,UACF,CAEA,uBACE,kBAAmB,CAEnB,+BAA8C,CAD9C,0BAEF,CAEA,mBACE,kBAAmB,CACnB,UACF,CAEA,yBACE,kBAAmB,CAEnB,+BAA+C,CAD/C,0BAEF,CAGA,yBACE,mBACE,iBACF,CAEA,YACE,cACF,CAEA,sBACE,cACF,CAEA,mBAEE,kBAAmB,CADnB,qBAEF,CAEA,kBACE,qBAAsB,CACtB,QACF,CACF,CC3JA,MAEE,wBAAyB,CACzB,8BAA+B,CAC/B,8BAA+B,CAC/B,6BAA8B,CAG9B,wBAAyB,CACzB,6BAA8B,CAC9B,8BAA+B,CAG/B,sBAAuB,CACvB,wBAAyB,CACzB,oBAAqB,CACrB,iBAAqB,CAGrB,iBAAqB,CACrB,sBAAuB,CACvB,qBAAsB,CACtB,qDAAgE,CAChE,4DAAuE,CAGvE,sBAAuB,CACvB,uBAAwB,CACxB,qBAAsB,CAGtB,+BAA0C,CAC1C,+BAA0C,CAC1C,iCAA2C,CAC3C,iCAA2C,CAG3C,oFAA0F,CAC1F,8DAAkE,CAClE,0CAA6C,CAG7C,cAAe,CACf,cAAe,CACf,gBAAiB,CACjB,cAAe,CACf,cAAe,CACf,eAAgB,CAChB,eAAgB,CAChB,eAAgB,CAGhB,gBAAiB,CACjB,gBAAiB,CACjB,iBAAkB,CAClB,iBAAkB,CAClB,iBAAkB,CAClB,kBAAmB,CAGnB,eAAgB,CAChB,eAAgB,CAChB,eAAgB,CAChB,gBAAiB,CAGjB,4BAA6B,CAC7B,6BAA8B,CAC9B,2BAA4B,CAG5B,iBAAkB,CAClB,eAAgB,CAChB,cAAe,CACf,uBAAwB,CACxB,cAAe,CACf,gBAAiB,CACjB,gBAAiB,CACjB,cAAe,CAKf,qBAAiC,CACjC,0BAA8B,CAC9B,sBAA0B,CAC1B,2CAAqD,CAGrD,sBAAuB,CACvB,uBAAwB,CACxB,4BAZF,CAgBA,cAAgB,aAA0B,CAA1B,yBAA4B,CAC5C,gBAAkB,aAA4B,CAA5B,2BAA8B,CAChD,YAAc,aAAwB,CAAxB,uBAA0B,CACxC,YAAc,UAAwB,CAAxB,uBAA0B,CAExC,YAAc,qBAAmC,CAAnC,kCAAqC,CACnD,cAAgB,wBAAqC,CAArC,oCAAuC,CACvD,aAAe,wBAAoC,CAApC,mCAAsC,CAErD,WAAa,8BAA4B,CAA5B,2BAA8B,CAC3C,WAAa,8BAA4B,CAA5B,2BAA8B,CAC3C,WAAa,gCAA4B,CAA5B,2BAA8B,CAC3C,WAAa,gCAA4B,CAA5B,2BAA8B,CAE3C,YAAc,iBAA+B,CAA/B,8BAAiC,CAC/C,YAAc,iBAA+B,CAA/B,8BAAiC,CAC/C,YAAc,iBAA+B,CAA/B,8BAAiC,CAC/C,YAAc,kBAA+B,CAA/B,8BAAiC,CAE/C,iBAAmB,wBAAsC,CAAtC,qCAAwC,CAC3D,mBAAqB,uBAAwC,CAAxC,uCAA0C,CAC/D,iBAAmB,uBAAsC,CAAtC,qCAAwC,CClH3D,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAGA,mHAOE,aAAc,CACd,eACF,CAEA,KAIE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CACzB,aAAc,CANd,mIAEY,CAKZ,eACF,CAEA,KACE,YAAa,CACb,qBAAsB,CACtB,gBACF,CAEA,UACE,YAAa,CACb,QAAO,CACP,iBACF,CAEA,UACE,aAAc,CACd,mBACF,CAEA,8CACE,UACE,2CACF,CACF,CAEA,YAKE,kBAAmB,CAJnB,wBAAyB,CAOzB,UAAY,CALZ,YAAa,CACb,qBAAsB,CAGtB,4BAA6B,CAD7B,sBAAuB,CAJvB,gBAOF,CAEA,UACE,aACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAGA,aAGE,kBAAmB,CAGnB,kDAA6D,CAC7D,UAAY,CANZ,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,gBAAiB,CAGjB,iBACF,CAEA,iBAME,iCAAkC,CAFlC,0BAA2B,CAC3B,iBAAkB,CADlB,qBAA2B,CAF3B,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,eACE,cAAe,CACf,eAAgB,CAEhB,mBAAqB,CADrB,QAEF,CAGA,WAGE,kBAAmB,CAGnB,kDAA6D,CAC7D,UAAY,CANZ,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,gBAAiB,CAIjB,YAAa,CADb,iBAEF,CAEA,cACE,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CADnB,kBAEF,CAEA,aACE,cAAe,CAGf,eAAgB,CAFhB,kBAAmB,CACnB,eAEF,CAEA,kBACE,gBAAoC,CAEpC,qBAAuB,CAEvB,iBAAkB,CAHlB,UAAY,CAMZ,cAAe,CAFf,cAAe,CACf,eAAgB,CAGhB,mBAAqB,CANrB,iBAAkB,CAKlB,uBAEF,CAEA,wBACE,eAAiB,CAGjB,gCAA0C,CAF1C,aAAc,CACd,0BAEF,CAGA,aAKE,YAAa,CACb,qBAAsB,CACtB,QAAS,CACT,eAAgB,CAPhB,cAAe,CAEf,UAAW,CADX,QAAS,CAET,YAKF,CAEA,YAME,mCAAqC,CAJrC,iBAAkB,CAGlB,+BAA0C,CAF1C,cAAe,CACf,eAAgB,CAHhB,iBAAkB,CAMlB,iBACF,CAEA,iBACE,kDAA6D,CAC7D,UACF,CAEA,yBACE,kDAA6D,CAC7D,UACF,CAEA,oBACE,kDAA6D,CAC7D,aACF,CAEA,kBACE,kDAA6D,CAC7D,UACF,CAEA,wBACE,GAEE,SAAU,CADV,0BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAGA,yBACE,aAGE,SAAU,CACV,cAAe,CAFf,UAAW,CADX,QAIF,CAEA,YAEE,cAAe,CADf,iBAEF,CACF", "sources": ["index.css", "components/TextEditor/TextEditor.css", "components/ImageLibrary/ImageLibrary.css", "components/FrontPage/FrontPage.css", "components/ResumePage/ResumePage.css", "components/ContactDetails/ContactDetails.css", "components/NameFlipbook/NameFlipbook.css", "components/AddPageModal/AddPageModal.css", "components/TextToolbar/TextToolbar.css", "components/FlipbookEditor/FlipbookEditor.css", "components/FlipbookPreview/FlipbookPreview.css", "components/FlipbookViewer/FlipbookViewer.css", "components/NotFound/NotFound.css", "styles/variables.css", "App.css"], "sourcesContent": ["* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  padding: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f5f5;\n}\n\n#root {\n  height: 100vh;\n}\n\n.App {\n  height: 100vh;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n/* Custom scrollbar */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n", "/* Text Editor Styles */\n\n.text-editor-container {\n  position: relative;\n  width: 100%;\n}\n\n.text-editor-toolbar {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  padding: 8px 12px;\n  background: white;\n  border: 1px solid #e0e0e0;\n  border-bottom: none;\n  border-radius: 6px 6px 0 0;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  z-index: 100;\n  position: absolute;\n  top: -50px;\n  left: 0;\n  right: 0;\n  min-width: 400px;\n  overflow-x: auto;\n  white-space: nowrap;\n}\n\n.toolbar-group {\n  display: flex;\n  align-items: center;\n  gap: 2px;\n}\n\n.toolbar-btn {\n  width: 32px;\n  height: 32px;\n  border: 1px solid transparent;\n  background: transparent;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.2s ease;\n  color: #666;\n}\n\n.toolbar-btn:hover {\n  background: #f0f0f0;\n  border-color: #ccc;\n}\n\n.toolbar-btn.active {\n  background: #8B5DBA;\n  color: white;\n  border-color: #8B5DBA;\n}\n\n.toolbar-select {\n  height: 32px;\n  padding: 4px 8px;\n  border: 1px solid #e0e0e0;\n  border-radius: 4px;\n  background: white;\n  font-size: 12px;\n  cursor: pointer;\n  min-width: 80px;\n}\n\n.toolbar-select:focus {\n  outline: none;\n  border-color: #8B5DBA;\n}\n\n.toolbar-color {\n  width: 32px;\n  height: 32px;\n  border: 1px solid #e0e0e0;\n  border-radius: 4px;\n  cursor: pointer;\n  background: none;\n  padding: 0;\n}\n\n.toolbar-color::-webkit-color-swatch-wrapper {\n  padding: 0;\n}\n\n.toolbar-color::-webkit-color-swatch {\n  border: none;\n  border-radius: 3px;\n}\n\n.toolbar-separator {\n  width: 1px;\n  height: 24px;\n  background: #e0e0e0;\n  margin: 0 4px;\n}\n\n.text-editor {\n  min-height: 40px;\n  padding: 12px;\n  border: 2px solid #e0e0e0;\n  border-radius: 6px;\n  background: white;\n  font-size: 14px;\n  line-height: 1.4;\n  color: #333;\n  transition: all 0.2s ease;\n  outline: none;\n  word-wrap: break-word;\n  overflow-wrap: break-word;\n  direction: ltr;\n  text-align: left;\n}\n\n.text-editor:focus,\n.text-editor.active {\n  border-color: #8B5DBA;\n  box-shadow: 0 0 0 3px rgba(139, 93, 186, 0.1);\n}\n\n.text-editor:empty:before {\n  content: attr(data-placeholder);\n  color: #999;\n  pointer-events: none;\n}\n\n.text-editor p {\n  margin: 0 0 8px 0;\n}\n\n.text-editor p:last-child {\n  margin-bottom: 0;\n}\n\n.text-editor ul,\n.text-editor ol {\n  margin: 0 0 8px 20px;\n  padding: 0;\n}\n\n.text-editor li {\n  margin-bottom: 4px;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .text-editor-toolbar {\n    min-width: 300px;\n    padding: 6px 8px;\n  }\n  \n  .toolbar-btn {\n    width: 28px;\n    height: 28px;\n    font-size: 12px;\n  }\n  \n  .toolbar-select {\n    height: 28px;\n    min-width: 60px;\n    font-size: 11px;\n  }\n  \n  .toolbar-color {\n    width: 28px;\n    height: 28px;\n  }\n}\n", "/* Image Library Styles */\n\n.image-library-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.image-library-modal {\n  width: 90%;\n  max-width: 800px;\n  height: 80%;\n  max-height: 600px;\n  background: white;\n  border-radius: 12px;\n  display: flex;\n  flex-direction: column;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n}\n\n.image-library-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20px;\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.image-library-header h2 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.close-btn {\n  width: 32px;\n  height: 32px;\n  border: none;\n  background: #f8f9fa;\n  border-radius: 50%;\n  cursor: pointer;\n  font-size: 18px;\n  color: #666;\n  transition: all 0.2s ease;\n}\n\n.close-btn:hover {\n  background: #e9ecef;\n  color: #333;\n}\n\n.image-library-tabs {\n  display: flex;\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.tab-btn {\n  flex: 1;\n  padding: 12px 20px;\n  border: none;\n  background: transparent;\n  color: #666;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border-bottom: 2px solid transparent;\n}\n\n.tab-btn:hover {\n  color: #8B5DBA;\n  background: #f8f4ff;\n}\n\n.tab-btn.active {\n  color: #8B5DBA;\n  border-bottom-color: #8B5DBA;\n  background: #f8f4ff;\n}\n\n.image-library-content {\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n}\n\n.upload-section {\n  margin-bottom: 30px;\n}\n\n.upload-dropzone {\n  border: 2px dashed #ccc;\n  border-radius: 8px;\n  padding: 40px 20px;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  background: #fafafa;\n}\n\n.upload-dropzone:hover {\n  border-color: #8B5DBA;\n  background: #f8f4ff;\n}\n\n.upload-dropzone.dragging {\n  border-color: #8B5DBA;\n  background: #f0f9ff;\n  transform: scale(1.02);\n}\n\n.upload-icon {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.upload-dropzone p {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  color: #333;\n}\n\n.upload-dropzone small {\n  color: #666;\n  font-size: 14px;\n}\n\n.images-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\n  gap: 16px;\n}\n\n.image-item {\n  border: 2px solid #e0e0e0;\n  border-radius: 8px;\n  overflow: hidden;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  background: white;\n}\n\n.image-item:hover {\n  border-color: #8B5DBA;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.image-preview {\n  aspect-ratio: 16/9;\n  overflow: hidden;\n  background: #f8f9fa;\n}\n\n.image-preview img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.image-info {\n  padding: 12px;\n}\n\n.image-name {\n  margin: 0 0 4px 0;\n  font-size: 12px;\n  font-weight: 500;\n  color: #333;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.image-type {\n  font-size: 10px;\n  color: #666;\n  text-transform: uppercase;\n  padding: 2px 6px;\n  background: #f0f0f0;\n  border-radius: 4px;\n}\n\n.empty-state {\n  grid-column: 1 / -1;\n  text-align: center;\n  padding: 40px 20px;\n  color: #666;\n}\n\n.empty-state p {\n  margin: 0;\n  font-size: 14px;\n}\n\n.image-library-footer {\n  padding: 20px;\n  border-top: 1px solid #e0e0e0;\n  text-align: right;\n}\n\n/* Button Styles */\n.btn {\n  padding: 8px 16px;\n  border: 1px solid #e0e0e0;\n  background: white;\n  color: #666;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.2s ease;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn:hover {\n  background: #f8f8f8;\n  border-color: #ccc;\n}\n\n.btn.btn-secondary {\n  background: #6c757d;\n  color: white;\n  border-color: #6c757d;\n}\n\n.btn.btn-secondary:hover {\n  background: #5a6268;\n  border-color: #5a6268;\n}\n", "/* Front Page - Exact Match to Screenshot 2 */\n\n.front-page {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  background: #ffffff;\n  position: relative;\n}\n\n/* Page Header - Top Section */\n.page-header {\n  text-align: center;\n  padding: 20px 0;\n  background: #ffffff;\n  border-bottom: 1px solid #e0e0e0;\n  z-index: 10;\n}\n\n.pages-title {\n  margin: 0 0 15px 0;\n  font-size: 14px;\n  font-weight: 400;\n  color: #999999;\n  letter-spacing: 3px;\n  text-transform: uppercase;\n}\n\n.flipbook-title {\n  margin-bottom: 0;\n}\n\n.title-editor .text-editor {\n  font-size: 24px !important;\n  font-weight: 600 !important;\n  text-align: center;\n  border: none;\n  background: transparent;\n  color: #333333;\n  min-height: auto;\n  padding: 5px 20px;\n}\n\n/* Main Content Area - Fills Rest of Page */\n.main-content {\n  flex: 1;\n  position: relative;\n  cursor: pointer;\n  overflow: hidden;\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n\n/* Decorative Border Pattern - Matching Screenshot */\n.decorative-border-pattern {\n  position: absolute;\n  top: 20px;\n  left: 20px;\n  right: 20px;\n  bottom: 20px;\n  background-image:\n    /* Corner decorations */\n    radial-gradient(circle at 0% 0%, rgba(139, 93, 186, 0.8) 0%, transparent 15%),\n    radial-gradient(circle at 100% 0%, rgba(139, 93, 186, 0.8) 0%, transparent 15%),\n    radial-gradient(circle at 0% 100%, rgba(139, 93, 186, 0.8) 0%, transparent 15%),\n    radial-gradient(circle at 100% 100%, rgba(139, 93, 186, 0.8) 0%, transparent 15%),\n    /* Border pattern */\n    linear-gradient(0deg, transparent 48%, rgba(139, 93, 186, 0.3) 49%, rgba(139, 93, 186, 0.3) 51%, transparent 52%),\n    linear-gradient(90deg, transparent 48%, rgba(139, 93, 186, 0.3) 49%, rgba(139, 93, 186, 0.3) 51%, transparent 52%);\n  background-size:\n    50px 50px, 50px 50px, 50px 50px, 50px 50px,\n    100% 100%, 100% 100%;\n  background-position:\n    top left, top right, bottom left, bottom right,\n    center, center;\n  border: 2px solid rgba(139, 93, 186, 0.6);\n  border-radius: 8px;\n  pointer-events: none;\n  z-index: 1;\n}\n\n/* Center Content - Name and Title */\n.center-content {\n  text-align: center;\n  z-index: 5;\n  position: relative;\n}\n\n.name-section {\n  margin-bottom: 20px;\n}\n\n.main-name-editor .text-editor {\n  font-size: 48px !important;\n  font-weight: bold !important;\n  text-align: center;\n  color: #2c3e50;\n  border: none;\n  background: transparent;\n  min-height: auto;\n  padding: 10px 20px;\n  text-transform: uppercase;\n  letter-spacing: 4px;\n}\n\n.profession-title {\n  font-size: 18px;\n  font-weight: 400;\n  color: #2c3e50;\n  letter-spacing: 6px;\n  text-transform: uppercase;\n  margin-top: 20px;\n}\n\n.upload-icon {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.upload-placeholder p {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.upload-placeholder small {\n  font-size: 14px;\n  color: #888;\n}\n\n/* Upload Hint */\n.upload-hint {\n  position: absolute;\n  bottom: 30px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: rgba(255, 255, 255, 0.9);\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-size: 12px;\n  color: #666;\n  border: 1px solid #ddd;\n  z-index: 6;\n}\n\n.main-content:hover .upload-hint {\n  background: rgba(248, 244, 255, 0.95);\n  border-color: #8B5DBA;\n  color: #8B5DBA;\n}\n\n/* Navigation Controls */\n.navigation-controls {\n  position: absolute;\n  right: 40px;\n  top: 50%;\n  transform: translateY(-50%);\n  z-index: 10;\n}\n\n.next-page-btn {\n  width: 60px;\n  height: 60px;\n  background: linear-gradient(135deg, #8B5DBA 0%, #7A4BA8 100%);\n  color: white;\n  border: none;\n  border-radius: 50%;\n  font-size: 24px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 16px rgba(139, 93, 186, 0.4);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.next-page-btn:hover {\n  transform: scale(1.1);\n  box-shadow: 0 6px 20px rgba(139, 93, 186, 0.6);\n}\n\n/* Background Image Styling */\n.main-content[style*=\"background-image\"] .decorative-border-pattern {\n  background-color: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(2px);\n}\n\n.main-content[style*=\"background-image\"] .center-content {\n  background: rgba(255, 255, 255, 0.95);\n  padding: 40px;\n  border-radius: 12px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .main-name-editor .text-editor {\n    font-size: 32px !important;\n    letter-spacing: 2px;\n  }\n  \n  .profession-title {\n    font-size: 18px;\n    letter-spacing: 4px;\n  }\n  \n  .decorative-border-pattern {\n    top: 10px;\n    left: 10px;\n    right: 10px;\n    bottom: 10px;\n  }\n  \n  .navigation-controls {\n    right: 20px;\n  }\n  \n  .next-page-btn {\n    width: 50px;\n    height: 50px;\n    font-size: 20px;\n  }\n}\n\n.front-page-text {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  max-width: 600px;\n  margin: 0 auto;\n}\n\n.title-section,\n.subtitle-section {\n  width: 100%;\n  margin-bottom: 24px;\n}\n\n.title-editor .text-editor {\n  min-height: 60px;\n  font-size: 32px !important;\n  font-weight: bold;\n  text-align: center;\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(5px);\n  border: 2px solid rgba(139, 93, 186, 0.3);\n}\n\n.subtitle-editor .text-editor {\n  min-height: 50px;\n  font-size: 18px !important;\n  text-align: center;\n  background: rgba(255, 255, 255, 0.8);\n  backdrop-filter: blur(3px);\n  border: 2px solid rgba(139, 93, 186, 0.2);\n}\n\n.navigation-controls {\n  position: absolute;\n  right: 20px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n.next-btn {\n  width: 60px;\n  height: 60px;\n  background: #8B5DBA;\n  color: white;\n  border: none;\n  border-radius: 50%;\n  cursor: pointer;\n  font-size: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 12px rgba(139, 93, 186, 0.3);\n}\n\n.next-btn:hover {\n  background: #7A4BA8;\n  transform: translateY(-50%) scale(1.1);\n  box-shadow: 0 6px 16px rgba(139, 93, 186, 0.4);\n}\n\n.arrow-icon {\n  transition: transform 0.2s ease;\n}\n\n.next-btn:hover .arrow-icon {\n  transform: translateX(2px);\n}\n\n.business-card-area {\n  position: absolute;\n  bottom: 40px;\n  right: 40px;\n  width: 300px;\n  z-index: 2;\n}\n\n.business-card-placeholder {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e0e0e0;\n  text-align: center;\n}\n\n.gear-icons {\n  position: relative;\n  height: 40px;\n  margin-bottom: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n}\n\n.gear {\n  font-size: 20px;\n  animation: rotate 3s linear infinite;\n}\n\n.gear-2 {\n  animation-delay: -1s;\n  animation-direction: reverse;\n}\n\n.gear-3 {\n  animation-delay: -2s;\n}\n\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.business-card-placeholder h3 {\n  margin: 0 0 16px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  letter-spacing: 1px;\n}\n\n.card-fields {\n  margin-bottom: 16px;\n}\n\n.card-field {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n  font-size: 12px;\n}\n\n.card-field label {\n  font-weight: 500;\n  color: #666;\n}\n\n.card-field span {\n  color: #333;\n}\n\n.lets-go-btn {\n  background: #28a745;\n  color: white;\n  border: none;\n  padding: 8px 20px;\n  border-radius: 4px;\n  font-size: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: background 0.2s ease;\n  letter-spacing: 0.5px;\n}\n\n.lets-go-btn:hover {\n  background: #218838;\n}\n\n/* Page Header Section - Matching Screenshot 2 */\n.page-header {\n  text-align: center;\n  padding: 20px 0;\n  background: white;\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.pages-title {\n  margin: 0 0 15px 0;\n  font-size: 16px;\n  font-weight: 400;\n  color: #999;\n  letter-spacing: 3px;\n  text-transform: uppercase;\n}\n\n.flipbook-title {\n  margin-bottom: 10px;\n}\n\n.title-editor .text-editor {\n  font-size: 20px !important;\n  font-weight: 600 !important;\n  text-align: center;\n  border: none;\n  background: transparent;\n  color: #333;\n  min-height: auto;\n}\n\n/* Canvas Area - Main Content */\n.canvas-area {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40px;\n  position: relative;\n  background: #f8f9fa;\n}\n\n.flipbook-canvas {\n  width: 600px;\n  height: 400px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n  position: relative;\n  overflow: hidden;\n}\n\n.decorative-frame {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  right: 15px;\n  bottom: 15px;\n  border: 3px solid #8B5DBA;\n  border-radius: 8px;\n  pointer-events: none;\n  z-index: 2;\n}\n\n.content-section {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1;\n}\n\n.main-title-area {\n  text-align: center;\n  z-index: 3;\n  position: relative;\n}\n\n.main-name-editor .text-editor {\n  font-size: 32px !important;\n  font-weight: bold !important;\n  text-align: center;\n  color: #2c3e50;\n  border: none;\n  background: transparent;\n  min-height: auto;\n  margin-bottom: 10px;\n}\n\n.profession-line {\n  text-align: center;\n}\n\n.profession-line span {\n  font-size: 18px;\n  font-weight: 600;\n  color: #34495e;\n  letter-spacing: 2px;\n}\n\n/* Remove old business card form styles - moved to ContactDetails modal */\n\n/* Navigation Arrow */\n.navigation-arrow {\n  position: absolute;\n  right: 40px;\n  top: 50%;\n  transform: translateY(-50%);\n  z-index: 10;\n}\n\n.next-arrow-btn {\n  width: 50px;\n  height: 50px;\n  background: #8B5DBA;\n  color: white;\n  border: none;\n  border-radius: 50%;\n  font-size: 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 12px rgba(139, 93, 186, 0.3);\n}\n\n.next-arrow-btn:hover {\n  background: #7A4BA8;\n  transform: scale(1.1);\n}\n\n/* Image Upload Area - Hidden by default, shown when no background */\n.image-upload-area {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 0;\n}\n\n.upload-placeholder {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  text-align: center;\n  color: #999;\n  background: rgba(255, 255, 255, 0.9);\n  padding: 20px;\n  border-radius: 8px;\n  border: 2px dashed #ccc;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.upload-placeholder:hover {\n  border-color: #8B5DBA;\n  background: rgba(248, 244, 255, 0.95);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .canvas-area {\n    padding: 20px;\n  }\n  \n  .flipbook-canvas {\n    width: 100%;\n    max-width: 500px;\n    height: 350px;\n  }\n  \n  .main-name-editor .text-editor {\n    font-size: 24px !important;\n  }\n  \n  .navigation-arrow {\n    position: static;\n    text-align: center;\n    margin-top: 20px;\n    transform: none;\n  }\n}\n", "/* Resume Page Styles */\n\n.resume-page {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  background: white;\n  overflow: hidden;\n  padding: 20px;\n  box-sizing: border-box;\n}\n\n/* Drag and Drop Styles */\n.resume-section {\n  cursor: move;\n  transition: all 0.3s ease;\n  user-select: none;\n}\n\n.resume-section:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(139, 93, 186, 0.15);\n}\n\n.resume-section.dragging {\n  opacity: 0.5;\n  transform: rotate(5deg) scale(0.95);\n  z-index: 1000;\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\n}\n\n.left-sections.drag-over,\n.right-sections.drag-over {\n  background: rgba(139, 93, 186, 0.1);\n  border: 2px dashed #8B5DBA;\n  border-radius: 8px;\n  min-height: 200px;\n}\n\n.resume-section.drag-over-item {\n  border-top: 3px solid #8B5DBA;\n  margin-top: 8px;\n}\n\n.drag-handle {\n  position: absolute;\n  left: -15px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 20px;\n  height: 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: grab;\n  opacity: 0;\n  transition: opacity 0.2s ease;\n  background: rgba(139, 93, 186, 0.8);\n  border-radius: 4px;\n  color: white;\n  font-size: 12px;\n}\n\n.drag-handle:active {\n  cursor: grabbing;\n}\n\n.resume-section:hover .drag-handle {\n  opacity: 1;\n}\n\n.drop-zone-indicator {\n  position: absolute;\n  bottom: -30px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: #8B5DBA;\n  color: white;\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  z-index: 100;\n  animation: pulse 1.5s infinite;\n}\n\n@keyframes pulse {\n  0%, 100% { opacity: 1; transform: translateX(-50%) scale(1); }\n  50% { opacity: 0.7; transform: translateX(-50%) scale(1.05); }\n}\n\n.resume-content {\n  width: 100%;\n  height: calc(100% - 40px);\n  display: flex;\n  position: relative;\n  z-index: 2;\n  background: white;\n  border: 10px solid transparent;\n  background-image: \n    linear-gradient(white, white),\n    repeating-linear-gradient(\n      45deg,\n      #8B5DBA 0px,\n      #8B5DBA 4px,\n      #6B46C1 4px,\n      #6B46C1 8px,\n      #9333EA 8px,\n      #9333EA 12px,\n      #7C3AED 12px,\n      #7C3AED 16px\n    );\n  background-origin: padding-box, border-box;\n  background-clip: padding-box, border-box;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);\n  border-radius: 8px;\n}\n\n.left-column {\n  flex: 1;\n  padding: 30px 25px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border-right: 3px solid #8B5DBA;\n  display: flex;\n  flex-direction: column;\n}\n\n.right-column {\n  flex: 1;\n  padding: 30px 25px;\n  background: white;\n  display: flex;\n  flex-direction: column;\n}\n\n.name-section {\n  margin-bottom: 20px;\n}\n\n.name-section h1 {\n  margin: 0;\n  font-size: 36px;\n  font-weight: bold;\n  color: #2c3e50;\n  text-align: center;\n  font-family: 'Times New Roman', serif;\n  letter-spacing: 1px;\n}\n\n.image-section {\n  margin-bottom: 25px;\n  display: flex;\n  justify-content: center;\n}\n\n.image-container {\n  width: 150px;\n  height: 180px;\n  cursor: pointer;\n  position: relative;\n}\n\n.profile-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 8px;\n  overflow: hidden;\n  position: relative;\n  border: 3px solid #8B5DBA;\n  transition: all 0.3s ease;\n}\n\n.profile-image:hover {\n  transform: scale(1.02);\n  box-shadow: 0 4px 12px rgba(139, 93, 186, 0.3);\n}\n\n.profile-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.image-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(139, 93, 186, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  color: white;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.profile-image:hover .image-overlay {\n  opacity: 1;\n}\n\n.image-placeholder {\n  width: 100%;\n  height: 100%;\n  border: 2px dashed #8B5DBA;\n  border-radius: 8px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: rgba(139, 93, 186, 0.05);\n  transition: all 0.3s ease;\n  color: #8B5DBA;\n}\n\n.image-placeholder:hover {\n  background: rgba(139, 93, 186, 0.1);\n  transform: scale(1.02);\n}\n\n.placeholder-icon {\n  font-size: 32px;\n  margin-bottom: 8px;\n}\n\n.image-placeholder p {\n  margin: 0;\n  font-size: 11px;\n  font-weight: 500;\n  text-align: center;\n  line-height: 1.3;\n}\n\n.left-sections,\n.right-sections {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  overflow-y: auto;\n}\n\n.right-header {\n  margin-bottom: 20px;\n  text-align: center;\n}\n\n.right-header h2 {\n  margin: 0;\n  font-size: 24px;\n  font-weight: bold;\n  color: #2c3e50;\n  font-family: 'Times New Roman', serif;\n  letter-spacing: 0.5px;\n}\n\n.resume-section {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 4px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(139, 93, 186, 0.2);\n  transition: all 0.3s ease;\n  margin-bottom: 15px;\n}\n\n.left-column .resume-section {\n  background: white;\n}\n\n.right-column .resume-section {\n  background: #f8f9fa;\n}\n\n.resume-section:hover {\n  box-shadow: 0 4px 12px rgba(139, 93, 186, 0.1);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n  padding-bottom: 8px;\n  border-bottom: 2px solid #8B5DBA;\n  position: relative;\n}\n\n.section-header h3 {\n  margin: 0;\n  font-size: 16px;\n  color: #2c3e50;\n  font-weight: bold;\n  font-family: 'Times New Roman', serif;\n}\n\n.delete-section-btn {\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 14px;\n  color: #dc3545;\n  padding: 4px;\n  border-radius: 4px;\n  transition: all 0.2s ease;\n}\n\n.delete-section-btn:hover {\n  background: rgba(220, 53, 69, 0.1);\n  transform: scale(1.1);\n}\n\n.section-content {\n  position: relative;\n}\n\n.resume-text-editor .text-editor {\n  min-height: 80px;\n  font-size: 12px;\n  line-height: 1.5;\n  border: 1px solid #e9ecef;\n  background: white;\n}\n\n.resume-text-editor .text-editor:focus,\n.resume-text-editor .text-editor.active {\n  border-color: #8B5DBA;\n}\n\n.navigation-controls {\n  position: absolute;\n  top: 50%;\n  right: -30px;\n  transform: translateY(-50%);\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n  z-index: 10;\n}\n\n.nav-btn {\n  width: 40px;\n  height: 40px;\n  border: 2px solid #8B5DBA;\n  border-radius: 50%;\n  cursor: pointer;\n  font-size: 18px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 12px rgba(139, 93, 186, 0.3);\n  background: white;\n  color: #8B5DBA;\n}\n\n.prev-btn:hover {\n  background: #6c757d;\n  color: white;\n  border-color: #6c757d;\n}\n\n.next-btn:hover {\n  background: #8B5DBA;\n  color: white;\n  border-color: #7C3AED;\n}\n\n.nav-btn:hover {\n  transform: scale(1.1);\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);\n}\n\n.prev-btn:hover {\n  background: #5a6268;\n}\n\n.next-btn:hover {\n  background: #7A4BA8;\n}\n\n.arrow-icon {\n  transition: transform 0.2s ease;\n}\n\n.prev-btn:hover .arrow-icon {\n  transform: translateX(-2px);\n}\n\n.next-btn:hover .arrow-icon {\n  transform: translateX(2px);\n}\n\n.add-section-controls {\n  position: absolute;\n  bottom: 20px;\n  left: 50%;\n  transform: translateX(-50%);\n  display: flex;\n  gap: 10px;\n  z-index: 5;\n}\n\n.add-left-section-btn,\n.add-right-section-btn {\n  padding: 8px 12px;\n  background: #8B5DBA;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  font-size: 10px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  letter-spacing: 0.5px;\n}\n\n.add-left-section-btn:hover,\n.add-right-section-btn:hover {\n  background: #7A4BA8;\n  transform: translateY(-1px);\n}\n\n.background-pattern {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image: \n    radial-gradient(circle at 20% 20%, rgba(139, 93, 186, 0.03) 0%, transparent 50%),\n    radial-gradient(circle at 80% 80%, rgba(139, 93, 186, 0.03) 0%, transparent 50%),\n    radial-gradient(circle at 40% 60%, rgba(139, 93, 186, 0.02) 0%, transparent 50%);\n  z-index: 1;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .resume-content {\n    flex-direction: column;\n  }\n  \n  .left-column,\n  .right-column {\n    flex: none;\n    height: auto;\n  }\n  \n  .left-column {\n    border-right: none;\n    border-bottom: 3px solid #8B5DBA;\n  }\n  \n  .navigation-controls {\n    position: static;\n    transform: none;\n    justify-content: center;\n    margin: 20px 0;\n  }\n  \n  .add-section-controls {\n    position: static;\n    transform: none;\n    justify-content: center;\n    margin-top: 20px;\n  }\n  \n  .name-section h1 {\n    font-size: 24px;\n  }\n  \n  .image-container {\n    width: 120px;\n    height: 150px;\n  }\n}\n\n/* Add Section Controls */\n.add-section-controls {\n  position: absolute;\n  bottom: 20px;\n  left: 50%;\n  transform: translateX(-50%);\n  display: flex;\n  gap: 20px;\n  z-index: 10;\n}\n\n.add-left-section-btn,\n.add-right-section-btn {\n  background: linear-gradient(135deg, #8B5DBA, #6B46C1);\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 25px;\n  font-size: 12px;\n  font-weight: bold;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 12px rgba(139, 93, 186, 0.3);\n}\n\n.add-left-section-btn:hover,\n.add-right-section-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 16px rgba(139, 93, 186, 0.4);\n  background: linear-gradient(135deg, #7C3AED, #5B21B6);\n}\n", "/* Contact Details Modal Styles */\n\n.contact-details-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.6);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2000;\n  backdrop-filter: blur(2px);\n}\n\n.contact-details-modal {\n  width: 90%;\n  max-width: 450px;\n  background: white;\n  border: 2px solid #333;\n  border-radius: 8px;\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n.modal-header {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 30px 30px 20px;\n  background: white;\n  color: #333;\n  position: relative;\n}\n\n/* Gear Animation for Business Card */\n.gear-header {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  margin-bottom: 20px;\n  height: 40px;\n}\n\n.gear-icon {\n  font-size: 24px;\n  animation: rotate 3s linear infinite;\n}\n\n.small-gear-1 {\n  animation-delay: -1s;\n  animation-direction: reverse;\n}\n\n.small-gear-2 {\n  animation-delay: -2s;\n}\n\n@keyframes rotate {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n.modal-header h2 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  text-align: center;\n  letter-spacing: 1px;\n}\n\n.close-btn {\n  position: absolute;\n  top: 15px;\n  right: 15px;\n  width: 30px;\n  height: 30px;\n  border: none;\n  background: #f0f0f0;\n  color: #666;\n  border-radius: 50%;\n  cursor: pointer;\n  font-size: 20px;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.close-btn:hover:not(:disabled) {\n  background: rgba(255, 255, 255, 0.3);\n  transform: scale(1.05);\n}\n\n.close-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.contact-form {\n  padding: 24px;\n  flex: 1;\n  overflow-y: auto;\n}\n\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 6px;\n  font-size: 14px;\n  font-weight: 500;\n  color: #333;\n}\n\n.form-group input {\n  width: 100%;\n  padding: 12px 0;\n  border: none;\n  border-bottom: 1px solid #ddd;\n  font-size: 14px;\n  transition: all 0.2s ease;\n  background: transparent;\n  box-sizing: border-box;\n  outline: none;\n  direction: ltr;\n  text-align: left;\n}\n\n.form-group input:focus {\n  border-bottom-color: #8B5DBA;\n}\n\n.form-group input.error {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);\n}\n\n.form-group input:disabled {\n  background: #f8f9fa;\n  color: #666;\n  cursor: not-allowed;\n}\n\n.error-message {\n  display: block;\n  color: #dc3545;\n  font-size: 12px;\n  margin-top: 4px;\n  font-weight: 500;\n}\n\n.field-hint {\n  display: block;\n  color: #666;\n  font-size: 12px;\n  margin-top: 4px;\n  font-style: italic;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  padding-top: 20px;\n  border-top: 1px solid #e0e0e0;\n  margin-top: 20px;\n}\n\n.btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: inline-flex;\n  align-items: center;\n  gap: 8px;\n  min-width: 120px;\n  justify-content: center;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.btn-secondary {\n  background: #6c757d;\n  color: white;\n}\n\n.btn-secondary:hover:not(:disabled) {\n  background: #5a6268;\n  transform: translateY(-1px);\n}\n\n.btn-primary {\n  background: #8B5DBA;\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background: #7A4BA8;\n  transform: translateY(-1px);\n}\n\n.loading-spinner {\n  width: 16px;\n  height: 16px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-top: 2px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.contact-preview {\n  background: #f8f9fa;\n  padding: 20px 24px;\n  border-top: 1px solid #e0e0e0;\n}\n\n.contact-preview h3 {\n  margin: 0 0 12px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.preview-card {\n  background: white;\n  border: 1px solid #e0e0e0;\n  border-radius: 6px;\n  padding: 16px;\n}\n\n.preview-item {\n  margin-bottom: 8px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.preview-item:last-child {\n  margin-bottom: 0;\n}\n\n.preview-item strong {\n  color: #8B5DBA;\n  font-weight: 600;\n  margin-right: 8px;\n  min-width: 80px;\n  display: inline-block;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .contact-details-modal {\n    width: 95%;\n    max-height: 95vh;\n  }\n  \n  .modal-header,\n  .contact-form,\n  .contact-preview {\n    padding: 16px;\n  }\n  \n  .form-actions {\n    flex-direction: column;\n  }\n  \n  .btn {\n    width: 100%;\n  }\n  \n  .preview-item strong {\n    min-width: auto;\n    display: block;\n    margin-bottom: 2px;\n    margin-right: 0;\n  }\n}\n\n/* Focus trap for accessibility */\n.contact-details-modal {\n  position: relative;\n}\n\n.contact-details-modal:focus-within {\n  outline: none;\n}\n\n/* Animation */\n.contact-details-modal {\n  animation: modalSlideIn 0.3s ease-out;\n}\n\n@keyframes modalSlideIn {\n  from {\n    opacity: 0;\n    transform: scale(0.9) translateY(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1) translateY(0);\n  }\n}\n", "/* Name Flipbook Modal Styles */\n\n.name-flipbook-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  backdrop-filter: blur(4px);\n}\n\n.name-flipbook-modal {\n  width: 500px;\n  max-width: 90vw;\n  background: white;\n  border: 2px solid #000;\n  border-radius: 0;\n  position: relative;\n  padding: 60px 40px 40px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);\n}\n\n.close-btn {\n  position: absolute;\n  top: 15px;\n  right: 20px;\n  background: none;\n  border: none;\n  font-size: 24px;\n  font-weight: bold;\n  color: #666;\n  cursor: pointer;\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 2px;\n  transition: all 0.2s ease;\n}\n\n.close-btn:hover {\n  background: #f0f0f0;\n  color: #333;\n}\n\n.modal-content {\n  text-align: center;\n}\n\n/* Book Icon Styles */\n.flipbook-icon {\n  margin-bottom: 30px;\n}\n\n.book-icon {\n  position: relative;\n  display: inline-block;\n  width: 80px;\n  height: 80px;\n  margin: 0 auto;\n}\n\n.book-cover {\n  position: absolute;\n  width: 60px;\n  height: 70px;\n  background: white;\n  border: 2px solid #8B5DBA;\n  border-radius: 4px;\n  top: 5px;\n  left: 10px;\n  z-index: 3;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.1);\n  transform: rotate(-5deg);\n}\n\n.book-logo {\n  font-size: 28px;\n  font-weight: bold;\n  color: #8B5DBA;\n  font-family: serif;\n}\n\n.book-pages {\n  position: absolute;\n  width: 58px;\n  height: 68px;\n  background: #f8f8f8;\n  border: 2px solid #ddd;\n  border-radius: 4px;\n  top: 8px;\n  left: 8px;\n  z-index: 2;\n  box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.1);\n}\n\n.book-pages::after {\n  content: '';\n  position: absolute;\n  width: 56px;\n  height: 66px;\n  background: #f0f0f0;\n  border: 2px solid #ccc;\n  border-radius: 4px;\n  top: 3px;\n  left: 3px;\n  z-index: 1;\n}\n\n/* Modal Title */\n.modal-title {\n  font-size: 20px;\n  font-weight: 600;\n  color: #333;\n  margin: 0 0 40px 0;\n  letter-spacing: 1px;\n  text-transform: uppercase;\n}\n\n/* Form Styles */\n.name-form {\n  width: 100%;\n}\n\n.input-wrapper {\n  margin-bottom: 30px;\n  position: relative;\n}\n\n.name-input {\n  width: 100%;\n  background: transparent;\n  border: none;\n  border-bottom: 2px solid #ddd;\n  padding: 12px 0;\n  font-size: 16px;\n  color: #333;\n  outline: none;\n  transition: border-color 0.3s ease;\n  direction: ltr;\n  text-align: left;\n}\n\n.name-input:focus {\n  border-bottom-color: #8B5DBA;\n}\n\n.name-input::placeholder {\n  color: #999;\n  font-style: italic;\n}\n\n/* Submit Button */\n.submit-btn {\n  background: #4CAF50;\n  color: white;\n  border: none;\n  padding: 14px 40px;\n  font-size: 14px;\n  font-weight: 600;\n  letter-spacing: 1px;\n  text-transform: uppercase;\n  border-radius: 25px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);\n}\n\n.submit-btn:hover:not(:disabled) {\n  background: #45a049;\n  transform: translateY(-2px);\n  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);\n}\n\n.submit-btn:disabled {\n  background: #ccc;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .name-flipbook-modal {\n    width: 400px;\n    padding: 40px 30px 30px;\n  }\n  \n  .modal-title {\n    font-size: 18px;\n    margin-bottom: 30px;\n  }\n  \n  .book-icon {\n    width: 70px;\n    height: 70px;\n  }\n  \n  .book-cover {\n    width: 50px;\n    height: 60px;\n  }\n  \n  .book-logo {\n    font-size: 24px;\n  }\n}\n\n@media (max-width: 480px) {\n  .name-flipbook-modal {\n    width: 350px;\n    padding: 40px 20px 30px;\n  }\n}\n", "/* Add Page Modal Styles */\n\n.add-page-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  background: rgba(0, 0, 0, 0.6);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2000;\n  backdrop-filter: blur(4px);\n}\n\n.add-page-modal {\n  width: 95vw;\n  max-width: 1200px;\n  height: 90vh;\n  max-height: 800px;\n  background: white;\n  border: 2px solid #333;\n  border-radius: 0;\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);\n}\n\n/* Modal Header */\n.modal-header {\n  padding: 20px 40px;\n  text-align: center;\n  border-bottom: 1px solid #e0e0e0;\n  position: relative;\n}\n\n.close-btn {\n  position: absolute;\n  top: 15px;\n  right: 20px;\n  background: none;\n  border: none;\n  font-size: 24px;\n  font-weight: bold;\n  color: #666;\n  cursor: pointer;\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 2px;\n  transition: all 0.2s ease;\n}\n\n.close-btn:hover {\n  background: #f0f0f0;\n  color: #333;\n}\n\n.modal-title {\n  font-size: 20px;\n  font-weight: 600;\n  color: #333;\n  margin: 0 0 8px 0;\n  letter-spacing: 2px;\n  text-transform: uppercase;\n}\n\n.modal-subtitle {\n  font-size: 14px;\n  color: #666;\n  margin: 0;\n  font-style: italic;\n}\n\n/* Modal Content */\n.modal-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 20px 40px;\n}\n\n.template-section {\n  margin-bottom: 40px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #8B5DBA;\n  margin: 0 0 20px 0;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n}\n\n.templates-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n/* Template Cards */\n.template-card {\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 2px solid transparent;\n}\n\n.template-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.template-card.selected {\n  border-color: #8B5DBA;\n  box-shadow: 0 4px 16px rgba(139, 93, 186, 0.3);\n}\n\n.template-preview {\n  width: 100%;\n  height: 120px;\n  background: #f8f9fa;\n  border: 1px solid #e0e0e0;\n  position: relative;\n  overflow: hidden;\n}\n\n.template-preview.custom {\n  position: relative;\n}\n\n.delete-btn {\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  width: 20px;\n  height: 20px;\n  background: #ff4757;\n  color: white;\n  border: none;\n  border-radius: 50%;\n  font-size: 12px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n}\n\n.template-name {\n  padding: 8px 12px;\n  text-align: center;\n  font-size: 12px;\n  font-weight: 500;\n  color: #333;\n  background: #f8f9fa;\n  border-top: 1px solid #e0e0e0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n}\n\n.red-dot {\n  color: #ff4757;\n  font-size: 16px;\n}\n\n/* Preview Content Styles */\n.preview-content {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  padding: 8px;\n  gap: 4px;\n}\n\n.left-section {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.right-section {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.image-placeholder {\n  background: #ddd;\n  border: 1px solid #ccc;\n  flex: 1;\n  min-height: 30px;\n}\n\n.text-lines {\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.text-line {\n  height: 3px;\n  background: #333;\n  border-radius: 1px;\n}\n\n.text-line.long {\n  width: 80%;\n}\n\n.text-block {\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n  margin-bottom: 4px;\n}\n\n.image-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 2px;\n}\n\n.small-image {\n  background: #ddd;\n  border: 1px solid #ccc;\n  aspect-ratio: 1;\n}\n\n/* Left Overflow & Tiles */\n.large-image-left {\n  flex: 1.5;\n  background: #ddd;\n  border: 1px solid #ccc;\n}\n\n.right-tiles {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.text-header {\n  height: 4px;\n  background: #333;\n  margin-bottom: 2px;\n}\n\n.text-header.long {\n  height: 6px;\n  width: 90%;\n}\n\n.tile-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 2px;\n  flex: 1;\n}\n\n.tile {\n  background: #ddd;\n  border: 1px solid #ccc;\n}\n\n/* Panoramica */\n.panoramic-image {\n  width: 100%;\n  height: 60%;\n  background: #ddd;\n  border: 1px solid #ccc;\n  margin-bottom: 4px;\n}\n\n.bottom-content {\n  display: flex;\n  gap: 4px;\n  height: 35%;\n}\n\n.text-block-left {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.image-pair {\n  display: flex;\n  gap: 2px;\n  flex: 1;\n}\n\n/* Left Overflow */\n.overflow-image {\n  flex: 1;\n  background: #ddd;\n  border: 1px solid #ccc;\n}\n\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n/* Generic Preview */\n.generic-preview {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  background: #f0f0f0;\n}\n\n.placeholder-text {\n  font-size: 10px;\n  color: #666;\n  text-align: center;\n}\n\n/* Custom Creation Area */\n.custom-creation-area {\n  display: flex;\n  gap: 20px;\n  margin-top: 20px;\n}\n\n.drag-drop-zone {\n  width: 200px;\n  height: 150px;\n  border: 3px dashed #8B5DBA;\n  background: #f8f4ff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 8px;\n}\n\n.drag-instructions {\n  text-align: center;\n  font-size: 12px;\n  font-weight: 600;\n  color: #8B5DBA;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  line-height: 1.4;\n}\n\n.singlets-library {\n  flex: 1;\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: 15px;\n}\n\n.singlet-item {\n  cursor: move;\n  transition: transform 0.2s ease;\n}\n\n.singlet-item:hover {\n  transform: scale(1.05);\n}\n\n.singlet-preview {\n  width: 100%;\n  height: 80px;\n  background: white;\n  border: 2px solid #ddd;\n  border-radius: 4px;\n  padding: 6px;\n  display: flex;\n  flex-direction: column;\n  gap: 3px;\n}\n\n/* Singlet Layout Styles */\n.singlet-preview.text-layout .text-lines {\n  flex: 1;\n}\n\n.singlet-preview.text-layout .image-bottom {\n  height: 20px;\n  background: #ddd;\n  border: 1px solid #ccc;\n}\n\n.singlet-preview.text-only .text-header {\n  height: 4px;\n  background: #333;\n  margin-bottom: 2px;\n}\n\n.singlet-preview.image-text .header-line {\n  height: 3px;\n  background: #333;\n  margin-bottom: 3px;\n}\n\n.singlet-preview.image-text .content-row {\n  display: flex;\n  gap: 3px;\n  flex: 1;\n}\n\n.singlet-preview.image-text .text-column {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.singlet-preview.two-column .header-full {\n  height: 4px;\n  background: #333;\n  margin-bottom: 3px;\n}\n\n.singlet-preview.two-column .two-col-content {\n  display: flex;\n  gap: 3px;\n  flex: 1;\n}\n\n.singlet-preview.two-column .col-left,\n.singlet-preview.two-column .col-right {\n  flex: 1;\n}\n\n.singlet-preview.two-column .col-left {\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.singlet-preview.grid-layout .header-line {\n  height: 3px;\n  background: #333;\n  margin-bottom: 3px;\n}\n\n.singlet-preview.grid-layout .grid-content {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 2px;\n  flex: 1;\n}\n\n.singlet-preview.grid-layout .grid-item {\n  background: #ddd;\n  border: 1px solid #ccc;\n}\n\n/* Modal Footer */\n.modal-footer {\n  padding: 20px 40px;\n  border-top: 1px solid #e0e0e0;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.insert-page-container {\n  display: flex;\n  align-items: center;\n}\n\n.insert-page-btn {\n  background: white;\n  border: 3px solid #8B5DBA;\n  border-radius: 50%;\n  width: 80px;\n  height: 80px;\n  cursor: pointer;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  color: #8B5DBA;\n  font-size: 10px;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  line-height: 1.2;\n}\n\n.insert-page-btn:hover:not(:disabled) {\n  background: #8B5DBA;\n  color: white;\n  transform: scale(1.1);\n}\n\n.insert-page-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.insert-icon {\n  margin-bottom: 4px;\n}\n\n.plus-sign {\n  font-size: 24px;\n  font-weight: bold;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .add-page-modal {\n    width: 98vw;\n    height: 95vh;\n  }\n  \n  .modal-content {\n    padding: 15px 20px;\n  }\n  \n  .templates-grid {\n    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));\n    gap: 15px;\n  }\n  \n  .template-preview {\n    height: 100px;\n  }\n  \n  .custom-creation-area {\n    flex-direction: column;\n  }\n  \n  .drag-drop-zone {\n    width: 100%;\n    height: 100px;\n  }\n  \n  .singlets-library {\n    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));\n    gap: 10px;\n  }\n  \n  .singlet-preview {\n    height: 60px;\n  }\n}\n\n@media (max-width: 480px) {\n  .modal-header {\n    padding: 15px 20px;\n  }\n  \n  .modal-title {\n    font-size: 16px;\n  }\n  \n  .templates-grid {\n    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  }\n  \n  .insert-page-btn {\n    width: 60px;\n    height: 60px;\n    font-size: 8px;\n  }\n  \n  .plus-sign {\n    font-size: 18px;\n  }\n}\n", "/* Text Toolbar Styles */\n\n.text-toolbar {\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border: 1px solid #ddd;\n  border-bottom: 2px solid #8B5DBA;\n  border-radius: 0;\n  padding: 12px 16px;\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  font-family: 'Segoe UI', system-ui, sans-serif;\n  font-size: 13px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n  flex-wrap: wrap;\n  position: relative;\n  z-index: 100;\n  margin: 0;\n  border-left: none;\n  border-right: none;\n}\n\n.toolbar-section {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  position: relative;\n}\n\n.toolbar-label {\n  font-weight: bold;\n  color: #333;\n  font-size: 11px;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.toolbar-micro-label {\n  font-size: 10px;\n  color: #666;\n  text-transform: uppercase;\n  letter-spacing: 0.3px;\n  margin-right: 2px;\n}\n\n/* Dropdowns */\n.font-family-select,\n.font-size-select,\n.line-height-select,\n.letter-spacing-select {\n  padding: 4px 6px;\n  border: 1px solid #ccc;\n  border-radius: 3px;\n  background: white;\n  font-size: 11px;\n  min-width: 60px;\n  cursor: pointer;\n  transition: border-color 0.2s ease;\n}\n\n.font-family-select {\n  min-width: 100px;\n}\n\n.font-size-select {\n  min-width: 50px;\n}\n\n.line-height-select,\n.letter-spacing-select {\n  min-width: 45px;\n}\n\n.font-family-select:hover,\n.font-size-select:hover,\n.line-height-select:hover,\n.letter-spacing-select:hover {\n  border-color: #999;\n}\n\n.font-family-select:focus,\n.font-size-select:focus,\n.line-height-select:focus,\n.letter-spacing-select:focus {\n  outline: none;\n  border-color: #8B5DBA;\n  box-shadow: 0 0 3px rgba(139, 93, 186, 0.3);\n}\n\n/* Format Buttons */\n.format-buttons,\n.alignment-buttons,\n.list-buttons {\n  display: flex;\n  gap: 2px;\n  border: 1px solid #ddd;\n  border-radius: 3px;\n  overflow: hidden;\n}\n\n.format-btn {\n  background: white;\n  border: none;\n  padding: 6px 8px;\n  cursor: pointer;\n  font-size: 12px;\n  font-family: inherit;\n  transition: all 0.2s ease;\n  min-width: 28px;\n  height: 28px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-right: 1px solid #ddd;\n}\n\n.format-btn:last-child {\n  border-right: none;\n}\n\n.format-btn:hover {\n  background: #e8e8e8;\n}\n\n.format-btn.active {\n  background: #8B5DBA;\n  color: white;\n}\n\n.format-btn:active {\n  transform: translateY(1px);\n}\n\n/* Color Section */\n.color-section {\n  display: flex;\n  gap: 4px;\n}\n\n.color-picker-container {\n  position: relative;\n}\n\n.color-btn {\n  background: white;\n  border: 1px solid #ddd;\n  border-radius: 3px;\n  padding: 4px;\n  cursor: pointer;\n  width: 32px;\n  height: 28px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s ease;\n}\n\n.color-btn:hover {\n  border-color: #999;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.color-indicator {\n  font-weight: bold;\n  font-size: 14px;\n  color: inherit;\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 2px;\n}\n\n.bg-indicator {\n  font-size: 16px;\n}\n\n.color-picker {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  background: white;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  padding: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  z-index: 1000;\n  margin-top: 2px;\n}\n\n.color-grid {\n  display: grid;\n  grid-template-columns: repeat(8, 1fr);\n  gap: 3px;\n  width: 200px;\n}\n\n.color-option {\n  width: 20px;\n  height: 20px;\n  border: 1px solid #ddd;\n  border-radius: 3px;\n  cursor: pointer;\n  transition: transform 0.2s ease;\n}\n\n.color-option:hover {\n  transform: scale(1.1);\n  border-color: #333;\n}\n\n/* Link Button */\n.link-btn {\n  background: white;\n  border: 1px solid #ddd;\n  border-radius: 3px;\n  padding: 6px 8px;\n  cursor: pointer;\n  font-size: 14px;\n  width: 32px;\n  height: 28px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s ease;\n}\n\n.link-btn:hover {\n  background: #e8e8e8;\n  border-color: #999;\n}\n\n/* Separator */\n.toolbar-section + .toolbar-section::before {\n  content: '';\n  width: 1px;\n  height: 20px;\n  background: #ddd;\n  margin: 0 4px;\n}\n\n.toolbar-section:first-child + .toolbar-section::before {\n  display: none;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .text-toolbar {\n    padding: 6px 8px;\n    gap: 8px;\n    font-size: 11px;\n  }\n\n  .font-family-select {\n    min-width: 80px;\n  }\n\n  .format-btn {\n    min-width: 24px;\n    height: 24px;\n    padding: 4px 6px;\n    font-size: 11px;\n  }\n\n  .color-btn,\n  .link-btn {\n    width: 28px;\n    height: 24px;\n  }\n\n  .toolbar-section + .toolbar-section::before {\n    display: none;\n  }\n}\n\n@media (max-width: 480px) {\n  .text-toolbar {\n    flex-wrap: wrap;\n    padding: 4px 6px;\n    gap: 6px;\n  }\n\n  .toolbar-section {\n    flex-shrink: 0;\n  }\n\n  .font-family-select,\n  .font-size-select {\n    font-size: 10px;\n  }\n\n  .color-grid {\n    grid-template-columns: repeat(6, 1fr);\n    width: 150px;\n  }\n}\n", "/* FlipbookEditor Styles */\n\n.flipbook-editor {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: #f5f5f5;\n}\n\n.editor-content {\n  display: flex;\n  flex: 1;\n  min-height: 0; /* Allow flex children to shrink */\n}\n\n/* Header Styling to match the image */\n.flipbook-editor .header {\n  background: linear-gradient(135deg, #8B5DBA 0%, #7A4BA8 100%);\n  color: white;\n  padding: 10px 0;\n}\n\n.flipbook-editor .header .nav-menu {\n  display: flex;\n  justify-content: center;\n  gap: 30px;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n}\n\n.flipbook-editor .header .nav-item {\n  color: white;\n  text-decoration: none;\n  padding: 8px 16px;\n  border-radius: 4px;\n  transition: background 0.2s ease;\n}\n\n.flipbook-editor .header .nav-item:hover {\n  background: rgba(255, 255, 255, 0.1);\n}\n\n.editor-header {\n  border-bottom: 1px solid #e0e0e0;\n  background: white;\n}\n\n.editor-toolbar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 12px 20px;\n  background: white;\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.toolbar-left {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.flipbook-title {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: var(--text-primary, #1e293b);\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n  transition: color 0.2s ease;\n}\n\n.flipbook-title:hover {\n  color: var(--primary-purple, #8B5DBA);\n}\n\n.new-badge {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n  font-size: 10px;\n  font-weight: 700;\n  padding: 2px 6px;\n  border-radius: 4px;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.last-saved {\n  font-size: 12px;\n  color: var(--text-muted, #94a3b8);\n  margin-right: 12px;\n}\n\n.page-navigator {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 8px 16px;\n  background: #f8f8f8;\n  border-radius: 6px;\n  border: 1px solid #e0e0e0;\n}\n\n.page-info {\n  font-size: 14px;\n  color: #666;\n  min-width: 80px;\n  text-align: center;\n}\n\n.toolbar-right {\n  display: flex;\n  gap: 12px;\n}\n\n.editor-content {\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n}\n\n.editor-sidebar {\n  width: 160px;\n  background: #2c3e50;\n  color: white;\n  display: flex;\n  flex-direction: column;\n  border-right: 1px solid #34495e;\n}\n\n.editor-sidebar .sidebar-section {\n  border-bottom: 1px solid #34495e;\n}\n\n/* Toolbar Icons */\n.toolbar-icons {\n  padding: 12px 8px;\n}\n\n.icon-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 8px;\n}\n\n.icon-btn {\n  background: #34495e;\n  border: 1px solid #4a5f7a;\n  color: white;\n  padding: 8px;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 14px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 32px;\n}\n\n.icon-btn:hover {\n  background: #4a5f7a;\n  border-color: #8B5DBA;\n}\n\n.icon-btn:active {\n  background: #8B5DBA;\n}\n\n.editor-sidebar .click-drag-section {\n  background: #34495e;\n  color: white;\n  text-align: center;\n  padding: 12px 8px;\n  font-size: 11px;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  line-height: 1.2;\n}\n\n.editor-sidebar .pages-section {\n  flex: 1;\n  padding: 15px 8px;\n}\n\n.editor-sidebar .page-thumbnail {\n  background: white;\n  margin-bottom: 8px;\n  border-radius: 4px;\n  aspect-ratio: 8.5 / 11;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  color: #666;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n}\n\n.editor-sidebar .page-thumbnail.active {\n  border: 2px solid #8B5DBA;\n}\n\n.editor-sidebar .page-thumbnail:hover {\n  transform: scale(1.05);\n}\n\n/* Page Drag and Drop Styles */\n.editor-sidebar .page-thumbnail.draggable {\n  cursor: move;\n  position: relative;\n}\n\n.editor-sidebar .page-thumbnail.not-draggable {\n  cursor: default;\n  opacity: 0.8;\n}\n\n.editor-sidebar .page-thumbnail.dragging {\n  opacity: 0.5;\n  transform: rotate(5deg) scale(0.9);\n  z-index: 1000;\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);\n}\n\n.editor-sidebar .page-thumbnail.drag-over {\n  border: 2px solid #8B5DBA;\n  background: rgba(139, 93, 186, 0.1);\n}\n\n.editor-sidebar .page-drag-handle {\n  position: absolute;\n  left: -8px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 16px;\n  height: 20px;\n  background: rgba(139, 93, 186, 0.8);\n  color: white;\n  border-radius: 3px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 8px;\n  opacity: 0;\n  transition: opacity 0.2s ease;\n  cursor: grab;\n}\n\n.editor-sidebar .page-drag-handle:active {\n  cursor: grabbing;\n}\n\n.editor-sidebar .page-thumbnail.draggable:hover .page-drag-handle {\n  opacity: 1;\n}\n\n.editor-sidebar .fixed-page-indicator {\n  position: absolute;\n  top: 2px;\n  right: 2px;\n  font-size: 10px;\n  color: #666;\n}\n\n.editor-sidebar .page-label {\n  position: relative;\n  z-index: 1;\n}\n\n.editor-sidebar .page-thumbnail.not-draggable::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: repeating-linear-gradient(\n    45deg,\n    transparent,\n    transparent 2px,\n    rgba(200, 200, 200, 0.3) 2px,\n    rgba(200, 200, 200, 0.3) 4px\n  );\n  pointer-events: none;\n}\n\n.editor-sidebar .click-drag-section {\n  background: #34495e;\n  color: white;\n  text-align: center;\n  padding: 12px 8px;\n  font-size: 11px;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  line-height: 1.2;\n  animation: pulse-text 2s ease-in-out infinite;\n}\n\n@keyframes pulse-text {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0.7; }\n}\n\n.editor-sidebar .add-page-section {\n  padding: 12px 8px;\n  text-align: center;\n}\n\n.editor-sidebar .add-page-btn {\n  width: 100%;\n  background: #27ae60;\n  color: white;\n  border: none;\n  padding: 8px;\n  border-radius: 4px;\n  font-size: 10px;\n  font-weight: 600;\n  cursor: pointer;\n  text-transform: uppercase;\n}\n\n.editor-sidebar .toolbar-icons {\n  padding: 15px 8px;\n  border-bottom: 1px solid #34495e;\n}\n\n.editor-sidebar .icon-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 8px;\n}\n\n.editor-sidebar .icon-btn {\n  width: 40px;\n  height: 40px;\n  background: #34495e;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: background 0.2s ease;\n}\n\n.editor-sidebar .icon-btn:hover {\n  background: #8B5DBA;\n}\n\n.editor-sidebar .icon-btn.active {\n  background: #8B5DBA;\n}\n\n.tool-panel, .pages-panel {\n  padding: 20px;\n}\n\n.tool-panel {\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.tool-panel h3, .pages-panel h3 {\n  margin: 0 0 16px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.tool-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 8px;\n}\n\n.tool-btn {\n  width: 50px;\n  height: 50px;\n  border: 1px solid #e0e0e0;\n  background: white;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 18px;\n}\n\n.tool-btn:hover {\n  background: #f8f8f8;\n  border-color: #8B5DBA;\n}\n\n.tool-btn:active {\n  transform: scale(0.95);\n}\n\n.pages-panel {\n  flex: 1;\n  overflow-y: auto;\n}\n\n.pages-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.page-thumbnail {\n  width: 100%;\n  height: 80px;\n  border: 2px solid #e0e0e0;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  overflow: hidden;\n}\n\n.page-thumbnail:hover {\n  border-color: #8B5DBA;\n}\n\n.page-thumbnail.active {\n  border-color: #8B5DBA;\n  background: #f8f4ff;\n}\n\n.thumbnail-preview {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: white;\n  color: #666;\n  font-size: 12px;\n}\n\n.add-page-btn {\n  width: 100%;\n  height: 40px;\n  border: 2px dashed #ccc;\n  background: transparent;\n  border-radius: 6px;\n  color: #666;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 12px;\n}\n\n.add-page-btn:hover {\n  border-color: #8B5DBA;\n  color: #8B5DBA;\n}\n\n.editor-canvas {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  background: #f5f7fa;\n}\n\n.canvas-container {\n  max-width: 700px;\n  width: 100%;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  border: 1px solid #e1e5e9;\n}\n\n.page-canvas {\n  width: 100%;\n  aspect-ratio: 8.5 / 11; /* Standard letter size */\n  background: white;\n  position: relative;\n}\n\n/* Pages Header */\n.pages-header {\n  text-align: center;\n  padding: 20px 0;\n  background: white;\n  border-bottom: 2px solid #e9ecef;\n  margin-bottom: 20px;\n}\n\n.pages-header h1 {\n  margin: 0 0 10px 0;\n  font-size: 14px;\n  font-weight: bold;\n  color: #666;\n  letter-spacing: 2px;\n  text-transform: uppercase;\n}\n\n.pages-header .flipbook-title {\n  margin: 0;\n  font-size: 24px;\n  font-weight: bold;\n  color: #2c3e50;\n  font-family: 'Times New Roman', serif;\n}\n\n.page-content {\n  padding: 40px;\n  height: 100%;\n  overflow-y: auto;\n}\n\n.page-content h1 {\n  margin: 0 0 20px 0;\n  color: #333;\n  font-size: 24px;\n}\n\n.page-content p {\n  margin: 0 0 16px 0;\n  color: #666;\n  line-height: 1.6;\n}\n\n.page-content ul {\n  color: #666;\n  line-height: 1.6;\n}\n\n.page-content li {\n  margin-bottom: 8px;\n}\n\n.properties-panel {\n  width: 280px;\n  background: white;\n  border-left: 1px solid #e0e0e0;\n  padding: 20px;\n}\n\n.properties-panel h3 {\n  margin: 0 0 20px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.property-group {\n  margin-bottom: 20px;\n}\n\n.property-group label {\n  display: block;\n  margin-bottom: 8px;\n  font-size: 12px;\n  font-weight: 500;\n  color: #666;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.property-group input[type=\"range\"] {\n  width: 100%;\n  height: 4px;\n  background: #e0e0e0;\n  border-radius: 2px;\n  outline: none;\n  -webkit-appearance: none;\n  appearance: none;\n}\n\n.property-group input[type=\"range\"]::-webkit-slider-thumb {\n  -webkit-appearance: none;\n  appearance: none;\n  width: 16px;\n  height: 16px;\n  background: #8B5DBA;\n  border-radius: 50%;\n  cursor: pointer;\n}\n\n.property-group input[type=\"color\"] {\n  width: 100%;\n  height: 40px;\n  border: 1px solid #e0e0e0;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.alignment-controls {\n  display: flex;\n  gap: 4px;\n}\n\n.alignment-controls .btn {\n  flex: 1;\n  padding: 8px;\n  font-size: 12px;\n}\n\n/* Button Styles */\n.btn {\n  padding: 8px 16px;\n  border: 1px solid #e0e0e0;\n  background: white;\n  color: #666;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.2s ease;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn:hover {\n  background: #f8f8f8;\n  border-color: #ccc;\n}\n\n.btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.btn.btn-primary {\n  background: #8B5DBA;\n  color: white;\n  border-color: #8B5DBA;\n}\n\n.btn.btn-primary:hover {\n  background: #7A4BA8;\n  border-color: #7A4BA8;\n}\n\n.btn.btn-secondary {\n  background: #6c757d;\n  color: white;\n  border-color: #6c757d;\n}\n\n.btn.btn-secondary:hover {\n  background: #5a6268;\n  border-color: #5a6268;\n}\n\n.btn.btn-sm {\n  padding: 4px 8px;\n  font-size: 12px;\n}\n\n/* Back Page Styles */\n.back-page {\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.back-page-content {\n  width: 100%;\n  height: 100%;\n  padding: 40px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.contact-info-section {\n  background: white;\n  padding: 30px;\n  border-radius: 12px;\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e0e0e0;\n  max-width: 400px;\n  width: 100%;\n}\n\n.contact-info-section h2 {\n  margin: 0 0 20px 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #2c3e50;\n  text-align: center;\n}\n\n.contact-card {\n  cursor: pointer;\n  transition: all 0.3s ease;\n  padding: 20px;\n  border: 2px solid #e0e0e0;\n  border-radius: 8px;\n  background: #f8f9fa;\n}\n\n.contact-card:hover {\n  border-color: #8B5DBA;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(139, 93, 186, 0.2);\n}\n\n.contact-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n  font-size: 14px;\n}\n\n.contact-item:last-child {\n  margin-bottom: 0;\n}\n\n.contact-item .label {\n  font-weight: 600;\n  color: #8B5DBA;\n  min-width: 80px;\n}\n\n.contact-item .value {\n  color: #2c3e50;\n  text-align: right;\n  flex: 1;\n  margin-left: 12px;\n}\n\n.edit-hint {\n  text-align: center;\n  font-size: 12px;\n  color: #666;\n  margin-top: 16px;\n  padding-top: 16px;\n  border-top: 1px solid #e0e0e0;\n  font-style: italic;\n}\n\n.flipbook-branding {\n  position: absolute;\n  bottom: 30px;\n  right: 30px;\n  text-align: center;\n  color: #666;\n}\n\n.brand-logo {\n  font-size: 24px;\n  font-weight: 600;\n  color: #8B5DBA;\n  margin-bottom: 4px;\n}\n\n.flipbook-branding p {\n  margin: 0;\n  font-size: 12px;\n}\n\n.navigation-controls {\n  position: absolute;\n  left: 30px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n.nav-btn {\n  width: 50px;\n  height: 50px;\n  border: none;\n  border-radius: 50%;\n  cursor: pointer;\n  font-size: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.prev-btn {\n  background: #6c757d;\n  color: white;\n}\n\n.next-btn {\n  background: #8B5DBA;\n  color: white;\n}\n\n.nav-btn:hover {\n  transform: scale(1.1);\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);\n}\n\n.prev-btn:hover {\n  background: #5a6268;\n}\n\n.next-btn:hover {\n  background: #7A4BA8;\n}\n\n.arrow-icon {\n  transition: transform 0.2s ease;\n}\n\n.prev-btn:hover .arrow-icon {\n  transform: translateX(-2px);\n}\n\n.next-btn:hover .arrow-icon {\n  transform: translateX(2px);\n}\n\n/* Waiting for Name State */\n.waiting-for-name {\n  width: 100%;\n  aspect-ratio: 8.5 / 11;\n  background: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px dashed #ddd;\n  border-radius: 8px;\n}\n\n.placeholder-content {\n  text-align: center;\n  color: #666;\n}\n\n.placeholder-content h2 {\n  margin: 0 0 12px 0;\n  font-size: 20px;\n  font-weight: 500;\n  color: #333;\n}\n\n.placeholder-content p {\n  margin: 0;\n  font-size: 14px;\n  color: #666;\n}\n", ".flipbook-preview-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.9);\n  z-index: 1000;\n  display: flex;\n  flex-direction: column;\n}\n\n.flipbook-preview {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  background-color: #f8fafc;\n}\n\n/* Header */\n.preview-header {\n  background: linear-gradient(135deg, #a5a2cd 0%, #9f7db3 100%);\n  height: 68px;\n  display: flex;\n  align-items: center;\n  padding: 0 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.header-content {\n  width: 100%;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.logo-btn {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 5px 10px;\n  border-radius: 4px;\n  transition: background-color 0.2s ease;\n}\n\n.logo-btn:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.logo-btn img {\n  height: 40px;\n  width: auto;\n}\n\n/* Controls */\n.inspiration-controls,\n.user-controls {\n  background-color: white;\n  border-bottom: 1px solid #e2e8f0;\n  padding: 10px 0;\n}\n\n.controls-container {\n  max-width: 1144px;\n  margin: 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 15px;\n  position: relative;\n}\n\n.copy-btn {\n  background: none;\n  border: none;\n  color: #57307a;\n  font-weight: 600;\n  cursor: pointer;\n  padding: 8px 16px;\n  border-radius: 4px;\n  transition: all 0.2s ease;\n  text-decoration: underline;\n}\n\n.copy-btn:hover {\n  background-color: #57307a;\n  color: white;\n  text-decoration: none;\n}\n\n.return-btn {\n  background: none;\n  border: none;\n  color: #57307a;\n  font-weight: 600;\n  cursor: pointer;\n  padding: 8px 16px;\n  border-radius: 4px;\n  transition: all 0.2s ease;\n  text-decoration: underline;\n}\n\n.return-btn:hover {\n  background-color: #57307a;\n  color: white;\n  text-decoration: none;\n}\n\n.playback-controls {\n  display: flex;\n  gap: 10px;\n}\n\n.play-btn,\n.pause-btn {\n  background-color: #57307a;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  transition: background-color 0.2s ease;\n}\n\n.play-btn:hover,\n.pause-btn:hover {\n  background-color: #4a2968;\n}\n\n.page-indicators {\n  position: absolute;\n  left: 50%;\n  transform: translateX(-50%);\n  display: flex;\n  gap: 20px;\n}\n\n.page-indicator {\n  background-color: #8B5CF6;\n  color: white;\n  padding: 6px 12px;\n  border-radius: 4px;\n  font-size: 12px;\n  font-weight: 600;\n}\n\n.first-page {\n  background-color: #10b981;\n}\n\n.last-page {\n  background-color: #ef4444;\n}\n\n/* Flipbook Viewer */\n.flipbook-viewer {\n  flex: 1;\n  overflow: hidden;\n  position: relative;\n  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);\n}\n.flipbook-canvas {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  overflow: auto;\n  padding: 10px;\n  box-sizing: border-box;\n}\n\n.book-container {\n  width: 100%;\n  max-width: min(95vw, 1400px);\n  height: 100%;\n  max-height: min(85vh, 1000px);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.flipbook-content {\n  width: 100% !important;\n  height: 100% !important;\n  min-width: min(90vw, 1200px) !important;\n  min-height: min(80vh, 900px) !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  background: white !important;\n  border-radius: 8px !important;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;\n  overflow: auto !important;\n}\n\n.preview-content {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.page-content {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: white;\n}\n\n.page-placeholder {\n  text-align: center;\n  color: #64748b;\n}\n\n.page-placeholder h3 {\n  font-size: 24px;\n  margin-bottom: 20px;\n  color: #1e293b;\n}\n\n.page-placeholder p {\n  font-size: 16px;\n  margin: 10px 0;\n}\n\n/* Navigation Controls */\n.navigation-controls {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 100%;\n  display: flex;\n  justify-content: space-between;\n  padding: 0 20px;\n  pointer-events: none;\n}\n\n.nav-btn {\n  background: rgba(255, 255, 255, 0.9);\n  border: none;\n  border-radius: 50%;\n  width: 50px;\n  height: 50px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);\n  transition: all 0.2s ease;\n  pointer-events: all;\n}\n\n.nav-btn:hover:not(:disabled) {\n  background: white;\n  transform: scale(1.1);\n}\n\n.nav-btn:disabled {\n  opacity: 0.3;\n  cursor: not-allowed;\n}\n\n.nav-btn img {\n  width: 20px;\n  height: 20px;\n}\n\n/* Page Slider */\n.page-slider {\n  position: absolute;\n  bottom: 30px;\n  left: 50%;\n  transform: translateX(-50%);\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  background: rgba(255, 255, 255, 0.9);\n  padding: 10px 20px;\n  border-radius: 25px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n.slider {\n  width: 200px;\n  height: 4px;\n  border-radius: 5px;\n  background: #e2e8f0;\n  outline: none;\n  -webkit-appearance: none;\n  appearance: none;\n}\n\n.slider::-webkit-slider-thumb {\n  -webkit-appearance: none;\n  appearance: none;\n  width: 16px;\n  height: 16px;\n  border-radius: 50%;\n  background: #8B5CF6;\n  cursor: pointer;\n  border: 2px solid white;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\n}\n\n.slider::-moz-range-thumb {\n  width: 16px;\n  height: 16px;\n  border-radius: 50%;\n  background: #8B5CF6;\n  cursor: pointer;\n  border: 2px solid white;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\n}\n\n.page-counter {\n  font-size: 14px;\n  font-weight: 600;\n  color: #334155;\n  min-width: 50px;\n  text-align: center;\n}\n\n/* Copy Modal */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1100;\n}\n\n.copy-modal {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n  max-width: 400px;\n  width: 90%;\n}\n\n.modal-content {\n  padding: 40px;\n  text-align: center;\n  position: relative;\n}\n\n.close-btn {\n  position: absolute;\n  top: 15px;\n  right: 15px;\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: #64748b;\n  width: 30px;\n  height: 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: background-color 0.2s ease;\n}\n\n.close-btn:hover {\n  background-color: #f1f5f9;\n}\n\n.modal-icon {\n  margin-bottom: 20px;\n}\n\n.modal-icon img {\n  width: 60px;\n  height: 60px;\n}\n\n.copy-modal h2 {\n  font-size: 24px;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 30px;\n  letter-spacing: 1px;\n}\n\n.input-group {\n  margin-bottom: 30px;\n}\n\n.title-input {\n  width: 100%;\n  padding: 12px 16px;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 16px;\n  transition: border-color 0.2s ease;\n}\n\n.title-input:focus {\n  outline: none;\n  border-color: #8B5CF6;\n}\n\n.submit-btn {\n  background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%);\n  color: white;\n  border: none;\n  padding: 12px 30px;\n  border-radius: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  letter-spacing: 0.5px;\n}\n\n.submit-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);\n}\n\n.submit-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n/* Responsive */\n@media (max-width: 768px) {\n  .controls-container {\n    flex-direction: column;\n    gap: 10px;\n    align-items: stretch;\n  }\n  \n  .page-indicators {\n    position: static;\n    transform: none;\n    justify-content: center;\n  }\n  \n  .page-slider .slider {\n    width: 150px;\n  }\n  \n  .modal-content {\n    margin: 20px;\n    padding: 15px;\n  }\n  \n  .book-container {\n    max-width: 98vw;\n    max-height: 75vh;\n  }\n  \n  .flipbook-canvas {\n    padding: 5px;\n  }\n  \n  /* Adjust text scaling for mobile */\n  .flipbook-page-html [style*=\"font-size: 23pt\"] {\n    font-size: clamp(20pt, 6vw, 28pt) !important;\n  }\n  \n  .flipbook-page-html [style*=\"font-size: 22pt\"] {\n    font-size: clamp(18pt, 5.5vw, 26pt) !important;\n  }\n}\n\n/* Original Flipbook - Architect Content */\n.architect-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  text-align: center;\n  font-family: 'Arial', sans-serif;\n}\n\n.architect-label {\n  font-size: 18px;\n  font-weight: 400;\n  color: #666;\n  margin-bottom: 8px;\n  letter-spacing: 2px;\n}\n\n.architect-title {\n  font-size: 36px;\n  font-weight: bold;\n  color: #333;\n  letter-spacing: 3px;\n}\n\n.page-content .page-info {\n  position: absolute;\n  bottom: 20px;\n  left: 50%;\n  transform: translateX(-50%);\n  font-size: 12px;\n  color: #888;\n  margin: 0;\n}\n\n/* Flipbook HTML Content */\n.flipbook-page-html {\n  width: 100%;\n  height: 100%;\n  overflow: auto;\n  position: relative;\n  background: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.flipbook-page-html .book-content {\n  width: 100% !important;\n  height: 100% !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  min-height: 85vh !important;\n  max-height: none !important;\n}\n\n.flipbook-page-html .div-flip-main-left,\n.flipbook-page-html .div-flip-main-right {\n  width: 100% !important;\n  height: 100% !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  position: relative !important;\n}\n\n/* Fix right page positioning */\n.flipbook-page-html .div-flip-main-right {\n  margin-left: 0 !important;\n  margin-top: 0 !important;\n  margin-right: 0 !important;\n  margin-bottom: 0 !important;\n  transform: translateX(0) !important;\n  transform: translateY(0) !important;\n  left: auto !important;\n  right: auto !important;\n  top: auto !important;\n  bottom: auto !important;\n}\n\n.flipbook-page-html .flip_wrapper {\n  max-width: 100% !important;\n  max-height: 100% !important;\n  width: min(90vw, 1200px) !important;\n  height: min(80vh, 900px) !important;\n  border: 1px solid #000 !important;\n  margin: 0 auto !important;\n}\n\n/* Resume container styling */\n.flipbook-page-html .resume-container,\n.flipbook-page-html .resume-container-right {\n  width: min(90vw, 1200px) !important;\n  height: min(80vh, 900px) !important;\n  border: clamp(8px, 1vw, 15px) solid rgb(210, 196, 177) !important;\n  background-color: rgb(210, 196, 177) !important;\n  margin: 0 auto !important;\n  display: flex !important;\n  box-sizing: border-box !important;\n  overflow: visible !important;\n}\n\n/* Project container styling */\n.flipbook-page-html .project-container,\n.flipbook-page-html .project-container-right,\n.flipbook-page-html .midcentury-container {\n  width: min(90vw, 1200px) !important;\n  height: min(80vh, 900px) !important;\n  margin: 0 auto !important;\n  display: flex !important;\n  background: white !important;\n}\n\n/* Container sizing consistency - Force maximum size */\n.flipbook-page-html .container,\n.flipbook-page-html .common_margin_layout,\n.flipbook-page-html .bb-custom-wrapper,\n.flipbook-page-html .bb-bookblock,\n.flipbook-page-html .bb-item {\n  width: 100% !important;\n  height: 100% !important;\n  min-width: min(85vw, 1000px) !important;\n  min-height: min(75vh, 700px) !important;\n  margin: 0 !important;\n  padding: 0 !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  overflow: visible !important;\n  box-sizing: border-box !important;\n}\n\n/* Fix layout wrapper to prevent content cutoff */\n.flipbook-page-html .custom_side_wrapper {\n  display: flex !important;\n  height: 100% !important;\n  width: 100% !important;\n  gap: clamp(10px, 2vw, 20px) !important;\n  box-sizing: border-box !important;\n  overflow: visible !important;\n}\n\n/* Fix image display issues */\n.flipbook-page-html img {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Hide broken image icons and eraser tools */\n.flipbook-page-html .eraserForAllDiv,\n.flipbook-page-html .divEraserForAll,\n.flipbook-page-html .Hamburger,\n.flipbook-page-html .trashcan,\n.flipbook-page-html .imgEraserForAll,\n.flipbook-page-html .eraserForAllDivimg,\n.flipbook-page-html .clIsPlaceHolder {\n  display: none !important;\n}\n\n/* Fix layout containers */\n.flipbook-page-html .container {\n  width: 100% !important;\n  height: 100% !important;\n  margin: 0 !important;\n  padding: 0 !important;\n}\n\n.flipbook-page-html .common_margin_layout,\n.flipbook-page-html .bb-custom-wrapper,\n.flipbook-page-html .bb-bookblock,\n.flipbook-page-html .bb-item {\n  width: 100% !important;\n  height: 100% !important;\n  margin: 0 !important;\n  padding: 0 !important;\n}\n\n/* Fix the two-column layout */\n.flipbook-page-html .custom_side_wrapper {\n  display: flex !important;\n  height: 100% !important;\n}\n\n.flipbook-page-html .bb-custom-side {\n  flex: 1 !important;\n  background: white !important;\n  margin: clamp(5px, 1vw, 15px) !important;\n  padding: clamp(20px, 3vw, 40px) !important;\n  min-width: 0 !important;\n  overflow: visible !important;\n  box-sizing: border-box !important;\n}\n\n/* Fix font rendering and scaling */\n.flipbook-page-html [style*=\"AvenirLTStd-Book\"] {\n  font-family: 'Avenir', 'Helvetica Neue', Arial, sans-serif !important;\n}\n\n.flipbook-page-html [style*=\"GillSansMT-Bold\"] {\n  font-family: 'Gill Sans', 'Helvetica Neue', Arial, sans-serif !important;\n  font-weight: bold !important;\n}\n\n.flipbook-page-html [style*=\"MillerText-Roman\"] {\n  font-family: 'Times New Roman', Times, serif !important;\n}\n\n.flipbook-page-html [style*=\"PalatinoLTStd\"] {\n  font-family: 'Palatino', 'Times New Roman', serif !important;\n}\n\n.flipbook-page-html [style*=\"ACaslonPro-Regular\"] {\n  font-family: 'Adobe Caslon Pro', 'Times New Roman', serif !important;\n}\n\n/* Ensure text is fully visible and properly sized */\n.flipbook-page-html p,\n.flipbook-page-html div,\n.flipbook-page-html span {\n  line-height: 1.4 !important;\n  word-wrap: break-word !important;\n  overflow-wrap: break-word !important;\n}\n\n/* Override ALL inline styles that prevent responsive sizing */\n.flipbook-page-html .flip_wrapper,\n.flipbook-page-html div[style*=\"width: 600px\"],\n.flipbook-page-html div[style*=\"height: 579px\"],\n.flipbook-page-html [style*=\"width: 736px\"],\n.flipbook-page-html [style*=\"height: 576px\"] {\n  width: min(90vw, 1200px) !important;\n  height: min(80vh, 900px) !important;\n  max-width: none !important;\n  max-height: none !important;\n}\n\n/* Specific overrides for embedded inline styles */\n.flipbook-page-html [style] {\n  max-width: 100% !important;\n}\n\n/* Fix column layouts and text overflow */\n.flipbook-page-html .left-column-right,\n.flipbook-page-html .right-column-right,\n.flipbook-page-html .bb-custom-side {\n  flex: 1 1 45% !important;\n  min-width: 0 !important;\n  overflow: visible !important;\n  word-wrap: break-word !important;\n  box-sizing: border-box !important;\n}\n\n/* Ensure text content doesn't overflow */\n.flipbook-page-html .content_wrap,\n.flipbook-page-html .content_wrap_in,\n.flipbook-page-html .left_sec {\n  width: 100% !important;\n  max-width: 100% !important;\n  overflow: visible !important;\n  box-sizing: border-box !important;\n}\n\n/* Responsive text scaling for larger flipbook */\n.flipbook-page-html [style*=\"font-size: 23pt\"] {\n  font-size: clamp(28pt, 4vw, 36pt) !important;\n}\n\n.flipbook-page-html [style*=\"font-size: 22pt\"] {\n  font-size: clamp(26pt, 3.5vw, 34pt) !important;\n}\n\n.flipbook-page-html [style*=\"font-size: 16pt\"] {\n  font-size: clamp(18pt, 2.5vw, 24pt) !important;\n}\n\n.flipbook-page-html [style*=\"font-size: 10pt\"] {\n  font-size: clamp(12pt, 1.5vw, 16pt) !important;\n}\n\n.flipbook-page-html [style*=\"font-size: 9pt\"] {\n  font-size: clamp(11pt, 1.3vw, 14pt) !important;\n}\n\n.flipbook-page-html [style*=\"font-size: 8.25pt\"] {\n  font-size: clamp(10pt, 1.2vw, 13pt) !important;\n}\n\n.flipbook-page-html [style*=\"font-size: 12pt\"] {\n  font-size: clamp(14pt, 1.8vw, 18pt) !important;\n}\n\n/* Fix any overflow issues and ensure proper text display */\n.flipbook-page-html * {\n  box-sizing: border-box !important;\n}\n\n.flipbook-page-html div,\n.flipbook-page-html span,\n.flipbook-page-html p {\n  overflow: visible !important;\n  text-overflow: visible !important;\n  white-space: normal !important;\n  word-wrap: break-word !important;\n}\n\n.flipbook-page-html .page-number {\n  position: absolute;\n  bottom: 10px;\n  right: 20px;\n  font-size: 12px;\n  color: #666;\n  background: rgba(255, 255, 255, 0.8);\n  padding: 2px 6px;\n  border-radius: 3px;\n  z-index: 100;\n}\n\n/* Page Loading */\n.page-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 400px;\n  color: #666;\n}\n\n.page-loading .loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #f3f3f3;\n  border-top: 4px solid #667eea;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 15px;\n}\n\n.page-loading p {\n  font-size: 14px;\n  margin: 0;\n}\n", "/* Flipbook<PERSON>iewer Styles */\n\n.flipbook-viewer {\n  width: 100vw;\n  height: 100vh;\n  background: rgba(0, 0, 0, 0.9);\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.flipbook-viewer-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100vh;\n  background: #f5f5f5;\n  color: #666;\n}\n\n.flipbook-viewer-loading .loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 3px solid #e0e0e0;\n  border-top: 3px solid #8B5DBA;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 20px;\n}\n\n.flipbook-viewer-error {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100vh;\n  background: #f5f5f5;\n  color: #666;\n  text-align: center;\n  padding: 40px;\n}\n\n.flipbook-viewer-error h2 {\n  color: #333;\n  margin-bottom: 20px;\n  font-size: 24px;\n}\n\n.flipbook-viewer-error p {\n  margin-bottom: 30px;\n  font-size: 16px;\n  max-width: 400px;\n}\n\n.flipbook-viewer-error .btn {\n  padding: 12px 24px;\n  background: #8B5DBA;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: background 0.3s ease;\n}\n\n.flipbook-viewer-error .btn:hover {\n  background: #7A4BA8;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n", "/* NotFound Styles */\n\n.not-found {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n  padding: 40px 20px;\n}\n\n.not-found-content {\n  text-align: center;\n  max-width: 600px;\n  width: 100%;\n  background: white;\n  padding: 60px 40px;\n  border-radius: 12px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n}\n\n.error-code {\n  font-size: 120px;\n  font-weight: 900;\n  color: #8B5DBA;\n  line-height: 1;\n  margin-bottom: 20px;\n  text-shadow: 2px 2px 4px rgba(139, 93, 186, 0.2);\n}\n\n.not-found-content h1 {\n  font-size: 32px;\n  color: #333;\n  margin-bottom: 20px;\n  font-weight: 700;\n}\n\n.not-found-content p {\n  font-size: 16px;\n  color: #666;\n  line-height: 1.6;\n  margin-bottom: 40px;\n  max-width: 400px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.not-found-actions {\n  display: flex;\n  gap: 16px;\n  justify-content: center;\n  margin-bottom: 50px;\n  flex-wrap: wrap;\n}\n\n.helpful-links {\n  border-top: 1px solid #e0e0e0;\n  padding-top: 30px;\n}\n\n.helpful-links h3 {\n  font-size: 18px;\n  color: #333;\n  margin-bottom: 20px;\n  font-weight: 600;\n}\n\n.helpful-links ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n  display: flex;\n  justify-content: center;\n  gap: 30px;\n  flex-wrap: wrap;\n}\n\n.helpful-links li {\n  margin: 0;\n}\n\n.helpful-links a {\n  color: #8B5DBA;\n  text-decoration: none;\n  font-weight: 500;\n  transition: color 0.3s ease;\n  padding: 8px 12px;\n  border-radius: 4px;\n  display: inline-block;\n}\n\n.helpful-links a:hover {\n  color: #7A4BA8;\n  background: rgba(139, 93, 186, 0.1);\n}\n\n/* Button Styles */\n.btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 140px;\n}\n\n.btn.btn-primary {\n  background: #8B5DBA;\n  color: white;\n}\n\n.btn.btn-primary:hover {\n  background: #7A4BA8;\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(139, 93, 186, 0.3);\n}\n\n.btn.btn-secondary {\n  background: #6c757d;\n  color: white;\n}\n\n.btn.btn-secondary:hover {\n  background: #5a6268;\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .not-found-content {\n    padding: 40px 20px;\n  }\n  \n  .error-code {\n    font-size: 80px;\n  }\n  \n  .not-found-content h1 {\n    font-size: 24px;\n  }\n  \n  .not-found-actions {\n    flex-direction: column;\n    align-items: center;\n  }\n  \n  .helpful-links ul {\n    flex-direction: column;\n    gap: 15px;\n  }\n}\n", "/* Global CSS Variables for consistent theming */\n\n:root {\n  /* Primary Colors */\n  --primary-purple: #8B5DBA;\n  --primary-purple-hover: #7A4BA8;\n  --primary-purple-light: #a5a2cd;\n  --primary-purple-dark: #6D4A9B;\n\n  /* Secondary Colors */\n  --secondary-gray: #f5f5f5;\n  --secondary-gray-dark: #e2e8f0;\n  --secondary-gray-light: #f8fafc;\n\n  /* Text Colors */\n  --text-primary: #1e293b;\n  --text-secondary: #64748b;\n  --text-muted: #94a3b8;\n  --text-white: #ffffff;\n\n  /* Background Colors */\n  --bg-primary: #ffffff;\n  --bg-secondary: #f8fafc;\n  --bg-tertiary: #f1f5f9;\n  --bg-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  --bg-purple-gradient: linear-gradient(135deg, #a5a2cd 0%, #9f7db3 100%);\n\n  /* Border Colors */\n  --border-light: #e2e8f0;\n  --border-medium: #cbd5e1;\n  --border-dark: #94a3b8;\n\n  /* Shadow Colors */\n  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);\n  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);\n  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);\n  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);\n\n  /* Typography */\n  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n  --font-secondary: 'Gill Sans', 'Helvetica Neue', Arial, sans-serif;\n  --font-serif: 'Times New Roman', Times, serif;\n\n  /* Font Sizes */\n  --text-xs: 12px;\n  --text-sm: 14px;\n  --text-base: 16px;\n  --text-lg: 18px;\n  --text-xl: 20px;\n  --text-2xl: 24px;\n  --text-3xl: 30px;\n  --text-4xl: 36px;\n\n  /* Spacing */\n  --spacing-xs: 4px;\n  --spacing-sm: 8px;\n  --spacing-md: 16px;\n  --spacing-lg: 24px;\n  --spacing-xl: 32px;\n  --spacing-2xl: 48px;\n\n  /* Border Radius */\n  --radius-sm: 4px;\n  --radius-md: 6px;\n  --radius-lg: 8px;\n  --radius-xl: 12px;\n\n  /* Transitions */\n  --transition-fast: 0.15s ease;\n  --transition-normal: 0.3s ease;\n  --transition-slow: 0.5s ease;\n\n  /* Z-Index Scale */\n  --z-dropdown: 1000;\n  --z-sticky: 1020;\n  --z-fixed: 1030;\n  --z-modal-backdrop: 1040;\n  --z-modal: 1050;\n  --z-popover: 1060;\n  --z-tooltip: 1070;\n  --z-toast: 1080;\n}\n\n/* Architecture-specific variables for flipbook content */\n:root {\n  --flipbook-bg: rgb(210, 196, 177);\n  --flipbook-content-bg: #ffffff;\n  --flipbook-border: #000000;\n  --flipbook-page-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n  \n  /* Flipbook dimensions */\n  --flipbook-width: 600px;\n  --flipbook-height: 579px;\n  --flipbook-border-width: 11px;\n}\n\n/* Utility classes */\n.text-primary { color: var(--text-primary); }\n.text-secondary { color: var(--text-secondary); }\n.text-muted { color: var(--text-muted); }\n.text-white { color: var(--text-white); }\n\n.bg-primary { background-color: var(--bg-primary); }\n.bg-secondary { background-color: var(--bg-secondary); }\n.bg-tertiary { background-color: var(--bg-tertiary); }\n\n.shadow-sm { box-shadow: var(--shadow-sm); }\n.shadow-md { box-shadow: var(--shadow-md); }\n.shadow-lg { box-shadow: var(--shadow-lg); }\n.shadow-xl { box-shadow: var(--shadow-xl); }\n\n.rounded-sm { border-radius: var(--radius-sm); }\n.rounded-md { border-radius: var(--radius-md); }\n.rounded-lg { border-radius: var(--radius-lg); }\n.rounded-xl { border-radius: var(--radius-xl); }\n\n.transition-fast { transition: all var(--transition-fast); }\n.transition-normal { transition: all var(--transition-normal); }\n.transition-slow { transition: all var(--transition-slow); }\n", "/* Import CSS Variables */\n@import './styles/variables.css';\n\n/* Global App Styles */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n/* Ensure all text inputs use left-to-right direction */\ninput[type=\"text\"],\ninput[type=\"email\"],\ninput[type=\"password\"],\ninput[type=\"tel\"],\ninput[type=\"url\"],\ntextarea,\n[contenteditable] {\n  direction: ltr;\n  text-align: left;\n}\n\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f8fafc;\n  color: #334155;\n  line-height: 1.6;\n}\n\n.App {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n}\n\n.app-body {\n  display: flex;\n  flex: 1;\n  position: relative;\n}\n\n.App-logo {\n  height: 40vmin;\n  pointer-events: none;\n}\n\n@media (prefers-reduced-motion: no-preference) {\n  .App-logo {\n    animation: App-logo-spin infinite 20s linear;\n  }\n}\n\n.App-header {\n  background-color: #282c34;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  font-size: calc(10px + 2vmin);\n  color: white;\n}\n\n.App-link {\n  color: #61dafb;\n}\n\n@keyframes App-logo-spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* App Loading State */\n.app-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  text-align: center;\n}\n\n.loading-spinner {\n  width: 60px;\n  height: 60px;\n  border: 6px solid rgba(255, 255, 255, 0.3);\n  border-top: 6px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 30px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.app-loading p {\n  font-size: 18px;\n  font-weight: 600;\n  margin: 0;\n  letter-spacing: 0.5px;\n}\n\n/* App Error State */\n.app-error {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #ff6a6a 0%, #ee0979 100%);\n  color: white;\n  text-align: center;\n  padding: 40px;\n}\n\n.app-error h2 {\n  font-size: 32px;\n  font-weight: 700;\n  margin-bottom: 20px;\n  letter-spacing: 1px;\n}\n\n.app-error p {\n  font-size: 18px;\n  margin-bottom: 30px;\n  max-width: 600px;\n  line-height: 1.6;\n}\n\n.app-error button {\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  border: 2px solid white;\n  padding: 15px 30px;\n  border-radius: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  letter-spacing: 0.5px;\n}\n\n.app-error button:hover {\n  background: white;\n  color: #ee0979;\n  transform: translateY(-2px);\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n}\n\n/* UI Messages */\n.ui-messages {\n  position: fixed;\n  top: 80px;\n  right: 20px;\n  z-index: 1000;\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n  max-width: 400px;\n}\n\n.ui-message {\n  padding: 15px 20px;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  animation: slideInRight 0.3s ease-out;\n  position: relative;\n}\n\n.ui-message.info {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.ui-message.announcement {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n  color: white;\n}\n\n.ui-message.warning {\n  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);\n  color: #8b4513;\n}\n\n.ui-message.error {\n  background: linear-gradient(135deg, #ff6a6a 0%, #ee0979 100%);\n  color: white;\n}\n\n@keyframes slideInRight {\n  from {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n/* Responsive UI Messages */\n@media (max-width: 768px) {\n  .ui-messages {\n    top: 70px;\n    right: 10px;\n    left: 10px;\n    max-width: none;\n  }\n  \n  .ui-message {\n    padding: 12px 16px;\n    font-size: 13px;\n  }\n}\n"], "names": [], "sourceRoot": ""}