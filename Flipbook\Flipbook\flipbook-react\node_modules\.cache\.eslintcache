[{"D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\index.tsx": "1", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\reportWebVitals.ts": "2", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\App.tsx": "3", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\Header\\Header.tsx": "4", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\Sidebar\\Sidebar.tsx": "5", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\FlipbookGrid\\FlipbookGrid.tsx": "6", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\FlipbookPreview\\FlipbookPreview.tsx": "7", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\hooks\\useFlipbooks.ts": "8", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\hooks\\useAuth.ts": "9", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\services\\api.service.ts": "10", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\services\\mockApi.service.ts": "11", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\hooks\\useInitialAppData.ts": "12", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\constants\\flipbook.constants.ts": "13", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\assets\\images\\index.ts": "14", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\Router\\AppRouter.tsx": "15", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\FlipbookEditor\\FlipbookEditor.tsx": "16", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\NotFound\\NotFound.tsx": "17", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\FlipbookViewer\\FlipbookViewer.tsx": "18", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\Dashboard\\Dashboard.tsx": "19", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\ResumePage\\ResumePage.tsx": "20", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\FrontPage\\FrontPage.tsx": "21", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\ContactDetails\\ContactDetails.tsx": "22", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\ImageLibrary\\ImageLibrary.tsx": "23", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\TextEditor\\TextEditor.tsx": "24", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\NameFlipbook\\NameFlipbook.tsx": "25", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\AddPageModal\\AddPageModal.tsx": "26", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\TextToolbar\\TextToolbar.tsx": "27", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\LandingPage\\LandingPage.tsx": "28", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\LandingPage\\Sidebar.tsx": "29", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\LandingPage\\Header.tsx": "30", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\LandingPage\\CreateFlipbookModal.tsx": "31", "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\LandingPage\\FlipbookGrid.tsx": "32"}, {"size": 554, "mtime": 1758867107376, "results": "33", "hashOfConfig": "34"}, {"size": 425, "mtime": 1758867106495, "results": "35", "hashOfConfig": "34"}, {"size": 216, "mtime": 1758877452958, "results": "36", "hashOfConfig": "34"}, {"size": 7961, "mtime": 1758869571108, "results": "37", "hashOfConfig": "34"}, {"size": 2456, "mtime": 1758879022533, "results": "38", "hashOfConfig": "34"}, {"size": 6982, "mtime": 1759135637104, "results": "39", "hashOfConfig": "34"}, {"size": 41733, "mtime": 1759297396154, "results": "40", "hashOfConfig": "34"}, {"size": 7484, "mtime": 1759300560037, "results": "41", "hashOfConfig": "34"}, {"size": 4139, "mtime": 1759300671441, "results": "42", "hashOfConfig": "34"}, {"size": 6885, "mtime": 1759300402194, "results": "43", "hashOfConfig": "34"}, {"size": 8394, "mtime": 1758878781882, "results": "44", "hashOfConfig": "34"}, {"size": 4173, "mtime": 1758873662915, "results": "45", "hashOfConfig": "34"}, {"size": 1393, "mtime": 1758876230184, "results": "46", "hashOfConfig": "34"}, {"size": 3458, "mtime": 1758875976401, "results": "47", "hashOfConfig": "34"}, {"size": 2357, "mtime": 1759298494980, "results": "48", "hashOfConfig": "34"}, {"size": 23951, "mtime": 1759300893987, "results": "49", "hashOfConfig": "34"}, {"size": 1217, "mtime": 1758877395583, "results": "50", "hashOfConfig": "34"}, {"size": 2645, "mtime": 1758878797593, "results": "51", "hashOfConfig": "34"}, {"size": 1662, "mtime": 1758877559986, "results": "52", "hashOfConfig": "34"}, {"size": 15140, "mtime": 1759226475080, "results": "53", "hashOfConfig": "34"}, {"size": 3245, "mtime": 1758897970549, "results": "54", "hashOfConfig": "34"}, {"size": 7522, "mtime": 1758897642391, "results": "55", "hashOfConfig": "34"}, {"size": 6046, "mtime": 1758893705318, "results": "56", "hashOfConfig": "34"}, {"size": 8881, "mtime": 1759216460475, "results": "57", "hashOfConfig": "34"}, {"size": 1917, "mtime": 1759135278483, "results": "58", "hashOfConfig": "34"}, {"size": 12025, "mtime": 1759145268907, "results": "59", "hashOfConfig": "34"}, {"size": 10413, "mtime": 1759216422922, "results": "60", "hashOfConfig": "34"}, {"size": 4598, "mtime": 1759302183190, "results": "61", "hashOfConfig": "34"}, {"size": 3088, "mtime": 1759298381586, "results": "62", "hashOfConfig": "34"}, {"size": 3708, "mtime": 1759300802171, "results": "63", "hashOfConfig": "34"}, {"size": 5973, "mtime": 1759298441781, "results": "64", "hashOfConfig": "34"}, {"size": 4949, "mtime": 1759301603006, "results": "65", "hashOfConfig": "34"}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gbbm9e", {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\index.tsx", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\reportWebVitals.ts", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\App.tsx", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\Header\\Header.tsx", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\Sidebar\\Sidebar.tsx", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\FlipbookGrid\\FlipbookGrid.tsx", ["162", "163"], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\FlipbookPreview\\FlipbookPreview.tsx", ["164", "165", "166", "167"], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\hooks\\useFlipbooks.ts", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\hooks\\useAuth.ts", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\services\\api.service.ts", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\services\\mockApi.service.ts", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\hooks\\useInitialAppData.ts", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\constants\\flipbook.constants.ts", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\assets\\images\\index.ts", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\Router\\AppRouter.tsx", ["168"], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\FlipbookEditor\\FlipbookEditor.tsx", ["169", "170", "171", "172", "173", "174", "175", "176", "177", "178"], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\NotFound\\NotFound.tsx", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\FlipbookViewer\\FlipbookViewer.tsx", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\Dashboard\\Dashboard.tsx", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\ResumePage\\ResumePage.tsx", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\FrontPage\\FrontPage.tsx", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\ContactDetails\\ContactDetails.tsx", ["179", "180"], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\ImageLibrary\\ImageLibrary.tsx", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\TextEditor\\TextEditor.tsx", ["181", "182", "183", "184"], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\NameFlipbook\\NameFlipbook.tsx", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\AddPageModal\\AddPageModal.tsx", ["185"], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\TextToolbar\\TextToolbar.tsx", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\LandingPage\\LandingPage.tsx", ["186", "187", "188", "189"], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\LandingPage\\Sidebar.tsx", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\LandingPage\\Header.tsx", ["190"], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\LandingPage\\CreateFlipbookModal.tsx", [], [], "D:\\TradeWorks\\Flipbook\\Flipbook\\Flipbook\\flipbook-react\\src\\components\\LandingPage\\FlipbookGrid.tsx", [], [], {"ruleId": "191", "severity": 1, "message": "192", "line": 22, "column": 5, "nodeType": "193", "messageId": "194", "endLine": 22, "endColumn": 19}, {"ruleId": "191", "severity": 1, "message": "195", "line": 24, "column": 5, "nodeType": "193", "messageId": "194", "endLine": 24, "endColumn": 19}, {"ruleId": "191", "severity": 1, "message": "196", "line": 2, "column": 10, "nodeType": "193", "messageId": "194", "endLine": 2, "endColumn": 22}, {"ruleId": "191", "severity": 1, "message": "197", "line": 2, "column": 24, "nodeType": "193", "messageId": "194", "endLine": 2, "endColumn": 40}, {"ruleId": "191", "severity": 1, "message": "198", "line": 3, "column": 10, "nodeType": "193", "messageId": "194", "endLine": 3, "endColumn": 26}, {"ruleId": "199", "severity": 1, "message": "200", "line": 652, "column": 6, "nodeType": "201", "endLine": 652, "endColumn": 47, "suggestions": "202"}, {"ruleId": "191", "severity": 1, "message": "203", "line": 4, "column": 8, "nodeType": "193", "messageId": "194", "endLine": 4, "endColumn": 17}, {"ruleId": "191", "severity": 1, "message": "204", "line": 3, "column": 8, "nodeType": "193", "messageId": "194", "endLine": 3, "endColumn": 14}, {"ruleId": "191", "severity": 1, "message": "205", "line": 42, "column": 11, "nodeType": "193", "messageId": "194", "endLine": 42, "endColumn": 15}, {"ruleId": "191", "severity": 1, "message": "206", "line": 48, "column": 10, "nodeType": "193", "messageId": "194", "endLine": 48, "endColumn": 23}, {"ruleId": "191", "severity": 1, "message": "207", "line": 49, "column": 10, "nodeType": "193", "messageId": "194", "endLine": 49, "endColumn": 18}, {"ruleId": "191", "severity": 1, "message": "208", "line": 50, "column": 10, "nodeType": "193", "messageId": "194", "endLine": 50, "endColumn": 19}, {"ruleId": "191", "severity": 1, "message": "209", "line": 74, "column": 10, "nodeType": "193", "messageId": "194", "endLine": 74, "endColumn": 18}, {"ruleId": "191", "severity": 1, "message": "210", "line": 128, "column": 9, "nodeType": "193", "messageId": "194", "endLine": 128, "endColumn": 19}, {"ruleId": "191", "severity": 1, "message": "211", "line": 153, "column": 9, "nodeType": "193", "messageId": "194", "endLine": 153, "endColumn": 24}, {"ruleId": "191", "severity": 1, "message": "212", "line": 160, "column": 9, "nodeType": "193", "messageId": "194", "endLine": 160, "endColumn": 22}, {"ruleId": "191", "severity": 1, "message": "213", "line": 166, "column": 9, "nodeType": "193", "messageId": "194", "endLine": 166, "endColumn": 20}, {"ruleId": "214", "severity": 1, "message": "215", "line": 44, "column": 29, "nodeType": "216", "messageId": "217", "endLine": 44, "endColumn": 30, "suggestions": "218"}, {"ruleId": "214", "severity": 1, "message": "219", "line": 44, "column": 31, "nodeType": "216", "messageId": "217", "endLine": 44, "endColumn": 32, "suggestions": "220"}, {"ruleId": "191", "severity": 1, "message": "221", "line": 19, "column": 11, "nodeType": "193", "messageId": "194", "endLine": 19, "endColumn": 21}, {"ruleId": "199", "severity": 1, "message": "222", "line": 129, "column": 6, "nodeType": "201", "endLine": 129, "endColumn": 16, "suggestions": "223"}, {"ruleId": "191", "severity": 1, "message": "224", "line": 132, "column": 11, "nodeType": "193", "messageId": "194", "endLine": 132, "endColumn": 18}, {"ruleId": "199", "severity": 1, "message": "225", "line": 226, "column": 6, "nodeType": "201", "endLine": 226, "endColumn": 35, "suggestions": "226"}, {"ruleId": "191", "severity": 1, "message": "227", "line": 41, "column": 9, "nodeType": "193", "messageId": "194", "endLine": 41, "endColumn": 29}, {"ruleId": "191", "severity": 1, "message": "205", "line": 70, "column": 11, "nodeType": "193", "messageId": "194", "endLine": 70, "endColumn": 15}, {"ruleId": "191", "severity": 1, "message": "228", "line": 70, "column": 17, "nodeType": "193", "messageId": "194", "endLine": 70, "endColumn": 32}, {"ruleId": "191", "severity": 1, "message": "229", "line": 74, "column": 5, "nodeType": "193", "messageId": "194", "endLine": 74, "endColumn": 12}, {"ruleId": "191", "severity": 1, "message": "230", "line": 75, "column": 5, "nodeType": "193", "messageId": "194", "endLine": 75, "endColumn": 10}, {"ruleId": "191", "severity": 1, "message": "231", "line": 123, "column": 10, "nodeType": "193", "messageId": "194", "endLine": 123, "endColumn": 25}, "@typescript-eslint/no-unused-vars", "'createFlipbook' is assigned a value but never used.", "Identifier", "unusedVar", "'deleteFlipbook' is assigned a value but never used.", "'getImageById' is defined but never used.", "'resolveImagePath' is defined but never used.", "'handleImageError' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadPageContent'. Either include it or remove the dependency array.", "ArrayExpression", ["232"], "'Dashboard' is defined but never used.", "'Header' is defined but never used.", "'user' is assigned a value but never used.", "'isNewFlipbook' is assigned a value but never used.", "'isSaving' is assigned a value but never used.", "'lastSaved' is assigned a value but never used.", "'isTyping' is assigned a value but never used.", "'handleSave' is assigned a value but never used.", "'handleTitleEdit' is assigned a value but never used.", "'handlePreview' is assigned a value but never used.", "'handleClose' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\(.", "Literal", "unnecessaryEscape", ["233", "234"], "Unnecessary escape character: \\).", ["235", "236"], "'TextFormat' is defined but never used.", "React Hook useEffect has a missing dependency: 'applyCommand'. Either include it or remove the dependency array.", ["237"], "'content' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleSelectionChange'. Either include it or remove the dependency array.", ["238"], "'handleTemplateSelect' is assigned a value but never used.", "'isAuthenticated' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'showSignInModal' is assigned a value but never used.", {"desc": "239", "fix": "240"}, {"messageId": "241", "fix": "242", "desc": "243"}, {"messageId": "244", "fix": "245", "desc": "246"}, {"messageId": "241", "fix": "247", "desc": "243"}, {"messageId": "244", "fix": "248", "desc": "246"}, {"desc": "249", "fix": "250"}, {"desc": "251", "fix": "252"}, "Update the dependencies array to be: [currentPage, portfolioId, isInspiration, loadPageContent]", {"range": "253", "text": "254"}, "removeEscape", {"range": "255", "text": "256"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "257", "text": "258"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "259", "text": "256"}, {"range": "260", "text": "258"}, "Update the dependencies array to be: [applyCommand, isActive]", {"range": "261", "text": "262"}, "Update the dependencies array to be: [handleSelectionChange, isActive, pendingFormatting]", {"range": "263", "text": "264"}, [34842, 34883], "[currentPage, portfolioId, isInspiration, loadPageContent]", [1058, 1059], "", [1058, 1058], "\\", [1060, 1061], [1060, 1060], [3622, 3632], "[applyCommand, isActive]", [7214, 7243], "[handleSelectionChange, isActive, pendingFormatting]"]