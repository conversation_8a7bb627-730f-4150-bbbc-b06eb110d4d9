/*! For license information please see main.613643c8.js.LICENSE.txt */
(()=>{var e={219:(e,t,n)=>{"use strict";var r=n(3763),a={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},o={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},l={};function s(e){return r.isMemo(e)?o:l[e.$$typeof]||a}l[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},l[r.Memo]=o;var c=Object.defineProperty,u=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,m=Object.prototype;e.exports=function e(t,n,r){if("string"!==typeof n){if(m){var a=p(n);a&&a!==m&&e(t,a,r)}var o=u(n);d&&(o=o.concat(d(n)));for(var l=s(t),h=s(n),g=0;g<o.length;++g){var v=o[g];if(!i[v]&&(!r||!r[v])&&(!h||!h[v])&&(!l||!l[v])){var b=f(n,v);try{c(t,v,b)}catch(y){}}}}return t}},579:(e,t,n)=>{"use strict";e.exports=n(1153)},1153:(e,t,n)=>{"use strict";var r=n(5043),a=Symbol.for("react.element"),i=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,i={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)o.call(t,r)&&!s.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:a,type:e,key:c,ref:u,props:i,_owner:l.current}}t.Fragment=i,t.jsx=c,t.jsxs=c},2086:(e,t,n)=>{"use strict";e.exports=n(5082)},2398:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>r});const r={architecture:{linkPic:n.p+"static/media/LinkPic.dfccf0dbc8fc177e1fb1.jpg",grimshawBeyerBlinder:n.p+"static/media/Grimshaw-and-Beyer-Blinder.d154de622db385eeb27e.jpg",peterAaron:n.p+"static/media/PeterAaron.1700eab2a9be4a850d90.jpg",mcMillen:n.p+"static/media/McMillen.cf56c6b9d1cb33de1503.png",quincy:n.p+"static/media/Quincy.f43aa2e7c9d4f890575a.png",overlook:n.p+"static/media/Overlook.368cee555a0dba7e92b0.png",kellerCenter:n.p+"static/media/Keller_Center.6a1cfd5c12f514224393.png",shingleHouse:n.p+"static/media/Shingle_House.d611b53f38fcb3cbb656.png",fredArchitect:n.p+"static/media/fred_architect.3595e6235db521c5cc21.png",fredNicolaus:n.p+"static/media/fred_nicolaus.76baa4bffef73828f477.png",frederichNicolaus:n.p+"static/media/frederich_nicolaus.d5474745752e48e2930f.png"},resumeInspiration:{portfolio2:{page1:n.p+"static/media/1.6bd231255e386eb80337.png",page2:n.p+"static/media/2.f8298fe28d1e84281bbd.jpg",page3:n.p+"static/media/3.d5ebba03d3c89393ef9c.jpg",page4:n.p+"static/media/4.939a39b4a7fd1131e0ac.jpg"}},endPapers:{basic:n.p+"static/media/EndPaper 03.2165d5b3ae056fc9fbc6.png",big01:n.p+"static/media/EndPaper-big-01.0d8bf6d4093ff37a32bb.png",big02:n.p+"static/media/EndPaper-big-02.4aa0236c62ca3b196edd.png",big03:n.p+"static/media/EndPaper-big-03.bae4fe7b8dd2ef9a86f6.png",big04:n.p+"static/media/EndPaper-big-04.e950277fc9ff3463ab1a.png",big05:n.p+"static/media/EndPaper-big-05.203d74634010b55def2c.png",big06:n.p+"static/media/EndPaper-big-06.66d273a46129336103fd.png",big07:n.p+"static/media/EndPaper-big-07.e233988bd2bba09d8d13.png",big08:n.p+"static/media/EndPaper-big-08.fdd8ce8bbfd7868aec16.png",big09:n.p+"static/media/EndPaper-big-09.351fb6096944fb50a0be.png",big10:n.p+"static/media/EndPaper-big-10.6348478a256e5a7b72d3.png",big11:n.p+"static/media/EndPaper-big-11.9b4648b8bd5bec215019.png",big12:n.p+"static/media/EndPaper-big-12.ee29a0b4c2f79a3e1272.png"},flipbook:{thankyou:n.p+"static/media/flipbook-thankyou.910f829a7adacd7beaef.png",blankImg:n.p+"static/media/Blank-Img.031020345a93f796469b.jpg"},logos:{flipbookLogo:n.p+"static/media/flipbook_logo.135a6731fd394b643fe1.jpg",flipbookCombineLogo:"data:image/jpeg;base64,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",tradeWorksLogo:"data:image/png;base64,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",twLogo:"data:image/png;base64,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"}}},2555:(e,t,n)=>{"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function a(e){var t=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!=r(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==r(t)?t:t+""}function i(e,t,n){return(t=a(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach(function(t){i(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}n.d(t,{A:()=>l})},2730:(e,t,n)=>{"use strict";var r=n(5043),a=n(8853);function i(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var o=new Set,l={};function s(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(l[e]=t,e=0;e<t.length;e++)o.add(t[e])}var u=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},m={};function h(e,t,n,r,a,i,o){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){g[e]=new h(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];g[t]=new h(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){g[e]=new h(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){g[e]=new h(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){g[e]=new h(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){g[e]=new h(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){g[e]=new h(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){g[e]=new h(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){g[e]=new h(e,5,!1,e.toLowerCase(),null,!1,!1)});var v=/[\-:]([a-z])/g;function b(e){return e[1].toUpperCase()}function y(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(m,e)||!d.call(p,e)&&(f.test(e)?m[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(v,b);g[t]=new h(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(v,b);g[t]=new h(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(v,b);g[t]=new h(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){g[e]=new h(e,1,!1,e.toLowerCase(),null,!1,!1)}),g.xlinkHref=new h("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){g[e]=new h(e,1,!1,e.toLowerCase(),null,!0,!0)});var x=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,k=Symbol.for("react.element"),w=Symbol.for("react.portal"),S=Symbol.for("react.fragment"),A=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),j=Symbol.for("react.provider"),N=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),E=Symbol.for("react.suspense"),T=Symbol.for("react.suspense_list"),I=Symbol.for("react.memo"),O=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var F=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var R=Symbol.iterator;function D(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=R&&e[R]||e["@@iterator"])?e:null}var L,z=Object.assign;function U(e){if(void 0===L)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);L=t&&t[1]||""}return"\n"+L+e}var M=!1;function _(e,t){if(!e||M)return"";M=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&"string"===typeof c.stack){for(var a=c.stack.split("\n"),i=r.stack.split("\n"),o=a.length-1,l=i.length-1;1<=o&&0<=l&&a[o]!==i[l];)l--;for(;1<=o&&0<=l;o--,l--)if(a[o]!==i[l]){if(1!==o||1!==l)do{if(o--,0>--l||a[o]!==i[l]){var s="\n"+a[o].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=o&&0<=l);break}}}finally{M=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?U(e):""}function B(e){switch(e.tag){case 5:return U(e.type);case 16:return U("Lazy");case 13:return U("Suspense");case 19:return U("SuspenseList");case 0:case 2:case 15:return e=_(e.type,!1);case 11:return e=_(e.type.render,!1);case 1:return e=_(e.type,!0);default:return""}}function H(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case S:return"Fragment";case w:return"Portal";case C:return"Profiler";case A:return"StrictMode";case E:return"Suspense";case T:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case N:return(e.displayName||"Context")+".Consumer";case j:return(e._context.displayName||"Context")+".Provider";case P:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case I:return null!==(t=e.displayName||null)?t:H(e.type)||"Memo";case O:t=e._payload,e=e._init;try{return H(e(t))}catch(n){}}return null}function W(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return H(t);case 8:return t===A?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function V(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function q(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function X(e){e._valueTracker||(e._valueTracker=function(e){var t=q(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,i.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=q(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function G(e,t){var n=t.checked;return z({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Y(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=V(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function J(e,t){null!=(t=t.checked)&&y(e,"checked",t,!1)}function Z(e,t){J(e,t);var n=V(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,V(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function $(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+V(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(i(91));return z({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(i(92));if(te(n)){if(1<n.length)throw Error(i(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:V(n)}}function ie(e,t){var n=V(t.value),r=V(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function oe(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function le(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?le(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,ue,de=(ue=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return ue(e,t)})}:ue);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},me=["Webkit","ms","Moz","O"];function he(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=he(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pe).forEach(function(e){me.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]})});var ve=z({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function be(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(i(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(i(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(i(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(i(62))}}function ye(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xe=null;function ke(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var we=null,Se=null,Ae=null;function Ce(e){if(e=ya(e)){if("function"!==typeof we)throw Error(i(280));var t=e.stateNode;t&&(t=ka(t),we(e.stateNode,e.type,t))}}function je(e){Se?Ae?Ae.push(e):Ae=[e]:Se=e}function Ne(){if(Se){var e=Se,t=Ae;if(Ae=Se=null,Ce(e),t)for(e=0;e<t.length;e++)Ce(t[e])}}function Pe(e,t){return e(t)}function Ee(){}var Te=!1;function Ie(e,t,n){if(Te)return e(t,n);Te=!0;try{return Pe(e,t,n)}finally{Te=!1,(null!==Se||null!==Ae)&&(Ee(),Ne())}}function Oe(e,t){var n=e.stateNode;if(null===n)return null;var r=ka(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(i(231,t,typeof n));return n}var Fe=!1;if(u)try{var Re={};Object.defineProperty(Re,"passive",{get:function(){Fe=!0}}),window.addEventListener("test",Re,Re),window.removeEventListener("test",Re,Re)}catch(ue){Fe=!1}function De(e,t,n,r,a,i,o,l,s){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var Le=!1,ze=null,Ue=!1,Me=null,_e={onError:function(e){Le=!0,ze=e}};function Be(e,t,n,r,a,i,o,l,s){Le=!1,ze=null,De.apply(_e,arguments)}function He(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function We(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Ve(e){if(He(e)!==e)throw Error(i(188))}function qe(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=He(e)))throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return Ve(a),e;if(o===r)return Ve(a),t;o=o.sibling}throw Error(i(188))}if(n.return!==r.return)n=a,r=o;else{for(var l=!1,s=a.child;s;){if(s===n){l=!0,n=a,r=o;break}if(s===r){l=!0,r=a,n=o;break}s=s.sibling}if(!l){for(s=o.child;s;){if(s===n){l=!0,n=o,r=a;break}if(s===r){l=!0,r=o,n=a;break}s=s.sibling}if(!l)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(3!==n.tag)throw Error(i(188));return n.stateNode.current===n?e:t}(e))?Xe(e):null}function Xe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Xe(e);if(null!==t)return t;e=e.sibling}return null}var Qe=a.unstable_scheduleCallback,Ke=a.unstable_cancelCallback,Ge=a.unstable_shouldYield,Ye=a.unstable_requestPaint,Je=a.unstable_now,Ze=a.unstable_getCurrentPriorityLevel,$e=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,it=null;var ot=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(lt(e)/st|0)|0},lt=Math.log,st=Math.LN2;var ct=64,ut=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,i=e.pingedLanes,o=268435455&n;if(0!==o){var l=o&~a;0!==l?r=dt(l):0!==(i&=o)&&(r=dt(i))}else 0!==(o=n&~a)?r=dt(o):0!==i&&(r=dt(i));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(i=t&-t)||16===a&&0!==(4194240&i)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-ot(t)),r|=e[n],t&=~a;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function mt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function ht(){var e=ct;return 0===(4194240&(ct<<=1))&&(ct=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-ot(t)]=n}function bt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ot(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var yt=0;function xt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var kt,wt,St,At,Ct,jt=!1,Nt=[],Pt=null,Et=null,Tt=null,It=new Map,Ot=new Map,Ft=[],Rt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Dt(e,t){switch(e){case"focusin":case"focusout":Pt=null;break;case"dragenter":case"dragleave":Et=null;break;case"mouseover":case"mouseout":Tt=null;break;case"pointerover":case"pointerout":It.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ot.delete(t.pointerId)}}function Lt(e,t,n,r,a,i){return null===e||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[a]},null!==t&&(null!==(t=ya(t))&&wt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function zt(e){var t=ba(e.target);if(null!==t){var n=He(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=We(n)))return e.blockedOn=t,void Ct(e.priority,function(){St(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Ut(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Gt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ya(n))&&wt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xe=r,n.target.dispatchEvent(r),xe=null,t.shift()}return!0}function Mt(e,t,n){Ut(e)&&n.delete(t)}function _t(){jt=!1,null!==Pt&&Ut(Pt)&&(Pt=null),null!==Et&&Ut(Et)&&(Et=null),null!==Tt&&Ut(Tt)&&(Tt=null),It.forEach(Mt),Ot.forEach(Mt)}function Bt(e,t){e.blockedOn===t&&(e.blockedOn=null,jt||(jt=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,_t)))}function Ht(e){function t(t){return Bt(t,e)}if(0<Nt.length){Bt(Nt[0],e);for(var n=1;n<Nt.length;n++){var r=Nt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Pt&&Bt(Pt,e),null!==Et&&Bt(Et,e),null!==Tt&&Bt(Tt,e),It.forEach(t),Ot.forEach(t),n=0;n<Ft.length;n++)(r=Ft[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Ft.length&&null===(n=Ft[0]).blockedOn;)zt(n),null===n.blockedOn&&Ft.shift()}var Wt=x.ReactCurrentBatchConfig,Vt=!0;function qt(e,t,n,r){var a=yt,i=Wt.transition;Wt.transition=null;try{yt=1,Qt(e,t,n,r)}finally{yt=a,Wt.transition=i}}function Xt(e,t,n,r){var a=yt,i=Wt.transition;Wt.transition=null;try{yt=4,Qt(e,t,n,r)}finally{yt=a,Wt.transition=i}}function Qt(e,t,n,r){if(Vt){var a=Gt(e,t,n,r);if(null===a)Vr(e,t,r,Kt,n),Dt(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Pt=Lt(Pt,e,t,n,r,a),!0;case"dragenter":return Et=Lt(Et,e,t,n,r,a),!0;case"mouseover":return Tt=Lt(Tt,e,t,n,r,a),!0;case"pointerover":var i=a.pointerId;return It.set(i,Lt(It.get(i)||null,e,t,n,r,a)),!0;case"gotpointercapture":return i=a.pointerId,Ot.set(i,Lt(Ot.get(i)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(Dt(e,r),4&t&&-1<Rt.indexOf(e)){for(;null!==a;){var i=ya(a);if(null!==i&&kt(i),null===(i=Gt(e,t,n,r))&&Vr(e,t,r,Kt,n),i===a)break;a=i}null!==a&&r.stopPropagation()}else Vr(e,t,r,null,n)}}var Kt=null;function Gt(e,t,n,r){if(Kt=null,null!==(e=ba(e=ke(r))))if(null===(t=He(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=We(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kt=e,null}function Yt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ze()){case $e:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Jt=null,Zt=null,$t=null;function en(){if($t)return $t;var e,t,n=Zt,r=n.length,a="value"in Jt?Jt.value:Jt.textContent,i=a.length;for(e=0;e<r&&n[e]===a[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===a[i-t];t++);return $t=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,i){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=i,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return z(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var on,ln,sn,cn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},un=an(cn),dn=z({},cn,{view:0,detail:0}),fn=an(dn),pn=z({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Cn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sn&&(sn&&"mousemove"===e.type?(on=e.screenX-sn.screenX,ln=e.screenY-sn.screenY):ln=on=0,sn=e),on)},movementY:function(e){return"movementY"in e?e.movementY:ln}}),mn=an(pn),hn=an(z({},pn,{dataTransfer:0})),gn=an(z({},dn,{relatedTarget:0})),vn=an(z({},cn,{animationName:0,elapsedTime:0,pseudoElement:0})),bn=z({},cn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),yn=an(bn),xn=an(z({},cn,{data:0})),kn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},wn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function An(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Sn[e])&&!!t[e]}function Cn(){return An}var jn=z({},dn,{key:function(e){if(e.key){var t=kn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?wn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Cn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Nn=an(jn),Pn=an(z({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),En=an(z({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Cn})),Tn=an(z({},cn,{propertyName:0,elapsedTime:0,pseudoElement:0})),In=z({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),On=an(In),Fn=[9,13,27,32],Rn=u&&"CompositionEvent"in window,Dn=null;u&&"documentMode"in document&&(Dn=document.documentMode);var Ln=u&&"TextEvent"in window&&!Dn,zn=u&&(!Rn||Dn&&8<Dn&&11>=Dn),Un=String.fromCharCode(32),Mn=!1;function _n(e,t){switch(e){case"keyup":return-1!==Fn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Hn=!1;var Wn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Vn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Wn[e.type]:"textarea"===t}function qn(e,t,n,r){je(r),0<(t=Xr(t,"onChange")).length&&(n=new un("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Xn=null,Qn=null;function Kn(e){Ur(e,0)}function Gn(e){if(Q(xa(e)))return e}function Yn(e,t){if("change"===e)return t}var Jn=!1;if(u){var Zn;if(u){var $n="oninput"in document;if(!$n){var er=document.createElement("div");er.setAttribute("oninput","return;"),$n="function"===typeof er.oninput}Zn=$n}else Zn=!1;Jn=Zn&&(!document.documentMode||9<document.documentMode)}function tr(){Xn&&(Xn.detachEvent("onpropertychange",nr),Qn=Xn=null)}function nr(e){if("value"===e.propertyName&&Gn(Qn)){var t=[];qn(t,Qn,e,ke(e)),Ie(Kn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Qn=n,(Xn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Gn(Qn)}function ir(e,t){if("click"===e)return Gn(t)}function or(e,t){if("input"===e||"change"===e)return Gn(t)}var lr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function sr(e,t){if(lr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!lr(e[a],t[a]))return!1}return!0}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ur(e,t){var n,r=cr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=cr(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function mr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,i=Math.min(r.start,a);r=void 0===r.end?i:Math.min(r.end,a),!e.extend&&i>r&&(a=r,r=i,i=a),a=ur(n,i);var o=ur(n,r);a&&o&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var hr=u&&"documentMode"in document&&11>=document.documentMode,gr=null,vr=null,br=null,yr=!1;function xr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;yr||null==gr||gr!==K(r)||("selectionStart"in(r=gr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},br&&sr(br,r)||(br=r,0<(r=Xr(vr,"onSelect")).length&&(t=new un("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function kr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var wr={animationend:kr("Animation","AnimationEnd"),animationiteration:kr("Animation","AnimationIteration"),animationstart:kr("Animation","AnimationStart"),transitionend:kr("Transition","TransitionEnd")},Sr={},Ar={};function Cr(e){if(Sr[e])return Sr[e];if(!wr[e])return e;var t,n=wr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Ar)return Sr[e]=n[t];return e}u&&(Ar=document.createElement("div").style,"AnimationEvent"in window||(delete wr.animationend.animation,delete wr.animationiteration.animation,delete wr.animationstart.animation),"TransitionEvent"in window||delete wr.transitionend.transition);var jr=Cr("animationend"),Nr=Cr("animationiteration"),Pr=Cr("animationstart"),Er=Cr("transitionend"),Tr=new Map,Ir="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Or(e,t){Tr.set(e,t),s(t,[e])}for(var Fr=0;Fr<Ir.length;Fr++){var Rr=Ir[Fr];Or(Rr.toLowerCase(),"on"+(Rr[0].toUpperCase()+Rr.slice(1)))}Or(jr,"onAnimationEnd"),Or(Nr,"onAnimationIteration"),Or(Pr,"onAnimationStart"),Or("dblclick","onDoubleClick"),Or("focusin","onFocus"),Or("focusout","onBlur"),Or(Er,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Dr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Lr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Dr));function zr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,o,l,s,c){if(Be.apply(this,arguments),Le){if(!Le)throw Error(i(198));var u=ze;Le=!1,ze=null,Ue||(Ue=!0,Me=u)}}(r,t,void 0,e),e.currentTarget=null}function Ur(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var l=r[o],s=l.instance,c=l.currentTarget;if(l=l.listener,s!==i&&a.isPropagationStopped())break e;zr(a,l,c),i=s}else for(o=0;o<r.length;o++){if(s=(l=r[o]).instance,c=l.currentTarget,l=l.listener,s!==i&&a.isPropagationStopped())break e;zr(a,l,c),i=s}}}if(Ue)throw e=Me,Ue=!1,Me=null,e}function Mr(e,t){var n=t[ha];void 0===n&&(n=t[ha]=new Set);var r=e+"__bubble";n.has(r)||(Wr(t,e,2,!1),n.add(r))}function _r(e,t,n){var r=0;t&&(r|=4),Wr(n,e,r,t)}var Br="_reactListening"+Math.random().toString(36).slice(2);function Hr(e){if(!e[Br]){e[Br]=!0,o.forEach(function(t){"selectionchange"!==t&&(Lr.has(t)||_r(t,!1,e),_r(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Br]||(t[Br]=!0,_r("selectionchange",!1,t))}}function Wr(e,t,n,r){switch(Yt(t)){case 1:var a=qt;break;case 4:a=Xt;break;default:a=Qt}n=a.bind(null,t,n,e),a=void 0,!Fe||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Vr(e,t,n,r,a){var i=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var l=r.stateNode.containerInfo;if(l===a||8===l.nodeType&&l.parentNode===a)break;if(4===o)for(o=r.return;null!==o;){var s=o.tag;if((3===s||4===s)&&((s=o.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;o=o.return}for(;null!==l;){if(null===(o=ba(l)))return;if(5===(s=o.tag)||6===s){r=i=o;continue e}l=l.parentNode}}r=r.return}Ie(function(){var r=i,a=ke(n),o=[];e:{var l=Tr.get(e);if(void 0!==l){var s=un,c=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":s=Nn;break;case"focusin":c="focus",s=gn;break;case"focusout":c="blur",s=gn;break;case"beforeblur":case"afterblur":s=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=mn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=hn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=En;break;case jr:case Nr:case Pr:s=vn;break;case Er:s=Tn;break;case"scroll":s=fn;break;case"wheel":s=On;break;case"copy":case"cut":case"paste":s=yn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=Pn}var u=0!==(4&t),d=!u&&"scroll"===e,f=u?null!==l?l+"Capture":null:l;u=[];for(var p,m=r;null!==m;){var h=(p=m).stateNode;if(5===p.tag&&null!==h&&(p=h,null!==f&&(null!=(h=Oe(m,f))&&u.push(qr(m,h,p)))),d)break;m=m.return}0<u.length&&(l=new s(l,c,null,n,a),o.push({event:l,listeners:u}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||n===xe||!(c=n.relatedTarget||n.fromElement)||!ba(c)&&!c[ma])&&(s||l)&&(l=a.window===a?a:(l=a.ownerDocument)?l.defaultView||l.parentWindow:window,s?(s=r,null!==(c=(c=n.relatedTarget||n.toElement)?ba(c):null)&&(c!==(d=He(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(s=null,c=r),s!==c)){if(u=mn,h="onMouseLeave",f="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(u=Pn,h="onPointerLeave",f="onPointerEnter",m="pointer"),d=null==s?l:xa(s),p=null==c?l:xa(c),(l=new u(h,m+"leave",s,n,a)).target=d,l.relatedTarget=p,h=null,ba(a)===r&&((u=new u(f,m+"enter",c,n,a)).target=p,u.relatedTarget=d,h=u),d=h,s&&c)e:{for(f=c,m=0,p=u=s;p;p=Qr(p))m++;for(p=0,h=f;h;h=Qr(h))p++;for(;0<m-p;)u=Qr(u),m--;for(;0<p-m;)f=Qr(f),p--;for(;m--;){if(u===f||null!==f&&u===f.alternate)break e;u=Qr(u),f=Qr(f)}u=null}else u=null;null!==s&&Kr(o,l,s,u,!1),null!==c&&null!==d&&Kr(o,d,c,u,!0)}if("select"===(s=(l=r?xa(r):window).nodeName&&l.nodeName.toLowerCase())||"input"===s&&"file"===l.type)var g=Yn;else if(Vn(l))if(Jn)g=or;else{g=ar;var v=rr}else(s=l.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===l.type||"radio"===l.type)&&(g=ir);switch(g&&(g=g(e,r))?qn(o,g,n,a):(v&&v(e,l,r),"focusout"===e&&(v=l._wrapperState)&&v.controlled&&"number"===l.type&&ee(l,"number",l.value)),v=r?xa(r):window,e){case"focusin":(Vn(v)||"true"===v.contentEditable)&&(gr=v,vr=r,br=null);break;case"focusout":br=vr=gr=null;break;case"mousedown":yr=!0;break;case"contextmenu":case"mouseup":case"dragend":yr=!1,xr(o,n,a);break;case"selectionchange":if(hr)break;case"keydown":case"keyup":xr(o,n,a)}var b;if(Rn)e:{switch(e){case"compositionstart":var y="onCompositionStart";break e;case"compositionend":y="onCompositionEnd";break e;case"compositionupdate":y="onCompositionUpdate";break e}y=void 0}else Hn?_n(e,n)&&(y="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(y="onCompositionStart");y&&(zn&&"ko"!==n.locale&&(Hn||"onCompositionStart"!==y?"onCompositionEnd"===y&&Hn&&(b=en()):(Zt="value"in(Jt=a)?Jt.value:Jt.textContent,Hn=!0)),0<(v=Xr(r,y)).length&&(y=new xn(y,e,null,n,a),o.push({event:y,listeners:v}),b?y.data=b:null!==(b=Bn(n))&&(y.data=b))),(b=Ln?function(e,t){switch(e){case"compositionend":return Bn(t);case"keypress":return 32!==t.which?null:(Mn=!0,Un);case"textInput":return(e=t.data)===Un&&Mn?null:e;default:return null}}(e,n):function(e,t){if(Hn)return"compositionend"===e||!Rn&&_n(e,t)?(e=en(),$t=Zt=Jt=null,Hn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return zn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Xr(r,"onBeforeInput")).length&&(a=new xn("onBeforeInput","beforeinput",null,n,a),o.push({event:a,listeners:r}),a.data=b))}Ur(o,t)})}function qr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Xr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,i=a.stateNode;5===a.tag&&null!==i&&(a=i,null!=(i=Oe(e,n))&&r.unshift(qr(e,i,a)),null!=(i=Oe(e,t))&&r.push(qr(e,i,a))),e=e.return}return r}function Qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,r,a){for(var i=t._reactName,o=[];null!==n&&n!==r;){var l=n,s=l.alternate,c=l.stateNode;if(null!==s&&s===r)break;5===l.tag&&null!==c&&(l=c,a?null!=(s=Oe(n,i))&&o.unshift(qr(n,s,l)):a||null!=(s=Oe(n,i))&&o.push(qr(n,s,l))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Gr=/\r\n?/g,Yr=/\u0000|\uFFFD/g;function Jr(e){return("string"===typeof e?e:""+e).replace(Gr,"\n").replace(Yr,"")}function Zr(e,t,n){if(t=Jr(t),Jr(e)!==t&&n)throw Error(i(425))}function $r(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,ia="function"===typeof Promise?Promise:void 0,oa="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ia?function(e){return ia.resolve(null).then(e).catch(la)}:ra;function la(e){setTimeout(function(){throw e})}function sa(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Ht(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Ht(t)}function ca(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ua(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,pa="__reactProps$"+da,ma="__reactContainer$"+da,ha="__reactEvents$"+da,ga="__reactListeners$"+da,va="__reactHandles$"+da;function ba(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ma]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ua(e);null!==e;){if(n=e[fa])return n;e=ua(e)}return t}n=(e=n).parentNode}return null}function ya(e){return!(e=e[fa]||e[ma])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(i(33))}function ka(e){return e[pa]||null}var wa=[],Sa=-1;function Aa(e){return{current:e}}function Ca(e){0>Sa||(e.current=wa[Sa],wa[Sa]=null,Sa--)}function ja(e,t){Sa++,wa[Sa]=e.current,e.current=t}var Na={},Pa=Aa(Na),Ea=Aa(!1),Ta=Na;function Ia(e,t){var n=e.type.contextTypes;if(!n)return Na;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,i={};for(a in n)i[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Oa(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Fa(){Ca(Ea),Ca(Pa)}function Ra(e,t,n){if(Pa.current!==Na)throw Error(i(168));ja(Pa,t),ja(Ea,n)}function Da(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(i(108,W(e)||"Unknown",a));return z({},n,r)}function La(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Na,Ta=Pa.current,ja(Pa,e),ja(Ea,Ea.current),!0}function za(e,t,n){var r=e.stateNode;if(!r)throw Error(i(169));n?(e=Da(e,t,Ta),r.__reactInternalMemoizedMergedChildContext=e,Ca(Ea),Ca(Pa),ja(Pa,e)):Ca(Ea),ja(Ea,n)}var Ua=null,Ma=!1,_a=!1;function Ba(e){null===Ua?Ua=[e]:Ua.push(e)}function Ha(){if(!_a&&null!==Ua){_a=!0;var e=0,t=yt;try{var n=Ua;for(yt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Ua=null,Ma=!1}catch(a){throw null!==Ua&&(Ua=Ua.slice(e+1)),Qe($e,Ha),a}finally{yt=t,_a=!1}}return null}var Wa=[],Va=0,qa=null,Xa=0,Qa=[],Ka=0,Ga=null,Ya=1,Ja="";function Za(e,t){Wa[Va++]=Xa,Wa[Va++]=qa,qa=e,Xa=t}function $a(e,t,n){Qa[Ka++]=Ya,Qa[Ka++]=Ja,Qa[Ka++]=Ga,Ga=e;var r=Ya;e=Ja;var a=32-ot(r)-1;r&=~(1<<a),n+=1;var i=32-ot(t)+a;if(30<i){var o=a-a%5;i=(r&(1<<o)-1).toString(32),r>>=o,a-=o,Ya=1<<32-ot(t)+a|n<<a|r,Ja=i+e}else Ya=1<<i|n<<a|r,Ja=e}function ei(e){null!==e.return&&(Za(e,1),$a(e,1,0))}function ti(e){for(;e===qa;)qa=Wa[--Va],Wa[Va]=null,Xa=Wa[--Va],Wa[Va]=null;for(;e===Ga;)Ga=Qa[--Ka],Qa[Ka]=null,Ja=Qa[--Ka],Qa[Ka]=null,Ya=Qa[--Ka],Qa[Ka]=null}var ni=null,ri=null,ai=!1,ii=null;function oi(e,t){var n=Ic(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function li(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ni=e,ri=ca(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ni=e,ri=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ga?{id:Ya,overflow:Ja}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Ic(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ni=e,ri=null,!0);default:return!1}}function si(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ci(e){if(ai){var t=ri;if(t){var n=t;if(!li(e,t)){if(si(e))throw Error(i(418));t=ca(n.nextSibling);var r=ni;t&&li(e,t)?oi(r,n):(e.flags=-4097&e.flags|2,ai=!1,ni=e)}}else{if(si(e))throw Error(i(418));e.flags=-4097&e.flags|2,ai=!1,ni=e}}}function ui(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ni=e}function di(e){if(e!==ni)return!1;if(!ai)return ui(e),ai=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=ri)){if(si(e))throw fi(),Error(i(418));for(;t;)oi(e,t),t=ca(t.nextSibling)}if(ui(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ri=ca(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ri=null}}else ri=ni?ca(e.stateNode.nextSibling):null;return!0}function fi(){for(var e=ri;e;)e=ca(e.nextSibling)}function pi(){ri=ni=null,ai=!1}function mi(e){null===ii?ii=[e]:ii.push(e)}var hi=x.ReactCurrentBatchConfig;function gi(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(i(309));var r=n.stateNode}if(!r)throw Error(i(147,e));var a=r,o=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===o?t.ref:(t=function(e){var t=a.refs;null===e?delete t[o]:t[o]=e},t._stringRef=o,t)}if("string"!==typeof e)throw Error(i(284));if(!n._owner)throw Error(i(290,e))}return e}function vi(e,t){throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function bi(e){return(0,e._init)(e._payload)}function yi(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Fc(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function l(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=zc(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function c(e,t,n,r){var i=n.type;return i===S?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===i||"object"===typeof i&&null!==i&&i.$$typeof===O&&bi(i)===t.type)?((r=a(t,n.props)).ref=gi(e,t,n),r.return=e,r):((r=Rc(n.type,n.key,n.props,null,e.mode,r)).ref=gi(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Uc(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,i){return null===t||7!==t.tag?((t=Dc(n,e.mode,r,i)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=zc(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case k:return(n=Rc(t.type,t.key,t.props,null,e.mode,n)).ref=gi(e,null,t),n.return=e,n;case w:return(t=Uc(t,e.mode,n)).return=e,t;case O:return f(e,(0,t._init)(t._payload),n)}if(te(t)||D(t))return(t=Dc(t,e.mode,n,null)).return=e,t;vi(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case k:return n.key===a?c(e,t,n,r):null;case w:return n.key===a?u(e,t,n,r):null;case O:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||D(n))return null!==a?null:d(e,t,n,r,null);vi(e,n)}return null}function m(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case k:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case w:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case O:return m(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||D(r))return d(t,e=e.get(n)||null,r,a,null);vi(t,r)}return null}function h(a,i,l,s){for(var c=null,u=null,d=i,h=i=0,g=null;null!==d&&h<l.length;h++){d.index>h?(g=d,d=null):g=d.sibling;var v=p(a,d,l[h],s);if(null===v){null===d&&(d=g);break}e&&d&&null===v.alternate&&t(a,d),i=o(v,i,h),null===u?c=v:u.sibling=v,u=v,d=g}if(h===l.length)return n(a,d),ai&&Za(a,h),c;if(null===d){for(;h<l.length;h++)null!==(d=f(a,l[h],s))&&(i=o(d,i,h),null===u?c=d:u.sibling=d,u=d);return ai&&Za(a,h),c}for(d=r(a,d);h<l.length;h++)null!==(g=m(d,a,h,l[h],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?h:g.key),i=o(g,i,h),null===u?c=g:u.sibling=g,u=g);return e&&d.forEach(function(e){return t(a,e)}),ai&&Za(a,h),c}function g(a,l,s,c){var u=D(s);if("function"!==typeof u)throw Error(i(150));if(null==(s=u.call(s)))throw Error(i(151));for(var d=u=null,h=l,g=l=0,v=null,b=s.next();null!==h&&!b.done;g++,b=s.next()){h.index>g?(v=h,h=null):v=h.sibling;var y=p(a,h,b.value,c);if(null===y){null===h&&(h=v);break}e&&h&&null===y.alternate&&t(a,h),l=o(y,l,g),null===d?u=y:d.sibling=y,d=y,h=v}if(b.done)return n(a,h),ai&&Za(a,g),u;if(null===h){for(;!b.done;g++,b=s.next())null!==(b=f(a,b.value,c))&&(l=o(b,l,g),null===d?u=b:d.sibling=b,d=b);return ai&&Za(a,g),u}for(h=r(a,h);!b.done;g++,b=s.next())null!==(b=m(h,a,g,b.value,c))&&(e&&null!==b.alternate&&h.delete(null===b.key?g:b.key),l=o(b,l,g),null===d?u=b:d.sibling=b,d=b);return e&&h.forEach(function(e){return t(a,e)}),ai&&Za(a,g),u}return function e(r,i,o,s){if("object"===typeof o&&null!==o&&o.type===S&&null===o.key&&(o=o.props.children),"object"===typeof o&&null!==o){switch(o.$$typeof){case k:e:{for(var c=o.key,u=i;null!==u;){if(u.key===c){if((c=o.type)===S){if(7===u.tag){n(r,u.sibling),(i=a(u,o.props.children)).return=r,r=i;break e}}else if(u.elementType===c||"object"===typeof c&&null!==c&&c.$$typeof===O&&bi(c)===u.type){n(r,u.sibling),(i=a(u,o.props)).ref=gi(r,u,o),i.return=r,r=i;break e}n(r,u);break}t(r,u),u=u.sibling}o.type===S?((i=Dc(o.props.children,r.mode,s,o.key)).return=r,r=i):((s=Rc(o.type,o.key,o.props,null,r.mode,s)).ref=gi(r,i,o),s.return=r,r=s)}return l(r);case w:e:{for(u=o.key;null!==i;){if(i.key===u){if(4===i.tag&&i.stateNode.containerInfo===o.containerInfo&&i.stateNode.implementation===o.implementation){n(r,i.sibling),(i=a(i,o.children||[])).return=r,r=i;break e}n(r,i);break}t(r,i),i=i.sibling}(i=Uc(o,r.mode,s)).return=r,r=i}return l(r);case O:return e(r,i,(u=o._init)(o._payload),s)}if(te(o))return h(r,i,o,s);if(D(o))return g(r,i,o,s);vi(r,o)}return"string"===typeof o&&""!==o||"number"===typeof o?(o=""+o,null!==i&&6===i.tag?(n(r,i.sibling),(i=a(i,o)).return=r,r=i):(n(r,i),(i=zc(o,r.mode,s)).return=r,r=i),l(r)):n(r,i)}}var xi=yi(!0),ki=yi(!1),wi=Aa(null),Si=null,Ai=null,Ci=null;function ji(){Ci=Ai=Si=null}function Ni(e){var t=wi.current;Ca(wi),e._currentValue=t}function Pi(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ei(e,t){Si=e,Ci=Ai=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(yl=!0),e.firstContext=null)}function Ti(e){var t=e._currentValue;if(Ci!==e)if(e={context:e,memoizedValue:t,next:null},null===Ai){if(null===Si)throw Error(i(308));Ai=e,Si.dependencies={lanes:0,firstContext:e}}else Ai=Ai.next=e;return t}var Ii=null;function Oi(e){null===Ii?Ii=[e]:Ii.push(e)}function Fi(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Oi(t)):(n.next=a.next,a.next=n),t.interleaved=n,Ri(e,r)}function Ri(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Di=!1;function Li(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function zi(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ui(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Mi(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Ps)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Ri(e,n)}return null===(a=r.interleaved)?(t.next=t,Oi(r)):(t.next=a.next,a.next=t),r.interleaved=t,Ri(e,n)}function _i(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,bt(e,n)}}function Bi(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,i=null;if(null!==(n=n.firstBaseUpdate)){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===i?a=i=o:i=i.next=o,n=n.next}while(null!==n);null===i?a=i=t:i=i.next=t}else a=i=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:i,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Hi(e,t,n,r){var a=e.updateQueue;Di=!1;var i=a.firstBaseUpdate,o=a.lastBaseUpdate,l=a.shared.pending;if(null!==l){a.shared.pending=null;var s=l,c=s.next;s.next=null,null===o?i=c:o.next=c,o=s;var u=e.alternate;null!==u&&((l=(u=u.updateQueue).lastBaseUpdate)!==o&&(null===l?u.firstBaseUpdate=c:l.next=c,u.lastBaseUpdate=s))}if(null!==i){var d=a.baseState;for(o=0,u=c=s=null,l=i;;){var f=l.lane,p=l.eventTime;if((r&f)===f){null!==u&&(u=u.next={eventTime:p,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var m=e,h=l;switch(f=t,p=n,h.tag){case 1:if("function"===typeof(m=h.payload)){d=m.call(p,d,f);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(f="function"===typeof(m=h.payload)?m.call(p,d,f):m)||void 0===f)break e;d=z({},d,f);break e;case 2:Di=!0}}null!==l.callback&&0!==l.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[l]:f.push(l))}else p={eventTime:p,lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===u?(c=u=p,s=d):u=u.next=p,o|=f;if(null===(l=l.next)){if(null===(l=a.shared.pending))break;l=(f=l).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===u&&(s=d),a.baseState=s,a.firstBaseUpdate=c,a.lastBaseUpdate=u,null!==(t=a.shared.interleaved)){a=t;do{o|=a.lane,a=a.next}while(a!==t)}else null===i&&(a.shared.lanes=0);Ls|=o,e.lanes=o,e.memoizedState=d}}function Wi(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(i(191,a));a.call(r)}}}var Vi={},qi=Aa(Vi),Xi=Aa(Vi),Qi=Aa(Vi);function Ki(e){if(e===Vi)throw Error(i(174));return e}function Gi(e,t){switch(ja(Qi,t),ja(Xi,e),ja(qi,Vi),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ca(qi),ja(qi,t)}function Yi(){Ca(qi),Ca(Xi),Ca(Qi)}function Ji(e){Ki(Qi.current);var t=Ki(qi.current),n=se(t,e.type);t!==n&&(ja(Xi,e),ja(qi,n))}function Zi(e){Xi.current===e&&(Ca(qi),Ca(Xi))}var $i=Aa(0);function eo(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var to=[];function no(){for(var e=0;e<to.length;e++)to[e]._workInProgressVersionPrimary=null;to.length=0}var ro=x.ReactCurrentDispatcher,ao=x.ReactCurrentBatchConfig,io=0,oo=null,lo=null,so=null,co=!1,uo=!1,fo=0,po=0;function mo(){throw Error(i(321))}function ho(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!lr(e[n],t[n]))return!1;return!0}function go(e,t,n,r,a,o){if(io=o,oo=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ro.current=null===e||null===e.memoizedState?$o:el,e=n(r,a),uo){o=0;do{if(uo=!1,fo=0,25<=o)throw Error(i(301));o+=1,so=lo=null,t.updateQueue=null,ro.current=tl,e=n(r,a)}while(uo)}if(ro.current=Zo,t=null!==lo&&null!==lo.next,io=0,so=lo=oo=null,co=!1,t)throw Error(i(300));return e}function vo(){var e=0!==fo;return fo=0,e}function bo(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===so?oo.memoizedState=so=e:so=so.next=e,so}function yo(){if(null===lo){var e=oo.alternate;e=null!==e?e.memoizedState:null}else e=lo.next;var t=null===so?oo.memoizedState:so.next;if(null!==t)so=t,lo=e;else{if(null===e)throw Error(i(310));e={memoizedState:(lo=e).memoizedState,baseState:lo.baseState,baseQueue:lo.baseQueue,queue:lo.queue,next:null},null===so?oo.memoizedState=so=e:so=so.next=e}return so}function xo(e,t){return"function"===typeof t?t(e):t}function ko(e){var t=yo(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=lo,a=r.baseQueue,o=n.pending;if(null!==o){if(null!==a){var l=a.next;a.next=o.next,o.next=l}r.baseQueue=a=o,n.pending=null}if(null!==a){o=a.next,r=r.baseState;var s=l=null,c=null,u=o;do{var d=u.lane;if((io&d)===d)null!==c&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===c?(s=c=f,l=r):c=c.next=f,oo.lanes|=d,Ls|=d}u=u.next}while(null!==u&&u!==o);null===c?l=r:c.next=s,lr(r,t.memoizedState)||(yl=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=c,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{o=a.lane,oo.lanes|=o,Ls|=o,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function wo(e){var t=yo(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var l=a=a.next;do{o=e(o,l.action),l=l.next}while(l!==a);lr(o,t.memoizedState)||(yl=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function So(){}function Ao(e,t){var n=oo,r=yo(),a=t(),o=!lr(r.memoizedState,a);if(o&&(r.memoizedState=a,yl=!0),r=r.queue,Lo(No.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||null!==so&&1&so.memoizedState.tag){if(n.flags|=2048,Io(9,jo.bind(null,n,r,a,t),void 0,null),null===Es)throw Error(i(349));0!==(30&io)||Co(n,t,a)}return a}function Co(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function jo(e,t,n,r){t.value=n,t.getSnapshot=r,Po(t)&&Eo(e)}function No(e,t,n){return n(function(){Po(t)&&Eo(e)})}function Po(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!lr(e,n)}catch(r){return!0}}function Eo(e){var t=Ri(e,1);null!==t&&nc(t,e,1,-1)}function To(e){var t=bo();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xo,lastRenderedState:e},t.queue=e,e=e.dispatch=Ko.bind(null,oo,e),[t.memoizedState,e]}function Io(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Oo(){return yo().memoizedState}function Fo(e,t,n,r){var a=bo();oo.flags|=e,a.memoizedState=Io(1|t,n,void 0,void 0===r?null:r)}function Ro(e,t,n,r){var a=yo();r=void 0===r?null:r;var i=void 0;if(null!==lo){var o=lo.memoizedState;if(i=o.destroy,null!==r&&ho(r,o.deps))return void(a.memoizedState=Io(t,n,i,r))}oo.flags|=e,a.memoizedState=Io(1|t,n,i,r)}function Do(e,t){return Fo(8390656,8,e,t)}function Lo(e,t){return Ro(2048,8,e,t)}function zo(e,t){return Ro(4,2,e,t)}function Uo(e,t){return Ro(4,4,e,t)}function Mo(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function _o(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ro(4,4,Mo.bind(null,t,e),n)}function Bo(){}function Ho(e,t){var n=yo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ho(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Wo(e,t){var n=yo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ho(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Vo(e,t,n){return 0===(21&io)?(e.baseState&&(e.baseState=!1,yl=!0),e.memoizedState=n):(lr(n,t)||(n=ht(),oo.lanes|=n,Ls|=n,e.baseState=!0),t)}function qo(e,t){var n=yt;yt=0!==n&&4>n?n:4,e(!0);var r=ao.transition;ao.transition={};try{e(!1),t()}finally{yt=n,ao.transition=r}}function Xo(){return yo().memoizedState}function Qo(e,t,n){var r=tc(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Go(e))Yo(t,n);else if(null!==(n=Fi(e,t,n,r))){nc(n,e,r,ec()),Jo(n,t,r)}}function Ko(e,t,n){var r=tc(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Go(e))Yo(t,a);else{var i=e.alternate;if(0===e.lanes&&(null===i||0===i.lanes)&&null!==(i=t.lastRenderedReducer))try{var o=t.lastRenderedState,l=i(o,n);if(a.hasEagerState=!0,a.eagerState=l,lr(l,o)){var s=t.interleaved;return null===s?(a.next=a,Oi(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(c){}null!==(n=Fi(e,t,a,r))&&(nc(n,e,r,a=ec()),Jo(n,t,r))}}function Go(e){var t=e.alternate;return e===oo||null!==t&&t===oo}function Yo(e,t){uo=co=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Jo(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,bt(e,n)}}var Zo={readContext:Ti,useCallback:mo,useContext:mo,useEffect:mo,useImperativeHandle:mo,useInsertionEffect:mo,useLayoutEffect:mo,useMemo:mo,useReducer:mo,useRef:mo,useState:mo,useDebugValue:mo,useDeferredValue:mo,useTransition:mo,useMutableSource:mo,useSyncExternalStore:mo,useId:mo,unstable_isNewReconciler:!1},$o={readContext:Ti,useCallback:function(e,t){return bo().memoizedState=[e,void 0===t?null:t],e},useContext:Ti,useEffect:Do,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Fo(4194308,4,Mo.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Fo(4194308,4,e,t)},useInsertionEffect:function(e,t){return Fo(4,2,e,t)},useMemo:function(e,t){var n=bo();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=bo();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Qo.bind(null,oo,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},bo().memoizedState=e},useState:To,useDebugValue:Bo,useDeferredValue:function(e){return bo().memoizedState=e},useTransition:function(){var e=To(!1),t=e[0];return e=qo.bind(null,e[1]),bo().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=oo,a=bo();if(ai){if(void 0===n)throw Error(i(407));n=n()}else{if(n=t(),null===Es)throw Error(i(349));0!==(30&io)||Co(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,Do(No.bind(null,r,o,e),[e]),r.flags|=2048,Io(9,jo.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=bo(),t=Es.identifierPrefix;if(ai){var n=Ja;t=":"+t+"R"+(n=(Ya&~(1<<32-ot(Ya)-1)).toString(32)+n),0<(n=fo++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=po++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},el={readContext:Ti,useCallback:Ho,useContext:Ti,useEffect:Lo,useImperativeHandle:_o,useInsertionEffect:zo,useLayoutEffect:Uo,useMemo:Wo,useReducer:ko,useRef:Oo,useState:function(){return ko(xo)},useDebugValue:Bo,useDeferredValue:function(e){return Vo(yo(),lo.memoizedState,e)},useTransition:function(){return[ko(xo)[0],yo().memoizedState]},useMutableSource:So,useSyncExternalStore:Ao,useId:Xo,unstable_isNewReconciler:!1},tl={readContext:Ti,useCallback:Ho,useContext:Ti,useEffect:Lo,useImperativeHandle:_o,useInsertionEffect:zo,useLayoutEffect:Uo,useMemo:Wo,useReducer:wo,useRef:Oo,useState:function(){return wo(xo)},useDebugValue:Bo,useDeferredValue:function(e){var t=yo();return null===lo?t.memoizedState=e:Vo(t,lo.memoizedState,e)},useTransition:function(){return[wo(xo)[0],yo().memoizedState]},useMutableSource:So,useSyncExternalStore:Ao,useId:Xo,unstable_isNewReconciler:!1};function nl(e,t){if(e&&e.defaultProps){for(var n in t=z({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function rl(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:z({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var al={isMounted:function(e){return!!(e=e._reactInternals)&&He(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ec(),a=tc(e),i=Ui(r,a);i.payload=t,void 0!==n&&null!==n&&(i.callback=n),null!==(t=Mi(e,i,a))&&(nc(t,e,a,r),_i(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ec(),a=tc(e),i=Ui(r,a);i.tag=1,i.payload=t,void 0!==n&&null!==n&&(i.callback=n),null!==(t=Mi(e,i,a))&&(nc(t,e,a,r),_i(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ec(),r=tc(e),a=Ui(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Mi(e,a,r))&&(nc(t,e,r,n),_i(t,e,r))}};function il(e,t,n,r,a,i,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,i,o):!t.prototype||!t.prototype.isPureReactComponent||(!sr(n,r)||!sr(a,i))}function ol(e,t,n){var r=!1,a=Na,i=t.contextType;return"object"===typeof i&&null!==i?i=Ti(i):(a=Oa(t)?Ta:Pa.current,i=(r=null!==(r=t.contextTypes)&&void 0!==r)?Ia(e,a):Na),t=new t(n,i),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=al,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=i),t}function ll(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&al.enqueueReplaceState(t,t.state,null)}function sl(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Li(e);var i=t.contextType;"object"===typeof i&&null!==i?a.context=Ti(i):(i=Oa(t)?Ta:Pa.current,a.context=Ia(e,i)),a.state=e.memoizedState,"function"===typeof(i=t.getDerivedStateFromProps)&&(rl(e,t,i,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&al.enqueueReplaceState(a,a.state,null),Hi(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function cl(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var a=n}catch(i){a="\nError generating stack: "+i.message+"\n"+i.stack}return{value:e,source:t,stack:a,digest:null}}function ul(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function dl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var fl="function"===typeof WeakMap?WeakMap:Map;function pl(e,t,n){(n=Ui(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Vs||(Vs=!0,qs=r),dl(0,t)},n}function ml(e,t,n){(n=Ui(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){dl(0,t)}}var i=e.stateNode;return null!==i&&"function"===typeof i.componentDidCatch&&(n.callback=function(){dl(0,t),"function"!==typeof r&&(null===Xs?Xs=new Set([this]):Xs.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function hl(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fl;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Cc.bind(null,e,t,n),t.then(e,e))}function gl(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function vl(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Ui(-1,1)).tag=2,Mi(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var bl=x.ReactCurrentOwner,yl=!1;function xl(e,t,n,r){t.child=null===e?ki(t,null,n,r):xi(t,e.child,n,r)}function kl(e,t,n,r,a){n=n.render;var i=t.ref;return Ei(t,a),r=go(e,t,n,r,i,a),n=vo(),null===e||yl?(ai&&n&&ei(t),t.flags|=1,xl(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Vl(e,t,a))}function wl(e,t,n,r,a){if(null===e){var i=n.type;return"function"!==typeof i||Oc(i)||void 0!==i.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Rc(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=i,Sl(e,t,i,r,a))}if(i=e.child,0===(e.lanes&a)){var o=i.memoizedProps;if((n=null!==(n=n.compare)?n:sr)(o,r)&&e.ref===t.ref)return Vl(e,t,a)}return t.flags|=1,(e=Fc(i,r)).ref=t.ref,e.return=t,t.child=e}function Sl(e,t,n,r,a){if(null!==e){var i=e.memoizedProps;if(sr(i,r)&&e.ref===t.ref){if(yl=!1,t.pendingProps=r=i,0===(e.lanes&a))return t.lanes=e.lanes,Vl(e,t,a);0!==(131072&e.flags)&&(yl=!0)}}return jl(e,t,n,r,a)}function Al(e,t,n){var r=t.pendingProps,a=r.children,i=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ja(Fs,Os),Os|=n;else{if(0===(1073741824&n))return e=null!==i?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ja(Fs,Os),Os|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==i?i.baseLanes:n,ja(Fs,Os),Os|=r}else null!==i?(r=i.baseLanes|n,t.memoizedState=null):r=n,ja(Fs,Os),Os|=r;return xl(e,t,a,n),t.child}function Cl(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function jl(e,t,n,r,a){var i=Oa(n)?Ta:Pa.current;return i=Ia(t,i),Ei(t,a),n=go(e,t,n,r,i,a),r=vo(),null===e||yl?(ai&&r&&ei(t),t.flags|=1,xl(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Vl(e,t,a))}function Nl(e,t,n,r,a){if(Oa(n)){var i=!0;La(t)}else i=!1;if(Ei(t,a),null===t.stateNode)Wl(e,t),ol(t,n,r),sl(t,n,r,a),r=!0;else if(null===e){var o=t.stateNode,l=t.memoizedProps;o.props=l;var s=o.context,c=n.contextType;"object"===typeof c&&null!==c?c=Ti(c):c=Ia(t,c=Oa(n)?Ta:Pa.current);var u=n.getDerivedStateFromProps,d="function"===typeof u||"function"===typeof o.getSnapshotBeforeUpdate;d||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(l!==r||s!==c)&&ll(t,o,r,c),Di=!1;var f=t.memoizedState;o.state=f,Hi(t,r,o,a),s=t.memoizedState,l!==r||f!==s||Ea.current||Di?("function"===typeof u&&(rl(t,n,u,r),s=t.memoizedState),(l=Di||il(t,n,l,r,f,s,c))?(d||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.flags|=4194308)):("function"===typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),o.props=r,o.state=s,o.context=c,r=l):("function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,zi(e,t),l=t.memoizedProps,c=t.type===t.elementType?l:nl(t.type,l),o.props=c,d=t.pendingProps,f=o.context,"object"===typeof(s=n.contextType)&&null!==s?s=Ti(s):s=Ia(t,s=Oa(n)?Ta:Pa.current);var p=n.getDerivedStateFromProps;(u="function"===typeof p||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(l!==d||f!==s)&&ll(t,o,r,s),Di=!1,f=t.memoizedState,o.state=f,Hi(t,r,o,a);var m=t.memoizedState;l!==d||f!==m||Ea.current||Di?("function"===typeof p&&(rl(t,n,p,r),m=t.memoizedState),(c=Di||il(t,n,c,r,f,m,s)||!1)?(u||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,m,s),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,m,s)),"function"===typeof o.componentDidUpdate&&(t.flags|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof o.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=m),o.props=r,o.state=m,o.context=s,r=c):("function"!==typeof o.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Pl(e,t,n,r,i,a)}function Pl(e,t,n,r,a,i){Cl(e,t);var o=0!==(128&t.flags);if(!r&&!o)return a&&za(t,n,!1),Vl(e,t,i);r=t.stateNode,bl.current=t;var l=o&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&o?(t.child=xi(t,e.child,null,i),t.child=xi(t,null,l,i)):xl(e,t,l,i),t.memoizedState=r.state,a&&za(t,n,!0),t.child}function El(e){var t=e.stateNode;t.pendingContext?Ra(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Ra(0,t.context,!1),Gi(e,t.containerInfo)}function Tl(e,t,n,r,a){return pi(),mi(a),t.flags|=256,xl(e,t,n,r),t.child}var Il,Ol,Fl,Rl,Dl={dehydrated:null,treeContext:null,retryLane:0};function Ll(e){return{baseLanes:e,cachePool:null,transitions:null}}function zl(e,t,n){var r,a=t.pendingProps,o=$i.current,l=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&o)),r?(l=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(o|=1),ja($i,1&o),null===e)return ci(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=a.children,e=a.fallback,l?(a=t.mode,l=t.child,s={mode:"hidden",children:s},0===(1&a)&&null!==l?(l.childLanes=0,l.pendingProps=s):l=Lc(s,a,0,null),e=Dc(e,a,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=Ll(n),t.memoizedState=Dl,e):Ul(t,s));if(null!==(o=e.memoizedState)&&null!==(r=o.dehydrated))return function(e,t,n,r,a,o,l){if(n)return 256&t.flags?(t.flags&=-257,Ml(e,t,l,r=ul(Error(i(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(o=r.fallback,a=t.mode,r=Lc({mode:"visible",children:r.children},a,0,null),(o=Dc(o,a,l,null)).flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,0!==(1&t.mode)&&xi(t,e.child,null,l),t.child.memoizedState=Ll(l),t.memoizedState=Dl,o);if(0===(1&t.mode))return Ml(e,t,l,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var s=r.dgst;return r=s,Ml(e,t,l,r=ul(o=Error(i(419)),r,void 0))}if(s=0!==(l&e.childLanes),yl||s){if(null!==(r=Es)){switch(l&-l){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|l))?0:a)&&a!==o.retryLane&&(o.retryLane=a,Ri(e,a),nc(r,e,a,-1))}return hc(),Ml(e,t,l,r=ul(Error(i(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Nc.bind(null,e),a._reactRetry=t,null):(e=o.treeContext,ri=ca(a.nextSibling),ni=t,ai=!0,ii=null,null!==e&&(Qa[Ka++]=Ya,Qa[Ka++]=Ja,Qa[Ka++]=Ga,Ya=e.id,Ja=e.overflow,Ga=t),t=Ul(t,r.children),t.flags|=4096,t)}(e,t,s,a,r,o,n);if(l){l=a.fallback,s=t.mode,r=(o=e.child).sibling;var c={mode:"hidden",children:a.children};return 0===(1&s)&&t.child!==o?((a=t.child).childLanes=0,a.pendingProps=c,t.deletions=null):(a=Fc(o,c)).subtreeFlags=14680064&o.subtreeFlags,null!==r?l=Fc(r,l):(l=Dc(l,s,n,null)).flags|=2,l.return=t,a.return=t,a.sibling=l,t.child=a,a=l,l=t.child,s=null===(s=e.child.memoizedState)?Ll(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},l.memoizedState=s,l.childLanes=e.childLanes&~n,t.memoizedState=Dl,a}return e=(l=e.child).sibling,a=Fc(l,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Ul(e,t){return(t=Lc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Ml(e,t,n,r){return null!==r&&mi(r),xi(t,e.child,null,n),(e=Ul(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function _l(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Pi(e.return,t,n)}function Bl(e,t,n,r,a){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=a)}function Hl(e,t,n){var r=t.pendingProps,a=r.revealOrder,i=r.tail;if(xl(e,t,r.children,n),0!==(2&(r=$i.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&_l(e,n,t);else if(19===e.tag)_l(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ja($i,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===eo(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Bl(t,!1,a,n,i);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===eo(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Bl(t,!0,n,null,i);break;case"together":Bl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Wl(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Vl(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Ls|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(n=Fc(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Fc(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function ql(e,t){if(!ai)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Xl(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ql(e,t,n){var r=t.pendingProps;switch(ti(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Xl(t),null;case 1:case 17:return Oa(t.type)&&Fa(),Xl(t),null;case 3:return r=t.stateNode,Yi(),Ca(Ea),Ca(Pa),no(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(di(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ii&&(oc(ii),ii=null))),Ol(e,t),Xl(t),null;case 5:Zi(t);var a=Ki(Qi.current);if(n=t.type,null!==e&&null!=t.stateNode)Fl(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(i(166));return Xl(t),null}if(e=Ki(qi.current),di(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[fa]=t,r[pa]=o,e=0!==(1&t.mode),n){case"dialog":Mr("cancel",r),Mr("close",r);break;case"iframe":case"object":case"embed":Mr("load",r);break;case"video":case"audio":for(a=0;a<Dr.length;a++)Mr(Dr[a],r);break;case"source":Mr("error",r);break;case"img":case"image":case"link":Mr("error",r),Mr("load",r);break;case"details":Mr("toggle",r);break;case"input":Y(r,o),Mr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},Mr("invalid",r);break;case"textarea":ae(r,o),Mr("invalid",r)}for(var s in be(n,o),a=null,o)if(o.hasOwnProperty(s)){var c=o[s];"children"===s?"string"===typeof c?r.textContent!==c&&(!0!==o.suppressHydrationWarning&&Zr(r.textContent,c,e),a=["children",c]):"number"===typeof c&&r.textContent!==""+c&&(!0!==o.suppressHydrationWarning&&Zr(r.textContent,c,e),a=["children",""+c]):l.hasOwnProperty(s)&&null!=c&&"onScroll"===s&&Mr("scroll",r)}switch(n){case"input":X(r),$(r,o,!0);break;case"textarea":X(r),oe(r);break;case"select":case"option":break;default:"function"===typeof o.onClick&&(r.onclick=$r)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=le(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[fa]=t,e[pa]=r,Il(e,t,!1,!1),t.stateNode=e;e:{switch(s=ye(n,r),n){case"dialog":Mr("cancel",e),Mr("close",e),a=r;break;case"iframe":case"object":case"embed":Mr("load",e),a=r;break;case"video":case"audio":for(a=0;a<Dr.length;a++)Mr(Dr[a],e);a=r;break;case"source":Mr("error",e),a=r;break;case"img":case"image":case"link":Mr("error",e),Mr("load",e),a=r;break;case"details":Mr("toggle",e),a=r;break;case"input":Y(e,r),a=G(e,r),Mr("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=z({},r,{value:void 0}),Mr("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Mr("invalid",e)}for(o in be(n,a),c=a)if(c.hasOwnProperty(o)){var u=c[o];"style"===o?ge(e,u):"dangerouslySetInnerHTML"===o?null!=(u=u?u.__html:void 0)&&de(e,u):"children"===o?"string"===typeof u?("textarea"!==n||""!==u)&&fe(e,u):"number"===typeof u&&fe(e,""+u):"suppressContentEditableWarning"!==o&&"suppressHydrationWarning"!==o&&"autoFocus"!==o&&(l.hasOwnProperty(o)?null!=u&&"onScroll"===o&&Mr("scroll",e):null!=u&&y(e,o,u,s))}switch(n){case"input":X(e),$(e,r,!1);break;case"textarea":X(e),oe(e);break;case"option":null!=r.value&&e.setAttribute("value",""+V(r.value));break;case"select":e.multiple=!!r.multiple,null!=(o=r.value)?ne(e,!!r.multiple,o,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=$r)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Xl(t),null;case 6:if(e&&null!=t.stateNode)Rl(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(i(166));if(n=Ki(Qi.current),Ki(qi.current),di(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(o=r.nodeValue!==n)&&null!==(e=ni))switch(e.tag){case 3:Zr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Zr(r.nodeValue,n,0!==(1&e.mode))}o&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return Xl(t),null;case 13:if(Ca($i),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ai&&null!==ri&&0!==(1&t.mode)&&0===(128&t.flags))fi(),pi(),t.flags|=98560,o=!1;else if(o=di(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(i(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(i(317));o[fa]=t}else pi(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Xl(t),o=!1}else null!==ii&&(oc(ii),ii=null),o=!0;if(!o)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&$i.current)?0===Rs&&(Rs=3):hc())),null!==t.updateQueue&&(t.flags|=4),Xl(t),null);case 4:return Yi(),Ol(e,t),null===e&&Hr(t.stateNode.containerInfo),Xl(t),null;case 10:return Ni(t.type._context),Xl(t),null;case 19:if(Ca($i),null===(o=t.memoizedState))return Xl(t),null;if(r=0!==(128&t.flags),null===(s=o.rendering))if(r)ql(o,!1);else{if(0!==Rs||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=eo(e))){for(t.flags|=128,ql(o,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(o=n).flags&=14680066,null===(s=o.alternate)?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ja($i,1&$i.current|2),t.child}e=e.sibling}null!==o.tail&&Je()>Hs&&(t.flags|=128,r=!0,ql(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=eo(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),ql(o,!0),null===o.tail&&"hidden"===o.tailMode&&!s.alternate&&!ai)return Xl(t),null}else 2*Je()-o.renderingStartTime>Hs&&1073741824!==n&&(t.flags|=128,r=!0,ql(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=o.last)?n.sibling=s:t.child=s,o.last=s)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Je(),t.sibling=null,n=$i.current,ja($i,r?1&n|2:1&n),t):(Xl(t),null);case 22:case 23:return dc(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Os)&&(Xl(t),6&t.subtreeFlags&&(t.flags|=8192)):Xl(t),null;case 24:case 25:return null}throw Error(i(156,t.tag))}function Kl(e,t){switch(ti(t),t.tag){case 1:return Oa(t.type)&&Fa(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Yi(),Ca(Ea),Ca(Pa),no(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Zi(t),null;case 13:if(Ca($i),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));pi()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ca($i),null;case 4:return Yi(),null;case 10:return Ni(t.type._context),null;case 22:case 23:return dc(),null;default:return null}}Il=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ol=function(){},Fl=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Ki(qi.current);var i,o=null;switch(n){case"input":a=G(e,a),r=G(e,r),o=[];break;case"select":a=z({},a,{value:void 0}),r=z({},r,{value:void 0}),o=[];break;case"textarea":a=re(e,a),r=re(e,r),o=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=$r)}for(u in be(n,r),n=null,a)if(!r.hasOwnProperty(u)&&a.hasOwnProperty(u)&&null!=a[u])if("style"===u){var s=a[u];for(i in s)s.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(l.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var c=r[u];if(s=null!=a?a[u]:void 0,r.hasOwnProperty(u)&&c!==s&&(null!=c||null!=s))if("style"===u)if(s){for(i in s)!s.hasOwnProperty(i)||c&&c.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in c)c.hasOwnProperty(i)&&s[i]!==c[i]&&(n||(n={}),n[i]=c[i])}else n||(o||(o=[]),o.push(u,n)),n=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,s=s?s.__html:void 0,null!=c&&s!==c&&(o=o||[]).push(u,c)):"children"===u?"string"!==typeof c&&"number"!==typeof c||(o=o||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(l.hasOwnProperty(u)?(null!=c&&"onScroll"===u&&Mr("scroll",e),o||s===c||(o=[])):(o=o||[]).push(u,c))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}},Rl=function(e,t,n,r){n!==r&&(t.flags|=4)};var Gl=!1,Yl=!1,Jl="function"===typeof WeakSet?WeakSet:Set,Zl=null;function $l(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Ac(e,t,r)}else n.current=null}function es(e,t,n){try{n()}catch(r){Ac(e,t,r)}}var ts=!1;function ns(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var i=a.destroy;a.destroy=void 0,void 0!==i&&es(t,n,i)}a=a.next}while(a!==r)}}function rs(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function as(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function is(e){var t=e.alternate;null!==t&&(e.alternate=null,is(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[pa],delete t[ha],delete t[ga],delete t[va])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function os(e){return 5===e.tag||3===e.tag||4===e.tag}function ls(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||os(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ss(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=$r));else if(4!==r&&null!==(e=e.child))for(ss(e,t,n),e=e.sibling;null!==e;)ss(e,t,n),e=e.sibling}function cs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(cs(e,t,n),e=e.sibling;null!==e;)cs(e,t,n),e=e.sibling}var us=null,ds=!1;function fs(e,t,n){for(n=n.child;null!==n;)ps(e,t,n),n=n.sibling}function ps(e,t,n){if(it&&"function"===typeof it.onCommitFiberUnmount)try{it.onCommitFiberUnmount(at,n)}catch(l){}switch(n.tag){case 5:Yl||$l(n,t);case 6:var r=us,a=ds;us=null,fs(e,t,n),ds=a,null!==(us=r)&&(ds?(e=us,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):us.removeChild(n.stateNode));break;case 18:null!==us&&(ds?(e=us,n=n.stateNode,8===e.nodeType?sa(e.parentNode,n):1===e.nodeType&&sa(e,n),Ht(e)):sa(us,n.stateNode));break;case 4:r=us,a=ds,us=n.stateNode.containerInfo,ds=!0,fs(e,t,n),us=r,ds=a;break;case 0:case 11:case 14:case 15:if(!Yl&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var i=a,o=i.destroy;i=i.tag,void 0!==o&&(0!==(2&i)||0!==(4&i))&&es(n,t,o),a=a.next}while(a!==r)}fs(e,t,n);break;case 1:if(!Yl&&($l(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Ac(n,t,l)}fs(e,t,n);break;case 21:fs(e,t,n);break;case 22:1&n.mode?(Yl=(r=Yl)||null!==n.memoizedState,fs(e,t,n),Yl=r):fs(e,t,n);break;default:fs(e,t,n)}}function ms(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Jl),t.forEach(function(t){var r=Pc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function hs(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var o=e,l=t,s=l;e:for(;null!==s;){switch(s.tag){case 5:us=s.stateNode,ds=!1;break e;case 3:case 4:us=s.stateNode.containerInfo,ds=!0;break e}s=s.return}if(null===us)throw Error(i(160));ps(o,l,a),us=null,ds=!1;var c=a.alternate;null!==c&&(c.return=null),a.return=null}catch(u){Ac(a,t,u)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gs(t,e),t=t.sibling}function gs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(hs(t,e),vs(e),4&r){try{ns(3,e,e.return),rs(3,e)}catch(g){Ac(e,e.return,g)}try{ns(5,e,e.return)}catch(g){Ac(e,e.return,g)}}break;case 1:hs(t,e),vs(e),512&r&&null!==n&&$l(n,n.return);break;case 5:if(hs(t,e),vs(e),512&r&&null!==n&&$l(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(g){Ac(e,e.return,g)}}if(4&r&&null!=(a=e.stateNode)){var o=e.memoizedProps,l=null!==n?n.memoizedProps:o,s=e.type,c=e.updateQueue;if(e.updateQueue=null,null!==c)try{"input"===s&&"radio"===o.type&&null!=o.name&&J(a,o),ye(s,l);var u=ye(s,o);for(l=0;l<c.length;l+=2){var d=c[l],f=c[l+1];"style"===d?ge(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):y(a,d,f,u)}switch(s){case"input":Z(a,o);break;case"textarea":ie(a,o);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!o.multiple;var m=o.value;null!=m?ne(a,!!o.multiple,m,!1):p!==!!o.multiple&&(null!=o.defaultValue?ne(a,!!o.multiple,o.defaultValue,!0):ne(a,!!o.multiple,o.multiple?[]:"",!1))}a[pa]=o}catch(g){Ac(e,e.return,g)}}break;case 6:if(hs(t,e),vs(e),4&r){if(null===e.stateNode)throw Error(i(162));a=e.stateNode,o=e.memoizedProps;try{a.nodeValue=o}catch(g){Ac(e,e.return,g)}}break;case 3:if(hs(t,e),vs(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Ht(t.containerInfo)}catch(g){Ac(e,e.return,g)}break;case 4:default:hs(t,e),vs(e);break;case 13:hs(t,e),vs(e),8192&(a=e.child).flags&&(o=null!==a.memoizedState,a.stateNode.isHidden=o,!o||null!==a.alternate&&null!==a.alternate.memoizedState||(Bs=Je())),4&r&&ms(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Yl=(u=Yl)||d,hs(t,e),Yl=u):hs(t,e),vs(e),8192&r){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!d&&0!==(1&e.mode))for(Zl=e,d=e.child;null!==d;){for(f=Zl=d;null!==Zl;){switch(m=(p=Zl).child,p.tag){case 0:case 11:case 14:case 15:ns(4,p,p.return);break;case 1:$l(p,p.return);var h=p.stateNode;if("function"===typeof h.componentWillUnmount){r=p,n=p.return;try{t=r,h.props=t.memoizedProps,h.state=t.memoizedState,h.componentWillUnmount()}catch(g){Ac(r,n,g)}}break;case 5:$l(p,p.return);break;case 22:if(null!==p.memoizedState){ks(f);continue}}null!==m?(m.return=p,Zl=m):ks(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,u?"function"===typeof(o=a.style).setProperty?o.setProperty("display","none","important"):o.display="none":(s=f.stateNode,l=void 0!==(c=f.memoizedProps.style)&&null!==c&&c.hasOwnProperty("display")?c.display:null,s.style.display=he("display",l))}catch(g){Ac(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(g){Ac(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:hs(t,e),vs(e),4&r&&ms(e);case 21:}}function vs(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(os(n)){var r=n;break e}n=n.return}throw Error(i(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),cs(e,ls(e),a);break;case 3:case 4:var o=r.stateNode.containerInfo;ss(e,ls(e),o);break;default:throw Error(i(161))}}catch(l){Ac(e,e.return,l)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function bs(e,t,n){Zl=e,ys(e,t,n)}function ys(e,t,n){for(var r=0!==(1&e.mode);null!==Zl;){var a=Zl,i=a.child;if(22===a.tag&&r){var o=null!==a.memoizedState||Gl;if(!o){var l=a.alternate,s=null!==l&&null!==l.memoizedState||Yl;l=Gl;var c=Yl;if(Gl=o,(Yl=s)&&!c)for(Zl=a;null!==Zl;)s=(o=Zl).child,22===o.tag&&null!==o.memoizedState?ws(a):null!==s?(s.return=o,Zl=s):ws(a);for(;null!==i;)Zl=i,ys(i,t,n),i=i.sibling;Zl=a,Gl=l,Yl=c}xs(e)}else 0!==(8772&a.subtreeFlags)&&null!==i?(i.return=a,Zl=i):xs(e)}}function xs(e){for(;null!==Zl;){var t=Zl;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Yl||rs(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Yl)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:nl(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;null!==o&&Wi(t,o,r);break;case 3:var l=t.updateQueue;if(null!==l){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Wi(t,l,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var d=u.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Ht(f)}}}break;default:throw Error(i(163))}Yl||512&t.flags&&as(t)}catch(p){Ac(t,t.return,p)}}if(t===e){Zl=null;break}if(null!==(n=t.sibling)){n.return=t.return,Zl=n;break}Zl=t.return}}function ks(e){for(;null!==Zl;){var t=Zl;if(t===e){Zl=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Zl=n;break}Zl=t.return}}function ws(e){for(;null!==Zl;){var t=Zl;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rs(4,t)}catch(s){Ac(t,n,s)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(s){Ac(t,a,s)}}var i=t.return;try{as(t)}catch(s){Ac(t,i,s)}break;case 5:var o=t.return;try{as(t)}catch(s){Ac(t,o,s)}}}catch(s){Ac(t,t.return,s)}if(t===e){Zl=null;break}var l=t.sibling;if(null!==l){l.return=t.return,Zl=l;break}Zl=t.return}}var Ss,As=Math.ceil,Cs=x.ReactCurrentDispatcher,js=x.ReactCurrentOwner,Ns=x.ReactCurrentBatchConfig,Ps=0,Es=null,Ts=null,Is=0,Os=0,Fs=Aa(0),Rs=0,Ds=null,Ls=0,zs=0,Us=0,Ms=null,_s=null,Bs=0,Hs=1/0,Ws=null,Vs=!1,qs=null,Xs=null,Qs=!1,Ks=null,Gs=0,Ys=0,Js=null,Zs=-1,$s=0;function ec(){return 0!==(6&Ps)?Je():-1!==Zs?Zs:Zs=Je()}function tc(e){return 0===(1&e.mode)?1:0!==(2&Ps)&&0!==Is?Is&-Is:null!==hi.transition?(0===$s&&($s=ht()),$s):0!==(e=yt)?e:e=void 0===(e=window.event)?16:Yt(e.type)}function nc(e,t,n,r){if(50<Ys)throw Ys=0,Js=null,Error(i(185));vt(e,n,r),0!==(2&Ps)&&e===Es||(e===Es&&(0===(2&Ps)&&(zs|=n),4===Rs&&lc(e,Is)),rc(e,r),1===n&&0===Ps&&0===(1&t.mode)&&(Hs=Je()+500,Ma&&Ha()))}function rc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-ot(i),l=1<<o,s=a[o];-1===s?0!==(l&n)&&0===(l&r)||(a[o]=pt(l,t)):s<=t&&(e.expiredLanes|=l),i&=~l}}(e,t);var r=ft(e,e===Es?Is:0);if(0===r)null!==n&&Ke(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ke(n),1===t)0===e.tag?function(e){Ma=!0,Ba(e)}(sc.bind(null,e)):Ba(sc.bind(null,e)),oa(function(){0===(6&Ps)&&Ha()}),n=null;else{switch(xt(r)){case 1:n=$e;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Ec(n,ac.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ac(e,t){if(Zs=-1,$s=0,0!==(6&Ps))throw Error(i(327));var n=e.callbackNode;if(wc()&&e.callbackNode!==n)return null;var r=ft(e,e===Es?Is:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gc(e,r);else{t=r;var a=Ps;Ps|=2;var o=mc();for(Es===e&&Is===t||(Ws=null,Hs=Je()+500,fc(e,t));;)try{bc();break}catch(s){pc(e,s)}ji(),Cs.current=o,Ps=a,null!==Ts?t=0:(Es=null,Is=0,t=Rs)}if(0!==t){if(2===t&&(0!==(a=mt(e))&&(r=a,t=ic(e,a))),1===t)throw n=Ds,fc(e,0),lc(e,r),rc(e,Je()),n;if(6===t)lc(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],i=a.getSnapshot;a=a.value;try{if(!lr(i(),a))return!1}catch(l){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=gc(e,r))&&(0!==(o=mt(e))&&(r=o,t=ic(e,o))),1===t))throw n=Ds,fc(e,0),lc(e,r),rc(e,Je()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(i(345));case 2:case 5:kc(e,_s,Ws);break;case 3:if(lc(e,r),(130023424&r)===r&&10<(t=Bs+500-Je())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){ec(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(kc.bind(null,e,_s,Ws),t);break}kc(e,_s,Ws);break;case 4:if(lc(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var l=31-ot(r);o=1<<l,(l=t[l])>a&&(a=l),r&=~o}if(r=a,10<(r=(120>(r=Je()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*As(r/1960))-r)){e.timeoutHandle=ra(kc.bind(null,e,_s,Ws),r);break}kc(e,_s,Ws);break;default:throw Error(i(329))}}}return rc(e,Je()),e.callbackNode===n?ac.bind(null,e):null}function ic(e,t){var n=Ms;return e.current.memoizedState.isDehydrated&&(fc(e,t).flags|=256),2!==(e=gc(e,t))&&(t=_s,_s=n,null!==t&&oc(t)),e}function oc(e){null===_s?_s=e:_s.push.apply(_s,e)}function lc(e,t){for(t&=~Us,t&=~zs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ot(t),r=1<<n;e[n]=-1,t&=~r}}function sc(e){if(0!==(6&Ps))throw Error(i(327));wc();var t=ft(e,0);if(0===(1&t))return rc(e,Je()),null;var n=gc(e,t);if(0!==e.tag&&2===n){var r=mt(e);0!==r&&(t=r,n=ic(e,r))}if(1===n)throw n=Ds,fc(e,0),lc(e,t),rc(e,Je()),n;if(6===n)throw Error(i(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,kc(e,_s,Ws),rc(e,Je()),null}function cc(e,t){var n=Ps;Ps|=1;try{return e(t)}finally{0===(Ps=n)&&(Hs=Je()+500,Ma&&Ha())}}function uc(e){null!==Ks&&0===Ks.tag&&0===(6&Ps)&&wc();var t=Ps;Ps|=1;var n=Ns.transition,r=yt;try{if(Ns.transition=null,yt=1,e)return e()}finally{yt=r,Ns.transition=n,0===(6&(Ps=t))&&Ha()}}function dc(){Os=Fs.current,Ca(Fs)}function fc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==Ts)for(n=Ts.return;null!==n;){var r=n;switch(ti(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Fa();break;case 3:Yi(),Ca(Ea),Ca(Pa),no();break;case 5:Zi(r);break;case 4:Yi();break;case 13:case 19:Ca($i);break;case 10:Ni(r.type._context);break;case 22:case 23:dc()}n=n.return}if(Es=e,Ts=e=Fc(e.current,null),Is=Os=t,Rs=0,Ds=null,Us=zs=Ls=0,_s=Ms=null,null!==Ii){for(t=0;t<Ii.length;t++)if(null!==(r=(n=Ii[t]).interleaved)){n.interleaved=null;var a=r.next,i=n.pending;if(null!==i){var o=i.next;i.next=a,r.next=o}n.pending=r}Ii=null}return e}function pc(e,t){for(;;){var n=Ts;try{if(ji(),ro.current=Zo,co){for(var r=oo.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}co=!1}if(io=0,so=lo=oo=null,uo=!1,fo=0,js.current=null,null===n||null===n.return){Rs=1,Ds=t,Ts=null;break}e:{var o=e,l=n.return,s=n,c=t;if(t=Is,s.flags|=32768,null!==c&&"object"===typeof c&&"function"===typeof c.then){var u=c,d=s,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var m=gl(l);if(null!==m){m.flags&=-257,vl(m,l,s,0,t),1&m.mode&&hl(o,u,t),c=u;var h=(t=m).updateQueue;if(null===h){var g=new Set;g.add(c),t.updateQueue=g}else h.add(c);break e}if(0===(1&t)){hl(o,u,t),hc();break e}c=Error(i(426))}else if(ai&&1&s.mode){var v=gl(l);if(null!==v){0===(65536&v.flags)&&(v.flags|=256),vl(v,l,s,0,t),mi(cl(c,s));break e}}o=c=cl(c,s),4!==Rs&&(Rs=2),null===Ms?Ms=[o]:Ms.push(o),o=l;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t,Bi(o,pl(0,c,t));break e;case 1:s=c;var b=o.type,y=o.stateNode;if(0===(128&o.flags)&&("function"===typeof b.getDerivedStateFromError||null!==y&&"function"===typeof y.componentDidCatch&&(null===Xs||!Xs.has(y)))){o.flags|=65536,t&=-t,o.lanes|=t,Bi(o,ml(o,s,t));break e}}o=o.return}while(null!==o)}xc(n)}catch(x){t=x,Ts===n&&null!==n&&(Ts=n=n.return);continue}break}}function mc(){var e=Cs.current;return Cs.current=Zo,null===e?Zo:e}function hc(){0!==Rs&&3!==Rs&&2!==Rs||(Rs=4),null===Es||0===(268435455&Ls)&&0===(268435455&zs)||lc(Es,Is)}function gc(e,t){var n=Ps;Ps|=2;var r=mc();for(Es===e&&Is===t||(Ws=null,fc(e,t));;)try{vc();break}catch(a){pc(e,a)}if(ji(),Ps=n,Cs.current=r,null!==Ts)throw Error(i(261));return Es=null,Is=0,Rs}function vc(){for(;null!==Ts;)yc(Ts)}function bc(){for(;null!==Ts&&!Ge();)yc(Ts)}function yc(e){var t=Ss(e.alternate,e,Os);e.memoizedProps=e.pendingProps,null===t?xc(e):Ts=t,js.current=null}function xc(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Ql(n,t,Os)))return void(Ts=n)}else{if(null!==(n=Kl(n,t)))return n.flags&=32767,void(Ts=n);if(null===e)return Rs=6,void(Ts=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Ts=t);Ts=t=e}while(null!==t);0===Rs&&(Rs=5)}function kc(e,t,n){var r=yt,a=Ns.transition;try{Ns.transition=null,yt=1,function(e,t,n,r){do{wc()}while(null!==Ks);if(0!==(6&Ps))throw Error(i(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-ot(n),i=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~i}}(e,o),e===Es&&(Ts=Es=null,Is=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Qs||(Qs=!0,Ec(tt,function(){return wc(),null})),o=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||o){o=Ns.transition,Ns.transition=null;var l=yt;yt=1;var s=Ps;Ps|=4,js.current=null,function(e,t){if(ea=Vt,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(k){n=null;break e}var l=0,s=-1,c=-1,u=0,d=0,f=e,p=null;t:for(;;){for(var m;f!==n||0!==a&&3!==f.nodeType||(s=l+a),f!==o||0!==r&&3!==f.nodeType||(c=l+r),3===f.nodeType&&(l+=f.nodeValue.length),null!==(m=f.firstChild);)p=f,f=m;for(;;){if(f===e)break t;if(p===n&&++u===a&&(s=l),p===o&&++d===r&&(c=l),null!==(m=f.nextSibling))break;p=(f=p).parentNode}f=m}n=-1===s||-1===c?null:{start:s,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},Vt=!1,Zl=t;null!==Zl;)if(e=(t=Zl).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Zl=e;else for(;null!==Zl;){t=Zl;try{var h=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==h){var g=h.memoizedProps,v=h.memoizedState,b=t.stateNode,y=b.getSnapshotBeforeUpdate(t.elementType===t.type?g:nl(t.type,g),v);b.__reactInternalSnapshotBeforeUpdate=y}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(i(163))}}catch(k){Ac(t,t.return,k)}if(null!==(e=t.sibling)){e.return=t.return,Zl=e;break}Zl=t.return}h=ts,ts=!1}(e,n),gs(n,e),mr(ta),Vt=!!ea,ta=ea=null,e.current=n,bs(n,e,a),Ye(),Ps=s,yt=l,Ns.transition=o}else e.current=n;if(Qs&&(Qs=!1,Ks=e,Gs=a),o=e.pendingLanes,0===o&&(Xs=null),function(e){if(it&&"function"===typeof it.onCommitFiberRoot)try{it.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),rc(e,Je()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Vs)throw Vs=!1,e=qs,qs=null,e;0!==(1&Gs)&&0!==e.tag&&wc(),o=e.pendingLanes,0!==(1&o)?e===Js?Ys++:(Ys=0,Js=e):Ys=0,Ha()}(e,t,n,r)}finally{Ns.transition=a,yt=r}return null}function wc(){if(null!==Ks){var e=xt(Gs),t=Ns.transition,n=yt;try{if(Ns.transition=null,yt=16>e?16:e,null===Ks)var r=!1;else{if(e=Ks,Ks=null,Gs=0,0!==(6&Ps))throw Error(i(331));var a=Ps;for(Ps|=4,Zl=e.current;null!==Zl;){var o=Zl,l=o.child;if(0!==(16&Zl.flags)){var s=o.deletions;if(null!==s){for(var c=0;c<s.length;c++){var u=s[c];for(Zl=u;null!==Zl;){var d=Zl;switch(d.tag){case 0:case 11:case 15:ns(8,d,o)}var f=d.child;if(null!==f)f.return=d,Zl=f;else for(;null!==Zl;){var p=(d=Zl).sibling,m=d.return;if(is(d),d===u){Zl=null;break}if(null!==p){p.return=m,Zl=p;break}Zl=m}}}var h=o.alternate;if(null!==h){var g=h.child;if(null!==g){h.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}Zl=o}}if(0!==(2064&o.subtreeFlags)&&null!==l)l.return=o,Zl=l;else e:for(;null!==Zl;){if(0!==(2048&(o=Zl).flags))switch(o.tag){case 0:case 11:case 15:ns(9,o,o.return)}var b=o.sibling;if(null!==b){b.return=o.return,Zl=b;break e}Zl=o.return}}var y=e.current;for(Zl=y;null!==Zl;){var x=(l=Zl).child;if(0!==(2064&l.subtreeFlags)&&null!==x)x.return=l,Zl=x;else e:for(l=y;null!==Zl;){if(0!==(2048&(s=Zl).flags))try{switch(s.tag){case 0:case 11:case 15:rs(9,s)}}catch(w){Ac(s,s.return,w)}if(s===l){Zl=null;break e}var k=s.sibling;if(null!==k){k.return=s.return,Zl=k;break e}Zl=s.return}}if(Ps=a,Ha(),it&&"function"===typeof it.onPostCommitFiberRoot)try{it.onPostCommitFiberRoot(at,e)}catch(w){}r=!0}return r}finally{yt=n,Ns.transition=t}}return!1}function Sc(e,t,n){e=Mi(e,t=pl(0,t=cl(n,t),1),1),t=ec(),null!==e&&(vt(e,1,t),rc(e,t))}function Ac(e,t,n){if(3===e.tag)Sc(e,e,n);else for(;null!==t;){if(3===t.tag){Sc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Xs||!Xs.has(r))){t=Mi(t,e=ml(t,e=cl(n,e),1),1),e=ec(),null!==t&&(vt(t,1,e),rc(t,e));break}}t=t.return}}function Cc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=ec(),e.pingedLanes|=e.suspendedLanes&n,Es===e&&(Is&n)===n&&(4===Rs||3===Rs&&(130023424&Is)===Is&&500>Je()-Bs?fc(e,0):Us|=n),rc(e,t)}function jc(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ut,0===(130023424&(ut<<=1))&&(ut=4194304)));var n=ec();null!==(e=Ri(e,t))&&(vt(e,t,n),rc(e,n))}function Nc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),jc(e,n)}function Pc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(i(314))}null!==r&&r.delete(t),jc(e,n)}function Ec(e,t){return Qe(e,t)}function Tc(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ic(e,t,n,r){return new Tc(e,t,n,r)}function Oc(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Fc(e,t){var n=e.alternate;return null===n?((n=Ic(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Rc(e,t,n,r,a,o){var l=2;if(r=e,"function"===typeof e)Oc(e)&&(l=1);else if("string"===typeof e)l=5;else e:switch(e){case S:return Dc(n.children,a,o,t);case A:l=8,a|=8;break;case C:return(e=Ic(12,n,t,2|a)).elementType=C,e.lanes=o,e;case E:return(e=Ic(13,n,t,a)).elementType=E,e.lanes=o,e;case T:return(e=Ic(19,n,t,a)).elementType=T,e.lanes=o,e;case F:return Lc(n,a,o,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case j:l=10;break e;case N:l=9;break e;case P:l=11;break e;case I:l=14;break e;case O:l=16,r=null;break e}throw Error(i(130,null==e?e:typeof e,""))}return(t=Ic(l,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function Dc(e,t,n,r){return(e=Ic(7,e,r,t)).lanes=n,e}function Lc(e,t,n,r){return(e=Ic(22,e,r,t)).elementType=F,e.lanes=n,e.stateNode={isHidden:!1},e}function zc(e,t,n){return(e=Ic(6,e,null,t)).lanes=n,e}function Uc(e,t,n){return(t=Ic(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Mc(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function _c(e,t,n,r,a,i,o,l,s){return e=new Mc(e,t,n,l,s),1===t?(t=1,!0===i&&(t|=8)):t=0,i=Ic(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Li(i),e}function Bc(e){if(!e)return Na;e:{if(He(e=e._reactInternals)!==e||1!==e.tag)throw Error(i(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Oa(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(i(171))}if(1===e.tag){var n=e.type;if(Oa(n))return Da(e,n,t)}return t}function Hc(e,t,n,r,a,i,o,l,s){return(e=_c(n,r,!0,e,0,i,0,l,s)).context=Bc(null),n=e.current,(i=Ui(r=ec(),a=tc(n))).callback=void 0!==t&&null!==t?t:null,Mi(n,i,a),e.current.lanes=a,vt(e,a,r),rc(e,r),e}function Wc(e,t,n,r){var a=t.current,i=ec(),o=tc(a);return n=Bc(n),null===t.context?t.context=n:t.pendingContext=n,(t=Ui(i,o)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Mi(a,t,o))&&(nc(e,a,o,i),_i(e,a,o)),o}function Vc(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function qc(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Xc(e,t){qc(e,t),(e=e.alternate)&&qc(e,t)}Ss=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ea.current)yl=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return yl=!1,function(e,t,n){switch(t.tag){case 3:El(t),pi();break;case 5:Ji(t);break;case 1:Oa(t.type)&&La(t);break;case 4:Gi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;ja(wi,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(ja($i,1&$i.current),t.flags|=128,null):0!==(n&t.child.childLanes)?zl(e,t,n):(ja($i,1&$i.current),null!==(e=Vl(e,t,n))?e.sibling:null);ja($i,1&$i.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Hl(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),ja($i,$i.current),r)break;return null;case 22:case 23:return t.lanes=0,Al(e,t,n)}return Vl(e,t,n)}(e,t,n);yl=0!==(131072&e.flags)}else yl=!1,ai&&0!==(1048576&t.flags)&&$a(t,Xa,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Wl(e,t),e=t.pendingProps;var a=Ia(t,Pa.current);Ei(t,n),a=go(null,t,r,e,a,n);var o=vo();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Oa(r)?(o=!0,La(t)):o=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Li(t),a.updater=al,t.stateNode=a,a._reactInternals=t,sl(t,r,e,n),t=Pl(null,t,r,!0,o,n)):(t.tag=0,ai&&o&&ei(t),xl(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Wl(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Oc(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===P)return 11;if(e===I)return 14}return 2}(r),e=nl(r,e),a){case 0:t=jl(null,t,r,e,n);break e;case 1:t=Nl(null,t,r,e,n);break e;case 11:t=kl(null,t,r,e,n);break e;case 14:t=wl(null,t,r,nl(r.type,e),n);break e}throw Error(i(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,jl(e,t,r,a=t.elementType===r?a:nl(r,a),n);case 1:return r=t.type,a=t.pendingProps,Nl(e,t,r,a=t.elementType===r?a:nl(r,a),n);case 3:e:{if(El(t),null===e)throw Error(i(387));r=t.pendingProps,a=(o=t.memoizedState).element,zi(e,t),Hi(t,r,null,n);var l=t.memoizedState;if(r=l.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Tl(e,t,r,n,a=cl(Error(i(423)),t));break e}if(r!==a){t=Tl(e,t,r,n,a=cl(Error(i(424)),t));break e}for(ri=ca(t.stateNode.containerInfo.firstChild),ni=t,ai=!0,ii=null,n=ki(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pi(),r===a){t=Vl(e,t,n);break e}xl(e,t,r,n)}t=t.child}return t;case 5:return Ji(t),null===e&&ci(t),r=t.type,a=t.pendingProps,o=null!==e?e.memoizedProps:null,l=a.children,na(r,a)?l=null:null!==o&&na(r,o)&&(t.flags|=32),Cl(e,t),xl(e,t,l,n),t.child;case 6:return null===e&&ci(t),null;case 13:return zl(e,t,n);case 4:return Gi(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=xi(t,null,r,n):xl(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,kl(e,t,r,a=t.elementType===r?a:nl(r,a),n);case 7:return xl(e,t,t.pendingProps,n),t.child;case 8:case 12:return xl(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,o=t.memoizedProps,l=a.value,ja(wi,r._currentValue),r._currentValue=l,null!==o)if(lr(o.value,l)){if(o.children===a.children&&!Ea.current){t=Vl(e,t,n);break e}}else for(null!==(o=t.child)&&(o.return=t);null!==o;){var s=o.dependencies;if(null!==s){l=o.child;for(var c=s.firstContext;null!==c;){if(c.context===r){if(1===o.tag){(c=Ui(-1,n&-n)).tag=2;var u=o.updateQueue;if(null!==u){var d=(u=u.shared).pending;null===d?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}o.lanes|=n,null!==(c=o.alternate)&&(c.lanes|=n),Pi(o.return,n,t),s.lanes|=n;break}c=c.next}}else if(10===o.tag)l=o.type===t.type?null:o.child;else if(18===o.tag){if(null===(l=o.return))throw Error(i(341));l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),Pi(l,n,t),l=o.sibling}else l=o.child;if(null!==l)l.return=o;else for(l=o;null!==l;){if(l===t){l=null;break}if(null!==(o=l.sibling)){o.return=l.return,l=o;break}l=l.return}o=l}xl(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Ei(t,n),r=r(a=Ti(a)),t.flags|=1,xl(e,t,r,n),t.child;case 14:return a=nl(r=t.type,t.pendingProps),wl(e,t,r,a=nl(r.type,a),n);case 15:return Sl(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:nl(r,a),Wl(e,t),t.tag=1,Oa(r)?(e=!0,La(t)):e=!1,Ei(t,n),ol(t,r,a),sl(t,r,a,n),Pl(null,t,r,!0,e,n);case 19:return Hl(e,t,n);case 22:return Al(e,t,n)}throw Error(i(156,t.tag))};var Qc="function"===typeof reportError?reportError:function(e){console.error(e)};function Kc(e){this._internalRoot=e}function Gc(e){this._internalRoot=e}function Yc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Jc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zc(){}function $c(e,t,n,r,a){var i=n._reactRootContainer;if(i){var o=i;if("function"===typeof a){var l=a;a=function(){var e=Vc(o);l.call(e)}}Wc(t,o,e,a)}else o=function(e,t,n,r,a){if(a){if("function"===typeof r){var i=r;r=function(){var e=Vc(o);i.call(e)}}var o=Hc(t,r,e,0,null,!1,0,"",Zc);return e._reactRootContainer=o,e[ma]=o.current,Hr(8===e.nodeType?e.parentNode:e),uc(),o}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var l=r;r=function(){var e=Vc(s);l.call(e)}}var s=_c(e,0,!1,null,0,!1,0,"",Zc);return e._reactRootContainer=s,e[ma]=s.current,Hr(8===e.nodeType?e.parentNode:e),uc(function(){Wc(t,s,n,r)}),s}(n,t,e,a,r);return Vc(o)}Gc.prototype.render=Kc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));Wc(e,t,null,null)},Gc.prototype.unmount=Kc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;uc(function(){Wc(null,e,null,null)}),t[ma]=null}},Gc.prototype.unstable_scheduleHydration=function(e){if(e){var t=At();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ft.length&&0!==t&&t<Ft[n].priority;n++);Ft.splice(n,0,e),0===n&&zt(e)}},kt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(bt(t,1|n),rc(t,Je()),0===(6&Ps)&&(Hs=Je()+500,Ha()))}break;case 13:uc(function(){var t=Ri(e,1);if(null!==t){var n=ec();nc(t,e,1,n)}}),Xc(e,1)}},wt=function(e){if(13===e.tag){var t=Ri(e,134217728);if(null!==t)nc(t,e,134217728,ec());Xc(e,134217728)}},St=function(e){if(13===e.tag){var t=tc(e),n=Ri(e,t);if(null!==n)nc(n,e,t,ec());Xc(e,t)}},At=function(){return yt},Ct=function(e,t){var n=yt;try{return yt=e,t()}finally{yt=n}},we=function(e,t,n){switch(t){case"input":if(Z(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=ka(r);if(!a)throw Error(i(90));Q(r),Z(r,a)}}}break;case"textarea":ie(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Pe=cc,Ee=uc;var eu={usingClientEntryPoint:!1,Events:[ya,xa,ka,je,Ne,cc]},tu={findFiberByHostInstance:ba,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nu={bundleType:tu.bundleType,version:tu.version,rendererPackageName:tu.rendererPackageName,rendererConfig:tu.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=qe(e))?null:e.stateNode},findFiberByHostInstance:tu.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ru=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ru.isDisabled&&ru.supportsFiber)try{at=ru.inject(nu),it=ru}catch(ue){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=eu,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Yc(t))throw Error(i(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:w,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Yc(e))throw Error(i(299));var n=!1,r="",a=Qc;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=_c(e,1,!1,null,0,n,0,r,a),e[ma]=t.current,Hr(8===e.nodeType?e.parentNode:e),new Kc(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return e=null===(e=qe(t))?null:e.stateNode},t.flushSync=function(e){return uc(e)},t.hydrate=function(e,t,n){if(!Jc(t))throw Error(i(200));return $c(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Yc(e))throw Error(i(405));var r=null!=n&&n.hydratedSources||null,a=!1,o="",l=Qc;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onRecoverableError&&(l=n.onRecoverableError)),t=Hc(t,null,e,1,null!=n?n:null,a,0,o,l),e[ma]=t.current,Hr(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Gc(t)},t.render=function(e,t,n){if(!Jc(t))throw Error(i(200));return $c(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Jc(e))throw Error(i(40));return!!e._reactRootContainer&&(uc(function(){$c(null,null,e,!1,function(){e._reactRootContainer=null,e[ma]=null})}),!0)},t.unstable_batchedUpdates=cc,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Jc(n))throw Error(i(200));if(null==e||void 0===e._reactInternals)throw Error(i(38));return $c(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},2928:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>u,mockApiService:()=>c});var r=n(2555);const a=n(9323).Xv;let i=[{PortfolioID:2001,PortfolioTitle:"My First Flipbook",PageCount:8,ThumbnailPath:"/images/flipbooks/placeholders/thumbnail-fallback.jpg",CreatedDate:new Date("2024-01-15").toISOString(),LastModifiedDate:new Date("2024-01-20").toISOString(),IsPublished:!0,IsPreferred:!1,UserID:1},{PortfolioID:2002,PortfolioTitle:"Project Portfolio",PageCount:24,ThumbnailPath:"/images/flipbooks/placeholders/thumbnail-fallback.jpg",CreatedDate:new Date("2024-02-01").toISOString(),LastModifiedDate:new Date("2024-02-15").toISOString(),IsPublished:!1,IsPreferred:!0,UserID:1}],o={userId:1,userFolderID:12345,isAuthenticated:!1,isAdminUser:!1,username:"john.doe",email:"<EMAIL>"};const l=[{id:1,message:"Welcome to Flipbook Application!",messageType:"info",isActive:!0,displayOrder:1},{id:2,message:"New features are now available.",messageType:"announcement",isActive:!0,displayOrder:2}],s=[{id:1,key:"MAX_FILE_SIZE",value:"10485760",category:"system",description:"Maximum file upload size in bytes"},{id:2,key:"SUPPORTED_FORMATS",value:"pdf,jpg,png,gif",category:"system",description:"Supported file formats"},{id:3,key:"APP_VERSION",value:"2.1.0",category:"app",description:"Current application version"}];const c=new class{delay(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:800;return new Promise(t=>setTimeout(t,e))}async mockRequest(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return await this.delay(),t?{data:null,success:!1,error:"Mock API error - simulated failure"}:{data:e,success:!0}}async getCurrentUser(){return this.mockRequest(o.isAuthenticated?o:null)}async signIn(e,t){return e&&t?(o.isAuthenticated=!0,this.mockRequest(o)):this.mockRequest(null,!0)}async signOut(){return o.isAuthenticated=!1,this.mockRequest(void 0)}async getMyFlipbooks(){return this.mockRequest(i)}async getPortfolioDetails(e){const t=i.find(t=>t.PortfolioID===e);if(t){const e={PortfolioID:t.PortfolioID,PortfolioTitle:t.PortfolioTitle,PageCount:t.PageCount,PathName:"/Users/".concat(o.userFolderID,"/Flipbooks/").concat(t.PortfolioID,"/"),CreatedOn:t.CreatedDate,LastModifiedOn:t.LastModifiedDate,IsPublished:t.IsPublished,IsPreferred:t.IsPreferred};return this.mockRequest(e)}return this.mockRequest(null,!0)}async createNewFlipbook(e){const t=Math.max(...i.map(e=>e.PortfolioID))+1,n={PortfolioID:t,PortfolioTitle:e.title,PageCount:1,ThumbnailPath:"/images/flipbooks/placeholders/thumbnail-fallback.jpg",CreatedDate:(new Date).toISOString(),LastModifiedDate:(new Date).toISOString(),IsPublished:!1,IsPreferred:!1,UserID:o.userId};return i.push(n),this.mockRequest(t)}async copyInspirationFlipbook(e){const t=a.find(t=>t.PortfolioID===e.portfolioId);if(t){const n=Math.max(...i.map(e=>e.PortfolioID))+1,r={PortfolioID:n,PortfolioTitle:e.title,PageCount:t.PageCount,ThumbnailPath:t.TnImageSrc,CreatedDate:(new Date).toISOString(),LastModifiedDate:(new Date).toISOString(),IsPublished:!1,IsPreferred:!1,UserID:o.userId};return i.push(r),this.mockRequest(n)}return this.mockRequest(null,!0)}async updateFlipbook(e){const t=i.findIndex(t=>t.PortfolioID===e.portfolioId);return-1!==t?(i[t]=(0,r.A)((0,r.A)({},i[t]),{},{PortfolioTitle:e.title,LastModifiedDate:(new Date).toISOString()},void 0!==e.isPreferred&&{IsPreferred:e.isPreferred}),this.mockRequest(!0)):this.mockRequest(!1,!0)}async deleteFlipbook(e){const t=i.findIndex(t=>t.PortfolioID===e);return-1!==t?(i.splice(t,1),this.mockRequest(!0)):this.mockRequest(!1,!0)}async setFlipbookPreferred(e,t){return this.updateFlipbook({portfolioId:e,title:"",isPreferred:t})}getThumbnailUrl(e,t){if(arguments.length>2&&void 0!==arguments[2]&&arguments[2]){const t=a.find(t=>t.PortfolioID===e);return(null===t||void 0===t?void 0:t.TnImageSrc)||"/images/flipbooks/placeholders/thumbnail-fallback.jpg"}const n=i.find(t=>t.PortfolioID===e);return(null===n||void 0===n?void 0:n.ThumbnailPath)||"/images/flipbooks/placeholders/thumbnail-fallback.jpg"}getFlipbookHtmlPath(e,t){const n=arguments.length>2&&void 0!==arguments[2]&&arguments[2]?"999999":t.toString();return"/Users/".concat(n,"/Flipbooks/").concat(e,"/HTML/")}async getUIMessages(){return this.mockRequest(l)}async getPortfolioDetailsInitial(){return this.mockRequest([])}async getCommonValues(){return this.mockRequest(s)}async getInspirationFlipbooks(){return this.mockRequest(a)}},u=c},3763:(e,t,n)=>{"use strict";e.exports=n(4983)},4202:(e,t)=>{"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}function b(){}function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},b.prototype=v.prototype;var x=y.prototype=new b;x.constructor=y,h(x,v.prototype),x.isPureReactComponent=!0;var k=Array.isArray,w=Object.prototype.hasOwnProperty,S={current:null},A={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,r){var a,i={},o=null,l=null;if(null!=t)for(a in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(o=""+t.key),t)w.call(t,a)&&!A.hasOwnProperty(a)&&(i[a]=t[a]);var s=arguments.length-2;if(1===s)i.children=r;else if(1<s){for(var c=Array(s),u=0;u<s;u++)c[u]=arguments[u+2];i.children=c}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===i[a]&&(i[a]=s[a]);return{$$typeof:n,type:e,key:o,ref:l,props:i,_owner:S.current}}function j(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var N=/\/+/g;function P(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function E(e,t,a,i,o){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var s=!1;if(null===e)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return o=o(s=e),e=""===i?"."+P(s,0):i,k(o)?(a="",null!=e&&(a=e.replace(N,"$&/")+"/"),E(o,t,a,"",function(e){return e})):null!=o&&(j(o)&&(o=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(o,a+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(N,"$&/")+"/")+e)),t.push(o)),1;if(s=0,i=""===i?".":i+":",k(e))for(var c=0;c<e.length;c++){var u=i+P(l=e[c],c);s+=E(l,t,a,u,o)}else if(u=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof u)for(e=u.call(e),c=0;!(l=e.next()).done;)s+=E(l=l.value,t,a,u=i+P(l,c++),o);else if("object"===l)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function T(e,t,n){if(null==e)return e;var r=[],a=0;return E(e,r,"","",function(e){return t.call(n,e,a++)}),r}function I(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var O={current:null},F={transition:null},R={ReactCurrentDispatcher:O,ReactCurrentBatchConfig:F,ReactCurrentOwner:S};function D(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:T,forEach:function(e,t,n){T(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return T(e,function(){t++}),t},toArray:function(e){return T(e,function(e){return e})||[]},only:function(e){if(!j(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=o,t.PureComponent=y,t.StrictMode=i,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=R,t.act=D,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=h({},e.props),i=e.key,o=e.ref,l=e._owner;if(null!=t){if(void 0!==t.ref&&(o=t.ref,l=S.current),void 0!==t.key&&(i=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(c in t)w.call(t,c)&&!A.hasOwnProperty(c)&&(a[c]=void 0===t[c]&&void 0!==s?s[c]:t[c])}var c=arguments.length-2;if(1===c)a.children=r;else if(1<c){s=Array(c);for(var u=0;u<c;u++)s[u]=arguments[u+2];a.children=s}return{$$typeof:n,type:e.type,key:i,ref:o,props:a,_owner:l}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:l,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=j,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:I}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=F.transition;F.transition={};try{e()}finally{F.transition=t}},t.unstable_act=D,t.useCallback=function(e,t){return O.current.useCallback(e,t)},t.useContext=function(e){return O.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return O.current.useDeferredValue(e)},t.useEffect=function(e,t){return O.current.useEffect(e,t)},t.useId=function(){return O.current.useId()},t.useImperativeHandle=function(e,t,n){return O.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return O.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return O.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return O.current.useMemo(e,t)},t.useReducer=function(e,t,n){return O.current.useReducer(e,t,n)},t.useRef=function(e){return O.current.useRef(e)},t.useState=function(e){return O.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return O.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return O.current.useTransition()},t.version="18.3.1"},4391:(e,t,n)=>{"use strict";var r=n(7950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},4983:(e,t)=>{"use strict";var n="function"===typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,a=n?Symbol.for("react.portal"):60106,i=n?Symbol.for("react.fragment"):60107,o=n?Symbol.for("react.strict_mode"):60108,l=n?Symbol.for("react.profiler"):60114,s=n?Symbol.for("react.provider"):60109,c=n?Symbol.for("react.context"):60110,u=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,p=n?Symbol.for("react.suspense"):60113,m=n?Symbol.for("react.suspense_list"):60120,h=n?Symbol.for("react.memo"):60115,g=n?Symbol.for("react.lazy"):60116,v=n?Symbol.for("react.block"):60121,b=n?Symbol.for("react.fundamental"):60117,y=n?Symbol.for("react.responder"):60118,x=n?Symbol.for("react.scope"):60119;function k(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case u:case d:case i:case l:case o:case p:return e;default:switch(e=e&&e.$$typeof){case c:case f:case g:case h:case s:return e;default:return t}}case a:return t}}}function w(e){return k(e)===d}t.AsyncMode=u,t.ConcurrentMode=d,t.ContextConsumer=c,t.ContextProvider=s,t.Element=r,t.ForwardRef=f,t.Fragment=i,t.Lazy=g,t.Memo=h,t.Portal=a,t.Profiler=l,t.StrictMode=o,t.Suspense=p,t.isAsyncMode=function(e){return w(e)||k(e)===u},t.isConcurrentMode=w,t.isContextConsumer=function(e){return k(e)===c},t.isContextProvider=function(e){return k(e)===s},t.isElement=function(e){return"object"===typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return k(e)===f},t.isFragment=function(e){return k(e)===i},t.isLazy=function(e){return k(e)===g},t.isMemo=function(e){return k(e)===h},t.isPortal=function(e){return k(e)===a},t.isProfiler=function(e){return k(e)===l},t.isStrictMode=function(e){return k(e)===o},t.isSuspense=function(e){return k(e)===p},t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===i||e===d||e===l||e===o||e===p||e===m||"object"===typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===h||e.$$typeof===s||e.$$typeof===c||e.$$typeof===f||e.$$typeof===b||e.$$typeof===y||e.$$typeof===x||e.$$typeof===v)},t.typeOf=k},5043:(e,t,n)=>{"use strict";e.exports=n(4202)},5082:(e,t)=>{"use strict";var n=60103,r=60106,a=60107,i=60108,o=60114,l=60109,s=60110,c=60112,u=60113,d=60120,f=60115,p=60116,m=60121,h=60122,g=60117,v=60129,b=60131;if("function"===typeof Symbol&&Symbol.for){var y=Symbol.for;n=y("react.element"),r=y("react.portal"),a=y("react.fragment"),i=y("react.strict_mode"),o=y("react.profiler"),l=y("react.provider"),s=y("react.context"),c=y("react.forward_ref"),u=y("react.suspense"),d=y("react.suspense_list"),f=y("react.memo"),p=y("react.lazy"),m=y("react.block"),h=y("react.server.block"),g=y("react.fundamental"),v=y("react.debug_trace_mode"),b=y("react.legacy_hidden")}function x(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case a:case o:case i:case u:case d:return e;default:switch(e=e&&e.$$typeof){case s:case c:case p:case f:case l:return e;default:return t}}case r:return t}}}t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===a||e===o||e===v||e===i||e===u||e===d||e===b||"object"===typeof e&&null!==e&&(e.$$typeof===p||e.$$typeof===f||e.$$typeof===l||e.$$typeof===s||e.$$typeof===c||e.$$typeof===g||e.$$typeof===m||e[0]===h)},t.typeOf=x},7234:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<i(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var l=2*(r+1)-1,s=e[l],c=l+1,u=e[c];if(0>i(s,n))c<a&&0>i(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[l]=n,r=l);else{if(!(c<a&&0>i(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var l=Date,s=l.now();t.unstable_now=function(){return l.now()-s}}var c=[],u=[],d=1,f=null,p=3,m=!1,h=!1,g=!1,v="function"===typeof setTimeout?setTimeout:null,b="function"===typeof clearTimeout?clearTimeout:null,y="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(u);null!==t;){if(null===t.callback)a(u);else{if(!(t.startTime<=e))break;a(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function k(e){if(g=!1,x(e),!h)if(null!==r(c))h=!0,F(w);else{var t=r(u);null!==t&&R(k,t.startTime-e)}}function w(e,n){h=!1,g&&(g=!1,b(j),j=-1),m=!0;var i=p;try{for(x(n),f=r(c);null!==f&&(!(f.expirationTime>n)||e&&!E());){var o=f.callback;if("function"===typeof o){f.callback=null,p=f.priorityLevel;var l=o(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof l?f.callback=l:f===r(c)&&a(c),x(n)}else a(c);f=r(c)}if(null!==f)var s=!0;else{var d=r(u);null!==d&&R(k,d.startTime-n),s=!1}return s}finally{f=null,p=i,m=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,A=!1,C=null,j=-1,N=5,P=-1;function E(){return!(t.unstable_now()-P<N)}function T(){if(null!==C){var e=t.unstable_now();P=e;var n=!0;try{n=C(!0,e)}finally{n?S():(A=!1,C=null)}}else A=!1}if("function"===typeof y)S=function(){y(T)};else if("undefined"!==typeof MessageChannel){var I=new MessageChannel,O=I.port2;I.port1.onmessage=T,S=function(){O.postMessage(null)}}else S=function(){v(T,0)};function F(e){C=e,A||(A=!0,S())}function R(e,n){j=v(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){h||m||(h=!0,F(w))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):N=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,i){var o=t.unstable_now();switch("object"===typeof i&&null!==i?i="number"===typeof(i=i.delay)&&0<i?o+i:o:i=o,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=1073741823;break;case 4:l=1e4;break;default:l=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:i,expirationTime:l=i+l,sortIndex:-1},i>o?(e.sortIndex=i,n(u,e),null===r(c)&&e===r(u)&&(g?(b(j),j=-1):g=!0,R(k,i-o))):(e.sortIndex=l,n(c,e),h||m||(h=!0,F(w))),e},t.unstable_shouldYield=E,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},7324:e=>{e.exports=function(e,t,n,r){var a=n?n.call(r,e,t):void 0;if(void 0!==a)return!!a;if(e===t)return!0;if("object"!==typeof e||!e||"object"!==typeof t||!t)return!1;var i=Object.keys(e),o=Object.keys(t);if(i.length!==o.length)return!1;for(var l=Object.prototype.hasOwnProperty.bind(t),s=0;s<i.length;s++){var c=i[s];if(!l(c))return!1;var u=e[c],d=t[c];if(!1===(a=n?n.call(r,u,d,c):void 0)||void 0===a&&u!==d)return!1}return!0}},7950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(2730)},8853:(e,t,n)=>{"use strict";e.exports=n(7234)},9323:(e,t,n)=>{"use strict";n.d(t,{Xv:()=>i,tS:()=>a});var r=n(2398);const a={FBTitle:"Original Flipbook",PortfolioID:2154,PageCount:26,TnImageSrc:r.Ay.resumeInspiration.portfolio2.page1},i=[a,{FBTitle:"Architecture Portfolio",PortfolioID:1002,PageCount:16,TnImageSrc:r.Ay.architecture.grimshawBeyerBlinder},{FBTitle:"Design Showcase",PortfolioID:1003,PageCount:20,TnImageSrc:r.Ay.architecture.shingleHouse}]}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.m=e,n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var i=Object.create(null);n.r(i);var o={};e=e||[null,t({}),t([]),t(t)];for(var l=2&a&&r;("object"==typeof l||"function"==typeof l)&&!~e.indexOf(l);l=t(l))Object.getOwnPropertyNames(l).forEach(e=>o[e]=()=>r[e]);return o.default=()=>r,n.d(i,o),i}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,r)=>(n.f[r](e,t),t),[])),n.u=e=>"static/js/"+e+".25f5046d.chunk.js",n.miniCssF=e=>{},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="flipbook-react:";n.l=(r,a,i,o)=>{if(e[r])e[r].push(a);else{var l,s;if(void 0!==i)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+i){l=d;break}}l||(s=!0,(l=document.createElement("script")).charset="utf-8",l.timeout=120,n.nc&&l.setAttribute("nonce",n.nc),l.setAttribute("data-webpack",t+i),l.src=r),e[r]=[a];var f=(t,n)=>{l.onerror=l.onload=null,clearTimeout(p);var a=e[r];if(delete e[r],l.parentNode&&l.parentNode.removeChild(l),a&&a.forEach(e=>e(n)),t)return t(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:l}),12e4);l.onerror=f.bind(null,l.onerror),l.onload=f.bind(null,l.onload),s&&document.head.appendChild(l)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{var e={792:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var i=new Promise((n,r)=>a=e[t]=[n,r]);r.push(a[2]=i);var o=n.p+n.u(t),l=new Error;n.l(o,r=>{if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var i=r&&("load"===r.type?"missing":r.type),o=r&&r.target&&r.target.src;l.message="Loading chunk "+t+" failed.\n("+i+": "+o+")",l.name="ChunkLoadError",l.type=i,l.request=o,a[1](l)}},"chunk-"+t,t)}};var t=(t,r)=>{var a,i,o=r[0],l=r[1],s=r[2],c=0;if(o.some(t=>0!==e[t])){for(a in l)n.o(l,a)&&(n.m[a]=l[a]);if(s)s(n)}for(t&&t(r);c<o.length;c++)i=o[c],n.o(e,i)&&e[i]&&e[i][0](),e[i]=0},r=self.webpackChunkflipbook_react=self.webpackChunkflipbook_react||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),n.nc=void 0,(()=>{"use strict";var e,t=n(5043),r=n.t(t,2),a=n(4391),i=n(7950),o=n.t(i,2);function l(){return l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},l.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(e||(e={}));const s="popstate";function c(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function u(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function d(e,t){return{usr:e.state,key:e.key,idx:t}}function f(e,t,n,r){return void 0===n&&(n=null),l({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?m(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function p(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function m(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function h(t,n,r,a){void 0===a&&(a={});let{window:i=document.defaultView,v5Compat:o=!1}=a,u=i.history,m=e.Pop,h=null,g=v();function v(){return(u.state||{idx:null}).idx}function b(){m=e.Pop;let t=v(),n=null==t?null:t-g;g=t,h&&h({action:m,location:x.location,delta:n})}function y(e){let t="null"!==i.location.origin?i.location.origin:i.location.href,n="string"===typeof e?e:p(e);return n=n.replace(/ $/,"%20"),c(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==g&&(g=0,u.replaceState(l({},u.state,{idx:g}),""));let x={get action(){return m},get location(){return t(i,u)},listen(e){if(h)throw new Error("A history only accepts one active listener");return i.addEventListener(s,b),h=e,()=>{i.removeEventListener(s,b),h=null}},createHref:e=>n(i,e),createURL:y,encodeLocation(e){let t=y(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(t,n){m=e.Push;let a=f(x.location,t,n);r&&r(a,t),g=v()+1;let l=d(a,g),s=x.createHref(a);try{u.pushState(l,"",s)}catch(c){if(c instanceof DOMException&&"DataCloneError"===c.name)throw c;i.location.assign(s)}o&&h&&h({action:m,location:x.location,delta:1})},replace:function(t,n){m=e.Replace;let a=f(x.location,t,n);r&&r(a,t),g=v();let i=d(a,g),l=x.createHref(a);u.replaceState(i,"",l),o&&h&&h({action:m,location:x.location,delta:0})},go:e=>u.go(e)};return x}var g;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(g||(g={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function v(e,t,n){return void 0===n&&(n="/"),b(e,t,n,!1)}function b(e,t,n,r){let a=O(("string"===typeof t?m(t):t).pathname||"/",n);if(null==a)return null;let i=y(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(i);let o=null;for(let l=0;null==o&&l<i.length;++l){let e=I(a);o=E(i[l],e,r)}return o}function y(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,i)=>{let o={relativePath:void 0===i?e.path||"":i,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};o.relativePath.startsWith("/")&&(c(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),o.relativePath=o.relativePath.slice(r.length));let l=z([r,o.relativePath]),s=n.concat(o);e.children&&e.children.length>0&&(c(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+l+'".'),y(e.children,t,s,l)),(null!=e.path||e.index)&&t.push({path:l,score:P(l,e.index),routesMeta:s})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of x(e.path))a(e,t,r);else a(e,t)}),t}function x(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),i=n.replace(/\?$/,"");if(0===r.length)return a?[i,""]:[i];let o=x(r.join("/")),l=[];return l.push(...o.map(e=>""===e?i:[i,e].join("/"))),a&&l.push(...o),l.map(t=>e.startsWith("/")&&""===t?"/":t)}const k=/^:[\w-]+$/,w=3,S=2,A=1,C=10,j=-2,N=e=>"*"===e;function P(e,t){let n=e.split("/"),r=n.length;return n.some(N)&&(r+=j),t&&(r+=S),n.filter(e=>!N(e)).reduce((e,t)=>e+(k.test(t)?w:""===t?A:C),r)}function E(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},i="/",o=[];for(let l=0;l<r.length;++l){let e=r[l],s=l===r.length-1,c="/"===i?t:t.slice(i.length)||"/",u=T({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},c),d=e.route;if(!u&&s&&n&&!r[r.length-1].route.index&&(u=T({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},c)),!u)return null;Object.assign(a,u.params),o.push({params:a,pathname:z([i,u.pathname]),pathnameBase:U(z([i,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(i=z([i,u.pathnameBase]))}return o}function T(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);u("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let i=new RegExp(a,t?void 0:"i");return[i,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let i=a[0],o=i.replace(/(.)\/+$/,"$1"),l=a.slice(1);return{params:r.reduce((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=l[n]||"";o=i.slice(0,i.length-e.length).replace(/(.)\/+$/,"$1")}const s=l[n];return e[r]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e},{}),pathname:i,pathnameBase:o,pattern:e}}function I(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return u(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function O(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function F(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function R(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function D(e,t){let n=R(e);return t?n.map((e,t)=>t===n.length-1?e.pathname:e.pathnameBase):n.map(e=>e.pathnameBase)}function L(e,t,n,r){let a;void 0===r&&(r=!1),"string"===typeof e?a=m(e):(a=l({},e),c(!a.pathname||!a.pathname.includes("?"),F("?","pathname","search",a)),c(!a.pathname||!a.pathname.includes("#"),F("#","pathname","hash",a)),c(!a.search||!a.search.includes("#"),F("#","search","hash",a)));let i,o=""===e||""===a.pathname,s=o?"/":a.pathname;if(null==s)i=n;else{let e=t.length-1;if(!r&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}i=e>=0?t[e]:"/"}let u=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"===typeof e?m(e):e,i=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:i,search:M(r),hash:_(a)}}(a,i),d=s&&"/"!==s&&s.endsWith("/"),f=(o||"."===s)&&n.endsWith("/");return u.pathname.endsWith("/")||!d&&!f||(u.pathname+="/"),u}const z=e=>e.join("/").replace(/\/\/+/g,"/"),U=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),M=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",_=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;function B(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const H=["post","put","patch","delete"],W=(new Set(H),["get",...H]);new Set(W),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function V(){return V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},V.apply(this,arguments)}const q=t.createContext(null);const X=t.createContext(null);const Q=t.createContext(null);const K=t.createContext(null);const G=t.createContext({outlet:null,matches:[],isDataRoute:!1});const Y=t.createContext(null);function J(){return null!=t.useContext(K)}function Z(){return J()||c(!1),t.useContext(K).location}function $(e){t.useContext(Q).static||t.useLayoutEffect(e)}function ee(){let{isDataRoute:e}=t.useContext(G);return e?function(){let{router:e}=ue(se.UseNavigateStable),n=fe(ce.UseNavigateStable),r=t.useRef(!1);return $(()=>{r.current=!0}),t.useCallback(function(t,a){void 0===a&&(a={}),r.current&&("number"===typeof t?e.navigate(t):e.navigate(t,V({fromRouteId:n},a)))},[e,n])}():function(){J()||c(!1);let e=t.useContext(q),{basename:n,future:r,navigator:a}=t.useContext(Q),{matches:i}=t.useContext(G),{pathname:o}=Z(),l=JSON.stringify(D(i,r.v7_relativeSplatPath)),s=t.useRef(!1);return $(()=>{s.current=!0}),t.useCallback(function(t,r){if(void 0===r&&(r={}),!s.current)return;if("number"===typeof t)return void a.go(t);let i=L(t,JSON.parse(l),o,"path"===r.relative);null==e&&"/"!==n&&(i.pathname="/"===i.pathname?n:z([n,i.pathname])),(r.replace?a.replace:a.push)(i,r.state,r)},[n,a,l,o,e])}()}function te(){let{matches:e}=t.useContext(G),n=e[e.length-1];return n?n.params:{}}function ne(n,r,a,i){J()||c(!1);let{navigator:o}=t.useContext(Q),{matches:l}=t.useContext(G),s=l[l.length-1],u=s?s.params:{},d=(s&&s.pathname,s?s.pathnameBase:"/");s&&s.route;let f,p=Z();if(r){var h;let e="string"===typeof r?m(r):r;"/"===d||(null==(h=e.pathname)?void 0:h.startsWith(d))||c(!1),f=e}else f=p;let g=f.pathname||"/",b=g;if("/"!==d){let e=d.replace(/^\//,"").split("/");b="/"+g.replace(/^\//,"").split("/").slice(e.length).join("/")}let y=v(n,{pathname:b});let x=le(y&&y.map(e=>Object.assign({},e,{params:Object.assign({},u,e.params),pathname:z([d,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?d:z([d,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),l,a,i);return r&&x?t.createElement(K.Provider,{value:{location:V({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:e.Pop}},x):x}function re(){let e=function(){var e;let n=t.useContext(Y),r=de(ce.UseRouteError),a=fe(ce.UseRouteError);if(void 0!==n)return n;return null==(e=r.errors)?void 0:e[a]}(),n=B(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",i={padding:"0.5rem",backgroundColor:a};return t.createElement(t.Fragment,null,t.createElement("h2",null,"Unexpected Application Error!"),t.createElement("h3",{style:{fontStyle:"italic"}},n),r?t.createElement("pre",{style:i},r):null,null)}const ae=t.createElement(re,null);class ie extends t.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?t.createElement(G.Provider,{value:this.props.routeContext},t.createElement(Y.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function oe(e){let{routeContext:n,match:r,children:a}=e,i=t.useContext(q);return i&&i.static&&i.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=r.route.id),t.createElement(G.Provider,{value:n},a)}function le(e,n,r,a){var i;if(void 0===n&&(n=[]),void 0===r&&(r=null),void 0===a&&(a=null),null==e){var o;if(!r)return null;if(r.errors)e=r.matches;else{if(!(null!=(o=a)&&o.v7_partialHydration&&0===n.length&&!r.initialized&&r.matches.length>0))return null;e=r.matches}}let l=e,s=null==(i=r)?void 0:i.errors;if(null!=s){let e=l.findIndex(e=>e.route.id&&void 0!==(null==s?void 0:s[e.route.id]));e>=0||c(!1),l=l.slice(0,Math.min(l.length,e+1))}let u=!1,d=-1;if(r&&a&&a.v7_partialHydration)for(let t=0;t<l.length;t++){let e=l[t];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(d=t),e.route.id){let{loaderData:t,errors:n}=r,a=e.route.loader&&void 0===t[e.route.id]&&(!n||void 0===n[e.route.id]);if(e.route.lazy||a){u=!0,l=d>=0?l.slice(0,d+1):[l[0]];break}}}return l.reduceRight((e,a,i)=>{let o,c=!1,f=null,p=null;var m;r&&(o=s&&a.route.id?s[a.route.id]:void 0,f=a.route.errorElement||ae,u&&(d<0&&0===i?(m="route-fallback",!1||pe[m]||(pe[m]=!0),c=!0,p=null):d===i&&(c=!0,p=a.route.hydrateFallbackElement||null)));let h=n.concat(l.slice(0,i+1)),g=()=>{let n;return n=o?f:c?p:a.route.Component?t.createElement(a.route.Component,null):a.route.element?a.route.element:e,t.createElement(oe,{match:a,routeContext:{outlet:e,matches:h,isDataRoute:null!=r},children:n})};return r&&(a.route.ErrorBoundary||a.route.errorElement||0===i)?t.createElement(ie,{location:r.location,revalidation:r.revalidation,component:f,error:o,children:g(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):g()},null)}var se=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(se||{}),ce=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ce||{});function ue(e){let n=t.useContext(q);return n||c(!1),n}function de(e){let n=t.useContext(X);return n||c(!1),n}function fe(e){let n=function(){let e=t.useContext(G);return e||c(!1),e}(),r=n.matches[n.matches.length-1];return r.route.id||c(!1),r.route.id}const pe={};function me(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}r.startTransition;function he(e){let{to:n,replace:r,state:a,relative:i}=e;J()||c(!1);let{future:o,static:l}=t.useContext(Q),{matches:s}=t.useContext(G),{pathname:u}=Z(),d=ee(),f=L(n,D(s,o.v7_relativeSplatPath),u,"path"===i),p=JSON.stringify(f);return t.useEffect(()=>d(JSON.parse(p),{replace:r,state:a,relative:i}),[d,p,i,r,a]),null}function ge(e){c(!1)}function ve(n){let{basename:r="/",children:a=null,location:i,navigationType:o=e.Pop,navigator:l,static:s=!1,future:u}=n;J()&&c(!1);let d=r.replace(/^\/*/,"/"),f=t.useMemo(()=>({basename:d,navigator:l,static:s,future:V({v7_relativeSplatPath:!1},u)}),[d,u,l,s]);"string"===typeof i&&(i=m(i));let{pathname:p="/",search:h="",hash:g="",state:v=null,key:b="default"}=i,y=t.useMemo(()=>{let e=O(p,d);return null==e?null:{location:{pathname:e,search:h,hash:g,state:v,key:b},navigationType:o}},[d,p,h,g,v,b,o]);return null==y?null:t.createElement(Q.Provider,{value:f},t.createElement(K.Provider,{children:a,value:y}))}function be(e){let{children:t,location:n}=e;return ne(ye(t),n)}new Promise(()=>{});t.Component;function ye(e,n){void 0===n&&(n=[]);let r=[];return t.Children.forEach(e,(e,a)=>{if(!t.isValidElement(e))return;let i=[...n,a];if(e.type===t.Fragment)return void r.push.apply(r,ye(e.props.children,i));e.type!==ge&&c(!1),e.props.index&&e.props.children&&c(!1);let o={id:e.props.id||i.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(o.children=ye(e.props.children,i)),r.push(o)}),r}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);try{window.__reactRouterVersion="6"}catch(xa){}new Map;const xe=r.startTransition;o.flushSync,r.useId;function ke(e){let{basename:n,children:r,future:a,window:i}=e,o=t.useRef();var l;null==o.current&&(o.current=(void 0===(l={window:i,v5Compat:!0})&&(l={}),h(function(e,t){let{pathname:n,search:r,hash:a}=e.location;return f("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"===typeof t?t:p(t)},null,l)));let s=o.current,[c,u]=t.useState({action:s.action,location:s.location}),{v7_startTransition:d}=a||{},m=t.useCallback(e=>{d&&xe?xe(()=>u(e)):u(e)},[u,d]);return t.useLayoutEffect(()=>s.listen(m),[s,m]),t.useEffect(()=>me(a),[a]),t.createElement(ve,{basename:n,children:r,location:c.location,navigationType:c.action,navigator:s,future:a})}"undefined"!==typeof window&&"undefined"!==typeof window.document&&window.document.createElement;var we,Se;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(we||(we={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(Se||(Se={}));function Ae(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}var Ce=n(2086),je=n(7324),Ne=n.n(je);const Pe=function(e){function t(e,r,s,c,f){for(var p,m,h,g,x,w=0,S=0,A=0,C=0,j=0,O=0,R=h=p=0,L=0,z=0,U=0,M=0,_=s.length,B=_-1,H="",W="",V="",q="";L<_;){if(m=s.charCodeAt(L),L===B&&0!==S+C+A+w&&(0!==S&&(m=47===S?10:47),C=A=w=0,_++,B++),0===S+C+A+w){if(L===B&&(0<z&&(H=H.replace(d,"")),0<H.trim().length)){switch(m){case 32:case 9:case 59:case 13:case 10:break;default:H+=s.charAt(L)}m=59}switch(m){case 123:for(p=(H=H.trim()).charCodeAt(0),h=1,M=++L;L<_;){switch(m=s.charCodeAt(L)){case 123:h++;break;case 125:h--;break;case 47:switch(m=s.charCodeAt(L+1)){case 42:case 47:e:{for(R=L+1;R<B;++R)switch(s.charCodeAt(R)){case 47:if(42===m&&42===s.charCodeAt(R-1)&&L+2!==R){L=R+1;break e}break;case 10:if(47===m){L=R+1;break e}}L=R}}break;case 91:m++;case 40:m++;case 34:case 39:for(;L++<B&&s.charCodeAt(L)!==m;);}if(0===h)break;L++}if(h=s.substring(M,L),0===p&&(p=(H=H.replace(u,"").trim()).charCodeAt(0)),64===p){switch(0<z&&(H=H.replace(d,"")),m=H.charCodeAt(1)){case 100:case 109:case 115:case 45:z=r;break;default:z=I}if(M=(h=t(r,z,h,m,f+1)).length,0<F&&(x=l(3,h,z=n(I,H,U),r,P,N,M,m,f,c),H=z.join(""),void 0!==x&&0===(M=(h=x.trim()).length)&&(m=0,h="")),0<M)switch(m){case 115:H=H.replace(k,o);case 100:case 109:case 45:h=H+"{"+h+"}";break;case 107:h=(H=H.replace(v,"$1 $2"))+"{"+h+"}",h=1===T||2===T&&i("@"+h,3)?"@-webkit-"+h+"@"+h:"@"+h;break;default:h=H+h,112===c&&(W+=h,h="")}else h=""}else h=t(r,n(r,H,U),h,c,f+1);V+=h,h=U=z=R=p=0,H="",m=s.charCodeAt(++L);break;case 125:case 59:if(1<(M=(H=(0<z?H.replace(d,""):H).trim()).length))switch(0===R&&(p=H.charCodeAt(0),45===p||96<p&&123>p)&&(M=(H=H.replace(" ",":")).length),0<F&&void 0!==(x=l(1,H,r,e,P,N,W.length,c,f,c))&&0===(M=(H=x.trim()).length)&&(H="\0\0"),p=H.charCodeAt(0),m=H.charCodeAt(1),p){case 0:break;case 64:if(105===m||99===m){q+=H+s.charAt(L);break}default:58!==H.charCodeAt(M-1)&&(W+=a(H,p,m,H.charCodeAt(2)))}U=z=R=p=0,H="",m=s.charCodeAt(++L)}}switch(m){case 13:case 10:47===S?S=0:0===1+p&&107!==c&&0<H.length&&(z=1,H+="\0"),0<F*D&&l(0,H,r,e,P,N,W.length,c,f,c),N=1,P++;break;case 59:case 125:if(0===S+C+A+w){N++;break}default:switch(N++,g=s.charAt(L),m){case 9:case 32:if(0===C+w+S)switch(j){case 44:case 58:case 9:case 32:g="";break;default:32!==m&&(g=" ")}break;case 0:g="\\0";break;case 12:g="\\f";break;case 11:g="\\v";break;case 38:0===C+S+w&&(z=U=1,g="\f"+g);break;case 108:if(0===C+S+w+E&&0<R)switch(L-R){case 2:112===j&&58===s.charCodeAt(L-3)&&(E=j);case 8:111===O&&(E=O)}break;case 58:0===C+S+w&&(R=L);break;case 44:0===S+A+C+w&&(z=1,g+="\r");break;case 34:case 39:0===S&&(C=C===m?0:0===C?m:C);break;case 91:0===C+S+A&&w++;break;case 93:0===C+S+A&&w--;break;case 41:0===C+S+w&&A--;break;case 40:if(0===C+S+w){if(0===p)if(2*j+3*O===533);else p=1;A++}break;case 64:0===S+A+C+w+R+h&&(h=1);break;case 42:case 47:if(!(0<C+w+A))switch(S){case 0:switch(2*m+3*s.charCodeAt(L+1)){case 235:S=47;break;case 220:M=L,S=42}break;case 42:47===m&&42===j&&M+2!==L&&(33===s.charCodeAt(M+2)&&(W+=s.substring(M,L+1)),g="",S=0)}}0===S&&(H+=g)}O=j,j=m,L++}if(0<(M=W.length)){if(z=r,0<F&&(void 0!==(x=l(2,W,z,e,P,N,M,c,f,c))&&0===(W=x).length))return q+W+V;if(W=z.join(",")+"{"+W+"}",0!==T*E){switch(2!==T||i(W,2)||(E=0),E){case 111:W=W.replace(y,":-moz-$1")+W;break;case 112:W=W.replace(b,"::-webkit-input-$1")+W.replace(b,"::-moz-$1")+W.replace(b,":-ms-input-$1")+W}E=0}}return q+W+V}function n(e,t,n){var a=t.trim().split(h);t=a;var i=a.length,o=e.length;switch(o){case 0:case 1:var l=0;for(e=0===o?"":e[0]+" ";l<i;++l)t[l]=r(e,t[l],n).trim();break;default:var s=l=0;for(t=[];l<i;++l)for(var c=0;c<o;++c)t[s++]=r(e[c]+" ",a[l],n).trim()}return t}function r(e,t,n){var r=t.charCodeAt(0);switch(33>r&&(r=(t=t.trim()).charCodeAt(0)),r){case 38:return t.replace(g,"$1"+e.trim());case 58:return e.trim()+t.replace(g,"$1"+e.trim());default:if(0<1*n&&0<t.indexOf("\f"))return t.replace(g,(58===e.charCodeAt(0)?"":"$1")+e.trim())}return e+t}function a(e,t,n,r){var o=e+";",l=2*t+3*n+4*r;if(944===l){e=o.indexOf(":",9)+1;var s=o.substring(e,o.length-1).trim();return s=o.substring(0,e).trim()+s+";",1===T||2===T&&i(s,1)?"-webkit-"+s+s:s}if(0===T||2===T&&!i(o,1))return o;switch(l){case 1015:return 97===o.charCodeAt(10)?"-webkit-"+o+o:o;case 951:return 116===o.charCodeAt(3)?"-webkit-"+o+o:o;case 963:return 110===o.charCodeAt(5)?"-webkit-"+o+o:o;case 1009:if(100!==o.charCodeAt(4))break;case 969:case 942:return"-webkit-"+o+o;case 978:return"-webkit-"+o+"-moz-"+o+o;case 1019:case 983:return"-webkit-"+o+"-moz-"+o+"-ms-"+o+o;case 883:if(45===o.charCodeAt(8))return"-webkit-"+o+o;if(0<o.indexOf("image-set(",11))return o.replace(j,"$1-webkit-$2")+o;break;case 932:if(45===o.charCodeAt(4))switch(o.charCodeAt(5)){case 103:return"-webkit-box-"+o.replace("-grow","")+"-webkit-"+o+"-ms-"+o.replace("grow","positive")+o;case 115:return"-webkit-"+o+"-ms-"+o.replace("shrink","negative")+o;case 98:return"-webkit-"+o+"-ms-"+o.replace("basis","preferred-size")+o}return"-webkit-"+o+"-ms-"+o+o;case 964:return"-webkit-"+o+"-ms-flex-"+o+o;case 1023:if(99!==o.charCodeAt(8))break;return"-webkit-box-pack"+(s=o.substring(o.indexOf(":",15)).replace("flex-","").replace("space-between","justify"))+"-webkit-"+o+"-ms-flex-pack"+s+o;case 1005:return p.test(o)?o.replace(f,":-webkit-")+o.replace(f,":-moz-")+o:o;case 1e3:switch(t=(s=o.substring(13).trim()).indexOf("-")+1,s.charCodeAt(0)+s.charCodeAt(t)){case 226:s=o.replace(x,"tb");break;case 232:s=o.replace(x,"tb-rl");break;case 220:s=o.replace(x,"lr");break;default:return o}return"-webkit-"+o+"-ms-"+s+o;case 1017:if(-1===o.indexOf("sticky",9))break;case 975:switch(t=(o=e).length-10,l=(s=(33===o.charCodeAt(t)?o.substring(0,t):o).substring(e.indexOf(":",7)+1).trim()).charCodeAt(0)+(0|s.charCodeAt(7))){case 203:if(111>s.charCodeAt(8))break;case 115:o=o.replace(s,"-webkit-"+s)+";"+o;break;case 207:case 102:o=o.replace(s,"-webkit-"+(102<l?"inline-":"")+"box")+";"+o.replace(s,"-webkit-"+s)+";"+o.replace(s,"-ms-"+s+"box")+";"+o}return o+";";case 938:if(45===o.charCodeAt(5))switch(o.charCodeAt(6)){case 105:return s=o.replace("-items",""),"-webkit-"+o+"-webkit-box-"+s+"-ms-flex-"+s+o;case 115:return"-webkit-"+o+"-ms-flex-item-"+o.replace(S,"")+o;default:return"-webkit-"+o+"-ms-flex-line-pack"+o.replace("align-content","").replace(S,"")+o}break;case 973:case 989:if(45!==o.charCodeAt(3)||122===o.charCodeAt(4))break;case 931:case 953:if(!0===C.test(e))return 115===(s=e.substring(e.indexOf(":")+1)).charCodeAt(0)?a(e.replace("stretch","fill-available"),t,n,r).replace(":fill-available",":stretch"):o.replace(s,"-webkit-"+s)+o.replace(s,"-moz-"+s.replace("fill-",""))+o;break;case 962:if(o="-webkit-"+o+(102===o.charCodeAt(5)?"-ms-"+o:"")+o,211===n+r&&105===o.charCodeAt(13)&&0<o.indexOf("transform",10))return o.substring(0,o.indexOf(";",27)+1).replace(m,"$1-webkit-$2")+o}return o}function i(e,t){var n=e.indexOf(1===t?":":"{"),r=e.substring(0,3!==t?n:10);return n=e.substring(n+1,e.length-1),R(2!==t?r:r.replace(A,"$1"),n,t)}function o(e,t){var n=a(t,t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2));return n!==t+";"?n.replace(w," or ($1)").substring(4):"("+t+")"}function l(e,t,n,r,a,i,o,l,s,u){for(var d,f=0,p=t;f<F;++f)switch(d=O[f].call(c,e,p,n,r,a,i,o,l,s,u)){case void 0:case!1:case!0:case null:break;default:p=d}if(p!==t)return p}function s(e){return void 0!==(e=e.prefix)&&(R=null,e?"function"!==typeof e?T=1:(T=2,R=e):T=0),s}function c(e,n){var r=e;if(33>r.charCodeAt(0)&&(r=r.trim()),r=[r],0<F){var a=l(-1,n,r,r,P,N,0,0,0,0);void 0!==a&&"string"===typeof a&&(n=a)}var i=t(I,r,n,0,0);return 0<F&&(void 0!==(a=l(-2,i,r,r,P,N,i.length,0,0,0))&&(i=a)),E=0,N=P=1,i}var u=/^\0+/g,d=/[\0\r\f]/g,f=/: */g,p=/zoo|gra/,m=/([,: ])(transform)/g,h=/,\r+?/g,g=/([\t\r\n ])*\f?&/g,v=/@(k\w+)\s*(\S*)\s*/,b=/::(place)/g,y=/:(read-only)/g,x=/[svh]\w+-[tblr]{2}/,k=/\(\s*(.*)\s*\)/g,w=/([\s\S]*?);/g,S=/-self|flex-/g,A=/[^]*?(:[rp][el]a[\w-]+)[^]*/,C=/stretch|:\s*\w+\-(?:conte|avail)/,j=/([^-])(image-set\()/,N=1,P=1,E=0,T=1,I=[],O=[],F=0,R=null,D=0;return c.use=function e(t){switch(t){case void 0:case null:F=O.length=0;break;default:if("function"===typeof t)O[F++]=t;else if("object"===typeof t)for(var n=0,r=t.length;n<r;++n)e(t[n]);else D=0|!!t}return e},c.set=s,void 0!==e&&s(e),c};const Ee={animationIterationCount:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function Te(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}var Ie=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,Oe=Te(function(e){return Ie.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91}),Fe=n(219),Re=n.n(Fe);function De(){return(De=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Le=function(e,t){for(var n=[e[0]],r=0,a=t.length;r<a;r+=1)n.push(t[r],e[r+1]);return n},ze=function(e){return null!==e&&"object"==typeof e&&"[object Object]"===(e.toString?e.toString():Object.prototype.toString.call(e))&&!(0,Ce.typeOf)(e)},Ue=Object.freeze([]),Me=Object.freeze({});function _e(e){return"function"==typeof e}function Be(e){return e.displayName||e.name||"Component"}function He(e){return e&&"string"==typeof e.styledComponentId}var We="undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_BASE_URL:"https://flipbook.franklinreport.com",REACT_APP_USE_MOCK_API:"false"}&&({NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_BASE_URL:"https://flipbook.franklinreport.com",REACT_APP_USE_MOCK_API:"false"}.REACT_APP_SC_ATTR||{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_BASE_URL:"https://flipbook.franklinreport.com",REACT_APP_USE_MOCK_API:"false"}.SC_ATTR)||"data-styled",Ve="undefined"!=typeof window&&"HTMLElement"in window,qe=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_BASE_URL:"https://flipbook.franklinreport.com",REACT_APP_USE_MOCK_API:"false"}&&(void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_BASE_URL:"https://flipbook.franklinreport.com",REACT_APP_USE_MOCK_API:"false"}.REACT_APP_SC_DISABLE_SPEEDY&&""!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_BASE_URL:"https://flipbook.franklinreport.com",REACT_APP_USE_MOCK_API:"false"}.REACT_APP_SC_DISABLE_SPEEDY?"false"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_BASE_URL:"https://flipbook.franklinreport.com",REACT_APP_USE_MOCK_API:"false"}.REACT_APP_SC_DISABLE_SPEEDY&&{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_BASE_URL:"https://flipbook.franklinreport.com",REACT_APP_USE_MOCK_API:"false"}.REACT_APP_SC_DISABLE_SPEEDY:void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_BASE_URL:"https://flipbook.franklinreport.com",REACT_APP_USE_MOCK_API:"false"}.SC_DISABLE_SPEEDY&&""!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_BASE_URL:"https://flipbook.franklinreport.com",REACT_APP_USE_MOCK_API:"false"}.SC_DISABLE_SPEEDY&&("false"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_BASE_URL:"https://flipbook.franklinreport.com",REACT_APP_USE_MOCK_API:"false"}.SC_DISABLE_SPEEDY&&{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_BASE_URL:"https://flipbook.franklinreport.com",REACT_APP_USE_MOCK_API:"false"}.SC_DISABLE_SPEEDY)));function Xe(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw new Error("An error occurred. See https://git.io/JUIaE#"+e+" for more information."+(n.length>0?" Args: "+n.join(", "):""))}var Qe=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}var t=e.prototype;return t.indexOfGroup=function(e){for(var t=0,n=0;n<e;n++)t+=this.groupSizes[n];return t},t.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var n=this.groupSizes,r=n.length,a=r;e>=a;)(a<<=1)<0&&Xe(16,""+e);this.groupSizes=new Uint32Array(a),this.groupSizes.set(n),this.length=a;for(var i=r;i<a;i++)this.groupSizes[i]=0}for(var o=this.indexOfGroup(e+1),l=0,s=t.length;l<s;l++)this.tag.insertRule(o,t[l])&&(this.groupSizes[e]++,o++)},t.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],n=this.indexOfGroup(e),r=n+t;this.groupSizes[e]=0;for(var a=n;a<r;a++)this.tag.deleteRule(n)}},t.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var n=this.groupSizes[e],r=this.indexOfGroup(e),a=r+n,i=r;i<a;i++)t+=this.tag.getRule(i)+"/*!sc*/\n";return t},e}(),Ke=new Map,Ge=new Map,Ye=1,Je=function(e){if(Ke.has(e))return Ke.get(e);for(;Ge.has(Ye);)Ye++;var t=Ye++;return Ke.set(e,t),Ge.set(t,e),t},Ze=function(e){return Ge.get(e)},$e=function(e,t){t>=Ye&&(Ye=t+1),Ke.set(e,t),Ge.set(t,e)},et="style["+We+'][data-styled-version="5.3.11"]',tt=new RegExp("^"+We+'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)'),nt=function(e,t,n){for(var r,a=n.split(","),i=0,o=a.length;i<o;i++)(r=a[i])&&e.registerName(t,r)},rt=function(e,t){for(var n=(t.textContent||"").split("/*!sc*/\n"),r=[],a=0,i=n.length;a<i;a++){var o=n[a].trim();if(o){var l=o.match(tt);if(l){var s=0|parseInt(l[1],10),c=l[2];0!==s&&($e(c,s),nt(e,c,l[3]),e.getTag().insertRules(s,r)),r.length=0}else r.push(o)}}},at=function(){return n.nc},it=function(e){var t=document.head,n=e||t,r=document.createElement("style"),a=function(e){for(var t=e.childNodes,n=t.length;n>=0;n--){var r=t[n];if(r&&1===r.nodeType&&r.hasAttribute(We))return r}}(n),i=void 0!==a?a.nextSibling:null;r.setAttribute(We,"active"),r.setAttribute("data-styled-version","5.3.11");var o=at();return o&&r.setAttribute("nonce",o),n.insertBefore(r,i),r},ot=function(){function e(e){var t=this.element=it(e);t.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,n=0,r=t.length;n<r;n++){var a=t[n];if(a.ownerNode===e)return a}Xe(17)}(t),this.length=0}var t=e.prototype;return t.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},t.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},t.getRule=function(e){var t=this.sheet.cssRules[e];return void 0!==t&&"string"==typeof t.cssText?t.cssText:""},e}(),lt=function(){function e(e){var t=this.element=it(e);this.nodes=t.childNodes,this.length=0}var t=e.prototype;return t.insertRule=function(e,t){if(e<=this.length&&e>=0){var n=document.createTextNode(t),r=this.nodes[e];return this.element.insertBefore(n,r||null),this.length++,!0}return!1},t.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},t.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),st=function(){function e(e){this.rules=[],this.length=0}var t=e.prototype;return t.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},t.deleteRule=function(e){this.rules.splice(e,1),this.length--},t.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),ct=Ve,ut={isServer:!Ve,useCSSOMInjection:!qe},dt=function(){function e(e,t,n){void 0===e&&(e=Me),void 0===t&&(t={}),this.options=De({},ut,{},e),this.gs=t,this.names=new Map(n),this.server=!!e.isServer,!this.server&&Ve&&ct&&(ct=!1,function(e){for(var t=document.querySelectorAll(et),n=0,r=t.length;n<r;n++){var a=t[n];a&&"active"!==a.getAttribute(We)&&(rt(e,a),a.parentNode&&a.parentNode.removeChild(a))}}(this))}e.registerId=function(e){return Je(e)};var t=e.prototype;return t.reconstructWithOptions=function(t,n){return void 0===n&&(n=!0),new e(De({},this.options,{},t),this.gs,n&&this.names||void 0)},t.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},t.getTag=function(){return this.tag||(this.tag=(n=(t=this.options).isServer,r=t.useCSSOMInjection,a=t.target,e=n?new st(a):r?new ot(a):new lt(a),new Qe(e)));var e,t,n,r,a},t.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},t.registerName=function(e,t){if(Je(e),this.names.has(e))this.names.get(e).add(t);else{var n=new Set;n.add(t),this.names.set(e,n)}},t.insertRules=function(e,t,n){this.registerName(e,t),this.getTag().insertRules(Je(e),n)},t.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},t.clearRules=function(e){this.getTag().clearGroup(Je(e)),this.clearNames(e)},t.clearTag=function(){this.tag=void 0},t.toString=function(){return function(e){for(var t=e.getTag(),n=t.length,r="",a=0;a<n;a++){var i=Ze(a);if(void 0!==i){var o=e.names.get(i),l=t.getGroup(a);if(o&&l&&o.size){var s=We+".g"+a+'[id="'+i+'"]',c="";void 0!==o&&o.forEach(function(e){e.length>0&&(c+=e+",")}),r+=""+l+s+'{content:"'+c+'"}/*!sc*/\n'}}}return r}(this)},e}(),ft=/(a)(d)/gi,pt=function(e){return String.fromCharCode(e+(e>25?39:97))};function mt(e){var t,n="";for(t=Math.abs(e);t>52;t=t/52|0)n=pt(t%52)+n;return(pt(t%52)+n).replace(ft,"$1-$2")}var ht=function(e,t){for(var n=t.length;n;)e=33*e^t.charCodeAt(--n);return e},gt=function(e){return ht(5381,e)};function vt(e){for(var t=0;t<e.length;t+=1){var n=e[t];if(_e(n)&&!He(n))return!1}return!0}var bt=gt("5.3.11"),yt=function(){function e(e,t,n){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===n||n.isStatic)&&vt(e),this.componentId=t,this.baseHash=ht(bt,t),this.baseStyle=n,dt.registerId(t)}return e.prototype.generateAndInjectStyles=function(e,t,n){var r=this.componentId,a=[];if(this.baseStyle&&a.push(this.baseStyle.generateAndInjectStyles(e,t,n)),this.isStatic&&!n.hash)if(this.staticRulesId&&t.hasNameForId(r,this.staticRulesId))a.push(this.staticRulesId);else{var i=zt(this.rules,e,t,n).join(""),o=mt(ht(this.baseHash,i)>>>0);if(!t.hasNameForId(r,o)){var l=n(i,"."+o,void 0,r);t.insertRules(r,o,l)}a.push(o),this.staticRulesId=o}else{for(var s=this.rules.length,c=ht(this.baseHash,n.hash),u="",d=0;d<s;d++){var f=this.rules[d];if("string"==typeof f)u+=f;else if(f){var p=zt(f,e,t,n),m=Array.isArray(p)?p.join(""):p;c=ht(c,m+d),u+=m}}if(u){var h=mt(c>>>0);if(!t.hasNameForId(r,h)){var g=n(u,"."+h,void 0,r);t.insertRules(r,h,g)}a.push(h)}}return a.join(" ")},e}(),xt=/^\s*\/\/.*$/gm,kt=[":","[",".","#"];function wt(e){var t,n,r,a,i=void 0===e?Me:e,o=i.options,l=void 0===o?Me:o,s=i.plugins,c=void 0===s?Ue:s,u=new Pe(l),d=[],f=function(e){function t(t){if(t)try{e(t+"}")}catch(e){}}return function(n,r,a,i,o,l,s,c,u,d){switch(n){case 1:if(0===u&&64===r.charCodeAt(0))return e(r+";"),"";break;case 2:if(0===c)return r+"/*|*/";break;case 3:switch(c){case 102:case 112:return e(a[0]+r),"";default:return r+(0===d?"/*|*/":"")}case-2:r.split("/*|*/}").forEach(t)}}}(function(e){d.push(e)}),p=function(e,r,i){return 0===r&&-1!==kt.indexOf(i[n.length])||i.match(a)?e:"."+t};function m(e,i,o,l){void 0===l&&(l="&");var s=e.replace(xt,""),c=i&&o?o+" "+i+" { "+s+" }":s;return t=l,n=i,r=new RegExp("\\"+n+"\\b","g"),a=new RegExp("(\\"+n+"\\b){2,}"),u(o||!i?"":i,c)}return u.use([].concat(c,[function(e,t,a){2===e&&a.length&&a[0].lastIndexOf(n)>0&&(a[0]=a[0].replace(r,p))},f,function(e){if(-2===e){var t=d;return d=[],t}}])),m.hash=c.length?c.reduce(function(e,t){return t.name||Xe(15),ht(e,t.name)},5381).toString():"",m}var St=t.createContext(),At=(St.Consumer,t.createContext()),Ct=(At.Consumer,new dt),jt=wt();function Nt(){return(0,t.useContext)(St)||Ct}function Pt(){return(0,t.useContext)(At)||jt}function Et(e){var n=(0,t.useState)(e.stylisPlugins),r=n[0],a=n[1],i=Nt(),o=(0,t.useMemo)(function(){var t=i;return e.sheet?t=e.sheet:e.target&&(t=t.reconstructWithOptions({target:e.target},!1)),e.disableCSSOMInjection&&(t=t.reconstructWithOptions({useCSSOMInjection:!1})),t},[e.disableCSSOMInjection,e.sheet,e.target]),l=(0,t.useMemo)(function(){return wt({options:{prefix:!e.disableVendorPrefixes},plugins:r})},[e.disableVendorPrefixes,r]);return(0,t.useEffect)(function(){Ne()(r,e.stylisPlugins)||a(e.stylisPlugins)},[e.stylisPlugins]),t.createElement(St.Provider,{value:o},t.createElement(At.Provider,{value:l},e.children))}var Tt=function(){function e(e,t){var n=this;this.inject=function(e,t){void 0===t&&(t=jt);var r=n.name+t.hash;e.hasNameForId(n.id,r)||e.insertRules(n.id,r,t(n.rules,r,"@keyframes"))},this.toString=function(){return Xe(12,String(n.name))},this.name=e,this.id="sc-keyframes-"+e,this.rules=t}return e.prototype.getName=function(e){return void 0===e&&(e=jt),this.name+e.hash},e}(),It=/([A-Z])/,Ot=/([A-Z])/g,Ft=/^ms-/,Rt=function(e){return"-"+e.toLowerCase()};function Dt(e){return It.test(e)?e.replace(Ot,Rt).replace(Ft,"-ms-"):e}var Lt=function(e){return null==e||!1===e||""===e};function zt(e,t,n,r){if(Array.isArray(e)){for(var a,i=[],o=0,l=e.length;o<l;o+=1)""!==(a=zt(e[o],t,n,r))&&(Array.isArray(a)?i.push.apply(i,a):i.push(a));return i}return Lt(e)?"":He(e)?"."+e.styledComponentId:_e(e)?"function"!=typeof(s=e)||s.prototype&&s.prototype.isReactComponent||!t?e:zt(e(t),t,n,r):e instanceof Tt?n?(e.inject(n,r),e.getName(r)):e:ze(e)?function e(t,n){var r,a,i=[];for(var o in t)t.hasOwnProperty(o)&&!Lt(t[o])&&(Array.isArray(t[o])&&t[o].isCss||_e(t[o])?i.push(Dt(o)+":",t[o],";"):ze(t[o])?i.push.apply(i,e(t[o],o)):i.push(Dt(o)+": "+(r=o,(null==(a=t[o])||"boolean"==typeof a||""===a?"":"number"!=typeof a||0===a||r in Ee||r.startsWith("--")?String(a).trim():a+"px")+";")));return n?[n+" {"].concat(i,["}"]):i}(e):e.toString();var s}var Ut=function(e){return Array.isArray(e)&&(e.isCss=!0),e};function Mt(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return _e(e)||ze(e)?Ut(zt(Le(Ue,[e].concat(n)))):0===n.length&&1===e.length&&"string"==typeof e[0]?e:Ut(zt(Le(e,n)))}new Set;var _t=function(e,t,n){return void 0===n&&(n=Me),e.theme!==n.theme&&e.theme||t||n.theme},Bt=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,Ht=/(^-|-$)/g;function Wt(e){return e.replace(Bt,"-").replace(Ht,"")}var Vt=function(e){return mt(gt(e)>>>0)};function qt(e){return"string"==typeof e&&!0}var Xt=function(e){return"function"==typeof e||"object"==typeof e&&null!==e&&!Array.isArray(e)},Qt=function(e){return"__proto__"!==e&&"constructor"!==e&&"prototype"!==e};function Kt(e,t,n){var r=e[n];Xt(t)&&Xt(r)?Gt(r,t):e[n]=t}function Gt(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(var a=0,i=n;a<i.length;a++){var o=i[a];if(Xt(o))for(var l in o)Qt(l)&&Kt(e,o[l],l)}return e}var Yt=t.createContext();Yt.Consumer;var Jt={};function Zt(e,n,r){var a=He(e),i=!qt(e),o=n.attrs,l=void 0===o?Ue:o,s=n.componentId,c=void 0===s?function(e,t){var n="string"!=typeof e?"sc":Wt(e);Jt[n]=(Jt[n]||0)+1;var r=n+"-"+Vt("5.3.11"+n+Jt[n]);return t?t+"-"+r:r}(n.displayName,n.parentComponentId):s,u=n.displayName,d=void 0===u?function(e){return qt(e)?"styled."+e:"Styled("+Be(e)+")"}(e):u,f=n.displayName&&n.componentId?Wt(n.displayName)+"-"+n.componentId:n.componentId||c,p=a&&e.attrs?Array.prototype.concat(e.attrs,l).filter(Boolean):l,m=n.shouldForwardProp;a&&e.shouldForwardProp&&(m=n.shouldForwardProp?function(t,r,a){return e.shouldForwardProp(t,r,a)&&n.shouldForwardProp(t,r,a)}:e.shouldForwardProp);var h,g=new yt(r,f,a?e.componentStyle:void 0),v=g.isStatic&&0===l.length,b=function(e,n){return function(e,n,r,a){var i=e.attrs,o=e.componentStyle,l=e.defaultProps,s=e.foldedComponentIds,c=e.shouldForwardProp,u=e.styledComponentId,d=e.target,f=function(e,t,n){void 0===e&&(e=Me);var r=De({},t,{theme:e}),a={};return n.forEach(function(e){var t,n,i,o=e;for(t in _e(o)&&(o=o(r)),o)r[t]=a[t]="className"===t?(n=a[t],i=o[t],n&&i?n+" "+i:n||i):o[t]}),[r,a]}(_t(n,(0,t.useContext)(Yt),l)||Me,n,i),p=f[0],m=f[1],h=function(e,t,n){var r=Nt(),a=Pt();return t?e.generateAndInjectStyles(Me,r,a):e.generateAndInjectStyles(n,r,a)}(o,a,p),g=r,v=m.$as||n.$as||m.as||n.as||d,b=qt(v),y=m!==n?De({},n,{},m):n,x={};for(var k in y)"$"!==k[0]&&"as"!==k&&("forwardedAs"===k?x.as=y[k]:(c?c(k,Oe,v):!b||Oe(k))&&(x[k]=y[k]));return n.style&&m.style!==n.style&&(x.style=De({},n.style,{},m.style)),x.className=Array.prototype.concat(s,u,h!==u?h:null,n.className,m.className).filter(Boolean).join(" "),x.ref=g,(0,t.createElement)(v,x)}(h,e,n,v)};return b.displayName=d,(h=t.forwardRef(b)).attrs=p,h.componentStyle=g,h.displayName=d,h.shouldForwardProp=m,h.foldedComponentIds=a?Array.prototype.concat(e.foldedComponentIds,e.styledComponentId):Ue,h.styledComponentId=f,h.target=a?e.target:e,h.withComponent=function(e){var t=n.componentId,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(n,["componentId"]),i=t&&t+"-"+(qt(e)?e:Wt(Be(e)));return Zt(e,De({},a,{attrs:p,componentId:i}),r)},Object.defineProperty(h,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(t){this._foldedDefaultProps=a?Gt({},e.defaultProps,t):t}}),Object.defineProperty(h,"toString",{value:function(){return"."+h.styledComponentId}}),i&&Re()(h,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0,withComponent:!0}),h}var $t=function(e){return function e(t,n,r){if(void 0===r&&(r=Me),!(0,Ce.isValidElementType)(n))return Xe(1,String(n));var a=function(){return t(n,r,Mt.apply(void 0,arguments))};return a.withConfig=function(a){return e(t,n,De({},r,{},a))},a.attrs=function(a){return e(t,n,De({},r,{attrs:Array.prototype.concat(r.attrs,a).filter(Boolean)}))},a}(Zt,e)};["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","textPath","tspan"].forEach(function(e){$t[e]=$t(e)});!function(){function e(e,t){this.rules=e,this.componentId=t,this.isStatic=vt(e),dt.registerId(this.componentId+1)}var t=e.prototype;t.createStyles=function(e,t,n,r){var a=r(zt(this.rules,t,n,r).join(""),""),i=this.componentId+e;n.insertRules(i,i,a)},t.removeStyles=function(e,t){t.clearRules(this.componentId+e)},t.renderStyles=function(e,t,n,r){e>2&&dt.registerId(this.componentId+e),this.removeStyles(e,n),this.createStyles(e,t,n,r)}}();!function(){function e(){var e=this;this._emitSheetCSS=function(){var t=e.instance.toString();if(!t)return"";var n=at();return"<style "+[n&&'nonce="'+n+'"',We+'="true"','data-styled-version="5.3.11"'].filter(Boolean).join(" ")+">"+t+"</style>"},this.getStyleTags=function(){return e.sealed?Xe(2):e._emitSheetCSS()},this.getStyleElement=function(){var n;if(e.sealed)return Xe(2);var r=((n={})[We]="",n["data-styled-version"]="5.3.11",n.dangerouslySetInnerHTML={__html:e.instance.toString()},n),a=at();return a&&(r.nonce=a),[t.createElement("style",De({},r,{key:"sc-0-0"}))]},this.seal=function(){e.sealed=!0},this.instance=new dt({isServer:!0}),this.sealed=!1}var n=e.prototype;n.collectStyles=function(e){return this.sealed?Xe(2):t.createElement(Et,{sheet:this.instance},e)},n.interleaveWithNodeStream=function(e){return Xe(3)}}();const en=$t;var tn=n(2555);class nn{constructor(){this.baseUrl=void 0,this.baseUrl="https://flipbook.franklinreport.com"}async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{const n=await fetch("".concat(this.baseUrl).concat(e),(0,tn.A)({headers:(0,tn.A)({"Content-Type":"application/json"},t.headers),credentials:"include"},t));if(!n.ok)throw new Error("HTTP error! status: ".concat(n.status));return{data:await n.json(),success:!0}}catch(n){return console.error("API request failed:",n),{data:null,success:!1,error:n instanceof Error?n.message:"Unknown error occurred"}}}async getCurrentUser(){return this.request("/Account/GetCurrentUser")}async signIn(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this.request("/Account/FBLogin",{method:"POST",body:JSON.stringify({UserName:e,Password:t,chkRememberMe:n?"1":"0"})})}async signOut(){return this.request("/Account/LogOff",{method:"POST"})}async getMyFlipbooks(){return this.request("/Flipbook/getPortfolioDetails/-1")}async getInspirationFlipbooks(){return this.request("/Flipbook/GetInspirationFlipbooks")}async getPortfolioDetails(e){return this.request("/Flipbook/getPortfolioDetails/".concat(e))}async getPortfolioPages(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.request("/Flipbook/GetPortPagesByPortfolioId?PortfolioID=".concat(e,"&IsAuto=").concat(t))}async createNewFlipbook(e){return this.request("/Flipbook/AddPortfolioTitle",{method:"POST",body:JSON.stringify({PortTitle:e.title,PortfolioID:e.templateId||0})})}async copyInspirationFlipbook(e){return this.request("/Flipbook/DuplicateWholeFlipbook",{method:"POST",body:JSON.stringify({PortTitle:e.title,PortfolioID:e.portfolioId,IsInspirationFB:!0})})}async updateFlipbook(e){return this.request("/Flipbook/UpdateFlipbookTitle",{method:"POST",body:JSON.stringify(e)})}async deleteFlipbook(e){return this.request("/Flipbook/DeleteFlipbook",{method:"POST",body:JSON.stringify({PortfolioID:e})})}async setFlipbookPreferred(e,t){return this.request("/Flipbook/SetflipbookIspreferred",{method:"POST",body:JSON.stringify({PortfolioID:e,SetIsPreferred:t})})}async getFlipbookPreviewData(e,t,n){const r=n?"/Publish/Inspiration/".concat(e,"/").concat(t):"/Publish/Preview/".concat(e,"/").concat(t);return this.request(r)}getThumbnailUrl(e,t){const n=arguments.length>2&&void 0!==arguments[2]&&arguments[2]?"999999":t.toString();return"".concat(this.baseUrl,"/Users/").concat(n,"/Flipbooks/").concat(e,"/ThumbnailImages/Tn1.jpg")}getFlipbookHtmlPath(e,t){const n=arguments.length>2&&void 0!==arguments[2]&&arguments[2]?"999999":t.toString();return"/Users/".concat(n,"/Flipbooks/").concat(e,"/HTML/")}async getUIMessages(){const e=Date.now();return this.request("/Admin/getMsgUI?_=".concat(e))}async getPortfolioDetailsInitial(){return this.request("/Flipbook/getPortfolioDetails/")}async getCommonValues(){const e=Date.now();return this.request("/Admin/getCommonValues?_=".concat(e))}}let rn;rn=new nn;const an=rn;n(2928);const on=()=>{const[e,n]=(0,t.useState)(null),[r,a]=(0,t.useState)(!0),[i,o]=(0,t.useState)(!1),[l,s]=(0,t.useState)(!1),[c,u]=(0,t.useState)(null),d=an,f=null!==e,p=(0,t.useCallback)(()=>{u(null)},[]),m=(0,t.useCallback)(async()=>{try{a(!0),u(null);const e=await d.getCurrentUser();e.success&&e.data?n(e.data):n(null)}catch(e){n(null),console.warn("Auth check failed:",e)}finally{a(!1)}},[d]),h=(0,t.useCallback)(async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{o(!0),u(null);const a=await d.signIn(e,t,r);if(a.success&&a.data)return n(a.data),!0;{const e=a.error||"Invalid login attempt";return u("0"===e||"Invalid Login Attempt."===e?"Invalid email or password. Please try again.":"2"===e?"Your account is locked. Please contact support.":"3"===e?"User not found. Please check your email address.":e),!1}}catch(a){return u(a instanceof Error?a.message:"An error occurred during sign in"),!1}finally{o(!1)}},[d]),g=(0,t.useCallback)(async()=>{try{s(!0),u(null),await d.signOut(),n(null)}catch(e){n(null),console.warn("Sign out error:",e)}finally{s(!1)}},[d]);return(0,t.useEffect)(()=>{m()},[m]),{user:e,isAuthenticated:f,loading:r,signingIn:i,signingOut:l,error:c,signIn:h,signOut:g,checkAuthStatus:m,clearError:p}};var ln,sn,cn,un,dn,fn,pn,mn,hn,gn,vn,bn,yn=n(579);const xn=en.header(ln||(ln=Ae(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: relative;\n  z-index: 100;\n"]))),kn=en.div(sn||(sn=Ae(["\n  display: flex;\n  align-items: center;\n  gap: 30px;\n"]))),wn=en.div(cn||(cn=Ae(["\n  font-family: 'Brush Script MT', cursive;\n  font-size: 28px;\n  font-weight: bold;\n  color: white;\n  text-decoration: none;\n  cursor: pointer;\n  \n  &:hover {\n    opacity: 0.9;\n  }\n"]))),Sn=en.nav(un||(un=Ae(["\n  display: flex;\n  gap: 25px;\n"]))),An=en.a(dn||(dn=Ae(["\n  color: white;\n  text-decoration: none;\n  font-size: 14px;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  padding: 8px 12px;\n  border-radius: 4px;\n  transition: background-color 0.3s ease;\n  cursor: pointer;\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n"]))),Cn=en.div(fn||(fn=Ae(["\n  display: flex;\n  align-items: center;\n  gap: 15px;\n"]))),jn=en.button(pn||(pn=Ae(["\n  background: none;\n  border: none;\n  color: white;\n  font-size: 18px;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 4px;\n  transition: background-color 0.3s ease;\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n"]))),Nn=en.div(mn||(mn=Ae(["\n  display: flex;\n  align-items: center;\n  gap: 10px;\n"]))),Pn=en.div(hn||(hn=Ae(["\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 14px;\n  color: white;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n\n  &:hover {\n    transform: scale(1.1);\n  }\n"]))),En=en.div(gn||(gn=Ae(["\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n"]))),Tn=en.span(vn||(vn=Ae(["\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n"]))),In=en.span(bn||(bn=Ae(["\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.7);\n"]))),On=()=>{var e,n;const{user:r,isAuthenticated:a,signOut:i}=on(),[o,l]=(0,t.useState)(!1);return(0,yn.jsxs)(xn,{children:[(0,yn.jsxs)(kn,{children:[(0,yn.jsx)(wn,{children:"Flipbook"}),(0,yn.jsxs)(Sn,{children:[(0,yn.jsx)(An,{href:"#file",children:"FILE"}),(0,yn.jsx)(An,{href:"#edit",children:"EDIT"}),(0,yn.jsx)(An,{href:"#share",children:"SHARE!"}),(0,yn.jsx)(An,{href:"#help",children:"HELP"})]})]}),(0,yn.jsxs)(Cn,{children:[(0,yn.jsx)(jn,{title:"Tools",children:"\ud83d\udd27"}),(0,yn.jsxs)(Nn,{children:[(0,yn.jsx)(Pn,{children:a&&r?((null===(e=r.firstName)||void 0===e?void 0:e.charAt(0))||"U")+((null===(n=r.lastName)||void 0===n?void 0:n.charAt(0))||"S"):"FR"}),(0,yn.jsxs)(En,{children:[(0,yn.jsx)(Tn,{onClick:()=>{a?i():l(!0)},style:{cursor:"pointer"},children:a?"Sign Out":"Sign In"}),!a&&(0,yn.jsx)(In,{children:"Join Us"}),a&&r&&(0,yn.jsxs)(In,{children:[r.firstName," ",r.lastName]})]})]})]})]})};var Fn,Rn,Dn,Ln;const zn=en.div(Fn||(Fn=Ae(["\n  width: 60px;\n  background-color: #2c3e50;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20px 0;\n  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);\n"]))),Un=en.div(Rn||(Rn=Ae(["\n  width: 40px;\n  height: 40px;\n  margin-bottom: 15px;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background-color: ",";\n  color: ",";\n  position: relative;\n\n  &:hover {\n    background-color: ",";\n    color: white;\n    transform: scale(1.1);\n  }\n"])),e=>e.active?"#3498db":"transparent",e=>e.active?"white":"#bdc3c7",e=>e.active?"#3498db":"#34495e"),Mn=en.div(Dn||(Dn=Ae(["\n  font-size: 18px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n"]))),_n=en.div(Ln||(Ln=Ae(["\n  position: absolute;\n  left: 60px;\n  background-color: #2c3e50;\n  color: white;\n  padding: 8px 12px;\n  border-radius: 4px;\n  font-size: 12px;\n  white-space: nowrap;\n  opacity: ",";\n  visibility: ",";\n  transition: all 0.3s ease;\n  z-index: 1000;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    left: -5px;\n    top: 50%;\n    transform: translateY(-50%);\n    border: 5px solid transparent;\n    border-right-color: #2c3e50;\n  }\n"])),e=>e.show?1:0,e=>e.show?"visible":"hidden"),Bn=()=>{const[e,n]=(0,t.useState)(null),[r,a]=(0,t.useState)("flipbooks"),i=()=>{n(null)};return(0,yn.jsx)(zn,{children:[{id:"flipbooks",icon:"\ud83d\udcd6",label:"My Flipbooks",active:!0},{id:"images",icon:"\ud83d\uddbc\ufe0f",label:"Images"},{id:"templates",icon:"\ud83d\udcc4",label:"Templates"},{id:"layouts",icon:"\ud83d\udcd0",label:"Layouts"},{id:"colors",icon:"\ud83c\udfa8",label:"Colors"},{id:"text",icon:"\ud83d\udcdd",label:"Text Tools"},{id:"effects",icon:"\u2728",label:"Effects"},{id:"settings",icon:"\u2699\ufe0f",label:"Settings"}].map(t=>(0,yn.jsxs)(Un,{active:r===t.id,onClick:()=>{return e=t.id,void a(e);var e},onMouseEnter:()=>{return e=t.id,void n(e);var e},onMouseLeave:i,children:[(0,yn.jsx)(Mn,{children:t.icon}),(0,yn.jsx)(_n,{show:e===t.id,children:t.label})]},t.id))})};var Hn,Wn,Vn,qn,Xn,Qn,Kn,Gn,Yn,Jn;const Zn=en.div(Hn||(Hn=Ae(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 20px;\n  padding: 20px 0;\n"]))),$n=en.div(Wn||(Wn=Ae(["\n  width: 200px;\n  height: 250px;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background-color: white;\n  position: relative;\n\n  &:hover {\n    transform: translateY(-8px);\n    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n  }\n"]))),er=en($n)(Vn||(Vn=Ae(["\n  border: 2px dashed #d1d5db;\n  background-color: #f9fafb;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    border-color: #3b82f6;\n    background-color: #eff6ff;\n  }\n"]))),tr=en.div(qn||(qn=Ae(["\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  background-color: #e5e7eb;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 32px;\n  color: #6b7280;\n  margin-bottom: 16px;\n  transition: all 0.3s ease;\n\n  ",":hover & {\n    background-color: #3b82f6;\n    color: white;\n  }\n"])),er),nr=en.span(Xn||(Xn=Ae(["\n  font-size: 14px;\n  color: #6b7280;\n  text-align: center;\n  font-weight: 500;\n  line-height: 1.4;\n\n  ",":hover & {\n    color: #3b82f6;\n  }\n"])),er),rr=en.div(Qn||(Qn=Ae(["\n  width: 100%;\n  height: 180px;\n  background-image: ",";\n  background-size: cover;\n  background-position: center;\n  background-color: #f3f4f6;\n  position: relative;\n\n  ","\n"])),e=>e.backgroundImage?"url(".concat(e.backgroundImage,")"):"none",e=>!e.backgroundImage&&"\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: #9ca3af;\n    font-size: 48px;\n  "),ar=en.div(Kn||(Kn=Ae(["\n  padding: 16px;\n  height: 70px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n"]))),ir=en.h3(Gn||(Gn=Ae(["\n  font-size: 14px;\n  font-weight: 600;\n  color: #1f2937;\n  margin: 0;\n  text-align: center;\n  line-height: 1.4;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n"]))),or=en.div(Yn||(Yn=Ae(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  \n  ",":hover & {\n    opacity: 1;\n  }\n"])),$n),lr=en.button(Jn||(Jn=Ae(["\n  background-color: #3b82f6;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  margin: 0 5px;\n  transition: background-color 0.3s ease;\n\n  &:hover {\n    background-color: #2563eb;\n  }\n"]))),sr=e=>{let{flipbooks:t,showCreateNew:n=!1,onCreateNew:r,onFlipbookClick:a,onFlipbookEdit:i,onFlipbookCopy:o}=e;return(0,yn.jsxs)(Zn,{children:[n&&(0,yn.jsxs)(er,{onClick:r,children:[(0,yn.jsx)(tr,{children:"+"}),(0,yn.jsx)(nr,{children:"Create New Flipbook"})]}),t.map(e=>(0,yn.jsxs)($n,{onClick:()=>null===a||void 0===a?void 0:a(e),children:[(0,yn.jsx)(rr,{backgroundImage:e.thumbnail,children:!e.thumbnail&&"\ud83d\udcd6"}),(0,yn.jsx)(ar,{children:(0,yn.jsx)(ir,{children:e.title})}),(0,yn.jsx)(or,{children:e.isInspiration?(0,yn.jsx)(lr,{onClick:t=>{t.stopPropagation(),null===o||void 0===o||o(e)},children:"Copy to My Flipbooks"}):(0,yn.jsxs)(yn.Fragment,{children:[(0,yn.jsx)(lr,{onClick:t=>{t.stopPropagation(),null===a||void 0===a||a(e)},children:"Open"}),(0,yn.jsx)(lr,{onClick:t=>{t.stopPropagation(),null===i||void 0===i||i(e)},children:"Edit"})]})})]},e.id))]})};var cr,ur,dr,fr,pr,mr,hr,gr,vr,br,yr,xr,kr,wr,Sr,Ar;const Cr=en.div(cr||(cr=Ae(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n"]))),jr=en.div(ur||(ur=Ae(["\n  background-color: white;\n  border-radius: 12px;\n  padding: 32px;\n  width: 90%;\n  max-width: 500px;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  position: relative;\n"]))),Nr=en.button(dr||(dr=Ae(["\n  position: absolute;\n  top: 16px;\n  right: 16px;\n  background: none;\n  border: none;\n  font-size: 24px;\n  color: #6b7280;\n  cursor: pointer;\n  padding: 4px;\n  border-radius: 4px;\n  transition: color 0.3s ease;\n\n  &:hover {\n    color: #374151;\n  }\n"]))),Pr=en.h2(fr||(fr=Ae(["\n  font-size: 24px;\n  font-weight: 600;\n  color: #1f2937;\n  margin: 0 0 24px 0;\n  text-align: center;\n"]))),Er=en.form(pr||(pr=Ae(["\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n"]))),Tr=en.div(mr||(mr=Ae(["\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n"]))),Ir=en.label(hr||(hr=Ae(["\n  font-size: 14px;\n  font-weight: 500;\n  color: #374151;\n"]))),Or=en.input(gr||(gr=Ae(["\n  padding: 12px 16px;\n  border: 2px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 16px;\n  transition: border-color 0.3s ease;\n\n  &:focus {\n    outline: none;\n    border-color: #3b82f6;\n  }\n\n  &::placeholder {\n    color: #9ca3af;\n  }\n"]))),Fr=en.div(vr||(vr=Ae(["\n  display: flex;\n  gap: 12px;\n  justify-content: flex-end;\n  margin-top: 24px;\n"]))),Rr=en.button(br||(br=Ae(["\n  padding: 12px 24px;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n\n  ","\n"])),e=>"primary"===e.variant?"\n    background-color: #3b82f6;\n    color: white;\n    border-color: #3b82f6;\n\n    &:hover {\n      background-color: #2563eb;\n      border-color: #2563eb;\n    }\n\n    &:disabled {\n      background-color: #9ca3af;\n      border-color: #9ca3af;\n      cursor: not-allowed;\n    }\n  ":"\n    background-color: white;\n    color: #374151;\n    border-color: #d1d5db;\n\n    &:hover {\n      background-color: #f9fafb;\n      border-color: #9ca3af;\n    }\n  "),Dr=en.div(yr||(yr=Ae(["\n  color: #ef4444;\n  font-size: 14px;\n  margin-top: 4px;\n"]))),Lr=en.div(xr||(xr=Ae(["\n  margin-top: 24px;\n"]))),zr=en.div(kr||(kr=Ae(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: 12px;\n  margin-top: 12px;\n"]))),Ur=en.div(wr||(wr=Ae(["\n  border: 2px solid ",";\n  border-radius: 8px;\n  padding: 12px;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background-color: ",";\n\n  &:hover {\n    border-color: #3b82f6;\n    background-color: #eff6ff;\n  }\n"])),e=>e.selected?"#3b82f6":"#d1d5db",e=>e.selected?"#eff6ff":"white"),Mr=en.div(Sr||(Sr=Ae(["\n  font-size: 32px;\n  margin-bottom: 8px;\n"]))),_r=en.div(Ar||(Ar=Ae(["\n  font-size: 12px;\n  color: #374151;\n  font-weight: 500;\n"]))),Br=e=>{let{onClose:n,onCreate:r}=e;const[a,i]=(0,t.useState)(""),[o,l]=(0,t.useState)("blank"),[s,c]=(0,t.useState)("");return(0,yn.jsx)(Cr,{onClick:n,children:(0,yn.jsxs)(jr,{onClick:e=>e.stopPropagation(),children:[(0,yn.jsx)(Nr,{onClick:n,children:"\xd7"}),(0,yn.jsx)(Pr,{children:"Create New Flipbook"}),(0,yn.jsxs)(Er,{onSubmit:e=>{e.preventDefault(),a.trim()?a.trim().length<3?c("Title must be at least 3 characters long"):r(a.trim(),o):c("Please enter a flipbook title")},children:[(0,yn.jsxs)(Tr,{children:[(0,yn.jsx)(Ir,{htmlFor:"title",children:"Flipbook Title"}),(0,yn.jsx)(Or,{id:"title",type:"text",value:a,onChange:e=>{i(e.target.value),s&&c("")},placeholder:"Enter your flipbook title...",autoFocus:!0}),s&&(0,yn.jsx)(Dr,{children:s})]}),(0,yn.jsxs)(Lr,{children:[(0,yn.jsx)(Ir,{children:"Choose a Template"}),(0,yn.jsx)(zr,{children:[{id:"blank",name:"Blank",icon:"\ud83d\udcc4"},{id:"portfolio",name:"Portfolio",icon:"\ud83c\udfa8"},{id:"business",name:"Business",icon:"\ud83d\udcbc"},{id:"personal",name:"Personal",icon:"\ud83d\udc64"}].map(e=>(0,yn.jsxs)(Ur,{selected:o===e.id,onClick:()=>l(e.id),children:[(0,yn.jsx)(Mr,{children:e.icon}),(0,yn.jsx)(_r,{children:e.name})]},e.id))})]}),(0,yn.jsxs)(Fr,{children:[(0,yn.jsx)(Rr,{type:"button",variant:"secondary",onClick:n,children:"Cancel"}),(0,yn.jsx)(Rr,{type:"submit",variant:"primary",disabled:!a.trim(),children:"Create Flipbook"})]})]})]})})},Hr=()=>{const[e,n]=(0,t.useState)([]),[r,a]=(0,t.useState)([]),[i,o]=(0,t.useState)(!1),[l,s]=(0,t.useState)(!1),[c,u]=(0,t.useState)(null),d=an,f=i||l,p=(0,t.useCallback)(async()=>{try{o(!0),u(null);const e=await d.getMyFlipbooks();e.success&&e.data?n(e.data):u(e.error||"Failed to fetch user flipbooks")}catch(e){u(e instanceof Error?e.message:"Failed to fetch user flipbooks")}finally{o(!1)}},[d]),m=(0,t.useCallback)(async()=>{try{s(!0),u(null);const e=await d.getInspirationFlipbooks();e.success&&e.data?a(e.data):u(e.error||"Failed to fetch inspiration flipbooks")}catch(e){u(e instanceof Error?e.message:"Failed to fetch inspiration flipbooks")}finally{s(!1)}},[d]),h=(0,t.useCallback)(async e=>{try{u(null);const t=await d.createNewFlipbook(e);return t.success&&t.data?(await p(),t.data):(u(t.error||"Failed to create flipbook"),null)}catch(t){return u(t instanceof Error?t.message:"Failed to create flipbook"),null}},[d,p]),g=(0,t.useCallback)(async e=>{try{u(null);const t=await d.copyInspirationFlipbook(e);return t.success&&t.data?(await p(),t.data):(u(t.error||"Failed to copy inspiration flipbook"),null)}catch(t){return u(t instanceof Error?t.message:"Failed to copy inspiration flipbook"),null}},[d,p]),v=(0,t.useCallback)(async e=>{try{u(null);const t=await d.updateFlipbook(e);return t.success?(await p(),!0):(u(t.error||"Failed to update flipbook"),!1)}catch(t){return u(t instanceof Error?t.message:"Failed to update flipbook"),!1}},[d,p]),b=(0,t.useCallback)(async e=>{try{u(null);const t=await d.deleteFlipbook(e);return t.success?(await p(),!0):(u(t.error||"Failed to delete flipbook"),!1)}catch(t){return u(t instanceof Error?t.message:"Failed to delete flipbook"),!1}},[d,p]),y=(0,t.useCallback)(async(e,t)=>{try{u(null);const n=await d.setFlipbookPreferred(e,t);return n.success?(await p(),!0):(u(n.error||"Failed to set flipbook preference"),!1)}catch(n){return u(n instanceof Error?n.message:"Failed to set flipbook preference"),!1}},[d,p]);return(0,t.useEffect)(()=>{p(),m()},[p,m]),{userFlipbooks:e,inspirationFlipbooks:r,loading:f,userFlipbooksLoading:i,inspirationFlipbooksLoading:l,error:c,refreshUserFlipbooks:p,refreshInspirationFlipbooks:m,createFlipbook:h,copyInspirationFlipbook:g,updateFlipbook:v,deleteFlipbook:b,setFlipbookPreferred:y}};var Wr,Vr,qr,Xr,Qr,Kr;const Gr=en.div(Wr||(Wr=Ae(["\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #f5f5f5;\n"]))),Yr=en.div(Vr||(Vr=Ae(["\n  display: flex;\n  flex: 1;\n  overflow: hidden;\n"]))),Jr=en.div(qr||(qr=Ae(["\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n  background-color: #ffffff;\n"]))),Zr=en.div(Xr||(Xr=Ae(["\n  margin-bottom: 40px;\n"]))),$r=en.h2(Qr||(Qr=Ae(["\n  font-size: 18px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 20px;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n"]))),ea=en.div(Kr||(Kr=Ae(["\n  position: fixed;\n  top: 80px;\n  right: 20px;\n  width: 100px;\n  height: 100px;\n  background-image: url('/images/certification-badge.png');\n  background-size: contain;\n  background-repeat: no-repeat;\n  z-index: 10;\n"]))),ta=()=>{const e=ee(),[n,r]=(0,t.useState)(!1),{user:a,isAuthenticated:i}=on(),{userFlipbooks:o,inspirationFlipbooks:l,loading:s,error:c,createFlipbook:u,copyInspirationFlipbook:d}=Hr(),f=o.map(e=>({id:e.PortfolioID,title:e.PortfolioTitle||"Untitled",thumbnail:e.ThumbnailPath||"https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop",isInspiration:!1})),p=l.map(e=>({id:e.PortfolioID,title:e.FBTitle,thumbnail:e.TnImageSrc||"https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop",isInspiration:!0}));return(0,yn.jsxs)(Gr,{children:[(0,yn.jsx)(On,{}),(0,yn.jsxs)(Yr,{children:[(0,yn.jsx)(Bn,{}),(0,yn.jsxs)(Jr,{children:[(0,yn.jsxs)(Zr,{children:[(0,yn.jsx)($r,{children:"My Flipbooks"}),(0,yn.jsx)(sr,{flipbooks:f,showCreateNew:!0,onCreateNew:()=>{r(!0)},onFlipbookClick:t=>e("/editor/".concat(t.id)),onFlipbookEdit:t=>e("/editor/".concat(t.id))})]}),(0,yn.jsxs)(Zr,{children:[(0,yn.jsx)($r,{children:"Inspiration"}),(0,yn.jsx)(sr,{flipbooks:p,onFlipbookClick:t=>{t.isInspiration?e("/viewer/".concat(t.id)):e("/editor/".concat(t.id))},onFlipbookCopy:async t=>{try{const n=await d({portfolioId:t.id,title:"Copy of ".concat(t.title)});n&&(console.log("Inspiration flipbook copied successfully with ID:",n),e("/editor/".concat(n)))}catch(n){console.error("Failed to copy inspiration flipbook:",n)}}})]})]})]}),(0,yn.jsx)(ea,{}),n&&(0,yn.jsx)(Br,{onClose:()=>{r(!1)},onCreate:async t=>{try{const n=await u({title:t});n&&(console.log("Flipbook created successfully with ID:",n),r(!1),e("/editor/".concat(n)))}catch(n){console.error("Failed to create flipbook:",n)}}})]})},na=e=>{let{value:n,onChange:r,placeholder:a="Enter text...",isActive:i,onFocus:o,onBlur:l,className:s="",style:c={},onSelectionChange:u,onCommandReceived:d,pendingFormatting:f,onFormattingApplied:p}=e;const[m,h]=(0,t.useState)({fontFamily:"Arial",fontSize:"14",bold:!1,italic:!1,underline:!1,textAlign:"left",color:"#000000",backgroundColor:"transparent",lineHeight:"1.4",letterSpacing:"0"}),g=(0,t.useRef)(null),[v,b]=(0,t.useState)(0);(0,t.useEffect)(()=>{i&&g.current&&g.current.focus()},[i]),(0,t.useEffect)(()=>{},[i,d]);const y=(e,t)=>{if(g.current){switch(g.current.focus(),e){case"bold":case"italic":case"underline":document.execCommand(e,!1);break;case"fontSize":const n=window.getSelection();if(n&&n.rangeCount>0){const e=n.getRangeAt(0);if(!e.collapsed){const n=document.createElement("span");n.style.fontSize=t+"pt";try{e.surroundContents(n)}catch(xa){document.execCommand("fontSize",!1,"7");g.current.querySelectorAll('font[size="7"]').forEach(e=>{e.style.fontSize=t+"pt"})}}}break;case"fontName":case"foreColor":case"backColor":case"justifyLeft":case"justifyCenter":case"justifyRight":case"justifyFull":case"insertUnorderedList":case"insertOrderedList":case"createLink":document.execCommand(e,!1,t)}setTimeout(()=>k(),10)}};(0,t.useEffect)(()=>{i&&g.current&&(g.current.applyCommand=y)},[i]);const x=()=>{if(!f||!g.current)return;const e=window.getSelection();if(!e||0===e.rangeCount)return;const t=e.getRangeAt(0);if(t.collapsed)return;const n=document.createElement("span");f.bold&&(n.style.fontWeight="bold"),f.italic&&(n.style.fontStyle="italic"),f.underline&&(n.style.textDecoration="underline"),f.fontSize&&(n.style.fontSize=f.fontSize+"pt"),f.fontFamily&&(n.style.fontFamily=f.fontFamily),f.color&&(n.style.color=f.color),f.backgroundColor&&"transparent"!==f.backgroundColor&&(n.style.backgroundColor=f.backgroundColor);try{t.surroundContents(n),null===p||void 0===p||p()}catch(xa){console.warn("Could not apply formatting to new text:",xa)}},k=()=>{if(!g.current)return;const e=window.getSelection();if(e&&0!==e.rangeCount&&!f){const e={fontFamily:document.queryCommandValue("fontName")||"Arial",fontSize:document.queryCommandValue("fontSize")||"14",bold:document.queryCommandState("bold"),italic:document.queryCommandState("italic"),underline:document.queryCommandState("underline"),textAlign:document.queryCommandState("justifyCenter")?"center":document.queryCommandState("justifyRight")?"right":document.queryCommandState("justifyFull")?"justify":"left",color:document.queryCommandValue("foreColor")||"#000000",backgroundColor:document.queryCommandValue("backColor")||"transparent",lineHeight:"1.4",letterSpacing:"0"};h(e),null===u||void 0===u||u(e)}},w=()=>{i&&!f&&k()};(0,t.useEffect)(()=>(document.addEventListener("selectionchange",w),()=>{document.removeEventListener("selectionchange",w)}),[i,f]),(0,t.useEffect)(()=>{b(n.length)},[n]);const S=(0,tn.A)((0,tn.A)({},c),{},{lineHeight:m.lineHeight,letterSpacing:m.letterSpacing});return(0,t.useEffect)(()=>{if(g.current&&g.current.innerHTML!==n){const e=window.getSelection(),t=e&&e.rangeCount>0?e.getRangeAt(0):null;if(g.current.innerHTML=n||"",t&&e)try{e.removeAllRanges(),e.addRange(t)}catch(xa){const n=document.createRange();n.selectNodeContents(g.current),n.collapse(!1),e.removeAllRanges(),e.addRange(n)}}},[n]),(0,yn.jsx)("div",{className:"text-editor-container ".concat(s),children:(0,yn.jsx)("div",{ref:g,className:"text-editor ".concat(i?"active":""),contentEditable:!0,suppressContentEditableWarning:!0,onInput:e=>{e.currentTarget.innerHTML;const t=e.currentTarget.innerText,n=t.length;f&&n>v&&x(),b(n),r(t)},onFocus:()=>{null===o||void 0===o||o(),k()},onBlur:e=>{null===l||void 0===l||l(e)},style:S,"data-placeholder":a})})},ra=e=>{let{isOpen:n,onClose:r,onSelectImage:a}=e;const[i,o]=(0,t.useState)("library"),[l,s]=(0,t.useState)([]),[c,u]=(0,t.useState)(!1),d=(0,t.useRef)(null),f=e=>{e&&Array.from(e).forEach(e=>{if(e.type.startsWith("image/")){const t=new FileReader;t.onload=t=>{var n;const r={id:"upload-".concat(Date.now(),"-").concat(Math.random()),url:null===(n=t.target)||void 0===n?void 0:n.result,name:e.name,type:"uploaded"};s(e=>[...e,r])},t.readAsDataURL(e)}})},p="library"===i?[{id:"preset-1",url:"/src/assets/images/RW-Image-16_Resume-1.jpg",name:"Resume Template 1",type:"preset"},{id:"preset-2",url:"/src/assets/images/RW-Image-16_Resume-2.jpg",name:"Resume Template 2",type:"preset"},{id:"preset-3",url:"/src/assets/images/RW-Image-16_Resume-3.jpg",name:"Resume Template 3",type:"preset"},{id:"preset-4",url:"/src/assets/images/RW-Image-16_Resume-4.jpg",name:"Resume Template 4",type:"preset"},{id:"preset-5",url:"/src/assets/images/RW-Image-design-1.jpg",name:"Design Background 1",type:"preset"},{id:"preset-6",url:"/src/assets/images/RW-Image-design-2.jpg",name:"Design Background 2",type:"preset"},{id:"preset-7",url:"/src/assets/images/RW-Image-design-3.jpg",name:"Design Background 3",type:"preset"},{id:"preset-8",url:"/src/assets/images/RW-Image-design-4.jpg",name:"Design Background 4",type:"preset"}]:l;return n?(0,yn.jsx)("div",{className:"image-library-overlay",children:(0,yn.jsxs)("div",{className:"image-library-modal",children:[(0,yn.jsxs)("div",{className:"image-library-header",children:[(0,yn.jsx)("h2",{children:"Image Library"}),(0,yn.jsx)("button",{className:"close-btn",onClick:r,children:"\xd7"})]}),(0,yn.jsxs)("div",{className:"image-library-tabs",children:[(0,yn.jsx)("button",{className:"tab-btn ".concat("library"===i?"active":""),onClick:()=>o("library"),children:"Image Library"}),(0,yn.jsx)("button",{className:"tab-btn ".concat("upload"===i?"active":""),onClick:()=>o("upload"),children:"Upload Images"})]}),(0,yn.jsxs)("div",{className:"image-library-content",children:["upload"===i&&(0,yn.jsxs)("div",{className:"upload-section",children:[(0,yn.jsxs)("div",{className:"upload-dropzone ".concat(c?"dragging":""),onDragOver:e=>{e.preventDefault(),u(!0)},onDragLeave:e=>{e.preventDefault(),u(!1)},onDrop:e=>{e.preventDefault(),u(!1),f(e.dataTransfer.files)},onClick:()=>{var e;return null===(e=d.current)||void 0===e?void 0:e.click()},children:[(0,yn.jsx)("div",{className:"upload-icon",children:"\ud83d\udcc1"}),(0,yn.jsx)("p",{children:"Drag & drop images here or click to browse"}),(0,yn.jsx)("small",{children:"Supports JPG, PNG, GIF formats"})]}),(0,yn.jsx)("input",{ref:d,type:"file",multiple:!0,accept:"image/*",onChange:e=>{f(e.target.files)},style:{display:"none"}})]}),(0,yn.jsx)("div",{className:"images-grid",children:0===p.length&&"upload"===i?(0,yn.jsx)("div",{className:"empty-state",children:(0,yn.jsx)("p",{children:"No uploaded images yet. Start by uploading some images above!"})}):p.map(e=>(0,yn.jsxs)("div",{className:"image-item",onClick:()=>{return t=e.url,a(t),void r();var t},children:[(0,yn.jsx)("div",{className:"image-preview",children:(0,yn.jsx)("img",{src:e.url,alt:e.name})}),(0,yn.jsxs)("div",{className:"image-info",children:[(0,yn.jsx)("p",{className:"image-name",children:e.name}),(0,yn.jsx)("span",{className:"image-type",children:e.type})]})]},e.id))})]}),(0,yn.jsx)("div",{className:"image-library-footer",children:(0,yn.jsx)("button",{className:"btn btn-secondary",onClick:r,children:"Cancel"})})]})}):null},aa=e=>{let{title:n,subtitle:r,backgroundImage:a,onTitleChange:i,onSubtitleChange:o,onBackgroundImageChange:l,onNext:s}=e;const[c,u]=(0,t.useState)(null),[d,f]=(0,t.useState)(!1),p=e=>{u(e)},m=()=>{u(null)};return(0,yn.jsxs)("div",{className:"front-page",children:[(0,yn.jsxs)("div",{className:"page-header",children:[(0,yn.jsx)("h1",{className:"pages-title",children:"PAGES"}),(0,yn.jsx)("div",{className:"flipbook-title",children:(0,yn.jsx)(na,{value:n,onChange:i,placeholder:"Hanash",isActive:"title"===c,onFocus:()=>p("title"),onBlur:m,className:"title-editor"})})]}),(0,yn.jsxs)("div",{className:"main-content",onClick:()=>{f(!0)},style:{backgroundImage:a?"url(".concat(a,")"):"none"},children:[(0,yn.jsx)("div",{className:"decorative-border-pattern"}),(0,yn.jsxs)("div",{className:"center-content",children:[(0,yn.jsx)("div",{className:"name-section",children:(0,yn.jsx)(na,{value:r||"ZARA IRUM",onChange:o,placeholder:"ZARA IRUM",isActive:"subtitle"===c,onFocus:()=>p("subtitle"),onBlur:m,className:"main-name-editor"})}),(0,yn.jsx)("div",{className:"profession-title",children:"ARCHITECT"})]}),!a&&(0,yn.jsx)("div",{className:"upload-hint",children:(0,yn.jsx)("span",{children:"Click to add background image"})})]}),(0,yn.jsx)("div",{className:"navigation-controls",children:(0,yn.jsx)("button",{className:"next-page-btn",onClick:s,children:"\u2192"})}),(0,yn.jsx)(ra,{isOpen:d,onClose:()=>f(!1),onSelectImage:e=>{l(e),f(!1)}})]})},ia=e=>{let{sections:n,profileImage:r,onSectionChange:a,onSectionDelete:i,onSectionReorder:o,onProfileImageChange:l,onTextEditorFocus:s,onTextEditorBlur:c,pendingFormatting:u,onFormattingApplied:d,onPrevious:f,onNext:p}=e;const[m,h]=(0,t.useState)(null),[g,v]=(0,t.useState)(!1),[b,y]=(0,t.useState)(null),[x,k]=(0,t.useState)(null),[w,S]=(0,t.useState)(null),A=e=>{h(e)},C=()=>{h(null)},j=n.length>0?n:[{id:"objective",title:"Objective",content:"To find a position as a junior designer in a residential architectural firm, preferably located in New York City."},{id:"professional",title:"Professional Interests",content:"As a 2016 summer intern at SOM, I have focused on office layouts, boost strength and color desk optimization."},{id:"personal",title:"Personal Statement",content:"Previously, I was a summer intern with Cesar Pelli, where we focused on environmental design."},{id:"education",title:"Education",content:"Bachelor Degree - London, August 2013 to Present\nInstitute, Graphics and Visualisation Environment Administrative\nAssembly prepared project consultation document, Review shop drawings and project materials for performance compliance."},{id:"experience",title:"Experience",content:"The Architecture Foundation - London, February 2008 - January 2009\nInterns, Graphics and Visualisation Recreations Administrative office administration"},{id:"skills",title:"Skill Proficiencies",content:"CAD maintenance\nPublic event and exhibition setup\nTeam management and scheduling"}],N=j.slice(0,3),P=j.slice(3),E=(e,t)=>{y(t),e.dataTransfer.effectAllowed="move",e.dataTransfer.setData("text/plain",t)},T=()=>{y(null),k(null),S(null)},I=(e,t,n)=>{e.preventDefault(),e.dataTransfer.dropEffect="move",k(t),void 0!==n&&S(n)},O=e=>{const t=e.currentTarget.getBoundingClientRect(),n=e.clientX,r=e.clientY;(n<t.left||n>t.right||r<t.top||r>t.bottom)&&(k(null),S(null))},F=(e,t,n)=>{if(e.preventDefault(),!b)return;const r=j.find(e=>e.id===b);if(!r)return;const a=[...j],i=a.findIndex(e=>e.id===b);let l;a.splice(i,1),"left"===t?(l=void 0!==n?n:0,l=Math.min(l,2)):(l=void 0!==n?n+3:3,l=Math.max(l,3)),a.splice(l,0,r),o(a),y(null),k(null),S(null)};return(0,yn.jsxs)("div",{className:"resume-page",children:[(0,yn.jsxs)("div",{className:"resume-content",children:[(0,yn.jsxs)("div",{className:"left-column",children:[(0,yn.jsx)("div",{className:"name-section",children:(0,yn.jsx)("h1",{children:"Zara Irum"})}),(0,yn.jsx)("div",{className:"image-section",children:(0,yn.jsx)("div",{className:"image-container",onClick:()=>{v(!0)},children:r?(0,yn.jsxs)("div",{className:"profile-image",children:[(0,yn.jsx)("img",{src:r,alt:"Profile"}),(0,yn.jsx)("div",{className:"image-overlay",children:(0,yn.jsx)("span",{children:"Change Image"})})]}):(0,yn.jsxs)("div",{className:"image-placeholder",children:[(0,yn.jsx)("div",{className:"placeholder-icon",children:"\ud83d\uddbc\ufe0f"}),(0,yn.jsx)("p",{children:"Click to add image"})]})})}),(0,yn.jsxs)("div",{className:"left-sections ".concat("left"===x?"drag-over":""),onDragOver:e=>I(e,"left"),onDragLeave:O,onDrop:e=>F(e,"left"),children:[N.map((e,t)=>(0,yn.jsxs)("div",{className:"resume-section ".concat(b===e.id?"dragging":""," ").concat("left"===x&&w===t?"drag-over-item":""),"data-section-id":e.id,draggable:!0,onDragStart:t=>E(t,e.id),onDragEnd:T,onDragOver:e=>I(e,"left",t),children:[(0,yn.jsxs)("div",{className:"section-header",children:[(0,yn.jsx)("div",{className:"drag-handle",title:"Drag to reorder",children:(0,yn.jsx)("span",{children:"\u22ee\u22ee"})}),(0,yn.jsx)("h3",{children:e.title}),(0,yn.jsx)("button",{className:"delete-section-btn",onClick:()=>i(e.id),title:"Delete Section",children:"\ud83d\uddd1\ufe0f"})]}),(0,yn.jsx)("div",{className:"section-content",children:(0,yn.jsx)(na,{value:e.content,onChange:t=>a(e.id,t),placeholder:"Enter ".concat(e.title.toLowerCase()," details..."),isActive:m===e.id,onFocus:()=>{if(A(e.id),s){const t=document.querySelector('[data-section-id="'.concat(e.id,'"] .text-editor'));t&&s(t,{fontFamily:"Arial",fontSize:"14",bold:!1,italic:!1,underline:!1,textAlign:"left",color:"#000000",backgroundColor:"transparent",lineHeight:"1.4",letterSpacing:"0"})}},onBlur:e=>{C(),null===c||void 0===c||c(e)},pendingFormatting:u,onFormattingApplied:d,className:"resume-text-editor"})})]},e.id)),"left"===x&&(0,yn.jsx)("div",{className:"drop-zone-indicator",children:"Drop section here"})]})]}),(0,yn.jsxs)("div",{className:"right-column",children:[(0,yn.jsx)("div",{className:"right-header",children:(0,yn.jsx)("h2",{children:"Resume Highlights"})}),(0,yn.jsxs)("div",{className:"right-sections ".concat("right"===x?"drag-over":""),onDragOver:e=>I(e,"right"),onDragLeave:O,onDrop:e=>F(e,"right"),children:[P.map((e,t)=>(0,yn.jsxs)("div",{className:"resume-section ".concat(b===e.id?"dragging":""," ").concat("right"===x&&w===t?"drag-over-item":""),"data-section-id":e.id,draggable:!0,onDragStart:t=>E(t,e.id),onDragEnd:T,onDragOver:e=>I(e,"right",t),children:[(0,yn.jsxs)("div",{className:"section-header",children:[(0,yn.jsx)("div",{className:"drag-handle",title:"Drag to reorder",children:(0,yn.jsx)("span",{children:"\u22ee\u22ee"})}),(0,yn.jsx)("h3",{children:e.title}),(0,yn.jsx)("button",{className:"delete-section-btn",onClick:()=>i(e.id),title:"Delete Section",children:"\ud83d\uddd1\ufe0f"})]}),(0,yn.jsx)("div",{className:"section-content",children:(0,yn.jsx)(na,{value:e.content,onChange:t=>a(e.id,t),placeholder:"Enter ".concat(e.title.toLowerCase()," information..."),isActive:m===e.id,onFocus:()=>{if(A(e.id),s){const t=document.querySelector('[data-section-id="'.concat(e.id,'"] .text-editor'));t&&s(t,{fontFamily:"Arial",fontSize:"14",bold:!1,italic:!1,underline:!1,textAlign:"left",color:"#000000",backgroundColor:"transparent",lineHeight:"1.4",letterSpacing:"0"})}},onBlur:e=>{C(),null===c||void 0===c||c(e)},pendingFormatting:u,onFormattingApplied:d,className:"resume-text-editor"})})]},e.id)),"right"===x&&(0,yn.jsx)("div",{className:"drop-zone-indicator",children:"Drop section here"})]})]}),(0,yn.jsxs)("div",{className:"navigation-controls",children:[(0,yn.jsx)("button",{className:"nav-btn prev-btn",onClick:f,title:"Previous Page",children:(0,yn.jsx)("span",{className:"arrow-icon",children:"\u2190"})}),(0,yn.jsx)("button",{className:"nav-btn next-btn",onClick:p,title:"Next Page",children:(0,yn.jsx)("span",{className:"arrow-icon",children:"\u2192"})})]}),(0,yn.jsxs)("div",{className:"add-section-controls",children:[(0,yn.jsx)("button",{className:"add-left-section-btn",title:"Add Left Section",children:"+ ADD LEFT SECTION"}),(0,yn.jsx)("button",{className:"add-right-section-btn",title:"Add Right Section",children:"+ ADD RIGHT SECTION"})]})]}),(0,yn.jsx)("div",{className:"background-pattern"}),(0,yn.jsx)(ra,{isOpen:g,onClose:()=>v(!1),onSelectImage:e=>{l(e),v(!1)}})]})},oa=e=>{let{isOpen:n,onClose:r,contactInfo:a,onSave:i}=e;const[o,l]=(0,t.useState)(a),[s,c]=(0,t.useState)({}),[u,d]=(0,t.useState)(!1);(0,t.useEffect)(()=>{n&&(l(a),c({}))},[n,a]);const f=(e,t)=>{l(n=>(0,tn.A)((0,tn.A)({},n),{},{[e]:t})),s[e]&&c(t=>(0,tn.A)((0,tn.A)({},t),{},{[e]:void 0}))},p=()=>{u||r()};return n?(0,yn.jsx)("div",{className:"contact-details-overlay",children:(0,yn.jsxs)("div",{className:"contact-details-modal",children:[(0,yn.jsxs)("div",{className:"modal-header",children:[(0,yn.jsxs)("div",{className:"gear-header",children:[(0,yn.jsx)("div",{className:"gear-icon main-gear",children:"\u2699\ufe0f"}),(0,yn.jsx)("div",{className:"gear-icon small-gear-1",children:"\u2699\ufe0f"}),(0,yn.jsx)("div",{className:"gear-icon small-gear-2",children:"\u2699\ufe0f"})]}),(0,yn.jsx)("h2",{children:"YOUR BUSINESS CARD"}),(0,yn.jsx)("button",{className:"close-btn",onClick:p,disabled:u,children:"\xd7"})]}),(0,yn.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),(()=>{const e={};return o.name.trim()||(e.name="Name is required"),o.mobile.trim()?/^\+?[\d\s\-\(\)]{10,}$/.test(o.mobile.replace(/\s/g,""))||(e.mobile="Please enter a valid mobile number"):e.mobile="Mobile number is required",o.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o.email)||(e.email="Please enter a valid email address"):e.email="Email is required",o.flipbookUrl.trim()?/^https?:\/\/.+/.test(o.flipbookUrl)||(e.flipbookUrl="Please enter a valid URL (starting with http:// or https://)"):e.flipbookUrl="Flipbook URL is required",c(e),0===Object.keys(e).length})()){d(!0);try{await new Promise(e=>setTimeout(e,500)),i(o),r()}catch(t){console.error("Error saving contact details:",t)}finally{d(!1)}}},className:"contact-form",children:[(0,yn.jsxs)("div",{className:"form-group",children:[(0,yn.jsx)("label",{htmlFor:"name",children:"Name"}),(0,yn.jsx)("input",{type:"text",value:o.name,onChange:e=>f("name",e.target.value),className:s.name?"error":"",placeholder:"Enter your full name",disabled:u}),s.name&&(0,yn.jsx)("span",{className:"error-message",children:s.name})]}),(0,yn.jsxs)("div",{className:"form-group",children:[(0,yn.jsx)("label",{htmlFor:"mobile",children:"Mobile Number"}),(0,yn.jsx)("input",{id:"mobile",type:"tel",value:o.mobile,onChange:e=>f("mobile",(e=>{const t=e.replace(/[^\d+]/g,"");return t.startsWith("+")?t:t.replace(/\+/g,"")})(e.target.value)),className:s.mobile?"error":"",placeholder:"+****************",disabled:u}),s.mobile&&(0,yn.jsx)("span",{className:"error-message",children:s.mobile}),(0,yn.jsx)("small",{className:"field-hint",children:"Include country code for international numbers"})]}),(0,yn.jsxs)("div",{className:"form-group",children:[(0,yn.jsx)("label",{htmlFor:"email",children:"Email"}),(0,yn.jsx)("input",{id:"email",type:"email",value:o.email,onChange:e=>f("email",e.target.value),className:s.email?"error":"",placeholder:"<EMAIL>",disabled:u}),s.email&&(0,yn.jsx)("span",{className:"error-message",children:s.email})]}),(0,yn.jsxs)("div",{className:"form-group",children:[(0,yn.jsx)("label",{htmlFor:"flipbookUrl",children:"Flipbook URL"}),(0,yn.jsx)("input",{id:"flipbookUrl",type:"url",value:o.flipbookUrl,onChange:e=>f("flipbookUrl",e.target.value),className:s.flipbookUrl?"error":"",placeholder:"https://your-flipbook.com/portfolio",disabled:u}),s.flipbookUrl&&(0,yn.jsx)("span",{className:"error-message",children:s.flipbookUrl}),(0,yn.jsx)("small",{className:"field-hint",children:"This will be the public URL for your flipbook"})]}),(0,yn.jsxs)("div",{className:"form-actions",children:[(0,yn.jsx)("button",{type:"button",className:"btn btn-secondary",onClick:p,disabled:u,children:"Cancel"}),(0,yn.jsx)("button",{type:"submit",className:"btn btn-primary",disabled:u,children:u?(0,yn.jsxs)(yn.Fragment,{children:[(0,yn.jsx)("span",{className:"loading-spinner"}),"Saving..."]}):"Save Changes"})]})]}),(0,yn.jsxs)("div",{className:"contact-preview",children:[(0,yn.jsx)("h3",{children:"Preview"}),(0,yn.jsxs)("div",{className:"preview-card",children:[(0,yn.jsxs)("div",{className:"preview-item",children:[(0,yn.jsx)("strong",{children:"Name:"})," ",o.name||"Your Name"]}),(0,yn.jsxs)("div",{className:"preview-item",children:[(0,yn.jsx)("strong",{children:"Mobile:"})," ",o.mobile||"Your Number"]}),(0,yn.jsxs)("div",{className:"preview-item",children:[(0,yn.jsx)("strong",{children:"Email:"})," ",o.email||"Your Email"]}),(0,yn.jsxs)("div",{className:"preview-item",children:[(0,yn.jsx)("strong",{children:"Portfolio URL:"})," ",o.flipbookUrl||"Your URL"]})]})]})]})}):null},la=e=>{let{isOpen:n,onClose:r,onSubmit:a}=e;const[i,o]=(0,t.useState)("");if(!n)return null;const l=()=>{o(""),r()};return(0,yn.jsx)("div",{className:"name-flipbook-overlay",onClick:l,children:(0,yn.jsxs)("div",{className:"name-flipbook-modal",onClick:e=>e.stopPropagation(),children:[(0,yn.jsx)("button",{className:"close-btn",onClick:l,title:"Close",children:"\xd7"}),(0,yn.jsxs)("div",{className:"modal-content",children:[(0,yn.jsx)("div",{className:"flipbook-icon",children:(0,yn.jsxs)("div",{className:"book-icon",children:[(0,yn.jsx)("div",{className:"book-cover",children:(0,yn.jsx)("div",{className:"book-logo",children:"T"})}),(0,yn.jsx)("div",{className:"book-pages"})]})}),(0,yn.jsx)("h2",{className:"modal-title",children:"NAME YOUR FLIPBOOK"}),(0,yn.jsxs)("form",{onSubmit:e=>{e.preventDefault(),i.trim()&&a(i.trim())},className:"name-form",children:[(0,yn.jsx)("div",{className:"input-wrapper",children:(0,yn.jsx)("input",{type:"text",value:i,onChange:e=>o(e.target.value),placeholder:"Enter flipbook name",className:"name-input",autoFocus:!0})}),(0,yn.jsx)("button",{type:"submit",className:"submit-btn",disabled:!i.trim(),children:"LET'S GO!"})]})]})]})})},sa=e=>{let{isOpen:n,onClose:r,onSelectTemplate:a}=e;const[i,o]=(0,t.useState)(null);if(!n)return null;return(0,yn.jsx)("div",{className:"add-page-modal-overlay",onClick:r,children:(0,yn.jsxs)("div",{className:"add-page-modal",onClick:e=>e.stopPropagation(),children:[(0,yn.jsxs)("div",{className:"modal-header",children:[(0,yn.jsx)("button",{className:"close-btn",onClick:r,children:"\xd7"}),(0,yn.jsx)("h2",{className:"modal-title",children:"\u2022 ADD A PAGE \u2022"}),(0,yn.jsx)("p",{className:"modal-subtitle",children:"All default shadow text and images will display as blank"})]}),(0,yn.jsxs)("div",{className:"modal-content",children:[(0,yn.jsxs)("div",{className:"template-section",children:[(0,yn.jsx)("h3",{className:"section-title",children:"DOUBLE SPREADS"}),(0,yn.jsx)("div",{className:"templates-grid",children:[{id:"left-plain",name:"Left Plain",category:"double-spreads",preview:"left-plain"},{id:"left-overflow-tiles",name:"Left Overflow & Tiles",category:"double-spreads",preview:"left-overflow-tiles"},{id:"panoramica",name:"Panoramica",category:"double-spreads",preview:"panoramica"},{id:"left-overflow",name:"Left Overflow",category:"double-spreads",preview:"left-overflow"},{id:"left-bleed",name:"Left Bleed",category:"double-spreads",preview:"left-bleed"},{id:"album",name:"Album",category:"double-spreads",preview:"album"},{id:"introduction-resume",name:"Introduction/Resume",category:"double-spreads",preview:"introduction-resume"}].map(e=>(0,yn.jsxs)("div",{className:"template-card ".concat((null===i||void 0===i?void 0:i.id)===e.id?"selected":""),onClick:()=>o(e),children:[(0,yn.jsx)("div",{className:"template-preview ".concat(e.preview),children:(0,yn.jsxs)("div",{className:"preview-content",children:["left-plain"===e.preview&&(0,yn.jsxs)(yn.Fragment,{children:[(0,yn.jsxs)("div",{className:"left-section",children:[(0,yn.jsx)("div",{className:"image-placeholder"}),(0,yn.jsxs)("div",{className:"text-lines",children:[(0,yn.jsx)("div",{className:"text-line"}),(0,yn.jsx)("div",{className:"text-line"})]})]}),(0,yn.jsxs)("div",{className:"right-section",children:[(0,yn.jsxs)("div",{className:"text-block",children:[(0,yn.jsx)("div",{className:"text-line long"}),(0,yn.jsx)("div",{className:"text-line"}),(0,yn.jsx)("div",{className:"text-line"})]}),(0,yn.jsxs)("div",{className:"image-grid",children:[(0,yn.jsx)("div",{className:"small-image"}),(0,yn.jsx)("div",{className:"small-image"}),(0,yn.jsx)("div",{className:"small-image"})]})]})]}),"left-overflow-tiles"===e.preview&&(0,yn.jsxs)(yn.Fragment,{children:[(0,yn.jsx)("div",{className:"large-image-left"}),(0,yn.jsxs)("div",{className:"right-tiles",children:[(0,yn.jsx)("div",{className:"text-header"}),(0,yn.jsxs)("div",{className:"tile-grid",children:[(0,yn.jsx)("div",{className:"tile"}),(0,yn.jsx)("div",{className:"tile"}),(0,yn.jsx)("div",{className:"tile"}),(0,yn.jsx)("div",{className:"tile"})]})]})]}),"panoramica"===e.preview&&(0,yn.jsxs)(yn.Fragment,{children:[(0,yn.jsx)("div",{className:"panoramic-image"}),(0,yn.jsxs)("div",{className:"bottom-content",children:[(0,yn.jsxs)("div",{className:"text-block-left",children:[(0,yn.jsx)("div",{className:"text-line"}),(0,yn.jsx)("div",{className:"text-line"})]}),(0,yn.jsxs)("div",{className:"image-pair",children:[(0,yn.jsx)("div",{className:"small-image"}),(0,yn.jsx)("div",{className:"small-image"})]})]})]}),"left-overflow"===e.preview&&(0,yn.jsxs)(yn.Fragment,{children:[(0,yn.jsx)("div",{className:"overflow-image"}),(0,yn.jsxs)("div",{className:"right-content",children:[(0,yn.jsx)("div",{className:"text-header long"}),(0,yn.jsxs)("div",{className:"text-lines",children:[(0,yn.jsx)("div",{className:"text-line"}),(0,yn.jsx)("div",{className:"text-line"}),(0,yn.jsx)("div",{className:"text-line"})]})]})]}),!["left-plain","left-overflow-tiles","panoramica","left-overflow"].includes(e.preview)&&(0,yn.jsx)("div",{className:"generic-preview",children:(0,yn.jsx)("div",{className:"placeholder-text",children:e.name})})]})}),(0,yn.jsxs)("div",{className:"template-name",children:[(0,yn.jsx)("span",{children:e.name}),"Left Plain"===e.name&&(0,yn.jsx)("span",{className:"red-dot",children:"\u25cf"})]})]},e.id))})]}),(0,yn.jsxs)("div",{className:"template-section",children:[(0,yn.jsx)("h3",{className:"section-title",children:"DOUBLE SPREADS YOU CREATED"}),(0,yn.jsx)("div",{className:"templates-grid",children:[{id:"custom-1",name:"Custom Layout 1",category:"created",preview:"custom-1"},{id:"custom-2",name:"Custom Layout 2",category:"created",preview:"custom-2"},{id:"custom-3",name:"Custom Layout 3",category:"created",preview:"custom-3"},{id:"custom-4",name:"Custom Layout 4",category:"created",preview:"custom-4"}].map(e=>(0,yn.jsxs)("div",{className:"template-card custom ".concat((null===i||void 0===i?void 0:i.id)===e.id?"selected":""),onClick:()=>o(e),children:[(0,yn.jsxs)("div",{className:"template-preview custom",children:[(0,yn.jsx)("button",{className:"delete-btn",children:"\xd7"}),(0,yn.jsx)("div",{className:"custom-preview-content",children:(0,yn.jsx)("div",{className:"custom-layout"})})]}),(0,yn.jsx)("div",{className:"template-name",children:(0,yn.jsx)("span",{children:e.name})})]},e.id))})]}),(0,yn.jsxs)("div",{className:"template-section",children:[(0,yn.jsx)("h3",{className:"section-title",children:"CREATE YOUR OWN DOUBLE SPREAD: drag and drop singlets below"}),(0,yn.jsxs)("div",{className:"custom-creation-area",children:[(0,yn.jsx)("div",{className:"drag-drop-zone",children:(0,yn.jsxs)("div",{className:"drag-instructions",children:["DRAG & DROP",(0,yn.jsx)("br",{}),"SINGLETS HERE"]})}),(0,yn.jsxs)("div",{className:"singlets-library",children:[(0,yn.jsx)("div",{className:"singlet-item",children:(0,yn.jsxs)("div",{className:"singlet-preview text-layout",children:[(0,yn.jsxs)("div",{className:"text-lines",children:[(0,yn.jsx)("div",{className:"text-line"}),(0,yn.jsx)("div",{className:"text-line"}),(0,yn.jsx)("div",{className:"text-line"})]}),(0,yn.jsx)("div",{className:"image-bottom"})]})}),(0,yn.jsx)("div",{className:"singlet-item",children:(0,yn.jsxs)("div",{className:"singlet-preview text-only",children:[(0,yn.jsx)("div",{className:"text-header"}),(0,yn.jsxs)("div",{className:"text-lines",children:[(0,yn.jsx)("div",{className:"text-line"}),(0,yn.jsx)("div",{className:"text-line"}),(0,yn.jsx)("div",{className:"text-line"})]})]})}),(0,yn.jsx)("div",{className:"singlet-item",children:(0,yn.jsxs)("div",{className:"singlet-preview image-text",children:[(0,yn.jsx)("div",{className:"header-line"}),(0,yn.jsxs)("div",{className:"content-row",children:[(0,yn.jsxs)("div",{className:"text-column",children:[(0,yn.jsx)("div",{className:"text-line"}),(0,yn.jsx)("div",{className:"text-line"})]}),(0,yn.jsx)("div",{className:"image-placeholder"})]})]})}),(0,yn.jsx)("div",{className:"singlet-item",children:(0,yn.jsxs)("div",{className:"singlet-preview two-column",children:[(0,yn.jsx)("div",{className:"header-full"}),(0,yn.jsxs)("div",{className:"two-col-content",children:[(0,yn.jsxs)("div",{className:"col-left",children:[(0,yn.jsx)("div",{className:"text-line"}),(0,yn.jsx)("div",{className:"text-line"})]}),(0,yn.jsx)("div",{className:"col-right",children:(0,yn.jsx)("div",{className:"image-placeholder"})})]})]})}),(0,yn.jsx)("div",{className:"singlet-item",children:(0,yn.jsxs)("div",{className:"singlet-preview grid-layout",children:[(0,yn.jsx)("div",{className:"header-line"}),(0,yn.jsxs)("div",{className:"grid-content",children:[(0,yn.jsx)("div",{className:"grid-item"}),(0,yn.jsx)("div",{className:"grid-item"}),(0,yn.jsx)("div",{className:"grid-item"})]})]})})]})]})]})]}),(0,yn.jsx)("div",{className:"modal-footer",children:(0,yn.jsx)("div",{className:"insert-page-container",children:(0,yn.jsxs)("button",{className:"insert-page-btn",onClick:()=>{i&&(a(i),r())},disabled:!i,children:[(0,yn.jsx)("div",{className:"insert-icon",children:(0,yn.jsx)("span",{className:"plus-sign",children:"+"})}),(0,yn.jsxs)("span",{children:["INSERT",(0,yn.jsx)("br",{}),"PAGE"]})]})})})]})})},ca=e=>{let{isVisible:n,currentFormatting:r,onFormatChange:a,onApplyFormatting:i}=e;const[o,l]=(0,t.useState)(!1),[s,c]=(0,t.useState)(!1),u=(0,t.useRef)(null),d=["#000000","#FFFFFF","#FF0000","#00FF00","#0000FF","#FFFF00","#FF00FF","#00FFFF","#800000","#008000","#000080","#808000","#800080","#008080","#C0C0C0","#808080","#9999FF","#993366","#FFFFCC","#CCFFFF","#660066","#FF8080","#0066CC","#CCCCFF"];(0,t.useEffect)(()=>{const e=e=>{u.current&&!u.current.contains(e.target)&&(l(!1),c(!1))};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);const f=e=>{a({textAlign:e});i({left:"justifyLeft",center:"justifyCenter",right:"justifyRight",justify:"justifyFull"}[e])},p=e=>{i(e?"insertOrderedList":"insertUnorderedList")};return n?(0,yn.jsxs)("div",{className:"text-toolbar",children:[(0,yn.jsx)("div",{className:"toolbar-section",children:(0,yn.jsx)("label",{className:"toolbar-label",children:"Text Toolbar"})}),(0,yn.jsx)("div",{className:"toolbar-section",children:(0,yn.jsx)("select",{className:"font-family-select",value:r.fontFamily,onChange:e=>{return t=e.target.value,a({fontFamily:t}),void i("fontName",t);var t},children:["Arial","Times New Roman","Helvetica","Georgia","Verdana","Trebuchet MS","Courier New","Impact","Comic Sans MS","Amenir"].map(e=>(0,yn.jsx)("option",{value:e,style:{fontFamily:e},children:e},e))})}),(0,yn.jsx)("div",{className:"toolbar-section",children:(0,yn.jsx)("select",{className:"font-size-select",value:r.fontSize,onChange:e=>{return t=e.target.value,a({fontSize:t}),void i("fontSize",t);var t},children:["8","9","10","11","12","14","16","18","20","22","24","26","28","30","32","36","48","72"].map(e=>(0,yn.jsxs)("option",{value:e,children:[e,"pt"]},e))})}),(0,yn.jsxs)("div",{className:"toolbar-section format-buttons",children:[(0,yn.jsx)("button",{className:"format-btn ".concat(r.bold?"active":""),onClick:()=>{const e=!r.bold;a({bold:e}),i("bold")},title:"Bold",children:(0,yn.jsx)("strong",{children:"B"})}),(0,yn.jsx)("button",{className:"format-btn ".concat(r.italic?"active":""),onClick:()=>{const e=!r.italic;a({italic:e}),i("italic")},title:"Italic",children:(0,yn.jsx)("em",{children:"I"})}),(0,yn.jsx)("button",{className:"format-btn ".concat(r.underline?"active":""),onClick:()=>{const e=!r.underline;a({underline:e}),i("underline")},title:"Underline",children:(0,yn.jsx)("u",{children:"U"})})]}),(0,yn.jsxs)("div",{className:"toolbar-section alignment-buttons",children:[(0,yn.jsx)("button",{className:"format-btn ".concat("left"===r.textAlign?"active":""),onClick:()=>f("left"),title:"Align Left",children:"\u2310"}),(0,yn.jsx)("button",{className:"format-btn ".concat("center"===r.textAlign?"active":""),onClick:()=>f("center"),title:"Align Center",children:"\u2261"}),(0,yn.jsx)("button",{className:"format-btn ".concat("right"===r.textAlign?"active":""),onClick:()=>f("right"),title:"Align Right",children:"\xac"}),(0,yn.jsx)("button",{className:"format-btn ".concat("justify"===r.textAlign?"active":""),onClick:()=>f("justify"),title:"Justify",children:"\u2263"})]}),(0,yn.jsxs)("div",{className:"toolbar-section list-buttons",children:[(0,yn.jsx)("button",{className:"format-btn",onClick:()=>p(!1),title:"Bullet List",children:"\u2022"}),(0,yn.jsx)("button",{className:"format-btn",onClick:()=>p(!0),title:"Numbered List",children:"1."})]}),(0,yn.jsxs)("div",{className:"toolbar-section color-section",children:[(0,yn.jsxs)("div",{className:"color-picker-container",ref:u,children:[(0,yn.jsx)("button",{className:"color-btn",onClick:()=>l(!o),title:"Text Color",children:(0,yn.jsx)("span",{className:"color-indicator",style:{backgroundColor:r.color},children:"A"})}),o&&(0,yn.jsx)("div",{className:"color-picker",children:(0,yn.jsx)("div",{className:"color-grid",children:d.map(e=>(0,yn.jsx)("button",{className:"color-option",style:{backgroundColor:e},onClick:()=>(e=>{a({color:e}),i("foreColor",e),l(!1)})(e),title:e},e))})})]}),(0,yn.jsxs)("div",{className:"color-picker-container",children:[(0,yn.jsx)("button",{className:"color-btn",onClick:()=>c(!s),title:"Background Color",children:(0,yn.jsx)("span",{className:"color-indicator bg-indicator",style:{backgroundColor:r.backgroundColor},children:"\u25a0"})}),s&&(0,yn.jsx)("div",{className:"color-picker",children:(0,yn.jsx)("div",{className:"color-grid",children:d.map(e=>(0,yn.jsx)("button",{className:"color-option",style:{backgroundColor:e},onClick:()=>(e=>{a({backgroundColor:e}),i("backColor",e),c(!1)})(e),title:e},e))})})]})]}),(0,yn.jsxs)("div",{className:"toolbar-section",children:[(0,yn.jsx)("label",{className:"toolbar-micro-label",children:"Leading"}),(0,yn.jsx)("select",{className:"line-height-select",value:r.lineHeight,onChange:e=>{return t=e.target.value,void a({lineHeight:t});var t},children:["1.0","1.2","1.4","1.6","1.8","2.0","2.5","3.0"].map(e=>(0,yn.jsx)("option",{value:e,children:e},e))})]}),(0,yn.jsxs)("div",{className:"toolbar-section",children:[(0,yn.jsx)("label",{className:"toolbar-micro-label",children:"Kerning"}),(0,yn.jsx)("select",{className:"letter-spacing-select",value:r.letterSpacing,onChange:e=>{return t=e.target.value,void a({letterSpacing:t});var t},children:["0","0.5px","1px","1.5px","2px","3px","4px","5px"].map(e=>(0,yn.jsx)("option",{value:e,children:e},e))})]}),(0,yn.jsx)("div",{className:"toolbar-section",children:(0,yn.jsx)("button",{className:"format-btn link-btn",onClick:()=>{const e=prompt("Enter URL:");e&&i("createLink",e)},title:"Insert Link",children:"\ud83d\udd17"})})]}):null},ua=()=>{const{portfolioId:e}=te(),n=ee(),{user:r}=on(),{userFlipbooks:a,updateFlipbook:i,refreshUserFlipbooks:o}=Hr(),[l,s]=(0,t.useState)(""),[c,u]=(0,t.useState)(1),[d,f]=(0,t.useState)(3),[p,m]=(0,t.useState)(!1),[h,g]=(0,t.useState)(!1),[v,b]=(0,t.useState)(null),[y,x]=(0,t.useState)(!1),[k,w]=(0,t.useState)(!1),[S,A]=(0,t.useState)(!1),[C,j]=(0,t.useState)(!1),[N,P]=(0,t.useState)(null),[E,T]=(0,t.useState)(null),[I,O]=(0,t.useState)([]),[F,R]=(0,t.useState)(null),[D,L]=(0,t.useState)({fontFamily:"Arial",fontSize:"14",bold:!1,italic:!1,underline:!1,textAlign:"left",color:"#000000",backgroundColor:"transparent",lineHeight:"1.4",letterSpacing:"0"}),[z,U]=(0,t.useState)(!1),[M,_]=(0,t.useState)(!1),[B,H]=(0,t.useState)(null),[W,V]=(0,t.useState)({title:"Welcome to My Portfolio",subtitle:"Professional Excellence in Every Detail",frontPageBackground:"",resumeSections:[{id:"objective",title:"Objective",content:"Enter your career objective here..."},{id:"education",title:"Education",content:"Enter your educational background..."},{id:"experience",title:"Experience",content:"Enter your work experience..."},{id:"skills",title:"Skill Proficiencies",content:"List your key skills..."},{id:"personal",title:"Personal Statement",content:"Write your personal statement..."},{id:"interests",title:"Professional Interests",content:"Describe your professional interests..."}],profileImage:"",contactInfo:{name:"Zara Irum",mobile:"************",email:"<EMAIL>",flipbookUrl:"https://flipbook.franklinreport.com/2137 2236 <EMAIL>"}});(0,t.useEffect)(()=>{if(e||S){m(!1);const t=a.find(t=>t.PortfolioID.toString()===e);if(t){s(t.PortfolioTitle);const e=Math.max(t.PageCount||3,3);console.log("Loading flipbook with page count:",e,"from stored:",t.PageCount),f(e)}else console.log("Flipbook not found, setting default 3 pages"),f(3)}else m(!0),w(!0)},[e,a,S]),(0,t.useEffect)(()=>{console.log("Total pages changed to:",d),O(Array.from({length:d},(e,t)=>t))},[d]);const q=()=>{P(null),T(null)},X=()=>{T(null)},Q=e=>0===e?"Front Cover":e===d-1?"Back Cover":"Page ".concat(e+1);return(0,yn.jsxs)("div",{className:"flipbook-editor",children:[(0,yn.jsx)(ca,{isVisible:z,currentFormatting:D,onFormatChange:e=>{const t=(0,tn.A)((0,tn.A)({},D),e);L(t),H(t),_(!1)},onApplyFormatting:(e,t)=>{if(F){if(F.applyCommand)F.applyCommand(e,t);else switch(F.focus(),e){case"bold":case"italic":case"underline":document.execCommand(e,!1);break;case"fontSize":const n=window.getSelection();if(n&&n.rangeCount>0){const e=n.getRangeAt(0);if(!e.collapsed){const n=document.createElement("span");n.style.fontSize=t+"pt";try{e.surroundContents(n)}catch(xa){document.execCommand("fontSize",!1,"7");F.querySelectorAll('font[size="7"]').forEach(e=>{e.style.fontSize=t+"pt"})}}}break;case"fontName":case"foreColor":case"backColor":case"justifyLeft":case"justifyCenter":case"justifyRight":case"justifyFull":case"insertUnorderedList":case"insertOrderedList":case"createLink":document.execCommand(e,!1,t)}H(D)}}}),(0,yn.jsxs)("div",{className:"editor-content",children:[(0,yn.jsxs)("div",{className:"editor-sidebar",children:[(0,yn.jsx)("div",{className:"toolbar-icons sidebar-section",children:(0,yn.jsxs)("div",{className:"icon-grid",children:[(0,yn.jsx)("button",{className:"icon-btn",title:"Select",children:(0,yn.jsx)("span",{children:"\ud83d\udccc"})}),(0,yn.jsx)("button",{className:"icon-btn",title:"Text",children:(0,yn.jsx)("span",{children:"A"})}),(0,yn.jsx)("button",{className:"icon-btn",title:"Image",children:(0,yn.jsx)("span",{children:"\ud83d\uddbc\ufe0f"})}),(0,yn.jsx)("button",{className:"icon-btn",title:"Shape",children:(0,yn.jsx)("span",{children:"\u2b1b"})}),(0,yn.jsx)("button",{className:"icon-btn",title:"Line",children:(0,yn.jsx)("span",{children:"\ud83d\udccf"})}),(0,yn.jsx)("button",{className:"icon-btn",title:"Crop",children:(0,yn.jsx)("span",{children:"\u2702\ufe0f"})})]})}),(0,yn.jsxs)("div",{className:"click-drag-section",children:["CLICK & DRAG",(0,yn.jsx)("br",{}),"TO REORDER PAGES"]}),(0,yn.jsx)("div",{className:"pages-section sidebar-section",children:I.map((e,t)=>{const n=0!==(r=e)&&r!==d-1;var r;const a=N===t,i=E===t;return(0,yn.jsxs)("div",{className:"page-thumbnail ".concat(c===t+1?"active":""," ").concat(a?"dragging":""," ").concat(i&&n?"drag-over":""," ").concat(n?"draggable":"not-draggable"),draggable:n,onClick:()=>u(t+1),onDragStart:e=>((e,t)=>{0!==t&&t!==d-1?(P(t),e.dataTransfer.effectAllowed="move",e.dataTransfer.setData("text/plain",t.toString())):e.preventDefault()})(e,t),onDragEnd:q,onDragOver:e=>((e,t)=>{e.preventDefault(),0!==t&&t!==d-1&&(e.dataTransfer.dropEffect="move",T(t))})(e,t),onDragLeave:X,onDrop:e=>((e,t)=>{if(e.preventDefault(),null===N||N===t)return;if(0===t||t===d-1)return;const n=[...I],r=n[N];n.splice(N,1);const a=N<t?t-1:t;n.splice(a,0,r),O(n),P(null),T(null),c===N+1&&u(a+1)})(e,t),title:n?"Drag to reorder - ".concat(Q(e)):Q(e),children:[n&&(0,yn.jsx)("div",{className:"page-drag-handle",children:(0,yn.jsx)("span",{children:"\u22ee\u22ee"})}),(0,yn.jsx)("span",{className:"page-label",children:Q(e)}),!n&&(0,yn.jsx)("div",{className:"fixed-page-indicator",children:(0,yn.jsx)("span",{children:"\ud83d\udccc"})})]},"page-".concat(e))})}),(0,yn.jsx)("div",{className:"add-page-section",children:(0,yn.jsx)("button",{className:"add-page-btn",onClick:()=>{j(!0)},children:"+ Add New Page"})})]}),(0,yn.jsx)("div",{className:"editor-canvas",children:(0,yn.jsx)("div",{className:"canvas-container",children:l?(0,yn.jsxs)("div",{className:"page-canvas",children:[(0,yn.jsxs)("div",{className:"pages-header",children:[(0,yn.jsx)("h1",{children:"PAGES"}),(0,yn.jsx)("h2",{className:"flipbook-title",children:l})]}),1===c&&d>=1&&(0,yn.jsx)(aa,{title:l||W.title,subtitle:W.subtitle,backgroundImage:W.frontPageBackground,onTitleChange:e=>{V(t=>(0,tn.A)((0,tn.A)({},t),{},{title:e}))},onSubtitleChange:e=>V(t=>(0,tn.A)((0,tn.A)({},t),{},{subtitle:e})),onBackgroundImageChange:e=>V(t=>(0,tn.A)((0,tn.A)({},t),{},{frontPageBackground:e})),onNext:()=>u(2)}),2===c&&d>=2&&(0,yn.jsx)(ia,{sections:W.resumeSections,profileImage:W.profileImage,onSectionChange:(e,t)=>{V(n=>(0,tn.A)((0,tn.A)({},n),{},{resumeSections:n.resumeSections.map(n=>n.id===e?(0,tn.A)((0,tn.A)({},n),{},{content:t}):n)}))},onSectionDelete:e=>{V(t=>(0,tn.A)((0,tn.A)({},t),{},{resumeSections:t.resumeSections.filter(t=>t.id!==e)}))},onSectionReorder:e=>{V(t=>(0,tn.A)((0,tn.A)({},t),{},{resumeSections:e}))},onProfileImageChange:e=>V(t=>(0,tn.A)((0,tn.A)({},t),{},{profileImage:e})),onTextEditorFocus:(e,t)=>{R(e),U(!0),!B&&t&&L(t)},onTextEditorBlur:e=>{if(null!==e&&void 0!==e&&e.relatedTarget){const t=e.relatedTarget,n=document.querySelector(".text-toolbar");if(n&&n.contains(t))return}setTimeout(()=>{const e=document.activeElement,t=document.querySelector(".text-toolbar");t&&t.contains(e)||e&&e.classList.contains("text-editor")||(U(!1),R(null))},150)},pendingFormatting:B,onFormattingApplied:()=>{H(null)},onPrevious:()=>u(1),onNext:()=>u(3)}),3===c&&d>=3&&(0,yn.jsx)("div",{className:"back-page",children:(0,yn.jsxs)("div",{className:"back-page-content",children:[(0,yn.jsxs)("div",{className:"contact-info-section",children:[(0,yn.jsx)("h2",{children:"Contact Information"}),(0,yn.jsxs)("div",{className:"contact-card",onClick:()=>x(!0),children:[(0,yn.jsxs)("div",{className:"contact-item",children:[(0,yn.jsx)("span",{className:"label",children:"Name:"}),(0,yn.jsx)("span",{className:"value",children:W.contactInfo.name})]}),(0,yn.jsxs)("div",{className:"contact-item",children:[(0,yn.jsx)("span",{className:"label",children:"Mobile:"}),(0,yn.jsx)("span",{className:"value",children:W.contactInfo.mobile})]}),(0,yn.jsxs)("div",{className:"contact-item",children:[(0,yn.jsx)("span",{className:"label",children:"Email:"}),(0,yn.jsx)("span",{className:"value",children:W.contactInfo.email})]}),(0,yn.jsxs)("div",{className:"contact-item",children:[(0,yn.jsx)("span",{className:"label",children:"Portfolio URL:"}),(0,yn.jsx)("span",{className:"value",children:W.contactInfo.flipbookUrl})]}),(0,yn.jsx)("div",{className:"edit-hint",children:"Click to edit contact details"})]})]}),(0,yn.jsxs)("div",{className:"flipbook-branding",children:[(0,yn.jsx)("div",{className:"brand-logo",children:"\ud83d\udcda Flipbook"}),(0,yn.jsx)("p",{children:"Created with Flipbook"})]}),(0,yn.jsx)("div",{className:"navigation-controls",children:(0,yn.jsx)("button",{className:"nav-btn prev-btn",onClick:()=>u(2),title:"Previous Page",children:(0,yn.jsx)("span",{className:"arrow-icon",children:"\u2190"})})})]})}),c>3&&(0,yn.jsxs)("div",{className:"page-content",children:[(0,yn.jsxs)("h1",{children:["Page ",c]}),(0,yn.jsx)("p",{children:"Additional page content can be added here."}),(0,yn.jsxs)("div",{className:"navigation-controls",children:[(0,yn.jsx)("button",{className:"nav-btn prev-btn",onClick:()=>u(Math.max(1,c-1)),title:"Previous Page",children:(0,yn.jsx)("span",{className:"arrow-icon",children:"\u2190"})}),c<d&&(0,yn.jsx)("button",{className:"nav-btn next-btn",onClick:()=>u(Math.min(d,c+1)),title:"Next Page",children:(0,yn.jsx)("span",{className:"arrow-icon",children:"\u2192"})})]})]})]}):(0,yn.jsx)("div",{className:"waiting-for-name",children:(0,yn.jsxs)("div",{className:"placeholder-content",children:[(0,yn.jsx)("h2",{children:"Creating New Flipbook..."}),(0,yn.jsx)("p",{children:"Please name your flipbook to continue"})]})})})})]}),(0,yn.jsx)(oa,{isOpen:y,onClose:()=>x(!1),contactInfo:W.contactInfo,onSave:e=>{V(t=>(0,tn.A)((0,tn.A)({},t),{},{contactInfo:e})),x(!1)}}),(0,yn.jsx)(la,{isOpen:k,onClose:()=>{n("/")},onSubmit:async e=>{try{const t=await an.createNewFlipbook({title:e,pageCount:3});if(t.success&&t.data){const r=t.data;s(e),w(!1),A(!0),await o(),n("/editor/".concat(r),{replace:!0})}else console.error("Failed to create flipbook:",t.error),alert("Failed to create flipbook. Please try again.")}catch(t){console.error("Error creating flipbook:",t),alert("An error occurred while creating the flipbook.")}}}),(0,yn.jsx)(sa,{isOpen:C,onClose:()=>j(!1),onSelectTemplate:e=>{f(e=>e+1),u(d+1),console.log("Selected template:",e)}})]})};var da=n(2398);const fa=e=>{let{portfolioId:n,pageCount:r,isInspiration:a,onClose:i,onCopyToAccount:o}=e;const[l,s]=(0,t.useState)(1),[c,u]=(0,t.useState)(!1),[d,f]=(0,t.useState)(!1),[p,m]=(0,t.useState)(""),[h,g]=(0,t.useState)(""),[v,b]=(0,t.useState)(!0);(0,t.useEffect)(()=>{console.log("Loading flipbook: ".concat(n," with ").concat(r," pages")),m("2154"===n?"Original Flipbook":"Flipbook ".concat(n))},[n,r]);const y=e=>{if(1===e)return'\n        <div class="book-content">\n          <div class="div-flip-main-left">\n            <style>\n              .flip_wrapper {\n                width: min(90vw, 1200px);\n                height: min(80vh, 900px);\n                margin: 0 auto 40px;\n                border: 1px solid #000000;\n                position: relative;\n                background: url(\'/Flipbooks/EndPapers/90-min.jpg\') 0% 0% / 200%;\n              }\n              .architect-content {\n                display: flex;\n                flex-direction: column;\n                align-items: center;\n                justify-content: center;\n                height: 100%;\n                text-align: center;\n                padding: 40px;\n                background-color: rgba(210, 196, 177, 0.9);\n                border: 7px solid rgb(210, 196, 177);\n              }\n              .architect-label {\n                font-family: \'AvenirLTStd-Book\', Arial, sans-serif;\n                font-size: 21pt;\n                color: #333;\n                margin-bottom: 10px;\n                letter-spacing: 2px;\n              }\n              .architect-title {\n                font-family: \'OptimaBold\', Arial, sans-serif;\n                font-size: 28pt;\n                font-weight: bold;\n                color: #333;\n                letter-spacing: 3px;\n                border-top: 3px solid #000;\n                padding-top: 10px;\n                width: 200px;\n                margin: 0 auto;\n              }\n            </style>\n            <div class="flip_wrapper">\n              <div class="architect-content" style="background-image: url(\''.concat(da.Ay.resumeInspiration.portfolio2.page1,'\'); background-size: cover; background-position: center;">\n                <div class="architect-label" style="background: rgba(255,255,255,0.9); padding: 10px 20px; border-radius: 5px;">LINK SALAS</div>\n                <div class="architect-title" style="background: rgba(255,255,255,0.9); padding: 10px 20px; border-radius: 5px; margin-top: 10px;">ARCHITECT</div>\n              </div>\n            </div>\n          </div>\n          <span class="page-number">').concat(e,"</span>\n        </div>\n      ");if(2===e)return'\n        <div class="book-content">\n          <div class="div-flip-main-left">\n            <div class="container divLayout2">\n              <div class="common_margin_layout">\n                <div class="bb-custom-wrapper">\n                  <div id="bb-bookblock" class="bb-bookblock">\n                    <div class="bb-item show_item">\n                      <div class="bg_end_paper" style="background: url(\''.concat(da.Ay.endPapers.big03,'\') 0% 0% / 100%;">\n                        <div style="border: 11px solid rgb(210, 196, 177); background-color: rgb(210, 196, 177); height: 100%;">\n                          <div class="divlayout2inner">\n                            <div class="custom_side_wrapper" style="display: flex; height: 100%;">\n                              <div class="bb-custom-side frederich_img frederich_img_Left" style="flex: 1; background: white; margin: clamp(8px, 1.2vw, 18px); padding: clamp(15px, 2.5vw, 35px); box-sizing: border-box; min-width: 0; overflow: visible;">\n                                <div class="content_wrap">\n                                  <div class="top_heading_wrap divTopHeadingLeft">\n                                    <div style="font-size: 23pt; font-family: \'Gill Sans\', Arial, sans-serif; font-weight: bold; margin-bottom: 20px;">Link Salas</div>\n                                  </div>\n                                  <div class="content_wrap_in ip_left">\n                                    <div class="left_sec">\n                                      <div style="margin-bottom: 20px;">\n                                        <div style="font-size: 16pt; font-family: \'Avenir\', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;">Objective</div>\n                                        <div style="font-size: 10pt; font-family: \'Palatino\', serif; line-height: 1.4;">To find a position as a junior designer in a residential architectural firm, preferably located in New York City.</div>\n                                      </div>\n                                      <div style="margin-bottom: 20px;">\n                                        <div style="font-size: 16pt; font-family: \'Avenir\', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;">Personal Statement</div>\n                                        <div style="font-size: 10pt; font-family: \'Palatino\', serif; line-height: 1.4;">My professors have consistently mentioned my strong spatial aptitude, fine drawing skills and AutoCAD expertise. I have a strong interest in neoclassical design, especially as it applies to modern office structures.</div>\n                                      </div>\n                                      <div style="margin-bottom: 20px;">\n                                        <div style="font-size: 16pt; font-family: \'Avenir\', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;">Professional Interests</div>\n                                        <div style="font-size: 10pt; font-family: \'Palatino\', serif; line-height: 1.4;">As a 2016 summer intern at SOM, I have focused on office layouts, beam strength and office desk optimization. I have the privilege of working with John Smith in the 200 Park Avenue renovation. Previously, I was a summer intern with Cesar Pelli, where we focused on enhancements to the San Francisco Billing Tower, particularly the elevator optimization.</div>\n                                      </div>\n                                    </div>\n                                  </div>\n                                </div>\n                              </div>\n                              <div class="bb-custom-side frederich_img frederich_img1" style="flex: 1; background: white; margin: clamp(8px, 1.2vw, 18px); padding: clamp(15px, 2.5vw, 35px); box-sizing: border-box; min-width: 0; overflow: visible;">\n                                <div class="content_wrap">\n                                  <div class="top_heading_wrap divTopHeadingRight">\n                                    <div style="font-size: 23pt; font-family: \'Gill Sans\', Arial, sans-serif; font-weight: bold; margin-bottom: 20px;">Resume Highlights</div>\n                                  </div>\n                                  <div class="content_wrap_in">\n                                    <div style="margin-bottom: 20px;">\n                                      <div style="font-size: 16pt; font-family: \'Avenir\', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;">Education</div>\n                                      <div style="font-size: 10pt; font-family: \'Palatino\', serif; line-height: 1.4;"><strong>University of Johannesburg - Jozi, South Africa</strong><br>Diploma in Architectural Technology (2011)</div>\n                                    </div>\n                                    <div style="margin-bottom: 20px;">\n                                      <div style="font-size: 16pt; font-family: \'Avenir\', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;">Experience</div>\n                                      <div style="font-size: 10pt; font-family: \'Palatino\', serif; line-height: 1.4;"><strong>Bespoke Careers - London, August 2012 to Present</strong><br>Interiors, Graphics and Visualisations Recruitment Administrator<br>Accurately prepared project consultation document. Revise shop drawings and project submittals for project compliance. Communicate and coordinate with clients, consultants, contractors and teams.</div>\n                                    </div>\n                                    <div style="margin-bottom: 20px;">\n                                      <div style="font-size: 16pt; font-family: \'Avenir\', Arial, sans-serif; font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px;">Skill Proficiencies</div>\n                                      <div style="font-size: 10pt; font-family: \'Palatino\', serif; line-height: 1.4;">FOH maintenance<br>Public event and exhibition setup<br>Intern management and scheduling</div>\n                                    </div>\n                                  </div>\n                                </div>\n                                <div class="footer_url" style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); text-align: center;">\n                                  <div style="font-size: 10pt; color: #333;">flipbook.franklinreport.com/Flipbook/UserPreview/LSalas1</div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <span class="page-number">').concat(e,"</span>\n        </div>\n      ");if(3===e)return'\n        <div class="book-content">\n          <div class="div-flip-main-right">\n            <style>\n              .resume-container-right {\n                width: min(90vw, 1200px);\n                height: min(80vh, 900px);\n                margin: 0 auto;\n                border: 11px solid rgb(210, 196, 177);\n                background-color: rgb(210, 196, 177);\n                display: flex;\n                font-family: Arial, sans-serif;\n                background: url(\'/Flipbooks/EndPapers/90-min.jpg\') 0% 0% / 100%;\n              }\n              .left-column-right, .right-column-right {\n                flex: 1;\n                padding: clamp(15px, 2.5vw, 35px);\n                background: white;\n                margin: clamp(8px, 1.2vw, 18px);\n                box-sizing: border-box;\n                min-width: 0;\n                overflow: visible;\n              }\n              .name-header-right {\n                font-family: \'GillSansMT-Bold\', Arial, sans-serif;\n                font-size: 23pt;\n                font-weight: bold;\n                margin-bottom: 20px;\n              }\n              .section-header-right {\n                font-family: \'AvenirLTStd-Book\', Arial, sans-serif;\n                font-size: 16pt;\n                font-weight: bold;\n                border-bottom: 1px solid #000;\n                padding-bottom: 5px;\n                margin: 15px 0 10px 0;\n              }\n              .section-content-right {\n                font-family: \'PalatinoLTStd\', Arial, sans-serif;\n                font-size: 10pt;\n                line-height: 1.4;\n                margin-bottom: 15px;\n              }\n              .footer-url-right {\n                position: absolute;\n                bottom: 20px;\n                left: 50%;\n                transform: translateX(-50%);\n                font-size: 10pt;\n                color: #333;\n                text-align: center;\n                width: 100%;\n              }\n            </style>\n            <div class="resume-container-right">\n              <div class="left-column-right">\n                <div class="name-header-right">Link Salas</div>\n                <div class="section-header-right">Objective</div>\n                <div class="section-content-right">To find a position as a junior designer in a residential architectural firm, preferably located in New York City.</div>\n                <div class="section-header-right">Personal Statement</div>\n                <div class="section-content-right">My professors have consistently mentioned my strong spatial aptitude, fine drawing skills and AutoCAD expertise. I have a strong interest in neoclassical design, especially as it applies to modern office structures.</div>\n                <div class="section-header-right">Professional Interests</div>\n                <div class="section-content-right">As a 2016 summer intern at SOM, I have focused on office layouts, beam strength and office desk optimization. I have the privilege of working with John Smith in the 200 Park Avenue renovation. Previously, I was a summer intern with Cesar Pelli, where we focused on enhancements to the San Francisco Billing Tower, particularly the elevator optimization.</div>\n              </div>\n              <div class="right-column-right">\n                <div class="name-header-right">Resume Highlights</div>\n                <div class="section-header-right">Education</div>\n                <div class="section-content-right"><strong>University of Johannesburg - Jozi, South Africa</strong><br>Diploma in Architectural Technology (2011)</div>\n                <div class="section-header-right">Experience</div>\n                <div class="section-content-right"><strong>Bespoke Careers - London, August 2012 to Present</strong><br>Interiors, Graphics and Visualisations Recruitment Administrator<br>Accurately prepared project consultation document. Revise shop drawings and project submittals for project compliance. Communicate and coordinate with clients, consultants, contractors and teams.<br><br><strong>The Architecture Foundation - London, February 2008 - January 2009</strong><br>Office administration</div>\n                <div class="section-header-right">Skill Proficiencies</div>\n                <div class="section-content-right">FOH maintenance<br>Public event and exhibition setup<br>Intern management and scheduling</div>\n              </div>\n            </div>\n            <div class="footer-url-right">flipbook.franklinreport.com/Flipbook/UserPreview/LSalas1</div>\n          </div>\n          <span class="page-number">'.concat(e,"</span>\n        </div>\n      ");if(4===e)return'\n        <div class="book-content">\n          <div class="div-flip-main-left">\n            <style>\n              .project-container {\n                width: min(90vw, 1200px);\n                height: min(80vh, 900px);\n                margin: 0 auto;\n                display: flex;\n                font-family: Arial, sans-serif;\n                background: white;\n              }\n              .project-image {\n                width: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                background: #f5f5f5;\n                border: 1px solid #ddd;\n              }\n              .project-image img {\n                width: 70%;\n                height: auto;\n                opacity: 0.7;\n              }\n              .project-content {\n                width: 50%;\n                padding: 49px;\n                display: flex;\n                flex-direction: column;\n                justify-content: flex-start;\n              }\n              .project-title {\n                font-family: \'AvenirLTStd-Book\', Arial, sans-serif;\n                font-size: 22pt;\n                color: #333;\n                margin-bottom: 10px;\n                font-weight: normal;\n              }\n              .project-byline {\n                font-family: \'MillerText-Roman\', serif;\n                font-size: 9pt;\n                color: #666;\n                margin-bottom: 20px;\n                line-height: 1.2;\n              }\n              .project-body {\n                font-family: \'PalatinoLTStd\', serif;\n                font-size: 8.25pt;\n                line-height: 1.6;\n                color: #333;\n                text-align: justify;\n              }\n            </style>\n            <div class="project-container">\n              <div class="project-image">\n                <img src="'.concat(da.Ay.architecture.grimshawBeyerBlinder,'" alt="Union Station" onError="this.src=\'').concat(da.Ay.flipbook.blankImg,'\'" />\n              </div>\n              <div class="project-content">\n                <div class="project-title">UNION STATION</div>\n                <div class="project-byline">In Washington, DC</div>\n                <div class="project-body">\n                  In 2015, Beyer Blinder &amp;Belle were selected to oversee the redesign of Washington DC\'s historic Union Station\u2013the remit was to take the iconic Beaux Arts transportation hub into its second century. Central to the challenge of the project was stabilizing its aging infrastructure and updating its internal systems without overly impacting the facade. The scope of work was significant, and the process involved collaboration with AmTrak, the city government, the federal government, and a small army of engineers, electricians, consultants, and other construction specialists.\n                </div>\n              </div>\n            </div>\n          </div>\n          <span class="page-number">').concat(e,"</span>\n        </div>\n      ");if(5===e)return'\n        <div class="book-content">\n          <div class="div-flip-main-right">\n            <style>\n              .project-container-right {\n                width: min(90vw, 1200px);\n                height: min(80vh, 900px);\n                margin: 0 auto;\n                display: flex;\n                font-family: Arial, sans-serif;\n                background: white;\n              }\n              .project-image-right {\n                width: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                background: #f5f5f5;\n                border: 1px solid #ddd;\n              }\n              .project-image-right img {\n                width: 70%;\n                height: auto;\n                opacity: 0.7;\n              }\n              .project-content-right {\n                width: 50%;\n                padding: 49px;\n                display: flex;\n                flex-direction: column;\n                justify-content: flex-start;\n              }\n              .project-title-right {\n                font-family: \'AvenirLTStd-Book\', Arial, sans-serif;\n                font-size: 22pt;\n                color: #333;\n                margin-bottom: 10px;\n                font-weight: normal;\n              }\n              .project-byline-right {\n                font-family: \'MillerText-Roman\', serif;\n                font-size: 9pt;\n                color: #666;\n                margin-bottom: 20px;\n                line-height: 1.2;\n              }\n              .project-body-right {\n                font-family: \'PalatinoLTStd\', serif;\n                font-size: 8.25pt;\n                line-height: 1.6;\n                color: #333;\n                text-align: justify;\n              }\n            </style>\n            <div class="project-container-right">\n              <div class="project-image-right">\n                <img src="'.concat(da.Ay.architecture.mcMillen,'" alt="Union Station Interior" onError="this.src=\'').concat(da.Ay.flipbook.blankImg,'\'" />\n              </div>\n              <div class="project-content-right">\n                <div class="project-title-right">UNION STATION</div>\n                <div class="project-byline-right">In Washington, DC</div>\n                <div class="project-body-right">\n                  In 2015, Beyer Blinder &amp;Belle were selected to oversee the redesign of Washington DC\'s historic Union Station\u2013the remit was to take the iconic Beaux Arts transportation hub into its second century. Central to the challenge of the project was stabilizing its aging infrastructure and updating its internal systems without overly impacting the facade. The scope of work was significant, and the process involved collaboration with AmTrak, the city government, the federal government, and a small army of engineers, electricians, consultants, and other construction specialists.\n                </div>\n              </div>\n            </div>\n          </div>\n          <span class="page-number">').concat(e,"</span>\n        </div>\n      ");if(6===e)return'\n        <div class="book-content">\n          <div class="div-flip-main-left">\n            <style>\n              .midcentury-container {\n                width: min(90vw, 1200px);\n                height: min(80vh, 900px);\n                margin: 0 auto;\n                display: flex;\n                font-family: Arial, sans-serif;\n                background: white;\n              }\n              .midcentury-image {\n                width: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                background: #f5f5f5;\n                border: 1px solid #ddd;\n              }\n              .midcentury-image img {\n                width: 90%;\n                height: 90%;\n                object-fit: cover;\n              }\n              .midcentury-content {\n                width: 50%;\n                padding: 40px;\n                display: flex;\n                flex-direction: column;\n                justify-content: flex-start;\n              }\n              .midcentury-title {\n                font-family: \'AvenirLTStd-Book\', Arial, sans-serif;\n                font-size: 22pt;\n                color: #333;\n                margin-bottom: 10px;\n                font-weight: normal;\n              }\n              .midcentury-byline {\n                font-family: \'MillerText-Roman\', serif;\n                font-size: 9pt;\n                color: #666;\n                margin-bottom: 20px;\n                line-height: 1.2;\n              }\n              .midcentury-body {\n                font-family: \'ACaslonPro-Regular\', serif;\n                font-size: 12pt;\n                line-height: 1.6;\n                color: #333;\n                text-align: justify;\n                margin-bottom: 30px;\n              }\n              .midcentury-images {\n                display: flex;\n                flex-direction: column;\n                gap: 15px;\n              }\n              .midcentury-small-image {\n                display: flex;\n                align-items: center;\n                gap: 15px;\n              }\n              .midcentury-small-image img {\n                width: 80px;\n                height: 60px;\n                object-fit: cover;\n                border: 1px solid #ddd;\n              }\n              .midcentury-caption {\n                font-family: \'MillerText-Roman\', serif;\n                font-size: 9pt;\n                color: #666;\n              }\n            </style>\n            <div class="midcentury-container">\n              <div class="midcentury-image">\n                <img src="'.concat(da.Ay.architecture.quincy,'" alt="Midcentury View" onError={(e) => {e.currentTarget.src = \'').concat(da.Ay.flipbook.blankImg,'\'}} />\n              </div>\n              <div class="midcentury-content">\n                <div class="midcentury-title">MIDCENTURY VIEW</div>\n                <div class="midcentury-byline">For the Harvey Family</div>\n                <div class="midcentury-body">\n                  This aerie retreat exemplifies midcentury California minimalism with a few rustic touches. As a junior architect on the project, I coordinated the structural research team to ensure adherence to local code.\n                  <br><br>\n                  Adobe Caslon\n                </div>\n                <div class="midcentury-images">\n                  <div class="midcentury-small-image">\n                    <img src="').concat(da.Ay.architecture.overlook,'" alt="Master Bedroom" onError={(e) => {e.currentTarget.src = \'').concat(da.Ay.flipbook.blankImg,'\'}} />\n                    <div class="midcentury-caption">Master Bedroom</div>\n                  </div>\n                  <div class="midcentury-small-image">\n                    <img src="').concat(da.Ay.architecture.kellerCenter,'" alt="The Sunset" onError={(e) => {e.currentTarget.src = \'').concat(da.Ay.flipbook.blankImg,'\'}} />\n                    <div class="midcentury-caption">The Sunset</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <span class="page-number">').concat(e,"</span>\n        </div>\n      ");return k(e)},x=(e,t)=>{const n=[da.Ay.architecture.grimshawBeyerBlinder,da.Ay.architecture.peterAaron,da.Ay.architecture.mcMillen,da.Ay.architecture.quincy,da.Ay.architecture.overlook,da.Ay.architecture.kellerCenter,da.Ay.architecture.shingleHouse,da.Ay.architecture.fredArchitect];return n[(3*e+t)%n.length]},k=e=>{const t=[{title:"DOWNTOWN LOFT RENOVATION",location:"In Manhattan, NYC",description:"A complete transformation of a 1920s industrial space into a modern residential loft. The project focused on maintaining the authentic brick and steel structure while introducing contemporary living elements. Custom millwork and strategic lighting highlight the dramatic ceiling heights.",images:["loft-main.jpg","loft-kitchen.jpg","loft-living.jpg"]},{title:"COASTAL RESIDENCE",location:"In Malibu, California",description:"This oceanfront home was designed to maximize the connection between interior and exterior spaces. Floor-to-ceiling glass walls provide unobstructed ocean views while weathered cedar siding helps the structure blend with the natural landscape.",images:["coastal-exterior.jpg","coastal-deck.jpg","coastal-interior.jpg"]},{title:"CORPORATE HEADQUARTERS",location:"In Seattle, Washington",description:"A 12-story mixed-use development combining office space with retail at street level. The design emphasizes natural light and flexible workspace configurations. Sustainable materials and energy-efficient systems earned the project LEED Gold certification.",images:["corp-exterior.jpg","corp-lobby.jpg","corp-office.jpg"]},{title:"HISTORIC CHURCH RESTORATION",location:"In Charleston, South Carolina",description:"Careful restoration of an 1850s Gothic Revival church damaged by hurricane flooding. The project required specialized conservation techniques to preserve original stonework while updating mechanical systems and accessibility features.",images:["church-exterior.jpg","church-interior.jpg","church-detail.jpg"]},{title:"MOUNTAIN CABIN RETREAT",location:"In Aspen, Colorado",description:"A contemporary interpretation of traditional alpine architecture. Local stone and reclaimed timber create harmony with the mountain environment while large windows frame dramatic valley views. Radiant floor heating and passive solar design ensure year-round comfort.",images:["cabin-exterior.jpg","cabin-interior.jpg","cabin-view.jpg"]},{title:"URBAN MIXED-USE DEVELOPMENT",location:"In Portland, Oregon",description:"A transit-oriented development combining affordable housing, retail, and community spaces. The design promotes walkability and social interaction through shared courtyards and ground-floor activation. Green roofs and rainwater collection systems address local environmental priorities.",images:["urban-street.jpg","urban-courtyard.jpg","urban-rooftop.jpg"]},{title:"UNIVERSITY LIBRARY EXPANSION",location:"In Berkeley, California",description:"A sensitive addition to a 1960s brutalist library building. The new wing provides collaborative study spaces and technology centers while respecting the existing concrete structure. Natural ventilation and daylighting reduce energy consumption.",images:["library-exterior.jpg","library-reading.jpg","library-study.jpg"]},{title:"WATERFRONT PARK PAVILION",location:"In San Diego, California",description:"A series of interconnected pavilions providing shelter and amenities for a popular bayside park. The structures use weathering steel and cast concrete to withstand marine conditions while creating dramatic shadows and framing water views.",images:["pavilion-water.jpg","pavilion-structure.jpg","pavilion-sunset.jpg"]}],n=Math.floor((e-7)/2)%t.length,r=t[n];return'\n      <div class="book-content">\n        <div class="'.concat(e%2===1?"div-flip-main-right":"div-flip-main-left",'" style="').concat("",'">\n          <div class="container">\n            <div class="common_margin_layout">\n              <div class="bb-custom-wrapper">\n                <div id="bb-bookblock" class="bb-bookblock">\n                  <div class="bb-item show_item">\n                    <div style="display: flex; height: 576px; background: white;">\n                      <div style="width: 50%; display: flex; align-items: center; justify-content: center; background: #f8f8f8; border: 1px solid #ddd;">\n                        <img src="').concat(x(n,0),'" alt="').concat(r.title,'" style="width: 90%; height: 90%; object-fit: cover;" onError="this.style.display=\'none\'" />\n                      </div>\n                      <div style="width: 50%; padding: 49px; display: flex; flex-direction: column; justify-content: flex-start;">\n                        <div style="font-family: \'Avenir\', Arial, sans-serif; font-size: 22pt; color: #333; margin-bottom: 10px; font-weight: normal;">').concat(r.title,"</div>\n                        <div style=\"font-family: 'Times New Roman', serif; font-size: 9pt; color: #666; margin-bottom: 20px; line-height: 1.2;\">").concat(r.location,"</div>\n                        <div style=\"font-family: 'Palatino', serif; font-size: 8.25pt; line-height: 1.6; color: #333; text-align: justify; margin-bottom: 30px;\">\n                          ").concat(r.description,'\n                        </div>\n                        <div style="display: flex; flex-direction: column; gap: 15px;">\n                          <div style="display: flex; align-items: center; gap: 15px;">\n                            <img src="').concat(x(n,1),'" alt="').concat(r.images[1],'" style="width: 60px; height: 45px; object-fit: cover; border: 1px solid #ccc; border-radius: 2px;" onError="this.style.display=\'none\'" />\n                            <div style="font-family: \'Times New Roman\', serif; font-size: 9pt; color: #666;">').concat(r.images[1],'</div>\n                          </div>\n                          <div style="display: flex; align-items: center; gap: 15px;">\n                            <img src="').concat(x(n,2),'" alt="').concat(r.images[2],'" style="width: 60px; height: 45px; object-fit: cover; border: 1px solid #ccc; border-radius: 2px;" onError="this.style.display=\'none\'" />\n                            <div style="font-family: \'Times New Roman\', serif; font-size: 9pt; color: #666;">').concat(r.images[2],'</div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <span class="page-number">').concat(e,"</span>\n      </div>\n    ")};(0,t.useEffect)(()=>{(async e=>{b(!0);try{g("2154"===n?y(e):'<div class="page-content"><h2>Page '.concat(e,"</h2><p>Content for page ").concat(e," of ").concat(p,"</p></div>"))}catch(t){console.error("Error loading page content:",t),g("2154"===n?y(e):'<div class="page-content"><h2>Page '.concat(e,"</h2><p>Content for page ").concat(e," of ").concat(p,"</p></div>"))}finally{b(!1)}})(l)},[l,n,a]);return(0,yn.jsx)("div",{className:"flipbook-preview-overlay",children:(0,yn.jsxs)("div",{className:"flipbook-preview",children:[(0,yn.jsx)("header",{className:"preview-header",children:(0,yn.jsx)("div",{className:"header-content",children:(0,yn.jsx)("div",{className:"header-left",children:(0,yn.jsx)("button",{className:"logo-btn",onClick:i,title:"Return to My Flipbooks",children:(0,yn.jsx)("span",{style:{color:"white",fontWeight:"bold",fontSize:"18px"},children:"Flipbook"})})})})}),a&&(0,yn.jsx)("div",{className:"inspiration-controls",children:(0,yn.jsxs)("div",{className:"controls-container",children:[(0,yn.jsx)("button",{className:"copy-btn",onClick:()=>f(!0),children:"COPY THIS INSPIRATION FLIPBOOK TO YOUR ACCOUNT TO EDIT & SAVE"}),(0,yn.jsx)("button",{className:"return-btn",onClick:i,children:"RETURN TO MY FLIPBOOKS"})]})}),!a&&(0,yn.jsxs)("div",{className:"user-controls",children:[(0,yn.jsxs)("div",{className:"controls-container",children:[(0,yn.jsx)("div",{className:"playback-controls",children:c?(0,yn.jsxs)("button",{className:"pause-btn",onClick:()=>{u(!1)},children:[(0,yn.jsx)("i",{className:"fa fa-pause"}),"\xa0\xa0Pause"]}):(0,yn.jsxs)("button",{className:"play-btn",onClick:()=>{u(!0)},children:[(0,yn.jsx)("i",{className:"fa fa-play"}),"\xa0\xa0Play"]})}),(0,yn.jsx)("button",{className:"return-btn",onClick:i,children:"Return to Page Builder"})]}),(0,yn.jsxs)("div",{className:"page-indicators",children:[(0,yn.jsx)("div",{className:"page-indicator first-page",children:"Front Cover Page"}),(0,yn.jsx)("div",{className:"page-indicator last-page",children:"Back Page"})]})]}),(0,yn.jsx)("div",{className:"flipbook-viewer",children:(0,yn.jsxs)("div",{className:"flipbook-canvas",children:[(0,yn.jsx)("div",{className:"book-container",children:(0,yn.jsx)("div",{className:"flipbook-content",children:v?(0,yn.jsxs)("div",{className:"page-loading",children:[(0,yn.jsx)("div",{className:"loading-spinner"}),(0,yn.jsxs)("p",{children:["Loading page ",l,"..."]})]}):(0,yn.jsx)("div",{className:"flipbook-page-html",dangerouslySetInnerHTML:{__html:h}})})}),(0,yn.jsxs)("div",{className:"navigation-controls",children:[(0,yn.jsx)("button",{className:"nav-btn prev-btn",onClick:()=>{l>1&&s(l-1)},disabled:1===l,children:(0,yn.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,yn.jsx)("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),(0,yn.jsx)("button",{className:"nav-btn next-btn",onClick:()=>{l<r&&s(l+1)},disabled:l===r,children:(0,yn.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,yn.jsx)("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),(0,yn.jsxs)("div",{className:"page-slider",children:[(0,yn.jsx)("input",{type:"range",min:"1",max:r,value:l,onChange:e=>{s(parseInt(e.target.value))},className:"slider"}),(0,yn.jsxs)("div",{className:"page-counter",children:[l," / ",r]})]})]})}),d&&(0,yn.jsx)("div",{className:"modal-overlay",children:(0,yn.jsx)("div",{className:"copy-modal",children:(0,yn.jsxs)("div",{className:"modal-content",children:[(0,yn.jsx)("button",{className:"close-btn",onClick:()=>f(!1),children:"\xd7"}),(0,yn.jsx)("div",{className:"modal-icon",children:(0,yn.jsxs)("svg",{width:"60",height:"60",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,yn.jsx)("path",{d:"M4 19.5A2.5 2.5 0 0 1 6.5 17H20",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,yn.jsx)("path",{d:"M6.5 2H20V22H6.5A2.5 2.5 0 0 1 4 19.5V4.5A2.5 2.5 0 0 1 6.5 2Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}),(0,yn.jsx)("h2",{children:"NAME MY FLIPBOOK"}),(0,yn.jsx)("div",{className:"input-group",children:(0,yn.jsx)("input",{type:"text",value:p,onChange:e=>m(e.target.value),placeholder:"Enter flipbook title",maxLength:38,className:"title-input"})}),(0,yn.jsx)("button",{className:"submit-btn",onClick:()=>{a&&o&&p.trim()&&(o(n,p.trim()),f(!1))},disabled:!p.trim(),children:"LET'S GO!"})]})})})]})})};var pa=n(9323);const ma=()=>{const{portfolioId:e}=te(),n=ee(),{loading:r,error:a}=(()=>{const[e,n]=(0,t.useState)([]),[r,a]=(0,t.useState)([]),[i,o]=(0,t.useState)([]),[l,s]=(0,t.useState)([pa.tS]),[c,u]=(0,t.useState)(!0),[d,f]=(0,t.useState)(null),p=(0,t.useCallback)(async()=>{try{u(!0),f(null),console.log("Loading initial app data...");const[e,t,r,i]=await Promise.all([an.getUIMessages(),an.getCommonValues(),an.getPortfolioDetailsInitial(),an.getInspirationFlipbooks()]);e.success?(console.log("UI Messages loaded:",e.data),n(e.data)):console.warn("Failed to load UI messages:",e.error),t.success?(console.log("Common values loaded:",t.data),a(t.data)):console.warn("Failed to load common values:",t.error),r.success?(console.log("Portfolio details loaded:",r.data),o(r.data)):console.warn("Failed to load portfolio details:",r.error),i.success?(console.log("Inspiration flipbooks loaded:",i.data),s(i.data)):(console.warn("Failed to load inspiration flipbooks:",i.error),s([pa.tS]),console.log("Using fallback inspiration flipbooks with Original Flipbook")),e.success||t.success||r.success||i.success||f("Failed to load initial application data. Please refresh the page.")}catch(e){console.error("Error loading initial app data:",e),f("An unexpected error occurred while loading application data."),s([pa.tS]),console.log("Using fallback Original Flipbook due to complete error")}finally{u(!1)}},[]),m=(0,t.useCallback)(async()=>{await p()},[p]);return(0,t.useEffect)(()=>{p()},[p]),{uiMessages:e,commonValues:r,portfolioDetails:i,inspirationFlipbooks:l,loading:c,error:d,refetch:m}})(),[i,o]=(0,t.useState)(null);(0,t.useEffect)(()=>{if(e){const t=[pa.tS].find(t=>t.PortfolioID.toString()===e);o(t||{FBTitle:"Flipbook ".concat(e),PortfolioID:parseInt(e),PageCount:10,TnImageSrc:"/images/flipbooks/placeholders/thumbnail-fallback.jpg"})}},[e]);const l=()=>{n("/")};return r?(0,yn.jsxs)("div",{className:"flipbook-viewer-loading",children:[(0,yn.jsx)("div",{className:"loading-spinner"}),(0,yn.jsx)("p",{children:"Loading flipbook..."})]}):a||!i?(0,yn.jsxs)("div",{className:"flipbook-viewer-error",children:[(0,yn.jsx)("h2",{children:"Flipbook Not Found"}),(0,yn.jsx)("p",{children:"The requested flipbook could not be found."}),(0,yn.jsx)("button",{onClick:l,className:"btn btn-primary",children:"Return to Dashboard"})]}):(0,yn.jsx)("div",{className:"flipbook-viewer",children:(0,yn.jsx)(fa,{portfolioId:i.PortfolioID.toString(),pageCount:i.PageCount,isInspiration:!0,onClose:l,onCopyToAccount:(e,t)=>{console.log("Copy to account:",e,t)}})})},ha=()=>{const e=ee();return(0,yn.jsx)("div",{className:"not-found",children:(0,yn.jsxs)("div",{className:"not-found-content",children:[(0,yn.jsx)("div",{className:"error-code",children:"404"}),(0,yn.jsx)("h1",{children:"Page Not Found"}),(0,yn.jsx)("p",{children:"Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL."}),(0,yn.jsxs)("div",{className:"not-found-actions",children:[(0,yn.jsx)("button",{className:"btn btn-primary",onClick:()=>{e("/")},children:"Return to Dashboard"}),(0,yn.jsx)("button",{className:"btn btn-secondary",onClick:()=>window.history.back(),children:"Go Back"})]}),(0,yn.jsxs)("div",{className:"helpful-links",children:[(0,yn.jsx)("h3",{children:"Helpful Links:"}),(0,yn.jsxs)("ul",{children:[(0,yn.jsx)("li",{children:(0,yn.jsx)("a",{href:"/",children:"Dashboard"})}),(0,yn.jsx)("li",{children:(0,yn.jsx)("a",{href:"/editor",children:"Create New Flipbook"})}),(0,yn.jsx)("li",{children:(0,yn.jsx)("a",{href:"/profile",children:"User Profile"})})]})]})]})})},ga=()=>(0,yn.jsxs)("div",{style:{padding:"40px",textAlign:"center"},children:[(0,yn.jsx)("h1",{children:"User Profile"}),(0,yn.jsx)("p",{children:"User profile functionality will be implemented here."}),(0,yn.jsx)("button",{onClick:()=>window.history.back(),children:"Go Back"})]}),va=()=>(0,yn.jsx)(ke,{children:(0,yn.jsxs)(be,{children:[(0,yn.jsx)(ge,{path:"/",element:(0,yn.jsx)(ta,{})}),(0,yn.jsx)(ge,{path:"/dashboard",element:(0,yn.jsx)(ta,{})}),(0,yn.jsx)(ge,{path:"/editor",element:(0,yn.jsx)(ua,{})}),(0,yn.jsx)(ge,{path:"/editor/:portfolioId",element:(0,yn.jsx)(ua,{})}),(0,yn.jsx)(ge,{path:"/viewer/:portfolioId",element:(0,yn.jsx)(ma,{})}),(0,yn.jsx)(ge,{path:"/preview/:portfolioId",element:(0,yn.jsx)(ma,{})}),(0,yn.jsx)(ge,{path:"/profile",element:(0,yn.jsx)(ga,{})}),(0,yn.jsx)(ge,{path:"/settings",element:(0,yn.jsx)(ga,{})}),(0,yn.jsx)(ge,{path:"/share/:portfolioId",element:(0,yn.jsx)(ma,{})}),(0,yn.jsx)(ge,{path:"/public/:portfolioId",element:(0,yn.jsx)(ma,{})}),(0,yn.jsx)(ge,{path:"/Flipbook/UserPreview/:portfolioId",element:(0,yn.jsx)(ma,{})}),(0,yn.jsx)(ge,{path:"/Users/<USER>/Flipbooks/:portfolioId",element:(0,yn.jsx)(ma,{})}),(0,yn.jsx)(ge,{path:"/flipbook",element:(0,yn.jsx)(he,{to:"/dashboard",replace:!0})}),(0,yn.jsx)(ge,{path:"/home",element:(0,yn.jsx)(he,{to:"/dashboard",replace:!0})}),(0,yn.jsx)(ge,{path:"*",element:(0,yn.jsx)(ha,{})})]})});const ba=function(){return(0,yn.jsx)("div",{className:"App",children:(0,yn.jsx)(va,{})})},ya=e=>{e&&e instanceof Function&&n.e(453).then(n.bind(n,6453)).then(t=>{let{getCLS:n,getFID:r,getFCP:a,getLCP:i,getTTFB:o}=t;n(e),r(e),a(e),i(e),o(e)})};a.createRoot(document.getElementById("root")).render((0,yn.jsx)(t.StrictMode,{children:(0,yn.jsx)(ba,{})})),ya()})()})();
//# sourceMappingURL=main.613643c8.js.map