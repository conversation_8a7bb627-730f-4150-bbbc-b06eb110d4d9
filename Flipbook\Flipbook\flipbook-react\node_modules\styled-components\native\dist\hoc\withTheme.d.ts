import React from 'react';
import { AnyComponent, ExecutionProps } from '../types';
export default function withTheme<T extends AnyComponent>(Component: T): React.ForwardRefExoticComponent<React.PropsWithoutRef<JSX.LibraryManagedAttributes<T, ExecutionProps>> & React.RefAttributes<T>> & { [key in Exclude<keyof T, T extends React.MemoExoticComponent<any> ? "propTypes" | "type" | "defaultProps" | "displayName" | "$$typeof" | "compare" : T extends React.ForwardRefExoticComponent<any> ? "propTypes" | "defaultProps" | "displayName" | "$$typeof" | "render" : "length" | "propTypes" | "type" | "contextType" | "contextTypes" | "childContextTypes" | "defaultProps" | "displayName" | "getDerivedStateFromProps" | "getDerivedStateFromError" | "getDefaultProps" | "mixins" | "name" | "prototype" | "caller" | "callee" | "arguments" | "arity">]: T[key]; };
