{"ast": null, "code": "/** @license React v17.0.2\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';\n\nvar b = 60103,\n  c = 60106,\n  d = 60107,\n  e = 60108,\n  f = 60114,\n  g = 60109,\n  h = 60110,\n  k = 60112,\n  l = 60113,\n  m = 60120,\n  n = 60115,\n  p = 60116,\n  q = 60121,\n  r = 60122,\n  u = 60117,\n  v = 60129,\n  w = 60131;\nif (\"function\" === typeof Symbol && Symbol.for) {\n  var x = Symbol.for;\n  b = x(\"react.element\");\n  c = x(\"react.portal\");\n  d = x(\"react.fragment\");\n  e = x(\"react.strict_mode\");\n  f = x(\"react.profiler\");\n  g = x(\"react.provider\");\n  h = x(\"react.context\");\n  k = x(\"react.forward_ref\");\n  l = x(\"react.suspense\");\n  m = x(\"react.suspense_list\");\n  n = x(\"react.memo\");\n  p = x(\"react.lazy\");\n  q = x(\"react.block\");\n  r = x(\"react.server.block\");\n  u = x(\"react.fundamental\");\n  v = x(\"react.debug_trace_mode\");\n  w = x(\"react.legacy_hidden\");\n}\nfunction y(a) {\n  if (\"object\" === typeof a && null !== a) {\n    var t = a.$$typeof;\n    switch (t) {\n      case b:\n        switch (a = a.type, a) {\n          case d:\n          case f:\n          case e:\n          case l:\n          case m:\n            return a;\n          default:\n            switch (a = a && a.$$typeof, a) {\n              case h:\n              case k:\n              case p:\n              case n:\n              case g:\n                return a;\n              default:\n                return t;\n            }\n        }\n      case c:\n        return t;\n    }\n  }\n}\nvar z = g,\n  A = b,\n  B = k,\n  C = d,\n  D = p,\n  E = n,\n  F = c,\n  G = f,\n  H = e,\n  I = l;\nexports.ContextConsumer = h;\nexports.ContextProvider = z;\nexports.Element = A;\nexports.ForwardRef = B;\nexports.Fragment = C;\nexports.Lazy = D;\nexports.Memo = E;\nexports.Portal = F;\nexports.Profiler = G;\nexports.StrictMode = H;\nexports.Suspense = I;\nexports.isAsyncMode = function () {\n  return !1;\n};\nexports.isConcurrentMode = function () {\n  return !1;\n};\nexports.isContextConsumer = function (a) {\n  return y(a) === h;\n};\nexports.isContextProvider = function (a) {\n  return y(a) === g;\n};\nexports.isElement = function (a) {\n  return \"object\" === typeof a && null !== a && a.$$typeof === b;\n};\nexports.isForwardRef = function (a) {\n  return y(a) === k;\n};\nexports.isFragment = function (a) {\n  return y(a) === d;\n};\nexports.isLazy = function (a) {\n  return y(a) === p;\n};\nexports.isMemo = function (a) {\n  return y(a) === n;\n};\nexports.isPortal = function (a) {\n  return y(a) === c;\n};\nexports.isProfiler = function (a) {\n  return y(a) === f;\n};\nexports.isStrictMode = function (a) {\n  return y(a) === e;\n};\nexports.isSuspense = function (a) {\n  return y(a) === l;\n};\nexports.isValidElementType = function (a) {\n  return \"string\" === typeof a || \"function\" === typeof a || a === d || a === f || a === v || a === e || a === l || a === m || a === w || \"object\" === typeof a && null !== a && (a.$$typeof === p || a.$$typeof === n || a.$$typeof === g || a.$$typeof === h || a.$$typeof === k || a.$$typeof === u || a.$$typeof === q || a[0] === r) ? !0 : !1;\n};\nexports.typeOf = y;", "map": {"version": 3, "names": ["b", "c", "d", "e", "f", "g", "h", "k", "l", "m", "n", "p", "q", "r", "u", "v", "w", "Symbol", "for", "x", "y", "a", "t", "$$typeof", "type", "z", "A", "B", "C", "D", "E", "F", "G", "H", "I", "exports", "ContextConsumer", "ContextProvider", "Element", "ForwardRef", "Fragment", "Lazy", "Memo", "Portal", "Profiler", "StrictMode", "Suspense", "isAsyncMode", "isConcurrentMode", "isContextConsumer", "isContextProvider", "isElement", "isForwardRef", "isFragment", "isLazy", "isMemo", "isPortal", "isProfiler", "isStrictMode", "isSuspense", "isValidElementType", "typeOf"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/node_modules/react-is/cjs/react-is.production.min.js"], "sourcesContent": ["/** @license React v17.0.2\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var b=60103,c=60106,d=60107,e=60108,f=60114,g=60109,h=60110,k=60112,l=60113,m=60120,n=60115,p=60116,q=60121,r=60122,u=60117,v=60129,w=60131;\nif(\"function\"===typeof Symbol&&Symbol.for){var x=Symbol.for;b=x(\"react.element\");c=x(\"react.portal\");d=x(\"react.fragment\");e=x(\"react.strict_mode\");f=x(\"react.profiler\");g=x(\"react.provider\");h=x(\"react.context\");k=x(\"react.forward_ref\");l=x(\"react.suspense\");m=x(\"react.suspense_list\");n=x(\"react.memo\");p=x(\"react.lazy\");q=x(\"react.block\");r=x(\"react.server.block\");u=x(\"react.fundamental\");v=x(\"react.debug_trace_mode\");w=x(\"react.legacy_hidden\")}\nfunction y(a){if(\"object\"===typeof a&&null!==a){var t=a.$$typeof;switch(t){case b:switch(a=a.type,a){case d:case f:case e:case l:case m:return a;default:switch(a=a&&a.$$typeof,a){case h:case k:case p:case n:case g:return a;default:return t}}case c:return t}}}var z=g,A=b,B=k,C=d,D=p,E=n,F=c,G=f,H=e,I=l;exports.ContextConsumer=h;exports.ContextProvider=z;exports.Element=A;exports.ForwardRef=B;exports.Fragment=C;exports.Lazy=D;exports.Memo=E;exports.Portal=F;exports.Profiler=G;exports.StrictMode=H;\nexports.Suspense=I;exports.isAsyncMode=function(){return!1};exports.isConcurrentMode=function(){return!1};exports.isContextConsumer=function(a){return y(a)===h};exports.isContextProvider=function(a){return y(a)===g};exports.isElement=function(a){return\"object\"===typeof a&&null!==a&&a.$$typeof===b};exports.isForwardRef=function(a){return y(a)===k};exports.isFragment=function(a){return y(a)===d};exports.isLazy=function(a){return y(a)===p};exports.isMemo=function(a){return y(a)===n};\nexports.isPortal=function(a){return y(a)===c};exports.isProfiler=function(a){return y(a)===f};exports.isStrictMode=function(a){return y(a)===e};exports.isSuspense=function(a){return y(a)===l};exports.isValidElementType=function(a){return\"string\"===typeof a||\"function\"===typeof a||a===d||a===f||a===v||a===e||a===l||a===m||a===w||\"object\"===typeof a&&null!==a&&(a.$$typeof===p||a.$$typeof===n||a.$$typeof===g||a.$$typeof===h||a.$$typeof===k||a.$$typeof===u||a.$$typeof===q||a[0]===r)?!0:!1};\nexports.typeOf=y;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAAC,IAAIA,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,KAAK;AACxJ,IAAG,UAAU,KAAG,OAAOC,MAAM,IAAEA,MAAM,CAACC,GAAG,EAAC;EAAC,IAAIC,CAAC,GAACF,MAAM,CAACC,GAAG;EAAClB,CAAC,GAACmB,CAAC,CAAC,eAAe,CAAC;EAAClB,CAAC,GAACkB,CAAC,CAAC,cAAc,CAAC;EAACjB,CAAC,GAACiB,CAAC,CAAC,gBAAgB,CAAC;EAAChB,CAAC,GAACgB,CAAC,CAAC,mBAAmB,CAAC;EAACf,CAAC,GAACe,CAAC,CAAC,gBAAgB,CAAC;EAACd,CAAC,GAACc,CAAC,CAAC,gBAAgB,CAAC;EAACb,CAAC,GAACa,CAAC,CAAC,eAAe,CAAC;EAACZ,CAAC,GAACY,CAAC,CAAC,mBAAmB,CAAC;EAACX,CAAC,GAACW,CAAC,CAAC,gBAAgB,CAAC;EAACV,CAAC,GAACU,CAAC,CAAC,qBAAqB,CAAC;EAACT,CAAC,GAACS,CAAC,CAAC,YAAY,CAAC;EAACR,CAAC,GAACQ,CAAC,CAAC,YAAY,CAAC;EAACP,CAAC,GAACO,CAAC,CAAC,aAAa,CAAC;EAACN,CAAC,GAACM,CAAC,CAAC,oBAAoB,CAAC;EAACL,CAAC,GAACK,CAAC,CAAC,mBAAmB,CAAC;EAACJ,CAAC,GAACI,CAAC,CAAC,wBAAwB,CAAC;EAACH,CAAC,GAACG,CAAC,CAAC,qBAAqB,CAAC;AAAA;AACjc,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAG,QAAQ,KAAG,OAAOA,CAAC,IAAE,IAAI,KAAGA,CAAC,EAAC;IAAC,IAAIC,CAAC,GAACD,CAAC,CAACE,QAAQ;IAAC,QAAOD,CAAC;MAAE,KAAKtB,CAAC;QAAC,QAAOqB,CAAC,GAACA,CAAC,CAACG,IAAI,EAACH,CAAC;UAAE,KAAKnB,CAAC;UAAC,KAAKE,CAAC;UAAC,KAAKD,CAAC;UAAC,KAAKK,CAAC;UAAC,KAAKC,CAAC;YAAC,OAAOY,CAAC;UAAC;YAAQ,QAAOA,CAAC,GAACA,CAAC,IAAEA,CAAC,CAACE,QAAQ,EAACF,CAAC;cAAE,KAAKf,CAAC;cAAC,KAAKC,CAAC;cAAC,KAAKI,CAAC;cAAC,KAAKD,CAAC;cAAC,KAAKL,CAAC;gBAAC,OAAOgB,CAAC;cAAC;gBAAQ,OAAOC,CAAC;YAAA;QAAC;MAAC,KAAKrB,CAAC;QAAC,OAAOqB,CAAC;IAAA;EAAC;AAAC;AAAC,IAAIG,CAAC,GAACpB,CAAC;EAACqB,CAAC,GAAC1B,CAAC;EAAC2B,CAAC,GAACpB,CAAC;EAACqB,CAAC,GAAC1B,CAAC;EAAC2B,CAAC,GAAClB,CAAC;EAACmB,CAAC,GAACpB,CAAC;EAACqB,CAAC,GAAC9B,CAAC;EAAC+B,CAAC,GAAC5B,CAAC;EAAC6B,CAAC,GAAC9B,CAAC;EAAC+B,CAAC,GAAC1B,CAAC;AAAC2B,OAAO,CAACC,eAAe,GAAC9B,CAAC;AAAC6B,OAAO,CAACE,eAAe,GAACZ,CAAC;AAACU,OAAO,CAACG,OAAO,GAACZ,CAAC;AAACS,OAAO,CAACI,UAAU,GAACZ,CAAC;AAACQ,OAAO,CAACK,QAAQ,GAACZ,CAAC;AAACO,OAAO,CAACM,IAAI,GAACZ,CAAC;AAACM,OAAO,CAACO,IAAI,GAACZ,CAAC;AAACK,OAAO,CAACQ,MAAM,GAACZ,CAAC;AAACI,OAAO,CAACS,QAAQ,GAACZ,CAAC;AAACG,OAAO,CAACU,UAAU,GAACZ,CAAC;AACnfE,OAAO,CAACW,QAAQ,GAACZ,CAAC;AAACC,OAAO,CAACY,WAAW,GAAC,YAAU;EAAC,OAAM,CAAC,CAAC;AAAA,CAAC;AAACZ,OAAO,CAACa,gBAAgB,GAAC,YAAU;EAAC,OAAM,CAAC,CAAC;AAAA,CAAC;AAACb,OAAO,CAACc,iBAAiB,GAAC,UAAS5B,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGf,CAAC;AAAA,CAAC;AAAC6B,OAAO,CAACe,iBAAiB,GAAC,UAAS7B,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGhB,CAAC;AAAA,CAAC;AAAC8B,OAAO,CAACgB,SAAS,GAAC,UAAS9B,CAAC,EAAC;EAAC,OAAM,QAAQ,KAAG,OAAOA,CAAC,IAAE,IAAI,KAAGA,CAAC,IAAEA,CAAC,CAACE,QAAQ,KAAGvB,CAAC;AAAA,CAAC;AAACmC,OAAO,CAACiB,YAAY,GAAC,UAAS/B,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGd,CAAC;AAAA,CAAC;AAAC4B,OAAO,CAACkB,UAAU,GAAC,UAAShC,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGnB,CAAC;AAAA,CAAC;AAACiC,OAAO,CAACmB,MAAM,GAAC,UAASjC,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGV,CAAC;AAAA,CAAC;AAACwB,OAAO,CAACoB,MAAM,GAAC,UAASlC,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGX,CAAC;AAAA,CAAC;AACpeyB,OAAO,CAACqB,QAAQ,GAAC,UAASnC,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGpB,CAAC;AAAA,CAAC;AAACkC,OAAO,CAACsB,UAAU,GAAC,UAASpC,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGjB,CAAC;AAAA,CAAC;AAAC+B,OAAO,CAACuB,YAAY,GAAC,UAASrC,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGlB,CAAC;AAAA,CAAC;AAACgC,OAAO,CAACwB,UAAU,GAAC,UAAStC,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,KAAGb,CAAC;AAAA,CAAC;AAAC2B,OAAO,CAACyB,kBAAkB,GAAC,UAASvC,CAAC,EAAC;EAAC,OAAM,QAAQ,KAAG,OAAOA,CAAC,IAAE,UAAU,KAAG,OAAOA,CAAC,IAAEA,CAAC,KAAGnB,CAAC,IAAEmB,CAAC,KAAGjB,CAAC,IAAEiB,CAAC,KAAGN,CAAC,IAAEM,CAAC,KAAGlB,CAAC,IAAEkB,CAAC,KAAGb,CAAC,IAAEa,CAAC,KAAGZ,CAAC,IAAEY,CAAC,KAAGL,CAAC,IAAE,QAAQ,KAAG,OAAOK,CAAC,IAAE,IAAI,KAAGA,CAAC,KAAGA,CAAC,CAACE,QAAQ,KAAGZ,CAAC,IAAEU,CAAC,CAACE,QAAQ,KAAGb,CAAC,IAAEW,CAAC,CAACE,QAAQ,KAAGlB,CAAC,IAAEgB,CAAC,CAACE,QAAQ,KAAGjB,CAAC,IAAEe,CAAC,CAACE,QAAQ,KAAGhB,CAAC,IAAEc,CAAC,CAACE,QAAQ,KAAGT,CAAC,IAAEO,CAAC,CAACE,QAAQ,KAAGX,CAAC,IAAES,CAAC,CAAC,CAAC,CAAC,KAAGR,CAAC,CAAC,GAAC,CAAC,CAAC,GAAC,CAAC,CAAC;AAAA,CAAC;AAC1esB,OAAO,CAAC0B,MAAM,GAACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}