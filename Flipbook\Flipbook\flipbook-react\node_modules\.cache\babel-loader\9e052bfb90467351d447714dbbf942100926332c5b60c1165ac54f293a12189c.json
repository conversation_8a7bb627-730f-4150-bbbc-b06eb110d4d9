{"ast": null, "code": "import _objectSpread from\"D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";// API Service for HTTP requests matching the .NET application endpoints\nclass ApiService{constructor(){this.baseUrl=void 0;// Use the Franklin Report Flipbook API URL\nthis.baseUrl=process.env.REACT_APP_API_BASE_URL||'https://flipbook.franklinreport.com';}async request(url){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};try{const response=await fetch(\"\".concat(this.baseUrl).concat(url),_objectSpread({headers:_objectSpread({'Content-Type':'application/json'},options.headers),credentials:'include'},options));if(!response.ok){throw new Error(\"HTTP error! status: \".concat(response.status));}const data=await response.json();return{data,success:true};}catch(error){console.error('API request failed:',error);return{data:null,success:false,error:error instanceof Error?error.message:'Unknown error occurred'};}}// Authentication methods - matching .NET AccountController\nasync getCurrentUser(){return this.request('/Account/GetCurrentUser');}async signIn(email,password){let rememberMe=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;// Matches .NET FBLogin method signature\nreturn this.request('/Account/FBLogin',{method:'POST',body:JSON.stringify({UserName:email,Password:password,chkRememberMe:rememberMe?\"1\":\"0\"})});}async signOut(){return this.request('/Account/LogOff',{method:'POST'});}// Flipbook/Portfolio methods - matching .NET controller actions\nasync getMyFlipbooks(){// Matches GetPortfoliosDetails_Pagemanager stored procedure call with -1 for current user\nreturn this.request('/Flipbook/getPortfolioDetails/-1');}async getInspirationFlipbooks(){// Matches FillInspireFBDetails method that calls GetPortfoliosDetails_Pagemanager with InspireFB_UserID\nreturn this.request('/Flipbook/GetInspirationFlipbooks');}async getPortfolioDetails(portfolioId){// Matches getPortfolioDetails method signature exactly\nreturn this.request(\"/Flipbook/getPortfolioDetails/\".concat(portfolioId));}async getPortfolioPages(portfolioId){let isAuto=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;return this.request(\"/Flipbook/GetPortPagesByPortfolioId?PortfolioID=\".concat(portfolioId,\"&IsAuto=\").concat(isAuto));}async createNewFlipbook(request){// Matches AddPortfolioTitle method signature exactly\nreturn this.request('/Flipbook/AddPortfolioTitle',{method:'POST',body:JSON.stringify({PortTitle:request.title,PortfolioID:request.templateId||0})});}async copyInspirationFlipbook(request){// Matches DuplicateWholeFlipbook method signature exactly\nreturn this.request('/Flipbook/DuplicateWholeFlipbook',{method:'POST',body:JSON.stringify({PortTitle:request.title,PortfolioID:request.portfolioId,IsInspirationFB:true})});}async updateFlipbook(request){return this.request('/Flipbook/UpdateFlipbookTitle',{method:'POST',body:JSON.stringify(request)});}async deleteFlipbook(portfolioId){return this.request('/Flipbook/DeleteFlipbook',{method:'POST',body:JSON.stringify({PortfolioID:portfolioId})});}async setFlipbookPreferred(portfolioId,isPreferred){return this.request('/Flipbook/SetflipbookIspreferred',{method:'POST',body:JSON.stringify({PortfolioID:portfolioId,SetIsPreferred:isPreferred})});}async getFlipbookPreviewData(portfolioId,pageCount,isInspiration){const endpoint=isInspiration?\"/Publish/Inspiration/\".concat(portfolioId,\"/\").concat(pageCount):\"/Publish/Preview/\".concat(portfolioId,\"/\").concat(pageCount);return this.request(endpoint);}// Helper method to get thumbnail URL\ngetThumbnailUrl(portfolioId,userFolderId){let isInspiration=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;const baseFolder=isInspiration?'999999':userFolderId.toString();return\"\".concat(this.baseUrl,\"/Users/\").concat(baseFolder,\"/Flipbooks/\").concat(portfolioId,\"/ThumbnailImages/Tn1.jpg\");}// Helper method to get flipbook HTML path\ngetFlipbookHtmlPath(portfolioId,userFolderId){let isInspiration=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;const baseFolder=isInspiration?'999999':userFolderId.toString();return\"/Users/\".concat(baseFolder,\"/Flipbooks/\").concat(portfolioId,\"/HTML/\");}// Initial load APIs (no authentication required)\nasync getUIMessages(){const timestamp=Date.now();return this.request(\"/Admin/getMsgUI?_=\".concat(timestamp));}async getPortfolioDetailsInitial(){return this.request('/Flipbook/getPortfolioDetails/');}async getCommonValues(){const timestamp=Date.now();return this.request(\"/Admin/getCommonValues?_=\".concat(timestamp));}}// Create and export a singleton instance\nconst useMockApi=process.env.REACT_APP_USE_MOCK_API==='true';// API Service setup logging (removed)\nlet apiServiceInstance;if(useMockApi){// Use mock API in development\nimport('./mockApi.service').then(module=>{apiServiceInstance=module.mockApiService;});// For immediate use, import synchronously (this is a workaround)\nconst{mockApiService}=require('./mockApi.service');apiServiceInstance=mockApiService;}else{apiServiceInstance=new ApiService();}export const apiService=apiServiceInstance;export default apiServiceInstance;", "map": {"version": 3, "names": ["ApiService", "constructor", "baseUrl", "process", "env", "REACT_APP_API_BASE_URL", "request", "url", "options", "arguments", "length", "undefined", "response", "fetch", "concat", "_objectSpread", "headers", "credentials", "ok", "Error", "status", "data", "json", "success", "error", "console", "message", "getCurrentUser", "signIn", "email", "password", "rememberMe", "method", "body", "JSON", "stringify", "UserName", "Password", "chkRememberMe", "signOut", "getMyFlipbooks", "getInspirationFlipbooks", "getPortfolioDetails", "portfolioId", "getPortfolioPages", "isAuto", "createNewFlipbook", "PortTitle", "title", "PortfolioID", "templateId", "copyInspirationFlipbook", "IsInspirationFB", "updateFlipbook", "deleteFlipbook", "setFlipbookPreferred", "isPreferred", "SetIsPreferred", "getFlipbookPreviewData", "pageCount", "isInspiration", "endpoint", "getThumbnailUrl", "userFolderId", "baseFolder", "toString", "getFlipbookHtmlPath", "getUIMessages", "timestamp", "Date", "now", "getPortfolioDetailsInitial", "getCommonValues", "useMockApi", "REACT_APP_USE_MOCK_API", "apiServiceInstance", "then", "module", "mockApiService", "require", "apiService"], "sources": ["D:/TradeWorks/Flipbook/Flipbook/Flipbook/flipbook-react/src/services/api.service.ts"], "sourcesContent": ["// API Service for HTTP requests matching the .NET application endpoints\n\nimport {\n  Inspiration,\n  Portfolio,\n  PortfolioDetails,\n  PortfolioPage,\n  ApiResponse,\n  CreateFlipbookRequest,\n  CopyInspirationRequest,\n  UpdateFlipbookRequest,\n  UserSession,\n  FlipbookPreviewData,\n  UIMessage,\n  CommonValue\n} from '../types/flipbook.types';\n\nclass ApiService {\n  private baseUrl: string;\n\n  constructor() {\n    // Use the Franklin Report Flipbook API URL\n    this.baseUrl = process.env.REACT_APP_API_BASE_URL || 'https://flipbook.franklinreport.com';\n  }\n\n  private async request<T>(\n    url: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const response = await fetch(`${this.baseUrl}${url}`, {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        credentials: 'include', // Include cookies for authentication\n        ...options,\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return {\n        data,\n        success: true,\n      };\n    } catch (error) {\n      console.error('API request failed:', error);\n      return {\n        data: null as any,\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error occurred',\n      };\n    }\n  }\n\n  // Authentication methods - matching .NET AccountController\n  async getCurrentUser(): Promise<ApiResponse<UserSession>> {\n    return this.request<UserSession>('/Account/GetCurrentUser');\n  }\n\n  async signIn(email: string, password: string, rememberMe: boolean = false): Promise<ApiResponse<UserSession>> {\n    // Matches .NET FBLogin method signature\n    return this.request<UserSession>('/Account/FBLogin', {\n      method: 'POST',\n      body: JSON.stringify({\n        UserName: email,\n        Password: password,\n        chkRememberMe: rememberMe ? \"1\" : \"0\"\n      }),\n    });\n  }\n\n  async signOut(): Promise<ApiResponse<void>> {\n    return this.request<void>('/Account/LogOff', {\n      method: 'POST',\n    });\n  }\n\n  // Flipbook/Portfolio methods - matching .NET controller actions\n  async getMyFlipbooks(): Promise<ApiResponse<Portfolio[]>> {\n    // Matches GetPortfoliosDetails_Pagemanager stored procedure call with -1 for current user\n    return this.request<Portfolio[]>('/Flipbook/getPortfolioDetails/-1');\n  }\n\n  async getInspirationFlipbooks(): Promise<ApiResponse<Inspiration[]>> {\n    // Matches FillInspireFBDetails method that calls GetPortfoliosDetails_Pagemanager with InspireFB_UserID\n    return this.request<Inspiration[]>('/Flipbook/GetInspirationFlipbooks');\n  }\n\n  async getPortfolioDetails(portfolioId: number): Promise<ApiResponse<PortfolioDetails>> {\n    // Matches getPortfolioDetails method signature exactly\n    return this.request<PortfolioDetails>(`/Flipbook/getPortfolioDetails/${portfolioId}`);\n  }\n\n  async getPortfolioPages(portfolioId: number, isAuto: number = 0): Promise<ApiResponse<PortfolioPage[]>> {\n    return this.request<PortfolioPage[]>(`/Flipbook/GetPortPagesByPortfolioId?PortfolioID=${portfolioId}&IsAuto=${isAuto}`);\n  }\n\n  async createNewFlipbook(request: CreateFlipbookRequest): Promise<ApiResponse<number>> {\n    // Matches AddPortfolioTitle method signature exactly\n    return this.request<number>('/Flipbook/AddPortfolioTitle', {\n      method: 'POST',\n      body: JSON.stringify({\n        PortTitle: request.title,\n        PortfolioID: request.templateId || 0\n      }),\n    });\n  }\n\n  async copyInspirationFlipbook(request: CopyInspirationRequest): Promise<ApiResponse<number>> {\n    // Matches DuplicateWholeFlipbook method signature exactly\n    return this.request<number>('/Flipbook/DuplicateWholeFlipbook', {\n      method: 'POST',\n      body: JSON.stringify({\n        PortTitle: request.title,\n        PortfolioID: request.portfolioId,\n        IsInspirationFB: true\n      }),\n    });\n  }\n\n  async updateFlipbook(request: UpdateFlipbookRequest): Promise<ApiResponse<boolean>> {\n    return this.request<boolean>('/Flipbook/UpdateFlipbookTitle', {\n      method: 'POST',\n      body: JSON.stringify(request),\n    });\n  }\n\n  async deleteFlipbook(portfolioId: number): Promise<ApiResponse<boolean>> {\n    return this.request<boolean>('/Flipbook/DeleteFlipbook', {\n      method: 'POST',\n      body: JSON.stringify({ PortfolioID: portfolioId }),\n    });\n  }\n\n  async setFlipbookPreferred(portfolioId: number, isPreferred: boolean): Promise<ApiResponse<boolean>> {\n    return this.request<boolean>('/Flipbook/SetflipbookIspreferred', {\n      method: 'POST',\n      body: JSON.stringify({ \n        PortfolioID: portfolioId, \n        SetIsPreferred: isPreferred \n      }),\n    });\n  }\n\n  async getFlipbookPreviewData(portfolioId: number, pageCount: number, isInspiration: boolean): Promise<ApiResponse<FlipbookPreviewData>> {\n    const endpoint = isInspiration \n      ? `/Publish/Inspiration/${portfolioId}/${pageCount}`\n      : `/Publish/Preview/${portfolioId}/${pageCount}`;\n    \n    return this.request<FlipbookPreviewData>(endpoint);\n  }\n\n  // Helper method to get thumbnail URL\n  getThumbnailUrl(portfolioId: number, userFolderId: number, isInspiration: boolean = false): string {\n    const baseFolder = isInspiration ? '999999' : userFolderId.toString();\n    return `${this.baseUrl}/Users/<USER>/Flipbooks/${portfolioId}/ThumbnailImages/Tn1.jpg`;\n  }\n\n  // Helper method to get flipbook HTML path\n  getFlipbookHtmlPath(portfolioId: number, userFolderId: number, isInspiration: boolean = false): string {\n    const baseFolder = isInspiration ? '999999' : userFolderId.toString();\n    return `/Users/<USER>/Flipbooks/${portfolioId}/HTML/`;\n  }\n\n  // Initial load APIs (no authentication required)\n  async getUIMessages(): Promise<ApiResponse<UIMessage[]>> {\n    const timestamp = Date.now();\n    return this.request<UIMessage[]>(`/Admin/getMsgUI?_=${timestamp}`);\n  }\n\n  async getPortfolioDetailsInitial(): Promise<ApiResponse<any[]>> {\n    return this.request<any[]>('/Flipbook/getPortfolioDetails/');\n  }\n\n  async getCommonValues(): Promise<ApiResponse<CommonValue[]>> {\n    const timestamp = Date.now();\n    return this.request<CommonValue[]>(`/Admin/getCommonValues?_=${timestamp}`);\n  }\n}\n\n// Create and export a singleton instance\nconst useMockApi = process.env.REACT_APP_USE_MOCK_API === 'true';\n// API Service setup logging (removed)\n\nlet apiServiceInstance: ApiService | any;\n\nif (useMockApi) {\n  // Use mock API in development\n  import('./mockApi.service').then(module => {\n    apiServiceInstance = module.mockApiService;\n  });\n  // For immediate use, import synchronously (this is a workaround)\n  const { mockApiService } = require('./mockApi.service');\n  apiServiceInstance = mockApiService;\n} else {\n  apiServiceInstance = new ApiService();\n}\n\nexport const apiService = apiServiceInstance;\nexport default apiServiceInstance;\n"], "mappings": "4IAAA;AAiBA,KAAM,CAAAA,UAAW,CAGfC,WAAWA,CAAA,CAAG,MAFNC,OAAO,QAGb;AACA,IAAI,CAACA,OAAO,CAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAI,qCAAqC,CAC5F,CAEA,KAAc,CAAAC,OAAOA,CACnBC,GAAW,CAEc,IADzB,CAAAC,OAAoB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAEzB,GAAI,CACF,KAAM,CAAAG,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAI,IAAI,CAACZ,OAAO,EAAAY,MAAA,CAAGP,GAAG,EAAAQ,aAAA,EAChDC,OAAO,CAAAD,aAAA,EACL,cAAc,CAAE,kBAAkB,EAC/BP,OAAO,CAACQ,OAAO,CACnB,CACDC,WAAW,CAAE,SAAS,EACnBT,OAAO,CACX,CAAC,CAEF,GAAI,CAACI,QAAQ,CAACM,EAAE,CAAE,CAChB,KAAM,IAAI,CAAAC,KAAK,wBAAAL,MAAA,CAAwBF,QAAQ,CAACQ,MAAM,CAAE,CAAC,CAC3D,CAEA,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAT,QAAQ,CAACU,IAAI,CAAC,CAAC,CAClC,MAAO,CACLD,IAAI,CACJE,OAAO,CAAE,IACX,CAAC,CACH,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3C,MAAO,CACLH,IAAI,CAAE,IAAW,CACjBE,OAAO,CAAE,KAAK,CACdC,KAAK,CAAEA,KAAK,WAAY,CAAAL,KAAK,CAAGK,KAAK,CAACE,OAAO,CAAG,wBAClD,CAAC,CACH,CACF,CAEA;AACA,KAAM,CAAAC,cAAcA,CAAA,CAAsC,CACxD,MAAO,KAAI,CAACrB,OAAO,CAAc,yBAAyB,CAAC,CAC7D,CAEA,KAAM,CAAAsB,MAAMA,CAACC,KAAa,CAAEC,QAAgB,CAAkE,IAAhE,CAAAC,UAAmB,CAAAtB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CACvE;AACA,MAAO,KAAI,CAACH,OAAO,CAAc,kBAAkB,CAAE,CACnD0B,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBC,QAAQ,CAAEP,KAAK,CACfQ,QAAQ,CAAEP,QAAQ,CAClBQ,aAAa,CAAEP,UAAU,CAAG,GAAG,CAAG,GACpC,CAAC,CACH,CAAC,CAAC,CACJ,CAEA,KAAM,CAAAQ,OAAOA,CAAA,CAA+B,CAC1C,MAAO,KAAI,CAACjC,OAAO,CAAO,iBAAiB,CAAE,CAC3C0B,MAAM,CAAE,MACV,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAQ,cAAcA,CAAA,CAAsC,CACxD;AACA,MAAO,KAAI,CAAClC,OAAO,CAAc,kCAAkC,CAAC,CACtE,CAEA,KAAM,CAAAmC,uBAAuBA,CAAA,CAAwC,CACnE;AACA,MAAO,KAAI,CAACnC,OAAO,CAAgB,mCAAmC,CAAC,CACzE,CAEA,KAAM,CAAAoC,mBAAmBA,CAACC,WAAmB,CAA0C,CACrF;AACA,MAAO,KAAI,CAACrC,OAAO,kCAAAQ,MAAA,CAAoD6B,WAAW,CAAE,CAAC,CACvF,CAEA,KAAM,CAAAC,iBAAiBA,CAACD,WAAmB,CAA6D,IAA3D,CAAAE,MAAc,CAAApC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAC7D,MAAO,KAAI,CAACH,OAAO,oDAAAQ,MAAA,CAAqE6B,WAAW,aAAA7B,MAAA,CAAW+B,MAAM,CAAE,CAAC,CACzH,CAEA,KAAM,CAAAC,iBAAiBA,CAACxC,OAA8B,CAAgC,CACpF;AACA,MAAO,KAAI,CAACA,OAAO,CAAS,6BAA6B,CAAE,CACzD0B,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBY,SAAS,CAAEzC,OAAO,CAAC0C,KAAK,CACxBC,WAAW,CAAE3C,OAAO,CAAC4C,UAAU,EAAI,CACrC,CAAC,CACH,CAAC,CAAC,CACJ,CAEA,KAAM,CAAAC,uBAAuBA,CAAC7C,OAA+B,CAAgC,CAC3F;AACA,MAAO,KAAI,CAACA,OAAO,CAAS,kCAAkC,CAAE,CAC9D0B,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBY,SAAS,CAAEzC,OAAO,CAAC0C,KAAK,CACxBC,WAAW,CAAE3C,OAAO,CAACqC,WAAW,CAChCS,eAAe,CAAE,IACnB,CAAC,CACH,CAAC,CAAC,CACJ,CAEA,KAAM,CAAAC,cAAcA,CAAC/C,OAA8B,CAAiC,CAClF,MAAO,KAAI,CAACA,OAAO,CAAU,+BAA+B,CAAE,CAC5D0B,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC7B,OAAO,CAC9B,CAAC,CAAC,CACJ,CAEA,KAAM,CAAAgD,cAAcA,CAACX,WAAmB,CAAiC,CACvE,MAAO,KAAI,CAACrC,OAAO,CAAU,0BAA0B,CAAE,CACvD0B,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CAAEc,WAAW,CAAEN,WAAY,CAAC,CACnD,CAAC,CAAC,CACJ,CAEA,KAAM,CAAAY,oBAAoBA,CAACZ,WAAmB,CAAEa,WAAoB,CAAiC,CACnG,MAAO,KAAI,CAAClD,OAAO,CAAU,kCAAkC,CAAE,CAC/D0B,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBc,WAAW,CAAEN,WAAW,CACxBc,cAAc,CAAED,WAClB,CAAC,CACH,CAAC,CAAC,CACJ,CAEA,KAAM,CAAAE,sBAAsBA,CAACf,WAAmB,CAAEgB,SAAiB,CAAEC,aAAsB,CAA6C,CACtI,KAAM,CAAAC,QAAQ,CAAGD,aAAa,yBAAA9C,MAAA,CACF6B,WAAW,MAAA7B,MAAA,CAAI6C,SAAS,sBAAA7C,MAAA,CAC5B6B,WAAW,MAAA7B,MAAA,CAAI6C,SAAS,CAAE,CAElD,MAAO,KAAI,CAACrD,OAAO,CAAsBuD,QAAQ,CAAC,CACpD,CAEA;AACAC,eAAeA,CAACnB,WAAmB,CAAEoB,YAAoB,CAA0C,IAAxC,CAAAH,aAAsB,CAAAnD,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CACvF,KAAM,CAAAuD,UAAU,CAAGJ,aAAa,CAAG,QAAQ,CAAGG,YAAY,CAACE,QAAQ,CAAC,CAAC,CACrE,SAAAnD,MAAA,CAAU,IAAI,CAACZ,OAAO,YAAAY,MAAA,CAAUkD,UAAU,gBAAAlD,MAAA,CAAc6B,WAAW,6BACrE,CAEA;AACAuB,mBAAmBA,CAACvB,WAAmB,CAAEoB,YAAoB,CAA0C,IAAxC,CAAAH,aAAsB,CAAAnD,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC3F,KAAM,CAAAuD,UAAU,CAAGJ,aAAa,CAAG,QAAQ,CAAGG,YAAY,CAACE,QAAQ,CAAC,CAAC,CACrE,gBAAAnD,MAAA,CAAiBkD,UAAU,gBAAAlD,MAAA,CAAc6B,WAAW,WACtD,CAEA;AACA,KAAM,CAAAwB,aAAaA,CAAA,CAAsC,CACvD,KAAM,CAAAC,SAAS,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAC5B,MAAO,KAAI,CAAChE,OAAO,sBAAAQ,MAAA,CAAmCsD,SAAS,CAAE,CAAC,CACpE,CAEA,KAAM,CAAAG,0BAA0BA,CAAA,CAAgC,CAC9D,MAAO,KAAI,CAACjE,OAAO,CAAQ,gCAAgC,CAAC,CAC9D,CAEA,KAAM,CAAAkE,eAAeA,CAAA,CAAwC,CAC3D,KAAM,CAAAJ,SAAS,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAC5B,MAAO,KAAI,CAAChE,OAAO,6BAAAQ,MAAA,CAA4CsD,SAAS,CAAE,CAAC,CAC7E,CACF,CAEA;AACA,KAAM,CAAAK,UAAU,CAAGtE,OAAO,CAACC,GAAG,CAACsE,sBAAsB,GAAK,MAAM,CAChE;AAEA,GAAI,CAAAC,kBAAoC,CAExC,GAAIF,UAAU,CAAE,CACd;AACA,MAAM,CAAC,mBAAmB,CAAC,CAACG,IAAI,CAACC,MAAM,EAAI,CACzCF,kBAAkB,CAAGE,MAAM,CAACC,cAAc,CAC5C,CAAC,CAAC,CACF;AACA,KAAM,CAAEA,cAAe,CAAC,CAAGC,OAAO,CAAC,mBAAmB,CAAC,CACvDJ,kBAAkB,CAAGG,cAAc,CACrC,CAAC,IAAM,CACLH,kBAAkB,CAAG,GAAI,CAAA3E,UAAU,CAAC,CAAC,CACvC,CAEA,MAAO,MAAM,CAAAgF,UAAU,CAAGL,kBAAkB,CAC5C,cAAe,CAAAA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}