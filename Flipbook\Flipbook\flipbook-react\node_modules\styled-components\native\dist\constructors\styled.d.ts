/// <reference types="react" />
import { WebTarget } from '../types';
import { Styled } from './constructWithOptions';
declare const styled: (<Target extends WebTarget>(tag: Target) => Styled<"web", Target, Target extends import("../types").KnownTarget ? import("react").ComponentPropsWithRef<Target> : object, object, object>) & {
    a: Styled<"web", "a", import("react").DetailedHTMLProps<import("react").AnchorHTMLAttributes<HTMLAnchorElement>, HTMLAnchorElement>, object, object>;
    abbr: Styled<"web", "abbr", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    address: Styled<"web", "address", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    area: Styled<"web", "area", import("react").DetailedHTMLProps<import("react").AreaHTMLAttributes<HTMLAreaElement>, HTMLAreaElement>, object, object>;
    article: Styled<"web", "article", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    aside: Styled<"web", "aside", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    audio: Styled<"web", "audio", import("react").DetailedHTMLProps<import("react").AudioHTMLAttributes<HTMLAudioElement>, HTMLAudioElement>, object, object>;
    b: Styled<"web", "b", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    base: Styled<"web", "base", import("react").DetailedHTMLProps<import("react").BaseHTMLAttributes<HTMLBaseElement>, HTMLBaseElement>, object, object>;
    bdi: Styled<"web", "bdi", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    bdo: Styled<"web", "bdo", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    big: Styled<"web", "big", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    blockquote: Styled<"web", "blockquote", import("react").DetailedHTMLProps<import("react").BlockquoteHTMLAttributes<HTMLQuoteElement>, HTMLQuoteElement>, object, object>;
    body: Styled<"web", "body", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLBodyElement>, HTMLBodyElement>, object, object>;
    br: Styled<"web", "br", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLBRElement>, HTMLBRElement>, object, object>;
    button: Styled<"web", "button", import("react").DetailedHTMLProps<import("react").ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>, object, object>;
    canvas: Styled<"web", "canvas", import("react").DetailedHTMLProps<import("react").CanvasHTMLAttributes<HTMLCanvasElement>, HTMLCanvasElement>, object, object>;
    caption: Styled<"web", "caption", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    cite: Styled<"web", "cite", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    code: Styled<"web", "code", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    col: Styled<"web", "col", import("react").DetailedHTMLProps<import("react").ColHTMLAttributes<HTMLTableColElement>, HTMLTableColElement>, object, object>;
    colgroup: Styled<"web", "colgroup", import("react").DetailedHTMLProps<import("react").ColgroupHTMLAttributes<HTMLTableColElement>, HTMLTableColElement>, object, object>;
    data: Styled<"web", "data", import("react").DetailedHTMLProps<import("react").DataHTMLAttributes<HTMLDataElement>, HTMLDataElement>, object, object>;
    datalist: Styled<"web", "datalist", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDataListElement>, HTMLDataListElement>, object, object>;
    dd: Styled<"web", "dd", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    del: Styled<"web", "del", import("react").DetailedHTMLProps<import("react").DelHTMLAttributes<HTMLModElement>, HTMLModElement>, object, object>;
    details: Styled<"web", "details", import("react").DetailedHTMLProps<import("react").DetailsHTMLAttributes<HTMLDetailsElement>, HTMLDetailsElement>, object, object>;
    dfn: Styled<"web", "dfn", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    dialog: Styled<"web", "dialog", import("react").DetailedHTMLProps<import("react").DialogHTMLAttributes<HTMLDialogElement>, HTMLDialogElement>, object, object>;
    div: Styled<"web", "div", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, object, object>;
    dl: Styled<"web", "dl", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDListElement>, HTMLDListElement>, object, object>;
    dt: Styled<"web", "dt", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    em: Styled<"web", "em", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    embed: Styled<"web", "embed", import("react").DetailedHTMLProps<import("react").EmbedHTMLAttributes<HTMLEmbedElement>, HTMLEmbedElement>, object, object>;
    fieldset: Styled<"web", "fieldset", import("react").DetailedHTMLProps<import("react").FieldsetHTMLAttributes<HTMLFieldSetElement>, HTMLFieldSetElement>, object, object>;
    figcaption: Styled<"web", "figcaption", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    figure: Styled<"web", "figure", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    footer: Styled<"web", "footer", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    form: Styled<"web", "form", import("react").DetailedHTMLProps<import("react").FormHTMLAttributes<HTMLFormElement>, HTMLFormElement>, object, object>;
    h1: Styled<"web", "h1", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, object, object>;
    h2: Styled<"web", "h2", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, object, object>;
    h3: Styled<"web", "h3", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, object, object>;
    h4: Styled<"web", "h4", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, object, object>;
    h5: Styled<"web", "h5", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, object, object>;
    h6: Styled<"web", "h6", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, object, object>;
    head: Styled<"web", "head", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadElement>, HTMLHeadElement>, object, object>;
    header: Styled<"web", "header", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    hgroup: Styled<"web", "hgroup", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    hr: Styled<"web", "hr", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHRElement>, HTMLHRElement>, object, object>;
    html: Styled<"web", "html", import("react").DetailedHTMLProps<import("react").HtmlHTMLAttributes<HTMLHtmlElement>, HTMLHtmlElement>, object, object>;
    i: Styled<"web", "i", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    iframe: Styled<"web", "iframe", import("react").DetailedHTMLProps<import("react").IframeHTMLAttributes<HTMLIFrameElement>, HTMLIFrameElement>, object, object>;
    img: Styled<"web", "img", import("react").DetailedHTMLProps<import("react").ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement>, object, object>;
    input: Styled<"web", "input", import("react").DetailedHTMLProps<import("react").InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>, object, object>;
    ins: Styled<"web", "ins", import("react").DetailedHTMLProps<import("react").InsHTMLAttributes<HTMLModElement>, HTMLModElement>, object, object>;
    kbd: Styled<"web", "kbd", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    keygen: Styled<"web", "keygen", import("react").DetailedHTMLProps<import("react").KeygenHTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    label: Styled<"web", "label", import("react").DetailedHTMLProps<import("react").LabelHTMLAttributes<HTMLLabelElement>, HTMLLabelElement>, object, object>;
    legend: Styled<"web", "legend", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLLegendElement>, HTMLLegendElement>, object, object>;
    li: Styled<"web", "li", import("react").DetailedHTMLProps<import("react").LiHTMLAttributes<HTMLLIElement>, HTMLLIElement>, object, object>;
    link: Styled<"web", "link", import("react").DetailedHTMLProps<import("react").LinkHTMLAttributes<HTMLLinkElement>, HTMLLinkElement>, object, object>;
    main: Styled<"web", "main", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    map: Styled<"web", "map", import("react").DetailedHTMLProps<import("react").MapHTMLAttributes<HTMLMapElement>, HTMLMapElement>, object, object>;
    mark: Styled<"web", "mark", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    menu: Styled<"web", "menu", import("react").DetailedHTMLProps<import("react").MenuHTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    menuitem: Styled<"web", "menuitem", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    meta: Styled<"web", "meta", import("react").DetailedHTMLProps<import("react").MetaHTMLAttributes<HTMLMetaElement>, HTMLMetaElement>, object, object>;
    meter: Styled<"web", "meter", import("react").DetailedHTMLProps<import("react").MeterHTMLAttributes<HTMLMeterElement>, HTMLMeterElement>, object, object>;
    nav: Styled<"web", "nav", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    noindex: Styled<"web", "noindex", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    noscript: Styled<"web", "noscript", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    object: Styled<"web", "object", import("react").DetailedHTMLProps<import("react").ObjectHTMLAttributes<HTMLObjectElement>, HTMLObjectElement>, object, object>;
    ol: Styled<"web", "ol", import("react").DetailedHTMLProps<import("react").OlHTMLAttributes<HTMLOListElement>, HTMLOListElement>, object, object>;
    optgroup: Styled<"web", "optgroup", import("react").DetailedHTMLProps<import("react").OptgroupHTMLAttributes<HTMLOptGroupElement>, HTMLOptGroupElement>, object, object>;
    option: Styled<"web", "option", import("react").DetailedHTMLProps<import("react").OptionHTMLAttributes<HTMLOptionElement>, HTMLOptionElement>, object, object>;
    output: Styled<"web", "output", import("react").DetailedHTMLProps<import("react").OutputHTMLAttributes<HTMLOutputElement>, HTMLOutputElement>, object, object>;
    p: Styled<"web", "p", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLParagraphElement>, HTMLParagraphElement>, object, object>;
    param: Styled<"web", "param", import("react").DetailedHTMLProps<import("react").ParamHTMLAttributes<HTMLParamElement>, HTMLParamElement>, object, object>;
    picture: Styled<"web", "picture", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    pre: Styled<"web", "pre", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLPreElement>, HTMLPreElement>, object, object>;
    progress: Styled<"web", "progress", import("react").DetailedHTMLProps<import("react").ProgressHTMLAttributes<HTMLProgressElement>, HTMLProgressElement>, object, object>;
    q: Styled<"web", "q", import("react").DetailedHTMLProps<import("react").QuoteHTMLAttributes<HTMLQuoteElement>, HTMLQuoteElement>, object, object>;
    rp: Styled<"web", "rp", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    rt: Styled<"web", "rt", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    ruby: Styled<"web", "ruby", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    s: Styled<"web", "s", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    samp: Styled<"web", "samp", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    slot: Styled<"web", "slot", import("react").DetailedHTMLProps<import("react").SlotHTMLAttributes<HTMLSlotElement>, HTMLSlotElement>, object, object>;
    script: Styled<"web", "script", import("react").DetailedHTMLProps<import("react").ScriptHTMLAttributes<HTMLScriptElement>, HTMLScriptElement>, object, object>;
    section: Styled<"web", "section", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    select: Styled<"web", "select", import("react").DetailedHTMLProps<import("react").SelectHTMLAttributes<HTMLSelectElement>, HTMLSelectElement>, object, object>;
    small: Styled<"web", "small", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    source: Styled<"web", "source", import("react").DetailedHTMLProps<import("react").SourceHTMLAttributes<HTMLSourceElement>, HTMLSourceElement>, object, object>;
    span: Styled<"web", "span", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, object, object>;
    strong: Styled<"web", "strong", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    style: Styled<"web", "style", import("react").DetailedHTMLProps<import("react").StyleHTMLAttributes<HTMLStyleElement>, HTMLStyleElement>, object, object>;
    sub: Styled<"web", "sub", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    summary: Styled<"web", "summary", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    sup: Styled<"web", "sup", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    table: Styled<"web", "table", import("react").DetailedHTMLProps<import("react").TableHTMLAttributes<HTMLTableElement>, HTMLTableElement>, object, object>;
    template: Styled<"web", "template", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTemplateElement>, HTMLTemplateElement>, object, object>;
    tbody: Styled<"web", "tbody", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTableSectionElement>, HTMLTableSectionElement>, object, object>;
    td: Styled<"web", "td", import("react").DetailedHTMLProps<import("react").TdHTMLAttributes<HTMLTableDataCellElement>, HTMLTableDataCellElement>, object, object>;
    textarea: Styled<"web", "textarea", import("react").DetailedHTMLProps<import("react").TextareaHTMLAttributes<HTMLTextAreaElement>, HTMLTextAreaElement>, object, object>;
    tfoot: Styled<"web", "tfoot", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTableSectionElement>, HTMLTableSectionElement>, object, object>;
    th: Styled<"web", "th", import("react").DetailedHTMLProps<import("react").ThHTMLAttributes<HTMLTableHeaderCellElement>, HTMLTableHeaderCellElement>, object, object>;
    thead: Styled<"web", "thead", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTableSectionElement>, HTMLTableSectionElement>, object, object>;
    time: Styled<"web", "time", import("react").DetailedHTMLProps<import("react").TimeHTMLAttributes<HTMLTimeElement>, HTMLTimeElement>, object, object>;
    title: Styled<"web", "title", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTitleElement>, HTMLTitleElement>, object, object>;
    tr: Styled<"web", "tr", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTableRowElement>, HTMLTableRowElement>, object, object>;
    track: Styled<"web", "track", import("react").DetailedHTMLProps<import("react").TrackHTMLAttributes<HTMLTrackElement>, HTMLTrackElement>, object, object>;
    u: Styled<"web", "u", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    ul: Styled<"web", "ul", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLUListElement>, HTMLUListElement>, object, object>;
    var: Styled<"web", "var", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    video: Styled<"web", "video", import("react").DetailedHTMLProps<import("react").VideoHTMLAttributes<HTMLVideoElement>, HTMLVideoElement>, object, object>;
    wbr: Styled<"web", "wbr", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, object, object>;
    webview: Styled<"web", "webview", import("react").DetailedHTMLProps<import("react").WebViewHTMLAttributes<HTMLWebViewElement>, HTMLWebViewElement>, object, object>;
    svg: Styled<"web", "svg", import("react").SVGProps<SVGSVGElement>, object, object>;
    animate: Styled<"web", "animate", import("react").SVGProps<SVGElement>, object, object>;
    animateMotion: Styled<"web", "animateMotion", import("react").SVGProps<SVGElement>, object, object>;
    animateTransform: Styled<"web", "animateTransform", import("react").SVGProps<SVGElement>, object, object>;
    circle: Styled<"web", "circle", import("react").SVGProps<SVGCircleElement>, object, object>;
    clipPath: Styled<"web", "clipPath", import("react").SVGProps<SVGClipPathElement>, object, object>;
    defs: Styled<"web", "defs", import("react").SVGProps<SVGDefsElement>, object, object>;
    desc: Styled<"web", "desc", import("react").SVGProps<SVGDescElement>, object, object>;
    ellipse: Styled<"web", "ellipse", import("react").SVGProps<SVGEllipseElement>, object, object>;
    feBlend: Styled<"web", "feBlend", import("react").SVGProps<SVGFEBlendElement>, object, object>;
    feColorMatrix: Styled<"web", "feColorMatrix", import("react").SVGProps<SVGFEColorMatrixElement>, object, object>;
    feComponentTransfer: Styled<"web", "feComponentTransfer", import("react").SVGProps<SVGFEComponentTransferElement>, object, object>;
    feComposite: Styled<"web", "feComposite", import("react").SVGProps<SVGFECompositeElement>, object, object>;
    feConvolveMatrix: Styled<"web", "feConvolveMatrix", import("react").SVGProps<SVGFEConvolveMatrixElement>, object, object>;
    feDiffuseLighting: Styled<"web", "feDiffuseLighting", import("react").SVGProps<SVGFEDiffuseLightingElement>, object, object>;
    feDisplacementMap: Styled<"web", "feDisplacementMap", import("react").SVGProps<SVGFEDisplacementMapElement>, object, object>;
    feDistantLight: Styled<"web", "feDistantLight", import("react").SVGProps<SVGFEDistantLightElement>, object, object>;
    feDropShadow: Styled<"web", "feDropShadow", import("react").SVGProps<SVGFEDropShadowElement>, object, object>;
    feFlood: Styled<"web", "feFlood", import("react").SVGProps<SVGFEFloodElement>, object, object>;
    feFuncA: Styled<"web", "feFuncA", import("react").SVGProps<SVGFEFuncAElement>, object, object>;
    feFuncB: Styled<"web", "feFuncB", import("react").SVGProps<SVGFEFuncBElement>, object, object>;
    feFuncG: Styled<"web", "feFuncG", import("react").SVGProps<SVGFEFuncGElement>, object, object>;
    feFuncR: Styled<"web", "feFuncR", import("react").SVGProps<SVGFEFuncRElement>, object, object>;
    feGaussianBlur: Styled<"web", "feGaussianBlur", import("react").SVGProps<SVGFEGaussianBlurElement>, object, object>;
    feImage: Styled<"web", "feImage", import("react").SVGProps<SVGFEImageElement>, object, object>;
    feMerge: Styled<"web", "feMerge", import("react").SVGProps<SVGFEMergeElement>, object, object>;
    feMergeNode: Styled<"web", "feMergeNode", import("react").SVGProps<SVGFEMergeNodeElement>, object, object>;
    feMorphology: Styled<"web", "feMorphology", import("react").SVGProps<SVGFEMorphologyElement>, object, object>;
    feOffset: Styled<"web", "feOffset", import("react").SVGProps<SVGFEOffsetElement>, object, object>;
    fePointLight: Styled<"web", "fePointLight", import("react").SVGProps<SVGFEPointLightElement>, object, object>;
    feSpecularLighting: Styled<"web", "feSpecularLighting", import("react").SVGProps<SVGFESpecularLightingElement>, object, object>;
    feSpotLight: Styled<"web", "feSpotLight", import("react").SVGProps<SVGFESpotLightElement>, object, object>;
    feTile: Styled<"web", "feTile", import("react").SVGProps<SVGFETileElement>, object, object>;
    feTurbulence: Styled<"web", "feTurbulence", import("react").SVGProps<SVGFETurbulenceElement>, object, object>;
    filter: Styled<"web", "filter", import("react").SVGProps<SVGFilterElement>, object, object>;
    foreignObject: Styled<"web", "foreignObject", import("react").SVGProps<SVGForeignObjectElement>, object, object>;
    g: Styled<"web", "g", import("react").SVGProps<SVGGElement>, object, object>;
    image: Styled<"web", "image", import("react").SVGProps<SVGImageElement>, object, object>;
    line: Styled<"web", "line", import("react").SVGProps<SVGLineElement>, object, object>;
    linearGradient: Styled<"web", "linearGradient", import("react").SVGProps<SVGLinearGradientElement>, object, object>;
    marker: Styled<"web", "marker", import("react").SVGProps<SVGMarkerElement>, object, object>;
    mask: Styled<"web", "mask", import("react").SVGProps<SVGMaskElement>, object, object>;
    metadata: Styled<"web", "metadata", import("react").SVGProps<SVGMetadataElement>, object, object>;
    mpath: Styled<"web", "mpath", import("react").SVGProps<SVGElement>, object, object>;
    path: Styled<"web", "path", import("react").SVGProps<SVGPathElement>, object, object>;
    pattern: Styled<"web", "pattern", import("react").SVGProps<SVGPatternElement>, object, object>;
    polygon: Styled<"web", "polygon", import("react").SVGProps<SVGPolygonElement>, object, object>;
    polyline: Styled<"web", "polyline", import("react").SVGProps<SVGPolylineElement>, object, object>;
    radialGradient: Styled<"web", "radialGradient", import("react").SVGProps<SVGRadialGradientElement>, object, object>;
    rect: Styled<"web", "rect", import("react").SVGProps<SVGRectElement>, object, object>;
    stop: Styled<"web", "stop", import("react").SVGProps<SVGStopElement>, object, object>;
    switch: Styled<"web", "switch", import("react").SVGProps<SVGSwitchElement>, object, object>;
    symbol: Styled<"web", "symbol", import("react").SVGProps<SVGSymbolElement>, object, object>;
    text: Styled<"web", "text", import("react").SVGProps<SVGTextElement>, object, object>;
    textPath: Styled<"web", "textPath", import("react").SVGProps<SVGTextPathElement>, object, object>;
    tspan: Styled<"web", "tspan", import("react").SVGProps<SVGTSpanElement>, object, object>;
    use: Styled<"web", "use", import("react").SVGProps<SVGUseElement>, object, object>;
    view: Styled<"web", "view", import("react").SVGProps<SVGViewElement>, object, object>;
};
export default styled;
