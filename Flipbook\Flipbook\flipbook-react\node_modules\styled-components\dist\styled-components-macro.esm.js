import{addDefault as e,addNamed as t}from"@babel/helper-module-imports";import o from"@babel/traverse";import{createMacro as r}from"babel-plugin-macros";import n from"babel-plugin-styled-components";function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e}).apply(this,arguments)}var i=r((function(r){var i,p=r.references,m=r.state,l=r.babel.types,s=r.config,f=(s=void 0===s?{}:s).importModuleName,c=void 0===f?"styled-components":f,u=function(e,t){if(null==e)return{};var o,r,n={},a=Object.keys(e);for(r=0;r<a.length;r++)o=a[r],t.indexOf(o)>=0||(n[o]=e[o]);return n}(s,["importModuleName"]),v=m.file.path;Object.keys(p).forEach((function(o){var r;"default"===o?(r=e(v,c,{nameHint:"styled"}),i=r):r=t(v,o,c,{nameHint:o}),p[o].forEach((function(e){e.node.name=r.name}))}));var b=a({},m,{opts:a({},u,{topLevelImportPaths:(u.topLevelImportPaths||[]).concat(c)}),customImportName:i});o(v.parent,n({types:l}).visitor,void 0,b)}),{configName:"styledComponents"});export default i;
//# sourceMappingURL=styled-components-macro.esm.js.map
