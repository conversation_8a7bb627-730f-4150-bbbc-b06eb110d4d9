!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("react-is"),require("react")):"function"==typeof define&&define.amd?define(["react-is","react"],t):(e=e||self).styled=t(e.ReactIs,e.React)}(this,(function(e,t){"use strict";var r="default"in e?e.default:e,n="default"in t?t.default:t;function i(e){return e&&"string"==typeof e.styledComponentId}var a=function(e,t){for(var r=[e[0]],n=0,i=t.length;n<i;n+=1)r.push(t[n],e[n+1]);return r},o=function(t){return null!==t&&"object"==typeof t&&"[object Object]"===(t.toString?t.toString():Object.prototype.toString.call(t))&&!e.typeOf(t)},s=Object.freeze([]),c=Object.freeze({});function l(e){return"function"==typeof e}function u(){return(u=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var f="undefined"!=typeof process&&void 0!==process.env&&(process.env.REACT_APP_SC_ATTR||process.env.SC_ATTR)||"data-styled",d="undefined"!=typeof window&&"HTMLElement"in window,h=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&(void 0!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&""!==process.env.REACT_APP_SC_DISABLE_SPEEDY?"false"!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&process.env.REACT_APP_SC_DISABLE_SPEEDY:void 0!==process.env.SC_DISABLE_SPEEDY&&""!==process.env.SC_DISABLE_SPEEDY&&"false"!==process.env.SC_DISABLE_SPEEDY&&process.env.SC_DISABLE_SPEEDY)),p={};function g(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];throw new Error("An error occurred. See https://git.io/JUIaE#"+e+" for more information."+(r.length>0?" Args: "+r.join(", "):""))}var m=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}var t=e.prototype;return t.indexOfGroup=function(e){for(var t=0,r=0;r<e;r++)t+=this.groupSizes[r];return t},t.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var r=this.groupSizes,n=r.length,i=n;e>=i;)(i<<=1)<0&&g(16,""+e);this.groupSizes=new Uint32Array(i),this.groupSizes.set(r),this.length=i;for(var a=n;a<i;a++)this.groupSizes[a]=0}for(var o=this.indexOfGroup(e+1),s=0,c=t.length;s<c;s++)this.tag.insertRule(o,t[s])&&(this.groupSizes[e]++,o++)},t.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],r=this.indexOfGroup(e),n=r+t;this.groupSizes[e]=0;for(var i=r;i<n;i++)this.tag.deleteRule(r)}},t.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var r=this.groupSizes[e],n=this.indexOfGroup(e),i=n+r,a=n;a<i;a++)t+=this.tag.getRule(a)+"/*!sc*/\n";return t},e}(),v=new Map,y=new Map,b=1,S=function(e){if(v.has(e))return v.get(e);for(;y.has(b);)b++;var t=b++;return v.set(e,t),y.set(t,e),t},k=function(e){return y.get(e)},w=function(e,t){t>=b&&(b=t+1),v.set(e,t),y.set(t,e)},C="style["+f+'][data-styled-version="5.3.11"]',A=new RegExp("^"+f+'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)'),x=function(e,t,r){for(var n,i=r.split(","),a=0,o=i.length;a<o;a++)(n=i[a])&&e.registerName(t,n)},I=function(e,t){for(var r=(t.textContent||"").split("/*!sc*/\n"),n=[],i=0,a=r.length;i<a;i++){var o=r[i].trim();if(o){var s=o.match(A);if(s){var c=0|parseInt(s[1],10),l=s[2];0!==c&&(w(l,c),x(e,l,s[3]),e.getTag().insertRules(c,n)),n.length=0}else n.push(o)}}},P=function(){return"undefined"!=typeof __webpack_nonce__?__webpack_nonce__:null},O=function(e){var t=document.head,r=e||t,n=document.createElement("style"),i=function(e){for(var t=e.childNodes,r=t.length;r>=0;r--){var n=t[r];if(n&&1===n.nodeType&&n.hasAttribute(f))return n}}(r),a=void 0!==i?i.nextSibling:null;n.setAttribute(f,"active"),n.setAttribute("data-styled-version","5.3.11");var o=P();return o&&n.setAttribute("nonce",o),r.insertBefore(n,a),n},R=function(){function e(e){var t=this.element=O(e);t.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,r=0,n=t.length;r<n;r++){var i=t[r];if(i.ownerNode===e)return i}g(17)}(t),this.length=0}var t=e.prototype;return t.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},t.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},t.getRule=function(e){var t=this.sheet.cssRules[e];return void 0!==t&&"string"==typeof t.cssText?t.cssText:""},e}(),E=function(){function e(e){var t=this.element=O(e);this.nodes=t.childNodes,this.length=0}var t=e.prototype;return t.insertRule=function(e,t){if(e<=this.length&&e>=0){var r=document.createTextNode(t),n=this.nodes[e];return this.element.insertBefore(r,n||null),this.length++,!0}return!1},t.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},t.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),T=function(){function e(e){this.rules=[],this.length=0}var t=e.prototype;return t.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},t.deleteRule=function(e){this.rules.splice(e,1),this.length--},t.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),_=d,j={isServer:!d,useCSSOMInjection:!h},N=function(){function e(e,t,r){void 0===e&&(e=c),void 0===t&&(t={}),this.options=u({},j,{},e),this.gs=t,this.names=new Map(r),this.server=!!e.isServer,!this.server&&d&&_&&(_=!1,function(e){for(var t=document.querySelectorAll(C),r=0,n=t.length;r<n;r++){var i=t[r];i&&"active"!==i.getAttribute(f)&&(I(e,i),i.parentNode&&i.parentNode.removeChild(i))}}(this))}e.registerId=function(e){return S(e)};var t=e.prototype;return t.reconstructWithOptions=function(t,r){return void 0===r&&(r=!0),new e(u({},this.options,{},t),this.gs,r&&this.names||void 0)},t.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},t.getTag=function(){return this.tag||(this.tag=(r=(t=this.options).isServer,n=t.useCSSOMInjection,i=t.target,e=r?new T(i):n?new R(i):new E(i),new m(e)));var e,t,r,n,i},t.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},t.registerName=function(e,t){if(S(e),this.names.has(e))this.names.get(e).add(t);else{var r=new Set;r.add(t),this.names.set(e,r)}},t.insertRules=function(e,t,r){this.registerName(e,t),this.getTag().insertRules(S(e),r)},t.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},t.clearRules=function(e){this.getTag().clearGroup(S(e)),this.clearNames(e)},t.clearTag=function(){this.tag=void 0},t.toString=function(){return function(e){for(var t=e.getTag(),r=t.length,n="",i=0;i<r;i++){var a=k(i);if(void 0!==a){var o=e.names.get(a),s=t.getGroup(i);if(o&&s&&o.size){var c=f+".g"+i+'[id="'+a+'"]',l="";void 0!==o&&o.forEach((function(e){e.length>0&&(l+=e+",")})),n+=""+s+c+'{content:"'+l+'"}/*!sc*/\n'}}}return n}(this)},e}(),z=/(a)(d)/gi,D=function(e){return String.fromCharCode(e+(e>25?39:97))};function M(e){var t,r="";for(t=Math.abs(e);t>52;t=t/52|0)r=D(t%52)+r;return(D(t%52)+r).replace(z,"$1-$2")}var L=function(e,t){for(var r=t.length;r;)e=33*e^t.charCodeAt(--r);return e},$=function(e){return L(5381,e)};function F(e){for(var t=0;t<e.length;t+=1){var r=e[t];if(l(r)&&!i(r))return!1}return!0}var B=$("5.3.11"),G=function(){function e(e,t,r){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===r||r.isStatic)&&F(e),this.componentId=t,this.baseHash=L(B,t),this.baseStyle=r,N.registerId(t)}return e.prototype.generateAndInjectStyles=function(e,t,r){var n=this.componentId,i=[];if(this.baseStyle&&i.push(this.baseStyle.generateAndInjectStyles(e,t,r)),this.isStatic&&!r.hash)if(this.staticRulesId&&t.hasNameForId(n,this.staticRulesId))i.push(this.staticRulesId);else{var a=le(this.rules,e,t,r).join(""),o=M(L(this.baseHash,a)>>>0);if(!t.hasNameForId(n,o)){var s=r(a,"."+o,void 0,n);t.insertRules(n,o,s)}i.push(o),this.staticRulesId=o}else{for(var c=this.rules.length,l=L(this.baseHash,r.hash),u="",f=0;f<c;f++){var d=this.rules[f];if("string"==typeof d)u+=d;else if(d){var h=le(d,e,t,r),p=Array.isArray(h)?h.join(""):h;l=L(l,p+f),u+=p}}if(u){var g=M(l>>>0);if(!t.hasNameForId(n,g)){var m=r(u,"."+g,void 0,n);t.insertRules(n,g,m)}i.push(g)}}return i.join(" ")},e}();function H(e){function t(e,t,n){var i=t.trim().split(p);t=i;var a=i.length,o=e.length;switch(o){case 0:case 1:var s=0;for(e=0===o?"":e[0]+" ";s<a;++s)t[s]=r(e,t[s],n).trim();break;default:var c=s=0;for(t=[];s<a;++s)for(var l=0;l<o;++l)t[c++]=r(e[l]+" ",i[s],n).trim()}return t}function r(e,t,r){var n=t.charCodeAt(0);switch(33>n&&(n=(t=t.trim()).charCodeAt(0)),n){case 38:return t.replace(g,"$1"+e.trim());case 58:return e.trim()+t.replace(g,"$1"+e.trim());default:if(0<1*r&&0<t.indexOf("\f"))return t.replace(g,(58===e.charCodeAt(0)?"":"$1")+e.trim())}return e+t}function n(e,t,r,a){var o=e+";",s=2*t+3*r+4*a;if(944===s){e=o.indexOf(":",9)+1;var c=o.substring(e,o.length-1).trim();return c=o.substring(0,e).trim()+c+";",1===R||2===R&&i(c,1)?"-webkit-"+c+c:c}if(0===R||2===R&&!i(o,1))return o;switch(s){case 1015:return 97===o.charCodeAt(10)?"-webkit-"+o+o:o;case 951:return 116===o.charCodeAt(3)?"-webkit-"+o+o:o;case 963:return 110===o.charCodeAt(5)?"-webkit-"+o+o:o;case 1009:if(100!==o.charCodeAt(4))break;case 969:case 942:return"-webkit-"+o+o;case 978:return"-webkit-"+o+"-moz-"+o+o;case 1019:case 983:return"-webkit-"+o+"-moz-"+o+"-ms-"+o+o;case 883:if(45===o.charCodeAt(8))return"-webkit-"+o+o;if(0<o.indexOf("image-set(",11))return o.replace(x,"$1-webkit-$2")+o;break;case 932:if(45===o.charCodeAt(4))switch(o.charCodeAt(5)){case 103:return"-webkit-box-"+o.replace("-grow","")+"-webkit-"+o+"-ms-"+o.replace("grow","positive")+o;case 115:return"-webkit-"+o+"-ms-"+o.replace("shrink","negative")+o;case 98:return"-webkit-"+o+"-ms-"+o.replace("basis","preferred-size")+o}return"-webkit-"+o+"-ms-"+o+o;case 964:return"-webkit-"+o+"-ms-flex-"+o+o;case 1023:if(99!==o.charCodeAt(8))break;return"-webkit-box-pack"+(c=o.substring(o.indexOf(":",15)).replace("flex-","").replace("space-between","justify"))+"-webkit-"+o+"-ms-flex-pack"+c+o;case 1005:return d.test(o)?o.replace(f,":-webkit-")+o.replace(f,":-moz-")+o:o;case 1e3:switch(t=(c=o.substring(13).trim()).indexOf("-")+1,c.charCodeAt(0)+c.charCodeAt(t)){case 226:c=o.replace(b,"tb");break;case 232:c=o.replace(b,"tb-rl");break;case 220:c=o.replace(b,"lr");break;default:return o}return"-webkit-"+o+"-ms-"+c+o;case 1017:if(-1===o.indexOf("sticky",9))break;case 975:switch(t=(o=e).length-10,s=(c=(33===o.charCodeAt(t)?o.substring(0,t):o).substring(e.indexOf(":",7)+1).trim()).charCodeAt(0)+(0|c.charCodeAt(7))){case 203:if(111>c.charCodeAt(8))break;case 115:o=o.replace(c,"-webkit-"+c)+";"+o;break;case 207:case 102:o=o.replace(c,"-webkit-"+(102<s?"inline-":"")+"box")+";"+o.replace(c,"-webkit-"+c)+";"+o.replace(c,"-ms-"+c+"box")+";"+o}return o+";";case 938:if(45===o.charCodeAt(5))switch(o.charCodeAt(6)){case 105:return c=o.replace("-items",""),"-webkit-"+o+"-webkit-box-"+c+"-ms-flex-"+c+o;case 115:return"-webkit-"+o+"-ms-flex-item-"+o.replace(w,"")+o;default:return"-webkit-"+o+"-ms-flex-line-pack"+o.replace("align-content","").replace(w,"")+o}break;case 973:case 989:if(45!==o.charCodeAt(3)||122===o.charCodeAt(4))break;case 931:case 953:if(!0===A.test(e))return 115===(c=e.substring(e.indexOf(":")+1)).charCodeAt(0)?n(e.replace("stretch","fill-available"),t,r,a).replace(":fill-available",":stretch"):o.replace(c,"-webkit-"+c)+o.replace(c,"-moz-"+c.replace("fill-",""))+o;break;case 962:if(o="-webkit-"+o+(102===o.charCodeAt(5)?"-ms-"+o:"")+o,211===r+a&&105===o.charCodeAt(13)&&0<o.indexOf("transform",10))return o.substring(0,o.indexOf(";",27)+1).replace(h,"$1-webkit-$2")+o}return o}function i(e,t){var r=e.indexOf(1===t?":":"{"),n=e.substring(0,3!==t?r:10);return r=e.substring(r+1,e.length-1),j(2!==t?n:n.replace(C,"$1"),r,t)}function a(e,t){var r=n(t,t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2));return r!==t+";"?r.replace(k," or ($1)").substring(4):"("+t+")"}function o(e,t,r,n,i,a,o,s,l,u){for(var f,d=0,h=t;d<_;++d)switch(f=T[d].call(c,e,h,r,n,i,a,o,s,l,u)){case void 0:case!1:case!0:case null:break;default:h=f}if(h!==t)return h}function s(e){return void 0!==(e=e.prefix)&&(j=null,e?"function"!=typeof e?R=1:(R=2,j=e):R=0),s}function c(e,r){var s=e;if(33>s.charCodeAt(0)&&(s=s.trim()),s=[s],0<_){var c=o(-1,r,s,s,P,I,0,0,0,0);void 0!==c&&"string"==typeof c&&(r=c)}var f=function e(r,s,c,f,d){for(var h,p,g,b,k,w=0,C=0,A=0,x=0,T=0,j=0,z=g=h=0,D=0,M=0,L=0,$=0,F=c.length,B=F-1,G="",H="",W="",Y="";D<F;){if(p=c.charCodeAt(D),D===B&&0!==C+x+A+w&&(0!==C&&(p=47===C?10:47),x=A=w=0,F++,B++),0===C+x+A+w){if(D===B&&(0<M&&(G=G.replace(u,"")),0<G.trim().length)){switch(p){case 32:case 9:case 59:case 13:case 10:break;default:G+=c.charAt(D)}p=59}switch(p){case 123:for(h=(G=G.trim()).charCodeAt(0),g=1,$=++D;D<F;){switch(p=c.charCodeAt(D)){case 123:g++;break;case 125:g--;break;case 47:switch(p=c.charCodeAt(D+1)){case 42:case 47:e:{for(z=D+1;z<B;++z)switch(c.charCodeAt(z)){case 47:if(42===p&&42===c.charCodeAt(z-1)&&D+2!==z){D=z+1;break e}break;case 10:if(47===p){D=z+1;break e}}D=z}}break;case 91:p++;case 40:p++;case 34:case 39:for(;D++<B&&c.charCodeAt(D)!==p;);}if(0===g)break;D++}switch(g=c.substring($,D),0===h&&(h=(G=G.replace(l,"").trim()).charCodeAt(0)),h){case 64:switch(0<M&&(G=G.replace(u,"")),p=G.charCodeAt(1)){case 100:case 109:case 115:case 45:M=s;break;default:M=E}if($=(g=e(s,M,g,p,d+1)).length,0<_&&(k=o(3,g,M=t(E,G,L),s,P,I,$,p,d,f),G=M.join(""),void 0!==k&&0===($=(g=k.trim()).length)&&(p=0,g="")),0<$)switch(p){case 115:G=G.replace(S,a);case 100:case 109:case 45:g=G+"{"+g+"}";break;case 107:g=(G=G.replace(m,"$1 $2"))+"{"+g+"}",g=1===R||2===R&&i("@"+g,3)?"@-webkit-"+g+"@"+g:"@"+g;break;default:g=G+g,112===f&&(H+=g,g="")}else g="";break;default:g=e(s,t(s,G,L),g,f,d+1)}W+=g,g=L=M=z=h=0,G="",p=c.charCodeAt(++D);break;case 125:case 59:if(1<($=(G=(0<M?G.replace(u,""):G).trim()).length))switch(0===z&&(h=G.charCodeAt(0),45===h||96<h&&123>h)&&($=(G=G.replace(" ",":")).length),0<_&&void 0!==(k=o(1,G,s,r,P,I,H.length,f,d,f))&&0===($=(G=k.trim()).length)&&(G="\0\0"),h=G.charCodeAt(0),p=G.charCodeAt(1),h){case 0:break;case 64:if(105===p||99===p){Y+=G+c.charAt(D);break}default:58!==G.charCodeAt($-1)&&(H+=n(G,h,p,G.charCodeAt(2)))}L=M=z=h=0,G="",p=c.charCodeAt(++D)}}switch(p){case 13:case 10:47===C?C=0:0===1+h&&107!==f&&0<G.length&&(M=1,G+="\0"),0<_*N&&o(0,G,s,r,P,I,H.length,f,d,f),I=1,P++;break;case 59:case 125:if(0===C+x+A+w){I++;break}default:switch(I++,b=c.charAt(D),p){case 9:case 32:if(0===x+w+C)switch(T){case 44:case 58:case 9:case 32:b="";break;default:32!==p&&(b=" ")}break;case 0:b="\\0";break;case 12:b="\\f";break;case 11:b="\\v";break;case 38:0===x+C+w&&(M=L=1,b="\f"+b);break;case 108:if(0===x+C+w+O&&0<z)switch(D-z){case 2:112===T&&58===c.charCodeAt(D-3)&&(O=T);case 8:111===j&&(O=j)}break;case 58:0===x+C+w&&(z=D);break;case 44:0===C+A+x+w&&(M=1,b+="\r");break;case 34:case 39:0===C&&(x=x===p?0:0===x?p:x);break;case 91:0===x+C+A&&w++;break;case 93:0===x+C+A&&w--;break;case 41:0===x+C+w&&A--;break;case 40:if(0===x+C+w){if(0===h)switch(2*T+3*j){case 533:break;default:h=1}A++}break;case 64:0===C+A+x+w+z+g&&(g=1);break;case 42:case 47:if(!(0<x+w+A))switch(C){case 0:switch(2*p+3*c.charCodeAt(D+1)){case 235:C=47;break;case 220:$=D,C=42}break;case 42:47===p&&42===T&&$+2!==D&&(33===c.charCodeAt($+2)&&(H+=c.substring($,D+1)),b="",C=0)}}0===C&&(G+=b)}j=T,T=p,D++}if(0<($=H.length)){if(M=s,0<_&&void 0!==(k=o(2,H,M,r,P,I,$,f,d,f))&&0===(H=k).length)return Y+H+W;if(H=M.join(",")+"{"+H+"}",0!=R*O){switch(2!==R||i(H,2)||(O=0),O){case 111:H=H.replace(y,":-moz-$1")+H;break;case 112:H=H.replace(v,"::-webkit-input-$1")+H.replace(v,"::-moz-$1")+H.replace(v,":-ms-input-$1")+H}O=0}}return Y+H+W}(E,s,r,0,0);return 0<_&&void 0!==(c=o(-2,f,s,s,P,I,f.length,0,0,0))&&(f=c),O=0,I=P=1,f}var l=/^\0+/g,u=/[\0\r\f]/g,f=/: */g,d=/zoo|gra/,h=/([,: ])(transform)/g,p=/,\r+?/g,g=/([\t\r\n ])*\f?&/g,m=/@(k\w+)\s*(\S*)\s*/,v=/::(place)/g,y=/:(read-only)/g,b=/[svh]\w+-[tblr]{2}/,S=/\(\s*(.*)\s*\)/g,k=/([\s\S]*?);/g,w=/-self|flex-/g,C=/[^]*?(:[rp][el]a[\w-]+)[^]*/,A=/stretch|:\s*\w+\-(?:conte|avail)/,x=/([^-])(image-set\()/,I=1,P=1,O=0,R=1,E=[],T=[],_=0,j=null,N=0;return c.use=function e(t){switch(t){case void 0:case null:_=T.length=0;break;default:if("function"==typeof t)T[_++]=t;else if("object"==typeof t)for(var r=0,n=t.length;r<n;++r)e(t[r]);else N=0|!!t}return e},c.set=s,void 0!==e&&s(e),c}var W=/^\s*\/\/.*$/gm,Y=[":","[",".","#"];function U(e){var t,r,n,i,a=void 0===e?c:e,o=a.options,l=void 0===o?c:o,u=a.plugins,f=void 0===u?s:u,d=new H(l),h=[],p=function(e){function t(t){if(t)try{e(t+"}")}catch(e){}}return function(r,n,i,a,o,s,c,l,u,f){switch(r){case 1:if(0===u&&64===n.charCodeAt(0))return e(n+";"),"";break;case 2:if(0===l)return n+"/*|*/";break;case 3:switch(l){case 102:case 112:return e(i[0]+n),"";default:return n+(0===f?"/*|*/":"")}case-2:n.split("/*|*/}").forEach(t)}}}((function(e){h.push(e)})),m=function(e,n,a){return 0===n&&-1!==Y.indexOf(a[r.length])||a.match(i)?e:"."+t};function v(e,a,o,s){void 0===s&&(s="&");var c=e.replace(W,""),l=a&&o?o+" "+a+" { "+c+" }":c;return t=s,r=a,n=new RegExp("\\"+r+"\\b","g"),i=new RegExp("(\\"+r+"\\b){2,}"),d(o||!a?"":a,l)}return d.use([].concat(f,[function(e,t,i){2===e&&i.length&&i[0].lastIndexOf(r)>0&&(i[0]=i[0].replace(n,m))},p,function(e){if(-2===e){var t=h;return h=[],t}}])),v.hash=f.length?f.reduce((function(e,t){return t.name||g(15),L(e,t.name)}),5381).toString():"",v}var q=n.createContext(),V=q.Consumer,X=n.createContext(),Z=(X.Consumer,new N),J=U();function K(){return t.useContext(q)||Z}function Q(){return t.useContext(X)||J}function ee(e){var r=t.useState(e.stylisPlugins),i=r[0],a=r[1],o=K(),s=t.useMemo((function(){var t=o;return e.sheet?t=e.sheet:e.target&&(t=t.reconstructWithOptions({target:e.target},!1)),e.disableCSSOMInjection&&(t=t.reconstructWithOptions({useCSSOMInjection:!1})),t}),[e.disableCSSOMInjection,e.sheet,e.target]),c=t.useMemo((function(){return U({options:{prefix:!e.disableVendorPrefixes},plugins:i})}),[e.disableVendorPrefixes,i]);return t.useEffect((function(){(function(e,t,r,n){var i=void 0;if(void 0!==i)return!!i;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;var a=Object.keys(e),o=Object.keys(t);if(a.length!==o.length)return!1;for(var s=Object.prototype.hasOwnProperty.bind(t),c=0;c<a.length;c++){var l=a[c];if(!s(l))return!1;var u=e[l],f=t[l];if(!1===(i=void 0)||void 0===i&&u!==f)return!1}return!0})(i,e.stylisPlugins)||a(e.stylisPlugins)}),[e.stylisPlugins]),n.createElement(q.Provider,{value:s},n.createElement(X.Provider,{value:c},e.children))}var te=function(){function e(e,t){var r=this;this.inject=function(e,t){void 0===t&&(t=J);var n=r.name+t.hash;e.hasNameForId(r.id,n)||e.insertRules(r.id,n,t(r.rules,n,"@keyframes"))},this.toString=function(){return g(12,String(r.name))},this.name=e,this.id="sc-keyframes-"+e,this.rules=t}return e.prototype.getName=function(e){return void 0===e&&(e=J),this.name+e.hash},e}(),re=/([A-Z])/,ne=/([A-Z])/g,ie=/^ms-/,ae=function(e){return"-"+e.toLowerCase()};function oe(e){return re.test(e)?e.replace(ne,ae).replace(ie,"-ms-"):e}var se={animationIterationCount:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},ce=function(e){return null==e||!1===e||""===e};function le(e,t,r,n){if(Array.isArray(e)){for(var a,s=[],c=0,u=e.length;c<u;c+=1)""!==(a=le(e[c],t,r,n))&&(Array.isArray(a)?s.push.apply(s,a):s.push(a));return s}return ce(e)?"":i(e)?"."+e.styledComponentId:l(e)?"function"!=typeof(f=e)||f.prototype&&f.prototype.isReactComponent||!t?e:le(e(t),t,r,n):e instanceof te?r?(e.inject(r,n),e.getName(n)):e:o(e)?function e(t,r){var n,i,a=[];for(var s in t)t.hasOwnProperty(s)&&!ce(t[s])&&(Array.isArray(t[s])&&t[s].isCss||l(t[s])?a.push(oe(s)+":",t[s],";"):o(t[s])?a.push.apply(a,e(t[s],s)):a.push(oe(s)+": "+(n=s,null==(i=t[s])||"boolean"==typeof i||""===i?"":"number"!=typeof i||0===i||n in se||n.startsWith("--")?String(i).trim():i+"px")+";"));return r?[r+" {"].concat(a,["}"]):a}(e):e.toString();var f}var ue=function(e){return Array.isArray(e)&&(e.isCss=!0),e};function fe(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return l(e)||o(e)?ue(le(a(s,[e].concat(r)))):0===r.length&&1===e.length&&"string"==typeof e[0]?e:ue(le(a(e,r)))}var de=function(){function e(e,t){this.rules=e,this.componentId=t,this.isStatic=F(e),N.registerId(this.componentId+1)}var t=e.prototype;return t.createStyles=function(e,t,r,n){var i=n(le(this.rules,t,r,n).join(""),""),a=this.componentId+e;r.insertRules(a,a,i)},t.removeStyles=function(e,t){t.clearRules(this.componentId+e)},t.renderStyles=function(e,t,r,n){e>2&&N.registerId(this.componentId+e),this.removeStyles(e,r),this.createStyles(e,t,r,n)},e}(),he=n.createContext(),pe=he.Consumer,ge=function(e,t,r){return void 0===r&&(r=c),e.theme!==r.theme&&e.theme||t||r.theme},me=function(e){return M($(e)>>>0)},ve=function(){function e(){var e=this;this._emitSheetCSS=function(){var t=e.instance.toString();if(!t)return"";var r=P();return"<style "+[r&&'nonce="'+r+'"',f+'="true"','data-styled-version="5.3.11"'].filter(Boolean).join(" ")+">"+t+"</style>"},this.getStyleTags=function(){return e.sealed?g(2):e._emitSheetCSS()},this.getStyleElement=function(){var t;if(e.sealed)return g(2);var r=((t={})[f]="",t["data-styled-version"]="5.3.11",t.dangerouslySetInnerHTML={__html:e.instance.toString()},t),i=P();return i&&(r.nonce=i),[n.createElement("style",u({},r,{key:"sc-0-0"}))]},this.seal=function(){e.sealed=!0},this.instance=new N({isServer:!0}),this.sealed=!1}var t=e.prototype;return t.collectStyles=function(e){return this.sealed?g(2):n.createElement(ee,{sheet:this.instance},e)},t.interleaveWithNodeStream=function(e){return g(3)},e}(),ye={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},be={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Se={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},ke={};function we(e){return r.isMemo(e)?Se:ke[e.$$typeof]||ye}ke[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},ke[r.Memo]=Se;var Ce=Object.defineProperty,Ae=Object.getOwnPropertyNames,xe=Object.getOwnPropertySymbols,Ie=Object.getOwnPropertyDescriptor,Pe=Object.getPrototypeOf,Oe=Object.prototype,Re=function e(t,r,n){if("string"!=typeof r){if(Oe){var i=Pe(r);i&&i!==Oe&&e(t,i,n)}var a=Ae(r);xe&&(a=a.concat(xe(r)));for(var o=we(t),s=we(r),c=0;c<a.length;++c){var l=a[c];if(!(be[l]||n&&n[l]||s&&s[l]||o&&o[l])){var u=Ie(r,l);try{Ce(t,l,u)}catch(e){}}}}return t},Ee={StyleSheet:N,masterSheet:Z},Te=Object.freeze({__proto__:null,createGlobalStyle:function(e){for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];var o=fe.apply(void 0,[e].concat(i)),s="sc-global-"+me(JSON.stringify(o)),c=new de(o,s);function l(e){var r=K(),n=Q(),i=t.useContext(he),a=t.useRef(r.allocateGSInstance(s)).current;return r.server&&f(a,e,r,i,n),t.useLayoutEffect((function(){if(!r.server)return f(a,e,r,i,n),function(){return c.removeStyles(a,r)}}),[a,e,r,i,n]),null}function f(e,t,r,n,i){if(c.isStatic)c.renderStyles(e,p,r,i);else{var a=u({},t,{theme:ge(t,n,l.defaultProps)});c.renderStyles(e,a,r,i)}}return n.memo(l)},css:fe,isStyledComponent:i,keyframes:function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var i=fe.apply(void 0,[e].concat(r)).join(""),a=me(i);return new te(a,i)},ServerStyleSheet:ve,StyleSheetConsumer:V,StyleSheetContext:q,StyleSheetManager:ee,ThemeConsumer:pe,ThemeContext:he,ThemeProvider:function(e){var r=t.useContext(he),i=t.useMemo((function(){return function(e,t){return e?l(e)?e(t):Array.isArray(e)||"object"!=typeof e?g(8):t?u({},t,{},e):e:g(14)}(e.theme,r)}),[e.theme,r]);return e.children?n.createElement(he.Provider,{value:i},e.children):null},useTheme:function(){return t.useContext(he)},version:"5.3.11",withTheme:function(e){var r=n.forwardRef((function(r,i){var a=t.useContext(he),o=e.defaultProps,s=ge(r,a,o);return n.createElement(e,u({},r,{theme:s,ref:i}))}));return Re(r,e),r.displayName="WithTheme(undefined)",r},__PRIVATE__:Ee});function _e(e){var t=Object.create(null);return function(r){return void 0===t[r]&&(t[r]=e(r)),t[r]}}var je=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|download|draggable|encType|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,Ne=_e((function(e){return je.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91})),ze=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,De=/(^-|-$)/g;function Me(e){return e.replace(ze,"-").replace(De,"")}function Le(e){return"string"==typeof e&&!0}var $e=function(e){return"function"==typeof e||"object"==typeof e&&null!==e&&!Array.isArray(e)},Fe=function(e){return"__proto__"!==e&&"constructor"!==e&&"prototype"!==e};function Be(e,t,r){var n=e[r];$e(t)&&$e(n)?Ge(n,t):e[r]=t}function Ge(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(var i=0,a=r;i<a.length;i++){var o=a[i];if($e(o))for(var s in o)Fe(s)&&Be(e,o[s],s)}return e}var He={};function We(e,r,a){var o=i(e),f=!Le(e),d=r.attrs,h=void 0===d?s:d,p=r.componentId,g=void 0===p?function(e,t){var r="string"!=typeof e?"sc":Me(e);He[r]=(He[r]||0)+1;var n=r+"-"+me("5.3.11"+r+He[r]);return t?t+"-"+n:n}(r.displayName,r.parentComponentId):p,m=r.displayName,v=void 0===m?function(e){return Le(e)?"styled."+e:"Styled(undefined)"}(e):m,y=r.displayName&&r.componentId?Me(r.displayName)+"-"+r.componentId:r.componentId||g,b=o&&e.attrs?Array.prototype.concat(e.attrs,h).filter(Boolean):h,S=r.shouldForwardProp;o&&e.shouldForwardProp&&(S=r.shouldForwardProp?function(t,n,i){return e.shouldForwardProp(t,n,i)&&r.shouldForwardProp(t,n,i)}:e.shouldForwardProp);var k,w=new G(a,y,o?e.componentStyle:void 0),C=w.isStatic&&0===h.length,A=function(e,r){return function(e,r,n,i){var a=e.attrs,o=e.componentStyle,s=e.defaultProps,f=e.foldedComponentIds,d=e.shouldForwardProp,h=e.styledComponentId,p=e.target,g=function(e,t,r){void 0===e&&(e=c);var n=u({},t,{theme:e}),i={};return r.forEach((function(e){var t,r,a,o=e;for(t in l(o)&&(o=o(n)),o)n[t]=i[t]="className"===t?(r=i[t],a=o[t],r&&a?r+" "+a:r||a):o[t]})),[n,i]}(ge(r,t.useContext(he),s)||c,r,a),m=g[0],v=g[1],y=function(e,t,r,n){var i=K(),a=Q();return t?e.generateAndInjectStyles(c,i,a):e.generateAndInjectStyles(r,i,a)}(o,i,m),b=n,S=v.$as||r.$as||v.as||r.as||p,k=Le(S),w=v!==r?u({},r,{},v):r,C={};for(var A in w)"$"!==A[0]&&"as"!==A&&("forwardedAs"===A?C.as=w[A]:(d?d(A,Ne,S):!k||Ne(A))&&(C[A]=w[A]));return r.style&&v.style!==r.style&&(C.style=u({},r.style,{},v.style)),C.className=Array.prototype.concat(f,h,y!==h?y:null,r.className,v.className).filter(Boolean).join(" "),C.ref=b,t.createElement(S,C)}(k,e,r,C)};return A.displayName=v,(k=n.forwardRef(A)).attrs=b,k.componentStyle=w,k.displayName=v,k.shouldForwardProp=S,k.foldedComponentIds=o?Array.prototype.concat(e.foldedComponentIds,e.styledComponentId):s,k.styledComponentId=y,k.target=o?e.target:e,k.withComponent=function(e){var t=r.componentId,n=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(r,["componentId"]),i=t&&t+"-"+(Le(e)?e:Me(void 0));return We(e,u({},n,{attrs:b,componentId:i}),a)},Object.defineProperty(k,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(t){this._foldedDefaultProps=o?Ge({},e.defaultProps,t):t}}),Object.defineProperty(k,"toString",{value:function(){return"."+k.styledComponentId}}),f&&Re(k,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0,withComponent:!0}),k}var Ye=function(t){return function t(r,n,i){if(void 0===i&&(i=c),!e.isValidElementType(n))return g(1,String(n));var a=function(){return r(n,i,fe.apply(void 0,arguments))};return a.withConfig=function(e){return t(r,n,u({},i,{},e))},a.attrs=function(e){return t(r,n,u({},i,{attrs:Array.prototype.concat(i.attrs,e).filter(Boolean)}))},a}(We,t)};for(var Ue in["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","textPath","tspan"].forEach((function(e){Ye[e]=Ye(e)})),Te)Ye[Ue]=Te[Ue];return Ye}));
//# sourceMappingURL=styled-components.min.js.map
