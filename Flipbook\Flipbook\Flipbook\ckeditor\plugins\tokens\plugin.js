CKEDITOR.plugins.add( 'tokens',
{   
   requires : ['richcombo'], //, 'styles' ],
   init : function( editor )
   {
      var config = editor.config,
         lang = editor.lang.format;
      var styles = {};
      // Gets the list of tags from the settings.
      var tags = []; //new Array();
      tags[0] = ["1.0", "100%", "100%"];
      tags[1] = ["0.75", "75%", "75%"];
      tags[2] = ["0.50", "50%", "50%"];
	  tags[3] = ["0.25", "25%", "25%"];
	  tags[4] = ["0.0", "0%", "0%"];

       // Create style objects for all defined styles.
	  var OpacityStyle = new CKEDITOR.style(CKEDITOR.config.StyleOpacity); //ST-1841
	  
      editor.ui.addRichCombo( 'tokens',
         {
            label : "Opacity",             
            voiceLabel : "Opacity",
            className : 'cke_format ckeOpacityClass',
            multiSelect : false,
            allowedContent: OpacityStyle,
            panel :
            {
               css : [ config.contentsCss, CKEDITOR.skin.getPath('editor') ],
               voiceLabel : lang.panelVoiceLabel
            },

            init : function()
            {
                this.className = 'ckeOpacityClassBody';
                this.startGroup("Opacity");/*ST-1354*/
               //this.add('value', 'drop_text', 'drop_label');
               for (var this_tag in tags){
                  this.add(tags[this_tag][0], tags[this_tag][1], tags[this_tag][2]);
               }
            },
            onClick : function( value )
            {         
                editor.focus();                
               editor.fire( 'saveSnapshot' );
				
			   styles = new CKEDITOR.style( {  attributes: { 'style': 'opacity:' + value } } );
		
				editor.applyStyle( styles );
				//this.setValue(this._.items,value);
               //editor.insertHtml(value);
               editor.fire( 'saveSnapshot' );
            }
            // ,
            //refresh: function() {
            //if ( !editor.activeFilter.check( style ) )
            //   this.setState( CKEDITOR.TRISTATE_DISABLED );
            //}
         });
   }
});

//ST-1841
CKEDITOR.config.StyleOpacity = {
    element: 'span',
    styles: { 'opacity': 'value' }
};