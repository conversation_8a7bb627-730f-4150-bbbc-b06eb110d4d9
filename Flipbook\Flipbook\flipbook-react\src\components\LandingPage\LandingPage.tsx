import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import Header from './Header';
import Sidebar from './Sidebar';
import FlipbookGrid from './FlipbookGrid';
import CreateFlipbookModal from './CreateFlipbookModal';
import { useFlipbooks } from '../../hooks/useFlipbooks';
import { useAuth } from '../../hooks/useAuth';

const LandingPageContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
`;

const MainContent = styled.div`
  display: flex;
  flex: 1;
  overflow: hidden;
`;

const ContentArea = styled.div`
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #ffffff;
`;

const Section = styled.div`
  margin-bottom: 40px;
`;

const SectionTitle = styled.h2`
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
`;



const CertificationBadge = styled.div`
  position: fixed;
  top: 80px;
  right: 20px;
  width: 100px;
  height: 100px;
  background-image: url('/images/certification-badge.png');
  background-size: contain;
  background-repeat: no-repeat;
  z-index: 10;
`;

interface Flipbook {
  id: number;
  title: string;
  thumbnail?: string;
  isInspiration?: boolean;
}

const LandingPage: React.FC = () => {
  const navigate = useNavigate();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Use real hooks that match .NET functionality
  const { user, isAuthenticated } = useAuth();
  const {
    userFlipbooks,
    inspirationFlipbooks,
    loading,
    error,
    createFlipbook,
    copyInspirationFlipbook
  } = useFlipbooks();

  // Convert data to match component interface
  const myFlipbooks: Flipbook[] = userFlipbooks.map(fb => ({
    id: fb.PortfolioID,
    title: fb.PortfolioTitle || 'Untitled',
    thumbnail: fb.ThumbnailPath || 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop',
    isInspiration: false
  }));

  const inspirationFlipbooksFormatted: Flipbook[] = inspirationFlipbooks.map(fb => ({
    id: fb.PortfolioID,
    title: fb.FBTitle,
    thumbnail: fb.TnImageSrc || 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop',
    isInspiration: true
  }));

  const handleCreateNew = () => {
    setIsCreateModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsCreateModalOpen(false);
  };

  const handleCreateFlipbook = async (title: string) => {
    try {
      const portfolioId = await createFlipbook({ title });
      if (portfolioId) {
        console.log('Flipbook created successfully with ID:', portfolioId);
        setIsCreateModalOpen(false);
        // Navigate to flipbook editor
        navigate(`/editor/${portfolioId}`);
      }
    } catch (err) {
      console.error('Failed to create flipbook:', err);
    }
  };

  const handleCopyInspiration = async (flipbook: Flipbook) => {
    try {
      const portfolioId = await copyInspirationFlipbook({
        portfolioId: flipbook.id,
        title: `Copy of ${flipbook.title}`
      });
      if (portfolioId) {
        console.log('Inspiration flipbook copied successfully with ID:', portfolioId);
        // Navigate to flipbook editor
        navigate(`/editor/${portfolioId}`);
      }
    } catch (err) {
      console.error('Failed to copy inspiration flipbook:', err);
    }
  };

  return (
    <LandingPageContainer>
      <Header />
      <MainContent>
        <Sidebar />
        <ContentArea>
          <Section>
            <SectionTitle>My Flipbooks</SectionTitle>
            <FlipbookGrid
              flipbooks={myFlipbooks}
              showCreateNew={true}
              onCreateNew={handleCreateNew}
              onFlipbookClick={(flipbook) => navigate(`/editor/${flipbook.id}`)}
              onFlipbookEdit={(flipbook) => navigate(`/editor/${flipbook.id}`)}
            />
          </Section>

          <Section>
            <SectionTitle>Inspiration</SectionTitle>
            <FlipbookGrid
              flipbooks={inspirationFlipbooksFormatted}
              onFlipbookClick={(flipbook) => {
                if (flipbook.isInspiration) {
                  // Navigate to flipbook viewer for inspiration flipbooks
                  navigate(`/viewer/${flipbook.id}`);
                } else {
                  // Navigate to editor for user flipbooks
                  navigate(`/editor/${flipbook.id}`);
                }
              }}
              onFlipbookCopy={handleCopyInspiration}
            />
          </Section>
        </ContentArea>
      </MainContent>
      
      <CertificationBadge />
      
      {isCreateModalOpen && (
        <CreateFlipbookModal
          onClose={handleCloseModal}
          onCreate={handleCreateFlipbook}
        />
      )}
    </LandingPageContainer>
  );
};

export default LandingPage;
